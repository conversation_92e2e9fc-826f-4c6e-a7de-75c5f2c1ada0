// 改进版本的并发处理代码
public class ImprovedEvaluationController {

    // 1. 动态批大小配置
    private static final int DEFAULT_BATCH_SIZE = 20; // 根据线程池大小调整
    private static final int MAX_CONCURRENT_TASKS = 50; // 最大并发任务数
    private static final long TASK_TIMEOUT_MINUTES = 10; // 任务超时时间

    @GetMapping("/run_improved")
    public ResponseEntity<byte[]> evaluationImproved(
            @RequestParam(value = "evaluation_name") String evaluationName,
            @RequestParam(value = "ids", required = false) String idsStr,
            @RequestParam(value = "limit", required = false) String limitStr,
            @RequestParam(value = "user_id", required = false) String userID) {

        List<EvaluationData> evaluationData = evaluationDataService.getEvaluationData();
        if (CollectionUtils.isEmpty(evaluationData)) {
            return new ResponseEntity<>(null, null, HttpStatus.OK);
        }

        // ... 其他初始化代码 ...

        // 改进的并发处理
        ConcurrentHashMap<Integer, String> errorMap = new ConcurrentHashMap<>();
        ConcurrentHashMap<Integer, String> case2Session = new ConcurrentHashMap<>();

        // 方案1: 使用信号量控制并发数
        processWithSemaphore(evaluationData, evaluationID, userID, errorMap, case2Session);

        // 或者方案2: 使用CompletableFuture.allOf处理所有任务
        // processAllAtOnce(evaluationData, evaluationID, userID, errorMap, case2Session);

        // ... 后续处理代码 ...
    }

    /**
     * 方案1: 使用信号量控制并发数量
     */
    private void processWithSemaphore(List<EvaluationData> evaluationData,
                                    String evaluationID,
                                    String userID,
                                    ConcurrentHashMap<Integer, String> errorMap,
                                    ConcurrentHashMap<Integer, String> case2Session) {

        Semaphore semaphore = new Semaphore(MAX_CONCURRENT_TASKS);
        List<CompletableFuture<Void>> allFutures = new ArrayList<>();

        for (EvaluationData data : evaluationData) {
            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                try {
                    // 获取信号量许可
                    semaphore.acquire();

                    long userIDL = parseUserId(userID);
                    String sessionID = evaluateOneCase(evaluationID, data, userIDL);
                    case2Session.put(data.getNum(), sessionID);

                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    log.error("Task interrupted for case: {}", data.getNum(), e);
                    errorMap.put(data.getNum(), "Task interrupted: " + e.getMessage());
                } catch (Exception e) {
                    log.error("evaluateOneCase num:{} error", data.getNum(), e);
                    errorMap.put(data.getNum(), e.getMessage());
                } finally {
                    // 释放信号量许可
                    semaphore.release();
                }
            }, Evaluation_POOL.getExecutor());

            // 添加超时控制
            CompletableFuture<Void> timeoutFuture = future.orTimeout(TASK_TIMEOUT_MINUTES, TimeUnit.MINUTES)
                .exceptionally(throwable -> {
                    if (throwable instanceof TimeoutException) {
                        log.error("Task timeout for case: {}", data.getNum());
                        errorMap.put(data.getNum(), "Task timeout after " + TASK_TIMEOUT_MINUTES + " minutes");
                    }
                    return null;
                });

            allFutures.add(timeoutFuture);
        }

        // 等待所有任务完成
        CompletableFuture<Void> allOf = CompletableFuture.allOf(
            allFutures.toArray(new CompletableFuture[0])
        );

        try {
            allOf.get(TASK_TIMEOUT_MINUTES * 2, TimeUnit.MINUTES); // 总超时时间
        } catch (TimeoutException e) {
            log.error("Overall evaluation timeout", e);
            // 取消未完成的任务
            allFutures.forEach(f -> f.cancel(true));
        } catch (Exception e) {
            log.error("Error waiting for evaluation completion", e);
        }
    }

    /**
     * 方案2: 智能批处理 - 根据线程池状态动态调整批大小
     */
    private void processWithDynamicBatching(List<EvaluationData> evaluationData,
                                          String evaluationID,
                                          String userID,
                                          ConcurrentHashMap<Integer, String> errorMap,
                                          ConcurrentHashMap<Integer, String> case2Session) {

        int batchSize = calculateOptimalBatchSize();

        for (int i = 0; i < evaluationData.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, evaluationData.size());
            List<EvaluationData> batch = evaluationData.subList(i, endIndex);

            List<CompletableFuture<Void>> batchFutures = batch.stream()
                .map(data -> createEvaluationTask(data, evaluationID, userID, errorMap, case2Session))
                .collect(Collectors.toList());

            // 等待当前批次完成
            CompletableFuture.allOf(batchFutures.toArray(new CompletableFuture[0]))
                .join();

            // 可选: 添加批次间的短暂延迟，避免系统过载
            if (i + batchSize < evaluationData.size()) {
                try {
                    Thread.sleep(100); // 100ms延迟
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    break;
                }
            }
        }
    }

    private CompletableFuture<Void> createEvaluationTask(EvaluationData data,
                                                        String evaluationID,
                                                        String userID,
                                                        ConcurrentHashMap<Integer, String> errorMap,
                                                        ConcurrentHashMap<Integer, String> case2Session) {
        return CompletableFuture.runAsync(() -> {
            try {
                long userIDL = parseUserId(userID);
                String sessionID = evaluateOneCase(evaluationID, data, userIDL);
                case2Session.put(data.getNum(), sessionID);
            } catch (Exception e) {
                log.error("evaluateOneCase num:{} error", data.getNum(), e);
                errorMap.put(data.getNum(), e.getMessage());
            }
        }, Evaluation_POOL.getExecutor())
        .orTimeout(TASK_TIMEOUT_MINUTES, TimeUnit.MINUTES)
        .exceptionally(throwable -> {
            if (throwable instanceof TimeoutException) {
                log.error("Task timeout for case: {}", data.getNum());
                errorMap.put(data.getNum(), "Task timeout");
            }
            return null;
        });
    }

    private int calculateOptimalBatchSize() {
        // 根据线程池配置和系统负载动态计算批大小
        int corePoolSize = 10; // 从线程池配置获取
        int maxPoolSize = 200;

        // 简单策略: 使用核心线程数的2倍作为批大小
        return Math.min(corePoolSize * 2, MAX_CONCURRENT_TASKS);
    }

    private long parseUserId(String userID) {
        long userIDL = 5552418191L; // 默认值
        if (!Strings.isNullOrEmpty(userID)) {
            try {
                userIDL = Long.parseLong(userID);
            } catch (NumberFormatException e) {
                log.warn("Invalid user ID format: {}, using default", userID);
            }
        }
        return userIDL;
    }
}

