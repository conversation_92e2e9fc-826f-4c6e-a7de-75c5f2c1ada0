package com.sankuai.dzhealth.ai.service.dto;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.sankuai.dzhealth.ai.service.enums.StreamEventErrorTypeEnum;

/**
 * @author:chenwei
 * @time: 2025/3/20 12:52
 * @version: 0.0.1
 */

@ThriftStruct
public class SseAwareException extends RuntimeException{

    private String event;

    private String content;

    public SseAwareException() {
    }

    public SseAwareException(String event, String content) {
        this.event = event;
        this.content = content;
    }

    public SseAwareException(StreamEventErrorTypeEnum errorTypeEnum) {
        this.event = errorTypeEnum.getType();
        this.content = errorTypeEnum.getDesc();
    }


    @ThriftField(1)
    public String getEvent() {
        return this.event;
    }

    @ThriftField
    public void setEvent(String event) {
        this.event = event;
    }

    @ThriftField(2)
    public String getContent() {
        return this.content;
    }

    @ThriftField
    public void setContent(String content) {
        this.content = content;
    }
}
