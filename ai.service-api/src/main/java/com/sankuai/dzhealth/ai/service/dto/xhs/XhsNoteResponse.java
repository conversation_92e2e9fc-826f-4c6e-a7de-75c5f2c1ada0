package com.sankuai.dzhealth.ai.service.dto.xhs;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;

import java.io.Serializable;
import java.util.List;

/**
 * @author:chenwei
 * @time: 2025/6/16 16:19
 * @version: 0.0.1
 */

@ThriftStruct
public class XhsNoteResponse implements Serializable {

    @FieldDoc(description = "生成笔记的批次号")
    private String batch;

    @FieldDoc(description = "笔记标题")
    private List<String> titleList;

    @FieldDoc(description = "笔记副标题")
    private String subTitle;

    @FieldDoc(description = "笔记内容")
    private String content;

    @FieldDoc(description = "笔记要点摘要")
    private String keypoint;

    @FieldDoc(description = "生成状态：SUCCESS, FAILED")
    private String status;

    @FieldDoc(description = "错误信息（如果生成失败）")
    private String errorMessage;

    @FieldDoc(description = "业务场景码")
    private String bizCode;

    @FieldDoc(description = "话题")
    private List<String> topicList;


    @ThriftField(1)
    public String getBatch() {
        return this.batch;
    }

    @ThriftField
    public void setBatch(String batch) {
        this.batch = batch;
    }

    @ThriftField(2)
    public List<String> getTitleList() {
        return this.titleList;
    }

    @ThriftField
    public void setTitleList(List<String> titleList) {
        this.titleList = titleList;
    }

    @ThriftField(3)
    public String getSubTitle() {
        return this.subTitle;
    }

    @ThriftField
    public void setSubTitle(String subTitle) {
        this.subTitle = subTitle;
    }

    @ThriftField(4)
    public String getContent() {
        return this.content;
    }

    @ThriftField
    public void setContent(String content) {
        this.content = content;
    }

    @ThriftField(5)
    public String getKeypoint() {
        return this.keypoint;
    }

    @ThriftField
    public void setKeypoint(String keypoint) {
        this.keypoint = keypoint;
    }

    @ThriftField(6)
    public String getStatus() {
        return this.status;
    }

    @ThriftField
    public void setStatus(String status) {
        this.status = status;
    }

    @ThriftField(7)
    public String getErrorMessage() {
        return this.errorMessage;
    }

    @ThriftField
    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    @ThriftField(8)
    public String getBizCode() {
        return this.bizCode;
    }

    @ThriftField
    public void setBizCode(String bizCode) {
        this.bizCode = bizCode;
    }


    @ThriftField(9)
    public List<String> getTopicList() {
        return this.topicList;
    }

    @ThriftField
    public void setTopicList(List<String> topicList) {
        this.topicList = topicList;
    }

    @Override
    public String toString() {
        return "XhsNoteResponse{" +
                "batch='" + batch + '\'' +
                ", title='" + titleList + '\'' +
                ", subTitle='" + subTitle + '\'' +
                ", noteContent='" + content + '\'' +
                ", keypoint='" + keypoint + '\'' +
                ", topic='" + topicList + '\'' +
                ", status='" + status + '\'' +
                ", bizCode='" + bizCode + '\'' +
                ", errorMessage='" + errorMessage + '\'' +
                '}';
    }
}
