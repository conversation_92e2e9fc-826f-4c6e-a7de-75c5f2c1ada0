package com.sankuai.dzhealth.ai.service.api;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.mtrace.Tracer;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * @author:chenwei
 * @time: 2024/12/10 15:53
 * @version: 0.0.1
 */
@EqualsAndHashCode
@ThriftStruct
public class RemoteResponse<T> implements Serializable {

    private Integer code;

    private String msg;

    private Boolean success;

    private T data;

    private String traceId;

    public RemoteResponse() {
        traceId = Tracer.id();
    }

    public static <T> RemoteResponse<T> buildSuccess(T data) {
        RemoteResponse<T> response = new RemoteResponse<>();
        response.setCode(ResponseCode.SUCCESS.code);
        response.setMsg(ResponseCode.SUCCESS.msg);
        response.setSuccess(true);
        response.setData(data);
        return response;
    }

    public static <T> RemoteResponse<T> buildFail(String msg) {
        return buildFail(msg, null);
    }

    public static <T> RemoteResponse<T> buildFail(String msg, T data) {
        RemoteResponse<T> response = new RemoteResponse<>();
        response.setCode(ResponseCode.FAIL.code);
        response.setMsg(msg);
        response.setSuccess(false);
        response.setData(data);
        return response;
    }

    public static <T> RemoteResponse<T> buildIllegalArgument(String msg) {
        return buildIllegalArgument(msg, null);
    }

    public static <T> RemoteResponse<T> buildIllegalArgument(String msg, T data) {
        RemoteResponse<T> response = new RemoteResponse<>();
        response.setCode(ResponseCode.ILLEGAL_ARGUMENT.code);
        response.setMsg(msg);
        response.setSuccess(false);
        response.setData(data);
        return response;
    }


    @ThriftField(1)
    public Integer getCode() {
        return this.code;
    }

    @ThriftField
    public void setCode(Integer code) {
        this.code = code;
    }

    @ThriftField(2)
    public String getMsg() {
        return this.msg;
    }

    @ThriftField
    public void setMsg(String msg) {
        this.msg = msg;
    }

    @ThriftField(3)
    public Boolean getSuccess() {
        return this.success;
    }

    @ThriftField
    public void setSuccess(Boolean success) {
        this.success = success;
    }

    @ThriftField(4)
    public T getData() {
        return this.data;
    }

    @ThriftField
    public void setData(T data) {
        this.data = data;
    }


    @ThriftField(5)
    public String getTraceId() {
        return this.traceId;
    }

    @ThriftField
    public void setTraceId(String traceId) {
        this.traceId = traceId;
    }

    public enum ResponseCode {
        SUCCESS(200, "SUCCESS"),
        FAIL(500, "FAIL"),
        ILLEGAL_ARGUMENT(100, "ILLEGAL_ARGUMENT"),
        ;

        public final int code;

        public final String msg;

        ResponseCode(int code, String msg) {
            this.code = code;
            this.msg = msg;
        }
    }
}
