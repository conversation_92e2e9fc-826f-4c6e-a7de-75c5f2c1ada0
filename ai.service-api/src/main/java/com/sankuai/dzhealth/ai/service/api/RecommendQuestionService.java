package com.sankuai.dzhealth.ai.service.api;

import com.facebook.swift.service.ThriftService;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.meituan.servicecatalog.api.annotations.ParamDoc;
import com.sankuai.dzhealth.ai.service.dto.RecommendQuestionDTO;
import com.sankuai.dzhealth.ai.service.request.RecommendQuestionRequest;


/**
 * 推荐问题服务接口
 */
@ThriftService
public interface RecommendQuestionService {
    @MethodDoc(name = "获取推荐问题", description = "根据用户ID和可选的商品名称获取推荐问题列表",
            parameters = {@ParamDoc(name = "request", description = "推荐问题请求")})
    RemoteResponse<RecommendQuestionDTO> getRecommendQuestions(RecommendQuestionRequest request);
}

