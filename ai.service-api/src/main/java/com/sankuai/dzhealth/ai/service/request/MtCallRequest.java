package com.sankuai.dzhealth.ai.service.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;

@TypeDoc(description = "电话拨通")
@ThriftStruct
public class MtCallRequest {

    @FieldDoc(description = "任务Id")
    private Long taskId;

    @FieldDoc(description = "用户Id")
    private String userId;

    @FieldDoc(description = "会话Id")
    private String conversationId;

    @FieldDoc(description = "消息Id")
    private Long messageId;



    @ThriftField(1)
    public Long getTaskId() {
        return this.taskId;
    }

    @ThriftField
    public void setTaskId(Long taskId) {
        this.taskId = taskId;
    }

    @ThriftField(2)
    public String getUserId() {
        return this.userId;
    }

    @ThriftField
    public void setUserId(String userId) {
        this.userId = userId;
    }

    @ThriftField(3)
    public String getConversationId() {
        return this.conversationId;
    }

    @ThriftField
    public void setConversationId(String conversationId) {
        this.conversationId = conversationId;
    }

    @ThriftField(4)
    public Long getMessageId() {
        return this.messageId;
    }

    @ThriftField
    public void setMessageId(Long messageId) {
        this.messageId = messageId;
    }

}
