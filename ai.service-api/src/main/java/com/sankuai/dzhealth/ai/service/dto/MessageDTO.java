package com.sankuai.dzhealth.ai.service.dto;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import java.io.Serializable;

/**
 * @author:chenwei
 * @time: 2025/3/25 10:00
 * @version: 0.0.1
 */
@TypeDoc(description = "消息数据传输对象")
@ThriftStruct
public class MessageDTO implements Serializable {

    @FieldDoc(description = "消息ID")
    private Long msgId;

    @FieldDoc(description = "角色，如user、robot等")
    private String role;

    @FieldDoc(description = "点赞类型，0未操作，1赞，2踩")
    private Integer likeType;

    @FieldDoc(description = "消息内容")
    private String content;
    
    @ThriftField(1)
    public Long getMsgId() {
        return msgId;
    }
    
    @ThriftField
    public void setMsgId(Long msgId) {
        this.msgId = msgId;
    }
    
    @ThriftField(2)
    public String getRole() {
        return role;
    }
    
    @ThriftField
    public void setRole(String role) {
        this.role = role;
    }
    
    @ThriftField(3)
    public Integer getLikeType() {
        return likeType;
    }
    
    @ThriftField
    public void setLikeType(Integer likeType) {
        this.likeType = likeType;
    }
    
    @ThriftField(4)
    public String getContent() {
        return content;
    }
    
    @ThriftField
    public void setContent(String content) {
        this.content = content;
    }
}
