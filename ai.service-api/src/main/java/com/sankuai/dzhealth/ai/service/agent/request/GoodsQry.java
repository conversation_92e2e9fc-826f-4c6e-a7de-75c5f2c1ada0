package com.sankuai.dzhealth.ai.service.agent.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;

@ThriftStruct
public class GoodsQry {
    @ThriftField(1)
    private Long goodsId;
    @ThriftField(2)
    private String goodsType;
    @ThriftField(3)
    private GoodsShopQry shopQry;

    public boolean equals(Object o) {
        if (o == this) {
            return true;
        } else if (!(o instanceof GoodsQry)) {
            return false;
        } else {
            GoodsQry other = (GoodsQry)o;
            if (!other.canEqual(this)) {
                return false;
            } else {
                Object this$goodsId = this.getGoodsId();
                Object other$goodsId = other.getGoodsId();
                if (this$goodsId == null) {
                    if (other$goodsId != null) {
                        return false;
                    }
                } else if (!this$goodsId.equals(other$goodsId)) {
                    return false;
                }

                Object this$goodsType = this.getGoodsType();
                Object other$goodsType = other.getGoodsType();
                if (this$goodsType == null) {
                    if (other$goodsType != null) {
                        return false;
                    }
                } else if (!this$goodsType.equals(other$goodsType)) {
                    return false;
                }

                Object this$shopQry = this.getShopQry();
                Object other$shopQry = other.getShopQry();
                if (this$shopQry == null) {
                    if (other$shopQry != null) {
                        return false;
                    }
                } else if (!this$shopQry.equals(other$shopQry)) {
                    return false;
                }

                return true;
            }
        }
    }

    protected boolean canEqual(Object other) {
        return other instanceof GoodsQry;
    }

    public int hashCode() {
        int PRIME = 59;
        int result = 1;
        Object $goodsId = this.getGoodsId();
        result = result * 59 + ($goodsId == null ? 43 : $goodsId.hashCode());
        Object $goodsType = this.getGoodsType();
        result = result * 59 + ($goodsType == null ? 43 : $goodsType.hashCode());
        Object $shopQry = this.getShopQry();
        result = result * 59 + ($shopQry == null ? 43 : $shopQry.hashCode());
        return result;
    }

    public Long getGoodsId() {
        return this.goodsId;
    }

    public String getGoodsType() {
        return this.goodsType;
    }

    public GoodsShopQry getShopQry() {
        return this.shopQry;
    }

    public void setGoodsId(Long goodsId) {
        this.goodsId = goodsId;
    }

    public void setGoodsType(String goodsType) {
        this.goodsType = goodsType;
    }

    public void setShopQry(GoodsShopQry shopQry) {
        this.shopQry = shopQry;
    }
}