package com.sankuai.dzhealth.ai.service.agent.enums;

import lombok.Getter;

import java.util.Map;

/**
 * @author:chenwei
 * @time: 2025/7/10 10:50
 * @version: 0.0.1
 */

@Getter
public enum ContextExtraKey {

    BIZ_ID("bizId", "业务id", String.class),
    OPTION_INPUT("optionInput", "选项", String.class),
    APPOINTMENT_BUTTON("appointment_button", "预约按钮", String.class),
    APPOINTMENT_MSG_ID("appointment_msgId", "预约消息id", String.class),

    RECOMMEND_PARAM_ID("recommendParamId", "AI对话页的置顶id", String.class),

    RECOMMEND_DYNAMIC_FILTER("recommendDynamicFilter", "listing动态筛选tab", String.class),

    RECOMMEND_POSITION_FILTER("recommendPositionFilter", "listing地理筛选tab", String.class),

    PAGE_NO("pageNo", "分页页码", Integer.class),

    PAGE_SIZE("pageSize", "分页页大小", Integer.class),

    SKIN_REPORT_ID("skinReportId", "测肤报告id", String.class),

    SUPPLY_KEY_CARD_MAP("supplyKeyCardMap", "供给key与card的映射", Map.class),

    IMG_URLS("imgUrls", "图片信息", String.class),

    ASSESS_PICTURE_URLS("assessPictureUrls", "判别图片", Map.class),

    /**
     * {"areaid":"","stationid":"","distance":""}
     */
    FIlTER_PARAMS("filterParams", "地铁商圈选择", String.class),


    SUPPLY_ID_TITLE("supplyIdTitle", "<supplyId与标题映射>", Map.class),

    ASSESS_SUPPLY_IDS("assessSupplyIds", "判别供给ID映射", Map.class),

    PRODUCT_QA("productQa", "商品详情页问答对", String.class),

    SORT_TYPE("sortType", "智能排序筛选器", String.class),


    ;
    private String key;

    private String desc;

    private Class clazz;


    private ContextExtraKey(String key, String desc, Class clazz) {
        this.key = key;
        this.desc = desc;
        this.clazz = clazz;
    }
}
