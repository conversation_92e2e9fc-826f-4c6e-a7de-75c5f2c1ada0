package com.sankuai.dzhealth.ai.service.agent.dto;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;

import java.io.Serializable;

/**
 * @author:chenwei
 * @time: 2025/7/17 17:26
 * @version: 0.0.1
 */

@TypeDoc(description = "导航资源位")
@ThriftStruct
public class NavigateDTO implements Serializable {

    @FieldDoc(description = "icon图标")
    private String icon;

    @FieldDoc(description = "文本")
    private String text;

    @FieldDoc(description = "跳转链接")
    private String url;

    @FieldDoc(description = "1-跳链，2-当前对话问答")
    private Integer type;

    @FieldDoc(description = "需要发送的问题")
    private String query;


    @ThriftField(1)
    public String getIcon() {
        return this.icon;
    }

    @ThriftField
    public void setIcon(String icon) {
        this.icon = icon;
    }

    @ThriftField(2)
    public String getText() {
        return this.text;
    }

    @ThriftField
    public void setText(String text) {
        this.text = text;
    }

    @ThriftField(3)
    public String getUrl() {
        return this.url;
    }

    @ThriftField
    public void setUrl(String url) {
        this.url = url;
    }


    @ThriftField(4)
    public Integer getType() {
        return this.type;
    }

    @ThriftField
    public void setType(Integer type) {
        this.type = type;
    }

    @ThriftField(5)
    public String getQuery() {
        return this.query;
    }

    @ThriftField
    public void setQuery(String query) {
        this.query = query;
    }
}
