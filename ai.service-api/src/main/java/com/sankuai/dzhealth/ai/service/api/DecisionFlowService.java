package com.sankuai.dzhealth.ai.service.api;

import com.facebook.swift.service.ThriftMethod;
import com.facebook.swift.service.ThriftService;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.meituan.servicecatalog.api.annotations.ParamDoc;
import com.sankuai.dzhealth.ai.service.dto.decision.*;
import com.sankuai.dzhealth.ai.service.request.*;
import com.sankuai.dzhealth.ai.service.request.DecisionTreeSearchRequest;

import java.util.List;

/**
 * 决策树 RPC 服务接口
 */
@ThriftService
public interface DecisionFlowService {

    // ========== 核心功能接口 ==========

    /**
     * 根据当前节点和匹配值过滤并返回下一节点候选列表
     */
    @MethodDoc(name = "决策下一节点列表", description = "根据当前节点 nodeId 与 edgeValue 过滤返回所有候选子节点，已按 sortOrder 排序",
            parameters = {@ParamDoc(name = "request", description = "决策下一节点查询请求")})
    RemoteResponse<List<DecisionNodeDTO>> decideNext(DecideNextRequest request);

    /**
     * 查询节点下的推荐资源
     */
    @MethodDoc(name = "查询推荐资源", description = "返回指定节点下按 sortOrder 排序的 TOPN 资源",
            parameters = {@ParamDoc(name = "request", description = "查询推荐资源请求")})
    RemoteResponse<List<ResourceRecommendationDTO>> listTopResources(ListTopResourcesRequest request);

    /**
     * 批量导入决策网络（节点 + 路径 + 资源关联）
     */
    @MethodDoc(name = "导入决策网络", description = "批量写入节点、路径与资源关联",
            parameters = {@ParamDoc(name = "request", description = "导入请求体 DecisionFlowImportDTO")})
    RemoteResponse<ImportResultDTO> importFlow(DecisionFlowImportRequest request);

    /**
     * 删除指定业务场景的决策流（灰度删除）
     */
    @MethodDoc(name = "删除决策流", description = "灰度删除指定业务场景下的所有节点、路径与资源关联，标记为GRAY_DELETE状态",
            parameters = {@ParamDoc(name = "request", description = "删除决策流请求")})
    RemoteResponse<Boolean> deleteFlow(BizSceneRequest request);

    /**
     * 搜索决策节点 / 资源
     */
    @ThriftMethod
    @MethodDoc(name="决策流搜索", description="搜索决策流，仅返回决策节点组成的完整决策树列表的 JSON 字符串")
    RemoteResponse<List<DecisionFlowDTO>> search(DecisionTreeSearchRequest request);

    // ========== 图展示专用接口 ==========

    /**
     * 查询决策流概览信息
     */
    @MethodDoc(name = "查询决策流概览", description = "返回决策流的基本统计信息和入口节点；previewGray=true 时会合并 GRAY 数据",
            parameters = {@ParamDoc(name = "request", description = "查询决策流概览请求")})
    RemoteResponse<DecisionFlowSummaryDTO> queryFlowSummary(QueryFlowSummaryRequest request);

    /**
     * 查询决策树/子树
     */
    @MethodDoc(name = "查询决策树", description = "查询决策树，支持完整图和子树模式；previewGray=true 合并 GRAY 数据",
            parameters = {@ParamDoc(name = "request", description = "查询决策树请求")})
    RemoteResponse<DecisionFlowDTO> queryDecisionTree(DecisionTreeQueryRequest request);

    // ========== 新增：业务场景列表 ==========
    @MethodDoc(name = "业务场景列表", description = "列出所有业务场景及其版本状态 (status = EMPTY/ONLINE/GRAY_IN_PROGRESS)")
    RemoteResponse<List<BizSceneDTO>> listBizScenes();

    /**
     * 回滚灰度版本
     */
    @ThriftMethod
    @MethodDoc(name="回滚灰度", description="删除 bizScene 下所有 GRAY / GRAY_DELETE 数据，恢复线上 ONLINE",
              parameters={@ParamDoc(name="request",description="业务场景请求")})
    RemoteResponse<Boolean> rollbackGray(BizSceneRequest request);

    /**
     * 灰度转正式上线
     */
    @ThriftMethod
    @MethodDoc(name="灰度上线", description="将 GRAY 数据替换 ONLINE，并硬删 *_DELETE",
              parameters={@ParamDoc(name="request",description="业务场景请求")})
    RemoteResponse<Boolean> grayToOnline(BizSceneRequest request);
} 