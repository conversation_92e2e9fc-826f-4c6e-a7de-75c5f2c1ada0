package com.sankuai.dzhealth.ai.service.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @author:chenwei
 * @time: 2025/3/20 12:49
 * @version: 0.0.1
 */

@Getter
@AllArgsConstructor
public enum StreamEventTypeEnum {
    OPEN("open", "连接打开"),

    CLOSE("close", "连接关闭"),

    ERROR("error", "异常"),

    MESSAGE("message", "消息体"),

    SESSION("session", "会话"),

    PHONE("phone", "电话"),

    TIME("time", "时间")

    ;

    private final String type;
    private final String desc;
    public static StreamEventTypeEnum getByType(String type) {
        for (StreamEventTypeEnum typeEnum : values()) {
            if (typeEnum.getType().equals(type)) {
                return typeEnum;
            }
        }
        return null;
    }
}
