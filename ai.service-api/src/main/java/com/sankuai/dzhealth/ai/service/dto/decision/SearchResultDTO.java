package com.sankuai.dzhealth.ai.service.dto.decision;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.*;

import java.util.List;

// Node and Resource DTOs
import com.sankuai.dzhealth.ai.service.dto.decision.DecisionNodeDTO;
import com.sankuai.dzhealth.ai.service.dto.decision.ResourceRecommendationDTO;

@ThriftStruct
@TypeDoc(name="决策搜索结果")
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SearchResultDTO {

    @FieldDoc(name="节点列表")
    @Getter(onMethod_=@ThriftField(1))
    @Setter(onMethod_=@ThriftField)
    private List<DecisionNodeDTO> nodes;

    @FieldDoc(name="资源列表")
    @Getter(onMethod_=@ThriftField(2))
    @Setter(onMethod_=@ThriftField)
    private List<ResourceRecommendationDTO> resources;
} 