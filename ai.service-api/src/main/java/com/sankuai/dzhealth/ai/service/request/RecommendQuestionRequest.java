package com.sankuai.dzhealth.ai.service.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import lombok.*;

import java.io.Serializable;

/**
 * 推荐问题请求
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ThriftStruct
public class RecommendQuestionRequest implements Serializable {
    @FieldDoc(description = "用户id")
    @Getter(onMethod_ = @ThriftField(1))
    @Setter(onMethod_ = @ThriftField)
    private String userId;

    @FieldDoc(description = "商品名称")
    @Getter(onMethod_ = @ThriftField(2))
    @Setter(onMethod_ = @ThriftField)
    private String productName;

    @FieldDoc(name = "平台标识", description = "1:点评 2:美团，用于区分不同平台的深链接拼接")
    @Getter(onMethod_ = @ThriftField(value = 3))
    @Setter(onMethod_ = @ThriftField)
    private Integer platform;

    @FieldDoc(name = "商品类目ID", description = "506:口腔 850:医美,用来区分入口来源")
    @Getter(onMethod_ = @ThriftField(value = 4))
    @Setter(onMethod_ = @ThriftField)
    private String categoryId;
}

