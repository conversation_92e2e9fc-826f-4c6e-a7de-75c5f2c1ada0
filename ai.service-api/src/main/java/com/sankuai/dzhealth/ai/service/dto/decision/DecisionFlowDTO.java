package com.sankuai.dzhealth.ai.service.dto.decision;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.*;

import java.io.Serializable;
import java.util.List;

@ThriftStruct
@TypeDoc(name = "决策网络")
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class DecisionFlowDTO implements Serializable {

    @FieldDoc(name = "业务场景")
    @Getter(onMethod_ = @ThriftField(value = 1))
    @Setter(onMethod_ = @ThriftField)
    private String bizScene;

    @FieldDoc(name = "节点列表")
    @Getter(onMethod_ = @ThriftField(value = 2))
    @Setter(onMethod_ = @ThriftField)
    private List<DecisionNodeDTO> nodes;

    @FieldDoc(name = "路径列表")
    @Getter(onMethod_ = @ThriftField(value = 3))
    @Setter(onMethod_ = @ThriftField)
    private List<DecisionEdgeDTO> edges;

    @FieldDoc(name = "资源列表")
    @Getter(onMethod_ = @ThriftField(value = 4))
    @Setter(onMethod_ = @ThriftField)
    private List<ResourceRecommendationDTO> resources;

    @FieldDoc(name = "节点资源关联关系")
    @Getter(onMethod_ = @ThriftField(value = 5))
    @Setter(onMethod_ = @ThriftField)
    private List<NodeResourceRelationDTO> relations;

    @Getter(onMethod_ = @ThriftField(value = 6))
    @Setter(onMethod_ = @ThriftField)
    private String decisionTreeModelsJson;
} 