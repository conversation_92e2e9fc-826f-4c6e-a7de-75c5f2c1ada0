package com.sankuai.dzhealth.ai.service.dto;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import lombok.*;

import java.util.List;

/**
 * 推荐问题DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ThriftStruct
public class RecommendQuestionDTO {
    @FieldDoc(name = "项目词和功效部位词")
    @Getter(onMethod_ = @ThriftField(value = 1))
    @Setter(onMethod_ = @ThriftField)
    @ThriftField(1)
    public List<String> recommendItems;

    @FieldDoc(name = "推荐问题")
    @Getter(onMethod_ = @ThriftField(value = 2))
    @Setter(onMethod_ = @ThriftField)
    @ThriftField(2)
    public List<String> recommendQuestions;
}


