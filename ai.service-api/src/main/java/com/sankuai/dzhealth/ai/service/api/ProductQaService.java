package com.sankuai.dzhealth.ai.service.api;

import com.facebook.swift.service.ThriftMethod;
import com.facebook.swift.service.ThriftService;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.meituan.servicecatalog.api.annotations.ParamDoc;
import com.meituan.servicecatalog.api.annotations.ServiceDoc;
import com.sankuai.dzhealth.ai.service.dto.ProductQaDTO;
import com.sankuai.dzhealth.ai.service.request.ProductQaRequest;

/**
 * 商品详情页AI问答服务接口
 * 提供商品相关的AI生成问答内容
 *
 * @author: AI Service Team
 * @version: 1.0.0
 */
@ServiceDoc(appkey = "com.sankuai.dzhealth.ai.service",
        name = "商品详情页AI问答接口",
        description = "为商品详情页提供AI生成的常见问题及答案",
        scenarios = "商品详情页AI问答",
        notice = "支持医美和口腔两个行业的商品问答")
@InterfaceDoc(type = "octo.thrift.annotation",
        displayName = "商品详情页AI问答接口",
        description = "为商品详情页提供AI生成的常见问题及答案",
        scenarios = "商品详情页AI问答")
@ThriftService
public interface ProductQaService {

    /**
     * 获取商品问答列表
     * 根据商品信息生成相关的问答内容，包含有答案的详细问答和无答案的相关问题
     *
     * 功能说明：
     * 1. 支持医美(medical)和口腔(dental)两个业务线
     * 2. 根据商品类目生成个性化问答内容
     * 3. 返回的问答列表包含：
     *    - 有答案的问题：包含问题、答案、配图、医生信息等完整结构
     *    - 无答案的问题：仅包含问题内容，用于引导用户点击跳转
     * 4. 每个问题都包含跳转到AI助手的深链接
     * 5. 根据平台类型(美团/点评)生成对应的深链接
     *
     * @param request 商品问答请求参数
     * @return 商品问答响应，包含问答列表和AI助手入口信息
     */
    @MethodDoc(displayName = "获取商品问答列表",
            description = "为商品详情页提供AI生成的问答内容，支持医美和口腔行业的个性化问答推荐。" +
                    "业务场景：商品详情页AI问答模块；" +
                    "支持行业：医美、口腔；" +
                    "返回格式：统一问答列表，有答案的包含完整信息，无答案的仅包含问题和跳转链接；" +
                    "平台支持：美团APP(platform=2)、点评APP(platform=1)",
            parameters = {
                @ParamDoc(name = "request",
                        description = "商品问答请求参数，包含idType(入参Id类型)、productId(商品ID)、categoryId(类目ID)、platform(平台标识)等字段",
                        example = "{\"idType\":2,\"productId\":\"123456789\",\"categoryId\":\"853000001\",\"platform\":2}")
            },
            returnValueDescription = "返回包含问答列表的响应对象，每个问答项可能包含完整答案或仅包含",
            extensions = {})
    @ThriftMethod
    RemoteResponse<ProductQaDTO> getProductQaList(ProductQaRequest request);
}

