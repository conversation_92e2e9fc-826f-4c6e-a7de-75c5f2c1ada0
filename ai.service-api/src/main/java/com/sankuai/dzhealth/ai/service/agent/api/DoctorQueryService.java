package com.sankuai.dzhealth.ai.service.agent.api;


import com.facebook.swift.service.ThriftMethod;
import com.facebook.swift.service.ThriftService;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.meituan.servicecatalog.api.annotations.ParamDoc;
import com.meituan.servicecatalog.api.annotations.ServiceDoc;
import com.sankuai.dzhealth.ai.service.agent.request.DoctorInfoRequest;
import com.sankuai.dzhealth.ai.service.api.RemoteResponse;

/**
 * 医生信息AI友好的Thrift服务接口
 */
@ServiceDoc(appkey = "com.sankuai.dzhealth.ai.service",
        name = "DoctorQueryService",
        description = "医生信息查询接口",
        scenarios = "医生信息查询",
        notice = "医生信息查询接口")
@InterfaceDoc(type = "octo.thrift.annotation", displayName = "DoctorQueryService", description = "医生信息查询接口", scenarios = "医生信息查询")
@ThriftService()
public interface DoctorQueryService {

    /**
     * 根据医生ID列表查询医生信息（AI友好JSON格式）
     * @return JSON格式的医生信息
     */
    @MethodDoc(displayName = "查询医生详情", description = "根据医生ID列表查询医生信息（AI友好的JSON格式）", parameters = {
            @ParamDoc(name = "doctorInfoRequest", description = "请求参数")}, returnValueDescription = "返回医生信息，JSON格式")
    @ThriftMethod
    RemoteResponse<String> queryDoctorsForAI(DoctorInfoRequest doctorInfoRequest);
}

