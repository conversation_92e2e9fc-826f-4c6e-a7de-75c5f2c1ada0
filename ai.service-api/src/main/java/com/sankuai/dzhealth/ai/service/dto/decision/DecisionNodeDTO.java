package com.sankuai.dzhealth.ai.service.dto.decision;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.*;

import java.io.Serializable;

@ThriftStruct
@TypeDoc(name = "决策节点")
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class DecisionNodeDTO implements Serializable {

    @FieldDoc(name="业务场景")
    @Getter(onMethod_=@ThriftField(value = 1))
    @Setter(onMethod_=@ThriftField)
    private String bizScene;

    @FieldDoc(name="节点业务ID")
    @Getter(onMethod_=@ThriftField(value = 2))
    @Setter(onMethod_=@ThriftField)
    private String nodeId;

    @FieldDoc(name = "节点名称")
    @Getter(onMethod_ = @ThriftField(value = 3))
    @Setter(onMethod_ = @ThriftField)
    private String nodeName;

    @FieldDoc(name = "判别文案")
    @Getter(onMethod_ = @ThriftField(value = 4))
    @Setter(onMethod_ = @ThriftField)
    private String assessmentText;

    @FieldDoc(name = "示例图URL")
    @Getter(onMethod_ = @ThriftField(value = 5))
    @Setter(onMethod_ = @ThriftField)
    private String assessmentImg;

    @FieldDoc(name = "指导文案")
    @Getter(onMethod_ = @ThriftField(value = 6))
    @Setter(onMethod_ = @ThriftField)
    private String guidanceText;

    @FieldDoc(name = "状态")
    @Getter(onMethod_ = @ThriftField(value = 7))
    @Setter(onMethod_ = @ThriftField)
    private String status;

    @FieldDoc(name = "是否需要推供给：1-是 0-否，决定下一层的需求中有没有推荐供给模块 (默认 1)")
    @Getter(onMethod_ = @ThriftField(value = 8))
    @Setter(onMethod_ = @ThriftField)
    private Boolean needSupply;

    @FieldDoc(name = "是否出医生：1-是 0-否 (默认 0)")
    @Getter(onMethod_ = @ThriftField(value = 9))
    @Setter(onMethod_ = @ThriftField)
    private Boolean needDoctor;
} 