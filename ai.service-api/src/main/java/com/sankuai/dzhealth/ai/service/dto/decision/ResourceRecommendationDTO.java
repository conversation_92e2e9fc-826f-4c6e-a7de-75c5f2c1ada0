package com.sankuai.dzhealth.ai.service.dto.decision;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.*;

import java.io.Serializable;

/**
 * 资源推荐 DTO
 */
@ThriftStruct
@ToString
@EqualsAndHashCode
@TypeDoc(name = "资源推荐")
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ResourceRecommendationDTO implements Serializable {

    @FieldDoc(name = "业务场景")
    @Getter(onMethod_ = @ThriftField(value = 1))
    @Setter(onMethod_ = @ThriftField)
    private String bizScene;

    @FieldDoc(name = "资源业务ID")
    @Getter(onMethod_ = @ThriftField(value = 2))
    @Setter(onMethod_ = @ThriftField)
    private String resourceId;

    @FieldDoc(name = "资源类型")
    @Getter(onMethod_ = @ThriftField(value = 3))
    @Setter(onMethod_ = @ThriftField)
    private String resourceType;

    @FieldDoc(name = "资源名称")
    @Getter(onMethod_ = @ThriftField(value = 4))
    @Setter(onMethod_ = @ThriftField)
    private String resourceName;

    @FieldDoc(name = "简要描述")
    @Getter(onMethod_ = @ThriftField(value = 5))
    @Setter(onMethod_ = @ThriftField)
    private String shortDesc;

    @FieldDoc(name = "结构化属性")
    @Getter(onMethod_ = @ThriftField(value = 6))
    @Setter(onMethod_ = @ThriftField)
    private String attributes;

    @FieldDoc(name = "标签JSON")
    @Getter(onMethod_ = @ThriftField(value = 7))
    @Setter(onMethod_ = @ThriftField)
    private String tags;
} 