package com.sankuai.dzhealth.ai.service.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.dzhealth.ai.service.dto.decision.DecisionEdgeDTO;
import com.sankuai.dzhealth.ai.service.dto.decision.DecisionNodeDTO;
import com.sankuai.dzhealth.ai.service.dto.decision.NodeResourceRelationDTO;
import com.sankuai.dzhealth.ai.service.dto.decision.ResourceRecommendationDTO;
import lombok.*;

import java.util.List;

@ThriftStruct
@TypeDoc(name="决策网络导入请求")
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class DecisionFlowImportRequest {
    @FieldDoc(name="业务场景")
    @Getter(onMethod_=@ThriftField(1))
    @Setter(onMethod_=@ThriftField)
    private String bizScene;

    @FieldDoc(name="节点列表")
    @Getter(onMethod_=@ThriftField(2))
    @Setter(onMethod_=@ThriftField)
    private List<DecisionNodeDTO> nodes;

    @FieldDoc(name="路径列表")
    @Getter(onMethod_=@ThriftField(3))
    @Setter(onMethod_=@ThriftField)
    private List<DecisionEdgeDTO> edges;

    @FieldDoc(name="资源关联列表")
    @Getter(onMethod_=@ThriftField(4))
    @Setter(onMethod_=@ThriftField)
    private List<NodeResourceRelationDTO> relations;

    @FieldDoc(name="资源列表")
    @Getter(onMethod_=@ThriftField(5))
    @Setter(onMethod_=@ThriftField)
    private List<ResourceRecommendationDTO> resources;

    @FieldDoc(name="待删除的节点ID列表")
    @Getter(onMethod_=@ThriftField(6))
    @Setter(onMethod_=@ThriftField)
    private List<String> deletedNodeIds;

    @FieldDoc(name="待删除的边ID列表")
    @Getter(onMethod_=@ThriftField(7))
    @Setter(onMethod_=@ThriftField)
    private List<String> deletedEdgeIds;

    @FieldDoc(name="待删除的关联关系ID列表")
    @Getter(onMethod_=@ThriftField(8))
    @Setter(onMethod_=@ThriftField)
    private List<Long> deletedRelationIds;

    @FieldDoc(name="待删除的资源ID列表")
    @Getter(onMethod_=@ThriftField(9))
    @Setter(onMethod_=@ThriftField)
    private List<String> deletedResourceIds;
} 