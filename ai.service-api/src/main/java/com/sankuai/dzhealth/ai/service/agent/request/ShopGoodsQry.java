package com.sankuai.dzhealth.ai.service.agent.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;

@ThriftStruct
public class ShopGoodsQry {
    @ThriftField(1)
    private Long goodsId;
    @ThriftField(2)
    private String goodsType;

    public boolean equals(Object o) {
        if (o == this) {
            return true;
        } else if (!(o instanceof ShopGoodsQry)) {
            return false;
        } else {
            ShopGoodsQry other = (ShopGoodsQry)o;
            if (!other.canEqual(this)) {
                return false;
            } else {
                Object this$goodsId = this.getGoodsId();
                Object other$goodsId = other.getGoodsId();
                if (this$goodsId == null) {
                    if (other$goodsId != null) {
                        return false;
                    }
                } else if (!this$goodsId.equals(other$goodsId)) {
                    return false;
                }

                Object this$goodsType = this.getGoodsType();
                Object other$goodsType = other.getGoodsType();
                if (this$goodsType == null) {
                    if (other$goodsType != null) {
                        return false;
                    }
                } else if (!this$goodsType.equals(other$goodsType)) {
                    return false;
                }

                return true;
            }
        }
    }

    protected boolean canEqual(Object other) {
        return other instanceof ShopGoodsQry;
    }

    public int hashCode() {
        int PRIME = 59;
        int result = 1;
        Object $goodsId = this.getGoodsId();
        result = result * 59 + ($goodsId == null ? 43 : $goodsId.hashCode());
        Object $goodsType = this.getGoodsType();
        result = result * 59 + ($goodsType == null ? 43 : $goodsType.hashCode());
        return result;
    }

    public Long getGoodsId() {
        return this.goodsId;
    }

    public String getGoodsType() {
        return this.goodsType;
    }

    public void setGoodsId(Long goodsId) {
        this.goodsId = goodsId;
    }

    public void setGoodsType(String goodsType) {
        this.goodsType = goodsType;
    }
}
