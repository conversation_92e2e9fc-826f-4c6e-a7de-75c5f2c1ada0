package com.sankuai.dzhealth.ai.service.agent.dto;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;

import java.io.Serializable;
import java.util.List;

/**
 * @author:chenwei
 * @time: 2025/7/17 17:30
 * @version: 0.0.1
 */
@TypeDoc(description = "导航资源位")
@ThriftStruct
public class NavigateDTOResponse implements Serializable {

    @FieldDoc(description = "导航资源位list")
    private List<NavigateDTO> navigateDTOList;


    @ThriftField(1)
    public List<NavigateDTO> getNavigateDTOList() {
        return this.navigateDTOList;
    }

    @ThriftField
    public void setNavigateDTOList(List<NavigateDTO> navigateDTOList) {
        this.navigateDTOList = navigateDTOList;
    }
}
