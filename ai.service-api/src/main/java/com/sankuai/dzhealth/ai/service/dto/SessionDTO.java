package com.sankuai.dzhealth.ai.service.dto;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import java.io.Serializable;

/**
 * @author:chenwei
 * @time: 2025/3/25 09:54
 * @version: 0.0.1
 */
@TypeDoc(description = "会话数据传输对象")
@ThriftStruct
public class SessionDTO implements Serializable {

    @FieldDoc(description = "会话ID")
    private Long sessionId;

    @FieldDoc(description = "会话标题")
    private String title;

    @FieldDoc(description = "会话日期时间")
    private String dateTime;

    @FieldDoc(description = "明确的时间表示")
    private String explicitTime;

    @FieldDoc(description = "消息跳链")
    private String sessionUrl;
    
    @ThriftField(1)
    public Long getSessionId() {
        return sessionId;
    }
    
    @ThriftField
    public void setSessionId(Long sessionId) {
        this.sessionId = sessionId;
    }
    
    @ThriftField(2)
    public String getTitle() {
        return title;
    }
    
    @ThriftField
    public void setTitle(String title) {
        this.title = title;
    }
    
    @ThriftField(3)
    public String getDateTime() {
        return dateTime;
    }
    
    @ThriftField
    public void setDateTime(String dateTime) {
        this.dateTime = dateTime;
    }
    
    @ThriftField(4)
    public String getExplicitTime() {
        return explicitTime;
    }
    
    @ThriftField
    public void setExplicitTime(String explicitTime) {
        this.explicitTime = explicitTime;
    }


    @ThriftField(5)
    public String getSessionUrl() {
        return this.sessionUrl;
    }

    @ThriftField
    public void setSessionUrl(String sessionUrl) {
        this.sessionUrl = sessionUrl;
    }
}
