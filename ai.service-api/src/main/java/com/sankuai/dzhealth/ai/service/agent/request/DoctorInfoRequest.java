package com.sankuai.dzhealth.ai.service.agent.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

@TypeDoc(
        description = "医生信息请求"
)
@ThriftStruct
public class DoctorInfoRequest implements Serializable {
    @FieldDoc(description = "融合医生id列表（最多20个）")
    @Getter(onMethod_ = @ThriftField(1))
    @Setter(onMethod_ = @ThriftField())
    private List<Long> mergeDoctorIds;


    @FieldDoc(description = "平台：1-点评，2-美团")
    @Getter(onMethod_ = @ThriftField(2))
    @Setter(onMethod_ = @ThriftField())
    private Integer platform;


    @FieldDoc(description = "用户Id，区分美团/点评")
    @Getter(onMethod_ = @ThriftField(3))
    @Setter(onMethod_ = @ThriftField())
    private Long userId;
}
