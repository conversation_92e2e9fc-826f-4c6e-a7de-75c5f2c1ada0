package com.sankuai.dzhealth.ai.service.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR> <PERSON><PERSON><PERSON>
 * @version : 0.1
 * @since : 2025/4/8
 */
@ThriftStruct
public class WebSearchRequest implements Serializable {

    @FieldDoc(name = "搜索关键词")
    private String query;

    @FieldDoc(name = "搜索域名范围")
    private List<String> sites;

    @FieldDoc(name = "召回文档数量")
    private Integer topK;

    @ThriftField(1)
    public String getQuery() {
        return this.query;
    }

    @ThriftField
    public void setQuery(String query) {
        this.query = query;
    }

    @ThriftField(2)
    public List<String> getSites() {
        return this.sites;
    }

    @ThriftField
    public void setSites(List<String> sites) {
        this.sites = sites;
    }

    @ThriftField(3)
    public Integer getTopK() {
        return this.topK;
    }

    @ThriftField
    public void setTopK(Integer topK) {
        this.topK = topK;
    }
}
