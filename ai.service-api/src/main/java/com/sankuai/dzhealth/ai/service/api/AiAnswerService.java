package com.sankuai.dzhealth.ai.service.api;

import com.facebook.swift.service.ThriftMethod;
import com.facebook.swift.service.ThriftService;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.meituan.servicecatalog.api.annotations.ParamDoc;
import com.meituan.servicecatalog.api.annotations.ServiceDoc;
import com.sankuai.dzhealth.ai.service.dto.MessageResponse;
import com.sankuai.dzhealth.ai.service.dto.SessionResponse;
import com.sankuai.dzhealth.ai.service.dto.StatusDTO;
import com.sankuai.dzhealth.ai.service.request.SessionRequest;

/**
 * @author:chenwei
 * @time: 2025/3/19 11:13
 * @version: 0.0.1
 */

@ServiceDoc(appkey = "com.sankuai.dzhealth.ai.service",
        name = "AI接口",
        description = "AI接口",
        scenarios = "AI接口",
        notice = "AI接口")
@InterfaceDoc(type = "octo.thrift.annotation", displayName = "AI接口", description = "AI接口", scenarios = "AI接口")
@ThriftService
public interface AiAnswerService {

    @MethodDoc(displayName = "查询历史会话列表", description = "查询历史会话列表", parameters = {
            @ParamDoc(name = "request", description = "请求参数", example = {})}, returnValueDescription = "返回会话列表", extensions = {})
    @ThriftMethod
    RemoteResponse<SessionResponse> queryHistorySessions(SessionRequest request);

    @MethodDoc(displayName = "查询会话消息列表", description = "根据会话ID查询消息列表", parameters = {
            @ParamDoc(name = "request", description = "请求参数", example = {})}, returnValueDescription = "返回消息列表", extensions = {})
    @ThriftMethod
    RemoteResponse<MessageResponse> querySessionMessages(SessionRequest request);

    @MethodDoc(displayName = "结束会话", description = "结束指定会话", parameters = {
            @ParamDoc(name = "request", description = "请求参数", example = {})}, returnValueDescription = "返回操作状态", extensions = {})
    @ThriftMethod
    RemoteResponse<StatusDTO> finishSession(SessionRequest request);

    @MethodDoc(displayName = "操作消息", description = "对消息进行操作，如点赞、点踩等", parameters = {
            @ParamDoc(name = "request", description = "请求参数", example = {})}, returnValueDescription = "返回操作状态", extensions = {})
    @ThriftMethod
    RemoteResponse<StatusDTO> operateMessage(SessionRequest request);

    @MethodDoc(displayName = "删除会话", description = "删除指定会话", parameters = {
            @ParamDoc(name = "request", description = "请求参数", example = {})}, returnValueDescription = "返回操作状态", extensions = {})
    @ThriftMethod
    RemoteResponse<StatusDTO> deleteSession(SessionRequest request);
}
