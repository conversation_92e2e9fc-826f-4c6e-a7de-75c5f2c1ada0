package com.sankuai.dzhealth.ai.service.agent.dto;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;

import java.io.Serializable;
import java.util.List;

/**
 * @author:chenwei
 * @time: 2025/7/10 10:22
 * @version: 0.0.1
 */
@TypeDoc(description = "消息响应")
@ThriftStruct
public class ChatMessageResponse implements Serializable {

    @FieldDoc(description = "消息列表")
    private List<ChatMessageDTO> messageList;

    @FieldDoc(description = "会话id")
    private String sessionId;

    @FieldDoc(description = "点赞是否成功")
    private Boolean likeStatus;


    @ThriftField(1)
    public List<ChatMessageDTO> getMessageList() {
        return this.messageList;
    }

    @ThriftField
    public void setMessageList(List<ChatMessageDTO> messageList) {
        this.messageList = messageList;
    }

    @ThriftField(2)
    public String getSessionId() {
        return this.sessionId;
    }

    @ThriftField
    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }


    @ThriftField(3)
    public Boolean getLikeStatus() {
        return this.likeStatus;
    }

    @ThriftField
    public void setLikeStatus(Boolean likeStatus) {
        this.likeStatus = likeStatus;
    }
}
