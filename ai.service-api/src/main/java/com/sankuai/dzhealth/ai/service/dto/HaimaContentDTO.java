package com.sankuai.dzhealth.ai.service.dto;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.Map;

/**
 * 海马内容数据传输对象
 * 用于在 Thrift 接口中传输 HaimaContent 对象
 * 海马是美团内部的运营位配置系统，用于管理各种运营配置内容
 */
@ThriftStruct
@TypeDoc(description = "海马内容数据传输对象")
public class HaimaContentDTO {

    /**
     * 配置ID
     * 标识该内容所属的配置项ID
     */
    @FieldDoc(description = "配置ID，标识该内容所属的配置项ID")
    @Getter(onMethod_ = @ThriftField(value = 1))
    @Setter(onMethod_ = @ThriftField)
    private int configId;

    /**
     * 内容ID
     * 标识具体内容项的唯一ID
     */
    @FieldDoc(description = "内容ID，标识具体内容项的唯一ID")
    @Getter(onMethod_ = @ThriftField(value = 2))
    @Setter(onMethod_ = @ThriftField)
    private int contentId;

    /**
     * 扩展JSON
     * 存储内容的详细信息，JSON格式字符串
     */
    @FieldDoc(description = "扩展JSON，存储内容的详细信息，JSON格式字符串")
    @Getter(onMethod_ = @ThriftField(value = 3))
    @Setter(onMethod_ = @ThriftField)
    private String extJson;

    /**
     * 内容映射
     * 将extJson解析后的键值对映射，方便客户端直接使用
     */
    @FieldDoc(description = "内容映射，将extJson解析后的键值对映射，方便客户端直接使用")
    @Getter(onMethod_ = @ThriftField(value = 4))
    @Setter(onMethod_ = @ThriftField)
    private Map<String, String> contentMap;

    /**
     * 创建时间
     * 内容创建的时间戳
     */
    @FieldDoc(description = "创建时间，内容创建的时间戳")
    @Getter(onMethod_ = @ThriftField(value = 5))
    @Setter(onMethod_ = @ThriftField)
    private Date createTime;

    /**
     * 更新时间
     * 内容最后更新的时间戳
     */
    @FieldDoc(description = "更新时间，内容最后更新的时间戳")
    @Getter(onMethod_ = @ThriftField(value = 6))
    @Setter(onMethod_ = @ThriftField)
    private Date updateTime;

    public HaimaContentDTO() {
    }
}