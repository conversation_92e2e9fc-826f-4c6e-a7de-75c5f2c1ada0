package com.sankuai.dzhealth.ai.service.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @author:chenwei
 * @time: 2025/3/20 12:53
 * @version: 0.0.1
 */
@Getter
@AllArgsConstructor
public enum StreamEventErrorTypeEnum {
    SUCCESS("success", "成功"),

    UN_LOGIN("unLogin", "未登录"),

    PARAM_ERROR("paramError", "参数异常"),

    NO_RIGHT("noRight", "无权限"),

    SERVER_ERROR("serverError", "服务异常"),

    SESSION_NOT_FOUND_ERROR("sessionNotFoundError", "检查会话ID"),

    AUDIT_ERROR("auditError", "审核异常"),

    SSE_BUFFER_ERROR("sseBufferError", "缓冲对象为空"),

    SSE_ERROR("sseError", "sse发送消息失败"),



    ;

    private final String type;

    private final String desc;

    public static StreamEventErrorTypeEnum getByErrorCode(int code) {
        if (code == 450) {
            return AUDIT_ERROR;
        }
        return SERVER_ERROR;
    }
}
