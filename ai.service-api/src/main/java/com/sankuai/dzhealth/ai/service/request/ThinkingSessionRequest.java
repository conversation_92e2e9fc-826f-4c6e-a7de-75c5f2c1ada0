package com.sankuai.dzhealth.ai.service.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.*;

import java.io.Serializable;
import java.util.Map;

/**
 * 思考会话请求
 * 用于RPC接口传输
 */
@ThriftStruct
@EqualsAndHashCode
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ToString
@TypeDoc(name = "思考会话请求")
public class ThinkingSessionRequest implements Serializable {

    @FieldDoc(name = "查询问题")
    @Getter(onMethod_ = @ThriftField(value = 1))
    @Setter(onMethod_ = @ThriftField)
    private String query;
    
    /**
     * 元数据 - 可存储任何领域特定的参数
     * 例如模板ID、组织ID等
     */
    @FieldDoc(name = "元数据", description = "可存储任何领域特定的参数，例如模板ID、组织ID等")
    @Getter(onMethod_ = @ThriftField(value = 2))
    @Setter(onMethod_ = @ThriftField)
    private Map<String, String> metadata;
    
    /**
     * 业务参数
     */
    @FieldDoc(name = "业务参数", description = "业务相关的参数配置")
    @Getter(onMethod_ = @ThriftField(value = 3))
    @Setter(onMethod_ = @ThriftField)
    private Map<String, String> businessParams;
} 