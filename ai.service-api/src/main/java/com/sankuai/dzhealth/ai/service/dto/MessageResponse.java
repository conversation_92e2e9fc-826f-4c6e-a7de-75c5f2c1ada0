package com.sankuai.dzhealth.ai.service.dto;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import java.io.Serializable;
import java.util.List;

/**
 * @author:chenwei
 * @time: 2025/3/25 10:01
 * @version: 0.0.1
 */
@TypeDoc(description = "消息列表响应")
@ThriftStruct
public class MessageResponse implements Serializable {

    @FieldDoc(description = "消息列表")
    private List<MessageDTO> messageList;
    
    @ThriftField(1)
    public List<MessageDTO> getMessageList() {
        return messageList;
    }
    
    @ThriftField
    public void setMessageList(List<MessageDTO> messageList) {
        this.messageList = messageList;
    }
}
