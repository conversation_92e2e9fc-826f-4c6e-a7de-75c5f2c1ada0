package com.sankuai.dzhealth.ai.service.agent.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import java.util.List;

@ThriftStruct
public class FillInfoRequest {
    @ThriftField(1)
    private List<ShopQry> shopQryList;
    @ThriftField(2)
    private List<GoodsQry> goodsQryList;
    @ThriftField(3)
    private Long userId;
    @ThriftField(4)
    private Integer platform;
    @ThriftField(5)
    private Integer cityId;
    @ThriftField(6)
    private Double lat;
    @ThriftField(7)
    private Double lng;
    @ThriftField(8)
    private String templateKey;
    @ThriftField(9)
    private String uuid;
    @ThriftField(10)
    private String appVersion;
    @ThriftField(11)
    private String os;

    public boolean equals(Object o) {
        if (o == this) {
            return true;
        } else if (!(o instanceof FillInfoRequest)) {
            return false;
        } else {
            FillInfoRequest other = (FillInfoRequest)o;
            if (!other.canEqual(this)) {
                return false;
            } else {
                Object this$shopQryList = this.getShopQryList();
                Object other$shopQryList = other.getShopQryList();
                if (this$shopQryList == null) {
                    if (other$shopQryList != null) {
                        return false;
                    }
                } else if (!this$shopQryList.equals(other$shopQryList)) {
                    return false;
                }

                Object this$goodsQryList = this.getGoodsQryList();
                Object other$goodsQryList = other.getGoodsQryList();
                if (this$goodsQryList == null) {
                    if (other$goodsQryList != null) {
                        return false;
                    }
                } else if (!this$goodsQryList.equals(other$goodsQryList)) {
                    return false;
                }

                Object this$userId = this.getUserId();
                Object other$userId = other.getUserId();
                if (this$userId == null) {
                    if (other$userId != null) {
                        return false;
                    }
                } else if (!this$userId.equals(other$userId)) {
                    return false;
                }

                Object this$platform = this.getPlatform();
                Object other$platform = other.getPlatform();
                if (this$platform == null) {
                    if (other$platform != null) {
                        return false;
                    }
                } else if (!this$platform.equals(other$platform)) {
                    return false;
                }

                Object this$cityId = this.getCityId();
                Object other$cityId = other.getCityId();
                if (this$cityId == null) {
                    if (other$cityId != null) {
                        return false;
                    }
                } else if (!this$cityId.equals(other$cityId)) {
                    return false;
                }

                Object this$lat = this.getLat();
                Object other$lat = other.getLat();
                if (this$lat == null) {
                    if (other$lat != null) {
                        return false;
                    }
                } else if (!this$lat.equals(other$lat)) {
                    return false;
                }

                Object this$lng = this.getLng();
                Object other$lng = other.getLng();
                if (this$lng == null) {
                    if (other$lng != null) {
                        return false;
                    }
                } else if (!this$lng.equals(other$lng)) {
                    return false;
                }

                Object this$templateKey = this.getTemplateKey();
                Object other$templateKey = other.getTemplateKey();
                if (this$templateKey == null) {
                    if (other$templateKey != null) {
                        return false;
                    }
                } else if (!this$templateKey.equals(other$templateKey)) {
                    return false;
                }

                Object this$uuid = this.getUuid();
                Object other$uuid = other.getUuid();
                if (this$uuid == null) {
                    if (other$uuid != null) {
                        return false;
                    }
                } else if (!this$uuid.equals(other$uuid)) {
                    return false;
                }

                Object this$appVersion = this.getAppVersion();
                Object other$appVersion = other.getAppVersion();
                if (this$appVersion == null) {
                    if (other$appVersion != null) {
                        return false;
                    }
                } else if (!this$appVersion.equals(other$appVersion)) {
                    return false;
                }

                Object this$os = this.getOs();
                Object other$os = other.getOs();
                if (this$os == null) {
                    if (other$os != null) {
                        return false;
                    }
                } else if (!this$os.equals(other$os)) {
                    return false;
                }

                return true;
            }
        }
    }

    protected boolean canEqual(Object other) {
        return other instanceof FillInfoRequest;
    }

    public int hashCode() {
        int PRIME = 59;
        int result = 1;
        Object $shopQryList = this.getShopQryList();
        result = result * 59 + ($shopQryList == null ? 43 : $shopQryList.hashCode());
        Object $goodsQryList = this.getGoodsQryList();
        result = result * 59 + ($goodsQryList == null ? 43 : $goodsQryList.hashCode());
        Object $userId = this.getUserId();
        result = result * 59 + ($userId == null ? 43 : $userId.hashCode());
        Object $platform = this.getPlatform();
        result = result * 59 + ($platform == null ? 43 : $platform.hashCode());
        Object $cityId = this.getCityId();
        result = result * 59 + ($cityId == null ? 43 : $cityId.hashCode());
        Object $lat = this.getLat();
        result = result * 59 + ($lat == null ? 43 : $lat.hashCode());
        Object $lng = this.getLng();
        result = result * 59 + ($lng == null ? 43 : $lng.hashCode());
        Object $templateKey = this.getTemplateKey();
        result = result * 59 + ($templateKey == null ? 43 : $templateKey.hashCode());
        Object $uuid = this.getUuid();
        result = result * 59 + ($uuid == null ? 43 : $uuid.hashCode());
        Object $appVersion = this.getAppVersion();
        result = result * 59 + ($appVersion == null ? 43 : $appVersion.hashCode());
        Object $os = this.getOs();
        result = result * 59 + ($os == null ? 43 : $os.hashCode());
        return result;
    }

    public List<ShopQry> getShopQryList() {
        return this.shopQryList;
    }

    public List<GoodsQry> getGoodsQryList() {
        return this.goodsQryList;
    }

    public Long getUserId() {
        return this.userId;
    }

    public Integer getPlatform() {
        return this.platform;
    }

    public Integer getCityId() {
        return this.cityId;
    }

    public Double getLat() {
        return this.lat;
    }

    public Double getLng() {
        return this.lng;
    }

    public String getTemplateKey() {
        return this.templateKey;
    }

    public String getUuid() {
        return this.uuid;
    }

    public String getAppVersion() {
        return this.appVersion;
    }

    public String getOs() {
        return this.os;
    }

    public void setShopQryList(List<ShopQry> shopQryList) {
        this.shopQryList = shopQryList;
    }

    public void setGoodsQryList(List<GoodsQry> goodsQryList) {
        this.goodsQryList = goodsQryList;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public void setPlatform(Integer platform) {
        this.platform = platform;
    }

    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    public void setLat(Double lat) {
        this.lat = lat;
    }

    public void setLng(Double lng) {
        this.lng = lng;
    }

    public void setTemplateKey(String templateKey) {
        this.templateKey = templateKey;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public void setAppVersion(String appVersion) {
        this.appVersion = appVersion;
    }

    public void setOs(String os) {
        this.os = os;
    }

    public String toString() {
        return "FillInfoRequest(shopQryList=" + this.getShopQryList() + ", goodsQryList=" + this.getGoodsQryList() + ", userId=" + this.getUserId() + ", platform=" + this.getPlatform() + ", cityId=" + this.getCityId() + ", lat=" + this.getLat() + ", lng=" + this.getLng() + ", templateKey=" + this.getTemplateKey() + ", uuid=" + this.getUuid() + ", appVersion=" + this.getAppVersion() + ", os=" + this.getOs() + ")";
    }
}
