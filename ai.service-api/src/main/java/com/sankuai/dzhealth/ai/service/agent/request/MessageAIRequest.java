package com.sankuai.dzhealth.ai.service.agent.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.dzhealth.ai.service.request.common.BasicParam;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * @author:chenwei
 * @time: 2025/7/6 10:57
 * @version: 0.0.1
 */

@TypeDoc(
        description = "请求"
)
@FieldDoc(description = "请求")
@ThriftStruct
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MessageAIRequest implements Serializable {

    @FieldDoc(description = "会话ID，用于关联多轮对话上下文")
    private String sessionId;

    @FieldDoc(description = "平台")
    private Integer platform;
    
    @FieldDoc(description = "基本参数")
    private BasicParam basicParam;
    @FieldDoc(description = "用户本轮输入的内容")
    private String userQuery;

    @FieldDoc(description = "业务类型，用于业务隔离和逻辑路由，例如：BEAUTY_CONSULTATION")
    private String bizType;
    
    @FieldDoc(description = "业务ID")
    private String bizId;
    
    @FieldDoc(description = "请求时间")
    private String requestTime;
    
    @FieldDoc(description = "角色")
    private String role;
    
    @FieldDoc(description = "图片URL列表")
    private List<String> imgUrls;

    @FieldDoc(description = "其他扩展参数，key为BUTTON")
    private Map<String, String> extraParams;

    @FieldDoc(description = "来源，评测还是正常请求")
    private String querySource;


    @ThriftField(1)
    public String getSessionId() {
        return this.sessionId;
    }

    @ThriftField
    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }

    @ThriftField(2)
    public Integer getPlatform() {
        return this.platform;
    }

    @ThriftField
    public void setPlatform(Integer platform) {
        this.platform = platform;
    }

    @ThriftField(3)
    public BasicParam getBasicParam() {
        return this.basicParam;
    }

    @ThriftField
    public void setBasicParam(BasicParam basicParam) {
        this.basicParam = basicParam;
    }

    @ThriftField(4)
    public String getUserQuery() {
        return this.userQuery;
    }

    @ThriftField
    public void setUserQuery(String userQuery) {
        this.userQuery = userQuery;
    }

    @ThriftField(5)
    public String getBizType() {
        return this.bizType;
    }

    @ThriftField
    public void setBizType(String bizType) {
        this.bizType = bizType;
    }

    @ThriftField(6)
    public String getBizId() {
        return this.bizId;
    }

    @ThriftField
    public void setBizId(String bizId) {
        this.bizId = bizId;
    }

    @ThriftField(7)
    public String getRequestTime() {
        return this.requestTime;
    }

    @ThriftField
    public void setRequestTime(String requestTime) {
        this.requestTime = requestTime;
    }

    @ThriftField(8)
    public String getRole() {
        return this.role;
    }

    @ThriftField
    public void setRole(String role) {
        this.role = role;
    }

    @ThriftField(9)
    public List<String> getImgUrls() {
        return this.imgUrls;
    }

    @ThriftField
    public void setImgUrls(List<String> imgUrls) {
        this.imgUrls = imgUrls;
    }

    @ThriftField(10)
    public Map<String, String> getExtraParams() {
        return this.extraParams;
    }

    @ThriftField
    public void setExtraParams(Map<String, String> extraParams) {
        this.extraParams = extraParams;
    }

    @ThriftField(11)
    public String getQuerySource() {
        return this.querySource;
    }

    @ThriftField
    public void setQuerySource(String querySource) {
        this.querySource = querySource;
    }
}
