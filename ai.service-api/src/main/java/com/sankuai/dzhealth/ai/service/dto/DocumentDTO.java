package com.sankuai.dzhealth.ai.service.dto;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.Map;

/**
 * <AUTHOR> youtianyu
 * @version : 0.1
 * @since : 2025/3/17
 */
@ThriftStruct
@ToString
@EqualsAndHashCode
@TypeDoc(name = "索引文档")
public class DocumentDTO implements Serializable {

    @FieldDoc(name = "唯一ID")
    @Getter(onMethod_ = @ThriftField(value = 1))
    @Setter(onMethod_ = @ThriftField)
    private String id;

    @FieldDoc(name = "文本内容")
    @Getter(onMethod_ = @ThriftField(value = 2))
    @Setter(onMethod_ = @ThriftField)
    private String text;

    @FieldDoc(name = "元数据",
            description = "Metadata for the document." +
                    "It should not be nested and values should be restricted to string, int, float, boolean for simple use with Vector Dbs.")
    @Getter(onMethod_ = @ThriftField(value = 3))
    @Setter(onMethod_ = @ThriftField)
    private Map<String, String> metadata;

    @FieldDoc(name = "分数",
            description =
                    "A numeric score associated with this document that can represent various types of relevance measures." +
                            "Common uses include:" +
                            "- Measure of similarity between the embedding value of the document's text/media and a query vector, where higher scores indicate greater similarity (opposite of distance measure)." +
                            "- Text relevancy rankings from retrieval systems." +
                            "- Custom relevancy metrics from RAG patterns." +
                            "Higher values typically indicate greater relevance or similarity.")
    @Getter(onMethod_ = @ThriftField(value = 4))
    @Setter(onMethod_ = @ThriftField)
    private Double score;

}