package com.sankuai.dzhealth.ai.service.api;

import com.facebook.swift.service.ThriftMethod;
import com.facebook.swift.service.ThriftService;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.meituan.servicecatalog.api.annotations.ParamDoc;
import com.sankuai.dzhealth.ai.service.dto.HaimaContentDTO;

import java.util.List;
import java.util.Map;

/**
 * 海马运营位服务 Thrift 接口
 * 提供与海马系统交互的方法，用于获取运营配置内容
 */
@ThriftService("HaimaThriftService")
public interface HaimaThriftService {


    @ThriftMethod
    @MethodDoc(name = "获取海马运营位内容",
               description = "根据场景键和字段获取海马运营位配置内容",
               responseParams = {
                   @ParamDoc(name = "运营位内容列表",
                             description = "海马运营位内容列表",
                             example = "[\n" +
                                       "    {\n" +
                                       "        \"configId\": 12345,\n" +
                                       "        \"contentId\": 67890,\n" +
                                       "        \"extJson\": \"{\\\"title\\\":\\\"医疗服务\\\",\\\"subtitle\\\":\\\"专业医疗咨询\\\",\\\"imageUrl\\\":\\\"https://example.com/image.jpg\\\",\\\"linkUrl\\\":\\\"https://example.com/service\\\"}\",\n" +
                                       "        \"contentMap\": {\n" +
                                       "            \"title\": \"医疗服务\",\n" +
                                       "            \"subtitle\": \"专业医疗咨询\",\n" +
                                       "            \"imageUrl\": \"https://example.com/image.jpg\",\n" +
                                       "            \"linkUrl\": \"https://example.com/service\"\n" +
                                       "        },\n" +
                                       "        \"createTime\": \"2025-03-21 14:32:00\",\n" +
                                       "        \"updateTime\": \"2025-03-21 14:32:00\"\n" +
                                       "    }\n" +
                                       "]")})
    List<HaimaContentDTO> getContent(
            @ParamDoc(name = "sceneKey", description = "场景键，用于标识特定的运营位场景", example = "public_hospital_brief")
            String sceneKey,
            @ParamDoc(name = "fields", description = "过滤字段，用于按特定条件筛选内容", example = "{\"mtShopId\":\"12345\"}")
            Map<String, String> fields);
}