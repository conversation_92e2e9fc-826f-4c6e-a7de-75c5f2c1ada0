package com.sankuai.dzhealth.ai.service.api.xhs;

import com.facebook.swift.service.ThriftMethod;
import com.facebook.swift.service.ThriftService;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.meituan.servicecatalog.api.annotations.ParamDoc;
import com.meituan.servicecatalog.api.annotations.ServiceDoc;
import com.sankuai.dzhealth.ai.service.dto.xhs.XhsNoteResponse;

/**
 * @author:chenwei
 * @time: 2025/6/16 17:00
 * @version: 0.0.1
 */
@ServiceDoc(appkey = "com.sankuai.dzhealth.ai.service",
        name = "XhsNoteCallbackService",
        description = "小红书笔记生成回调接口",
        scenarios = "小红书笔记接口回调",
        notice = "小红书笔记接口回调")
@InterfaceDoc(type = "octo.thrift.annotation", displayName = "XhsNoteCallbackService", 
        description = "小红书笔记生成回调接口", scenarios = "接口回调")
@ThriftService
public interface XhsNoteCallbackService {

    @MethodDoc(displayName = "onNoteGenerated", description = "笔记生成完成回调", 
            parameters = {@ParamDoc(name = "response", description = "笔记生成结果", example = {})}, 
            returnValueDescription = "无返回值", extensions = {})
    @ThriftMethod
    void onNoteGenerated(XhsNoteResponse response);
} 