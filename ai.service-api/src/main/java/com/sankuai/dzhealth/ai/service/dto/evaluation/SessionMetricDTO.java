package com.sankuai.dzhealth.ai.service.dto.evaluation;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 测评列表DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SessionMetricDTO implements Serializable {
    @JsonProperty("session_id")
    private String sessionId;

    private String intent;

    private List<Header> headers;

    private Map<String, Metric> metrics;

    @JsonProperty("manual_headers")
    private List<Header> manualHeaders;

    @JsonProperty("manual_metrics")
    private Map<String, Metric> manualMetrics;

    @Data
    @NoArgsConstructor
    public static class Header implements Serializable {
        private String key;
        private String name;

        public Header(String key, String name) {
            this.key = key;
            this.name = name;
        }
    }


    @Data
    @NoArgsConstructor
    public static class Metric implements Serializable {
        private String score;
        private String reason;

        public Metric(String score, String reason) {
            this.score = score;
            this.reason = reason;
        }
    }
}
