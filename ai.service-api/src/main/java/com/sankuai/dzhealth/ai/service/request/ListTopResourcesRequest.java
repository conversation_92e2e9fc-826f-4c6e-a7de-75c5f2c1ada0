package com.sankuai.dzhealth.ai.service.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.*;

@ThriftStruct
@TypeDoc(name="查询推荐资源请求")
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ListTopResourcesRequest {
    @FieldDoc(name="节点业务ID")
    @Getter(onMethod_=@ThriftField(1))
    @Setter(onMethod_=@ThriftField)
    private String nodeId;

    @FieldDoc(name="最多返回条数")
    @Getter(onMethod_=@ThriftField(2))
    @Setter(onMethod_=@ThriftField)
    private Integer limit;
}

