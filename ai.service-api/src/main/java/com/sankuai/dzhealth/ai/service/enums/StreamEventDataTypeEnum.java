package com.sankuai.dzhealth.ai.service.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;


@Getter
@AllArgsConstructor
public enum StreamEventDataTypeEnum {

    LOADING_STATUS("loadingStatus", "状态"),

    MAIN_TEXT("mainText", "正文"),

    THINK_PROCESS_STREAM("thinkProcessStream", "推理"),

    MESSAGE_ID("messageId", "用户发送消息ID"),

    REPLY_MESSAGE_ID("replyMessageId", "回复消息ID"),

    SESSION_ID("sessionId", "会话id"),

    TITLE("title", "标题")

    ;

    private final String type;

    private final String desc;

    public static StreamEventDataTypeEnum getByType(String type) {
        for (StreamEventDataTypeEnum typeEnum : values()) {
            if (typeEnum.getType().equals(type)) {
                return typeEnum;
            }
        }
        return null;
    }

}
