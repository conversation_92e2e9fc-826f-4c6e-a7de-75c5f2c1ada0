package com.sankuai.dzhealth.ai.service.dto;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import java.io.Serializable;
import java.util.List;

/**
 * @author:chenwei
 * @time: 2025/3/25 09:58
 * @version: 0.0.1
 */
@TypeDoc(description = "会话列表响应")
@ThriftStruct
public class SessionResponse implements Serializable {

    @FieldDoc(description = "会话列表")
    private List<SessionDTO> sessionList;

    @FieldDoc(description = "是否需要一次新的会话")
    private Long nextSessionId;

    @FieldDoc(description = "标题")
    private String title;

    @FieldDoc(description = "头图")
    private String headPic;

    @FieldDoc(description = "介绍")
    private String introduce;

    @FieldDoc(description = "历史记录跳链")
    private String recordListUrl;

    @FieldDoc(description = "商户名称")
    private String shopName;
    
    @ThriftField(1)
    public List<SessionDTO> getSessionList() {
        return sessionList;
    }
    
    @ThriftField
    public void setSessionList(List<SessionDTO> sessionList) {
        this.sessionList = sessionList;
    }


    @ThriftField(2)
    public Long getNextSessionId() {
        return this.nextSessionId;
    }

    @ThriftField
    public void setNextSessionId(Long nextSessionId) {
        this.nextSessionId = nextSessionId;
    }


    @ThriftField(3)
    public String getTitle() {
        return this.title;
    }

    @ThriftField
    public void setTitle(String title) {
        this.title = title;
    }

    @ThriftField(4)
    public String getHeadPic() {
        return this.headPic;
    }

    @ThriftField
    public void setHeadPic(String headPic) {
        this.headPic = headPic;
    }

    @ThriftField(5)
    public String getIntroduce() {
        return this.introduce;
    }

    @ThriftField
    public void setIntroduce(String introduce) {
        this.introduce = introduce;
    }


    @ThriftField(6)
    public String getRecordListUrl() {
        return this.recordListUrl;
    }

    @ThriftField
    public void setRecordListUrl(String recordListUrl) {
        this.recordListUrl = recordListUrl;
    }


    @ThriftField(7)
    public String getShopName() {
        return this.shopName;
    }

    @ThriftField
    public void setShopName(String shopName) {
        this.shopName = shopName;
    }
}
