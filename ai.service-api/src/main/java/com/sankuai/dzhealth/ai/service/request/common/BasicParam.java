package com.sankuai.dzhealth.ai.service.request.common;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @author:chenwei
 * @time: 2025/3/18 19:06
 * @version: 0.0.1
 */
@TypeDoc(description = "请求通参")
@Data
@AllArgsConstructor
@NoArgsConstructor
@ThriftStruct
@Builder
public class BasicParam implements Serializable {

    @ThriftField(1)
    @FieldDoc(description = "用户id", requiredness = Requiredness.REQUIRED)
    public Long userId;

    @ThriftField(2)
    @FieldDoc(description = "用户选择的地址纬度", requiredness = Requiredness.OPTIONAL)
    public Double lat;

    @ThriftField(3)
    @FieldDoc(description = "用户选择的地址经度", requiredness = Requiredness.OPTIONAL)
    public Double lng;

    @ThriftField(4)
    @FieldDoc(description = "客户端类型", requiredness = Requiredness.OPTIONAL)
    public String clientType;

    @ThriftField(5)
    @FieldDoc(description = "uuid美团侧唯一设备id", requiredness = Requiredness.REQUIRED)
    public String uuid;

    @ThriftField(6)
    @FieldDoc(description = "app版本，如8.29.0", requiredness = Requiredness.OPTIONAL)
    public String appVersion;

    @ThriftField(7)
    @FieldDoc(description = "用户选择的地址-二级城市id，如city=北京市", requiredness = Requiredness.OPTIONAL)
    public Integer cityId;

    @ThriftField(8)
    @FieldDoc(description = "用户真实城市id", requiredness = Requiredness.OPTIONAL)
    public Integer userCityId;

    @ThriftField(8)
    @FieldDoc(description = "是否授权", requiredness = Requiredness.OPTIONAL)
    public Integer locationAuthorized;

    @ThriftField(9)
    @FieldDoc(description = "ip地址", requiredness = Requiredness.OPTIONAL)
    public String ip;
}
