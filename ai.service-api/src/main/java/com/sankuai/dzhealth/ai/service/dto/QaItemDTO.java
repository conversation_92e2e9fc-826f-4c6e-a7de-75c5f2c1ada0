package com.sankuai.dzhealth.ai.service.dto;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.*;

import java.io.Serializable;

/**
 * 问答条目DTO
 * 用于商品详情页AI问答列表中的单个问答项
 */
@ThriftStruct
@ToString
@EqualsAndHashCode
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TypeDoc(name = "问答条目")
public class QaItemDTO implements Serializable {

    @FieldDoc(name = "问题内容", description = "用户关心的问题，所有条目都必须有此字段")
    @Getter(onMethod_ = @ThriftField(value = 1))
    @Setter(onMethod_ = @ThriftField)
    private String question;

    @FieldDoc(name = "答案内容", description = "问题的详细答案，无答案时为null")
    @Getter(onMethod_ = @ThriftField(value = 2))
    @Setter(onMethod_ = @ThriftField)
    private String answer;

    @FieldDoc(name = "配图URL", description = "答案配图的图片链接，可选")
    @Getter(onMethod_ = @ThriftField(value = 3))
    @Setter(onMethod_ = @ThriftField)
    private String imageUrl;

    @FieldDoc(name = "AI助手问答链接", description = "点击问题后跳转到AI助手对话页面的深链接，携带当前问题")
    @Getter(onMethod_ = @ThriftField(value = 5))
    @Setter(onMethod_ = @ThriftField)
    private String agentQaUrl;
}

