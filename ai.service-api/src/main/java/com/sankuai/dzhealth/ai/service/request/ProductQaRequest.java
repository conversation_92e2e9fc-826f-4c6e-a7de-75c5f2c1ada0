package com.sankuai.dzhealth.ai.service.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.*;

import java.io.Serializable;

/**
 * 商品详情页AI问答请求
 * 用于获取商品相关的AI生成问答内容
 */
@ThriftStruct
@ToString
@EqualsAndHashCode
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TypeDoc(name = "商品详情页AI问答请求")
public class ProductQaRequest implements Serializable {

    @FieldDoc(
        name = "入参Id类型",
        description = "商品体系类型，用于区分两套商品体系：1-点评团购商品，2-美团团购商品，3-泛商品业务商品。@see com.sankuai.general.product.query.center.client.enums.IdTypeEnum",
        requiredness = Requiredness.REQUIRED
    )
    @Getter(onMethod_ = @ThriftField(value = 1))
    @Setter(onMethod_ = @ThriftField)
    private Integer idType;

    @FieldDoc(name = "商品ID", description = "商品唯一标识，团购商品体系为舟ID，泛商品体系为泛商品ID")
    @Getter(onMethod_ = @ThriftField(value = 2))
    @Setter(onMethod_ = @ThriftField)
    private String productId;

    @FieldDoc(name = "商品二级类目ID", description = "商品所属二级类目ID，团购和泛商品体系对应不同的类目ID体系，口腔齿科506，医美850")
    @Getter(onMethod_ = @ThriftField(value = 3))
    @Setter(onMethod_ = @ThriftField)
    private String categoryId;

    @FieldDoc(name = "平台标识", description = "1:点评 2:美团，用于区分不同平台的深链接拼接")
    @Getter(onMethod_ = @ThriftField(value = 4))
    @Setter(onMethod_ = @ThriftField)
    private Integer platform;

    @FieldDoc(name = "问题筛选", description = "用于筛选特定问题的关键词，可选参数。传入后会优先返回包含该关键词的问答内容")
    @Getter(onMethod_ = @ThriftField(value = 5))
    @Setter(onMethod_ = @ThriftField)
    private String question;
}

