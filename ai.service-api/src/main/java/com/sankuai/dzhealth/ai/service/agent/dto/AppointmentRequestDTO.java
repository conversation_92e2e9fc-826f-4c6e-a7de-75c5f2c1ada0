package com.sankuai.dzhealth.ai.service.agent.dto;


import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;

@ThriftStruct
public class AppointmentRequestDTO {

    @FieldDoc(description = "消息Id")
    private String msgId;

    @FieldDoc(description = "用户Id")
    private Long userId;

    @FieldDoc(description = "回复消息Id")
    private String replyMsgId;

    @FieldDoc(description = "app版本")
    private String appVersion;

    @FieldDoc(description = "用户标识")
    private String uuid;

    @FieldDoc(description = "用户城市Id")
    private Integer cityId;

    @ThriftField(1)
    public String getMsgId() {
        return this.msgId;
    }

    @ThriftField(1)
    public void setMsgId(String msgId) {
        this.msgId = msgId;
    }

    @ThriftField(2)
    public Long getUserId() {
        return this.userId;
    }

    @ThriftField(2)
    public void setUserId(Long userId) {
        this.userId = userId;
    }

    @ThriftField(3)
    public String getAppVersion() {
        return this.appVersion;
    }

    @ThriftField(3)
    public void setAppVersion(String appVersion) {
        this.appVersion = appVersion;
    }

    @ThriftField(4)
    public String getUuid() {
        return this.uuid;
    }

    @ThriftField(4)
    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    @ThriftField(5)
    public String getReplyMsgId() {
        return this.replyMsgId;
    }

    @ThriftField(5)
    public void setReplyMsgId(String replyMsgId) {
        this.replyMsgId = replyMsgId;
    }

    @ThriftField(6)
    public Integer getCityId() {
        return this.cityId;
    }

    @ThriftField(6)
    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }
}
