package com.sankuai.dzhealth.ai.service.agent.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;

@ThriftStruct
public class ShopQry {
    @ThriftField(1)
    private Long shopId;
    @ThriftField(2)
    private ShopGoodsQry goodsQry;

    public boolean equals(Object o) {
        if (o == this) {
            return true;
        } else if (!(o instanceof ShopQry)) {
            return false;
        } else {
            ShopQry other = (ShopQry)o;
            if (!other.canEqual(this)) {
                return false;
            } else {
                Object this$shopId = this.getShopId();
                Object other$shopId = other.getShopId();
                if (this$shopId == null) {
                    if (other$shopId != null) {
                        return false;
                    }
                } else if (!this$shopId.equals(other$shopId)) {
                    return false;
                }

                Object this$goodsQry = this.getGoodsQry();
                Object other$goodsQry = other.getGoodsQry();
                if (this$goodsQry == null) {
                    if (other$goodsQry != null) {
                        return false;
                    }
                } else if (!this$goodsQry.equals(other$goodsQry)) {
                    return false;
                }

                return true;
            }
        }
    }

    protected boolean canEqual(Object other) {
        return other instanceof ShopQry;
    }

    public int hashCode() {
        int PRIME = 59;
        int result = 1;
        Object $shopId = this.getShopId();
        result = result * 59 + ($shopId == null ? 43 : $shopId.hashCode());
        Object $goodsQry = this.getGoodsQry();
        result = result * 59 + ($goodsQry == null ? 43 : $goodsQry.hashCode());
        return result;
    }

    public Long getShopId() {
        return this.shopId;
    }

    public ShopGoodsQry getGoodsQry() {
        return this.goodsQry;
    }

    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    public void setGoodsQry(ShopGoodsQry goodsQry) {
        this.goodsQry = goodsQry;
    }
}
