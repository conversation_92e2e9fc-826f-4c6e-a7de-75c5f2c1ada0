package com.sankuai.dzhealth.ai.service.request.xhs;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;

import java.io.Serializable;

/**
 * @author:chenwei
 * @time: 2025/6/16 14:36
 * @version: 0.0.1
 */
@ThriftStruct
public class YushuNoteRequest implements Serializable {
    @FieldDoc(
            description = "批次号"
    )
    private String batch;

    @FieldDoc(
            description = "新红数据Json字符串"
    )
    private String xhsJson;


    @ThriftField(1)
    public String getBatch() {
        return this.batch;
    }

    @ThriftField
    public void setBatch(String batch) {
        this.batch = batch;
    }


    @ThriftField(2)
    public String getXhsJson() {
        return this.xhsJson;
    }

    @ThriftField
    public void setXhsJson(String xhsJson) {
        this.xhsJson = xhsJson;
    }
}
