package com.sankuai.dzhealth.ai.service.dto.decision;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.*;

import java.io.Serializable;

@ThriftStruct
@TypeDoc(name = "决策路径")
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class DecisionEdgeDTO implements Serializable {

    /** 业务场景 */
    @FieldDoc(name = "业务场景")
    @Getter(onMethod_ = @ThriftField(value = 1))
    @Setter(onMethod_ = @ThriftField)
    private String bizScene;

    /** 边业务 ID */
    @FieldDoc(name = "边业务ID")
    @Getter(onMethod_ = @ThriftField(value = 2))
    @Setter(onMethod_ = @ThriftField)
    private String edgeId;

    /** 父节点 ID */
    @FieldDoc(name = "父节点ID")
    @Getter(onMethod_ = @ThriftField(value = 3))
    @Setter(onMethod_ = @ThriftField)
    private String parentId;

    /** 子节点 ID */
    @FieldDoc(name = "子节点ID")
    @Getter(onMethod_ = @ThriftField(value = 4))
    @Setter(onMethod_ = @ThriftField)
    private String childId;

    /** 边类型 */
    @FieldDoc(name = "边类型")
    @Getter(onMethod_ = @ThriftField(value = 5))
    @Setter(onMethod_ = @ThriftField)
    private String edgeType;

    /** 描述 */
    @FieldDoc(name = "描述")
    @Getter(onMethod_ = @ThriftField(value = 6))
    @Setter(onMethod_ = @ThriftField)
    private String edgeDesc;

    /** 优先级 */
    @FieldDoc(name = "优先级")
    @Getter(onMethod_ = @ThriftField(value = 7))
    @Setter(onMethod_ = @ThriftField)
    private Long sortOrder;

    /** 状态 */
    @FieldDoc(name = "状态")
    @Getter(onMethod_ = @ThriftField(value = 8))
    @Setter(onMethod_ = @ThriftField)
    private String status;
} 