package com.sankuai.dzhealth.ai.service.agent.api;


import com.facebook.swift.service.ThriftMethod;
import com.facebook.swift.service.ThriftService;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.meituan.servicecatalog.api.annotations.ParamDoc;
import com.meituan.servicecatalog.api.annotations.ServiceDoc;
import com.sankuai.dzhealth.ai.service.agent.dto.AppointmentRequestDTO;
import com.sankuai.dzhealth.ai.service.agent.request.ShopQry;
import com.sankuai.dzhealth.ai.service.api.RemoteResponse;

import java.util.Date;
import java.util.List;

@ServiceDoc(appkey = "com.sankuai.dzhealth.ai.service",
        name = "预约服务",
        description = "医美&口腔预约服务",
        scenarios = "医美&口腔预约服务",
        notice = "医美&口腔预约服务")
@InterfaceDoc(type = "octo.thrift.annotation", displayName = "医美&口腔预约服务", description = "医美&口腔预约服务", scenarios = "医美&口腔预约服务")
@ThriftService
public interface AppointMentService {

    @MethodDoc(displayName = "查询用户的电话号码", description = "查询用户电话", parameters = {
            @ParamDoc(name = "userId", description = "用户id", example = {})}, returnValueDescription = "返回电话", extensions = {})
    @ThriftMethod
    String queryUserPhoneNum(Long userId);

    @MethodDoc(displayName = "解析经纬度", description = "从经纬度地址解析地址", parameters = {
            @ParamDoc(name = "lng", description = "经度", example = {}),
            @ParamDoc(name = "lat", description = "纬度", example = {})}, returnValueDescription = "返回地址", extensions = {})
    @ThriftMethod
    String parseAddressByLatLng(Double lng, Double lat);

    @MethodDoc(displayName = "解析经纬度", description = "从经纬度地址解析地址", parameters = {
            @ParamDoc(name = "cityId", description = "城市id", example = {}),
            @ParamDoc(name = "address", description = "地址", example = {})}, returnValueDescription = "返回地址", extensions = {})
    @ThriftMethod
    String parseLatLngByAddress(String cityId, String address);


    @MethodDoc(displayName = "推送预约卡片", description = "推送预约卡片", parameters = {
            @ParamDoc(name = "input", description = "预约信息JSON", example = {}),
            @ParamDoc(name = "msgId", description = "消息ID", example = {})}, returnValueDescription = "返回预约卡片数据", extensions = {})
    @ThriftMethod
    RemoteResponse<String> pushAppointment(String input, String msgId);

    @MethodDoc(displayName = "确认预约", description = "确认预约信息发起帮约", parameters = {
            @ParamDoc(name = "request", description = "请求参数", example = {})}, returnValueDescription = "返回结果", extensions = {})
    @ThriftMethod
    RemoteResponse<String> confirmAppointment(AppointmentRequestDTO request);

    @MethodDoc(displayName = "重新安排预约", description = "重启本次任务", parameters = {
            @ParamDoc(name = "request", description = "请求参数", example = {})}, returnValueDescription = "返回结果", extensions = {})
    @ThriftMethod
    RemoteResponse<String> rescheduleAppointment(AppointmentRequestDTO request);

    @MethodDoc(displayName = "取消预约", description = "取消预约任务", parameters = {
            @ParamDoc(name = "request", description = "请求参数", example = {})}, returnValueDescription = "返回结果", extensions = {})
    @ThriftMethod
    RemoteResponse<String> cancelAppointment(AppointmentRequestDTO request);

    @MethodDoc(displayName = "AI预约信号处理", description = "监听AI约信号处理同步", parameters = {
            @ParamDoc(name = "callBackDTO", description = "AI外呼回调数据", example = {})}, returnValueDescription = "返回处理结果", extensions = {})
    @ThriftMethod
    RemoteResponse<Boolean> aiAppointmentSignalISProcess(String callBackDTO);

    @MethodDoc(displayName = "按钮取消成功预约", description = "取消成功任务", parameters = {
            @ParamDoc(name = "request", description = "请求参数", example = {})}, returnValueDescription = "返回结果", extensions = {})
    @ThriftMethod
    RemoteResponse<String> cancelSuccessAppointByButton(AppointmentRequestDTO request);

    @MethodDoc(displayName = "消息取消成功预约", description = "监听消息取消", parameters = {
            @ParamDoc(name = "leadId", description = "线索ID", example = {})}, returnValueDescription = "返回处理结果", extensions = {})
    @ThriftMethod
    RemoteResponse<Boolean> cancelSuccessAppointByMessage(Long leadId);

    @MethodDoc(displayName = "再次预约", description = "再次预约", parameters = {
            @ParamDoc(name = "request", description = "请求参数", example = {})}, returnValueDescription = "返回结果", extensions = {})
    @ThriftMethod
    RemoteResponse<String> reAppointment(AppointmentRequestDTO request);


    @MethodDoc(displayName = "解析日期字符串", description = "解析日期字符串为Date对象", parameters = {
            @ParamDoc(name = "dateStr", description = "日期字符串", example = {})}, returnValueDescription = "返回日期字符串", extensions = {})
    @ThriftMethod
    RemoteResponse<String> parseStringToDate(String dateStr);


    @MethodDoc(displayName = "测试商家选择", description = "测试")
    @ThriftMethod
    public RemoteResponse<String> testSelectShop(List<ShopQry> shopQryList, String msgId);

    @MethodDoc(displayName = "测试营业时间", description = "测试")
    @ThriftMethod
    boolean testBusinessPoint(Long shopId);

    @MethodDoc(displayName = "测试营业范围", description = "测试")
    @ThriftMethod
    boolean testBusinessRange(Long shopId, Date date,Date endDate);

    boolean testMessage(Long shopId,Long time);


}
