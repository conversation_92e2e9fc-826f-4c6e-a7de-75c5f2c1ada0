package com.sankuai.dzhealth.ai.service.agent.enums;

import lombok.Getter;

/**
 * @author:chenwei
 * @time: 2025/7/12 17:31
 * @version: 0.0.1
 */
@Getter
public enum SessionSceneEnum {

    ENTRY_PAGE(0, "进入页面"),

    HISTORY_SESSION(1, "请求历史会话"),

    NEW_SESSION(2, "新建会话"),

    PRODUCT_DETAIL(3, "商详页"),

    HISTORY_MESSAGE(4, "历史会话消息"),

    DELETE_SESSION(5, "删除会话操作"),

    LIKE_MESSAGE(6, "点赞消息操作"),

    MESSAGE_DETAIL(7, "消息详情")

    ;



    public final int code;

    public final String desc;

    SessionSceneEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }


}
