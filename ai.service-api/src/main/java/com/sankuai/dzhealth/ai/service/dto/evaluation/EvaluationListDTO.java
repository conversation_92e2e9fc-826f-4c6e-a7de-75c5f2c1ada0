package com.sankuai.dzhealth.ai.service.dto.evaluation;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 测评列表DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EvaluationListDTO {
    /**
     * 测评ID
     */
    @JsonProperty("evaluation_id")
    private String evaluationId;

    /**
     * 测评名称
     */
    @JsonProperty("evaluation_name")
    private String evaluationName;

    /**
     * 测评状态 0-进行中 1-已完成
     */
    private String status;

    /**
     * 创建时间
     */
    @JsonProperty("create_time")
    private String createTime;

    /**
     * 结束时间
     */
    @JsonProperty("end_time")
    private String endTime;
}

