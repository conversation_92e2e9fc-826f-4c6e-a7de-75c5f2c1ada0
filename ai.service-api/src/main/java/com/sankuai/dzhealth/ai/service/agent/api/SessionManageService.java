package com.sankuai.dzhealth.ai.service.agent.api;

import com.facebook.swift.service.ThriftMethod;
import com.facebook.swift.service.ThriftService;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.meituan.servicecatalog.api.annotations.ParamDoc;
import com.meituan.servicecatalog.api.annotations.ServiceDoc;
import com.sankuai.dzhealth.ai.service.agent.dto.ChatMessageResponse;
import com.sankuai.dzhealth.ai.service.agent.dto.ChatSessionResponse;
import com.sankuai.dzhealth.ai.service.agent.dto.NavigateDTOResponse;
import com.sankuai.dzhealth.ai.service.agent.request.SessionManageRequest;
import com.sankuai.dzhealth.ai.service.api.RemoteResponse;

/**
 * @author:chenwei
 * @time: 2025/7/10 10:12
 * @version: 0.0.1
 */

@ServiceDoc(appkey = "com.sankuai.dzhealth.ai.service",
        name = "SessionManageService",
        description = "会话管理接口",
        scenarios = "会话管理",
        notice = "会话管理接口")
@InterfaceDoc(type = "octo.thrift.annotation", displayName = "SessionManageService", description = "会话管理接口", scenarios = "会话管理")
@ThriftService
public interface SessionManageService {

    @MethodDoc(displayName = "操作消息", description = "操作消息", parameters = {
            @ParamDoc(name = "request", description = "请求参数", example = {})}, returnValueDescription = "返回消息列表", extensions = {})
    @ThriftMethod
    RemoteResponse<ChatMessageResponse> processMessages(SessionManageRequest request);


    @MethodDoc(displayName = "操作会话列表", description = "操作消息列表", parameters = {
            @ParamDoc(name = "request", description = "请求参数", example = {})}, returnValueDescription = "返回历史会话列表", extensions = {})
    @ThriftMethod
    RemoteResponse<ChatSessionResponse> operateSessions(SessionManageRequest request);


    RemoteResponse<NavigateDTOResponse> queryNavigate(String bizType);










}
