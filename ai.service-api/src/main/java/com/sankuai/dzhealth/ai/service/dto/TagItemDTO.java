package com.sankuai.dzhealth.ai.service.dto;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;

import java.io.Serializable;

/**
 * @author:chenwei
 * @time: 2025/4/1 10:04
 * @version: 0.0.1
 */
@ThriftStruct
public class TagItemDTO implements Serializable {

    private String text;

    private Integer type;

    private Integer styleType;

    private Integer height;

    private Integer width;

    private String url;

    private String iconUrl;

    private String bgColor;

    private String textColor;


    @ThriftField(1)
    public String getText() {
        return this.text;
    }

    @ThriftField
    public void setText(String text) {
        this.text = text;
    }

    @ThriftField(2)
    public Integer getType() {
        return this.type;
    }

    @ThriftField
    public void setType(Integer type) {
        this.type = type;
    }

    @ThriftField(3)
    public Integer getStyleType() {
        return this.styleType;
    }

    @ThriftField
    public void setStyleType(Integer styleType) {
        this.styleType = styleType;
    }

    @ThriftField(4)
    public Integer getHeight() {
        return this.height;
    }

    @ThriftField
    public void setHeight(Integer height) {
        this.height = height;
    }

    @ThriftField(5)
    public Integer getWidth() {
        return this.width;
    }

    @ThriftField
    public void setWidth(Integer width) {
        this.width = width;
    }

    @ThriftField(6)
    public String getUrl() {
        return this.url;
    }

    @ThriftField
    public void setUrl(String url) {
        this.url = url;
    }


    @ThriftField(7)
    public String getIconUrl() {
        return this.iconUrl;
    }

    @ThriftField
    public void setIconUrl(String iconUrl) {
        this.iconUrl = iconUrl;
    }


    @ThriftField(8)
    public String getBgColor() {
        return this.bgColor;
    }

    @ThriftField
    public void setBgColor(String bgColor) {
        this.bgColor = bgColor;
    }


    @ThriftField(9)
    public String getTextColor() {
        return this.textColor;
    }

    @ThriftField
    public void setTextColor(String textColor) {
        this.textColor = textColor;
    }
}
