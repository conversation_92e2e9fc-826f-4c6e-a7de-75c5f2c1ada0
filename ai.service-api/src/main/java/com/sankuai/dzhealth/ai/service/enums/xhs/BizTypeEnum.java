package com.sankuai.dzhealth.ai.service.enums.xhs;

/**
 * @author:chenwei
 * @time: 2025/6/17 14:56
 * @version: 0.0.1
 */

public enum BizTypeEnum {

    MEDICAL(1, "medical", "医美"),
    JOY_LIFE(2, "joyLife", "乐生活"),
    ;

    private final int code;

    private final String bizCode;

    private final String desc;

    BizTypeEnum(int code, String bizCode, String desc) {
        this.code = code;
        this.bizCode = bizCode;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getBizCode() {
        return bizCode;
    }

    public String getDesc() {
        return desc;
    }

    /**
     * 根据bizCode获取对应的枚举
     * @param bizCode 业务代码
     * @return 对应的枚举，如果未找到则返回null
     */
    public static BizTypeEnum getByBizCode(String bizCode) {
        if (bizCode == null) {
            return null;
        }

        for (BizTypeEnum bizType : BizTypeEnum.values()) {
            if (bizCode.equals(bizType.getBizCode())) {
                return bizType;
            }
        }

        return null;
    }
}
