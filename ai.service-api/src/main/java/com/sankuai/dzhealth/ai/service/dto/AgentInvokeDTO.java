package com.sankuai.dzhealth.ai.service.dto;


import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import lombok.*;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ThriftStruct
public class AgentInvokeDTO {

    @Getter(onMethod_ = @ThriftField(value = 1))
    @Setter(onMethod_ = @ThriftField)
    @ThriftField(1)
    public String session_id;

    @Getter(onMethod_ = @ThriftField(value = 2))
    @Setter(onMethod_ = @ThriftField)
    @ThriftField(2)
    public List<String> records;
}
