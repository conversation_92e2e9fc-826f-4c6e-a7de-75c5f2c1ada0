package com.sankuai.dzhealth.ai.service.dto.stream;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.NoArgsConstructor;

import java.util.List;

@Builder
@NoArgsConstructor
@AllArgsConstructor
@ThriftStruct
public class BufferMergedDTO {

    private int messageType;

    private List<StreamEventDTO> streamEventDTOS;


    @ThriftField(1)
    public int getMessageType() {
        return this.messageType;
    }

    @ThriftField
    public void setMessageType(int messageType) {
        this.messageType = messageType;
    }

    @ThriftField(2)
    public List<StreamEventDTO> getStreamEventDTOS() {
        return this.streamEventDTOS;
    }

    @ThriftField
    public void setStreamEventDTOS(List<StreamEventDTO> streamEventDTOS) {
        this.streamEventDTOS = streamEventDTOS;
    }
}
