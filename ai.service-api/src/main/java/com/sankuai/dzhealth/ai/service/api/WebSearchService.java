package com.sankuai.dzhealth.ai.service.api;

import com.facebook.swift.service.ThriftService;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.meituan.servicecatalog.api.annotations.ParamDoc;
import com.sankuai.dzhealth.ai.service.dto.WebSearchDTO;
import com.sankuai.dzhealth.ai.service.request.WebSearchRequest;

import java.util.List;

/**
 * <AUTHOR> youtianyu
 * @version : 0.1
 * @since : 2025/4/8
 */
@ThriftService
public interface WebSearchService {
    @MethodDoc(name = "联网搜索", description = "根据关键词搜索相关网站", parameters = {@ParamDoc(name = "request", description = "搜索请求")})
    RemoteResponse<List<WebSearchDTO>> search(WebSearchRequest request);
}
