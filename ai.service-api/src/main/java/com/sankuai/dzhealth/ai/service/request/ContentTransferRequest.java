package com.sankuai.dzhealth.ai.service.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.*;

import java.io.Serializable;

/**
 * 内容转 Markdown 请求，只包含原始内容。
 */
@ThriftStruct
@ToString
@EqualsAndHashCode
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TypeDoc(name = "内容转换请求")
public class ContentTransferRequest implements Serializable {

    @FieldDoc(description = "原始文本内容，可为 HTML、普通文本、JSON 等")
    @Getter(onMethod_ = @ThriftField(value = 1))
    @Setter(onMethod_ = @ThriftField)
    private String originTextContent;

} 