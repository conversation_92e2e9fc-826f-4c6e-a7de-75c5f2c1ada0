package com.sankuai.dzhealth.ai.service.agent.dto;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;

import java.io.Serializable;
import java.util.List;

/**
 * @author:chenwei
 * @time: 2025/7/10 10:22
 * @version: 0.0.1
 */

@TypeDoc(description = "会话响应")
@ThriftStruct
public class ChatSessionResponse implements Serializable {

    @FieldDoc(description = "会话列表")
    private List<ChatSessionDTO> sessionList;

    @FieldDoc(description = "是否需要一次新的会话")
    private String nextSessionId;

    @FieldDoc(description = "历史记录跳链")
    private String recordListUrl;

    @FieldDoc(description = "删除操作是否成功")
    private Boolean deleteStatus;


    @ThriftField(1)
    public List<ChatSessionDTO> getSessionList() {
        return this.sessionList;
    }

    @ThriftField
    public void setSessionList(List<ChatSessionDTO> sessionList) {
        this.sessionList = sessionList;
    }

    @ThriftField(2)
    public String getNextSessionId() {
        return this.nextSessionId;
    }

    @ThriftField
    public void setNextSessionId(String nextSessionId) {
        this.nextSessionId = nextSessionId;
    }

    @ThriftField(3)
    public String getRecordListUrl() {
        return this.recordListUrl;
    }

    @ThriftField
    public void setRecordListUrl(String recordListUrl) {
        this.recordListUrl = recordListUrl;
    }


    @ThriftField(4)
    public Boolean getDeleteStatus() {
        return this.deleteStatus;
    }

    @ThriftField
    public void setDeleteStatus(Boolean deleteStatus) {
        this.deleteStatus = deleteStatus;
    }
}
