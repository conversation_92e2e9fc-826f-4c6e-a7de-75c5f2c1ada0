package com.sankuai.dzhealth.ai.service.dto;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import java.io.Serializable;

/**
 * @author:chenwei
 * @time: 2025/3/25 10:02
 * @version: 0.0.1
 */
@TypeDoc(description = "状态响应")
@ThriftStruct
public class StatusDTO implements Serializable {

    @FieldDoc(description = "操作状态，true表示成功，false表示失败")
    private Boolean status;
    
    @ThriftField(1)
    public Boolean getStatus() {
        return status;
    }
    
    @ThriftField
    public void setStatus(Boolean status) {
        this.status = status;
    }
}
