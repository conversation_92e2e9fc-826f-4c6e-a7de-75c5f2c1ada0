package com.sankuai.dzhealth.ai.service.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;

/**
 * <AUTHOR> xia<PERSON><PERSON><PERSON>
 * @since : 2025/7/25 16:40
 * @version : 1.0
 */
@Getter
@AllArgsConstructor
public enum QuerySourceEnum {

    NORMAL("normal", 0, "正常查询"),
    EVALUATION("evaluation", 1, "评估查询")
    ;

    private final String type;

    private final int code;

    private final String desc;

    public static QuerySourceEnum getByType(String type) {
        if (StringUtils.isBlank(type)) {
            return NORMAL;
        }
        return Arrays.stream(QuerySourceEnum.values()).filter(e -> e.getType().equals(type)).findFirst().orElse(NORMAL);
    }
}
