package com.sankuai.dzhealth.ai.service.dto;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.*;

import java.io.Serializable;
import java.util.List;

/**
 * 商品详情页AI问答响应
 * 包含商品相关的问答列表
 */
@ThriftStruct
@ToString
@EqualsAndHashCode
@Builder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
@TypeDoc(name = "商品详情页AI问答响应")
public class ProductQaDTO implements Serializable {

    @FieldDoc(name = "问答列表", description = "商品相关的问答条目列表，包含有答案和无答案的问题")
    @Getter(onMethod_ = @ThriftField(value = 1))
    @Setter(onMethod_ = @ThriftField)
    private List<QaItemDTO> qaItems;

    @FieldDoc(name = "AI助手入口文案", description = "跳转到AI助手对话的入口提示文案")
    @Getter(onMethod_ = @ThriftField(value = 2))
    @Setter(onMethod_ = @ThriftField)
    private String agentEntranceText;

    @FieldDoc(name = "AI助手入口链接", description = "跳转到AI助手对话页面的深链接")
    @Getter(onMethod_ = @ThriftField(value = 3))
    @Setter(onMethod_ = @ThriftField)
    private String agentEntranceUrl;

    @FieldDoc(name = "医生信息", description = "问答对应的医生信息，统一放在外层")
    @Getter(onMethod_ = @ThriftField(value = 4))
    @Setter(onMethod_ = @ThriftField)
    private DoctorInfoDTO doctorInfo;
}

