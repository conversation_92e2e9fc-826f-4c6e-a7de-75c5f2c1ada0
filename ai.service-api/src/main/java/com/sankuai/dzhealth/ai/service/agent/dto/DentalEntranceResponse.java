package com.sankuai.dzhealth.ai.service.agent.dto;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@TypeDoc(description = "口腔频道页入口")
@ThriftStruct
@AllArgsConstructor
@NoArgsConstructor
public class DentalEntranceResponse {
    
    /**
     * 引导问题文案
     */
    @FieldDoc(description = "引导问题文案")
    @Getter(onMethod_ = @ThriftField(1))
    @Setter(onMethod_ = @ThriftField())
    private String guideQuestionText;

    /**
     * 有文案跳链 - 带引导问题的跳转链接
     */
    @FieldDoc(description = "有文案跳链 - 带引导问题的跳转链接")
    @Getter(onMethod_ = @ThriftField(2))
    @Setter(onMethod_ = @ThriftField())
    private String jumpLinkWithText;

    /**
     * 无文案跳链 - 纯跳转链接
     */
    @FieldDoc(description = "无文案跳链 - 纯跳转链接")
    @Getter(onMethod_ = @ThriftField(3))
    @Setter(onMethod_ = @ThriftField())
    private String jumpLinkWithoutText;

}
