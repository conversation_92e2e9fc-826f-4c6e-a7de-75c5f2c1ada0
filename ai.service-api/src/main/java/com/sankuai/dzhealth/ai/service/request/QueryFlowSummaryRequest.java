package com.sankuai.dzhealth.ai.service.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.*;

@ThriftStruct
@TypeDoc(name="查询决策流概览请求")
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class QueryFlowSummaryRequest {
    @FieldDoc(name="业务场景标识")
    @Getter(onMethod_=@ThriftField(1))
    @Setter(onMethod_=@ThriftField)
    private String bizScene;

    @FieldDoc(name="是否预览灰度版")
    @Getter(onMethod_=@ThriftField(2))
    @Setter(onMethod_=@ThriftField)
    private Boolean previewGray;
}

