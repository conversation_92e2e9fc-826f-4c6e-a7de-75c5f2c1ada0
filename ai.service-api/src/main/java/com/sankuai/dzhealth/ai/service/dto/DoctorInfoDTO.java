package com.sankuai.dzhealth.ai.service.dto;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.*;

import java.io.Serializable;

/**
 * 医生信息DTO
 * 用于商品问答中展示医生相关信息
 */
@ThriftStruct
@ToString
@EqualsAndHashCode
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TypeDoc(name = "医生信息")
public class DoctorInfoDTO implements Serializable {

    @FieldDoc(name = "医生ID", description = "医生唯一标识")
    @Getter(onMethod_ = @ThriftField(value = 1))
    @Setter(onMethod_ = @ThriftField)
    private String doctorId;

    @FieldDoc(name = "医生姓名", description = "医生真实姓名")
    @Getter(onMethod_ = @ThriftField(value = 2))
    @Setter(onMethod_ = @ThriftField)
    private String name;

    @FieldDoc(name = "医生职称", description = "如主任医师、副主任医师等")
    @Getter(onMethod_ = @ThriftField(value = 3))
    @Setter(onMethod_ = @ThriftField)
    private String title;

    @FieldDoc(name = "医院等级", description = "如三甲、二甲等")
    @Getter(onMethod_ = @ThriftField(value = 4))
    @Setter(onMethod_ = @ThriftField)
    private String hospitalLevel;

    @FieldDoc(name = "医生头像", description = "医生头像图片URL")
    @Getter(onMethod_ = @ThriftField(value = 5))
    @Setter(onMethod_ = @ThriftField)
    private String avatar;
}

