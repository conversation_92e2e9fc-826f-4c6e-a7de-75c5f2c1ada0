package com.sankuai.dzhealth.ai.service.agent.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;

import java.io.Serializable;

/**
 * @author:chenwei
 * @time: 2025/7/10 10:13
 * @version: 0.0.1
 */

@TypeDoc(description = "会话操作请求")
@ThriftStruct
public class SessionManageRequest implements Serializable {

    @FieldDoc(description = "用户ID")
    private Long userId;

    @FieldDoc(description = "会话ID")
    private String sessionId;

    @FieldDoc(description = "消息ID")
    private String msgId;

    @FieldDoc(description = "操作类型：0-无动作, 1-点赞，2-点踩")
    private Integer operateType = 0;

    @FieldDoc(description = "是否删除全部会话")
    private Boolean deleteTotalSession = false;

    @FieldDoc(description = "页码")
    private Integer pageNo;

    @FieldDoc(description = "页大小")
    private Integer pageSize;

    @FieldDoc(description = "场景,0-进入页面,1-请求历史会话,2-新建会话, 3-商详页进入, 4-查询历史消息, 5-删除会话操作, 6-点赞消息")
    private Integer scene = 0;

    @FieldDoc(description = "业务类型")
    private String bizType;

    @FieldDoc(description = "业务id")
    private String bizId;

    @FieldDoc(description = "平台")
    private Integer platform = 2;

    @FieldDoc(description = "历史消息")
    private String historyMsg;


    @ThriftField(1)
    public Long getUserId() {
        return this.userId;
    }

    @ThriftField
    public void setUserId(Long userId) {
        this.userId = userId;
    }

    @ThriftField(2)
    public String getSessionId() {
        return this.sessionId;
    }

    @ThriftField
    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }

    @ThriftField(3)
    public String getMsgId() {
        return this.msgId;
    }

    @ThriftField
    public void setMsgId(String msgId) {
        this.msgId = msgId;
    }

    @ThriftField(4)
    public Integer getOperateType() {
        return this.operateType;
    }

    @ThriftField
    public void setOperateType(Integer operateType) {
        this.operateType = operateType;
    }

    @ThriftField(5)
    public Boolean getDeleteTotalSession() {
        return this.deleteTotalSession;
    }

    @ThriftField
    public void setDeleteTotalSession(Boolean deleteTotalSession) {
        this.deleteTotalSession = deleteTotalSession;
    }

    @ThriftField(6)
    public Integer getPageNo() {
        return this.pageNo;
    }

    @ThriftField
    public void setPageNo(Integer pageNo) {
        this.pageNo = pageNo;
    }

    @ThriftField(7)
    public Integer getPageSize() {
        return this.pageSize;
    }

    @ThriftField
    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    @ThriftField(8)
    public Integer getScene() {
        return this.scene;
    }

    @ThriftField
    public void setScene(Integer scene) {
        this.scene = scene;
    }

    @ThriftField(9)
    public String getBizType() {
        return this.bizType;
    }

    @ThriftField
    public void setBizType(String bizType) {
        this.bizType = bizType;
    }

    @ThriftField(10)
    public String getBizId() {
        return this.bizId;
    }

    @ThriftField
    public void setBizId(String bizId) {
        this.bizId = bizId;
    }

    @ThriftField(11)
    public Integer getPlatform() {
        return this.platform;
    }

    @ThriftField
    public void setPlatform(Integer platform) {
        this.platform = platform;
    }

    @ThriftField(12)
    public String getHistoryMsg() {
        return this.historyMsg;
    }

    @ThriftField
    public void setHistoryMsg(String historyMsg) {
        this.historyMsg = historyMsg;
    }
}
