package com.sankuai.dzhealth.ai.service.api;


import com.facebook.swift.service.ThriftService;
import com.meituan.servicecatalog.api.annotations.ServiceDoc;
import com.sankuai.dzhealth.ai.service.dto.MessageDTO;
import com.sankuai.dzhealth.ai.service.dto.StatusDTO;
import com.sankuai.dzhealth.ai.service.request.ChatSummaryRequest;
import com.sankuai.dzhealth.ai.service.request.MtCallRequest;
import com.sankuai.dzhealth.ai.service.request.MtCallTaskRequest;

@ServiceDoc(appkey = "com.sankuai.dzhealth.ai.service",
        name = "电话外呼接口",
        description = "电话外呼相关接口",
        scenarios = "电话外呼",
        notice = "电话外呼")
@ThriftService
public interface MtCallService {

    // 生成外呼任务
    RemoteResponse<MessageDTO> generateCallTask(MtCallTaskRequest request);

    // 电话外呼
    RemoteResponse<StatusDTO> mtCallHospital(MtCallRequest request);

    // 模板匹配
    RemoteResponse<String >callTemplateMatch(String question);

    // 取消外呼
    RemoteResponse<StatusDTO> mtCallHospitalCancel(MtCallRequest request);

    RemoteResponse<String> getChatSummary(ChatSummaryRequest chatSummaryRequest);

    RemoteResponse<MessageDTO>  getDialogDate(MtCallRequest request);

}
