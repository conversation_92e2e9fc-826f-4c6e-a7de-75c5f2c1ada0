package com.sankuai.dzhealth.ai.service.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 业务关联类型枚举
 * @author: duanxiaowen
 * @date: 2025/3/19
 */
@Getter
@AllArgsConstructor
public enum BusinessTypeEnum {

    SHOP("SHOP", "门店"),
    PRODUCT("PRODUCT", "商品"),
    ORDER("ORDER", "订单"),
    ACTIVITY("ACTIVITY", "活动");

    private final String type;
    private final String desc;
    /**
     * 根据类型获取枚举
     * @param type 类型
     * @return 枚举
     */
    public static BusinessTypeEnum getByType(String type) {
        for (BusinessTypeEnum businessType : BusinessTypeEnum.values()) {
            if (businessType.getType().equals(type)) {
                return businessType;
            }
        }
        return SHOP; // 默认返回SHOP类型
    }
}