package com.sankuai.dzhealth.ai.service.dto;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;

import java.io.Serializable;

/**
 * <AUTHOR> you<PERSON>yu
 * @version : 0.1
 * @since : 2025/4/8
 */
@ThriftStruct
public class WebSearchDTO implements Serializable {

    @FieldDoc(name = "网站URL")
    private String url;

    /**
     * 网站摘要片段
     */
    @FieldDoc(name = "网站摘要片段")
    private String snippet;

    /**
     * 网站标题
     */
    @FieldDoc(name = "网站标题")
    private String name;

    @FieldDoc(name = "发布时间")
    private String datePublished;

    @ThriftField(1)
    public String getUrl() {
        return this.url;
    }

    @ThriftField
    public void setUrl(String url) {
        this.url = url;
    }

    @ThriftField(2)
    public String getSnippet() {
        return this.snippet;
    }

    @ThriftField
    public void setSnippet(String snippet) {
        this.snippet = snippet;
    }

    @ThriftField(3)
    public String getName() {
        return this.name;
    }

    @ThriftField
    public void setName(String name) {
        this.name = name;
    }

    @ThriftField(4)
    public String getDatePublished() {
        return this.datePublished;
    }

    @ThriftField
    public void setDatePublished(String datePublished) {
        this.datePublished = datePublished;
    }
}
