package com.sankuai.dzhealth.ai.service.dto.decision;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.*;

import java.io.Serializable;
import java.util.List;

@ThriftStruct
@TypeDoc(name = "决策流概览信息")
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class DecisionFlowSummaryDTO implements Serializable {

    @FieldDoc(name = "业务场景")
    @Getter(onMethod_ = @ThriftField(value = 1))
    @Setter(onMethod_ = @ThriftField)
    private String bizScene;

    @FieldDoc(name = "节点总数")
    @Getter(onMethod_ = @ThriftField(value = 2))
    @Setter(onMethod_ = @ThriftField)
    private Integer totalNodes;

    @FieldDoc(name = "路径总数")
    @Getter(onMethod_ = @ThriftField(value = 3))
    @Setter(onMethod_ = @ThriftField)
    private Integer totalEdges;

    @FieldDoc(name = "根节点列表")
    @Getter(onMethod_ = @ThriftField(value = 4))
    @Setter(onMethod_ = @ThriftField)
    private List<DecisionNodeDTO> rootNodes;

    @FieldDoc(name = "叶子节点数量")
    @Getter(onMethod_ = @ThriftField(value = 5))
    @Setter(onMethod_ = @ThriftField)
    private Integer leafNodeCount;

    @FieldDoc(name = "最后更新时间")
    @Getter(onMethod_ = @ThriftField(value = 6))
    @Setter(onMethod_ = @ThriftField)
    private Long lastUpdateTime;

    @FieldDoc(name = "决策流状态")
    @Getter(onMethod_ = @ThriftField(value = 7))
    @Setter(onMethod_ = @ThriftField)
    private String flowStatus;
}

