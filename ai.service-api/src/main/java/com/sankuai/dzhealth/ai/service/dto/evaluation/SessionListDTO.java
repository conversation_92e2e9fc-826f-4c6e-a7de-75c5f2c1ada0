package com.sankuai.dzhealth.ai.service.dto.evaluation;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 测评列表DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SessionListDTO {
    /**
     * 测评ID
     */
    @JsonProperty("session_id")
    private String sessionId;

    /**
     * case ID
     */
    @JsonProperty("case_id")
    private String caseId;

    /**
     * 测评名称
     */
    @JsonProperty("session_name")
    private String sessionName;

    /**
     * 测评状态 0-进行中 1-已完成
     */
    private String status;

    /**
     * 创建时间
     */
    @JsonProperty("create_time")
    private String createTime;

    /**
     * 结束时间
     */
    @JsonProperty("end_time")
    private String endTime;
}

