package com.sankuai.dzhealth.ai.service.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;

@TypeDoc(description = "外呼生成请求")
@ThriftStruct
public class MtCallTaskRequest {
    @FieldDoc(description = "会话ID")
    private Long sessionId;

    @FieldDoc(description = "美团id")
    private  Long mtShopId;

    @FieldDoc(description = "问题")
    private String question;

    @FieldDoc(description = "用户id")
    private String userId;

    @ThriftField(1)
    public Long getSessionId() {
        return this.sessionId;
    }

    @ThriftField
    public void setSessionId(Long sessionId) {
        this.sessionId = sessionId;
    }


    @ThriftField(2)
    public Long getMtShopId() {
        return this.mtShopId;
    }

    @ThriftField
    public void setMtShopId(Long mtShopId) {
        this.mtShopId = mtShopId;
    }

    @ThriftField(3)
    public String getQuestion() {
        return this.question;
    }

    @ThriftField
    public void setQuestion(String question) {
        this.question = question;
    }

    @ThriftField(4)
    public String getUserId() {
        return this.userId;
    }

    @ThriftField
    public void setUserId(String userId) {
        this.userId = userId;
    }


}
