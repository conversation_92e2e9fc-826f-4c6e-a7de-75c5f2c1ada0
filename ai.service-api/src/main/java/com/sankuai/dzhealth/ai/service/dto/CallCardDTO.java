package com.sankuai.dzhealth.ai.service.dto;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;

import java.io.Serializable;

/**
 * @author:chenwei
 * @time: 2025/4/1 10:31
 * @version: 0.0.1
 */
@ThriftStruct
public class CallCardDTO implements Serializable {

    @FieldDoc(description = "状态")
    private Integer status;

    @FieldDoc(description = "顶部标题")
    private String topTitle;

    @FieldDoc(description = "顶部图标")
    private String topIcon;

    @FieldDoc(description = "底部标题")
    private String bottomTitle;

    @FieldDoc(description = "副标题")
    private String subTitle;

    @FieldDoc(description = "精炼文本")
    private String refinedTxt;

    @FieldDoc(description = "文本内容")
    private String txt;

    @FieldDoc(description = "跳转链接")
    private String jumpUrl;

    @FieldDoc(description = "按钮文本")
    private String buttonTxt;

    @FieldDoc(description = "历史文本")
    private String historyTxt;

    @FieldDoc(description = "历史跳转链接")
    private String historyJumpUrl;

    @FieldDoc(description ="外呼次数")
    private int callTime;

    @FieldDoc(description ="开始时间")
    private String startTime;

    @FieldDoc(description ="任务Id")
    private Long taskId;


    @ThriftField(1)
    public Integer getStatus() {
        return this.status;
    }

    @ThriftField
    public void setStatus(Integer status) {
        this.status = status;
    }

    @ThriftField(2)
    public String getTopTitle() {
        return this.topTitle;
    }

    @ThriftField
    public void setTopTitle(String topTitle) {
        this.topTitle = topTitle;
    }

    @ThriftField(3)
    public String getTopIcon() {
        return this.topIcon;
    }

    @ThriftField
    public void setTopIcon(String topIcon) {
        this.topIcon = topIcon;
    }

    @ThriftField(4)
    public String getBottomTitle() {
        return this.bottomTitle;
    }

    @ThriftField
    public void setBottomTitle(String bottomTitle) {
        this.bottomTitle = bottomTitle;
    }

    @ThriftField(5)
    public String getSubTitle() {
        return this.subTitle;
    }

    @ThriftField
    public void setSubTitle(String subTitle) {
        this.subTitle = subTitle;
    }

    @ThriftField(6)
    public String getRefinedTxt() {
        return this.refinedTxt;
    }

    @ThriftField
    public void setRefinedTxt(String refinedTxt) {
        this.refinedTxt = refinedTxt;
    }

    @ThriftField(7)
    public String getTxt() {
        return this.txt;
    }

    @ThriftField
    public void setTxt(String txt) {
        this.txt = txt;
    }

    @ThriftField(8)
    public String getJumpUrl() {
        return this.jumpUrl;
    }

    @ThriftField
    public void setJumpUrl(String jumpUrl) {
        this.jumpUrl = jumpUrl;
    }

    @ThriftField(9)
    public String getButtonTxt() {
        return this.buttonTxt;
    }

    @ThriftField
    public void setButtonTxt(String buttonTxt) {
        this.buttonTxt = buttonTxt;
    }

    @ThriftField(10)
    public String getHistoryTxt() {
        return this.historyTxt;
    }

    @ThriftField
    public void setHistoryTxt(String historyTxt) {
        this.historyTxt = historyTxt;
    }

    @ThriftField(11)
    public String getHistoryJumpUrl() {
        return this.historyJumpUrl;
    }

    @ThriftField
    public void setHistoryJumpUrl(String historyJumpUrl) {
        this.historyJumpUrl = historyJumpUrl;
    }

    @ThriftField(12)
    public int getCallTime() {
        return this.callTime;
    }

    @ThriftField
    public void setCallTime(int callTime) {
        this.callTime = callTime;
    }

    @ThriftField(13)
    public String getStartTime() {
        return this.startTime;
    }

    @ThriftField
    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    @ThriftField(14)
    public Long getTaskId() {
        return this.taskId;
    }

    @ThriftField
    public void setTaskId(Long taskId) {
        this.taskId = taskId;
    }
}
