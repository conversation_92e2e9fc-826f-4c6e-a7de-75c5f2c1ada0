package com.sankuai.dzhealth.ai.service.dto.stream;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;

import java.util.Map;

/**
 * @author:chenwei
 * @time: 2025/3/20 12:18
 * @version: 0.0.1
 */
@ThriftStruct
public class StreamEventCardDataDTO {

    private String type;

    private String key;

    private Map<String, Object> cardProps;


    @ThriftField(1)
    public String getType() {
        return this.type;
    }

    @ThriftField
    public void setType(String type) {
        this.type = type;
    }

    @ThriftField(2)
    public String getKey() {
        return this.key;
    }

    @ThriftField
    public void setKey(String key) {
        this.key = key;
    }

    @ThriftField(3)
    public Map<String, Object> getCardProps() {
        return this.cardProps;
    }

    @ThriftField
    public void setCardProps(Map<String, Object> cardProps) {
        this.cardProps = cardProps;
    }
}
