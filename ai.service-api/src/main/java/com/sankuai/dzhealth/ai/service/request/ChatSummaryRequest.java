package com.sankuai.dzhealth.ai.service.request;
import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

@Data
@ThriftStruct
public class ChatSummaryRequest {
    @FieldDoc(name = "任务id")
    @Getter(onMethod_ = @ThriftField(value = 1))
    @Setter(onMethod_ = @ThriftField)
    private String taskId;

    @FieldDoc(name = "用户意图")
    @Getter(onMethod_ = @ThriftField(value = 2))
    @Setter(onMethod_ = @ThriftField)
    private String userIntention;

    @FieldDoc(name = "对话内容")
    @Getter(onMethod_ = @ThriftField(value = 3))
    @Setter(onMethod_ = @ThriftField)
    private String dialogueContent;

    @FieldDoc(name = "总结场景, com.sankuai.dzhealth.ai.service.enums.SummarySceneEnum")
    @Getter(onMethod_ = @ThriftField(value = 4))
    @Setter(onMethod_ = @ThriftField)
    private Integer sceneType;
}
