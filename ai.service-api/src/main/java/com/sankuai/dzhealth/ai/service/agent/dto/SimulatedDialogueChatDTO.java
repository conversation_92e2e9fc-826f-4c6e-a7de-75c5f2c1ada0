package com.sankuai.dzhealth.ai.service.agent.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 模拟对话聊天数据传输对象
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SimulatedDialogueChatDTO {

    /**
     * 思考过程
     */
    private String thought;

    /**
     * 话题列表
     */
    private List<Topic> topics;

    /**
     * 已讨论的话题
     */
    private List<String> discussed;

    /**
     * 行为模式
     */
    private String behaviour;

    /**
     * 响应内容
     */
    private String response;
}


