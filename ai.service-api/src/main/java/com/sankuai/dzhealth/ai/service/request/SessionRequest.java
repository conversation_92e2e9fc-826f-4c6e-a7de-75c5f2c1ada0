package com.sankuai.dzhealth.ai.service.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;

import java.io.Serializable;
import java.util.List;

/**
 * @author:chenwei
 * @time: 2025/3/25 10:14
 * @version: 0.0.1
 */
@TypeDoc(description = "会话操作请求")
@ThriftStruct
public class SessionRequest implements Serializable {
    @FieldDoc(description = "用户ID")
    private Long userId;

    @FieldDoc(description = "会话ID")
    private Long sessionId;

    @FieldDoc(description = "消息ID")
    private Long msgId;

    @FieldDoc(description = "操作类型：1-点赞，2-点踩")
    private Integer operateType = 0;

    @FieldDoc(description = "是否删除全部会话")
    private Boolean totalSession;

    @FieldDoc(description = "页码")
    private Integer pageNo;

    @FieldDoc(description = "页大小")
    private Integer pageSize;

    @FieldDoc(description = "场景,0-进入页面,1-请求历史会话,2-新建会话")
    private Integer scene = 1;

    @FieldDoc(description = "商户id")
    private Long shopId;

    @FieldDoc(description = "平台")
    private Integer platform = 2;

    @FieldDoc(description = "历史消息")
    private List<String> historyMsgList;
    
    @ThriftField(1)
    public Long getUserId() {
        return userId;
    }
    
    @ThriftField
    public void setUserId(Long userId) {
        this.userId = userId;
    }
    
    @ThriftField(2)
    public Long getSessionId() {
        return sessionId;
    }
    
    @ThriftField
    public void setSessionId(Long sessionId) {
        this.sessionId = sessionId;
    }
    
    @ThriftField(3)
    public Long getMsgId() {
        return msgId;
    }
    
    @ThriftField
    public void setMsgId(Long msgId) {
        this.msgId = msgId;
    }
    
    @ThriftField(4)
    public Integer getOperateType() {
        return operateType;
    }
    
    @ThriftField
    public void setOperateType(Integer operateType) {
        this.operateType = operateType;
    }
    
    @ThriftField(5)
    public Boolean getTotalSession() {
        return totalSession;
    }
    
    @ThriftField
    public void setTotalSession(Boolean totalSession) {
        this.totalSession = totalSession;
    }


    @ThriftField(6)
    public Integer getPageNo() {
        return this.pageNo;
    }

    @ThriftField
    public void setPageNo(Integer pageNo) {
        this.pageNo = pageNo;
    }

    @ThriftField(7)
    public Integer getPageSize() {
        return this.pageSize;
    }

    @ThriftField
    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }


    @ThriftField(8)
    public Integer getScene() {
        return this.scene;
    }

    @ThriftField
    public void setScene(Integer scene) {
        this.scene = scene;
    }


    @ThriftField(9)
    public Long getShopId() {
        return this.shopId;
    }

    @ThriftField
    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }


    @ThriftField(10)
    public Integer getPlatform() {
        return this.platform;
    }

    @ThriftField
    public void setPlatform(Integer platform) {
        this.platform = platform;
    }


    @ThriftField(11)
    public List<String> getHistoryMsgList() {
        return this.historyMsgList;
    }

    @ThriftField
    public void setHistoryMsgList(List<String> historyMsgList) {
        this.historyMsgList = historyMsgList;
    }
}
