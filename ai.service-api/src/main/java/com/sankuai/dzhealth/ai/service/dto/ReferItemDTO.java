package com.sankuai.dzhealth.ai.service.dto;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;

import java.io.Serializable;

/**
 * @author:chenwei
 * @time: 2025/3/26 20:13
 * @version: 0.0.1
 */
@ThriftStruct
public class ReferItemDTO implements Serializable {

    @FieldDoc(
            description = "引用文本"
    )
    private String content;

    @FieldDoc(
            description = "引用链接"
    )
    private String uri;

    @FieldDoc(
            description = "发布时间"
    )
    private String publishTime;

    @FieldDoc(
            description = "来源"
    )
    private String source;

    @FieldDoc(
            description = "序号"
    )
    private String index;


    @ThriftField(1)
    public String getContent() {
        return this.content;
    }

    @ThriftField
    public void setContent(String content) {
        this.content = content;
    }

    @ThriftField(2)
    public String getUri() {
        return this.uri;
    }

    @ThriftField
    public void setUri(String uri) {
        this.uri = uri;
    }

    @ThriftField(3)
    public String getPublishTime() {
        return this.publishTime;
    }

    @ThriftField
    public void setPublishTime(String publishTime) {
        this.publishTime = publishTime;
    }


    @ThriftField(4)
    public String getSource() {
        return this.source;
    }

    @ThriftField
    public void setSource(String source) {
        this.source = source;
    }


    @ThriftField(5)
    public String getIndex() {
        return this.index;
    }

    @ThriftField
    public void setIndex(String index) {
        this.index = index;
    }
}
