package com.sankuai.dzhealth.ai.service.api;

import com.facebook.swift.service.ThriftMethod;
import com.facebook.swift.service.ThriftService;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.meituan.servicecatalog.api.annotations.ParamDoc;
import com.sankuai.dzhealth.ai.service.dto.DocumentDTO;
import com.sankuai.dzhealth.ai.service.request.DocumentSearchRequest;

import java.util.List;

/**
 * <AUTHOR> youtianyu
 * @version : 0.1
 * @since : 2025/3/17
 */
@ThriftService
public interface ESVectorStoreService {

    @ThriftMethod
    @MethodDoc(name = "创建索引", description = "创建索引", responseParams = {
            @ParamDoc(name = "索引文档",
                      description = "索引文档",
                      example = "[\n" +
                                "    {\n" +
                                "        \"id\": \"doc001\",\n" +
                                "        \"text\": \"这是一段示例文本，描述了医美项目相关内容。玻尿酸填充可以改善面部轮廓。\",\n" +
                                "        \"metadata\": {\n" +
                                "            \"category\": \"填充\",\n" +
                                "            \"projectName\": \"玻尿酸\",\n" +
                                "            \"createTime\": \"2024-03-17\",\n" +
                                "            \"isValid\": true,\n" +
                                "            \"price\": 2999\n" +
                                "        },\n" +
                                "        \"score\": null\n" +
                                "    },\n" +
                                "    {\n" +
                                "        \"id\": \"doc002\",\n" +
                                "        \"text\": \"注射肉毒素可以改善面部表情纹，达到面部年轻化的效果。\",\n" +
                                "        \"metadata\": {\n" +
                                "            \"category\": \"注射\",\n" +
                                "            \"projectName\": \"肉毒素\",\n" +
                                "            \"createTime\": \"2024-03-17\",\n" +
                                "            \"isValid\": true,\n" +
                                "            \"price\": 3999\n" +
                                "        },\n" +
                                "        \"score\": null\n" +
                                "    }\n" +
                                "]")})
    RemoteResponse<Boolean> buildIndex(List<DocumentDTO> documents);

    @ThriftMethod
    @MethodDoc(name = "搜索", description = "搜索", responseParams = {
            @ParamDoc(name = "搜索结果",
                      description = "搜索结果",
                      example = "{\n" +
                                "    \"query\": \"这是一段示例文本，描述了医美项目相关内容。玻尿酸填充可以改善面部轮廓。\",\n" +
                                "    \"topK\": 20,\n" +
                                "    \"metaData\": {\n" +
                                "        \"isValid\": \"true\"\n" +
                                "    }\n" +
                                "}")})
    RemoteResponse<List<DocumentDTO>> similaritySearch(DocumentSearchRequest request);

    @ThriftMethod
    @MethodDoc(name = "删除索引", description = "删除索引", responseParams = {
            @ParamDoc(name = "删除结果", description = "删除结果", example = "true")})
    RemoteResponse<Boolean> deleteIndex(List<String> ids);
}
