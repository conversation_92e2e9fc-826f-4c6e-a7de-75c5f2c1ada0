package com.sankuai.dzhealth.ai.service.enums;

/**
 * 元数据字段统一枚举（API 层），避免在代码中出现硬编码的字符串常量。
 */
public enum MetadataKeyEnum {
    SHOP_ID("shopId"),
    REWRITE_TEXT("rewriteText"),
    SOURCE("source"),
    RESOURCE_URI("resource_uri"),
    PUBLISH_TIME("publish_time"),
    CHANNEL("channel"),
    BIZ_SCENE("biz_scene"),
    DOC_TYPE("doc_type"),
    RESOURCE_TYPE("resource_type"),
    STATUS("status"),
    URL("url"),
    URLS("urls"),
    TITLE("title");

    private final String key;

    MetadataKeyEnum(String key) {
        this.key = key;
    }

    public String getKey() {
        return key;
    }

    @Override
    public String toString() {
        return key;
    }
} 