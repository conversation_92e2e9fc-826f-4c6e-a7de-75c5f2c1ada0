package com.sankuai.dzhealth.ai.service.agent.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;

@ThriftStruct
public class GoodsShopQry {
    @ThriftField(1)
    private Long shopId;

    public boolean equals(Object o) {
        if (o == this) {
            return true;
        } else if (!(o instanceof GoodsShopQry)) {
            return false;
        } else {
            GoodsShopQry other = (GoodsShopQry)o;
            if (!other.canEqual(this)) {
                return false;
            } else {
                Object this$shopId = this.getShopId();
                Object other$shopId = other.getShopId();
                if (this$shopId == null) {
                    if (other$shopId != null) {
                        return false;
                    }
                } else if (!this$shopId.equals(other$shopId)) {
                    return false;
                }

                return true;
            }
        }
    }

    protected boolean canEqual(Object other) {
        return other instanceof GoodsShopQry;
    }

    public int hashCode() {
        int PRIME = 59;
        int result = 1;
        Object $shopId = this.getShopId();
        result = result * 59 + ($shopId == null ? 43 : $shopId.hashCode());
        return result;
    }

    public Long getShopId() {
        return this.shopId;
    }

    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }
}