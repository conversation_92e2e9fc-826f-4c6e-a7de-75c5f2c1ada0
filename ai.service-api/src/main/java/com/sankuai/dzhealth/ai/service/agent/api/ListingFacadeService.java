package com.sankuai.dzhealth.ai.service.agent.api;

import com.facebook.swift.service.ThriftMethod;
import com.facebook.swift.service.ThriftService;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.meituan.servicecatalog.api.annotations.ParamDoc;
import com.meituan.servicecatalog.api.annotations.ServiceDoc;
import com.sankuai.dzhealth.ai.service.agent.request.FillInfoRequest;
import com.sankuai.dzhealth.ai.service.api.RemoteResponse;

@ServiceDoc(appkey = "com.sankuai.dzhealth.ai.service",
        name = "ListingFacadeService",
        description = "大模型友好的商品详情查询接口",
        scenarios = "大模型友好的商品详情查询接口",
        notice = "大模型友好的商品详情查询接口")
@InterfaceDoc(type = "octo.thrift.annotation", displayName = "ListingFacadeService", description = "大模型友好的商品详情查询接口", scenarios = "大模型友好的商品详情查询接口")
@ThriftService
public interface ListingFacadeService {

    @MethodDoc(displayName = "queryListingForAI", description = "查询大模型友好的商品详情", parameters = {
            @ParamDoc(name = "fillInfoRequest", description = "查询参数")}, returnValueDescription = "JSON格式大模型友好的商品详情")
    @ThriftMethod
    RemoteResponse<String> queryListingForAI(FillInfoRequest fillInfoRequest);
}
