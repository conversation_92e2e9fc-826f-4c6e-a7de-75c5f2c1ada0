package com.sankuai.dzhealth.ai.service.dto.stream;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.sankuai.dzhealth.ai.service.dto.stream.StreamEventCardDataDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @author:chenwei
 * @time: 2025/3/20 12:17
 * @version: 0.0.1
 */
@ThriftStruct
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class StreamEventDataDTO {

    private String event;
    private String content;
    private List<StreamEventCardDataDTO> cardsData;


    @ThriftField(1)
    public String getEvent() {
        return this.event;
    }

    @ThriftField
    public void setEvent(String event) {
        this.event = event;
    }

    @ThriftField(2)
    public String getContent() {
        return this.content;
    }

    @ThriftField
    public void setContent(String content) {
        this.content = content;
    }

    @ThriftField(3)
    public List<StreamEventCardDataDTO> getCardsData() {
        return this.cardsData;
    }

    @ThriftField
    public void setCardsData(List<StreamEventCardDataDTO> cardsData) {
        this.cardsData = cardsData;
    }
}
