package com.sankuai.dzhealth.ai.service.api.xhs;

import com.facebook.swift.service.ThriftMethod;
import com.facebook.swift.service.ThriftService;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.meituan.servicecatalog.api.annotations.ParamDoc;
import com.meituan.servicecatalog.api.annotations.ServiceDoc;
import com.sankuai.dzhealth.ai.service.api.RemoteResponse;
import com.sankuai.dzhealth.ai.service.request.xhs.XhsBuildNoteRequest;

/**
@author:chenwei
@time: 2025/6/16 16:17
@version: 0.0.1
*/
@ServiceDoc(appkey = "com.sankuai.dzhealth.ai.service",
        name = "XhsNoteStoreService",
        description = "小红书笔记接口",
        scenarios = "AI接口",
        notice = "AI接口")
@InterfaceDoc(type = "octo.thrift.annotation", displayName = "XhsNoteStoreService", description = "小红书笔记接口", scenarios = "AI接口")
@ThriftService
public interface XhsNoteStoreService {

    @MethodDoc(displayName = "generateNote", description = "生产xhs笔记", parameters = {
            @ParamDoc(name = "request", description = "请求参数", example = {})}, returnValueDescription = "", extensions = {})
    @ThriftMethod
    RemoteResponse<String> generateNote(XhsBuildNoteRequest request);

    @ThriftMethod
    RemoteResponse<String> analyzeNote(XhsBuildNoteRequest request);
}
