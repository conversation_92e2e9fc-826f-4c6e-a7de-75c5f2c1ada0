package com.sankuai.dzhealth.ai.service.enums;

import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.List;


@Getter
@AllArgsConstructor
public enum StreamEventCardTypeEnum {

    RELATED_QUESTION_CARD("GuessAskQuestion", "相关问题卡片", true,true),

    FEEDBACK_TAIL_CARD("FeedbackTail", "反馈尾部卡片", false,true),

    REFERENCE_CARD("ReferenceSource", "数据来源卡片", false,true),

    DEPARTMENT_CARD("DepartmentCards", "科室卡片", false, true),

    HOSPITAL_CARD("HospitalCards", "医院卡片", false, true),

    TAB_CARD("TabCards", "追加问题卡片", false, true),

    TELEPHONE("TelephoneConsult", "电话卡片", false, true),

    RICH_TEXT_TITLE_CARD("RichTextTitleCard", "标题卡片", true, true),

    OPTION_CARD("OptionCard", "筛选项卡片", true, true),

    PHOTO_TAKING("PhotoTaking", "一键拍照卡片", true, true),

    PRODUCT_COMPARISON_CARD("ProductComparisonCard", "商品科普对比", false, true),

    APPOINTMENT_INFO_CARD("AppointmentInfoCard", "预约信息卡片", false, true),


    SHOP_PRODUCT_CARD("ShopProductCard", "商户卡片", false, true),
    DOCTOR_CARD("DoctorCard", "医师卡片", false, true),

    THINK_PROCESS_STREAM("ThinkProcessStream", "思考过程卡片", false,true),

    CASE_CARD("CaseCard", "案例卡片", false, true),

    PRODUCT_CARD("ProductCard", "商品卡片", true, true)












    ;


    private final String type;

    private final String desc;

    /**
     * 是否为气泡外卡片，类似猜你想问卡片
     */
    private boolean isOutBubble;

    /**
     * 是否需要卡片
     */
    private boolean isNeedCard;

    private static final List<StreamEventCardTypeEnum> NOT_DISPLAY_IN_MEMORY_CARD_TYPES = Lists.newArrayList(RELATED_QUESTION_CARD, FEEDBACK_TAIL_CARD, PHOTO_TAKING);

    public static StreamEventCardTypeEnum getByType(String type) {
        for (StreamEventCardTypeEnum typeEnum : values()) {
            if (typeEnum.getType().equals(type)) {
                return typeEnum;
            }
        }
        return null;
    }

    public String getPrefixTag() {
        return "<" + type + ">";
    }

    public String getSuffixTag() {
        return "</" + type + ">";
    }

    public static String buildCardContent(StreamEventCardTypeEnum typeEnum, String key) {
        return typeEnum.getPrefixTag() + key + typeEnum.getSuffixTag();
    }

    public static String buildCardContent(String type, String key) {
        return String.format("<%s>%s</%s>", type, key, type);
    }



    public static boolean excludeChatMemoryCardType(String type) {
        if (StringUtils.isBlank(type)) {
            return true;
        }
        return NOT_DISPLAY_IN_MEMORY_CARD_TYPES.contains(getByType(type));
    }
}
