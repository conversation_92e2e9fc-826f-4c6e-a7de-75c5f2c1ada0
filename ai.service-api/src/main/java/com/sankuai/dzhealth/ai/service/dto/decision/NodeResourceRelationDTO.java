package com.sankuai.dzhealth.ai.service.dto.decision;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.*;

import java.io.Serializable;

@ThriftStruct
@TypeDoc(name = "节点资源关联")
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class NodeResourceRelationDTO implements Serializable {
    @FieldDoc(name="业务场景")
    @Getter(onMethod_=@ThriftField(value = 1))
    @Setter(onMethod_=@ThriftField)
    private String bizScene;

    @FieldDoc(name = "节点ID")
    @Getter(onMethod_ = @ThriftField(value = 2))
    @Setter(onMethod_ = @ThriftField)
    private String nodeId;

    @FieldDoc(name = "资源ID")
    @Getter(onMethod_ = @ThriftField(value = 3))
    @Setter(onMethod_ = @ThriftField)
    private String resourceId;

    @FieldDoc(name = "资源类型")
    @Getter(onMethod_ = @ThriftField(value = 4))
    @Setter(onMethod_ = @ThriftField)
    private String resourceType;

    @FieldDoc(name = "资源排序")
    @Getter(onMethod_ = @ThriftField(value = 5))
    @Setter(onMethod_ = @ThriftField)
    private Long sortOrder;

    @FieldDoc(name = "状态")
    @Getter(onMethod_ = @ThriftField(value = 6))
    @Setter(onMethod_ = @ThriftField)
    private String status;

    @FieldDoc(name = "推荐理由")
    @Getter(onMethod_=@ThriftField(value = 7))
    @Setter(onMethod_=@ThriftField)
    private String rationale;
} 