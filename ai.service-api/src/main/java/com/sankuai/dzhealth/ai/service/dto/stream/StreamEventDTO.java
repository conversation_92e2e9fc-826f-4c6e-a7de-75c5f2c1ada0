package com.sankuai.dzhealth.ai.service.dto.stream;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.sankuai.dzhealth.ai.service.dto.stream.StreamEventDataDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.NoArgsConstructor;

/**
 * @author:chenwei
 * @time: 2025/3/20 12:16
 * @version: 0.0.1
 */
@ThriftStruct
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StreamEventDTO {

    private String type;

    private Integer index;

    private StreamEventDataDTO data;


    @ThriftField(1)
    public String getType() {
        return this.type;
    }

    @ThriftField
    public void setType(String type) {
        this.type = type;
    }

    @ThriftField(2)
    public Integer getIndex() {
        return this.index;
    }

    @ThriftField
    public void setIndex(Integer index) {
        this.index = index;
    }

    @ThriftField(3)
    public StreamEventDataDTO getData() {
        return this.data;
    }

    @ThriftField
    public void setData(StreamEventDataDTO data) {
        this.data = data;
    }
}
