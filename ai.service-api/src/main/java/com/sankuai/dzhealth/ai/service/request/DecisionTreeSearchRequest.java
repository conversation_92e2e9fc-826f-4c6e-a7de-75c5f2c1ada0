package com.sankuai.dzhealth.ai.service.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 请求：决策树搜索
 * <p>
 * 仅针对决策节点（decision）进行检索，返回匹配的完整决策树列表。
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DecisionTreeSearchRequest implements Serializable {

    /** 业务场景 */
    private String bizScene;

    /** 关键词（可选） */
    private String query;

    /** 返回条数，默认 20 */
    private Integer topK;

    /** 是否预览灰度（合并 GRAY 数据） */
    private Boolean previewGray;

    /** 搜索深度，-1表示搜索完整树，其他值表示指定层数 */
    private Integer maxDepth;
} 