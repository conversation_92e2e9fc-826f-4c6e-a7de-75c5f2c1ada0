package com.sankuai.dzhealth.ai.service.dto;

import com.meituan.servicecatalog.api.annotations.FieldDoc;

import java.io.Serializable;

/**
 * Markdown 转换结果 DTO
 */
public class MarkdownResponseDTO implements Serializable {

    @FieldDoc(description = "转换后的 Markdown 文本")
    private String markdown;

    public MarkdownResponseDTO() {
    }

    public MarkdownResponseDTO(String markdown) {
        this.markdown = markdown;
    }

    public String getMarkdown() {
        return markdown;
    }

    public void setMarkdown(String markdown) {
        this.markdown = markdown;
    }
} 