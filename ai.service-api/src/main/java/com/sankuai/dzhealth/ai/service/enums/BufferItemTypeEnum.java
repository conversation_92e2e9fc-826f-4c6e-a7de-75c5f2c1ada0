package com.sankuai.dzhealth.ai.service.enums;


import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum BufferItemTypeEnum {
    LOADING_STATUS(100, "状态"),
    THINK_PROCESS_STREAM(200, "思考流式文本"),
    MAIN_TEXT(300, "正文"),
    RECOMMEND_QUESTION(400, "推荐问题"),
    FINISH_WRITE(500, "写入完成"),
    ;

    private final int type;

    private final String desc;
    public static BufferItemTypeEnum getByType(int type) {
        for (BufferItemTypeEnum typeEnum : values()) {
            if (typeEnum.getType() == type) {
                return typeEnum;
            }
        }
        return null;
    }
}
