package com.sankuai.dzhealth.ai.service.dto.decision;

import lombok.*;

@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class ImportResultDTO {

    /** 是否成功 */
    private Boolean success;

    /** 提示信息 */
    private String message;

    /** 是否有变更 */
    private Boolean hasChanges;

    /** 节点变更数量 */
    private Integer nodeCount;

    /** 边变更数量 */
    private Integer edgeCount;

    /** 删除节点数量 */
    private Integer deletedNodeCount;

    /** 删除边数量 */
    private Integer deletedEdgeCount;

    /** 资源变更数量 */
    private Integer resourceCount;

    /** 关联关系变更数量 */
    private Integer relationCount;
}

