package com.sankuai.dzhealth.ai.service.dto.evaluation;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 测评列表DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MessageListDTO {
    /**
     *   字段: session_id
     *   说明: 对话id
     */
    @JsonProperty("session_id")
    private String sessionId;
    /**
     *   字段: message_id
     *   说明: 消息id
     */
    @JsonProperty("message_id")
    private String messageId;

    /**
     *   字段: messageContent
     *   说明: 消息内容
     */
    @JsonProperty("message_content")
    private String messageContent;

    /**
     *   字段: status
     *   说明: 测评状态,0-开始,1-失败,2-成功,3-人工评测完成
     */
    private Integer status;

    /**
     *   字段: create_time
     *   说明: 创建时间
     */
    @JsonProperty("create_time")
    private String createTime;
}

