package com.sankuai.dzhealth.ai.service.dto;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.*;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 思考会话DTO
 * 用于RPC接口传输
 */
@ThriftStruct
@ToString
@EqualsAndHashCode
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TypeDoc(name = "思考会话结果")
public class ThinkingSessionDTO implements Serializable {

    @FieldDoc(name = "会话ID")
    @Getter(onMethod_ = @ThriftField(value = 1))
    @Setter(onMethod_ = @ThriftField)
    private Long id;
    
    @FieldDoc(name = "查询问题")
    @Getter(onMethod_ = @ThriftField(value = 2))
    @Setter(onMethod_ = @ThriftField)
    private String query;
    
    @FieldDoc(name = "会话状态")
    @Getter(onMethod_ = @ThriftField(value = 3))
    @Setter(onMethod_ = @ThriftField)
    private String status;
    
    @FieldDoc(name = "最终答案")
    @Getter(onMethod_ = @ThriftField(value = 4))
    @Setter(onMethod_ = @ThriftField)
    private String finalAnswer;
    
    @FieldDoc(name = "思考步骤列表")
    @Getter(onMethod_ = @ThriftField(value = 5))
    @Setter(onMethod_ = @ThriftField)
    private List<ThinkingStepDTO> thoughts;
    
    @FieldDoc(name = "置信度评分", description = "答案的置信度评分，范围0-1，值越大表示置信度越高")
    @Getter(onMethod_ = @ThriftField(value = 6))
    @Setter(onMethod_ = @ThriftField)
    private Double confidenceScore;
    
    @FieldDoc(name = "开始时间")
    @Getter(onMethod_ = @ThriftField(value = 7))
    @Setter(onMethod_ = @ThriftField)
    private Date startTime;
    
    @FieldDoc(name = "结束时间")
    @Getter(onMethod_ = @ThriftField(value = 8))
    @Setter(onMethod_ = @ThriftField)
    private Date endTime;
}