package com.sankuai.dzhealth.ai.service.request.xhs;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;

import java.io.Serializable;
import java.util.Map;

/**
 * @author:chenwei
 * @time: 2025/6/16 16:01
 * @version: 0.0.1
 */
@ThriftStruct
public class XhsBuildNoteRequest implements Serializable {

    @FieldDoc(description = "本次生成的笔记批次")
    private String batch;

    @FieldDoc(description = "回调服务的appkey，用于异步通知生成结果")
    private String callbackAppkey;

    @FieldDoc(description = "业务场景，@BizTypeEnum, 这个字段决定了如何解析下面的 params")
    private String bizCode;

    @FieldDoc(description = "业务场景相关的动态参数。简单值直接存，复杂类型（如List）转为JSON字符串存储")
    private Map<String, String> params;

    @FieldDoc(description = "cookies")
    private String cookie;

    @FieldDoc(description = "yushu prompt")
    private String prompt;

    @FieldDoc(description = "超时时间, 时间为秒")
    private Long maxTimeout;



    @ThriftField(1)
    public String getBatch() {
        return this.batch;
    }

    @ThriftField
    public void setBatch(String batch) {
        this.batch = batch;
    }

    @ThriftField(2)
    public String getCallbackAppkey() {
        return callbackAppkey;
    }

    @ThriftField
    public void setCallbackAppkey(String callbackAppkey) {
        this.callbackAppkey = callbackAppkey;
    }


    @ThriftField(3)
    public String getBizCode() {
        return this.bizCode;
    }

    @ThriftField
    public void setBizCode(String bizCode) {
        this.bizCode = bizCode;
    }

    @ThriftField(4)
    public Map<String, String> getParams() {
        return this.params;
    }

    @ThriftField
    public void setParams(Map<String, String> params) {
        this.params = params;
    }


    @ThriftField(5)
    public String getCookie() {
        return this.cookie;
    }

    @ThriftField
    public void setCookie(String cookie) {
        this.cookie = cookie;
    }

    @ThriftField(6)
    public String getPrompt() {
        return this.prompt;
    }

    @ThriftField
    public void setPrompt(String prompt) {
        this.prompt = prompt;
    }


    @ThriftField(7)
    public Long getMaxTimeout() {
        return this.maxTimeout;
    }

    @ThriftField
    public void setMaxTimeout(Long maxTimeout) {
        this.maxTimeout = maxTimeout;
    }
}
