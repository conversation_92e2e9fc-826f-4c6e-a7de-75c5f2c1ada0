package com.sankuai.dzhealth.ai.service.api;

import com.facebook.swift.service.ThriftMethod;
import com.facebook.swift.service.ThriftService;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.meituan.servicecatalog.api.annotations.ParamDoc;
import com.sankuai.dzhealth.ai.service.dto.MarkdownResponseDTO;
import com.sankuai.dzhealth.ai.service.request.ContentTransferRequest;

/**
 * 内容转 Markdown 服务接口
 */
@ThriftService
public interface ContentProcessService {

    @ThriftMethod
    @MethodDoc(name = "内容转 Markdown", description = "兼容 HTML/纯文本/JSON 等，自动转换为 Markdown，并将图片解析为文本描述", responseParams = {
            @ParamDoc(name = "markdown", description = "转换后的 Markdown")})
    RemoteResponse<MarkdownResponseDTO> content2md(ContentTransferRequest request);
} 