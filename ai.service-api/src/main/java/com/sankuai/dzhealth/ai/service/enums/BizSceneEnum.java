package com.sankuai.dzhealth.ai.service.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * @author:chenwei
 * @time: 2025/3/21 17:10
 * @version: 0.0.1
 */

@Getter
@AllArgsConstructor
public enum BizSceneEnum {

    PUBLIC_HOSPITAL(1, "public_hospital", "公立医院导诊"),
    MEDICAL_CONSULT(2, "medical_consult", "医美Agent"),

    MEDICAL_RECOMMEND_LIST(3, "medical_recommend_list", "查看更多listing"),

    MOUTH_CONSULT(4, "mouth_consult", "口腔Agent"),

    ;

    private final int code;

    private final String bizScene;

    private final String desc;

    public static BizSceneEnum getByBizType(String bizScene) {
        if (StringUtils.isBlank(bizScene)) {
            return null;
        }
        
        for (BizSceneEnum bizSceneEnum : BizSceneEnum.values()) {
            if (bizSceneEnum.getBizScene().equals(bizScene)) {
                return bizSceneEnum;
            }
        }
        return null;
    }


}
