package com.sankuai.dzhealth.ai.service.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum EventCardShowTypeEnum {

    //BLZKnowledgeSource
    BLZ("KnowledgeSource", "信息来自：", "https://p0.meituan.net/travelcube/b86f4b20d498ea1828a411206794cf0c10315.png", 1, "18", "41.5");

    private final String knowledgeSourceType;
    private final String title;
    private final String icon;
    private final Integer showType;
    private final String iconHeight;
    private final String iconWidth;
    public static EventCardShowTypeEnum getByKnowledgeSourceType(String knowledgeSourceType) {
        for (EventCardShowTypeEnum typeEnum : values()) {
            if (typeEnum.getKnowledgeSourceType().equals(knowledgeSourceType)) {
                return typeEnum;
            }
        }
        return null;
    }

}
