package com.sankuai.dzhealth.ai.service.api;

import com.facebook.swift.service.ThriftService;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.meituan.servicecatalog.api.annotations.ParamDoc;
import com.sankuai.dzhealth.ai.service.dto.ThinkingSessionDTO;
import com.sankuai.dzhealth.ai.service.request.ThinkingSessionRequest;

/**
 * 思考引擎RPC服务接口
 */
@ThriftService
public interface ThinkingService {
    
    /**
     * 创建并执行思考会话
     *
     * @param request 思考会话请求
     * @return 思考会话结果
     */
    @MethodDoc(name = "创建思考会话", description = "通过序列思考方法解决问题", parameters = {
            @ParamDoc(name = "request", description = "思考会话请求参数")
    })
    RemoteResponse<ThinkingSessionDTO> createAndExecuteThinking(ThinkingSessionRequest request);
    
    /**
     * 根据会话ID获取思考结果
     *
     * @param sessionId 会话ID
     * @return 思考会话结果
     */
    @MethodDoc(name = "获取思考结果", description = "根据会话ID获取思考结果", parameters = {
            @ParamDoc(name = "sessionId", description = "会话ID")
    })
    RemoteResponse<ThinkingSessionDTO> getThinkingResult(Long sessionId);

}