package com.sankuai.dzhealth.ai.service.agent.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import lombok.Getter;
import lombok.Setter;

@ThriftStruct
public class DentalEntranceRequest {

    /**
     * 用户ID
     */
    @FieldDoc(description = "用户ID")
    @Getter(onMethod_ = @ThriftField(1))
    @Setter(onMethod_ = @ThriftField())
    private Long userId;

    /**
     * 平台标识
     */
    @FieldDoc(description = "平台标识，1:点评，2:美团")
    @Getter(onMethod_ = @ThriftField(2))
    @Setter(onMethod_ = @ThriftField())
    private Integer platform;
}
