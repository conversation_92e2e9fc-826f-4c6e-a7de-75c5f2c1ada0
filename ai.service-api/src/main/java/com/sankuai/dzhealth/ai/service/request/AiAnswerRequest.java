package com.sankuai.dzhealth.ai.service.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.dzhealth.ai.service.request.common.BasicParam;

import java.io.Serializable;

/**
 * @author:chenwei
 * @time: 2025/3/18 19:05
 * @version: 0.0.1
 */
@TypeDoc(
        description = "ai请求"
)
@FieldDoc(description = "ai请求")
@ThriftStruct
public class AiAnswerRequest implements Serializable {

    @FieldDoc(
            description = "基本参数信息"
    )
    private BasicParam basicParam;

    @FieldDoc(
            description = "会话id"
    )
    private Long sessionId;

    @FieldDoc(
            description = "前端请求id"
    )
    private String requestId;

    @FieldDoc(
            description = "扩展信息-json"
    )
    private String extension;

    @FieldDoc(
            description = "业务类型"

    )
    private String bizType;

    @FieldDoc(
            description = "请求时间"
    )
    private String requestTime;

    @FieldDoc(
            description = "请求内容"
    )
    private String content;

    @FieldDoc(
            description = "请求消息类型 1:text、2:image"
    )
    private Integer type;

    @FieldDoc(
            description = "角色: 见MsgRoleEnum"
    )
    private String role;

    @FieldDoc(
            description = "商户id"
    )
    private Long shopId;

    @FieldDoc(
            description = "是否流式"
    )
    private Boolean stream = true;

    @FieldDoc(
            description = "消息id"
    )
    private Long msgId;


    @FieldDoc(
            description = "平台"
    )
    private Integer platform;



    @ThriftField(1)
    public BasicParam getBasicParam() {
        return this.basicParam;
    }

    @ThriftField
    public void setBasicParam(BasicParam basicParam) {
        this.basicParam = basicParam;
    }

    @ThriftField(2)
    public Long getSessionId() {
        return this.sessionId;
    }

    @ThriftField
    public void setSessionId(Long sessionId) {
        this.sessionId = sessionId;
    }

    @ThriftField(3)
    public String getRequestId() {
        return this.requestId;
    }

    @ThriftField
    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    @ThriftField(4)
    public String getExtension() {
        return this.extension;
    }

    @ThriftField
    public void setExtension(String extension) {
        this.extension = extension;
    }

    @ThriftField(5)
    public String getBizType() {
        return this.bizType;
    }

    @ThriftField
    public void setBizType(String bizType) {
        this.bizType = bizType;
    }

    @ThriftField(6)
    public String getRequestTime() {
        return this.requestTime;
    }

    @ThriftField
    public void setRequestTime(String requestTime) {
        this.requestTime = requestTime;
    }

    @ThriftField(7)
    public String getContent() {
        return this.content;
    }

    @ThriftField
    public void setContent(String content) {
        this.content = content;
    }

    @ThriftField(8)
    public Integer getType() {
        return this.type;
    }

    @ThriftField
    public void setType(Integer type) {
        this.type = type;
    }


    @ThriftField(9)
    public String getRole() {
        return this.role;
    }

    @ThriftField
    public void setRole(String role) {
        this.role = role;
    }


    @ThriftField(10)
    public Long getShopId() {
        return this.shopId;
    }

    @ThriftField
    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }


    @ThriftField(11)
    public Boolean getStream() {
        return this.stream;
    }

    @ThriftField
    public void setStream(Boolean stream) {
        this.stream = stream;
    }


    @ThriftField(12)
    public Long getMsgId() {
        return this.msgId;
    }

    @ThriftField
    public void setMsgId(Long msgId) {
        this.msgId = msgId;
    }


    @ThriftField(13)
    public Integer getPlatform() {
        return this.platform;
    }

    @ThriftField
    public void setPlatform(Integer platform) {
        this.platform = platform;
    }
}
