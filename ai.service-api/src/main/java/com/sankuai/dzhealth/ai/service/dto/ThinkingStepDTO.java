package com.sankuai.dzhealth.ai.service.dto;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.*;

import java.io.Serializable;
import java.util.List;

/**
 * 思考步骤DTO
 * 用于RPC接口传输
 */
@ThriftStruct
@ToString
@EqualsAndHashCode
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TypeDoc(name = "思考步骤")
public class ThinkingStepDTO implements Serializable {

    @FieldDoc(name = "思考内容")
    @Getter(onMethod_ = @ThriftField(value = 1))
    @Setter(onMethod_ = @ThriftField)
    private String thought;
    
    @FieldDoc(name = "思考序号")
    @Getter(onMethod_ = @ThriftField(value = 2))
    @Setter(onMethod_ = @ThriftField)
    private Long thoughtNumber;
    
    @FieldDoc(name = "总思考步骤数")
    @Getter(onMethod_ = @ThriftField(value = 3))
    @Setter(onMethod_ = @ThriftField)
    private Long totalThoughts;
    
    @FieldDoc(name = "是否是对之前思考的修正")
    @Getter(onMethod_ = @ThriftField(value = 4))
    @Setter(onMethod_ = @ThriftField)
    private Boolean isRevision;
    
    @FieldDoc(name = "修正的是哪个思考步骤")
    @Getter(onMethod_ = @ThriftField(value = 5))
    @Setter(onMethod_ = @ThriftField)
    private Long revisesThought;
    
    @FieldDoc(name = "从哪个思考步骤分支出来的")
    @Getter(onMethod_ = @ThriftField(value = 6))
    @Setter(onMethod_ = @ThriftField)
    private Long branchFromThought;
    
    @FieldDoc(name = "分支ID")
    @Getter(onMethod_ = @ThriftField(value = 7))
    @Setter(onMethod_ = @ThriftField)
    private String branchId;
    
    @FieldDoc(name = "是否需要更多思考步骤")
    @Getter(onMethod_ = @ThriftField(value = 8))
    @Setter(onMethod_ = @ThriftField)
    private Boolean needsMoreThoughts;
    
    @FieldDoc(name = "是否需要下一个思考步骤")
    @Getter(onMethod_ = @ThriftField(value = 9))
    @Setter(onMethod_ = @ThriftField)
    private Boolean nextThoughtNeeded;
    
    @FieldDoc(name = "关联的搜索结果", description = "思考过程中引用的外部URL或搜索结果")
    @Getter(onMethod_ = @ThriftField(value = 10))
    @Setter(onMethod_ = @ThriftField)
    private List<String> searchResults;
    
    @FieldDoc(name = "思考的置信度评分", description = "此思考步骤的置信度评分，范围0-1，值越大表示置信度越高")
    @Getter(onMethod_ = @ThriftField(value = 11))
    @Setter(onMethod_ = @ThriftField)
    private Double confidenceScore;
} 