package com.sankuai.dzhealth.ai.service.dto.haima;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class EvaluationCaseInfo {
    private int num;

    private String type;

    private String project;

    @JsonProperty("user_info")
    private String userInfo;

    private double[] location;

    @JsonProperty("initial_question")
    private String initialQuestion;

    private String[] initialQuestions;

    @JsonProperty("answer")
    private String answer;
}
