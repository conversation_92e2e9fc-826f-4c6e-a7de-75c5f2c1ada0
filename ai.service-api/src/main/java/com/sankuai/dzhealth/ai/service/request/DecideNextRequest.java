package com.sankuai.dzhealth.ai.service.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.*;

@ThriftStruct
@TypeDoc(name="决策下一节点查询请求")
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class DecideNextRequest {
    @FieldDoc(name="当前节点业务ID")
    @Getter(onMethod_=@ThriftField(1))
    @Setter(onMethod_=@ThriftField)
    private String nodeId;

    @FieldDoc(name="匹配值（AI结果码/用户选项等）")
    @Getter(onMethod_=@ThriftField(2))
    @Setter(onMethod_=@ThriftField)
    private String edgeValue;
}

