package com.sankuai.dzhealth.ai.service.dto;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;

import java.util.List;

/**
 * 医院卡片数据传输对象
 */
@ThriftStruct
public class HospitalCardDTO {
    /**
     * 医院ID
     */
    private Long hospitalId;

    /**
     * 医院名称
     */
    private String hospitalName;

    /**
     * 医院类目
     */
    private String hospitalCategory;

    /**
     * 医院地址
     */
    private String address;

    /**
     * 与用户的距离，单位米
     */
    private String distance;

    /**
     * 推荐理由
     */
    private String recommendReason;

    /**
     * 医院详情页链接
     */
    private String poiUrl;

    private String departmentName;

    private Integer registerStatus;

    private String registerCanTime;

    private String departDetailUrl;

    private List<TagItemDTO> hospitalTags;

    private String buttonText;
    private List<DepartmentCardDTO> departmentCardDTOList;

    private String moreJumpUrl;


    @ThriftField(1)
    public Long getHospitalId() {
        return this.hospitalId;
    }

    @ThriftField
    public void setHospitalId(Long hospitalId) {
        this.hospitalId = hospitalId;
    }

    @ThriftField(2)
    public String getHospitalName() {
        return this.hospitalName;
    }

    @ThriftField
    public void setHospitalName(String hospitalName) {
        this.hospitalName = hospitalName;
    }

    @ThriftField(3)
    public String getHospitalCategory() {
        return this.hospitalCategory;
    }

    @ThriftField
    public void setHospitalCategory(String hospitalCategory) {
        this.hospitalCategory = hospitalCategory;
    }

    @ThriftField(4)
    public String getAddress() {
        return this.address;
    }

    @ThriftField
    public void setAddress(String address) {
        this.address = address;
    }

    @ThriftField(5)
    public String getDistance() {
        return this.distance;
    }

    @ThriftField
    public void setDistance(String distance) {
        this.distance = distance;
    }

    @ThriftField(6)
    public String getRecommendReason() {
        return this.recommendReason;
    }

    @ThriftField
    public void setRecommendReason(String recommendReason) {
        this.recommendReason = recommendReason;
    }

    @ThriftField(7)
    public String getPoiUrl() {
        return this.poiUrl;
    }

    @ThriftField
    public void setPoiUrl(String poiUrl) {
        this.poiUrl = poiUrl;
    }

    @ThriftField(8)
    public String getDepartmentName() {
        return this.departmentName;
    }

    @ThriftField
    public void setDepartmentName(String departmentName) {
        this.departmentName = departmentName;
    }

    @ThriftField(9)
    public Integer getRegisterStatus() {
        return this.registerStatus;
    }

    @ThriftField
    public void setRegisterStatus(Integer registerStatus) {
        this.registerStatus = registerStatus;
    }

    @ThriftField(10)
    public String getRegisterCanTime() {
        return this.registerCanTime;
    }

    @ThriftField
    public void setRegisterCanTime(String registerCanTime) {
        this.registerCanTime = registerCanTime;
    }

    @ThriftField(11)
    public String getDepartDetailUrl() {
        return this.departDetailUrl;
    }

    @ThriftField
    public void setDepartDetailUrl(String departDetailUrl) {
        this.departDetailUrl = departDetailUrl;
    }

    @ThriftField(12)
    public List<TagItemDTO> getHospitalTags() {
        return this.hospitalTags;
    }

    @ThriftField
    public void setHospitalTags(List<TagItemDTO> hospitalTags) {
        this.hospitalTags = hospitalTags;
    }


    @ThriftField(13)
    public String getButtonText() {
        return this.buttonText;
    }

    @ThriftField
    public void setButtonText(String buttonText) {
        this.buttonText = buttonText;
    }


    @ThriftField(14)
    public List<DepartmentCardDTO> getDepartmentCardDTOList() {
        return this.departmentCardDTOList;
    }

    @ThriftField
    public void setDepartmentCardDTOList(List<DepartmentCardDTO> departmentCardDTOList) {
        this.departmentCardDTOList = departmentCardDTOList;
    }


    @ThriftField(15)
    public String getMoreJumpUrl() {
        return this.moreJumpUrl;
    }

    @ThriftField
    public void setMoreJumpUrl(String moreJumpUrl) {
        this.moreJumpUrl = moreJumpUrl;
    }
}
