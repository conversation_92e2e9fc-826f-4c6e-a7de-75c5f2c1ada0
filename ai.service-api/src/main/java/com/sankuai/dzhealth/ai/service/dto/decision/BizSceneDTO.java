package com.sankuai.dzhealth.ai.service.dto.decision;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.dzhealth.ai.service.enums.DicisionFlowStatusEnum;
import lombok.*;

import java.io.Serializable;

/**
 * 业务场景 DTO，用于前端下拉框
 */
@ThriftStruct
@TypeDoc(name = "业务场景信息")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ToString
public class BizSceneDTO implements Serializable {

    @FieldDoc(name = "场景编码")
    @ThriftField(1)
    private Integer code;

    @FieldDoc(name = "bizScene 标识")
    @ThriftField(2)
    private String bizScene;

    @FieldDoc(name = "场景描述")
    @ThriftField(3)
    private String desc;

    @FieldDoc(name = "场景版本状态 EMPTY/ONLINE/GRAY_IN_PROGRESS")
    @ThriftField(4)
    private DicisionFlowStatusEnum status;
} 