package com.sankuai.dzhealth.ai.service.dto;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;

import java.io.Serializable;

/**
 * @author:chenwei
 * @time: 2025/3/28 17:25
 * @version: 0.0.1
 */
@ThriftStruct
public class DepartmentCardDTO implements Serializable {

    private Long departmentId;

    private String departmentName;

    private String recommendTag;

    private Integer registerStatus;

    private String registerCanTime;

    private String recommendReason;

    private String departDetailUrl;

    private String buttonText;


    @ThriftField(1)
    public Long getDepartmentId() {
        return this.departmentId;
    }

    @ThriftField
    public void setDepartmentId(Long departmentId) {
        this.departmentId = departmentId;
    }

    @ThriftField(2)
    public String getDepartmentName() {
        return this.departmentName;
    }

    @ThriftField
    public void setDepartmentName(String departmentName) {
        this.departmentName = departmentName;
    }

    @ThriftField(3)
    public String getRecommendTag() {
        return this.recommendTag;
    }

    @ThriftField
    public void setRecommendTag(String recommendTag) {
        this.recommendTag = recommendTag;
    }

    @ThriftField(4)
    public Integer getRegisterStatus() {
        return this.registerStatus;
    }

    @ThriftField
    public void setRegisterStatus(Integer registerStatus) {
        this.registerStatus = registerStatus;
    }

    @ThriftField(5)
    public String getRegisterCanTime() {
        return this.registerCanTime;
    }

    @ThriftField
    public void setRegisterCanTime(String registerCanTime) {
        this.registerCanTime = registerCanTime;
    }

    @ThriftField(6)
    public String getRecommendReason() {
        return this.recommendReason;
    }

    @ThriftField
    public void setRecommendReason(String recommendReason) {
        this.recommendReason = recommendReason;
    }

    @ThriftField(7)
    public String getDepartDetailUrl() {
        return this.departDetailUrl;
    }

    @ThriftField
    public void setDepartDetailUrl(String departDetailUrl) {
        this.departDetailUrl = departDetailUrl;
    }


    @ThriftField(8)
    public String getButtonText() {
        return this.buttonText;
    }

    @ThriftField
    public void setButtonText(String buttonText) {
        this.buttonText = buttonText;
    }
}
