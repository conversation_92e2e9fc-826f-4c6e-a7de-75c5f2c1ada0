package com.sankuai.dzhealth.ai.service.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.*;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> youtianyu
 * @version : 0.1
 * @since : 2025/3/17
 */
@ThriftStruct
@EqualsAndHashCode
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ToString
@TypeDoc(name = "文档检索请求")
public class DocumentSearchRequest implements Serializable {
    @FieldDoc(name = "检索关键词")
    @Getter(onMethod_ = @ThriftField(value = 1))
    @Setter(onMethod_ = @ThriftField)
    private String query;

    @FieldDoc(name = "返回数量")
    @Getter(onMethod_ = @ThriftField(value = 2))
    @Setter(onMethod_ = @ThriftField)
    private Integer topK;

    @FieldDoc(name = "过滤元数据")
    @Getter(onMethod_ = @ThriftField(value = 3))
    @Setter(onMethod_ = @ThriftField)
    private Map<String, List<String>> metaData;

}