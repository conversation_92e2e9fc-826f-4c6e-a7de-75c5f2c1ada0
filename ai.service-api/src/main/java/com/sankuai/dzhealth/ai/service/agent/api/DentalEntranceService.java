package com.sankuai.dzhealth.ai.service.agent.api;

import com.facebook.swift.service.ThriftMethod;
import com.facebook.swift.service.ThriftService;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.meituan.servicecatalog.api.annotations.ParamDoc;
import com.meituan.servicecatalog.api.annotations.ServiceDoc;
import com.sankuai.dzhealth.ai.service.agent.dto.DentalEntranceResponse;
import com.sankuai.dzhealth.ai.service.agent.request.DentalEntranceRequest;
import com.sankuai.dzhealth.ai.service.api.RemoteResponse;

@ServiceDoc(appkey = "com.sankuai.dzhealth.ai.service",
        name = "DentalEntranceService",
        description = "口腔频道页入口接口",
        scenarios = "口腔频道页入口",
        notice = "口腔频道页入口")
@InterfaceDoc(type = "octo.thrift.annotation", displayName = "DentalEntranceService", description = "口腔频道页入口接口", scenarios = "口腔频道页入口")
@ThriftService
public interface DentalEntranceService {

    @MethodDoc(displayName = "获取口腔频道页入口", description = "获取口腔频道页入口", parameters = {
            @ParamDoc(name = "request", description = "请求参数")}, returnValueDescription = "返回口腔频道页入口")
    @ThriftMethod
    RemoteResponse<DentalEntranceResponse> getDentalEntrance(DentalEntranceRequest request);
}
