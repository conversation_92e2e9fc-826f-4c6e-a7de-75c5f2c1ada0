package com.sankuai.dzhealth.ai.service.request;

import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.*;

@ThriftStruct
@TypeDoc(name="决策树查询请求")
@ToString
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class DecisionTreeQueryRequest {
    @FieldDoc(name="业务场景标识")
    @Getter(onMethod_=@ThriftField(1))
    @Setter(onMethod_=@ThriftField)
    private String bizScene;

    @FieldDoc(name="根节点ID")
    @Getter(onMethod_=@ThriftField(2))
    @Setter(onMethod_=@ThriftField)
    private String rootNodeId;

    @FieldDoc(name="最大深度")
    @Getter(onMethod_=@ThriftField(3))
    @Setter(onMethod_=@ThriftField)
    private Integer maxDepth;

    @FieldDoc(name="是否预览灰度版")
    @Getter(onMethod_=@ThriftField(4))
    @Setter(onMethod_=@ThriftField)
    private Boolean previewGray;

    @FieldDoc(name="是否包含资源信息")
    @Getter(onMethod_=@ThriftField(5))
    @Setter(onMethod_=@ThriftField)
    private Boolean includeResources;
}

