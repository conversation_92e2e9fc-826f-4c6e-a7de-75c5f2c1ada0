package com.sankuai.dzhealth.ai.service.agent.request;

import com.sankuai.dzhealth.ai.service.request.common.BasicParam;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @since : 2025/7/15 17:48
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EncyclopediaRequest {
    /**
     * 会话id
     */
    private String sessionId;

    /**
     * 用户输入内容
     */
    private String userQuery;

    /**
     * 平台，参考 com.meituan.beauty.fundamental.light.plat.Platform
     */
    private int platform;

    /**
     * 基本参数
     */
    private BasicParam basicParam;
}
