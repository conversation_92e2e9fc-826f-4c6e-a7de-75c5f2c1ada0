<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.sankuai.dzhealth</groupId>
        <artifactId>ai.service</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>ai.service-api</artifactId>
    <version>${api.version}</version>
    <packaging>jar</packaging>
    <name>ai.service-api</name>

    <properties>
        <java.version>8</java.version>
    </properties>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                </configuration>
            </plugin>
        </plugins>
    </build>

    <dependencies>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        <dependency>
            <groupId>com.facebook.swift</groupId>
            <artifactId>swift-annotations</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.thrift</groupId>
            <artifactId>libthrift</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.servicecatalog</groupId>
            <artifactId>api-annotations</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.mtrace</groupId>
            <artifactId>mtrace</artifactId>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>

</project>
