<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.meituan.mdp</groupId>
        <artifactId>mdp-basic-parent</artifactId>
        <version>2.9.0.15</version>
        <relativePath/>
    </parent>

    <groupId>com.sankuai.dzhealth</groupId>
    <artifactId>ai.service</artifactId>
    <version>${revision}</version>
    <packaging>pom</packaging>
    <name>ai-service</name>

    <modules>
        <module>ai.service-api</module>
        <module>ai.service-starter</module>
        <module>ai.service-application</module>
        <module>ai.service-domain</module>
        <module>ai.service-infrastructure</module>
    </modules>

    <properties>
        <revision>1.0.0-SNAPSHOT</revision>
        <api.version>1.0.5</api.version>
        <java.version>17</java.version>
        <maven.compiler.source>${java.version}</maven.compiler.source>
        <maven.compiler.target>${java.version}</maven.compiler.target>
        <travel-cerberus.version>1.8.0</travel-cerberus.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.sankuai.dzhealth</groupId>
                <artifactId>ai.service-api</artifactId>
                <version>${api.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.dzhealth</groupId>
                <artifactId>ai.service-starter</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.dzhealth</groupId>
                <artifactId>ai.service-domain</artifactId>
                <version>${revision}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>dom4j</artifactId>
                        <groupId>dom4j</groupId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.sankuai.dzhealth</groupId>
                <artifactId>ai.service-application</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.dzhealth</groupId>
                <artifactId>ai.service-infrastructure</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.sankuai.meituan</groupId>
                <artifactId>poros-java-api-client</artifactId>
                <version>1.0.1</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.meituan</groupId>
                <artifactId>poros-client</artifactId>
                <version>0.9.23.1-RC4</version>
            </dependency>
            <dependency>
                <groupId>co.elastic.clients</groupId>
                <artifactId>elasticsearch-java</artifactId>
                <version>8.13.4-mt2</version>
            </dependency>
            <dependency>
                <groupId>jakarta.json</groupId>
                <artifactId>jakarta.json-api</artifactId>
                <version>2.1.3</version>
            </dependency>
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>1.18.36</version>
            </dependency>
            <dependency>
                <groupId>com.meituan.inf</groupId>
                <artifactId>xmd-log4j2</artifactId>
                <version>2.2.0</version>
            </dependency>
            <dependency>
                <groupId>com.meituan.log</groupId>
                <artifactId>scribe-log4j2</artifactId>
                <version>2.1.0</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-log4j12</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.meituan.inf</groupId>
                        <artifactId>xmd-log4j2</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.meituan.inf</groupId>
                        <artifactId>xmd-common-log4j2</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.sankuai.hotel</groupId>
                <artifactId>travel-cerberus</artifactId>
                <version>${travel-cerberus.version}</version>
            </dependency>
            <dependency>
                <groupId>com.dianping.credit</groupId>
                <artifactId>credit-access-api</artifactId>
                <version>1.2.9</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai</groupId>
                <artifactId>beautycontent.store.api</artifactId>
                <version>0.0.2.7</version>
            </dependency>
        </dependencies>

    </dependencyManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>flatten-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <id>flatten</id>
                        <phase>process-resources</phase>
                        <goals>
                            <goal>flatten</goal>
                        </goals>
                        <configuration>
                            <flattenMode>defaults</flattenMode>
                            <pomElements>
                                <parent>expand</parent>
                                <profiles>expand</profiles>
                                <dependencies>keep</dependencies>
                                <build>keep</build>
                            </pomElements>
                            <updatePomFile>true</updatePomFile>
                        </configuration>
                    </execution>
                    <execution>
                        <id>flatten-clean</id>
                        <phase>clean</phase>
                        <goals>
                            <goal>clean</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <encoding>${project.build.sourceEncoding}</encoding>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <configuration>
                    <encoding>${project.build.sourceEncoding}</encoding>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>
