# 项目简介

## 项目概述
- 项目名称：AI Service
- 项目代号：com.sankuai.dzhealth.ai.service
- 开发语言：Java 17
- 构建工具：Maven

## 项目架构
项目采用 DDD (Domain-Driven Design) 分层架构：
- ai.service-api：对外提供的 API 接口
- ai.service-starter：应用启动模块
- ai.service-application：应用层，处理业务流程
- ai.service-domain：领域层，包含核心业务逻辑
- ai.service-infrastructure：基础设施层，提供技术支持

## 项目目标
[待补充具体业务目标]

## 关键依赖
- MDP Basic Parent (2.9.0)
- Poros Java API Client (1.0.1)
- Elasticsearch Java Client (8.13.4-mt2)
- Travel Cerberus (1.8.0) 