# 系统模式

## 架构概览
项目采用 DDD (Domain-Driven Design) 分层架构，各层职责如下：

### API 层 (ai.service-api)
- 定义对外服务接口
- 定义数据传输对象 (DTO)
- 版本控制和兼容性管理

### 应用层 (ai.service-application)
- 编排业务流程
- 处理用户请求
- 调用领域服务
- 事务管理

### 领域层 (ai.service-domain)
- 实现核心业务逻辑
- 定义领域模型
- 定义领域服务
- 定义领域事件

### 基础设施层 (ai.service-infrastructure)
- 提供技术实现
- 数据持久化
- 外部服务集成
- 消息队列等中间件支持

### 启动层 (ai.service-starter)
- 应用程序入口
- 配置加载
- 依赖注入
- 服务启动

## 设计模式
[待补充项目中使用的设计模式]

## 数据流
系统主要包含两个核心服务流：AI 问答流和向量存储流。

### 1. AI 问答流
```mermaid
flowchart TD
    Client[客户端] --> |1. 发送问题| AIS[AiAnswerService]
    AIS --> |2. 处理请求| APP[应用层]
    APP --> |3. 调用领域服务| DOM[领域层]
    DOM --> |4. 查询知识库| VS[向量存储]
    VS --> |5. 返回相关文档| DOM
    DOM --> |6. 调用AI服务| AI[AI服务]
    AI --> |7. 生成回答| DOM
    DOM --> |8. 处理响应| APP
    APP --> |9. 格式化结果| AIS
    AIS --> |10. 返回答案| Client
```

#### 数据流说明
1. 客户端发送问题到 AiAnswerService
2. 应用层接收请求并进行预处理
3. 领域层处理核心业务逻辑
4. 查询向量存储获取相关知识
5. 向量存储返回相关文档
6. 调用 AI 服务生成回答
7. AI 服务返回生成的回答
8. 领域层处理 AI 响应
9. 应用层格式化结果
10. 返回最终答案给客户端

### 2. 向量存储流
```mermaid
flowchart TD
    Admin[管理端] --> |1. 提交文档| ESS[ESVectorStoreService]
    ESS --> |2. 处理文档| APP[应用层]
    APP --> |3. 向量化| DOM[领域层]
    DOM --> |4. 存储文档| ES[Elasticsearch]
    
    Client[客户端] --> |A. 相似度查询| ESS
    ESS --> |B. 处理查询| APP
    APP --> |C. 向量化查询| DOM
    DOM --> |D. 搜索向量| ES
    ES --> |E. 返回结果| DOM
    DOM --> |F. 处理结果| APP
    APP --> |G. 格式化结果| ESS
    ESS --> |H. 返回文档| Client
```

#### 数据流说明
文档索引流程：
1. 管理端提交文档到 ESVectorStoreService
2. 应用层处理文档请求
3. 领域层进行文档向量化
4. 将向量化后的文档存储到 Elasticsearch

相似度搜索流程：
1. 客户端发送相似度查询请求
2. 应用层处理查询请求
3. 领域层向量化查询文本
4. 在 Elasticsearch 中搜索相似向量
5. Elasticsearch 返回搜索结果
6. 领域层处理搜索结果
7. 应用层格式化结果
8. 返回相关文档给客户端

## 组件关系
[待补充组件关系图]

## 关键技术决策
[待补充关键技术决策记录] 