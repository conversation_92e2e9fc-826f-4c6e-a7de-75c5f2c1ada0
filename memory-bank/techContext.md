# 技术上下文

## 开发环境
- JDK 17
- Maven
- IDE 建议：IntelliJ IDEA

## 技术栈
### 核心框架
- Spring Boot (通过 MDP Basic Parent 2.9.0)
- DDD 架构模式

### 存储
- Elasticsearch (8.13.4-mt2)

### 日志系统
- XMD Log4j2
- Scribe Log4j2

### 服务调用
- Poros Java API Client (1.0.1)

### 安全
- Travel Cerberus (1.8.0)

## 项目配置
### Maven 配置
- 使用 flatten-maven-plugin 管理版本
- 当前版本：1.0.0-SNAPSHOT

## 开发规范
[待补充项目具体开发规范]

## 接口规范
### 前端接口
[待补充前端接口规范]

### 后端接口
[待补充后端接口规范] 