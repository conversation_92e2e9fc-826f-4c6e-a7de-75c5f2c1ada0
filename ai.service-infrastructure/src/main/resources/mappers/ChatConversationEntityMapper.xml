<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.dzhealth.ai.service.infrastructure.dal.mapper.ChatConversationEntityMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.dzhealth.ai.service.infrastructure.dal.entity.ChatConversationEntity">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="conversation_id" jdbcType="VARCHAR" property="conversationId" />
    <result column="conversation_type" jdbcType="TINYINT" property="conversationType" />
    <result column="biz_scene" jdbcType="VARCHAR" property="bizScene" />
    <result column="title" jdbcType="VARCHAR" property="title" />
    <result column="group_id" jdbcType="VARCHAR" property="groupId" />
    <result column="creator_id" jdbcType="VARCHAR" property="creatorId" />
    <result column="first_message_id" jdbcType="VARCHAR" property="firstMessageId" />
    <result column="last_message_id" jdbcType="VARCHAR" property="lastMessageId" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
    <result column="business_type" jdbcType="VARCHAR" property="businessType" />
    <result column="business_id" jdbcType="VARCHAR" property="businessId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, conversation_id, conversation_type, biz_scene, title, group_id, creator_id, first_message_id, 
    last_message_id, status, created_at, updated_at, business_type, business_id
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.dzhealth.ai.service.infrastructure.dal.example.ChatConversationEntityExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from chat_conversation
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from chat_conversation
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from chat_conversation
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.dzhealth.ai.service.infrastructure.dal.example.ChatConversationEntityExample">
    delete from chat_conversation
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sankuai.dzhealth.ai.service.infrastructure.dal.entity.ChatConversationEntity">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into chat_conversation (conversation_id, conversation_type, 
      biz_scene, title, group_id, 
      creator_id, first_message_id, last_message_id, 
      status, created_at, updated_at, 
      business_type, business_id)
    values (#{conversationId,jdbcType=VARCHAR}, #{conversationType,jdbcType=TINYINT}, 
      #{bizScene,jdbcType=VARCHAR}, #{title,jdbcType=VARCHAR}, #{groupId,jdbcType=VARCHAR}, 
      #{creatorId,jdbcType=VARCHAR}, #{firstMessageId,jdbcType=VARCHAR}, #{lastMessageId,jdbcType=VARCHAR}, 
      #{status,jdbcType=TINYINT}, #{createdAt,jdbcType=TIMESTAMP}, #{updatedAt,jdbcType=TIMESTAMP}, 
      #{businessType,jdbcType=VARCHAR}, #{businessId,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.sankuai.dzhealth.ai.service.infrastructure.dal.entity.ChatConversationEntity">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into chat_conversation
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="conversationId != null">
        conversation_id,
      </if>
      <if test="conversationType != null">
        conversation_type,
      </if>
      <if test="bizScene != null">
        biz_scene,
      </if>
      <if test="title != null">
        title,
      </if>
      <if test="groupId != null">
        group_id,
      </if>
      <if test="creatorId != null">
        creator_id,
      </if>
      <if test="firstMessageId != null">
        first_message_id,
      </if>
      <if test="lastMessageId != null">
        last_message_id,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="createdAt != null">
        created_at,
      </if>
      <if test="updatedAt != null">
        updated_at,
      </if>
      <if test="businessType != null">
        business_type,
      </if>
      <if test="businessId != null">
        business_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="conversationId != null">
        #{conversationId,jdbcType=VARCHAR},
      </if>
      <if test="conversationType != null">
        #{conversationType,jdbcType=TINYINT},
      </if>
      <if test="bizScene != null">
        #{bizScene,jdbcType=VARCHAR},
      </if>
      <if test="title != null">
        #{title,jdbcType=VARCHAR},
      </if>
      <if test="groupId != null">
        #{groupId,jdbcType=VARCHAR},
      </if>
      <if test="creatorId != null">
        #{creatorId,jdbcType=VARCHAR},
      </if>
      <if test="firstMessageId != null">
        #{firstMessageId,jdbcType=VARCHAR},
      </if>
      <if test="lastMessageId != null">
        #{lastMessageId,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="createdAt != null">
        #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedAt != null">
        #{updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="businessType != null">
        #{businessType,jdbcType=VARCHAR},
      </if>
      <if test="businessId != null">
        #{businessId,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.dzhealth.ai.service.infrastructure.dal.example.ChatConversationEntityExample" resultType="java.lang.Long">
    select count(*) from chat_conversation
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update chat_conversation
    <set>
      <if test="row.id != null">
        id = #{row.id,jdbcType=BIGINT},
      </if>
      <if test="row.conversationId != null">
        conversation_id = #{row.conversationId,jdbcType=VARCHAR},
      </if>
      <if test="row.conversationType != null">
        conversation_type = #{row.conversationType,jdbcType=TINYINT},
      </if>
      <if test="row.bizScene != null">
        biz_scene = #{row.bizScene,jdbcType=VARCHAR},
      </if>
      <if test="row.title != null">
        title = #{row.title,jdbcType=VARCHAR},
      </if>
      <if test="row.groupId != null">
        group_id = #{row.groupId,jdbcType=VARCHAR},
      </if>
      <if test="row.creatorId != null">
        creator_id = #{row.creatorId,jdbcType=VARCHAR},
      </if>
      <if test="row.firstMessageId != null">
        first_message_id = #{row.firstMessageId,jdbcType=VARCHAR},
      </if>
      <if test="row.lastMessageId != null">
        last_message_id = #{row.lastMessageId,jdbcType=VARCHAR},
      </if>
      <if test="row.status != null">
        status = #{row.status,jdbcType=TINYINT},
      </if>
      <if test="row.createdAt != null">
        created_at = #{row.createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="row.updatedAt != null">
        updated_at = #{row.updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="row.businessType != null">
        business_type = #{row.businessType,jdbcType=VARCHAR},
      </if>
      <if test="row.businessId != null">
        business_id = #{row.businessId,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update chat_conversation
    set id = #{row.id,jdbcType=BIGINT},
      conversation_id = #{row.conversationId,jdbcType=VARCHAR},
      conversation_type = #{row.conversationType,jdbcType=TINYINT},
      biz_scene = #{row.bizScene,jdbcType=VARCHAR},
      title = #{row.title,jdbcType=VARCHAR},
      group_id = #{row.groupId,jdbcType=VARCHAR},
      creator_id = #{row.creatorId,jdbcType=VARCHAR},
      first_message_id = #{row.firstMessageId,jdbcType=VARCHAR},
      last_message_id = #{row.lastMessageId,jdbcType=VARCHAR},
      status = #{row.status,jdbcType=TINYINT},
      created_at = #{row.createdAt,jdbcType=TIMESTAMP},
      updated_at = #{row.updatedAt,jdbcType=TIMESTAMP},
      business_type = #{row.businessType,jdbcType=VARCHAR},
      business_id = #{row.businessId,jdbcType=VARCHAR}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.dzhealth.ai.service.infrastructure.dal.entity.ChatConversationEntity">
    update chat_conversation
    <set>
      <if test="conversationId != null">
        conversation_id = #{conversationId,jdbcType=VARCHAR},
      </if>
      <if test="conversationType != null">
        conversation_type = #{conversationType,jdbcType=TINYINT},
      </if>
      <if test="bizScene != null">
        biz_scene = #{bizScene,jdbcType=VARCHAR},
      </if>
      <if test="title != null">
        title = #{title,jdbcType=VARCHAR},
      </if>
      <if test="groupId != null">
        group_id = #{groupId,jdbcType=VARCHAR},
      </if>
      <if test="creatorId != null">
        creator_id = #{creatorId,jdbcType=VARCHAR},
      </if>
      <if test="firstMessageId != null">
        first_message_id = #{firstMessageId,jdbcType=VARCHAR},
      </if>
      <if test="lastMessageId != null">
        last_message_id = #{lastMessageId,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="createdAt != null">
        created_at = #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedAt != null">
        updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="businessType != null">
        business_type = #{businessType,jdbcType=VARCHAR},
      </if>
      <if test="businessId != null">
        business_id = #{businessId,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.dzhealth.ai.service.infrastructure.dal.entity.ChatConversationEntity">
    update chat_conversation
    set conversation_id = #{conversationId,jdbcType=VARCHAR},
      conversation_type = #{conversationType,jdbcType=TINYINT},
      biz_scene = #{bizScene,jdbcType=VARCHAR},
      title = #{title,jdbcType=VARCHAR},
      group_id = #{groupId,jdbcType=VARCHAR},
      creator_id = #{creatorId,jdbcType=VARCHAR},
      first_message_id = #{firstMessageId,jdbcType=VARCHAR},
      last_message_id = #{lastMessageId,jdbcType=VARCHAR},
      status = #{status,jdbcType=TINYINT},
      created_at = #{createdAt,jdbcType=TIMESTAMP},
      updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      business_type = #{businessType,jdbcType=VARCHAR},
      business_id = #{businessId,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <sql id="Select_By_Example_With_Page_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <select id="selectByExampleWithPage" resultMap="BaseResultMap">
    select
    <if test="example.distinct">
        distinct
    </if>
    <include refid="Base_Column_List" />
    from chat_conversation
    <if test="example != null">
      <include refid="Select_By_Example_With_Page_Where_Clause" />
    </if>
    <if test="example.orderByClause != null">
        order by ${example.orderByClause}
    </if>
    limit #{offset}, #{limit}
  </select>
</mapper>