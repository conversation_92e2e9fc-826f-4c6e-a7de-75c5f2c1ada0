<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.dzhealth.ai.service.infrastructure.dal.mapper.ChatPhoneCallTaskEntityMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.dzhealth.ai.service.infrastructure.dal.entity.ChatPhoneCallTaskEntity">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="contact_id" jdbcType="VARCHAR" property="contactId" />
    <result column="message_id" jdbcType="BIGINT" property="messageId" />
    <result column="session_id" jdbcType="BIGINT" property="sessionId" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="call_count" jdbcType="INTEGER" property="callCount" />
    <result column="mt_shop_id" jdbcType="BIGINT" property="mtShopId" />
    <result column="start_time" jdbcType="TIMESTAMP" property="startTime" />
    <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
    <result column="talking_time_len" jdbcType="INTEGER" property="talkingTimeLen" />
    <result column="release_reason" jdbcType="INTEGER" property="releaseReason" />
    <result column="question" jdbcType="VARCHAR" property="question" />
    <result column="mediatxt" jdbcType="VARCHAR" property="mediatxt" />
    <result column="dialog_raw_data" jdbcType="CHAR" property="dialogRawData" />
    <result column="dialog_refined_data" jdbcType="CHAR" property="dialogRefinedData" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, contact_id, message_id, session_id, status, call_count, mt_shop_id, start_time, 
    end_time, talking_time_len, release_reason, question, mediatxt, dialog_raw_data, 
    dialog_refined_data
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.dzhealth.ai.service.infrastructure.dal.example.ChatPhoneCallTaskEntityExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from chat_phonecall_task
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from chat_phonecall_task
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from chat_phonecall_task
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.dzhealth.ai.service.infrastructure.dal.example.ChatPhoneCallTaskEntityExample">
    delete from chat_phonecall_task
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sankuai.dzhealth.ai.service.infrastructure.dal.entity.ChatPhoneCallTaskEntity">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into chat_phonecall_task (contact_id, message_id, session_id, 
      status, call_count, mt_shop_id, 
      start_time, end_time, talking_time_len, 
      release_reason, question, mediatxt, 
      dialog_raw_data, dialog_refined_data)
    values (#{contactId,jdbcType=VARCHAR}, #{messageId,jdbcType=BIGINT}, #{sessionId,jdbcType=BIGINT},
      #{status,jdbcType=INTEGER}, #{callCount,jdbcType=INTEGER}, #{mtShopId,jdbcType=BIGINT}, 
      #{startTime,jdbcType=TIMESTAMP}, #{endTime,jdbcType=TIMESTAMP}, #{talkingTimeLen,jdbcType=INTEGER}, 
      #{releaseReason,jdbcType=INTEGER}, #{question,jdbcType=VARCHAR}, #{mediatxt,jdbcType=VARCHAR},
      #{dialogRawData,jdbcType=CHAR}, #{dialogRefinedData,jdbcType=CHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.sankuai.dzhealth.ai.service.infrastructure.dal.entity.ChatPhoneCallTaskEntity">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into chat_phonecall_task
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="contactId != null">
        contact_id,
      </if>
      <if test="messageId != null">
        message_id,
      </if>
      <if test="sessionId != null">
        session_id,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="callCount != null">
        call_count,
      </if>
      <if test="mtShopId != null">
        mt_shop_id,
      </if>
      <if test="startTime != null">
        start_time,
      </if>
      <if test="endTime != null">
        end_time,
      </if>
      <if test="talkingTimeLen != null">
        talking_time_len,
      </if>
      <if test="releaseReason != null">
        release_reason,
      </if>
      <if test="question != null">
        question,
      </if>
      <if test="mediatxt != null">
        mediatxt,
      </if>
      <if test="dialogRawData != null">
        dialog_raw_data,
      </if>
      <if test="dialogRefinedData != null">
        dialog_refined_data,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="contactId != null">
        #{contactId,jdbcType=VARCHAR},
      </if>
      <if test="messageId != null">
        #{messageId,jdbcType=BIGINT},
      </if>
      <if test="sessionId != null">
        #{sessionId,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="callCount != null">
        #{callCount,jdbcType=INTEGER},
      </if>
      <if test="mtShopId != null">
        #{mtShopId,jdbcType=BIGINT},
      </if>
      <if test="startTime != null">
        #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="talkingTimeLen != null">
        #{talkingTimeLen,jdbcType=INTEGER},
      </if>
      <if test="releaseReason != null">
        #{releaseReason,jdbcType=INTEGER},
      </if>
      <if test="question != null">
        #{question,jdbcType=VARCHAR},
      </if>
      <if test="mediatxt != null">
        #{mediatxt,jdbcType=VARCHAR},
      </if>
      <if test="dialogRawData != null">
        #{dialogRawData,jdbcType=CHAR},
      </if>
      <if test="dialogRefinedData != null">
        #{dialogRefinedData,jdbcType=CHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.dzhealth.ai.service.infrastructure.dal.example.ChatPhoneCallTaskEntityExample" resultType="java.lang.Long">
    select count(*) from chat_phonecall_task
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update chat_phonecall_task
    <set>
      <if test="row.id != null">
        id = #{row.id,jdbcType=BIGINT},
      </if>
      <if test="row.contactId != null">
        contact_id = #{row.contactId,jdbcType=VARCHAR},
      </if>
      <if test="row.messageId != null">
        message_id = #{row.messageId,jdbcType=BIGINT},
      </if>
      <if test="row.sessionId != null">
        session_id = #{row.sessionId,jdbcType=BIGINT},
      </if>
      <if test="row.status != null">
        status = #{row.status,jdbcType=INTEGER},
      </if>
      <if test="row.callCount != null">
        call_count = #{row.callCount,jdbcType=INTEGER},
      </if>
      <if test="row.mtShopId != null">
        mt_shop_id = #{row.mtShopId,jdbcType=BIGINT},
      </if>
      <if test="row.startTime != null">
        start_time = #{row.startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.endTime != null">
        end_time = #{row.endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.talkingTimeLen != null">
        talking_time_len = #{row.talkingTimeLen,jdbcType=INTEGER},
      </if>
      <if test="row.releaseReason != null">
        release_reason = #{row.releaseReason,jdbcType=INTEGER},
      </if>
      <if test="row.question != null">
        question = #{row.question,jdbcType=VARCHAR},
      </if>
      <if test="row.mediatxt != null">
        mediatxt = #{row.mediatxt,jdbcType=VARCHAR},
      </if>
      <if test="row.dialogRawData != null">
        dialog_raw_data = #{row.dialogRawData,jdbcType=CHAR},
      </if>
      <if test="row.dialogRefinedData != null">
        dialog_refined_data = #{row.dialogRefinedData,jdbcType=CHAR},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update chat_phonecall_task
    set id = #{row.id,jdbcType=BIGINT},
      contact_id = #{row.contactId,jdbcType=VARCHAR},
      message_id = #{row.messageId,jdbcType=BIGINT},
      session_id = #{row.sessionId,jdbcType=BIGINT},
      status = #{row.status,jdbcType=INTEGER},
      call_count = #{row.callCount,jdbcType=INTEGER},
      mt_shop_id = #{row.mtShopId,jdbcType=BIGINT},
      start_time = #{row.startTime,jdbcType=TIMESTAMP},
      end_time = #{row.endTime,jdbcType=TIMESTAMP},
      talking_time_len = #{row.talkingTimeLen,jdbcType=INTEGER},
      release_reason = #{row.releaseReason,jdbcType=INTEGER},
      question = #{row.question,jdbcType=VARCHAR},
      mediatxt = #{row.mediatxt,jdbcType=VARCHAR},
      dialog_raw_data = #{row.dialogRawData,jdbcType=CHAR},
      dialog_refined_data = #{row.dialogRefinedData,jdbcType=CHAR}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.dzhealth.ai.service.infrastructure.dal.entity.ChatPhoneCallTaskEntity">
    update chat_phonecall_task
    <set>
      <if test="contactId != null">
        contact_id = #{contactId,jdbcType=VARCHAR},
      </if>
      <if test="messageId != null">
        message_id = #{messageId,jdbcType=BIGINT},
      </if>
      <if test="sessionId != null">
        session_id = #{sessionId,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="callCount != null">
        call_count = #{callCount,jdbcType=INTEGER},
      </if>
      <if test="mtShopId != null">
        mt_shop_id = #{mtShopId,jdbcType=BIGINT},
      </if>
      <if test="startTime != null">
        start_time = #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        end_time = #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="talkingTimeLen != null">
        talking_time_len = #{talkingTimeLen,jdbcType=INTEGER},
      </if>
      <if test="releaseReason != null">
        release_reason = #{releaseReason,jdbcType=INTEGER},
      </if>
      <if test="question != null">
        question = #{question,jdbcType=VARCHAR},
      </if>
      <if test="mediatxt != null">
        mediatxt = #{mediatxt,jdbcType=VARCHAR},
      </if>
      <if test="dialogRawData != null">
        dialog_raw_data = #{dialogRawData,jdbcType=CHAR},
      </if>
      <if test="dialogRefinedData != null">
        dialog_refined_data = #{dialogRefinedData,jdbcType=CHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.dzhealth.ai.service.infrastructure.dal.entity.ChatPhoneCallTaskEntity">
    update chat_phonecall_task
    set contact_id = #{contactId,jdbcType=VARCHAR},
      message_id = #{messageId,jdbcType=BIGINT},
      session_id = #{sessionId,jdbcType=BIGINT},
      status = #{status,jdbcType=INTEGER},
      call_count = #{callCount,jdbcType=INTEGER},
      mt_shop_id = #{mtShopId,jdbcType=BIGINT},
      start_time = #{startTime,jdbcType=TIMESTAMP},
      end_time = #{endTime,jdbcType=TIMESTAMP},
      talking_time_len = #{talkingTimeLen,jdbcType=INTEGER},
      release_reason = #{releaseReason,jdbcType=INTEGER},
      question = #{question,jdbcType=VARCHAR},
      mediatxt = #{mediatxt,jdbcType=VARCHAR},
      dialog_raw_data = #{dialogRawData,jdbcType=CHAR},
      dialog_refined_data = #{dialogRefinedData,jdbcType=CHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>