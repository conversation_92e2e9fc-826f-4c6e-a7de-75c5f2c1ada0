<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.dzhealth.ai.service.agent.infrastructure.dal.mapper.ChatSessionDOMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.dzhealth.ai.service.agent.infrastructure.dal.entity.ChatSessionDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="session_id" jdbcType="VARCHAR" property="sessionId" />
    <result column="biz_type" jdbcType="VARCHAR" property="bizType" />
    <result column="biz_id" jdbcType="VARCHAR" property="bizId" />
    <result column="title" jdbcType="VARCHAR" property="title" />
    <result column="digest" jdbcType="VARCHAR" property="digest" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="platform" jdbcType="INTEGER" property="platform" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.sankuai.dzhealth.ai.service.agent.infrastructure.dal.entity.ChatSessionDOWithBLOBs">
    <result column="memory" jdbcType="LONGVARCHAR" property="memory" />
    <result column="extra" jdbcType="LONGVARCHAR" property="extra" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, session_id, biz_type, biz_id, title, digest, user_id, platform, status, create_time, 
    update_time
  </sql>
  <sql id="Blob_Column_List">
    memory, extra
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.sankuai.dzhealth.ai.service.agent.infrastructure.dal.example.ChatSessionDOExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from chat_session
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="com.sankuai.dzhealth.ai.service.agent.infrastructure.dal.example.ChatSessionDOExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from chat_session
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from chat_session
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from chat_session
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.dzhealth.ai.service.agent.infrastructure.dal.example.ChatSessionDOExample">
    delete from chat_session
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sankuai.dzhealth.ai.service.agent.infrastructure.dal.entity.ChatSessionDOWithBLOBs">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into chat_session (session_id, biz_type, biz_id, 
      title, digest, user_id, 
      platform, status, create_time, 
      update_time, memory, extra
      )
    values (#{sessionId,jdbcType=VARCHAR}, #{bizType,jdbcType=VARCHAR}, #{bizId,jdbcType=VARCHAR}, 
      #{title,jdbcType=VARCHAR}, #{digest,jdbcType=VARCHAR}, #{userId,jdbcType=BIGINT}, 
      #{platform,jdbcType=INTEGER}, #{status,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{memory,jdbcType=LONGVARCHAR}, #{extra,jdbcType=LONGVARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.sankuai.dzhealth.ai.service.agent.infrastructure.dal.entity.ChatSessionDOWithBLOBs">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into chat_session
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="sessionId != null">
        session_id,
      </if>
      <if test="bizType != null">
        biz_type,
      </if>
      <if test="bizId != null">
        biz_id,
      </if>
      <if test="title != null">
        title,
      </if>
      <if test="digest != null">
        digest,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="platform != null">
        platform,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="memory != null">
        memory,
      </if>
      <if test="extra != null">
        extra,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="sessionId != null">
        #{sessionId,jdbcType=VARCHAR},
      </if>
      <if test="bizType != null">
        #{bizType,jdbcType=VARCHAR},
      </if>
      <if test="bizId != null">
        #{bizId,jdbcType=VARCHAR},
      </if>
      <if test="title != null">
        #{title,jdbcType=VARCHAR},
      </if>
      <if test="digest != null">
        #{digest,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=BIGINT},
      </if>
      <if test="platform != null">
        #{platform,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="memory != null">
        #{memory,jdbcType=LONGVARCHAR},
      </if>
      <if test="extra != null">
        #{extra,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.dzhealth.ai.service.agent.infrastructure.dal.example.ChatSessionDOExample" resultType="java.lang.Long">
    select count(*) from chat_session
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update chat_session
    <set>
      <if test="row.id != null">
        id = #{row.id,jdbcType=BIGINT},
      </if>
      <if test="row.sessionId != null">
        session_id = #{row.sessionId,jdbcType=VARCHAR},
      </if>
      <if test="row.bizType != null">
        biz_type = #{row.bizType,jdbcType=VARCHAR},
      </if>
      <if test="row.bizId != null">
        biz_id = #{row.bizId,jdbcType=VARCHAR},
      </if>
      <if test="row.title != null">
        title = #{row.title,jdbcType=VARCHAR},
      </if>
      <if test="row.digest != null">
        digest = #{row.digest,jdbcType=VARCHAR},
      </if>
      <if test="row.userId != null">
        user_id = #{row.userId,jdbcType=BIGINT},
      </if>
      <if test="row.platform != null">
        platform = #{row.platform,jdbcType=INTEGER},
      </if>
      <if test="row.status != null">
        status = #{row.status,jdbcType=INTEGER},
      </if>
      <if test="row.createTime != null">
        create_time = #{row.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.updateTime != null">
        update_time = #{row.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.memory != null">
        memory = #{row.memory,jdbcType=LONGVARCHAR},
      </if>
      <if test="row.extra != null">
        extra = #{row.extra,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    update chat_session
    set id = #{row.id,jdbcType=BIGINT},
      session_id = #{row.sessionId,jdbcType=VARCHAR},
      biz_type = #{row.bizType,jdbcType=VARCHAR},
      biz_id = #{row.bizId,jdbcType=VARCHAR},
      title = #{row.title,jdbcType=VARCHAR},
      digest = #{row.digest,jdbcType=VARCHAR},
      user_id = #{row.userId,jdbcType=BIGINT},
      platform = #{row.platform,jdbcType=INTEGER},
      status = #{row.status,jdbcType=INTEGER},
      create_time = #{row.createTime,jdbcType=TIMESTAMP},
      update_time = #{row.updateTime,jdbcType=TIMESTAMP},
      memory = #{row.memory,jdbcType=LONGVARCHAR},
      extra = #{row.extra,jdbcType=LONGVARCHAR}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update chat_session
    set id = #{row.id,jdbcType=BIGINT},
      session_id = #{row.sessionId,jdbcType=VARCHAR},
      biz_type = #{row.bizType,jdbcType=VARCHAR},
      biz_id = #{row.bizId,jdbcType=VARCHAR},
      title = #{row.title,jdbcType=VARCHAR},
      digest = #{row.digest,jdbcType=VARCHAR},
      user_id = #{row.userId,jdbcType=BIGINT},
      platform = #{row.platform,jdbcType=INTEGER},
      status = #{row.status,jdbcType=INTEGER},
      create_time = #{row.createTime,jdbcType=TIMESTAMP},
      update_time = #{row.updateTime,jdbcType=TIMESTAMP}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.dzhealth.ai.service.agent.infrastructure.dal.entity.ChatSessionDOWithBLOBs">
    update chat_session
    <set>
      <if test="sessionId != null">
        session_id = #{sessionId,jdbcType=VARCHAR},
      </if>
      <if test="bizType != null">
        biz_type = #{bizType,jdbcType=VARCHAR},
      </if>
      <if test="bizId != null">
        biz_id = #{bizId,jdbcType=VARCHAR},
      </if>
      <if test="title != null">
        title = #{title,jdbcType=VARCHAR},
      </if>
      <if test="digest != null">
        digest = #{digest,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        user_id = #{userId,jdbcType=BIGINT},
      </if>
      <if test="platform != null">
        platform = #{platform,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="memory != null">
        memory = #{memory,jdbcType=LONGVARCHAR},
      </if>
      <if test="extra != null">
        extra = #{extra,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.sankuai.dzhealth.ai.service.agent.infrastructure.dal.entity.ChatSessionDOWithBLOBs">
    update chat_session
    set session_id = #{sessionId,jdbcType=VARCHAR},
      biz_type = #{bizType,jdbcType=VARCHAR},
      biz_id = #{bizId,jdbcType=VARCHAR},
      title = #{title,jdbcType=VARCHAR},
      digest = #{digest,jdbcType=VARCHAR},
      user_id = #{userId,jdbcType=BIGINT},
      platform = #{platform,jdbcType=INTEGER},
      status = #{status,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      memory = #{memory,jdbcType=LONGVARCHAR},
      extra = #{extra,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.dzhealth.ai.service.agent.infrastructure.dal.entity.ChatSessionDO">
    update chat_session
    set session_id = #{sessionId,jdbcType=VARCHAR},
      biz_type = #{bizType,jdbcType=VARCHAR},
      biz_id = #{bizId,jdbcType=VARCHAR},
      title = #{title,jdbcType=VARCHAR},
      digest = #{digest,jdbcType=VARCHAR},
      user_id = #{userId,jdbcType=BIGINT},
      platform = #{platform,jdbcType=INTEGER},
      status = #{status,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>


  <insert id="batchInsert">
    INSERT INTO chat_session (session_id, biz_type, biz_id, title, digest, user_id, platform, status, create_time,update_time, memory, extra)
    VALUES
    <foreach collection="list" item="item" separator=",">
        (
        #{item.sessionId,jdbcType=VARCHAR}, #{item.bizType,jdbcType=VARCHAR}, #{item.bizId,jdbcType=VARCHAR},
        #{item.title,jdbcType=VARCHAR}, #{item.digest,jdbcType=VARCHAR}, #{item.userId,jdbcType=BIGINT},
        #{item.platform,jdbcType=INTEGER}, #{item.status,jdbcType=INTEGER}, #{item.createTime,jdbcType=TIMESTAMP},
        #{item.updateTime,jdbcType=TIMESTAMP}, #{item.memory,jdbcType=LONGVARCHAR}, #{item.extra,jdbcType=LONGVARCHAR}
        )
    </foreach>
  </insert>
</mapper>