<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.dzhealth.ai.service.infrastructure.dal.mapper.RecommendResourceEntityMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.dzhealth.ai.service.infrastructure.dal.entity.RecommendResourceDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="biz_scene" jdbcType="VARCHAR" property="bizScene" />
    <result column="resource_id" jdbcType="VARCHAR" property="resourceId" />
    <result column="resource_type" jdbcType="VARCHAR" property="resourceType" />
    <result column="resource_name" jdbcType="VARCHAR" property="resourceName" />
    <result column="short_desc" jdbcType="VARCHAR" property="shortDesc" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="attributes" jdbcType="CHAR" property="attributes" />
    <result column="tags" jdbcType="CHAR" property="tags" />
    <result column="ext" jdbcType="CHAR" property="ext" />
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, biz_scene, resource_id, resource_type, resource_name, short_desc, status, attributes, tags, 
    ext, add_time, update_time
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.dzhealth.ai.service.infrastructure.dal.example.RecommendResourceEntityExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from recommend_resource
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from recommend_resource
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from recommend_resource
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.dzhealth.ai.service.infrastructure.dal.example.RecommendResourceEntityExample">
    delete from recommend_resource
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sankuai.dzhealth.ai.service.infrastructure.dal.entity.RecommendResourceDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into recommend_resource (biz_scene, resource_id, resource_type, resource_name, 
      short_desc, status, attributes, 
      tags, ext, add_time, update_time
      )
    values (#{bizScene,jdbcType=VARCHAR}, #{resourceId,jdbcType=VARCHAR}, #{resourceType,jdbcType=VARCHAR}, #{resourceName,jdbcType=VARCHAR}, 
      #{shortDesc,jdbcType=VARCHAR}, #{status,jdbcType=VARCHAR}, #{attributes,jdbcType=CHAR}, 
      #{tags,jdbcType=CHAR}, #{ext,jdbcType=CHAR}, #{addTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.sankuai.dzhealth.ai.service.infrastructure.dal.entity.RecommendResourceDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into recommend_resource
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="bizScene != null">
        biz_scene,
      </if>
      <if test="resourceId != null">
        resource_id,
      </if>
      <if test="resourceType != null">
        resource_type,
      </if>
      <if test="resourceName != null">
        resource_name,
      </if>
      <if test="shortDesc != null">
        short_desc,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="attributes != null">
        attributes,
      </if>
      <if test="tags != null">
        tags,
      </if>
      <if test="ext != null">
        ext,
      </if>
      <if test="addTime != null">
        add_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="bizScene != null">
        #{bizScene,jdbcType=VARCHAR},
      </if>
      <if test="resourceId != null">
        #{resourceId,jdbcType=VARCHAR},
      </if>
      <if test="resourceType != null">
        #{resourceType,jdbcType=VARCHAR},
      </if>
      <if test="resourceName != null">
        #{resourceName,jdbcType=VARCHAR},
      </if>
      <if test="shortDesc != null">
        #{shortDesc,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="attributes != null">
        #{attributes,jdbcType=CHAR},
      </if>
      <if test="tags != null">
        #{tags,jdbcType=CHAR},
      </if>
      <if test="ext != null">
        #{ext,jdbcType=CHAR},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <!-- batch insert -->
  <insert id="insertList" parameterType="java.util.List">
    insert into recommend_resource (biz_scene, resource_id, resource_type, resource_name, short_desc, status, attributes, tags, ext, add_time, update_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.bizScene}, #{item.resourceId}, #{item.resourceType}, #{item.resourceName}, #{item.shortDesc}, #{item.status}, #{item.attributes}, #{item.tags}, #{item.ext}, #{item.addTime}, #{item.updateTime})
    </foreach>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.dzhealth.ai.service.infrastructure.dal.example.RecommendResourceEntityExample" resultType="java.lang.Long">
    select count(*) from recommend_resource
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update recommend_resource
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.bizScene != null">
        biz_scene = #{record.bizScene,jdbcType=VARCHAR},
      </if>
      <if test="record.resourceId != null">
        resource_id = #{record.resourceId,jdbcType=VARCHAR},
      </if>
      <if test="record.resourceType != null">
        resource_type = #{record.resourceType,jdbcType=VARCHAR},
      </if>
      <if test="record.resourceName != null">
        resource_name = #{record.resourceName,jdbcType=VARCHAR},
      </if>
      <if test="record.shortDesc != null">
        short_desc = #{record.shortDesc,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=VARCHAR},
      </if>
      <if test="record.attributes != null">
        attributes = #{record.attributes,jdbcType=CHAR},
      </if>
      <if test="record.tags != null">
        tags = #{record.tags,jdbcType=CHAR},
      </if>
      <if test="record.ext != null">
        ext = #{record.ext,jdbcType=CHAR},
      </if>
      <if test="record.addTime != null">
        add_time = #{record.addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update recommend_resource
    set id = #{record.id,jdbcType=BIGINT},
      biz_scene = #{record.bizScene,jdbcType=VARCHAR},
      resource_id = #{record.resourceId,jdbcType=VARCHAR},
      resource_type = #{record.resourceType,jdbcType=VARCHAR},
      resource_name = #{record.resourceName,jdbcType=VARCHAR},
      short_desc = #{record.shortDesc,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=VARCHAR},
      attributes = #{record.attributes,jdbcType=CHAR},
      tags = #{record.tags,jdbcType=CHAR},
      ext = #{record.ext,jdbcType=CHAR},
      add_time = #{record.addTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.dzhealth.ai.service.infrastructure.dal.entity.RecommendResourceDO">
    update recommend_resource
    <set>
      <if test="bizScene != null">
        biz_scene = #{bizScene,jdbcType=VARCHAR},
      </if>
      <if test="resourceId != null">
        resource_id = #{resourceId,jdbcType=VARCHAR},
      </if>
      <if test="resourceType != null">
        resource_type = #{resourceType,jdbcType=VARCHAR},
      </if>
      <if test="resourceName != null">
        resource_name = #{resourceName,jdbcType=VARCHAR},
      </if>
      <if test="shortDesc != null">
        short_desc = #{shortDesc,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=VARCHAR},
      </if>
      <if test="attributes != null">
        attributes = #{attributes,jdbcType=CHAR},
      </if>
      <if test="tags != null">
        tags = #{tags,jdbcType=CHAR},
      </if>
      <if test="ext != null">
        ext = #{ext,jdbcType=CHAR},
      </if>
      <if test="addTime != null">
        add_time = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.dzhealth.ai.service.infrastructure.dal.entity.RecommendResourceDO">
    update recommend_resource
    set biz_scene = #{bizScene,jdbcType=VARCHAR},
      resource_id = #{resourceId,jdbcType=VARCHAR},
      resource_type = #{resourceType,jdbcType=VARCHAR},
      resource_name = #{resourceName,jdbcType=VARCHAR},
      short_desc = #{shortDesc,jdbcType=VARCHAR},
      status = #{status,jdbcType=VARCHAR},
      attributes = #{attributes,jdbcType=CHAR},
      tags = #{tags,jdbcType=CHAR},
      ext = #{ext,jdbcType=CHAR},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>