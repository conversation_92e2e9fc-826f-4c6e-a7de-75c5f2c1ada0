<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.dzhealth.ai.service.infrastructure.dal.mapper.DecisionNodeEntityMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.dzhealth.ai.service.infrastructure.dal.entity.DecisionNodeDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="biz_scene" jdbcType="VARCHAR" property="bizScene" />
    <result column="node_id" jdbcType="VARCHAR" property="nodeId" />
    <result column="node_name" jdbcType="VARCHAR" property="nodeName" />
    <result column="assessment_text" jdbcType="VARCHAR" property="assessmentText" />
    <result column="assessment_img" jdbcType="VARCHAR" property="assessmentImg" />
    <result column="guidance_text" jdbcType="VARCHAR" property="guidanceText" />
    <result column="need_supply" jdbcType="TINYINT" property="needSupply" />
    <result column="need_doctor" jdbcType="TINYINT" property="needDoctor" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="ext" jdbcType="CHAR" property="ext" />
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, biz_scene, node_id, node_name, assessment_text, assessment_img, guidance_text, 
    need_supply, 
    need_doctor, 
    status, ext, add_time, update_time
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.dzhealth.ai.service.infrastructure.dal.example.DecisionNodeEntityExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from decision_node
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from decision_node
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from decision_node
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.dzhealth.ai.service.infrastructure.dal.example.DecisionNodeEntityExample">
    delete from decision_node
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sankuai.dzhealth.ai.service.infrastructure.dal.entity.DecisionNodeDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into decision_node (biz_scene, node_id, node_name, 
      assessment_text, assessment_img, guidance_text, 
      need_supply, 
      need_doctor, 
      status, ext, add_time, 
      update_time)
    values (#{bizScene,jdbcType=VARCHAR}, #{nodeId,jdbcType=VARCHAR}, #{nodeName,jdbcType=VARCHAR}, 
      #{assessmentText,jdbcType=VARCHAR}, #{assessmentImg,jdbcType=VARCHAR}, #{guidanceText,jdbcType=VARCHAR}, 
      #{needSupply,jdbcType=TINYINT}, 
      #{needDoctor,jdbcType=TINYINT}, 
      #{status,jdbcType=VARCHAR}, #{ext,jdbcType=CHAR}, #{addTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.sankuai.dzhealth.ai.service.infrastructure.dal.entity.DecisionNodeDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into decision_node
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="bizScene != null">
        biz_scene,
      </if>
      <if test="nodeId != null">
        node_id,
      </if>
      <if test="nodeName != null">
        node_name,
      </if>
      <if test="assessmentText != null">
        assessment_text,
      </if>
      <if test="assessmentImg != null">
        assessment_img,
      </if>
      <if test="guidanceText != null">
        guidance_text,
      </if>
      <if test="needSupply != null">
        need_supply,
      </if>
      <if test="needDoctor != null">
        need_doctor,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="ext != null">
        ext,
      </if>
      <if test="addTime != null">
        add_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="bizScene != null">
        #{bizScene,jdbcType=VARCHAR},
      </if>
      <if test="nodeId != null">
        #{nodeId,jdbcType=VARCHAR},
      </if>
      <if test="nodeName != null">
        #{nodeName,jdbcType=VARCHAR},
      </if>
      <if test="assessmentText != null">
        #{assessmentText,jdbcType=VARCHAR},
      </if>
      <if test="assessmentImg != null">
        #{assessmentImg,jdbcType=VARCHAR},
      </if>
      <if test="guidanceText != null">
        #{guidanceText,jdbcType=VARCHAR},
      </if>
      <if test="needSupply != null">
        #{needSupply,jdbcType=TINYINT},
      </if>
      <if test="needDoctor != null">
        #{needDoctor,jdbcType=TINYINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="ext != null">
        #{ext,jdbcType=CHAR},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.dzhealth.ai.service.infrastructure.dal.example.DecisionNodeEntityExample" resultType="java.lang.Long">
    select count(*) from decision_node
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update decision_node
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.bizScene != null">
        biz_scene = #{record.bizScene,jdbcType=VARCHAR},
      </if>
      <if test="record.nodeId != null">
        node_id = #{record.nodeId,jdbcType=VARCHAR},
      </if>
      <if test="record.nodeName != null">
        node_name = #{record.nodeName,jdbcType=VARCHAR},
      </if>
      <if test="record.assessmentText != null">
        assessment_text = #{record.assessmentText,jdbcType=VARCHAR},
      </if>
      <if test="record.assessmentImg != null">
        assessment_img = #{record.assessmentImg,jdbcType=VARCHAR},
      </if>
      <if test="record.guidanceText != null">
        guidance_text = #{record.guidanceText,jdbcType=VARCHAR},
      </if>
      <if test="record.needSupply != null">
        need_supply = #{record.needSupply,jdbcType=TINYINT},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=VARCHAR},
      </if>
      <if test="record.ext != null">
        ext = #{record.ext,jdbcType=CHAR},
      </if>
      <if test="record.addTime != null">
        add_time = #{record.addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update decision_node
    set id = #{record.id,jdbcType=BIGINT},
      biz_scene = #{record.bizScene,jdbcType=VARCHAR},
      node_id = #{record.nodeId,jdbcType=VARCHAR},
      node_name = #{record.nodeName,jdbcType=VARCHAR},
      assessment_text = #{record.assessmentText,jdbcType=VARCHAR},
      assessment_img = #{record.assessmentImg,jdbcType=VARCHAR},
      guidance_text = #{record.guidanceText,jdbcType=VARCHAR},
      need_supply = #{needSupply,jdbcType=TINYINT},
      status = #{record.status,jdbcType=VARCHAR},
      ext = #{record.ext,jdbcType=CHAR},
      add_time = #{record.addTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.dzhealth.ai.service.infrastructure.dal.entity.DecisionNodeDO">
    update decision_node
    <set>
      <if test="bizScene != null">
        biz_scene = #{bizScene,jdbcType=VARCHAR},
      </if>
      <if test="nodeId != null">
        node_id = #{nodeId,jdbcType=VARCHAR},
      </if>
      <if test="nodeName != null">
        node_name = #{nodeName,jdbcType=VARCHAR},
      </if>
      <if test="assessmentText != null">
        assessment_text = #{assessmentText,jdbcType=VARCHAR},
      </if>
      <if test="assessmentImg != null">
        assessment_img = #{assessmentImg,jdbcType=VARCHAR},
      </if>
      <if test="guidanceText != null">
        guidance_text = #{guidanceText,jdbcType=VARCHAR},
      </if>
      <if test="needSupply != null">
        need_supply = #{needSupply,jdbcType=TINYINT},
      </if>
      <if test="needDoctor != null">
        need_doctor = #{needDoctor,jdbcType=TINYINT},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=VARCHAR},
      </if>
      <if test="ext != null">
        ext = #{ext,jdbcType=CHAR},
      </if>
      <if test="addTime != null">
        add_time = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.dzhealth.ai.service.infrastructure.dal.entity.DecisionNodeDO">
    update decision_node
    set biz_scene = #{bizScene,jdbcType=VARCHAR},
      node_id = #{nodeId,jdbcType=VARCHAR},
      node_name = #{nodeName,jdbcType=VARCHAR},
      assessment_text = #{assessmentText,jdbcType=VARCHAR},
      assessment_img = #{assessmentImg,jdbcType=VARCHAR},
      guidance_text = #{guidanceText,jdbcType=VARCHAR},
      need_supply = #{needSupply,jdbcType=TINYINT},
      status = #{status,jdbcType=VARCHAR},
      ext = #{ext,jdbcType=CHAR},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <!-- 批量插入节点 -->
  <insert id="batchInsert" parameterType="java.util.List">
    <foreach collection="list" item="item" separator=";">
      insert into decision_node
      <trim prefix="(" suffix=")" suffixOverrides=",">
        biz_scene,
        node_id,
        <if test="item.nodeName != null">node_name,</if>
        <if test="item.assessmentText != null">assessment_text,</if>
        <if test="item.assessmentImg != null">assessment_img,</if>
        <if test="item.guidanceText != null">guidance_text,</if>
        <if test="item.needSupply != null">need_supply,</if>
        <if test="item.needDoctor != null">need_doctor,</if>
        status,
        ext,
        add_time,
        update_time
      </trim>
      <trim prefix="values (" suffix=")" suffixOverrides=",">
        #{item.bizScene,jdbcType=VARCHAR},
        #{item.nodeId,jdbcType=VARCHAR},
        <if test="item.nodeName != null">#{item.nodeName,jdbcType=VARCHAR},</if>
        <if test="item.assessmentText != null">#{item.assessmentText,jdbcType=VARCHAR},</if>
        <if test="item.assessmentImg != null">#{item.assessmentImg,jdbcType=VARCHAR},</if>
        <if test="item.guidanceText != null">#{item.guidanceText,jdbcType=VARCHAR},</if>
        <if test="item.needSupply != null">#{item.needSupply,jdbcType=TINYINT},</if>
        <if test="item.needDoctor != null">#{item.needDoctor,jdbcType=TINYINT},</if>
        #{item.status,jdbcType=VARCHAR},
        #{item.ext,jdbcType=CHAR},
        #{item.addTime,jdbcType=TIMESTAMP},
        #{item.updateTime,jdbcType=TIMESTAMP}
      </trim>
    </foreach>
  </insert>

  <!-- 批量更新节点（根据nodeId） -->
  <!-- 使用CASE WHEN语句实现真正的批量更新 -->
  <update id="batchUpdateByNodeId" parameterType="java.util.List">
    update decision_node
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="biz_scene = case" suffix="end,">
        <foreach collection="list" item="item">
          <if test="item.bizScene != null">
            when node_id = #{item.nodeId,jdbcType=VARCHAR} then #{item.bizScene,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="node_name = case" suffix="end,">
        <foreach collection="list" item="item">
          <if test="item.nodeName != null">
            when node_id = #{item.nodeId,jdbcType=VARCHAR} then #{item.nodeName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="assessment_text = case" suffix="end,">
        <foreach collection="list" item="item">
          <if test="item.assessmentText != null">
            when node_id = #{item.nodeId,jdbcType=VARCHAR} then #{item.assessmentText,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="assessment_img = case" suffix="end,">
        <foreach collection="list" item="item">
          <if test="item.assessmentImg != null">
            when node_id = #{item.nodeId,jdbcType=VARCHAR} then #{item.assessmentImg,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="guidance_text = case" suffix="end,">
        <foreach collection="list" item="item">
          <if test="item.guidanceText != null">
            when node_id = #{item.nodeId,jdbcType=VARCHAR} then #{item.guidanceText,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="need_supply = case" suffix="end,">
        <foreach collection="list" item="item">
          <if test="item.needSupply != null">
            when node_id = #{item.nodeId,jdbcType=VARCHAR} then #{item.needSupply,jdbcType=TINYINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="need_doctor = case" suffix="end,">
        <foreach collection="list" item="item">
          <if test="item.needDoctor != null">
            when node_id = #{item.nodeId,jdbcType=VARCHAR} then #{item.needDoctor,jdbcType=TINYINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="status = case" suffix="end,">
        <foreach collection="list" item="item">
          <if test="item.status != null">
            when node_id = #{item.nodeId,jdbcType=VARCHAR} then #{item.status,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="ext = case" suffix="end,">
        <foreach collection="list" item="item">
          <if test="item.ext != null">
            when node_id = #{item.nodeId,jdbcType=VARCHAR} then #{item.ext,jdbcType=CHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="add_time = case" suffix="end,">
        <foreach collection="list" item="item">
          <if test="item.addTime != null">
            when node_id = #{item.nodeId,jdbcType=VARCHAR} then #{item.addTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="update_time = case" suffix="end,">
        <foreach collection="list" item="item">
          <if test="item.updateTime != null">
            when node_id = #{item.nodeId,jdbcType=VARCHAR} then #{item.updateTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
    </trim>
    where node_id in
    <foreach collection="list" item="item" open="(" close=")" separator=",">
      #{item.nodeId,jdbcType=VARCHAR}
    </foreach>
  </update>
</mapper>