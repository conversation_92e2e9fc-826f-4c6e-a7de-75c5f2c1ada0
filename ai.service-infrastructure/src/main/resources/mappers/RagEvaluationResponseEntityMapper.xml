<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.dzhealth.ai.service.infrastructure.dal.mapper.RagEvaluationResponseEntityMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.dzhealth.ai.service.infrastructure.dal.entity.RagEvaluationResponseEntity">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="session_id" jdbcType="BIGINT" property="sessionId" />
    <result column="msg_id" jdbcType="BIGINT" property="msgId" />
    <result column="evaluation_key" jdbcType="VARCHAR" property="evaluationKey" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="pass" jdbcType="BIT" property="pass" />
    <result column="score" jdbcType="REAL" property="score" />
    <result column="success" jdbcType="BIT" property="success" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
    <result column="bizScene" jdbcType="VARCHAR" property="bizscene" />
    <result column="modelScene" jdbcType="VARCHAR" property="modelscene" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.sankuai.dzhealth.ai.service.infrastructure.dal.entity.RagEvaluationResponseEntityWithBLOBs">
    <result column="feedback" jdbcType="LONGVARCHAR" property="feedback" />
    <result column="metadata" jdbcType="LONGVARCHAR" property="metadata" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, session_id, msg_id, evaluation_key, description, pass, score, success, created_at, 
    updated_at, bizScene, modelScene
  </sql>
  <sql id="Blob_Column_List">
    feedback, metadata
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.sankuai.dzhealth.ai.service.infrastructure.dal.example.RagEvaluationResponseEntityExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from rag_evaluation_response
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="com.sankuai.dzhealth.ai.service.infrastructure.dal.example.RagEvaluationResponseEntityExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from rag_evaluation_response
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from rag_evaluation_response
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from rag_evaluation_response
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.dzhealth.ai.service.infrastructure.dal.example.RagEvaluationResponseEntityExample">
    delete from rag_evaluation_response
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sankuai.dzhealth.ai.service.infrastructure.dal.entity.RagEvaluationResponseEntityWithBLOBs">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into rag_evaluation_response (session_id, msg_id, evaluation_key, 
      description, pass, score, 
      success, created_at, updated_at, 
      bizScene, modelScene, feedback, 
      metadata)
    values (#{sessionId,jdbcType=BIGINT}, #{msgId,jdbcType=BIGINT}, #{evaluationKey,jdbcType=VARCHAR}, 
      #{description,jdbcType=VARCHAR}, #{pass,jdbcType=BIT}, #{score,jdbcType=REAL}, 
      #{success,jdbcType=BIT}, #{createdAt,jdbcType=TIMESTAMP}, #{updatedAt,jdbcType=TIMESTAMP}, 
      #{bizscene,jdbcType=VARCHAR}, #{modelscene,jdbcType=VARCHAR}, #{feedback,jdbcType=LONGVARCHAR}, 
      #{metadata,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.sankuai.dzhealth.ai.service.infrastructure.dal.entity.RagEvaluationResponseEntityWithBLOBs">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into rag_evaluation_response
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="sessionId != null">
        session_id,
      </if>
      <if test="msgId != null">
        msg_id,
      </if>
      <if test="evaluationKey != null">
        evaluation_key,
      </if>
      <if test="description != null">
        description,
      </if>
      <if test="pass != null">
        pass,
      </if>
      <if test="score != null">
        score,
      </if>
      <if test="success != null">
        success,
      </if>
      <if test="createdAt != null">
        created_at,
      </if>
      <if test="updatedAt != null">
        updated_at,
      </if>
      <if test="bizscene != null">
        bizScene,
      </if>
      <if test="modelscene != null">
        modelScene,
      </if>
      <if test="feedback != null">
        feedback,
      </if>
      <if test="metadata != null">
        metadata,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="sessionId != null">
        #{sessionId,jdbcType=BIGINT},
      </if>
      <if test="msgId != null">
        #{msgId,jdbcType=BIGINT},
      </if>
      <if test="evaluationKey != null">
        #{evaluationKey,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        #{description,jdbcType=VARCHAR},
      </if>
      <if test="pass != null">
        #{pass,jdbcType=BIT},
      </if>
      <if test="score != null">
        #{score,jdbcType=REAL},
      </if>
      <if test="success != null">
        #{success,jdbcType=BIT},
      </if>
      <if test="createdAt != null">
        #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedAt != null">
        #{updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="bizscene != null">
        #{bizscene,jdbcType=VARCHAR},
      </if>
      <if test="modelscene != null">
        #{modelscene,jdbcType=VARCHAR},
      </if>
      <if test="feedback != null">
        #{feedback,jdbcType=LONGVARCHAR},
      </if>
      <if test="metadata != null">
        #{metadata,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.dzhealth.ai.service.infrastructure.dal.example.RagEvaluationResponseEntityExample" resultType="java.lang.Long">
    select count(*) from rag_evaluation_response
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update rag_evaluation_response
    <set>
      <if test="row.id != null">
        id = #{row.id,jdbcType=BIGINT},
      </if>
      <if test="row.sessionId != null">
        session_id = #{row.sessionId,jdbcType=BIGINT},
      </if>
      <if test="row.msgId != null">
        msg_id = #{row.msgId,jdbcType=BIGINT},
      </if>
      <if test="row.evaluationKey != null">
        evaluation_key = #{row.evaluationKey,jdbcType=VARCHAR},
      </if>
      <if test="row.description != null">
        description = #{row.description,jdbcType=VARCHAR},
      </if>
      <if test="row.pass != null">
        pass = #{row.pass,jdbcType=BIT},
      </if>
      <if test="row.score != null">
        score = #{row.score,jdbcType=REAL},
      </if>
      <if test="row.success != null">
        success = #{row.success,jdbcType=BIT},
      </if>
      <if test="row.createdAt != null">
        created_at = #{row.createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="row.updatedAt != null">
        updated_at = #{row.updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="row.bizscene != null">
        bizScene = #{row.bizscene,jdbcType=VARCHAR},
      </if>
      <if test="row.modelscene != null">
        modelScene = #{row.modelscene,jdbcType=VARCHAR},
      </if>
      <if test="row.feedback != null">
        feedback = #{row.feedback,jdbcType=LONGVARCHAR},
      </if>
      <if test="row.metadata != null">
        metadata = #{row.metadata,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    update rag_evaluation_response
    set id = #{row.id,jdbcType=BIGINT},
      session_id = #{row.sessionId,jdbcType=BIGINT},
      msg_id = #{row.msgId,jdbcType=BIGINT},
      evaluation_key = #{row.evaluationKey,jdbcType=VARCHAR},
      description = #{row.description,jdbcType=VARCHAR},
      pass = #{row.pass,jdbcType=BIT},
      score = #{row.score,jdbcType=REAL},
      success = #{row.success,jdbcType=BIT},
      created_at = #{row.createdAt,jdbcType=TIMESTAMP},
      updated_at = #{row.updatedAt,jdbcType=TIMESTAMP},
      bizScene = #{row.bizscene,jdbcType=VARCHAR},
      modelScene = #{row.modelscene,jdbcType=VARCHAR},
      feedback = #{row.feedback,jdbcType=LONGVARCHAR},
      metadata = #{row.metadata,jdbcType=LONGVARCHAR}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update rag_evaluation_response
    set id = #{row.id,jdbcType=BIGINT},
      session_id = #{row.sessionId,jdbcType=BIGINT},
      msg_id = #{row.msgId,jdbcType=BIGINT},
      evaluation_key = #{row.evaluationKey,jdbcType=VARCHAR},
      description = #{row.description,jdbcType=VARCHAR},
      pass = #{row.pass,jdbcType=BIT},
      score = #{row.score,jdbcType=REAL},
      success = #{row.success,jdbcType=BIT},
      created_at = #{row.createdAt,jdbcType=TIMESTAMP},
      updated_at = #{row.updatedAt,jdbcType=TIMESTAMP},
      bizScene = #{row.bizscene,jdbcType=VARCHAR},
      modelScene = #{row.modelscene,jdbcType=VARCHAR}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.dzhealth.ai.service.infrastructure.dal.entity.RagEvaluationResponseEntityWithBLOBs">
    update rag_evaluation_response
    <set>
      <if test="sessionId != null">
        session_id = #{sessionId,jdbcType=BIGINT},
      </if>
      <if test="msgId != null">
        msg_id = #{msgId,jdbcType=BIGINT},
      </if>
      <if test="evaluationKey != null">
        evaluation_key = #{evaluationKey,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        description = #{description,jdbcType=VARCHAR},
      </if>
      <if test="pass != null">
        pass = #{pass,jdbcType=BIT},
      </if>
      <if test="score != null">
        score = #{score,jdbcType=REAL},
      </if>
      <if test="success != null">
        success = #{success,jdbcType=BIT},
      </if>
      <if test="createdAt != null">
        created_at = #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedAt != null">
        updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="bizscene != null">
        bizScene = #{bizscene,jdbcType=VARCHAR},
      </if>
      <if test="modelscene != null">
        modelScene = #{modelscene,jdbcType=VARCHAR},
      </if>
      <if test="feedback != null">
        feedback = #{feedback,jdbcType=LONGVARCHAR},
      </if>
      <if test="metadata != null">
        metadata = #{metadata,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.sankuai.dzhealth.ai.service.infrastructure.dal.entity.RagEvaluationResponseEntityWithBLOBs">
    update rag_evaluation_response
    set session_id = #{sessionId,jdbcType=BIGINT},
      msg_id = #{msgId,jdbcType=BIGINT},
      evaluation_key = #{evaluationKey,jdbcType=VARCHAR},
      description = #{description,jdbcType=VARCHAR},
      pass = #{pass,jdbcType=BIT},
      score = #{score,jdbcType=REAL},
      success = #{success,jdbcType=BIT},
      created_at = #{createdAt,jdbcType=TIMESTAMP},
      updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      bizScene = #{bizscene,jdbcType=VARCHAR},
      modelScene = #{modelscene,jdbcType=VARCHAR},
      feedback = #{feedback,jdbcType=LONGVARCHAR},
      metadata = #{metadata,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.dzhealth.ai.service.infrastructure.dal.entity.RagEvaluationResponseEntity">
    update rag_evaluation_response
    set session_id = #{sessionId,jdbcType=BIGINT},
      msg_id = #{msgId,jdbcType=BIGINT},
      evaluation_key = #{evaluationKey,jdbcType=VARCHAR},
      description = #{description,jdbcType=VARCHAR},
      pass = #{pass,jdbcType=BIT},
      score = #{score,jdbcType=REAL},
      success = #{success,jdbcType=BIT},
      created_at = #{createdAt,jdbcType=TIMESTAMP},
      updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      bizScene = #{bizscene,jdbcType=VARCHAR},
      modelScene = #{modelscene,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>