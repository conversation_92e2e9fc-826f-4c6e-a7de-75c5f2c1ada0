<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.dzhealth.ai.service.agent.infrastructure.dal.mapper.MessageEvaluationResultEntityMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.dzhealth.ai.service.agent.infrastructure.dal.entity.MessageEvaluationResultEntity">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="biz_scene" jdbcType="VARCHAR" property="bizScene" />
    <result column="session_id" jdbcType="VARCHAR" property="sessionId" />
    <result column="message_id" jdbcType="VARCHAR" property="messageId" />
    <result column="model_scene" jdbcType="VARCHAR" property="modelScene" />
    <result column="item" jdbcType="VARCHAR" property="item" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="reason" jdbcType="VARCHAR" property="reason" />
    <result column="score" jdbcType="DECIMAL" property="score" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="source" jdbcType="INTEGER" property="source" />
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="evaluation_id" jdbcType="BIGINT" property="evaluationId" />
    <result column="evaluation_ver" jdbcType="INTEGER" property="evaluationVer" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, biz_scene, session_id, message_id, model_scene, item, description, reason, score, 
    type, source, add_time, update_time, evaluation_id, evaluation_ver
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.dzhealth.ai.service.agent.infrastructure.dal.example.MessageEvaluationResultEntityExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from message_evaluation_result
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from message_evaluation_result
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from message_evaluation_result
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.dzhealth.ai.service.agent.infrastructure.dal.example.MessageEvaluationResultEntityExample">
    delete from message_evaluation_result
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sankuai.dzhealth.ai.service.agent.infrastructure.dal.entity.MessageEvaluationResultEntity">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into message_evaluation_result (biz_scene, session_id, message_id, 
      model_scene, item, description, 
      reason, score, type, 
      source, add_time, update_time, 
      evaluation_id, evaluation_ver)
    values (#{bizScene,jdbcType=VARCHAR}, #{sessionId,jdbcType=VARCHAR}, #{messageId,jdbcType=VARCHAR}, 
      #{modelScene,jdbcType=VARCHAR}, #{item,jdbcType=VARCHAR}, #{description,jdbcType=VARCHAR}, 
      #{reason,jdbcType=VARCHAR}, #{score,jdbcType=DECIMAL}, #{type,jdbcType=INTEGER}, 
      #{source,jdbcType=INTEGER}, #{addTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{evaluationId,jdbcType=BIGINT}, #{evaluationVer,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.sankuai.dzhealth.ai.service.agent.infrastructure.dal.entity.MessageEvaluationResultEntity">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into message_evaluation_result
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="bizScene != null">
        biz_scene,
      </if>
      <if test="sessionId != null">
        session_id,
      </if>
      <if test="messageId != null">
        message_id,
      </if>
      <if test="modelScene != null">
        model_scene,
      </if>
      <if test="item != null">
        item,
      </if>
      <if test="description != null">
        description,
      </if>
      <if test="reason != null">
        reason,
      </if>
      <if test="score != null">
        score,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="source != null">
        source,
      </if>
      <if test="addTime != null">
        add_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="evaluationId != null">
        evaluation_id,
      </if>
      <if test="evaluationVer != null">
        evaluation_ver,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="bizScene != null">
        #{bizScene,jdbcType=VARCHAR},
      </if>
      <if test="sessionId != null">
        #{sessionId,jdbcType=VARCHAR},
      </if>
      <if test="messageId != null">
        #{messageId,jdbcType=VARCHAR},
      </if>
      <if test="modelScene != null">
        #{modelScene,jdbcType=VARCHAR},
      </if>
      <if test="item != null">
        #{item,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        #{description,jdbcType=VARCHAR},
      </if>
      <if test="reason != null">
        #{reason,jdbcType=VARCHAR},
      </if>
      <if test="score != null">
        #{score,jdbcType=DECIMAL},
      </if>
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="source != null">
        #{source,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="evaluationId != null">
        #{evaluationId,jdbcType=BIGINT},
      </if>
      <if test="evaluationVer != null">
        #{evaluationVer,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.dzhealth.ai.service.agent.infrastructure.dal.example.MessageEvaluationResultEntityExample" resultType="java.lang.Long">
    select count(*) from message_evaluation_result
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update message_evaluation_result
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.bizScene != null">
        biz_scene = #{record.bizScene,jdbcType=VARCHAR},
      </if>
      <if test="record.sessionId != null">
        session_id = #{record.sessionId,jdbcType=VARCHAR},
      </if>
      <if test="record.messageId != null">
        message_id = #{record.messageId,jdbcType=VARCHAR},
      </if>
      <if test="record.modelScene != null">
        model_scene = #{record.modelScene,jdbcType=VARCHAR},
      </if>
      <if test="record.item != null">
        item = #{record.item,jdbcType=VARCHAR},
      </if>
      <if test="record.description != null">
        description = #{record.description,jdbcType=VARCHAR},
      </if>
      <if test="record.reason != null">
        reason = #{record.reason,jdbcType=VARCHAR},
      </if>
      <if test="record.score != null">
        score = #{record.score,jdbcType=DECIMAL},
      </if>
      <if test="record.type != null">
        type = #{record.type,jdbcType=INTEGER},
      </if>
      <if test="record.source != null">
        source = #{record.source,jdbcType=INTEGER},
      </if>
      <if test="record.addTime != null">
        add_time = #{record.addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.evaluationId != null">
        evaluation_id = #{record.evaluationId,jdbcType=BIGINT},
      </if>
      <if test="record.evaluationVer != null">
        evaluation_ver = #{record.evaluationVer,jdbcType=INTEGER},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update message_evaluation_result
    set id = #{record.id,jdbcType=BIGINT},
      biz_scene = #{record.bizScene,jdbcType=VARCHAR},
      session_id = #{record.sessionId,jdbcType=VARCHAR},
      message_id = #{record.messageId,jdbcType=VARCHAR},
      model_scene = #{record.modelScene,jdbcType=VARCHAR},
      item = #{record.item,jdbcType=VARCHAR},
      description = #{record.description,jdbcType=VARCHAR},
      reason = #{record.reason,jdbcType=VARCHAR},
      score = #{record.score,jdbcType=DECIMAL},
      type = #{record.type,jdbcType=INTEGER},
      source = #{record.source,jdbcType=INTEGER},
      add_time = #{record.addTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      evaluation_id = #{record.evaluationId,jdbcType=BIGINT},
      evaluation_ver = #{record.evaluationVer,jdbcType=INTEGER}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.dzhealth.ai.service.agent.infrastructure.dal.entity.MessageEvaluationResultEntity">
    update message_evaluation_result
    <set>
      <if test="bizScene != null">
        biz_scene = #{bizScene,jdbcType=VARCHAR},
      </if>
      <if test="sessionId != null">
        session_id = #{sessionId,jdbcType=VARCHAR},
      </if>
      <if test="messageId != null">
        message_id = #{messageId,jdbcType=VARCHAR},
      </if>
      <if test="modelScene != null">
        model_scene = #{modelScene,jdbcType=VARCHAR},
      </if>
      <if test="item != null">
        item = #{item,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        description = #{description,jdbcType=VARCHAR},
      </if>
      <if test="reason != null">
        reason = #{reason,jdbcType=VARCHAR},
      </if>
      <if test="score != null">
        score = #{score,jdbcType=DECIMAL},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=INTEGER},
      </if>
      <if test="source != null">
        source = #{source,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        add_time = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="evaluationId != null">
        evaluation_id = #{evaluationId,jdbcType=BIGINT},
      </if>
      <if test="evaluationVer != null">
        evaluation_ver = #{evaluationVer,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.dzhealth.ai.service.agent.infrastructure.dal.entity.MessageEvaluationResultEntity">
    update message_evaluation_result
    set biz_scene = #{bizScene,jdbcType=VARCHAR},
      session_id = #{sessionId,jdbcType=VARCHAR},
      message_id = #{messageId,jdbcType=VARCHAR},
      model_scene = #{modelScene,jdbcType=VARCHAR},
      item = #{item,jdbcType=VARCHAR},
      description = #{description,jdbcType=VARCHAR},
      reason = #{reason,jdbcType=VARCHAR},
      score = #{score,jdbcType=DECIMAL},
      type = #{type,jdbcType=INTEGER},
      source = #{source,jdbcType=INTEGER},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      evaluation_id = #{evaluationId,jdbcType=BIGINT},
      evaluation_ver = #{evaluationVer,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>