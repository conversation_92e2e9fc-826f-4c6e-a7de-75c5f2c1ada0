<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.dzhealth.ai.service.infrastructure.dal.mapper.DeepSearchResultEntityMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.dzhealth.ai.service.infrastructure.dal.entity.DeepSearchResultEntity">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="business_source" jdbcType="VARCHAR" property="businessSource" />
    <result column="question" jdbcType="VARCHAR" property="question" />
    <result column="source_urls" jdbcType="CHAR" property="sourceUrls" />
    <result column="confidence_score" jdbcType="DECIMAL" property="confidenceScore" />
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.sankuai.dzhealth.ai.service.infrastructure.dal.entity.DeepSearchResultEntity">
    <result column="answer" jdbcType="LONGVARCHAR" property="answer" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, business_source, question, source_urls, confidence_score, add_time, update_time
  </sql>
  <sql id="Blob_Column_List">
    answer
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.sankuai.dzhealth.ai.service.infrastructure.dal.example.DeepSearchResultEntityExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from deep_search_results
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="com.sankuai.dzhealth.ai.service.infrastructure.dal.example.DeepSearchResultEntityExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from deep_search_results
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from deep_search_results
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from deep_search_results
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.dzhealth.ai.service.infrastructure.dal.example.DeepSearchResultEntityExample">
    delete from deep_search_results
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sankuai.dzhealth.ai.service.infrastructure.dal.entity.DeepSearchResultEntity">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into deep_search_results (business_source, question, source_urls, 
      confidence_score, add_time, update_time, 
      answer)
    values (#{businessSource,jdbcType=VARCHAR}, #{question,jdbcType=VARCHAR}, #{sourceUrls,jdbcType=CHAR}, 
      #{confidenceScore,jdbcType=DECIMAL}, #{addTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{answer,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.sankuai.dzhealth.ai.service.infrastructure.dal.entity.DeepSearchResultEntity">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into deep_search_results
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="businessSource != null">
        business_source,
      </if>
      <if test="question != null">
        question,
      </if>
      <if test="sourceUrls != null">
        source_urls,
      </if>
      <if test="confidenceScore != null">
        confidence_score,
      </if>
      <if test="addTime != null">
        add_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="answer != null">
        answer,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="businessSource != null">
        #{businessSource,jdbcType=VARCHAR},
      </if>
      <if test="question != null">
        #{question,jdbcType=VARCHAR},
      </if>
      <if test="sourceUrls != null">
        #{sourceUrls,jdbcType=CHAR},
      </if>
      <if test="confidenceScore != null">
        #{confidenceScore,jdbcType=DECIMAL},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="answer != null">
        #{answer,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.dzhealth.ai.service.infrastructure.dal.example.DeepSearchResultEntityExample" resultType="java.lang.Long">
    select count(*) from deep_search_results
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update deep_search_results
    <set>
      <if test="row.id != null">
        id = #{row.id,jdbcType=BIGINT},
      </if>
      <if test="row.businessSource != null">
        business_source = #{row.businessSource,jdbcType=VARCHAR},
      </if>
      <if test="row.question != null">
        question = #{row.question,jdbcType=VARCHAR},
      </if>
      <if test="row.sourceUrls != null">
        source_urls = #{row.sourceUrls,jdbcType=CHAR},
      </if>
      <if test="row.confidenceScore != null">
        confidence_score = #{row.confidenceScore,jdbcType=DECIMAL},
      </if>
      <if test="row.addTime != null">
        add_time = #{row.addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.updateTime != null">
        update_time = #{row.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.answer != null">
        answer = #{row.answer,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    update deep_search_results
    set id = #{row.id,jdbcType=BIGINT},
      business_source = #{row.businessSource,jdbcType=VARCHAR},
      question = #{row.question,jdbcType=VARCHAR},
      source_urls = #{row.sourceUrls,jdbcType=CHAR},
      confidence_score = #{row.confidenceScore,jdbcType=DECIMAL},
      add_time = #{row.addTime,jdbcType=TIMESTAMP},
      update_time = #{row.updateTime,jdbcType=TIMESTAMP},
      answer = #{row.answer,jdbcType=LONGVARCHAR}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update deep_search_results
    set id = #{row.id,jdbcType=BIGINT},
      business_source = #{row.businessSource,jdbcType=VARCHAR},
      question = #{row.question,jdbcType=VARCHAR},
      source_urls = #{row.sourceUrls,jdbcType=CHAR},
      confidence_score = #{row.confidenceScore,jdbcType=DECIMAL},
      add_time = #{row.addTime,jdbcType=TIMESTAMP},
      update_time = #{row.updateTime,jdbcType=TIMESTAMP}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.dzhealth.ai.service.infrastructure.dal.entity.DeepSearchResultEntity">
    update deep_search_results
    <set>
      <if test="businessSource != null">
        business_source = #{businessSource,jdbcType=VARCHAR},
      </if>
      <if test="question != null">
        question = #{question,jdbcType=VARCHAR},
      </if>
      <if test="sourceUrls != null">
        source_urls = #{sourceUrls,jdbcType=CHAR},
      </if>
      <if test="confidenceScore != null">
        confidence_score = #{confidenceScore,jdbcType=DECIMAL},
      </if>
      <if test="addTime != null">
        add_time = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="answer != null">
        answer = #{answer,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.sankuai.dzhealth.ai.service.infrastructure.dal.entity.DeepSearchResultEntity">
    update deep_search_results
    set business_source = #{businessSource,jdbcType=VARCHAR},
      question = #{question,jdbcType=VARCHAR},
      source_urls = #{sourceUrls,jdbcType=CHAR},
      confidence_score = #{confidenceScore,jdbcType=DECIMAL},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      answer = #{answer,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.dzhealth.ai.service.infrastructure.dal.entity.DeepSearchResultEntity">
    update deep_search_results
    set business_source = #{businessSource,jdbcType=VARCHAR},
      question = #{question,jdbcType=VARCHAR},
      source_urls = #{sourceUrls,jdbcType=CHAR},
      confidence_score = #{confidenceScore,jdbcType=DECIMAL},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>