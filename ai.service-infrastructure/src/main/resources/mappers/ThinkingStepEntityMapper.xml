<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.dzhealth.ai.service.infrastructure.dal.mapper.ThinkingStepEntityMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.dzhealth.ai.service.infrastructure.dal.entity.ThinkingStepEntity">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="session_id" jdbcType="BIGINT" property="sessionId" />
    <result column="step_number" jdbcType="BIGINT" property="stepNumber" />
    <result column="total_steps" jdbcType="BIGINT" property="totalSteps" />
    <result column="thought" jdbcType="VARCHAR" property="thought" />
    <result column="search_results" jdbcType="CHAR" property="searchResults" />
    <result column="is_revision" jdbcType="BIT" property="isRevision" />
    <result column="revises_step_number" jdbcType="BIGINT" property="revisesStepNumber" />
    <result column="next_thought_needed" jdbcType="BIT" property="nextThoughtNeeded" />
    <result column="branch_from_step" jdbcType="BIGINT" property="branchFromStep" />
    <result column="branch_id" jdbcType="VARCHAR" property="branchId" />
    <result column="confidence_score" jdbcType="DECIMAL" property="confidenceScore" />
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, session_id, step_number, total_steps, thought, search_results, is_revision, revises_step_number, 
    next_thought_needed, branch_from_step, branch_id, confidence_score, add_time, update_time
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.dzhealth.ai.service.infrastructure.dal.example.ThinkingStepEntityExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from thinking_steps
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from thinking_steps
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from thinking_steps
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.dzhealth.ai.service.infrastructure.dal.example.ThinkingStepEntityExample">
    delete from thinking_steps
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sankuai.dzhealth.ai.service.infrastructure.dal.entity.ThinkingStepEntity">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into thinking_steps (session_id, step_number, total_steps, 
      thought, search_results, is_revision, 
      revises_step_number, next_thought_needed, branch_from_step, 
      branch_id, confidence_score, add_time, 
      update_time)
    values (#{sessionId,jdbcType=BIGINT}, #{stepNumber,jdbcType=BIGINT}, #{totalSteps,jdbcType=BIGINT}, 
      #{thought,jdbcType=VARCHAR}, #{searchResults,jdbcType=CHAR}, #{isRevision,jdbcType=BIT}, 
      #{revisesStepNumber,jdbcType=BIGINT}, #{nextThoughtNeeded,jdbcType=BIT}, #{branchFromStep,jdbcType=BIGINT}, 
      #{branchId,jdbcType=VARCHAR}, #{confidenceScore,jdbcType=DECIMAL}, #{addTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.sankuai.dzhealth.ai.service.infrastructure.dal.entity.ThinkingStepEntity">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into thinking_steps
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="sessionId != null">
        session_id,
      </if>
      <if test="stepNumber != null">
        step_number,
      </if>
      <if test="totalSteps != null">
        total_steps,
      </if>
      <if test="thought != null">
        thought,
      </if>
      <if test="searchResults != null">
        search_results,
      </if>
      <if test="isRevision != null">
        is_revision,
      </if>
      <if test="revisesStepNumber != null">
        revises_step_number,
      </if>
      <if test="nextThoughtNeeded != null">
        next_thought_needed,
      </if>
      <if test="branchFromStep != null">
        branch_from_step,
      </if>
      <if test="branchId != null">
        branch_id,
      </if>
      <if test="confidenceScore != null">
        confidence_score,
      </if>
      <if test="addTime != null">
        add_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="sessionId != null">
        #{sessionId,jdbcType=BIGINT},
      </if>
      <if test="stepNumber != null">
        #{stepNumber,jdbcType=BIGINT},
      </if>
      <if test="totalSteps != null">
        #{totalSteps,jdbcType=BIGINT},
      </if>
      <if test="thought != null">
        #{thought,jdbcType=VARCHAR},
      </if>
      <if test="searchResults != null">
        #{searchResults,jdbcType=CHAR},
      </if>
      <if test="isRevision != null">
        #{isRevision,jdbcType=BIT},
      </if>
      <if test="revisesStepNumber != null">
        #{revisesStepNumber,jdbcType=BIGINT},
      </if>
      <if test="nextThoughtNeeded != null">
        #{nextThoughtNeeded,jdbcType=BIT},
      </if>
      <if test="branchFromStep != null">
        #{branchFromStep,jdbcType=BIGINT},
      </if>
      <if test="branchId != null">
        #{branchId,jdbcType=VARCHAR},
      </if>
      <if test="confidenceScore != null">
        #{confidenceScore,jdbcType=DECIMAL},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.dzhealth.ai.service.infrastructure.dal.example.ThinkingStepEntityExample" resultType="java.lang.Long">
    select count(*) from thinking_steps
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update thinking_steps
    <set>
      <if test="row.id != null">
        id = #{row.id,jdbcType=BIGINT},
      </if>
      <if test="row.sessionId != null">
        session_id = #{row.sessionId,jdbcType=BIGINT},
      </if>
      <if test="row.stepNumber != null">
        step_number = #{row.stepNumber,jdbcType=BIGINT},
      </if>
      <if test="row.totalSteps != null">
        total_steps = #{row.totalSteps,jdbcType=BIGINT},
      </if>
      <if test="row.thought != null">
        thought = #{row.thought,jdbcType=VARCHAR},
      </if>
      <if test="row.searchResults != null">
        search_results = #{row.searchResults,jdbcType=CHAR},
      </if>
      <if test="row.isRevision != null">
        is_revision = #{row.isRevision,jdbcType=BIT},
      </if>
      <if test="row.revisesStepNumber != null">
        revises_step_number = #{row.revisesStepNumber,jdbcType=BIGINT},
      </if>
      <if test="row.nextThoughtNeeded != null">
        next_thought_needed = #{row.nextThoughtNeeded,jdbcType=BIT},
      </if>
      <if test="row.branchFromStep != null">
        branch_from_step = #{row.branchFromStep,jdbcType=BIGINT},
      </if>
      <if test="row.branchId != null">
        branch_id = #{row.branchId,jdbcType=VARCHAR},
      </if>
      <if test="row.confidenceScore != null">
        confidence_score = #{row.confidenceScore,jdbcType=DECIMAL},
      </if>
      <if test="row.addTime != null">
        add_time = #{row.addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.updateTime != null">
        update_time = #{row.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update thinking_steps
    set id = #{row.id,jdbcType=BIGINT},
      session_id = #{row.sessionId,jdbcType=BIGINT},
      step_number = #{row.stepNumber,jdbcType=BIGINT},
      total_steps = #{row.totalSteps,jdbcType=BIGINT},
      thought = #{row.thought,jdbcType=VARCHAR},
      search_results = #{row.searchResults,jdbcType=CHAR},
      is_revision = #{row.isRevision,jdbcType=BIT},
      revises_step_number = #{row.revisesStepNumber,jdbcType=BIGINT},
      next_thought_needed = #{row.nextThoughtNeeded,jdbcType=BIT},
      branch_from_step = #{row.branchFromStep,jdbcType=BIGINT},
      branch_id = #{row.branchId,jdbcType=VARCHAR},
      confidence_score = #{row.confidenceScore,jdbcType=DECIMAL},
      add_time = #{row.addTime,jdbcType=TIMESTAMP},
      update_time = #{row.updateTime,jdbcType=TIMESTAMP}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.dzhealth.ai.service.infrastructure.dal.entity.ThinkingStepEntity">
    update thinking_steps
    <set>
      <if test="sessionId != null">
        session_id = #{sessionId,jdbcType=BIGINT},
      </if>
      <if test="stepNumber != null">
        step_number = #{stepNumber,jdbcType=BIGINT},
      </if>
      <if test="totalSteps != null">
        total_steps = #{totalSteps,jdbcType=BIGINT},
      </if>
      <if test="thought != null">
        thought = #{thought,jdbcType=VARCHAR},
      </if>
      <if test="searchResults != null">
        search_results = #{searchResults,jdbcType=CHAR},
      </if>
      <if test="isRevision != null">
        is_revision = #{isRevision,jdbcType=BIT},
      </if>
      <if test="revisesStepNumber != null">
        revises_step_number = #{revisesStepNumber,jdbcType=BIGINT},
      </if>
      <if test="nextThoughtNeeded != null">
        next_thought_needed = #{nextThoughtNeeded,jdbcType=BIT},
      </if>
      <if test="branchFromStep != null">
        branch_from_step = #{branchFromStep,jdbcType=BIGINT},
      </if>
      <if test="branchId != null">
        branch_id = #{branchId,jdbcType=VARCHAR},
      </if>
      <if test="confidenceScore != null">
        confidence_score = #{confidenceScore,jdbcType=DECIMAL},
      </if>
      <if test="addTime != null">
        add_time = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.dzhealth.ai.service.infrastructure.dal.entity.ThinkingStepEntity">
    update thinking_steps
    set session_id = #{sessionId,jdbcType=BIGINT},
      step_number = #{stepNumber,jdbcType=BIGINT},
      total_steps = #{totalSteps,jdbcType=BIGINT},
      thought = #{thought,jdbcType=VARCHAR},
      search_results = #{searchResults,jdbcType=CHAR},
      is_revision = #{isRevision,jdbcType=BIT},
      revises_step_number = #{revisesStepNumber,jdbcType=BIGINT},
      next_thought_needed = #{nextThoughtNeeded,jdbcType=BIT},
      branch_from_step = #{branchFromStep,jdbcType=BIGINT},
      branch_id = #{branchId,jdbcType=VARCHAR},
      confidence_score = #{confidenceScore,jdbcType=DECIMAL},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>