<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.dzhealth.ai.service.infrastructure.dal.mapper.CorpusLibraryDOMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.dzhealth.ai.service.infrastructure.dal.entity.CorpusLibraryDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="sort_order" jdbcType="INTEGER" property="sortOrder" />
    <result column="prev_corpus_id" jdbcType="BIGINT" property="prevCorpusId" />
    <result column="resource_id" jdbcType="BIGINT" property="resourceId" />
    <result column="resource_channel" jdbcType="VARCHAR" property="resourceChannel" />
    <result column="resource_uri" jdbcType="VARCHAR" property="resourceUri" />
    <result column="corpus_type" jdbcType="INTEGER" property="corpusType" />
    <result column="is_content_quantified" jdbcType="BIT" property="isContentQuantified" />
    <result column="priority" jdbcType="INTEGER" property="priority" />
    <result column="confidence" jdbcType="REAL" property="confidence" />
    <result column="publish_time" jdbcType="TIMESTAMP" property="publishTime" />
    <result column="created_time" jdbcType="TIMESTAMP" property="createdTime" />
    <result column="updated_time" jdbcType="TIMESTAMP" property="updatedTime" />
    <result column="mt_shop_id" jdbcType="BIGINT" property="mtShopId" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.sankuai.dzhealth.ai.service.infrastructure.dal.entity.CorpusLibraryDOWithBLOBs">
    <result column="corpus_summary" jdbcType="LONGVARCHAR" property="corpusSummary" />
    <result column="corpus_content" jdbcType="LONGVARCHAR" property="corpusContent" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, sort_order, prev_corpus_id, resource_id, resource_channel, resource_uri, corpus_type, 
    is_content_quantified, priority, confidence, publish_time, created_time, updated_time, 
    mt_shop_id
  </sql>
  <sql id="Blob_Column_List">
    corpus_summary, corpus_content
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.sankuai.dzhealth.ai.service.infrastructure.dal.example.CorpusLibraryDOExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from corpus_library
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="com.sankuai.dzhealth.ai.service.infrastructure.dal.example.CorpusLibraryDOExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from corpus_library
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from corpus_library
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from corpus_library
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.dzhealth.ai.service.infrastructure.dal.example.CorpusLibraryDOExample">
    delete from corpus_library
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sankuai.dzhealth.ai.service.infrastructure.dal.entity.CorpusLibraryDOWithBLOBs">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into corpus_library (sort_order, prev_corpus_id, resource_id, 
      resource_channel, resource_uri, corpus_type, 
      is_content_quantified, priority, confidence, 
      publish_time, created_time, updated_time, 
      mt_shop_id, corpus_summary, corpus_content
      )
    values (#{sortOrder,jdbcType=INTEGER}, #{prevCorpusId,jdbcType=BIGINT}, #{resourceId,jdbcType=BIGINT}, 
      #{resourceChannel,jdbcType=VARCHAR}, #{resourceUri,jdbcType=VARCHAR}, #{corpusType,jdbcType=INTEGER}, 
      #{isContentQuantified,jdbcType=BIT}, #{priority,jdbcType=INTEGER}, #{confidence,jdbcType=REAL}, 
      #{publishTime,jdbcType=TIMESTAMP}, #{createdTime,jdbcType=TIMESTAMP}, #{updatedTime,jdbcType=TIMESTAMP}, 
      #{mtShopId,jdbcType=BIGINT}, #{corpusSummary,jdbcType=LONGVARCHAR}, #{corpusContent,jdbcType=LONGVARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.sankuai.dzhealth.ai.service.infrastructure.dal.entity.CorpusLibraryDOWithBLOBs">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into corpus_library
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="sortOrder != null">
        sort_order,
      </if>
      <if test="prevCorpusId != null">
        prev_corpus_id,
      </if>
      <if test="resourceId != null">
        resource_id,
      </if>
      <if test="resourceChannel != null">
        resource_channel,
      </if>
      <if test="resourceUri != null">
        resource_uri,
      </if>
      <if test="corpusType != null">
        corpus_type,
      </if>
      <if test="isContentQuantified != null">
        is_content_quantified,
      </if>
      <if test="priority != null">
        priority,
      </if>
      <if test="confidence != null">
        confidence,
      </if>
      <if test="publishTime != null">
        publish_time,
      </if>
      <if test="createdTime != null">
        created_time,
      </if>
      <if test="updatedTime != null">
        updated_time,
      </if>
      <if test="mtShopId != null">
        mt_shop_id,
      </if>
      <if test="corpusSummary != null">
        corpus_summary,
      </if>
      <if test="corpusContent != null">
        corpus_content,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="sortOrder != null">
        #{sortOrder,jdbcType=INTEGER},
      </if>
      <if test="prevCorpusId != null">
        #{prevCorpusId,jdbcType=BIGINT},
      </if>
      <if test="resourceId != null">
        #{resourceId,jdbcType=BIGINT},
      </if>
      <if test="resourceChannel != null">
        #{resourceChannel,jdbcType=VARCHAR},
      </if>
      <if test="resourceUri != null">
        #{resourceUri,jdbcType=VARCHAR},
      </if>
      <if test="corpusType != null">
        #{corpusType,jdbcType=INTEGER},
      </if>
      <if test="isContentQuantified != null">
        #{isContentQuantified,jdbcType=BIT},
      </if>
      <if test="priority != null">
        #{priority,jdbcType=INTEGER},
      </if>
      <if test="confidence != null">
        #{confidence,jdbcType=REAL},
      </if>
      <if test="publishTime != null">
        #{publishTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createdTime != null">
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedTime != null">
        #{updatedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtShopId != null">
        #{mtShopId,jdbcType=BIGINT},
      </if>
      <if test="corpusSummary != null">
        #{corpusSummary,jdbcType=LONGVARCHAR},
      </if>
      <if test="corpusContent != null">
        #{corpusContent,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.dzhealth.ai.service.infrastructure.dal.example.CorpusLibraryDOExample" resultType="java.lang.Long">
    select count(*) from corpus_library
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update corpus_library
    <set>
      <if test="row.id != null">
        id = #{row.id,jdbcType=BIGINT},
      </if>
      <if test="row.sortOrder != null">
        sort_order = #{row.sortOrder,jdbcType=INTEGER},
      </if>
      <if test="row.prevCorpusId != null">
        prev_corpus_id = #{row.prevCorpusId,jdbcType=BIGINT},
      </if>
      <if test="row.resourceId != null">
        resource_id = #{row.resourceId,jdbcType=BIGINT},
      </if>
      <if test="row.resourceChannel != null">
        resource_channel = #{row.resourceChannel,jdbcType=VARCHAR},
      </if>
      <if test="row.resourceUri != null">
        resource_uri = #{row.resourceUri,jdbcType=VARCHAR},
      </if>
      <if test="row.corpusType != null">
        corpus_type = #{row.corpusType,jdbcType=INTEGER},
      </if>
      <if test="row.isContentQuantified != null">
        is_content_quantified = #{row.isContentQuantified,jdbcType=BIT},
      </if>
      <if test="row.priority != null">
        priority = #{row.priority,jdbcType=INTEGER},
      </if>
      <if test="row.confidence != null">
        confidence = #{row.confidence,jdbcType=REAL},
      </if>
      <if test="row.publishTime != null">
        publish_time = #{row.publishTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.createdTime != null">
        created_time = #{row.createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.updatedTime != null">
        updated_time = #{row.updatedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.mtShopId != null">
        mt_shop_id = #{row.mtShopId,jdbcType=BIGINT},
      </if>
      <if test="row.corpusSummary != null">
        corpus_summary = #{row.corpusSummary,jdbcType=LONGVARCHAR},
      </if>
      <if test="row.corpusContent != null">
        corpus_content = #{row.corpusContent,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    update corpus_library
    set id = #{row.id,jdbcType=BIGINT},
      sort_order = #{row.sortOrder,jdbcType=INTEGER},
      prev_corpus_id = #{row.prevCorpusId,jdbcType=BIGINT},
      resource_id = #{row.resourceId,jdbcType=BIGINT},
      resource_channel = #{row.resourceChannel,jdbcType=VARCHAR},
      resource_uri = #{row.resourceUri,jdbcType=VARCHAR},
      corpus_type = #{row.corpusType,jdbcType=INTEGER},
      is_content_quantified = #{row.isContentQuantified,jdbcType=BIT},
      priority = #{row.priority,jdbcType=INTEGER},
      confidence = #{row.confidence,jdbcType=REAL},
      publish_time = #{row.publishTime,jdbcType=TIMESTAMP},
      created_time = #{row.createdTime,jdbcType=TIMESTAMP},
      updated_time = #{row.updatedTime,jdbcType=TIMESTAMP},
      mt_shop_id = #{row.mtShopId,jdbcType=BIGINT},
      corpus_summary = #{row.corpusSummary,jdbcType=LONGVARCHAR},
      corpus_content = #{row.corpusContent,jdbcType=LONGVARCHAR}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update corpus_library
    set id = #{row.id,jdbcType=BIGINT},
      sort_order = #{row.sortOrder,jdbcType=INTEGER},
      prev_corpus_id = #{row.prevCorpusId,jdbcType=BIGINT},
      resource_id = #{row.resourceId,jdbcType=BIGINT},
      resource_channel = #{row.resourceChannel,jdbcType=VARCHAR},
      resource_uri = #{row.resourceUri,jdbcType=VARCHAR},
      corpus_type = #{row.corpusType,jdbcType=INTEGER},
      is_content_quantified = #{row.isContentQuantified,jdbcType=BIT},
      priority = #{row.priority,jdbcType=INTEGER},
      confidence = #{row.confidence,jdbcType=REAL},
      publish_time = #{row.publishTime,jdbcType=TIMESTAMP},
      created_time = #{row.createdTime,jdbcType=TIMESTAMP},
      updated_time = #{row.updatedTime,jdbcType=TIMESTAMP},
      mt_shop_id = #{row.mtShopId,jdbcType=BIGINT}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.dzhealth.ai.service.infrastructure.dal.entity.CorpusLibraryDOWithBLOBs">
    update corpus_library
    <set>
      <if test="sortOrder != null">
        sort_order = #{sortOrder,jdbcType=INTEGER},
      </if>
      <if test="prevCorpusId != null">
        prev_corpus_id = #{prevCorpusId,jdbcType=BIGINT},
      </if>
      <if test="resourceId != null">
        resource_id = #{resourceId,jdbcType=BIGINT},
      </if>
      <if test="resourceChannel != null">
        resource_channel = #{resourceChannel,jdbcType=VARCHAR},
      </if>
      <if test="resourceUri != null">
        resource_uri = #{resourceUri,jdbcType=VARCHAR},
      </if>
      <if test="corpusType != null">
        corpus_type = #{corpusType,jdbcType=INTEGER},
      </if>
      <if test="isContentQuantified != null">
        is_content_quantified = #{isContentQuantified,jdbcType=BIT},
      </if>
      <if test="priority != null">
        priority = #{priority,jdbcType=INTEGER},
      </if>
      <if test="confidence != null">
        confidence = #{confidence,jdbcType=REAL},
      </if>
      <if test="publishTime != null">
        publish_time = #{publishTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createdTime != null">
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedTime != null">
        updated_time = #{updatedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtShopId != null">
        mt_shop_id = #{mtShopId,jdbcType=BIGINT},
      </if>
      <if test="corpusSummary != null">
        corpus_summary = #{corpusSummary,jdbcType=LONGVARCHAR},
      </if>
      <if test="corpusContent != null">
        corpus_content = #{corpusContent,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.sankuai.dzhealth.ai.service.infrastructure.dal.entity.CorpusLibraryDOWithBLOBs">
    update corpus_library
    set sort_order = #{sortOrder,jdbcType=INTEGER},
      prev_corpus_id = #{prevCorpusId,jdbcType=BIGINT},
      resource_id = #{resourceId,jdbcType=BIGINT},
      resource_channel = #{resourceChannel,jdbcType=VARCHAR},
      resource_uri = #{resourceUri,jdbcType=VARCHAR},
      corpus_type = #{corpusType,jdbcType=INTEGER},
      is_content_quantified = #{isContentQuantified,jdbcType=BIT},
      priority = #{priority,jdbcType=INTEGER},
      confidence = #{confidence,jdbcType=REAL},
      publish_time = #{publishTime,jdbcType=TIMESTAMP},
      created_time = #{createdTime,jdbcType=TIMESTAMP},
      updated_time = #{updatedTime,jdbcType=TIMESTAMP},
      mt_shop_id = #{mtShopId,jdbcType=BIGINT},
      corpus_summary = #{corpusSummary,jdbcType=LONGVARCHAR},
      corpus_content = #{corpusContent,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.dzhealth.ai.service.infrastructure.dal.entity.CorpusLibraryDO">
    update corpus_library
    set sort_order = #{sortOrder,jdbcType=INTEGER},
      prev_corpus_id = #{prevCorpusId,jdbcType=BIGINT},
      resource_id = #{resourceId,jdbcType=BIGINT},
      resource_channel = #{resourceChannel,jdbcType=VARCHAR},
      resource_uri = #{resourceUri,jdbcType=VARCHAR},
      corpus_type = #{corpusType,jdbcType=INTEGER},
      is_content_quantified = #{isContentQuantified,jdbcType=BIT},
      priority = #{priority,jdbcType=INTEGER},
      confidence = #{confidence,jdbcType=REAL},
      publish_time = #{publishTime,jdbcType=TIMESTAMP},
      created_time = #{createdTime,jdbcType=TIMESTAMP},
      updated_time = #{updatedTime,jdbcType=TIMESTAMP},
      mt_shop_id = #{mtShopId,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>