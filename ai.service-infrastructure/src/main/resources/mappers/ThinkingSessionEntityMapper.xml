<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.dzhealth.ai.service.infrastructure.dal.mapper.ThinkingSessionEntityMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.dzhealth.ai.service.infrastructure.dal.entity.ThinkingSessionEntity">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="query" jdbcType="VARCHAR" property="query" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="search_results" jdbcType="CHAR" property="searchResults" />
    <result column="search_config" jdbcType="CHAR" property="searchConfig" />
    <result column="total_planned_steps" jdbcType="BIGINT" property="totalPlannedSteps" />
    <result column="actual_steps" jdbcType="BIGINT" property="actualSteps" />
    <result column="start_time" jdbcType="TIMESTAMP" property="startTime" />
    <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, query, status, search_results, search_config, total_planned_steps, actual_steps, 
    start_time, end_time, add_time, update_time
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.dzhealth.ai.service.infrastructure.dal.example.ThinkingSessionEntityExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from thinking_sessions
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from thinking_sessions
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from thinking_sessions
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.dzhealth.ai.service.infrastructure.dal.example.ThinkingSessionEntityExample">
    delete from thinking_sessions
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sankuai.dzhealth.ai.service.infrastructure.dal.entity.ThinkingSessionEntity">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into thinking_sessions (query, status, search_results, 
      search_config, total_planned_steps, actual_steps, 
      start_time, end_time, add_time, 
      update_time)
    values (#{query,jdbcType=VARCHAR}, #{status,jdbcType=VARCHAR}, #{searchResults,jdbcType=CHAR}, 
      #{searchConfig,jdbcType=CHAR}, #{totalPlannedSteps,jdbcType=BIGINT}, #{actualSteps,jdbcType=BIGINT}, 
      #{startTime,jdbcType=TIMESTAMP}, #{endTime,jdbcType=TIMESTAMP}, #{addTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.sankuai.dzhealth.ai.service.infrastructure.dal.entity.ThinkingSessionEntity">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into thinking_sessions
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="query != null">
        query,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="searchResults != null">
        search_results,
      </if>
      <if test="searchConfig != null">
        search_config,
      </if>
      <if test="totalPlannedSteps != null">
        total_planned_steps,
      </if>
      <if test="actualSteps != null">
        actual_steps,
      </if>
      <if test="startTime != null">
        start_time,
      </if>
      <if test="endTime != null">
        end_time,
      </if>
      <if test="addTime != null">
        add_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="query != null">
        #{query,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="searchResults != null">
        #{searchResults,jdbcType=CHAR},
      </if>
      <if test="searchConfig != null">
        #{searchConfig,jdbcType=CHAR},
      </if>
      <if test="totalPlannedSteps != null">
        #{totalPlannedSteps,jdbcType=BIGINT},
      </if>
      <if test="actualSteps != null">
        #{actualSteps,jdbcType=BIGINT},
      </if>
      <if test="startTime != null">
        #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.dzhealth.ai.service.infrastructure.dal.example.ThinkingSessionEntityExample" resultType="java.lang.Long">
    select count(*) from thinking_sessions
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update thinking_sessions
    <set>
      <if test="row.id != null">
        id = #{row.id,jdbcType=BIGINT},
      </if>
      <if test="row.query != null">
        query = #{row.query,jdbcType=VARCHAR},
      </if>
      <if test="row.status != null">
        status = #{row.status,jdbcType=VARCHAR},
      </if>
      <if test="row.searchResults != null">
        search_results = #{row.searchResults,jdbcType=CHAR},
      </if>
      <if test="row.searchConfig != null">
        search_config = #{row.searchConfig,jdbcType=CHAR},
      </if>
      <if test="row.totalPlannedSteps != null">
        total_planned_steps = #{row.totalPlannedSteps,jdbcType=BIGINT},
      </if>
      <if test="row.actualSteps != null">
        actual_steps = #{row.actualSteps,jdbcType=BIGINT},
      </if>
      <if test="row.startTime != null">
        start_time = #{row.startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.endTime != null">
        end_time = #{row.endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.addTime != null">
        add_time = #{row.addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.updateTime != null">
        update_time = #{row.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update thinking_sessions
    set id = #{row.id,jdbcType=BIGINT},
      query = #{row.query,jdbcType=VARCHAR},
      status = #{row.status,jdbcType=VARCHAR},
      search_results = #{row.searchResults,jdbcType=CHAR},
      search_config = #{row.searchConfig,jdbcType=CHAR},
      total_planned_steps = #{row.totalPlannedSteps,jdbcType=BIGINT},
      actual_steps = #{row.actualSteps,jdbcType=BIGINT},
      start_time = #{row.startTime,jdbcType=TIMESTAMP},
      end_time = #{row.endTime,jdbcType=TIMESTAMP},
      add_time = #{row.addTime,jdbcType=TIMESTAMP},
      update_time = #{row.updateTime,jdbcType=TIMESTAMP}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.dzhealth.ai.service.infrastructure.dal.entity.ThinkingSessionEntity">
    update thinking_sessions
    <set>
      <if test="query != null">
        query = #{query,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=VARCHAR},
      </if>
      <if test="searchResults != null">
        search_results = #{searchResults,jdbcType=CHAR},
      </if>
      <if test="searchConfig != null">
        search_config = #{searchConfig,jdbcType=CHAR},
      </if>
      <if test="totalPlannedSteps != null">
        total_planned_steps = #{totalPlannedSteps,jdbcType=BIGINT},
      </if>
      <if test="actualSteps != null">
        actual_steps = #{actualSteps,jdbcType=BIGINT},
      </if>
      <if test="startTime != null">
        start_time = #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        end_time = #{endTime,jdbcType=TIMESTAMP},
      </if>
      <if test="addTime != null">
        add_time = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.dzhealth.ai.service.infrastructure.dal.entity.ThinkingSessionEntity">
    update thinking_sessions
    set query = #{query,jdbcType=VARCHAR},
      status = #{status,jdbcType=VARCHAR},
      search_results = #{searchResults,jdbcType=CHAR},
      search_config = #{searchConfig,jdbcType=CHAR},
      total_planned_steps = #{totalPlannedSteps,jdbcType=BIGINT},
      actual_steps = #{actualSteps,jdbcType=BIGINT},
      start_time = #{startTime,jdbcType=TIMESTAMP},
      end_time = #{endTime,jdbcType=TIMESTAMP},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>