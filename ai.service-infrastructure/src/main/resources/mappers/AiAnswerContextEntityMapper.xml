<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.dzhealth.ai.service.infrastructure.dal.mapper.AiAnswerContextEntityMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.dzhealth.ai.service.infrastructure.dal.entity.AiAnswerContextEntity">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="userId" jdbcType="VARCHAR" property="userid" />
    <result column="lat" jdbcType="DOUBLE" property="lat" />
    <result column="lng" jdbcType="DOUBLE" property="lng" />
    <result column="clientType" jdbcType="VARCHAR" property="clienttype" />
    <result column="uuid" jdbcType="VARCHAR" property="uuid" />
    <result column="appVersion" jdbcType="VARCHAR" property="appversion" />
    <result column="cityId" jdbcType="INTEGER" property="cityid" />
    <result column="sessionId" jdbcType="BIGINT" property="sessionid" />
    <result column="msgId" jdbcType="BIGINT" property="msgid" />
    <result column="requestId" jdbcType="VARCHAR" property="requestid" />
    <result column="extension" jdbcType="CHAR" property="extension" />
    <result column="bizScene" jdbcType="VARCHAR" property="bizscene" />
    <result column="requestTime" jdbcType="VARCHAR" property="requesttime" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="role" jdbcType="VARCHAR" property="role" />
    <result column="msgContext" jdbcType="CHAR" property="msgcontext" />
    <result column="shopId" jdbcType="BIGINT" property="shopid" />
    <result column="mtShopId" jdbcType="BIGINT" property="mtshopid" />
    <result column="stream" jdbcType="BIT" property="stream" />
    <result column="platform" jdbcType="INTEGER" property="platform" />
    <result column="conversationIdStr" jdbcType="VARCHAR" property="conversationidstr" />
    <result column="messageIdStr" jdbcType="VARCHAR" property="messageidstr" />
    <result column="traceId" jdbcType="VARCHAR" property="traceid" />
    <result column="spans" jdbcType="CHAR" property="spans" />
    <result column="businessType" jdbcType="VARCHAR" property="businesstype" />
    <result column="businessId" jdbcType="VARCHAR" property="businessid" />
    <result column="departName2IdMap" jdbcType="CHAR" property="departname2idmap" />
    <result column="stdDepartName2IdMap" jdbcType="CHAR" property="stddepartname2idmap" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="searchInfo" jdbcType="CHAR" property="searchinfo" />
    <result column="ragInfo" jdbcType="CHAR" property="raginfo" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.sankuai.dzhealth.ai.service.infrastructure.dal.entity.AiAnswerContextEntityWithBLOBs">
    <result column="content" jdbcType="LONGVARCHAR" property="content" />
    <result column="history" jdbcType="LONGVARCHAR" property="history" />
    <result column="assistantVisibleContent" jdbcType="LONGVARCHAR" property="assistantvisiblecontent" />
    <result column="assistantContent" jdbcType="LONGVARCHAR" property="assistantcontent" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, userId, lat, lng, clientType, uuid, appVersion, cityId, sessionId, msgId, requestId, 
    extension, bizScene, requestTime, type, role, msgContext, shopId, mtShopId, stream, 
    platform, conversationIdStr, messageIdStr, traceId, spans, businessType, businessId, 
    departName2IdMap, stdDepartName2IdMap, status, searchInfo, ragInfo, created_at, updated_at
  </sql>
  <sql id="Blob_Column_List">
    content, history, assistantVisibleContent, assistantContent
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.sankuai.dzhealth.ai.service.infrastructure.dal.example.AiAnswerContextEntityExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from answer_context
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="com.sankuai.dzhealth.ai.service.infrastructure.dal.example.AiAnswerContextEntityExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from answer_context
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from answer_context
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from answer_context
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.dzhealth.ai.service.infrastructure.dal.example.AiAnswerContextEntityExample">
    delete from answer_context
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sankuai.dzhealth.ai.service.infrastructure.dal.entity.AiAnswerContextEntityWithBLOBs">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into answer_context (userId, lat, lng, 
      clientType, uuid, appVersion, 
      cityId, sessionId, msgId, 
      requestId, extension, bizScene, 
      requestTime, type, role, 
      msgContext, shopId, mtShopId, 
      stream, platform, conversationIdStr, 
      messageIdStr, traceId, spans, 
      businessType, businessId, departName2IdMap, 
      stdDepartName2IdMap, status, searchInfo, 
      ragInfo, created_at, updated_at, 
      content, history, assistantVisibleContent, 
      assistantContent)
    values (#{userid,jdbcType=VARCHAR}, #{lat,jdbcType=DOUBLE}, #{lng,jdbcType=DOUBLE}, 
      #{clienttype,jdbcType=VARCHAR}, #{uuid,jdbcType=VARCHAR}, #{appversion,jdbcType=VARCHAR}, 
      #{cityid,jdbcType=INTEGER}, #{sessionid,jdbcType=BIGINT}, #{msgid,jdbcType=BIGINT}, 
      #{requestid,jdbcType=VARCHAR}, #{extension,jdbcType=CHAR}, #{bizscene,jdbcType=VARCHAR}, 
      #{requesttime,jdbcType=VARCHAR}, #{type,jdbcType=INTEGER}, #{role,jdbcType=VARCHAR}, 
      #{msgcontext,jdbcType=CHAR}, #{shopid,jdbcType=BIGINT}, #{mtshopid,jdbcType=BIGINT}, 
      #{stream,jdbcType=BIT}, #{platform,jdbcType=INTEGER}, #{conversationidstr,jdbcType=VARCHAR}, 
      #{messageidstr,jdbcType=VARCHAR}, #{traceid,jdbcType=VARCHAR}, #{spans,jdbcType=CHAR}, 
      #{businesstype,jdbcType=VARCHAR}, #{businessid,jdbcType=VARCHAR}, #{departname2idmap,jdbcType=CHAR}, 
      #{stddepartname2idmap,jdbcType=CHAR}, #{status,jdbcType=VARCHAR}, #{searchinfo,jdbcType=CHAR}, 
      #{raginfo,jdbcType=CHAR}, #{createdAt,jdbcType=TIMESTAMP}, #{updatedAt,jdbcType=TIMESTAMP}, 
      #{content,jdbcType=LONGVARCHAR}, #{history,jdbcType=LONGVARCHAR}, #{assistantvisiblecontent,jdbcType=LONGVARCHAR}, 
      #{assistantcontent,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.sankuai.dzhealth.ai.service.infrastructure.dal.entity.AiAnswerContextEntityWithBLOBs">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into answer_context
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="userid != null">
        userId,
      </if>
      <if test="lat != null">
        lat,
      </if>
      <if test="lng != null">
        lng,
      </if>
      <if test="clienttype != null">
        clientType,
      </if>
      <if test="uuid != null">
        uuid,
      </if>
      <if test="appversion != null">
        appVersion,
      </if>
      <if test="cityid != null">
        cityId,
      </if>
      <if test="sessionid != null">
        sessionId,
      </if>
      <if test="msgid != null">
        msgId,
      </if>
      <if test="requestid != null">
        requestId,
      </if>
      <if test="extension != null">
        extension,
      </if>
      <if test="bizscene != null">
        bizScene,
      </if>
      <if test="requesttime != null">
        requestTime,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="role != null">
        role,
      </if>
      <if test="msgcontext != null">
        msgContext,
      </if>
      <if test="shopid != null">
        shopId,
      </if>
      <if test="mtshopid != null">
        mtShopId,
      </if>
      <if test="stream != null">
        stream,
      </if>
      <if test="platform != null">
        platform,
      </if>
      <if test="conversationidstr != null">
        conversationIdStr,
      </if>
      <if test="messageidstr != null">
        messageIdStr,
      </if>
      <if test="traceid != null">
        traceId,
      </if>
      <if test="spans != null">
        spans,
      </if>
      <if test="businesstype != null">
        businessType,
      </if>
      <if test="businessid != null">
        businessId,
      </if>
      <if test="departname2idmap != null">
        departName2IdMap,
      </if>
      <if test="stddepartname2idmap != null">
        stdDepartName2IdMap,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="searchinfo != null">
        searchInfo,
      </if>
      <if test="raginfo != null">
        ragInfo,
      </if>
      <if test="createdAt != null">
        created_at,
      </if>
      <if test="updatedAt != null">
        updated_at,
      </if>
      <if test="content != null">
        content,
      </if>
      <if test="history != null">
        history,
      </if>
      <if test="assistantvisiblecontent != null">
        assistantVisibleContent,
      </if>
      <if test="assistantcontent != null">
        assistantContent,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="userid != null">
        #{userid,jdbcType=VARCHAR},
      </if>
      <if test="lat != null">
        #{lat,jdbcType=DOUBLE},
      </if>
      <if test="lng != null">
        #{lng,jdbcType=DOUBLE},
      </if>
      <if test="clienttype != null">
        #{clienttype,jdbcType=VARCHAR},
      </if>
      <if test="uuid != null">
        #{uuid,jdbcType=VARCHAR},
      </if>
      <if test="appversion != null">
        #{appversion,jdbcType=VARCHAR},
      </if>
      <if test="cityid != null">
        #{cityid,jdbcType=INTEGER},
      </if>
      <if test="sessionid != null">
        #{sessionid,jdbcType=BIGINT},
      </if>
      <if test="msgid != null">
        #{msgid,jdbcType=BIGINT},
      </if>
      <if test="requestid != null">
        #{requestid,jdbcType=VARCHAR},
      </if>
      <if test="extension != null">
        #{extension,jdbcType=CHAR},
      </if>
      <if test="bizscene != null">
        #{bizscene,jdbcType=VARCHAR},
      </if>
      <if test="requesttime != null">
        #{requesttime,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="role != null">
        #{role,jdbcType=VARCHAR},
      </if>
      <if test="msgcontext != null">
        #{msgcontext,jdbcType=CHAR},
      </if>
      <if test="shopid != null">
        #{shopid,jdbcType=BIGINT},
      </if>
      <if test="mtshopid != null">
        #{mtshopid,jdbcType=BIGINT},
      </if>
      <if test="stream != null">
        #{stream,jdbcType=BIT},
      </if>
      <if test="platform != null">
        #{platform,jdbcType=INTEGER},
      </if>
      <if test="conversationidstr != null">
        #{conversationidstr,jdbcType=VARCHAR},
      </if>
      <if test="messageidstr != null">
        #{messageidstr,jdbcType=VARCHAR},
      </if>
      <if test="traceid != null">
        #{traceid,jdbcType=VARCHAR},
      </if>
      <if test="spans != null">
        #{spans,jdbcType=CHAR},
      </if>
      <if test="businesstype != null">
        #{businesstype,jdbcType=VARCHAR},
      </if>
      <if test="businessid != null">
        #{businessid,jdbcType=VARCHAR},
      </if>
      <if test="departname2idmap != null">
        #{departname2idmap,jdbcType=CHAR},
      </if>
      <if test="stddepartname2idmap != null">
        #{stddepartname2idmap,jdbcType=CHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="searchinfo != null">
        #{searchinfo,jdbcType=CHAR},
      </if>
      <if test="raginfo != null">
        #{raginfo,jdbcType=CHAR},
      </if>
      <if test="createdAt != null">
        #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedAt != null">
        #{updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="content != null">
        #{content,jdbcType=LONGVARCHAR},
      </if>
      <if test="history != null">
        #{history,jdbcType=LONGVARCHAR},
      </if>
      <if test="assistantvisiblecontent != null">
        #{assistantvisiblecontent,jdbcType=LONGVARCHAR},
      </if>
      <if test="assistantcontent != null">
        #{assistantcontent,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.dzhealth.ai.service.infrastructure.dal.example.AiAnswerContextEntityExample" resultType="java.lang.Long">
    select count(*) from answer_context
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update answer_context
    <set>
      <if test="row.id != null">
        id = #{row.id,jdbcType=BIGINT},
      </if>
      <if test="row.userid != null">
        userId = #{row.userid,jdbcType=VARCHAR},
      </if>
      <if test="row.lat != null">
        lat = #{row.lat,jdbcType=DOUBLE},
      </if>
      <if test="row.lng != null">
        lng = #{row.lng,jdbcType=DOUBLE},
      </if>
      <if test="row.clienttype != null">
        clientType = #{row.clienttype,jdbcType=VARCHAR},
      </if>
      <if test="row.uuid != null">
        uuid = #{row.uuid,jdbcType=VARCHAR},
      </if>
      <if test="row.appversion != null">
        appVersion = #{row.appversion,jdbcType=VARCHAR},
      </if>
      <if test="row.cityid != null">
        cityId = #{row.cityid,jdbcType=INTEGER},
      </if>
      <if test="row.sessionid != null">
        sessionId = #{row.sessionid,jdbcType=BIGINT},
      </if>
      <if test="row.msgid != null">
        msgId = #{row.msgid,jdbcType=BIGINT},
      </if>
      <if test="row.requestid != null">
        requestId = #{row.requestid,jdbcType=VARCHAR},
      </if>
      <if test="row.extension != null">
        extension = #{row.extension,jdbcType=CHAR},
      </if>
      <if test="row.bizscene != null">
        bizScene = #{row.bizscene,jdbcType=VARCHAR},
      </if>
      <if test="row.requesttime != null">
        requestTime = #{row.requesttime,jdbcType=VARCHAR},
      </if>
      <if test="row.type != null">
        type = #{row.type,jdbcType=INTEGER},
      </if>
      <if test="row.role != null">
        role = #{row.role,jdbcType=VARCHAR},
      </if>
      <if test="row.msgcontext != null">
        msgContext = #{row.msgcontext,jdbcType=CHAR},
      </if>
      <if test="row.shopid != null">
        shopId = #{row.shopid,jdbcType=BIGINT},
      </if>
      <if test="row.mtshopid != null">
        mtShopId = #{row.mtshopid,jdbcType=BIGINT},
      </if>
      <if test="row.stream != null">
        stream = #{row.stream,jdbcType=BIT},
      </if>
      <if test="row.platform != null">
        platform = #{row.platform,jdbcType=INTEGER},
      </if>
      <if test="row.conversationidstr != null">
        conversationIdStr = #{row.conversationidstr,jdbcType=VARCHAR},
      </if>
      <if test="row.messageidstr != null">
        messageIdStr = #{row.messageidstr,jdbcType=VARCHAR},
      </if>
      <if test="row.traceid != null">
        traceId = #{row.traceid,jdbcType=VARCHAR},
      </if>
      <if test="row.spans != null">
        spans = #{row.spans,jdbcType=CHAR},
      </if>
      <if test="row.businesstype != null">
        businessType = #{row.businesstype,jdbcType=VARCHAR},
      </if>
      <if test="row.businessid != null">
        businessId = #{row.businessid,jdbcType=VARCHAR},
      </if>
      <if test="row.departname2idmap != null">
        departName2IdMap = #{row.departname2idmap,jdbcType=CHAR},
      </if>
      <if test="row.stddepartname2idmap != null">
        stdDepartName2IdMap = #{row.stddepartname2idmap,jdbcType=CHAR},
      </if>
      <if test="row.status != null">
        status = #{row.status,jdbcType=VARCHAR},
      </if>
      <if test="row.searchinfo != null">
        searchInfo = #{row.searchinfo,jdbcType=CHAR},
      </if>
      <if test="row.raginfo != null">
        ragInfo = #{row.raginfo,jdbcType=CHAR},
      </if>
      <if test="row.createdAt != null">
        created_at = #{row.createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="row.updatedAt != null">
        updated_at = #{row.updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="row.content != null">
        content = #{row.content,jdbcType=LONGVARCHAR},
      </if>
      <if test="row.history != null">
        history = #{row.history,jdbcType=LONGVARCHAR},
      </if>
      <if test="row.assistantvisiblecontent != null">
        assistantVisibleContent = #{row.assistantvisiblecontent,jdbcType=LONGVARCHAR},
      </if>
      <if test="row.assistantcontent != null">
        assistantContent = #{row.assistantcontent,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    update answer_context
    set id = #{row.id,jdbcType=BIGINT},
      userId = #{row.userid,jdbcType=VARCHAR},
      lat = #{row.lat,jdbcType=DOUBLE},
      lng = #{row.lng,jdbcType=DOUBLE},
      clientType = #{row.clienttype,jdbcType=VARCHAR},
      uuid = #{row.uuid,jdbcType=VARCHAR},
      appVersion = #{row.appversion,jdbcType=VARCHAR},
      cityId = #{row.cityid,jdbcType=INTEGER},
      sessionId = #{row.sessionid,jdbcType=BIGINT},
      msgId = #{row.msgid,jdbcType=BIGINT},
      requestId = #{row.requestid,jdbcType=VARCHAR},
      extension = #{row.extension,jdbcType=CHAR},
      bizScene = #{row.bizscene,jdbcType=VARCHAR},
      requestTime = #{row.requesttime,jdbcType=VARCHAR},
      type = #{row.type,jdbcType=INTEGER},
      role = #{row.role,jdbcType=VARCHAR},
      msgContext = #{row.msgcontext,jdbcType=CHAR},
      shopId = #{row.shopid,jdbcType=BIGINT},
      mtShopId = #{row.mtshopid,jdbcType=BIGINT},
      stream = #{row.stream,jdbcType=BIT},
      platform = #{row.platform,jdbcType=INTEGER},
      conversationIdStr = #{row.conversationidstr,jdbcType=VARCHAR},
      messageIdStr = #{row.messageidstr,jdbcType=VARCHAR},
      traceId = #{row.traceid,jdbcType=VARCHAR},
      spans = #{row.spans,jdbcType=CHAR},
      businessType = #{row.businesstype,jdbcType=VARCHAR},
      businessId = #{row.businessid,jdbcType=VARCHAR},
      departName2IdMap = #{row.departname2idmap,jdbcType=CHAR},
      stdDepartName2IdMap = #{row.stddepartname2idmap,jdbcType=CHAR},
      status = #{row.status,jdbcType=VARCHAR},
      searchInfo = #{row.searchinfo,jdbcType=CHAR},
      ragInfo = #{row.raginfo,jdbcType=CHAR},
      created_at = #{row.createdAt,jdbcType=TIMESTAMP},
      updated_at = #{row.updatedAt,jdbcType=TIMESTAMP},
      content = #{row.content,jdbcType=LONGVARCHAR},
      history = #{row.history,jdbcType=LONGVARCHAR},
      assistantVisibleContent = #{row.assistantvisiblecontent,jdbcType=LONGVARCHAR},
      assistantContent = #{row.assistantcontent,jdbcType=LONGVARCHAR}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update answer_context
    set id = #{row.id,jdbcType=BIGINT},
      userId = #{row.userid,jdbcType=VARCHAR},
      lat = #{row.lat,jdbcType=DOUBLE},
      lng = #{row.lng,jdbcType=DOUBLE},
      clientType = #{row.clienttype,jdbcType=VARCHAR},
      uuid = #{row.uuid,jdbcType=VARCHAR},
      appVersion = #{row.appversion,jdbcType=VARCHAR},
      cityId = #{row.cityid,jdbcType=INTEGER},
      sessionId = #{row.sessionid,jdbcType=BIGINT},
      msgId = #{row.msgid,jdbcType=BIGINT},
      requestId = #{row.requestid,jdbcType=VARCHAR},
      extension = #{row.extension,jdbcType=CHAR},
      bizScene = #{row.bizscene,jdbcType=VARCHAR},
      requestTime = #{row.requesttime,jdbcType=VARCHAR},
      type = #{row.type,jdbcType=INTEGER},
      role = #{row.role,jdbcType=VARCHAR},
      msgContext = #{row.msgcontext,jdbcType=CHAR},
      shopId = #{row.shopid,jdbcType=BIGINT},
      mtShopId = #{row.mtshopid,jdbcType=BIGINT},
      stream = #{row.stream,jdbcType=BIT},
      platform = #{row.platform,jdbcType=INTEGER},
      conversationIdStr = #{row.conversationidstr,jdbcType=VARCHAR},
      messageIdStr = #{row.messageidstr,jdbcType=VARCHAR},
      traceId = #{row.traceid,jdbcType=VARCHAR},
      spans = #{row.spans,jdbcType=CHAR},
      businessType = #{row.businesstype,jdbcType=VARCHAR},
      businessId = #{row.businessid,jdbcType=VARCHAR},
      departName2IdMap = #{row.departname2idmap,jdbcType=CHAR},
      stdDepartName2IdMap = #{row.stddepartname2idmap,jdbcType=CHAR},
      status = #{row.status,jdbcType=VARCHAR},
      searchInfo = #{row.searchinfo,jdbcType=CHAR},
      ragInfo = #{row.raginfo,jdbcType=CHAR},
      created_at = #{row.createdAt,jdbcType=TIMESTAMP},
      updated_at = #{row.updatedAt,jdbcType=TIMESTAMP}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.dzhealth.ai.service.infrastructure.dal.entity.AiAnswerContextEntityWithBLOBs">
    update answer_context
    <set>
      <if test="userid != null">
        userId = #{userid,jdbcType=VARCHAR},
      </if>
      <if test="lat != null">
        lat = #{lat,jdbcType=DOUBLE},
      </if>
      <if test="lng != null">
        lng = #{lng,jdbcType=DOUBLE},
      </if>
      <if test="clienttype != null">
        clientType = #{clienttype,jdbcType=VARCHAR},
      </if>
      <if test="uuid != null">
        uuid = #{uuid,jdbcType=VARCHAR},
      </if>
      <if test="appversion != null">
        appVersion = #{appversion,jdbcType=VARCHAR},
      </if>
      <if test="cityid != null">
        cityId = #{cityid,jdbcType=INTEGER},
      </if>
      <if test="sessionid != null">
        sessionId = #{sessionid,jdbcType=BIGINT},
      </if>
      <if test="msgid != null">
        msgId = #{msgid,jdbcType=BIGINT},
      </if>
      <if test="requestid != null">
        requestId = #{requestid,jdbcType=VARCHAR},
      </if>
      <if test="extension != null">
        extension = #{extension,jdbcType=CHAR},
      </if>
      <if test="bizscene != null">
        bizScene = #{bizscene,jdbcType=VARCHAR},
      </if>
      <if test="requesttime != null">
        requestTime = #{requesttime,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=INTEGER},
      </if>
      <if test="role != null">
        role = #{role,jdbcType=VARCHAR},
      </if>
      <if test="msgcontext != null">
        msgContext = #{msgcontext,jdbcType=CHAR},
      </if>
      <if test="shopid != null">
        shopId = #{shopid,jdbcType=BIGINT},
      </if>
      <if test="mtshopid != null">
        mtShopId = #{mtshopid,jdbcType=BIGINT},
      </if>
      <if test="stream != null">
        stream = #{stream,jdbcType=BIT},
      </if>
      <if test="platform != null">
        platform = #{platform,jdbcType=INTEGER},
      </if>
      <if test="conversationidstr != null">
        conversationIdStr = #{conversationidstr,jdbcType=VARCHAR},
      </if>
      <if test="messageidstr != null">
        messageIdStr = #{messageidstr,jdbcType=VARCHAR},
      </if>
      <if test="traceid != null">
        traceId = #{traceid,jdbcType=VARCHAR},
      </if>
      <if test="spans != null">
        spans = #{spans,jdbcType=CHAR},
      </if>
      <if test="businesstype != null">
        businessType = #{businesstype,jdbcType=VARCHAR},
      </if>
      <if test="businessid != null">
        businessId = #{businessid,jdbcType=VARCHAR},
      </if>
      <if test="departname2idmap != null">
        departName2IdMap = #{departname2idmap,jdbcType=CHAR},
      </if>
      <if test="stddepartname2idmap != null">
        stdDepartName2IdMap = #{stddepartname2idmap,jdbcType=CHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=VARCHAR},
      </if>
      <if test="searchinfo != null">
        searchInfo = #{searchinfo,jdbcType=CHAR},
      </if>
      <if test="raginfo != null">
        ragInfo = #{raginfo,jdbcType=CHAR},
      </if>
      <if test="createdAt != null">
        created_at = #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedAt != null">
        updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="content != null">
        content = #{content,jdbcType=LONGVARCHAR},
      </if>
      <if test="history != null">
        history = #{history,jdbcType=LONGVARCHAR},
      </if>
      <if test="assistantvisiblecontent != null">
        assistantVisibleContent = #{assistantvisiblecontent,jdbcType=LONGVARCHAR},
      </if>
      <if test="assistantcontent != null">
        assistantContent = #{assistantcontent,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.sankuai.dzhealth.ai.service.infrastructure.dal.entity.AiAnswerContextEntityWithBLOBs">
    update answer_context
    set userId = #{userid,jdbcType=VARCHAR},
      lat = #{lat,jdbcType=DOUBLE},
      lng = #{lng,jdbcType=DOUBLE},
      clientType = #{clienttype,jdbcType=VARCHAR},
      uuid = #{uuid,jdbcType=VARCHAR},
      appVersion = #{appversion,jdbcType=VARCHAR},
      cityId = #{cityid,jdbcType=INTEGER},
      sessionId = #{sessionid,jdbcType=BIGINT},
      msgId = #{msgid,jdbcType=BIGINT},
      requestId = #{requestid,jdbcType=VARCHAR},
      extension = #{extension,jdbcType=CHAR},
      bizScene = #{bizscene,jdbcType=VARCHAR},
      requestTime = #{requesttime,jdbcType=VARCHAR},
      type = #{type,jdbcType=INTEGER},
      role = #{role,jdbcType=VARCHAR},
      msgContext = #{msgcontext,jdbcType=CHAR},
      shopId = #{shopid,jdbcType=BIGINT},
      mtShopId = #{mtshopid,jdbcType=BIGINT},
      stream = #{stream,jdbcType=BIT},
      platform = #{platform,jdbcType=INTEGER},
      conversationIdStr = #{conversationidstr,jdbcType=VARCHAR},
      messageIdStr = #{messageidstr,jdbcType=VARCHAR},
      traceId = #{traceid,jdbcType=VARCHAR},
      spans = #{spans,jdbcType=CHAR},
      businessType = #{businesstype,jdbcType=VARCHAR},
      businessId = #{businessid,jdbcType=VARCHAR},
      departName2IdMap = #{departname2idmap,jdbcType=CHAR},
      stdDepartName2IdMap = #{stddepartname2idmap,jdbcType=CHAR},
      status = #{status,jdbcType=VARCHAR},
      searchInfo = #{searchinfo,jdbcType=CHAR},
      ragInfo = #{raginfo,jdbcType=CHAR},
      created_at = #{createdAt,jdbcType=TIMESTAMP},
      updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      content = #{content,jdbcType=LONGVARCHAR},
      history = #{history,jdbcType=LONGVARCHAR},
      assistantVisibleContent = #{assistantvisiblecontent,jdbcType=LONGVARCHAR},
      assistantContent = #{assistantcontent,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.dzhealth.ai.service.infrastructure.dal.entity.AiAnswerContextEntity">
    update answer_context
    set userId = #{userid,jdbcType=VARCHAR},
      lat = #{lat,jdbcType=DOUBLE},
      lng = #{lng,jdbcType=DOUBLE},
      clientType = #{clienttype,jdbcType=VARCHAR},
      uuid = #{uuid,jdbcType=VARCHAR},
      appVersion = #{appversion,jdbcType=VARCHAR},
      cityId = #{cityid,jdbcType=INTEGER},
      sessionId = #{sessionid,jdbcType=BIGINT},
      msgId = #{msgid,jdbcType=BIGINT},
      requestId = #{requestid,jdbcType=VARCHAR},
      extension = #{extension,jdbcType=CHAR},
      bizScene = #{bizscene,jdbcType=VARCHAR},
      requestTime = #{requesttime,jdbcType=VARCHAR},
      type = #{type,jdbcType=INTEGER},
      role = #{role,jdbcType=VARCHAR},
      msgContext = #{msgcontext,jdbcType=CHAR},
      shopId = #{shopid,jdbcType=BIGINT},
      mtShopId = #{mtshopid,jdbcType=BIGINT},
      stream = #{stream,jdbcType=BIT},
      platform = #{platform,jdbcType=INTEGER},
      conversationIdStr = #{conversationidstr,jdbcType=VARCHAR},
      messageIdStr = #{messageidstr,jdbcType=VARCHAR},
      traceId = #{traceid,jdbcType=VARCHAR},
      spans = #{spans,jdbcType=CHAR},
      businessType = #{businesstype,jdbcType=VARCHAR},
      businessId = #{businessid,jdbcType=VARCHAR},
      departName2IdMap = #{departname2idmap,jdbcType=CHAR},
      stdDepartName2IdMap = #{stddepartname2idmap,jdbcType=CHAR},
      status = #{status,jdbcType=VARCHAR},
      searchInfo = #{searchinfo,jdbcType=CHAR},
      ragInfo = #{raginfo,jdbcType=CHAR},
      created_at = #{createdAt,jdbcType=TIMESTAMP},
      updated_at = #{updatedAt,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>