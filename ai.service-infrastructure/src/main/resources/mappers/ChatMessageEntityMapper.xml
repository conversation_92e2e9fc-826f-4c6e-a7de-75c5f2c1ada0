<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.dzhealth.ai.service.infrastructure.dal.mapper.ChatMessageEntityMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.dzhealth.ai.service.infrastructure.dal.entity.ChatMessageEntity">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="message_id" jdbcType="VARCHAR" property="messageId" />
    <result column="conversation_id" jdbcType="VARCHAR" property="conversationId" />
    <result column="role" jdbcType="VARCHAR" property="role" />
    <result column="sender_id" jdbcType="VARCHAR" property="senderId" />
    <result column="content_type" jdbcType="TINYINT" property="contentType" />
    <result column="ttft" jdbcType="BIGINT" property="ttft" />
    <result column="e2e_time" jdbcType="BIGINT" property="e2eTime" />
    <result column="parent_message_id" jdbcType="VARCHAR" property="parentMessageId" />
    <result column="sequence" jdbcType="BIGINT" property="sequence" />
    <result column="audit_status" jdbcType="TINYINT" property="auditStatus" />
    <result column="extra_data" jdbcType="CHAR" property="extraData" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.sankuai.dzhealth.ai.service.infrastructure.dal.entity.ChatMessageEntity">
    <result column="content" jdbcType="LONGVARCHAR" property="content" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, message_id, conversation_id, role, sender_id, content_type, ttft, e2e_time, parent_message_id,
    sequence, audit_status, extra_data, created_at
  </sql>
  <sql id="Blob_Column_List">
    content
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.sankuai.dzhealth.ai.service.infrastructure.dal.example.ChatMessageEntityExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from chat_message
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="com.sankuai.dzhealth.ai.service.infrastructure.dal.example.ChatMessageEntityExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from chat_message
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    select
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from chat_message
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from chat_message
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.dzhealth.ai.service.infrastructure.dal.example.ChatMessageEntityExample">
    delete from chat_message
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sankuai.dzhealth.ai.service.infrastructure.dal.entity.ChatMessageEntity">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into chat_message (message_id, conversation_id, role,
      sender_id, content_type, ttft,
      e2e_time, parent_message_id, sequence,
      audit_status, extra_data, created_at,
      content)
    values (#{messageId,jdbcType=VARCHAR}, #{conversationId,jdbcType=VARCHAR}, #{role,jdbcType=VARCHAR},
      #{senderId,jdbcType=VARCHAR}, #{contentType,jdbcType=TINYINT}, #{ttft,jdbcType=BIGINT},
      #{e2eTime,jdbcType=BIGINT}, #{parentMessageId,jdbcType=VARCHAR}, #{sequence,jdbcType=BIGINT},
      #{auditStatus,jdbcType=TINYINT}, #{extraData,jdbcType=CHAR}, #{createdAt,jdbcType=TIMESTAMP},
      #{content,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.sankuai.dzhealth.ai.service.infrastructure.dal.entity.ChatMessageEntity">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into chat_message
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="messageId != null">
        message_id,
      </if>
      <if test="conversationId != null">
        conversation_id,
      </if>
      <if test="role != null">
        role,
      </if>
      <if test="senderId != null">
        sender_id,
      </if>
      <if test="contentType != null">
        content_type,
      </if>
      <if test="ttft != null">
        ttft,
      </if>
      <if test="e2eTime != null">
        e2e_time,
      </if>
      <if test="parentMessageId != null">
        parent_message_id,
      </if>
      <if test="sequence != null">
        sequence,
      </if>
      <if test="auditStatus != null">
        audit_status,
      </if>
      <if test="extraData != null">
        extra_data,
      </if>
      <if test="createdAt != null">
        created_at,
      </if>
      <if test="content != null">
        content,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="messageId != null">
        #{messageId,jdbcType=VARCHAR},
      </if>
      <if test="conversationId != null">
        #{conversationId,jdbcType=VARCHAR},
      </if>
      <if test="role != null">
        #{role,jdbcType=VARCHAR},
      </if>
      <if test="senderId != null">
        #{senderId,jdbcType=VARCHAR},
      </if>
      <if test="contentType != null">
        #{contentType,jdbcType=TINYINT},
      </if>
      <if test="ttft != null">
        #{ttft,jdbcType=BIGINT},
      </if>
      <if test="e2eTime != null">
        #{e2eTime,jdbcType=BIGINT},
      </if>
      <if test="parentMessageId != null">
        #{parentMessageId,jdbcType=VARCHAR},
      </if>
      <if test="sequence != null">
        #{sequence,jdbcType=BIGINT},
      </if>
      <if test="auditStatus != null">
        #{auditStatus,jdbcType=TINYINT},
      </if>
      <if test="extraData != null">
        #{extraData,jdbcType=CHAR},
      </if>
      <if test="createdAt != null">
        #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="content != null">
        #{content,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.dzhealth.ai.service.infrastructure.dal.example.ChatMessageEntityExample" resultType="java.lang.Long">
    select count(*) from chat_message
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update chat_message
    <set>
      <if test="row.id != null">
        id = #{row.id,jdbcType=BIGINT},
      </if>
      <if test="row.messageId != null">
        message_id = #{row.messageId,jdbcType=VARCHAR},
      </if>
      <if test="row.conversationId != null">
        conversation_id = #{row.conversationId,jdbcType=VARCHAR},
      </if>
      <if test="row.role != null">
        role = #{row.role,jdbcType=VARCHAR},
      </if>
      <if test="row.senderId != null">
        sender_id = #{row.senderId,jdbcType=VARCHAR},
      </if>
      <if test="row.contentType != null">
        content_type = #{row.contentType,jdbcType=TINYINT},
      </if>
      <if test="row.ttft != null">
        ttft = #{row.ttft,jdbcType=BIGINT},
      </if>
      <if test="row.e2eTime != null">
        e2e_time = #{row.e2eTime,jdbcType=BIGINT},
      </if>
      <if test="row.parentMessageId != null">
        parent_message_id = #{row.parentMessageId,jdbcType=VARCHAR},
      </if>
      <if test="row.sequence != null">
        sequence = #{row.sequence,jdbcType=BIGINT},
      </if>
      <if test="row.auditStatus != null">
        audit_status = #{row.auditStatus,jdbcType=TINYINT},
      </if>
      <if test="row.extraData != null">
        extra_data = #{row.extraData,jdbcType=CHAR},
      </if>
      <if test="row.createdAt != null">
        created_at = #{row.createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="row.content != null">
        content = #{row.content,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    update chat_message
    set id = #{row.id,jdbcType=BIGINT},
      message_id = #{row.messageId,jdbcType=VARCHAR},
      conversation_id = #{row.conversationId,jdbcType=VARCHAR},
      role = #{row.role,jdbcType=VARCHAR},
      sender_id = #{row.senderId,jdbcType=VARCHAR},
      content_type = #{row.contentType,jdbcType=TINYINT},
      ttft = #{row.ttft,jdbcType=BIGINT},
      e2e_time = #{row.e2eTime,jdbcType=BIGINT},
      parent_message_id = #{row.parentMessageId,jdbcType=VARCHAR},
      sequence = #{row.sequence,jdbcType=BIGINT},
      audit_status = #{row.auditStatus,jdbcType=TINYINT},
      extra_data = #{row.extraData,jdbcType=CHAR},
      created_at = #{row.createdAt,jdbcType=TIMESTAMP},
      content = #{row.content,jdbcType=LONGVARCHAR}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update chat_message
    set id = #{row.id,jdbcType=BIGINT},
      message_id = #{row.messageId,jdbcType=VARCHAR},
      conversation_id = #{row.conversationId,jdbcType=VARCHAR},
      role = #{row.role,jdbcType=VARCHAR},
      sender_id = #{row.senderId,jdbcType=VARCHAR},
      content_type = #{row.contentType,jdbcType=TINYINT},
      ttft = #{row.ttft,jdbcType=BIGINT},
      e2e_time = #{row.e2eTime,jdbcType=BIGINT},
      parent_message_id = #{row.parentMessageId,jdbcType=VARCHAR},
      sequence = #{row.sequence,jdbcType=BIGINT},
      audit_status = #{row.auditStatus,jdbcType=TINYINT},
      extra_data = #{row.extraData,jdbcType=CHAR},
      created_at = #{row.createdAt,jdbcType=TIMESTAMP}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.dzhealth.ai.service.infrastructure.dal.entity.ChatMessageEntity">
    update chat_message
    <set>
      <if test="messageId != null">
        message_id = #{messageId,jdbcType=VARCHAR},
      </if>
      <if test="conversationId != null">
        conversation_id = #{conversationId,jdbcType=VARCHAR},
      </if>
      <if test="role != null">
        role = #{role,jdbcType=VARCHAR},
      </if>
      <if test="senderId != null">
        sender_id = #{senderId,jdbcType=VARCHAR},
      </if>
      <if test="contentType != null">
        content_type = #{contentType,jdbcType=TINYINT},
      </if>
      <if test="ttft != null">
        ttft = #{ttft,jdbcType=BIGINT},
      </if>
      <if test="e2eTime != null">
        e2e_time = #{e2eTime,jdbcType=BIGINT},
      </if>
      <if test="parentMessageId != null">
        parent_message_id = #{parentMessageId,jdbcType=VARCHAR},
      </if>
      <if test="sequence != null">
        sequence = #{sequence,jdbcType=BIGINT},
      </if>
      <if test="auditStatus != null">
        audit_status = #{auditStatus,jdbcType=TINYINT},
      </if>
      <if test="extraData != null">
        extra_data = #{extraData,jdbcType=CHAR},
      </if>
      <if test="createdAt != null">
        created_at = #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="content != null">
        content = #{content,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.sankuai.dzhealth.ai.service.infrastructure.dal.entity.ChatMessageEntity">
    update chat_message
    set message_id = #{messageId,jdbcType=VARCHAR},
      conversation_id = #{conversationId,jdbcType=VARCHAR},
      role = #{role,jdbcType=VARCHAR},
      sender_id = #{senderId,jdbcType=VARCHAR},
      content_type = #{contentType,jdbcType=TINYINT},
      ttft = #{ttft,jdbcType=BIGINT},
      e2e_time = #{e2eTime,jdbcType=BIGINT},
      parent_message_id = #{parentMessageId,jdbcType=VARCHAR},
      sequence = #{sequence,jdbcType=BIGINT},
      audit_status = #{auditStatus,jdbcType=TINYINT},
      extra_data = #{extraData,jdbcType=CHAR},
      created_at = #{createdAt,jdbcType=TIMESTAMP},
      content = #{content,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.dzhealth.ai.service.infrastructure.dal.entity.ChatMessageEntity">
    update chat_message
    set message_id = #{messageId,jdbcType=VARCHAR},
      conversation_id = #{conversationId,jdbcType=VARCHAR},
      role = #{role,jdbcType=VARCHAR},
      sender_id = #{senderId,jdbcType=VARCHAR},
      content_type = #{contentType,jdbcType=TINYINT},
      ttft = #{ttft,jdbcType=BIGINT},
      e2e_time = #{e2eTime,jdbcType=BIGINT},
      parent_message_id = #{parentMessageId,jdbcType=VARCHAR},
      sequence = #{sequence,jdbcType=BIGINT},
      audit_status = #{auditStatus,jdbcType=TINYINT},
      extra_data = #{extraData,jdbcType=CHAR},
      created_at = #{createdAt,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="selectLatestNByConversationId" resultMap="ResultMapWithBLOBs">
    SELECT * FROM (
        SELECT
        <include refid="Base_Column_List" />
        ,
        <include refid="Blob_Column_List" />
        FROM chat_message
        WHERE conversation_id = #{conversationId}
        ORDER BY sequence DESC
        LIMIT #{n}
    ) tmp
    ORDER BY sequence ASC
  </select>
  <select id="selectFirstUserMessageByConversationId" resultMap="ResultMapWithBLOBs">
    select
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from chat_message
    where conversation_id = #{conversationId}
    and role = 'user'
    order by sequence ASC
    limit 1
  </select>
  <sql id="Select_By_Example_With_Page_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>

  <select id="selectByExampleWithPage" resultMap="ResultMapWithBLOBs">
    select
    <if test="example.distinct">
        distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from chat_message
    <if test="example != null">
        <include refid="Select_By_Example_With_Page_Where_Clause" />
    </if>
    <if test="example.orderByClause != null">
        order by ${example.orderByClause}
    </if>
    limit #{offset}, #{limit}
  </select>
</mapper>