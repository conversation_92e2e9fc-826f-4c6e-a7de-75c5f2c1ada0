<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.dzhealth.ai.service.agent.infrastructure.dal.mapper.AppointmentInfoDOMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.dzhealth.ai.service.agent.infrastructure.dal.entity.AppointmentInfoDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="session_id" jdbcType="VARCHAR" property="sessionId" />
    <result column="msg_id" jdbcType="VARCHAR" property="msgId" />
    <result column="reserved_msg_id" jdbcType="VARCHAR" property="reservedMsgId" />
    <result column="product_name" jdbcType="INTEGER" property="productName" />
    <result column="appointment_start_time" jdbcType="TIMESTAMP" property="appointmentStartTime" />
    <result column="appointment_end_time" jdbcType="TIMESTAMP" property="appointmentEndTime" />
    <result column="position_txt" jdbcType="VARCHAR" property="positionTxt" />
    <result column="lng" jdbcType="DOUBLE" property="lng" />
    <result column="lat" jdbcType="DOUBLE" property="lat" />
    <result column="person_desc" jdbcType="VARCHAR" property="personDesc" />
    <result column="filter_items" jdbcType="VARCHAR" property="filterItems" />
    <result column="phone" jdbcType="VARCHAR" property="phone" />
    <result column="success_shop_id" jdbcType="BIGINT" property="successShopId" />
    <result column="task_id" jdbcType="BIGINT" property="taskId" />
    <result column="card_info" jdbcType="VARCHAR" property="cardInfo" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="lead_id" jdbcType="BIGINT" property="leadId" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="cityId" jdbcType="INTEGER" property="cityid" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.sankuai.dzhealth.ai.service.agent.infrastructure.dal.entity.AppointmentInfoDO">
    <result column="shop_id_list" jdbcType="LONGVARCHAR" property="shopIdList" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, user_id, session_id, msg_id, reserved_msg_id, product_name, appointment_start_time,
    appointment_end_time, position_txt, lng, lat, person_desc, filter_items, phone, success_shop_id,
    task_id, card_info, status, create_time, update_time, lead_id, remark, cityId
  </sql>
  <sql id="Blob_Column_List">
    shop_id_list
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.sankuai.dzhealth.ai.service.agent.infrastructure.dal.example.AppointmentInfoDOExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from appointment_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="com.sankuai.dzhealth.ai.service.agent.infrastructure.dal.example.AppointmentInfoDOExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from appointment_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    select
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from appointment_info
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from appointment_info
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.dzhealth.ai.service.agent.infrastructure.dal.example.AppointmentInfoDOExample">
    delete from appointment_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sankuai.dzhealth.ai.service.agent.infrastructure.dal.entity.AppointmentInfoDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into appointment_info (user_id, session_id, msg_id,
    reserved_msg_id, product_name, appointment_start_time,
    appointment_end_time, position_txt, lng,
    lat, person_desc, filter_items,
    phone, success_shop_id, task_id,
    card_info, status, create_time,
    update_time, lead_id, remark,
    cityId, shop_id_list)
    values (#{userId,jdbcType=BIGINT}, #{sessionId,jdbcType=VARCHAR}, #{msgId,jdbcType=VARCHAR},
    #{reservedMsgId,jdbcType=VARCHAR}, #{productName,jdbcType=INTEGER}, #{appointmentStartTime,jdbcType=TIMESTAMP},
    #{appointmentEndTime,jdbcType=TIMESTAMP}, #{positionTxt,jdbcType=VARCHAR}, #{lng,jdbcType=DOUBLE},
    #{lat,jdbcType=DOUBLE}, #{personDesc,jdbcType=VARCHAR}, #{filterItems,jdbcType=VARCHAR},
    #{phone,jdbcType=VARCHAR}, #{successShopId,jdbcType=BIGINT}, #{taskId,jdbcType=BIGINT},
    #{cardInfo,jdbcType=VARCHAR}, #{status,jdbcType=TINYINT}, #{createTime,jdbcType=TIMESTAMP},
    #{updateTime,jdbcType=TIMESTAMP}, #{leadId,jdbcType=BIGINT}, #{remark,jdbcType=VARCHAR},
    #{cityid,jdbcType=INTEGER}, #{shopIdList,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.sankuai.dzhealth.ai.service.agent.infrastructure.dal.entity.AppointmentInfoDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into appointment_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="userId != null">
        user_id,
      </if>
      <if test="sessionId != null">
        session_id,
      </if>
      <if test="msgId != null">
        msg_id,
      </if>
      <if test="reservedMsgId != null">
        reserved_msg_id,
      </if>
      <if test="productName != null">
        product_name,
      </if>
      <if test="appointmentStartTime != null">
        appointment_start_time,
      </if>
      <if test="appointmentEndTime != null">
        appointment_end_time,
      </if>
      <if test="positionTxt != null">
        position_txt,
      </if>
      <if test="lng != null">
        lng,
      </if>
      <if test="lat != null">
        lat,
      </if>
      <if test="personDesc != null">
        person_desc,
      </if>
      <if test="filterItems != null">
        filter_items,
      </if>
      <if test="phone != null">
        phone,
      </if>
      <if test="successShopId != null">
        success_shop_id,
      </if>
      <if test="taskId != null">
        task_id,
      </if>
      <if test="cardInfo != null">
        card_info,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="leadId != null">
        lead_id,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="cityid != null">
        cityId,
      </if>
      <if test="shopIdList != null">
        shop_id_list,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="userId != null">
        #{userId,jdbcType=BIGINT},
      </if>
      <if test="sessionId != null">
        #{sessionId,jdbcType=VARCHAR},
      </if>
      <if test="msgId != null">
        #{msgId,jdbcType=VARCHAR},
      </if>
      <if test="reservedMsgId != null">
        #{reservedMsgId,jdbcType=VARCHAR},
      </if>
      <if test="productName != null">
        #{productName,jdbcType=INTEGER},
      </if>
      <if test="appointmentStartTime != null">
        #{appointmentStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="appointmentEndTime != null">
        #{appointmentEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="positionTxt != null">
        #{positionTxt,jdbcType=VARCHAR},
      </if>
      <if test="lng != null">
        #{lng,jdbcType=DOUBLE},
      </if>
      <if test="lat != null">
        #{lat,jdbcType=DOUBLE},
      </if>
      <if test="personDesc != null">
        #{personDesc,jdbcType=VARCHAR},
      </if>
      <if test="filterItems != null">
        #{filterItems,jdbcType=VARCHAR},
      </if>
      <if test="phone != null">
        #{phone,jdbcType=VARCHAR},
      </if>
      <if test="successShopId != null">
        #{successShopId,jdbcType=BIGINT},
      </if>
      <if test="taskId != null">
        #{taskId,jdbcType=BIGINT},
      </if>
      <if test="cardInfo != null">
        #{cardInfo,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="leadId != null">
        #{leadId,jdbcType=BIGINT},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="cityid != null">
        #{cityid,jdbcType=INTEGER},
      </if>
      <if test="shopIdList != null">
        #{shopIdList,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.dzhealth.ai.service.agent.infrastructure.dal.example.AppointmentInfoDOExample" resultType="java.lang.Long">
    select count(*) from appointment_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update appointment_info
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.userId != null">
        user_id = #{record.userId,jdbcType=BIGINT},
      </if>
      <if test="record.sessionId != null">
        session_id = #{record.sessionId,jdbcType=VARCHAR},
      </if>
      <if test="record.msgId != null">
        msg_id = #{record.msgId,jdbcType=VARCHAR},
      </if>
      <if test="record.reservedMsgId != null">
        reserved_msg_id = #{record.reservedMsgId,jdbcType=VARCHAR},
      </if>
      <if test="record.productName != null">
        product_name = #{record.productName,jdbcType=INTEGER},
      </if>
      <if test="record.appointmentStartTime != null">
        appointment_start_time = #{record.appointmentStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.appointmentEndTime != null">
        appointment_end_time = #{record.appointmentEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.positionTxt != null">
        position_txt = #{record.positionTxt,jdbcType=VARCHAR},
      </if>
      <if test="record.lng != null">
        lng = #{record.lng,jdbcType=DOUBLE},
      </if>
      <if test="record.lat != null">
        lat = #{record.lat,jdbcType=DOUBLE},
      </if>
      <if test="record.personDesc != null">
        person_desc = #{record.personDesc,jdbcType=VARCHAR},
      </if>
      <if test="record.filterItems != null">
        filter_items = #{record.filterItems,jdbcType=VARCHAR},
      </if>
      <if test="record.phone != null">
        phone = #{record.phone,jdbcType=VARCHAR},
      </if>
      <if test="record.successShopId != null">
        success_shop_id = #{record.successShopId,jdbcType=BIGINT},
      </if>
      <if test="record.taskId != null">
        task_id = #{record.taskId,jdbcType=BIGINT},
      </if>
      <if test="record.cardInfo != null">
        card_info = #{record.cardInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.leadId != null">
        lead_id = #{record.leadId,jdbcType=BIGINT},
      </if>
      <if test="record.remark != null">
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.cityid != null">
        cityId = #{record.cityid,jdbcType=INTEGER},
      </if>
      <if test="record.shopIdList != null">
        shop_id_list = #{record.shopIdList,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    update appointment_info
    set id = #{record.id,jdbcType=BIGINT},
    user_id = #{record.userId,jdbcType=BIGINT},
    session_id = #{record.sessionId,jdbcType=VARCHAR},
    msg_id = #{record.msgId,jdbcType=VARCHAR},
    reserved_msg_id = #{record.reservedMsgId,jdbcType=VARCHAR},
    product_name = #{record.productName,jdbcType=INTEGER},
    appointment_start_time = #{record.appointmentStartTime,jdbcType=TIMESTAMP},
    appointment_end_time = #{record.appointmentEndTime,jdbcType=TIMESTAMP},
    position_txt = #{record.positionTxt,jdbcType=VARCHAR},
    lng = #{record.lng,jdbcType=DOUBLE},
    lat = #{record.lat,jdbcType=DOUBLE},
    person_desc = #{record.personDesc,jdbcType=VARCHAR},
    filter_items = #{record.filterItems,jdbcType=VARCHAR},
    phone = #{record.phone,jdbcType=VARCHAR},
    success_shop_id = #{record.successShopId,jdbcType=BIGINT},
    task_id = #{record.taskId,jdbcType=BIGINT},
    card_info = #{record.cardInfo,jdbcType=VARCHAR},
    status = #{record.status,jdbcType=TINYINT},
    create_time = #{record.createTime,jdbcType=TIMESTAMP},
    update_time = #{record.updateTime,jdbcType=TIMESTAMP},
    lead_id = #{record.leadId,jdbcType=BIGINT},
    remark = #{record.remark,jdbcType=VARCHAR},
    cityId = #{record.cityid,jdbcType=INTEGER},
    shop_id_list = #{record.shopIdList,jdbcType=LONGVARCHAR}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update appointment_info
    set id = #{record.id,jdbcType=BIGINT},
    user_id = #{record.userId,jdbcType=BIGINT},
    session_id = #{record.sessionId,jdbcType=VARCHAR},
    msg_id = #{record.msgId,jdbcType=VARCHAR},
    reserved_msg_id = #{record.reservedMsgId,jdbcType=VARCHAR},
    product_name = #{record.productName,jdbcType=INTEGER},
    appointment_start_time = #{record.appointmentStartTime,jdbcType=TIMESTAMP},
    appointment_end_time = #{record.appointmentEndTime,jdbcType=TIMESTAMP},
    position_txt = #{record.positionTxt,jdbcType=VARCHAR},
    lng = #{record.lng,jdbcType=DOUBLE},
    lat = #{record.lat,jdbcType=DOUBLE},
    person_desc = #{record.personDesc,jdbcType=VARCHAR},
    filter_items = #{record.filterItems,jdbcType=VARCHAR},
    phone = #{record.phone,jdbcType=VARCHAR},
    success_shop_id = #{record.successShopId,jdbcType=BIGINT},
    task_id = #{record.taskId,jdbcType=BIGINT},
    card_info = #{record.cardInfo,jdbcType=VARCHAR},
    status = #{record.status,jdbcType=TINYINT},
    create_time = #{record.createTime,jdbcType=TIMESTAMP},
    update_time = #{record.updateTime,jdbcType=TIMESTAMP},
    lead_id = #{record.leadId,jdbcType=BIGINT},
    remark = #{record.remark,jdbcType=VARCHAR},
    cityId = #{record.cityid,jdbcType=INTEGER}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.dzhealth.ai.service.agent.infrastructure.dal.entity.AppointmentInfoDO">
    update appointment_info
    <set>
      <if test="userId != null">
        user_id = #{userId,jdbcType=BIGINT},
      </if>
      <if test="sessionId != null">
        session_id = #{sessionId,jdbcType=VARCHAR},
      </if>
      <if test="msgId != null">
        msg_id = #{msgId,jdbcType=VARCHAR},
      </if>
      <if test="reservedMsgId != null">
        reserved_msg_id = #{reservedMsgId,jdbcType=VARCHAR},
      </if>
      <if test="productName != null">
        product_name = #{productName,jdbcType=INTEGER},
      </if>
      <if test="appointmentStartTime != null">
        appointment_start_time = #{appointmentStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="appointmentEndTime != null">
        appointment_end_time = #{appointmentEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="positionTxt != null">
        position_txt = #{positionTxt,jdbcType=VARCHAR},
      </if>
      <if test="lng != null">
        lng = #{lng,jdbcType=DOUBLE},
      </if>
      <if test="lat != null">
        lat = #{lat,jdbcType=DOUBLE},
      </if>
      <if test="personDesc != null">
        person_desc = #{personDesc,jdbcType=VARCHAR},
      </if>
      <if test="filterItems != null">
        filter_items = #{filterItems,jdbcType=VARCHAR},
      </if>
      <if test="phone != null">
        phone = #{phone,jdbcType=VARCHAR},
      </if>
      <if test="successShopId != null">
        success_shop_id = #{successShopId,jdbcType=BIGINT},
      </if>
      <if test="taskId != null">
        task_id = #{taskId,jdbcType=BIGINT},
      </if>
      <if test="cardInfo != null">
        card_info = #{cardInfo,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="leadId != null">
        lead_id = #{leadId,jdbcType=BIGINT},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="cityid != null">
        cityId = #{cityid,jdbcType=INTEGER},
      </if>
      <if test="shopIdList != null">
        shop_id_list = #{shopIdList,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.sankuai.dzhealth.ai.service.agent.infrastructure.dal.entity.AppointmentInfoDO">
    update appointment_info
    set user_id = #{userId,jdbcType=BIGINT},
        session_id = #{sessionId,jdbcType=VARCHAR},
        msg_id = #{msgId,jdbcType=VARCHAR},
        reserved_msg_id = #{reservedMsgId,jdbcType=VARCHAR},
        product_name = #{productName,jdbcType=INTEGER},
        appointment_start_time = #{appointmentStartTime,jdbcType=TIMESTAMP},
        appointment_end_time = #{appointmentEndTime,jdbcType=TIMESTAMP},
        position_txt = #{positionTxt,jdbcType=VARCHAR},
        lng = #{lng,jdbcType=DOUBLE},
        lat = #{lat,jdbcType=DOUBLE},
        person_desc = #{personDesc,jdbcType=VARCHAR},
        filter_items = #{filterItems,jdbcType=VARCHAR},
        phone = #{phone,jdbcType=VARCHAR},
        success_shop_id = #{successShopId,jdbcType=BIGINT},
        task_id = #{taskId,jdbcType=BIGINT},
        card_info = #{cardInfo,jdbcType=VARCHAR},
        status = #{status,jdbcType=TINYINT},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        update_time = #{updateTime,jdbcType=TIMESTAMP},
        lead_id = #{leadId,jdbcType=BIGINT},
        remark = #{remark,jdbcType=VARCHAR},
        cityId = #{cityid,jdbcType=INTEGER},
        shop_id_list = #{shopIdList,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.dzhealth.ai.service.agent.infrastructure.dal.entity.AppointmentInfoDO">
    update appointment_info
    set user_id = #{userId,jdbcType=BIGINT},
        session_id = #{sessionId,jdbcType=VARCHAR},
        msg_id = #{msgId,jdbcType=VARCHAR},
        reserved_msg_id = #{reservedMsgId,jdbcType=VARCHAR},
        product_name = #{productName,jdbcType=INTEGER},
        appointment_start_time = #{appointmentStartTime,jdbcType=TIMESTAMP},
        appointment_end_time = #{appointmentEndTime,jdbcType=TIMESTAMP},
        position_txt = #{positionTxt,jdbcType=VARCHAR},
        lng = #{lng,jdbcType=DOUBLE},
        lat = #{lat,jdbcType=DOUBLE},
        person_desc = #{personDesc,jdbcType=VARCHAR},
        filter_items = #{filterItems,jdbcType=VARCHAR},
        phone = #{phone,jdbcType=VARCHAR},
        success_shop_id = #{successShopId,jdbcType=BIGINT},
        task_id = #{taskId,jdbcType=BIGINT},
        card_info = #{cardInfo,jdbcType=VARCHAR},
        status = #{status,jdbcType=TINYINT},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        update_time = #{updateTime,jdbcType=TIMESTAMP},
        lead_id = #{leadId,jdbcType=BIGINT},
        remark = #{remark,jdbcType=VARCHAR},
        cityId = #{cityid,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>