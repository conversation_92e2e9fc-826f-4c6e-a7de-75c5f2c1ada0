<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.dzhealth.ai.service.infrastructure.dal.mapper.DecisionNodeEdgeEntityMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.dzhealth.ai.service.infrastructure.dal.entity.DecisionNodeEdgeDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="biz_scene" jdbcType="VARCHAR" property="bizScene" />
    <result column="edge_id" jdbcType="VARCHAR" property="edgeId" />
    <result column="parent_id" jdbcType="VARCHAR" property="parentId" />
    <result column="child_id" jdbcType="VARCHAR" property="childId" />
    <result column="edge_type" jdbcType="VARCHAR" property="edgeType" />
    <result column="edge_desc" jdbcType="VARCHAR" property="edgeDesc" />
    <result column="sort_order" jdbcType="BIGINT" property="sortOrder" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="ext" jdbcType="CHAR" property="ext" />
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, biz_scene, edge_id, parent_id, child_id, edge_type, edge_desc, sort_order, status, 
    ext, add_time, update_time
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.dzhealth.ai.service.infrastructure.dal.example.DecisionNodeEdgeEntityExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from decision_node_edge
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from decision_node_edge
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from decision_node_edge
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.dzhealth.ai.service.infrastructure.dal.example.DecisionNodeEdgeEntityExample">
    delete from decision_node_edge
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sankuai.dzhealth.ai.service.infrastructure.dal.entity.DecisionNodeEdgeDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into decision_node_edge (biz_scene, edge_id, parent_id, 
      child_id, edge_type, edge_desc, 
      sort_order, status, ext, 
      add_time, update_time)
    values (#{bizScene,jdbcType=VARCHAR}, #{edgeId,jdbcType=VARCHAR}, #{parentId,jdbcType=VARCHAR}, 
      #{childId,jdbcType=VARCHAR}, #{edgeType,jdbcType=VARCHAR}, #{edgeDesc,jdbcType=VARCHAR}, 
      #{sortOrder,jdbcType=BIGINT}, #{status,jdbcType=VARCHAR}, #{ext,jdbcType=CHAR}, 
      #{addTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.sankuai.dzhealth.ai.service.infrastructure.dal.entity.DecisionNodeEdgeDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into decision_node_edge
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="bizScene != null">
        biz_scene,
      </if>
      <if test="edgeId != null">
        edge_id,
      </if>
      <if test="parentId != null">
        parent_id,
      </if>
      <if test="childId != null">
        child_id,
      </if>
      <if test="edgeType != null">
        edge_type,
      </if>
      <if test="edgeDesc != null">
        edge_desc,
      </if>
      <if test="sortOrder != null">
        sort_order,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="ext != null">
        ext,
      </if>
      <if test="addTime != null">
        add_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="bizScene != null">
        #{bizScene,jdbcType=VARCHAR},
      </if>
      <if test="edgeId != null">
        #{edgeId,jdbcType=VARCHAR},
      </if>
      <if test="parentId != null">
        #{parentId,jdbcType=VARCHAR},
      </if>
      <if test="childId != null">
        #{childId,jdbcType=VARCHAR},
      </if>
      <if test="edgeType != null">
        #{edgeType,jdbcType=VARCHAR},
      </if>
      <if test="edgeDesc != null">
        #{edgeDesc,jdbcType=VARCHAR},
      </if>
      <if test="sortOrder != null">
        #{sortOrder,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="ext != null">
        #{ext,jdbcType=CHAR},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.dzhealth.ai.service.infrastructure.dal.example.DecisionNodeEdgeEntityExample" resultType="java.lang.Long">
    select count(*) from decision_node_edge
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update decision_node_edge
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.bizScene != null">
        biz_scene = #{record.bizScene,jdbcType=VARCHAR},
      </if>
      <if test="record.edgeId != null">
        edge_id = #{record.edgeId,jdbcType=VARCHAR},
      </if>
      <if test="record.parentId != null">
        parent_id = #{record.parentId,jdbcType=VARCHAR},
      </if>
      <if test="record.childId != null">
        child_id = #{record.childId,jdbcType=VARCHAR},
      </if>
      <if test="record.edgeType != null">
        edge_type = #{record.edgeType,jdbcType=VARCHAR},
      </if>
      <if test="record.edgeDesc != null">
        edge_desc = #{record.edgeDesc,jdbcType=VARCHAR},
      </if>
      <if test="record.sortOrder != null">
        sort_order = #{record.sortOrder,jdbcType=BIGINT},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=VARCHAR},
      </if>
      <if test="record.ext != null">
        ext = #{record.ext,jdbcType=CHAR},
      </if>
      <if test="record.addTime != null">
        add_time = #{record.addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update decision_node_edge
    set id = #{record.id,jdbcType=BIGINT},
      biz_scene = #{record.bizScene,jdbcType=VARCHAR},
      edge_id = #{record.edgeId,jdbcType=VARCHAR},
      parent_id = #{record.parentId,jdbcType=VARCHAR},
      child_id = #{record.childId,jdbcType=VARCHAR},
      edge_type = #{record.edgeType,jdbcType=VARCHAR},
      edge_desc = #{record.edgeDesc,jdbcType=VARCHAR},
      sort_order = #{record.sortOrder,jdbcType=BIGINT},
      status = #{record.status,jdbcType=VARCHAR},
      ext = #{record.ext,jdbcType=CHAR},
      add_time = #{record.addTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.dzhealth.ai.service.infrastructure.dal.entity.DecisionNodeEdgeDO">
    update decision_node_edge
    <set>
      <if test="bizScene != null">
        biz_scene = #{bizScene,jdbcType=VARCHAR},
      </if>
      <if test="edgeId != null">
        edge_id = #{edgeId,jdbcType=VARCHAR},
      </if>
      <if test="parentId != null">
        parent_id = #{parentId,jdbcType=VARCHAR},
      </if>
      <if test="childId != null">
        child_id = #{childId,jdbcType=VARCHAR},
      </if>
      <if test="edgeType != null">
        edge_type = #{edgeType,jdbcType=VARCHAR},
      </if>
      <if test="edgeDesc != null">
        edge_desc = #{edgeDesc,jdbcType=VARCHAR},
      </if>
      <if test="sortOrder != null">
        sort_order = #{sortOrder,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=VARCHAR},
      </if>
      <if test="ext != null">
        ext = #{ext,jdbcType=CHAR},
      </if>
      <if test="addTime != null">
        add_time = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.dzhealth.ai.service.infrastructure.dal.entity.DecisionNodeEdgeDO">
    update decision_node_edge
    set biz_scene = #{bizScene,jdbcType=VARCHAR},
      edge_id = #{edgeId,jdbcType=VARCHAR},
      parent_id = #{parentId,jdbcType=VARCHAR},
      child_id = #{childId,jdbcType=VARCHAR},
      edge_type = #{edgeType,jdbcType=VARCHAR},
      edge_desc = #{edgeDesc,jdbcType=VARCHAR},
      sort_order = #{sortOrder,jdbcType=BIGINT},
      status = #{status,jdbcType=VARCHAR},
      ext = #{ext,jdbcType=CHAR},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <!-- 批量插入边 -->
  <insert id="batchInsert" parameterType="java.util.List">
    <foreach collection="list" item="item" separator=";">
      insert into decision_node_edge
      <trim prefix="(" suffix=")" suffixOverrides=",">
        biz_scene,
        edge_id,
        <if test="item.parentId != null">parent_id,</if>
        <if test="item.childId != null">child_id,</if>
        <if test="item.edgeType != null">edge_type,</if>
        <if test="item.edgeDesc != null">edge_desc,</if>
        <if test="item.sortOrder != null">sort_order,</if>
        status,
        ext,
        add_time,
        update_time
      </trim>
      <trim prefix="values (" suffix=")" suffixOverrides=",">
        #{item.bizScene,jdbcType=VARCHAR},
        #{item.edgeId,jdbcType=VARCHAR},
        <if test="item.parentId != null">#{item.parentId,jdbcType=VARCHAR},</if>
        <if test="item.childId != null">#{item.childId,jdbcType=VARCHAR},</if>
        <if test="item.edgeType != null">#{item.edgeType,jdbcType=VARCHAR},</if>
        <if test="item.edgeDesc != null">#{item.edgeDesc,jdbcType=VARCHAR},</if>
        <if test="item.sortOrder != null">#{item.sortOrder,jdbcType=BIGINT},</if>
        #{item.status,jdbcType=VARCHAR},
        #{item.ext,jdbcType=CHAR},
        #{item.addTime,jdbcType=TIMESTAMP},
        #{item.updateTime,jdbcType=TIMESTAMP}
      </trim>
    </foreach>
  </insert>

  <!-- 批量更新边（根据edgeId） -->
  <!-- 使用CASE WHEN语句实现真正的批量更新 -->
  <update id="batchUpdateByEdgeId" parameterType="java.util.List">
    update decision_node_edge
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="biz_scene = case" suffix="end,">
        <foreach collection="list" item="item">
          <if test="item.bizScene != null">
            when edge_id = #{item.edgeId,jdbcType=VARCHAR} then #{item.bizScene,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="parent_id = case" suffix="end,">
        <foreach collection="list" item="item">
          <if test="item.parentId != null">
            when edge_id = #{item.edgeId,jdbcType=VARCHAR} then #{item.parentId,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="child_id = case" suffix="end,">
        <foreach collection="list" item="item">
          <if test="item.childId != null">
            when edge_id = #{item.edgeId,jdbcType=VARCHAR} then #{item.childId,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="edge_type = case" suffix="end,">
        <foreach collection="list" item="item">
          <if test="item.edgeType != null">
            when edge_id = #{item.edgeId,jdbcType=VARCHAR} then #{item.edgeType,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="edge_desc = case" suffix="end,">
        <foreach collection="list" item="item">
          <if test="item.edgeDesc != null">
            when edge_id = #{item.edgeId,jdbcType=VARCHAR} then #{item.edgeDesc,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="sort_order = case" suffix="end,">
        <foreach collection="list" item="item">
          <if test="item.sortOrder != null">
            when edge_id = #{item.edgeId,jdbcType=VARCHAR} then #{item.sortOrder,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="status = case" suffix="end,">
        <foreach collection="list" item="item">
          <if test="item.status != null">
            when edge_id = #{item.edgeId,jdbcType=VARCHAR} then #{item.status,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="ext = case" suffix="end,">
        <foreach collection="list" item="item">
          <if test="item.ext != null">
            when edge_id = #{item.edgeId,jdbcType=VARCHAR} then #{item.ext,jdbcType=CHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="add_time = case" suffix="end,">
        <foreach collection="list" item="item">
          <if test="item.addTime != null">
            when edge_id = #{item.edgeId,jdbcType=VARCHAR} then #{item.addTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="update_time = case" suffix="end,">
        <foreach collection="list" item="item">
          <if test="item.updateTime != null">
            when edge_id = #{item.edgeId,jdbcType=VARCHAR} then #{item.updateTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
    </trim>
    where edge_id in
    <foreach collection="list" item="item" open="(" close=")" separator=",">
      #{item.edgeId,jdbcType=VARCHAR}
    </foreach>
  </update>
</mapper>