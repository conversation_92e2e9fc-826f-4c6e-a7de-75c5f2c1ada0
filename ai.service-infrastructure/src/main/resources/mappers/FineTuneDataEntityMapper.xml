<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.dzhealth.ai.service.infrastructure.dal.mapper.FineTuneDataEntityMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.dzhealth.ai.service.infrastructure.dal.entity.FineTuneDataEntity">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="session_id" jdbcType="BIGINT" property="sessionId" />
    <result column="msg_id" jdbcType="BIGINT" property="msgId" />
    <result column="data_key" jdbcType="VARCHAR" property="dataKey" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="success" jdbcType="BIT" property="success" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.sankuai.dzhealth.ai.service.infrastructure.dal.entity.FineTuneDataEntityWithBLOBs">
    <result column="feedback" jdbcType="LONGVARCHAR" property="feedback" />
    <result column="metadata" jdbcType="LONGVARCHAR" property="metadata" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, session_id, msg_id, data_key, description, success, created_at, updated_at
  </sql>
  <sql id="Blob_Column_List">
    feedback, metadata
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.sankuai.dzhealth.ai.service.infrastructure.dal.example.FineTuneDataEntityExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from fine_tune_data
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="com.sankuai.dzhealth.ai.service.infrastructure.dal.example.FineTuneDataEntityExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from fine_tune_data
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from fine_tune_data
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from fine_tune_data
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.dzhealth.ai.service.infrastructure.dal.example.FineTuneDataEntityExample">
    delete from fine_tune_data
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sankuai.dzhealth.ai.service.infrastructure.dal.entity.FineTuneDataEntityWithBLOBs">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into fine_tune_data (session_id, msg_id, data_key, 
      description, success, created_at, 
      updated_at, feedback, metadata
      )
    values (#{sessionId,jdbcType=BIGINT}, #{msgId,jdbcType=BIGINT}, #{dataKey,jdbcType=VARCHAR}, 
      #{description,jdbcType=VARCHAR}, #{success,jdbcType=BIT}, #{createdAt,jdbcType=TIMESTAMP}, 
      #{updatedAt,jdbcType=TIMESTAMP}, #{feedback,jdbcType=LONGVARCHAR}, #{metadata,jdbcType=LONGVARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.sankuai.dzhealth.ai.service.infrastructure.dal.entity.FineTuneDataEntityWithBLOBs">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into fine_tune_data
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="sessionId != null">
        session_id,
      </if>
      <if test="msgId != null">
        msg_id,
      </if>
      <if test="dataKey != null">
        data_key,
      </if>
      <if test="description != null">
        description,
      </if>
      <if test="success != null">
        success,
      </if>
      <if test="createdAt != null">
        created_at,
      </if>
      <if test="updatedAt != null">
        updated_at,
      </if>
      <if test="feedback != null">
        feedback,
      </if>
      <if test="metadata != null">
        metadata,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="sessionId != null">
        #{sessionId,jdbcType=BIGINT},
      </if>
      <if test="msgId != null">
        #{msgId,jdbcType=BIGINT},
      </if>
      <if test="dataKey != null">
        #{dataKey,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        #{description,jdbcType=VARCHAR},
      </if>
      <if test="success != null">
        #{success,jdbcType=BIT},
      </if>
      <if test="createdAt != null">
        #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedAt != null">
        #{updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="feedback != null">
        #{feedback,jdbcType=LONGVARCHAR},
      </if>
      <if test="metadata != null">
        #{metadata,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.dzhealth.ai.service.infrastructure.dal.example.FineTuneDataEntityExample" resultType="java.lang.Long">
    select count(*) from fine_tune_data
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update fine_tune_data
    <set>
      <if test="row.id != null">
        id = #{row.id,jdbcType=BIGINT},
      </if>
      <if test="row.sessionId != null">
        session_id = #{row.sessionId,jdbcType=BIGINT},
      </if>
      <if test="row.msgId != null">
        msg_id = #{row.msgId,jdbcType=BIGINT},
      </if>
      <if test="row.dataKey != null">
        data_key = #{row.dataKey,jdbcType=VARCHAR},
      </if>
      <if test="row.description != null">
        description = #{row.description,jdbcType=VARCHAR},
      </if>
      <if test="row.success != null">
        success = #{row.success,jdbcType=BIT},
      </if>
      <if test="row.createdAt != null">
        created_at = #{row.createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="row.updatedAt != null">
        updated_at = #{row.updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="row.feedback != null">
        feedback = #{row.feedback,jdbcType=LONGVARCHAR},
      </if>
      <if test="row.metadata != null">
        metadata = #{row.metadata,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    update fine_tune_data
    set id = #{row.id,jdbcType=BIGINT},
      session_id = #{row.sessionId,jdbcType=BIGINT},
      msg_id = #{row.msgId,jdbcType=BIGINT},
      data_key = #{row.dataKey,jdbcType=VARCHAR},
      description = #{row.description,jdbcType=VARCHAR},
      success = #{row.success,jdbcType=BIT},
      created_at = #{row.createdAt,jdbcType=TIMESTAMP},
      updated_at = #{row.updatedAt,jdbcType=TIMESTAMP},
      feedback = #{row.feedback,jdbcType=LONGVARCHAR},
      metadata = #{row.metadata,jdbcType=LONGVARCHAR}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update fine_tune_data
    set id = #{row.id,jdbcType=BIGINT},
      session_id = #{row.sessionId,jdbcType=BIGINT},
      msg_id = #{row.msgId,jdbcType=BIGINT},
      data_key = #{row.dataKey,jdbcType=VARCHAR},
      description = #{row.description,jdbcType=VARCHAR},
      success = #{row.success,jdbcType=BIT},
      created_at = #{row.createdAt,jdbcType=TIMESTAMP},
      updated_at = #{row.updatedAt,jdbcType=TIMESTAMP}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.dzhealth.ai.service.infrastructure.dal.entity.FineTuneDataEntityWithBLOBs">
    update fine_tune_data
    <set>
      <if test="sessionId != null">
        session_id = #{sessionId,jdbcType=BIGINT},
      </if>
      <if test="msgId != null">
        msg_id = #{msgId,jdbcType=BIGINT},
      </if>
      <if test="dataKey != null">
        data_key = #{dataKey,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        description = #{description,jdbcType=VARCHAR},
      </if>
      <if test="success != null">
        success = #{success,jdbcType=BIT},
      </if>
      <if test="createdAt != null">
        created_at = #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedAt != null">
        updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="feedback != null">
        feedback = #{feedback,jdbcType=LONGVARCHAR},
      </if>
      <if test="metadata != null">
        metadata = #{metadata,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.sankuai.dzhealth.ai.service.infrastructure.dal.entity.FineTuneDataEntityWithBLOBs">
    update fine_tune_data
    set session_id = #{sessionId,jdbcType=BIGINT},
      msg_id = #{msgId,jdbcType=BIGINT},
      data_key = #{dataKey,jdbcType=VARCHAR},
      description = #{description,jdbcType=VARCHAR},
      success = #{success,jdbcType=BIT},
      created_at = #{createdAt,jdbcType=TIMESTAMP},
      updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      feedback = #{feedback,jdbcType=LONGVARCHAR},
      metadata = #{metadata,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.dzhealth.ai.service.infrastructure.dal.entity.FineTuneDataEntity">
    update fine_tune_data
    set session_id = #{sessionId,jdbcType=BIGINT},
      msg_id = #{msgId,jdbcType=BIGINT},
      data_key = #{dataKey,jdbcType=VARCHAR},
      description = #{description,jdbcType=VARCHAR},
      success = #{success,jdbcType=BIT},
      created_at = #{createdAt,jdbcType=TIMESTAMP},
      updated_at = #{updatedAt,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>