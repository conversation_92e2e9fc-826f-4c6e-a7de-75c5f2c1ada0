<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.dzhealth.ai.service.infrastructure.dal.mapper.RefinedKnowledgeDOMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.dzhealth.ai.service.infrastructure.dal.entity.RefinedKnowledgeDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="corpus_id" jdbcType="BIGINT" property="corpusId" />
    <result column="resource_id" jdbcType="BIGINT" property="resourceId" />
    <result column="resource_channel" jdbcType="VARCHAR" property="resourceChannel" />
    <result column="resource_uri" jdbcType="VARCHAR" property="resourceUri" />
    <result column="knowledge_type" jdbcType="INTEGER" property="knowledgeType" />
    <result column="is_success" jdbcType="BIT" property="isSuccess" />
    <result column="is_content_quantified" jdbcType="BIT" property="isContentQuantified" />
    <result column="priority" jdbcType="INTEGER" property="priority" />
    <result column="confidence" jdbcType="REAL" property="confidence" />
    <result column="publish_time" jdbcType="TIMESTAMP" property="publishTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="resource_content" jdbcType="VARCHAR" property="resourceContent" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.sankuai.dzhealth.ai.service.infrastructure.dal.entity.RefinedKnowledgeDOWithBLOBs">
    <result column="knowledge_summary" jdbcType="LONGVARCHAR" property="knowledgeSummary" />
    <result column="knowledge_content" jdbcType="LONGVARCHAR" property="knowledgeContent" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, corpus_id, resource_id, resource_channel, resource_uri, knowledge_type, is_success, 
    is_content_quantified, priority, confidence, publish_time, create_time, update_time, 
    resource_content
  </sql>
  <sql id="Blob_Column_List">
    knowledge_summary, knowledge_content
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.sankuai.dzhealth.ai.service.infrastructure.dal.example.RefinedKnowledgeDOExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from refined_knowledge
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="com.sankuai.dzhealth.ai.service.infrastructure.dal.example.RefinedKnowledgeDOExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from refined_knowledge
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from refined_knowledge
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from refined_knowledge
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.dzhealth.ai.service.infrastructure.dal.example.RefinedKnowledgeDOExample">
    delete from refined_knowledge
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sankuai.dzhealth.ai.service.infrastructure.dal.entity.RefinedKnowledgeDOWithBLOBs">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into refined_knowledge (corpus_id, resource_id, resource_channel, 
      resource_uri, knowledge_type, is_success, 
      is_content_quantified, priority, confidence, 
      publish_time, create_time, update_time, 
      resource_content, knowledge_summary, 
      knowledge_content)
    values (#{corpusId,jdbcType=BIGINT}, #{resourceId,jdbcType=BIGINT}, #{resourceChannel,jdbcType=VARCHAR}, 
      #{resourceUri,jdbcType=VARCHAR}, #{knowledgeType,jdbcType=INTEGER}, #{isSuccess,jdbcType=BIT}, 
      #{isContentQuantified,jdbcType=BIT}, #{priority,jdbcType=INTEGER}, #{confidence,jdbcType=REAL}, 
      #{publishTime,jdbcType=TIMESTAMP}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{resourceContent,jdbcType=VARCHAR}, #{knowledgeSummary,jdbcType=LONGVARCHAR}, 
      #{knowledgeContent,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.sankuai.dzhealth.ai.service.infrastructure.dal.entity.RefinedKnowledgeDOWithBLOBs">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into refined_knowledge
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="corpusId != null">
        corpus_id,
      </if>
      <if test="resourceId != null">
        resource_id,
      </if>
      <if test="resourceChannel != null">
        resource_channel,
      </if>
      <if test="resourceUri != null">
        resource_uri,
      </if>
      <if test="knowledgeType != null">
        knowledge_type,
      </if>
      <if test="isSuccess != null">
        is_success,
      </if>
      <if test="isContentQuantified != null">
        is_content_quantified,
      </if>
      <if test="priority != null">
        priority,
      </if>
      <if test="confidence != null">
        confidence,
      </if>
      <if test="publishTime != null">
        publish_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="resourceContent != null">
        resource_content,
      </if>
      <if test="knowledgeSummary != null">
        knowledge_summary,
      </if>
      <if test="knowledgeContent != null">
        knowledge_content,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="corpusId != null">
        #{corpusId,jdbcType=BIGINT},
      </if>
      <if test="resourceId != null">
        #{resourceId,jdbcType=BIGINT},
      </if>
      <if test="resourceChannel != null">
        #{resourceChannel,jdbcType=VARCHAR},
      </if>
      <if test="resourceUri != null">
        #{resourceUri,jdbcType=VARCHAR},
      </if>
      <if test="knowledgeType != null">
        #{knowledgeType,jdbcType=INTEGER},
      </if>
      <if test="isSuccess != null">
        #{isSuccess,jdbcType=BIT},
      </if>
      <if test="isContentQuantified != null">
        #{isContentQuantified,jdbcType=BIT},
      </if>
      <if test="priority != null">
        #{priority,jdbcType=INTEGER},
      </if>
      <if test="confidence != null">
        #{confidence,jdbcType=REAL},
      </if>
      <if test="publishTime != null">
        #{publishTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="resourceContent != null">
        #{resourceContent,jdbcType=VARCHAR},
      </if>
      <if test="knowledgeSummary != null">
        #{knowledgeSummary,jdbcType=LONGVARCHAR},
      </if>
      <if test="knowledgeContent != null">
        #{knowledgeContent,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.dzhealth.ai.service.infrastructure.dal.example.RefinedKnowledgeDOExample" resultType="java.lang.Long">
    select count(*) from refined_knowledge
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update refined_knowledge
    <set>
      <if test="row.id != null">
        id = #{row.id,jdbcType=BIGINT},
      </if>
      <if test="row.corpusId != null">
        corpus_id = #{row.corpusId,jdbcType=BIGINT},
      </if>
      <if test="row.resourceId != null">
        resource_id = #{row.resourceId,jdbcType=BIGINT},
      </if>
      <if test="row.resourceChannel != null">
        resource_channel = #{row.resourceChannel,jdbcType=VARCHAR},
      </if>
      <if test="row.resourceUri != null">
        resource_uri = #{row.resourceUri,jdbcType=VARCHAR},
      </if>
      <if test="row.knowledgeType != null">
        knowledge_type = #{row.knowledgeType,jdbcType=INTEGER},
      </if>
      <if test="row.isSuccess != null">
        is_success = #{row.isSuccess,jdbcType=BIT},
      </if>
      <if test="row.isContentQuantified != null">
        is_content_quantified = #{row.isContentQuantified,jdbcType=BIT},
      </if>
      <if test="row.priority != null">
        priority = #{row.priority,jdbcType=INTEGER},
      </if>
      <if test="row.confidence != null">
        confidence = #{row.confidence,jdbcType=REAL},
      </if>
      <if test="row.publishTime != null">
        publish_time = #{row.publishTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.createTime != null">
        create_time = #{row.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.updateTime != null">
        update_time = #{row.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.resourceContent != null">
        resource_content = #{row.resourceContent,jdbcType=VARCHAR},
      </if>
      <if test="row.knowledgeSummary != null">
        knowledge_summary = #{row.knowledgeSummary,jdbcType=LONGVARCHAR},
      </if>
      <if test="row.knowledgeContent != null">
        knowledge_content = #{row.knowledgeContent,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    update refined_knowledge
    set id = #{row.id,jdbcType=BIGINT},
      corpus_id = #{row.corpusId,jdbcType=BIGINT},
      resource_id = #{row.resourceId,jdbcType=BIGINT},
      resource_channel = #{row.resourceChannel,jdbcType=VARCHAR},
      resource_uri = #{row.resourceUri,jdbcType=VARCHAR},
      knowledge_type = #{row.knowledgeType,jdbcType=INTEGER},
      is_success = #{row.isSuccess,jdbcType=BIT},
      is_content_quantified = #{row.isContentQuantified,jdbcType=BIT},
      priority = #{row.priority,jdbcType=INTEGER},
      confidence = #{row.confidence,jdbcType=REAL},
      publish_time = #{row.publishTime,jdbcType=TIMESTAMP},
      create_time = #{row.createTime,jdbcType=TIMESTAMP},
      update_time = #{row.updateTime,jdbcType=TIMESTAMP},
      resource_content = #{row.resourceContent,jdbcType=VARCHAR},
      knowledge_summary = #{row.knowledgeSummary,jdbcType=LONGVARCHAR},
      knowledge_content = #{row.knowledgeContent,jdbcType=LONGVARCHAR}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update refined_knowledge
    set id = #{row.id,jdbcType=BIGINT},
      corpus_id = #{row.corpusId,jdbcType=BIGINT},
      resource_id = #{row.resourceId,jdbcType=BIGINT},
      resource_channel = #{row.resourceChannel,jdbcType=VARCHAR},
      resource_uri = #{row.resourceUri,jdbcType=VARCHAR},
      knowledge_type = #{row.knowledgeType,jdbcType=INTEGER},
      is_success = #{row.isSuccess,jdbcType=BIT},
      is_content_quantified = #{row.isContentQuantified,jdbcType=BIT},
      priority = #{row.priority,jdbcType=INTEGER},
      confidence = #{row.confidence,jdbcType=REAL},
      publish_time = #{row.publishTime,jdbcType=TIMESTAMP},
      create_time = #{row.createTime,jdbcType=TIMESTAMP},
      update_time = #{row.updateTime,jdbcType=TIMESTAMP},
      resource_content = #{row.resourceContent,jdbcType=VARCHAR}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.dzhealth.ai.service.infrastructure.dal.entity.RefinedKnowledgeDOWithBLOBs">
    update refined_knowledge
    <set>
      <if test="corpusId != null">
        corpus_id = #{corpusId,jdbcType=BIGINT},
      </if>
      <if test="resourceId != null">
        resource_id = #{resourceId,jdbcType=BIGINT},
      </if>
      <if test="resourceChannel != null">
        resource_channel = #{resourceChannel,jdbcType=VARCHAR},
      </if>
      <if test="resourceUri != null">
        resource_uri = #{resourceUri,jdbcType=VARCHAR},
      </if>
      <if test="knowledgeType != null">
        knowledge_type = #{knowledgeType,jdbcType=INTEGER},
      </if>
      <if test="isSuccess != null">
        is_success = #{isSuccess,jdbcType=BIT},
      </if>
      <if test="isContentQuantified != null">
        is_content_quantified = #{isContentQuantified,jdbcType=BIT},
      </if>
      <if test="priority != null">
        priority = #{priority,jdbcType=INTEGER},
      </if>
      <if test="confidence != null">
        confidence = #{confidence,jdbcType=REAL},
      </if>
      <if test="publishTime != null">
        publish_time = #{publishTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="resourceContent != null">
        resource_content = #{resourceContent,jdbcType=VARCHAR},
      </if>
      <if test="knowledgeSummary != null">
        knowledge_summary = #{knowledgeSummary,jdbcType=LONGVARCHAR},
      </if>
      <if test="knowledgeContent != null">
        knowledge_content = #{knowledgeContent,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.sankuai.dzhealth.ai.service.infrastructure.dal.entity.RefinedKnowledgeDOWithBLOBs">
    update refined_knowledge
    set corpus_id = #{corpusId,jdbcType=BIGINT},
      resource_id = #{resourceId,jdbcType=BIGINT},
      resource_channel = #{resourceChannel,jdbcType=VARCHAR},
      resource_uri = #{resourceUri,jdbcType=VARCHAR},
      knowledge_type = #{knowledgeType,jdbcType=INTEGER},
      is_success = #{isSuccess,jdbcType=BIT},
      is_content_quantified = #{isContentQuantified,jdbcType=BIT},
      priority = #{priority,jdbcType=INTEGER},
      confidence = #{confidence,jdbcType=REAL},
      publish_time = #{publishTime,jdbcType=TIMESTAMP},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      resource_content = #{resourceContent,jdbcType=VARCHAR},
      knowledge_summary = #{knowledgeSummary,jdbcType=LONGVARCHAR},
      knowledge_content = #{knowledgeContent,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.dzhealth.ai.service.infrastructure.dal.entity.RefinedKnowledgeDO">
    update refined_knowledge
    set corpus_id = #{corpusId,jdbcType=BIGINT},
      resource_id = #{resourceId,jdbcType=BIGINT},
      resource_channel = #{resourceChannel,jdbcType=VARCHAR},
      resource_uri = #{resourceUri,jdbcType=VARCHAR},
      knowledge_type = #{knowledgeType,jdbcType=INTEGER},
      is_success = #{isSuccess,jdbcType=BIT},
      is_content_quantified = #{isContentQuantified,jdbcType=BIT},
      priority = #{priority,jdbcType=INTEGER},
      confidence = #{confidence,jdbcType=REAL},
      publish_time = #{publishTime,jdbcType=TIMESTAMP},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      resource_content = #{resourceContent,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>