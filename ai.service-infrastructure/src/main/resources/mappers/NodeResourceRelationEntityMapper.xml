<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.dzhealth.ai.service.infrastructure.dal.mapper.NodeResourceRelationEntityMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.dzhealth.ai.service.infrastructure.dal.entity.NodeResourceRelationDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="biz_scene" jdbcType="VARCHAR" property="bizScene" />
    <result column="node_id" jdbcType="VARCHAR" property="nodeId" />
    <result column="resource_id" jdbcType="VARCHAR" property="resourceId" />
    <result column="resource_type" jdbcType="VARCHAR" property="resourceType" />
    <result column="rationale" jdbcType="VARCHAR" property="rationale" />
    <result column="sort_order" jdbcType="BIGINT" property="sortOrder" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="ext" jdbcType="CHAR" property="ext" />
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, biz_scene, node_id, resource_id, resource_type, rationale,
    sort_order, status, ext, add_time, update_time
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.dzhealth.ai.service.infrastructure.dal.example.NodeResourceRelationEntityExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from node_resource_relation
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from node_resource_relation
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from node_resource_relation
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.dzhealth.ai.service.infrastructure.dal.example.NodeResourceRelationEntityExample">
    delete from node_resource_relation
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sankuai.dzhealth.ai.service.infrastructure.dal.entity.NodeResourceRelationDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into node_resource_relation (biz_scene, node_id, resource_id, 
      resource_type, rationale, sort_order,
      status, ext, add_time, update_time
      )
    values (#{bizScene,jdbcType=VARCHAR}, #{nodeId,jdbcType=VARCHAR}, #{resourceId,jdbcType=VARCHAR}, 
      #{resourceType,jdbcType=VARCHAR}, #{rationale,jdbcType=VARCHAR}, #{sortOrder,jdbcType=BIGINT},
      #{status,jdbcType=VARCHAR}, #{ext,jdbcType=CHAR}, #{addTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.sankuai.dzhealth.ai.service.infrastructure.dal.entity.NodeResourceRelationDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into node_resource_relation
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="bizScene != null">
        biz_scene,
      </if>
      <if test="nodeId != null">
        node_id,
      </if>
      <if test="resourceId != null">
        resource_id,
      </if>
      <if test="resourceType != null">
        resource_type,
      </if>
      <if test="rationale != null">
        rationale,
      </if>
      <if test="sortOrder != null">
        sort_order,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="ext != null">
        ext,
      </if>
      <if test="addTime != null">
        add_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="bizScene != null">
        #{bizScene,jdbcType=VARCHAR},
      </if>
      <if test="nodeId != null">
        #{nodeId,jdbcType=VARCHAR},
      </if>
      <if test="resourceId != null">
        #{resourceId,jdbcType=VARCHAR},
      </if>
      <if test="resourceType != null">
        #{resourceType,jdbcType=VARCHAR},
      </if>
      <if test="rationale != null">
        #{rationale,jdbcType=VARCHAR},
      </if>
      <if test="sortOrder != null">
        #{sortOrder,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="ext != null">
        #{ext,jdbcType=CHAR},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <!-- batch insert -->
  <insert id="insertList" parameterType="java.util.List">
    insert into node_resource_relation (biz_scene, node_id, resource_id, resource_type, rationale, sort_order, status, ext, add_time, update_time)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.bizScene,jdbcType=VARCHAR}, #{item.nodeId,jdbcType=VARCHAR}, #{item.resourceId,jdbcType=VARCHAR}, #{item.resourceType,jdbcType=VARCHAR}, #{item.rationale,jdbcType=VARCHAR}, #{item.sortOrder,jdbcType=BIGINT}, #{item.status,jdbcType=VARCHAR}, #{item.ext,jdbcType=CHAR}, #{item.addTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP})
    </foreach>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.dzhealth.ai.service.infrastructure.dal.example.NodeResourceRelationEntityExample" resultType="java.lang.Long"> parameterType="com.sankuai.dzhealth.ai.service.infrastructure.dal.example.NodeResourceRelationEntityExample" resultType="java.lang.Long">
    select count(*) from node_resource_relation
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update node_resource_relation
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.bizScene != null">
        biz_scene = #{record.bizScene,jdbcType=VARCHAR},
      </if>
      <if test="record.nodeId != null">
        node_id = #{record.nodeId,jdbcType=VARCHAR},
      </if>
      <if test="record.resourceId != null">
        resource_id = #{record.resourceId,jdbcType=VARCHAR},
      </if>
      <if test="record.resourceType != null">
        resource_type = #{record.resourceType,jdbcType=VARCHAR},
      </if>
      <if test="record.rationale != null">
        rationale = #{record.rationale,jdbcType=VARCHAR},
      </if>
      <if test="record.sortOrder != null">
        sort_order = #{record.sortOrder,jdbcType=BIGINT},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=VARCHAR},
      </if>
      <if test="record.ext != null">
        ext = #{record.ext,jdbcType=CHAR},
      </if>
      <if test="record.addTime != null">
        add_time = #{record.addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update node_resource_relation
    set id = #{record.id,jdbcType=BIGINT},
      biz_scene = #{record.bizScene,jdbcType=VARCHAR},
      node_id = #{record.nodeId,jdbcType=VARCHAR},
      resource_id = #{record.resourceId,jdbcType=VARCHAR},
      resource_type = #{record.resourceType,jdbcType=VARCHAR},
      rationale = #{record.rationale,jdbcType=VARCHAR},
      sort_order = #{record.sortOrder,jdbcType=BIGINT},
      status = #{record.status,jdbcType=VARCHAR},
      ext = #{record.ext,jdbcType=CHAR},
      add_time = #{record.addTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.dzhealth.ai.service.infrastructure.dal.entity.NodeResourceRelationDO">
    update node_resource_relation
    <set>
      <if test="bizScene != null">
        biz_scene = #{bizScene,jdbcType=VARCHAR},
      </if>
      <if test="nodeId != null">
        node_id = #{nodeId,jdbcType=VARCHAR},
      </if>
      <if test="resourceId != null">
        resource_id = #{resourceId,jdbcType=VARCHAR},
      </if>
      <if test="resourceType != null">
        resource_type = #{resourceType,jdbcType=VARCHAR},
      </if>
      <if test="rationale != null">
        rationale = #{rationale,jdbcType=VARCHAR},
      </if>
      <if test="sortOrder != null">
        sort_order = #{sortOrder,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=VARCHAR},
      </if>
      <if test="ext != null">
        ext = #{ext,jdbcType=CHAR},
      </if>
      <if test="addTime != null">
        add_time = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.dzhealth.ai.service.infrastructure.dal.entity.NodeResourceRelationDO">
    update node_resource_relation
    set biz_scene = #{bizScene,jdbcType=VARCHAR},
      node_id = #{nodeId,jdbcType=VARCHAR},
      resource_id = #{resourceId,jdbcType=VARCHAR},
      resource_type = #{resourceType,jdbcType=VARCHAR},
      rationale = #{rationale,jdbcType=VARCHAR},
      sort_order = #{sortOrder,jdbcType=BIGINT},
      status = #{status,jdbcType=VARCHAR},
      ext = #{ext,jdbcType=CHAR},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>