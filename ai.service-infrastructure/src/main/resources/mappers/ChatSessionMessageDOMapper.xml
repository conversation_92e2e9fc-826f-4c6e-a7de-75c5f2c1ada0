<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.dzhealth.ai.service.agent.infrastructure.dal.mapper.ChatSessionMessageDOMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.dzhealth.ai.service.agent.infrastructure.dal.entity.ChatSessionMessageDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="message_id" jdbcType="VARCHAR" property="messageId" />
    <result column="session_id" jdbcType="VARCHAR" property="sessionId" />
    <result column="role" jdbcType="VARCHAR" property="role" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="platform" jdbcType="INTEGER" property="platform" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="feedback" jdbcType="INTEGER" property="feedback" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.sankuai.dzhealth.ai.service.agent.infrastructure.dal.entity.ChatSessionMessageDOWithBLOBs">
    <result column="content" jdbcType="LONGVARCHAR" property="content" />
    <result column="memory_snapshot" jdbcType="LONGVARCHAR" property="memorySnapshot" />
    <result column="extra" jdbcType="LONGVARCHAR" property="extra" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, message_id, session_id, role, user_id, platform, type, status, feedback, create_time, 
    update_time
  </sql>
  <sql id="Blob_Column_List">
    content, memory_snapshot, extra
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.sankuai.dzhealth.ai.service.agent.infrastructure.dal.example.ChatSessionMessageDOExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from chat_session_message
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="com.sankuai.dzhealth.ai.service.agent.infrastructure.dal.example.ChatSessionMessageDOExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from chat_session_message
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from chat_session_message
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from chat_session_message
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.dzhealth.ai.service.agent.infrastructure.dal.example.ChatSessionMessageDOExample">
    delete from chat_session_message
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sankuai.dzhealth.ai.service.agent.infrastructure.dal.entity.ChatSessionMessageDOWithBLOBs">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into chat_session_message (message_id, session_id, role, 
      user_id, platform, type, 
      status, feedback, create_time, 
      update_time, content, memory_snapshot, 
      extra)
    values (#{messageId,jdbcType=VARCHAR}, #{sessionId,jdbcType=VARCHAR}, #{role,jdbcType=VARCHAR}, 
      #{userId,jdbcType=BIGINT}, #{platform,jdbcType=INTEGER}, #{type,jdbcType=INTEGER}, 
      #{status,jdbcType=INTEGER}, #{feedback,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{content,jdbcType=LONGVARCHAR}, #{memorySnapshot,jdbcType=LONGVARCHAR}, 
      #{extra,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.sankuai.dzhealth.ai.service.agent.infrastructure.dal.entity.ChatSessionMessageDOWithBLOBs">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into chat_session_message
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="messageId != null">
        message_id,
      </if>
      <if test="sessionId != null">
        session_id,
      </if>
      <if test="role != null">
        role,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="platform != null">
        platform,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="feedback != null">
        feedback,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="content != null">
        content,
      </if>
      <if test="memorySnapshot != null">
        memory_snapshot,
      </if>
      <if test="extra != null">
        extra,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="messageId != null">
        #{messageId,jdbcType=VARCHAR},
      </if>
      <if test="sessionId != null">
        #{sessionId,jdbcType=VARCHAR},
      </if>
      <if test="role != null">
        #{role,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=BIGINT},
      </if>
      <if test="platform != null">
        #{platform,jdbcType=INTEGER},
      </if>
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="feedback != null">
        #{feedback,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="content != null">
        #{content,jdbcType=LONGVARCHAR},
      </if>
      <if test="memorySnapshot != null">
        #{memorySnapshot,jdbcType=LONGVARCHAR},
      </if>
      <if test="extra != null">
        #{extra,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.dzhealth.ai.service.agent.infrastructure.dal.example.ChatSessionMessageDOExample" resultType="java.lang.Long">
    select count(*) from chat_session_message
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update chat_session_message
    <set>
      <if test="row.id != null">
        id = #{row.id,jdbcType=BIGINT},
      </if>
      <if test="row.messageId != null">
        message_id = #{row.messageId,jdbcType=VARCHAR},
      </if>
      <if test="row.sessionId != null">
        session_id = #{row.sessionId,jdbcType=VARCHAR},
      </if>
      <if test="row.role != null">
        role = #{row.role,jdbcType=VARCHAR},
      </if>
      <if test="row.userId != null">
        user_id = #{row.userId,jdbcType=BIGINT},
      </if>
      <if test="row.platform != null">
        platform = #{row.platform,jdbcType=INTEGER},
      </if>
      <if test="row.type != null">
        type = #{row.type,jdbcType=INTEGER},
      </if>
      <if test="row.status != null">
        status = #{row.status,jdbcType=INTEGER},
      </if>
      <if test="row.feedback != null">
        feedback = #{row.feedback,jdbcType=INTEGER},
      </if>
      <if test="row.createTime != null">
        create_time = #{row.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.updateTime != null">
        update_time = #{row.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.content != null">
        content = #{row.content,jdbcType=LONGVARCHAR},
      </if>
      <if test="row.memorySnapshot != null">
        memory_snapshot = #{row.memorySnapshot,jdbcType=LONGVARCHAR},
      </if>
      <if test="row.extra != null">
        extra = #{row.extra,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    update chat_session_message
    set id = #{row.id,jdbcType=BIGINT},
      message_id = #{row.messageId,jdbcType=VARCHAR},
      session_id = #{row.sessionId,jdbcType=VARCHAR},
      role = #{row.role,jdbcType=VARCHAR},
      user_id = #{row.userId,jdbcType=BIGINT},
      platform = #{row.platform,jdbcType=INTEGER},
      type = #{row.type,jdbcType=INTEGER},
      status = #{row.status,jdbcType=INTEGER},
      feedback = #{row.feedback,jdbcType=INTEGER},
      create_time = #{row.createTime,jdbcType=TIMESTAMP},
      update_time = #{row.updateTime,jdbcType=TIMESTAMP},
      content = #{row.content,jdbcType=LONGVARCHAR},
      memory_snapshot = #{row.memorySnapshot,jdbcType=LONGVARCHAR},
      extra = #{row.extra,jdbcType=LONGVARCHAR}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update chat_session_message
    set id = #{row.id,jdbcType=BIGINT},
      message_id = #{row.messageId,jdbcType=VARCHAR},
      session_id = #{row.sessionId,jdbcType=VARCHAR},
      role = #{row.role,jdbcType=VARCHAR},
      user_id = #{row.userId,jdbcType=BIGINT},
      platform = #{row.platform,jdbcType=INTEGER},
      type = #{row.type,jdbcType=INTEGER},
      status = #{row.status,jdbcType=INTEGER},
      feedback = #{row.feedback,jdbcType=INTEGER},
      create_time = #{row.createTime,jdbcType=TIMESTAMP},
      update_time = #{row.updateTime,jdbcType=TIMESTAMP}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.dzhealth.ai.service.agent.infrastructure.dal.entity.ChatSessionMessageDOWithBLOBs">
    update chat_session_message
    <set>
      <if test="messageId != null">
        message_id = #{messageId,jdbcType=VARCHAR},
      </if>
      <if test="sessionId != null">
        session_id = #{sessionId,jdbcType=VARCHAR},
      </if>
      <if test="role != null">
        role = #{role,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        user_id = #{userId,jdbcType=BIGINT},
      </if>
      <if test="platform != null">
        platform = #{platform,jdbcType=INTEGER},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="feedback != null">
        feedback = #{feedback,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="content != null">
        content = #{content,jdbcType=LONGVARCHAR},
      </if>
      <if test="memorySnapshot != null">
        memory_snapshot = #{memorySnapshot,jdbcType=LONGVARCHAR},
      </if>
      <if test="extra != null">
        extra = #{extra,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.sankuai.dzhealth.ai.service.agent.infrastructure.dal.entity.ChatSessionMessageDOWithBLOBs">
    update chat_session_message
    set message_id = #{messageId,jdbcType=VARCHAR},
      session_id = #{sessionId,jdbcType=VARCHAR},
      role = #{role,jdbcType=VARCHAR},
      user_id = #{userId,jdbcType=BIGINT},
      platform = #{platform,jdbcType=INTEGER},
      type = #{type,jdbcType=INTEGER},
      status = #{status,jdbcType=INTEGER},
      feedback = #{feedback,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      content = #{content,jdbcType=LONGVARCHAR},
      memory_snapshot = #{memorySnapshot,jdbcType=LONGVARCHAR},
      extra = #{extra,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.dzhealth.ai.service.agent.infrastructure.dal.entity.ChatSessionMessageDO">
    update chat_session_message
    set message_id = #{messageId,jdbcType=VARCHAR},
      session_id = #{sessionId,jdbcType=VARCHAR},
      role = #{role,jdbcType=VARCHAR},
      user_id = #{userId,jdbcType=BIGINT},
      platform = #{platform,jdbcType=INTEGER},
      type = #{type,jdbcType=INTEGER},
      status = #{status,jdbcType=INTEGER},
      feedback = #{feedback,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>