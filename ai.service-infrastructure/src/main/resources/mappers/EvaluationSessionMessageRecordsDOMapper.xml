<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.dzhealth.ai.service.agent.infrastructure.dal.mapper.EvaluationSessionMessageRecordsDOMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.dzhealth.ai.service.agent.infrastructure.dal.entity.EvaluationSessionMessageRecordsDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="session_id" jdbcType="VARCHAR" property="sessionId" />
    <result column="message_id" jdbcType="VARCHAR" property="messageId" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.sankuai.dzhealth.ai.service.agent.infrastructure.dal.entity.EvaluationSessionMessageRecordsDO">
    <result column="evaluation_result" jdbcType="LONGVARCHAR" property="evaluationResult" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, session_id, message_id, status, create_time, update_time
  </sql>
  <sql id="Blob_Column_List">
    evaluation_result
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.sankuai.dzhealth.ai.service.agent.infrastructure.dal.example.EvaluationSessionMessageRecordsDOExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from evaluation_session_message_records
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="com.sankuai.dzhealth.ai.service.agent.infrastructure.dal.example.EvaluationSessionMessageRecordsDOExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from evaluation_session_message_records
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from evaluation_session_message_records
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from evaluation_session_message_records
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.dzhealth.ai.service.agent.infrastructure.dal.example.EvaluationSessionMessageRecordsDOExample">
    delete from evaluation_session_message_records
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sankuai.dzhealth.ai.service.agent.infrastructure.dal.entity.EvaluationSessionMessageRecordsDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into evaluation_session_message_records (session_id,
      message_id, status, create_time, 
      update_time, evaluation_result)
    values (#{sessionId,jdbcType=VARCHAR},
      #{messageId,jdbcType=VARCHAR}, #{status,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{evaluationResult,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.sankuai.dzhealth.ai.service.agent.infrastructure.dal.entity.EvaluationSessionMessageRecordsDO">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into evaluation_session_message_records
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="sessionId != null">
        session_id,
      </if>
      <if test="messageId != null">
        message_id,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="evaluationResult != null">
        evaluation_result,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="sessionId != null">
        #{sessionId,jdbcType=VARCHAR},
      </if>
      <if test="messageId != null">
        #{messageId,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="evaluationResult != null">
        #{evaluationResult,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.dzhealth.ai.service.agent.infrastructure.dal.example.EvaluationSessionMessageRecordsDOExample" resultType="java.lang.Long">
    select count(*) from evaluation_session_message_records
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update evaluation_session_message_records
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.sessionId != null">
        session_id = #{record.sessionId,jdbcType=VARCHAR},
      </if>
      <if test="record.messageId != null">
        message_id = #{record.messageId,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.evaluationResult != null">
        evaluation_result = #{record.evaluationResult,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    update evaluation_session_message_records
    set id = #{record.id,jdbcType=BIGINT},
      session_id = #{record.sessionId,jdbcType=VARCHAR},
      message_id = #{record.messageId,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=INTEGER},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      evaluation_result = #{record.evaluationResult,jdbcType=LONGVARCHAR}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update evaluation_session_message_records
    set id = #{record.id,jdbcType=BIGINT},
      session_id = #{record.sessionId,jdbcType=VARCHAR},
      message_id = #{record.messageId,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=INTEGER},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.dzhealth.ai.service.agent.infrastructure.dal.entity.EvaluationSessionMessageRecordsDO">
    update evaluation_session_message_records
    <set>
      <if test="sessionId != null">
        session_id = #{sessionId,jdbcType=VARCHAR},
      </if>
      <if test="messageId != null">
        message_id = #{messageId,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="evaluationResult != null">
        evaluation_result = #{evaluationResult,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.sankuai.dzhealth.ai.service.agent.infrastructure.dal.entity.EvaluationSessionMessageRecordsDO">
    update evaluation_session_message_records
    set
      session_id = #{sessionId,jdbcType=VARCHAR},
      message_id = #{messageId,jdbcType=VARCHAR},
      status = #{status,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      evaluation_result = #{evaluationResult,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.dzhealth.ai.service.agent.infrastructure.dal.entity.EvaluationSessionMessageRecordsDO">
    update evaluation_session_message_records
    set
      session_id = #{sessionId,jdbcType=VARCHAR},
      message_id = #{messageId,jdbcType=VARCHAR},
      status = #{status,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="insertOrUpdate" parameterType="com.sankuai.dzhealth.ai.service.agent.infrastructure.dal.entity.EvaluationSessionMessageRecordsDO">
    INSERT INTO evaluation_session_message_records (session_id,message_id, create_time,
      update_time, evaluation_result)
    values (#{row.sessionId,jdbcType=VARCHAR},#{row.messageId,jdbcType=VARCHAR}, #{row.createTime,jdbcType=TIMESTAMP},
      #{row.updateTime,jdbcType=TIMESTAMP}, #{row.evaluationResult,jdbcType=LONGVARCHAR})
    ON DUPLICATE KEY UPDATE
      evaluation_result = #{row.evaluationResult,jdbcType=LONGVARCHAR}
  </insert>
</mapper>