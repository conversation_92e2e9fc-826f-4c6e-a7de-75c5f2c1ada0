<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE generatorConfiguration
        PUBLIC "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN"
        "http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd">

<generatorConfiguration>

    <context id="chat" targetRuntime="MyBatis3">
        <!-- 生成的entity实体引入lombok注解 -->
        <plugin type="com.meituan.mdp.mybatis.generator.plugins.LombokPlugin"/>
        <!-- 使用MdpMapperPlugin生成通用sql方法 -->
        <plugin type="com.meituan.mdp.mybatis.generator.plugins.MdpMapperPlugin"/>
        <!-- xml覆盖配置 -->
        <plugin type="org.mybatis.generator.plugins.UnmergeableXmlMappersPlugin"/>
        <!-- Example类存储路径 -->
        <plugin type="com.meituan.mdp.mybatis.generator.plugins.ExampleTargetPlugin">
            <property name="targetPackage" value="com.sankuai.dzhealth.ai.service.agent.infrastructure.dal.example"/>
        </plugin>
        <!-- 数据库注释生成器 -->
        <commentGenerator type="com.meituan.mdp.mybatis.generator.internal.RemarksCommentGenerator">
            <property name="suppressAllComments" value="true"/>
            <property name="suppressDate" value="true"/>
            <property name="addRemarkComments" value="true"/>
        </commentGenerator>

        <!-- 配置数据库连接 -->
        <jdbcConnection driverClass="com.mysql.jdbc.Driver"
                        connectionURL="*******************************************************************************************************************************************"
                        userId="rds_healthaitest"
                        password="i#YNRC4ty3mpH3">
        </jdbcConnection>

        <javaTypeResolver>
            <property name="forceBigDecimals" value="false"/>
        </javaTypeResolver>

        <!-- 实体类生成配置 -->
        <javaModelGenerator targetPackage="com.sankuai.dzhealth.ai.service.agent.infrastructure.dal.entity" targetProject="src/main/java">
            <property name="enableSubPackages" value="true"/>
            <property name="trimStrings" value="true"/>
        </javaModelGenerator>

        <!-- XML文件生成配置 -->
        <sqlMapGenerator targetPackage="mappers" targetProject="src/main/resources">
            <property name="enableSubPackages" value="true"/>
        </sqlMapGenerator>

        <!-- Mapper接口生成配置 -->
        <javaClientGenerator type="XMLMAPPER" targetPackage="com.sankuai.dzhealth.ai.service.agent.infrastructure.dal.mapper" targetProject="src/main/java">
            <property name="enableSubPackages" value="true"/>
        </javaClientGenerator>

        <!-- 配置需要生成的表 -->
<!--        <table tableName="chat_conversation" domainObjectName="ChatConversationEntity">
            <generatedKey column="id" sqlStatement="MYSQL" identity="true"/>
        </table>

        <table tableName="chat_message" domainObjectName="ChatMessageEntity">
            <generatedKey column="id" sqlStatement="MYSQL" identity="true"/>
        </table>

        <table tableName="chat_message_feedback" domainObjectName="ChatMessageFeedbackEntity">
            <generatedKey column="id" sqlStatement="MYSQL" identity="true"/>
        </table>-->

        <table tableName="appointment_info" domainObjectName="AppointmentInfoDO">
            <generatedKey column="id" sqlStatement="MYSQL" identity="true"/>
        </table>

<!--        <table tableName="evaluation_session_message_records" domainObjectName="EvaluationSessionMessageRecordsDO">-->
<!--            <generatedKey column="id" sqlStatement="MYSQL" identity="true"/>-->
<!--        </table>-->
<!--        <table tableName="corpus_library" domainObjectName="CorpusLibraryDO">-->
<!--            <property name="mapUnderscoreToCamelCase" value="true"/>-->
<!--            <generatedKey column="Id" sqlStatement="MySql" identity="true"/>-->
<!--        </table>-->
<!--        <table tableName="refined_knowledge" domainObjectName="RefinedKnowledgeDO">-->
<!--            <property name="mapUnderscoreToCamelCase" value="true"/>-->
<!--            <generatedKey column="Id" sqlStatement="MySql" identity="true"/>-->
<!--        </table>-->


<!--        <table tableName="deep_search_results" domainObjectName="DeepSearchResultEntity">-->
<!--            <generatedKey column="id" sqlStatement="MYSQL" identity="true"/>-->
<!--        </table>-->


<!--        <table tableName="thinking_sessions" domainObjectName="ThinkingSessionEntity">-->
<!--            <generatedKey column="id" sqlStatement="MYSQL" identity="true"/>-->
<!--        </table>-->

<!--        <table tableName="thinking_steps" domainObjectName="ThinkingStepEntity">-->
<!--            <generatedKey column="id" sqlStatement="MYSQL" identity="true"/>-->
<!--        </table>-->
<!--        <table tableName="rag_evaluation_response" domainObjectName="RagEvaluationResponseEntity">-->
<!--            <property name="mapUnderscoreToCamelCase" value="true"/>-->
<!--            <generatedKey column="Id" sqlStatement="MySql" identity="true"/>-->
<!--        </table>-->

        <!-- 决策树相关表配置 -->
<!--        <table tableName="decision_node" domainObjectName="DecisionNodeEntity">-->
<!--            <generatedKey column="id" sqlStatement="MYSQL" identity="true"/>-->
<!--        </table>-->

<!--        <table tableName="decision_node_edge" domainObjectName="DecisionNodeEdgeEntity">-->
<!--            <generatedKey column="id" sqlStatement="MYSQL" identity="true"/>-->
<!--        </table>-->

<!--        <table tableName="recommend_resource" domainObjectName="RecommendResourceEntity">-->
<!--            <generatedKey column="id" sqlStatement="MYSQL" identity="true"/>-->
<!--        </table>-->

<!--        <table tableName="node_resource_relation" domainObjectName="NodeResourceRelationEntity">-->
<!--            <generatedKey column="id" sqlStatement="MYSQL" identity="true"/>-->
<!--        </table>-->

<!--        <table tableName="message_evaluation_result" domainObjectName="MessageEvaluationResultEntity">-->
<!--            <generatedKey column="id" sqlStatement="MYSQL" identity="true"/>-->
<!--        </table>-->

<!--        <table tableName="session_evaluation_result" domainObjectName="SessionEvaluationResultEntity">-->
<!--            <generatedKey column="id" sqlStatement="MYSQL" identity="true"/>-->
<!--        </table>-->

    </context>


</generatorConfiguration>