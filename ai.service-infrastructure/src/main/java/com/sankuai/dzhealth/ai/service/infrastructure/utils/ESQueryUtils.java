package com.sankuai.dzhealth.ai.service.infrastructure.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import org.springframework.ai.embedding.EmbeddingModel;
import org.springframework.ai.model.EmbeddingUtils;
import org.springframework.ai.vectorstore.elasticsearch.SimilarityFunction;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * ES查询生成工具类
 */
public class ESQueryUtils {

    /**
     * 使用嵌入模型生成ES查询
     * 
     * @param query 用户查询文本
     * @param embeddingModel 嵌入模型
     * @param topK 返回结果数量
     * @param filterExpression 过滤表达式
     * @param similarityFunction 相似度函数
     * @return ES查询JSON字符串
     */
    public static String generateESQueryWithModel(String query, 
                                                 EmbeddingModel embeddingModel, 
                                                 int topK, 
                                                 String filterExpression,
                                                 SimilarityFunction similarityFunction) {
        // 生成查询向量
        float[] queryVector = embeddingModel.embed(query);
        
        // 使用向量生成ES查询
        return generateESQuery(query, queryVector, topK, filterExpression, similarityFunction);
    }

    /**
     * 生成ES查询JSON
     * 
     * @param query 用户查询文本
     * @param queryVector 查询向量
     * @param topK 返回结果数量
     * @param filterExpression 过滤表达式，如"channel:deep_search_result"
     * @param similarityFunction 相似度函数
     * @return ES查询JSON字符串
     */
    public static String generateESQuery(String query, 
                                        float[] queryVector, 
                                        int topK, 
                                        String filterExpression,
                                        SimilarityFunction similarityFunction) {
        Map<String, Object> esQuery = new HashMap<>();
        
        // 设置_source
        esQuery.put("_source", Map.of("includes", Arrays.asList("id", "content", "metadata")));
        
        // 设置查询
        Map<String, Object> boolQuery = new HashMap<>();
        
        // 构建filter
        Map<String, Object> filter = Map.of(
            "query_string", Map.of("query", filterExpression)
        );
        
        // 构建must
        Map<String, Object> must = Map.of(
            "match", Map.of("content", query)
        );
        
        boolQuery.put("filter", filter);
        boolQuery.put("must", must);
        
        esQuery.put("query", Map.of("bool", boolQuery));
        
        // 计算相似度阈值
        float threshold = 0.0f;
        if (similarityFunction == SimilarityFunction.l2_norm) {
            threshold = 1.0f;  // 对于L2 Norm，较小的值表示更高的相似度
        }

        // 构建KNN查询
        Map<String, Object> knn = new HashMap<>();
        knn.put("query_vector", queryVector);
        knn.put("similarity", threshold);
        knn.put("k", topK);
        knn.put("field", "embedding");
        knn.put("num_candidates", (int)(topK * 1.5));
        
        // 重要：不使用引用，而是复制相同的filter对象
        Map<String, Object> knnFilter = Map.of(
            "query_string", Map.of("query", filterExpression)
        );
        knn.put("filter", knnFilter);
        
        esQuery.put("knn", knn);
        
        // 设置排序
        Map<String, Object> rank = new HashMap<>();
        rank.put("mrrf", Map.of("window_size", (int)(topK * 1.5)));
        esQuery.put("rank", rank);
        
        // 设置大小
        esQuery.put("size", topK);
        
        return JSON.toJSONString(esQuery, SerializerFeature.PrettyFormat);
    }
    
    /**
     * 从向量数组转换为List
     */
    public static List<Float> toFloatList(float[] vector) {
        return EmbeddingUtils.toList(vector);
    }
} 