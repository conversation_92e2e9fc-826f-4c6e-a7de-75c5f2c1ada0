package com.sankuai.dzhealth.ai.service.agent.infrastructure.proxy.doctorinfo;

import com.alibaba.fastjson.JSON;
import com.meituan.mdp.boot.starter.thrift.annotation.MdpThriftClient;
import com.sankuai.dzhealth.ai.service.agent.request.DoctorInfoRequest;
import com.sankuai.medicalcosmetology.display.api.DoctorInfoQueryService;
import com.sankuai.medicalcosmetology.display.dto.AuthorizedBrandInfoDTO;
import com.sankuai.medicalcosmetology.display.dto.DoctorSimpleInfoDTO;
import com.sankuai.medicalcosmetology.display.dto.RemoteResponse;
import com.sankuai.medicalcosmetology.display.request.DoctorSimpleInfoBatchQueryRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 医生详情查询防腐层
 */
@Component
@Slf4j
public class DoctorInfoAcl {

    @MdpThriftClient(remoteAppKey = "com.sankuai.medicalcosmetology.doctor.function", timeout = 5000, testTimeout = 10000)
    private DoctorInfoQueryService doctorInfoQueryService;

    /**
     * 查询医生简单信息列表
     *
     * @param request 批量查询请求
     * @return 医生信息列表的JSON字符串，失败返回空字符串
     */
    public String queryDoctorInfo(DoctorInfoRequest request) {
        try {

            DoctorSimpleInfoBatchQueryRequest batchQueryRequest = new DoctorSimpleInfoBatchQueryRequest();
            batchQueryRequest.setMergeDoctorIds(request.getMergeDoctorIds());
            batchQueryRequest.setPlatform(request.getPlatform());
            batchQueryRequest.setUserId(request.getUserId());

            RemoteResponse<List<DoctorSimpleInfoDTO>> response = doctorInfoQueryService.queryDoctorSimpleInfo(batchQueryRequest);

            if (response != null && response.getSuccess() && response.getData() != null) {
                // 转换为大模型友好的格式
                return convertToAIFriendlyFormat(response.getData());
            }

            return null; // 返回空JSON
        } catch (Exception e) {
            log.error("DoctorDisplayAcl.queryDoctorInfo error, request:{}", request, e);
            throw e;
        }
    }

    /**
     * 转换为大模型友好的JSON格式
     */
    private String convertToAIFriendlyFormat(List<DoctorSimpleInfoDTO> doctorList) {
        if (doctorList == null || doctorList.isEmpty()) {
            return "{}";
        }

        List<Map<String, Object>> aiFormattedList = doctorList.stream()
            .map(this::convertDoctorToMap)
            .collect(Collectors.toList());

        return JSON.toJSONString(aiFormattedList);
    }

    /**
     * 将医生信息转换为Map格式，便于大模型理解
     */
    private Map<String, Object> convertDoctorToMap(DoctorSimpleInfoDTO doctor) {
        Map<String, Object> doctorMap = new HashMap<>();

        doctorMap.put("融合医生id", doctor.getMergeDoctorId());
        doctorMap.put("临床职称", doctor.getClinicalTitle()); // 临床职称：主任医师、主治医师
        doctorMap.put("学术职称", doctor.getAcademicTitle()); // 学术职称：教授、副教授等
        doctorMap.put("从业年限", doctor.getWorkYears()); // 从业年限
        doctorMap.put("擅长领域", doctor.getSpeciality()); // 擅长领域
        doctorMap.put("好评率", doctor.getPositiveRate()); // 好评率，如：99%
        doctorMap.put("评价数", doctor.getMergeReviewCnt()); // 评价数
        doctorMap.put("品牌授权信息", convertAuthorizedBrands(doctor.getAuthorizedBrands()));
        doctorMap.put("是否在司南榜", doctor.getIsSiNanRankDoctor()); // 是否在司南榜
        doctorMap.put("用户体验报告量", doctor.getDoctorCaseNum()); // 用户体验报告量

        return doctorMap;
    }

    /**
     * 转换品牌授权信息为更友好的格式
     */
    private List<Map<String, Object>> convertAuthorizedBrands(List<AuthorizedBrandInfoDTO> brands) {
        if (brands == null || brands.isEmpty()) {
            return new ArrayList<>();
        }

        return brands.stream()
                .map(brand -> {
                    Map<String, Object> brandMap = new HashMap<>();
                    brandMap.put("品牌id", brand.getAuthorizedBrandId());
                    brandMap.put("品牌名称", brand.getAuthorizedBrandName());
                    return brandMap;
                })
                .collect(Collectors.toList());
    }
}
