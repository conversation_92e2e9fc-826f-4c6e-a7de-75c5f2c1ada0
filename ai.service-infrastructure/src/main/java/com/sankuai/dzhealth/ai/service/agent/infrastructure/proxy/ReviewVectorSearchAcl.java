package com.sankuai.dzhealth.ai.service.agent.infrastructure.proxy;

import com.meituan.mdp.boot.starter.pigeon.annotation.MdpPigeonClient;
import com.sankuai.dzim.pilot.api.VectorSearchService;
import com.sankuai.dzim.pilot.api.data.ReviewDTO;
import com.sankuai.dzim.pilot.api.data.ReviewVectorSearchRequest;
import com.sankuai.dzim.pilot.api.data.VectorSearchResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;

/**
 * 评价向量搜索服务ACL
 * 统一管理评价相关的VectorSearchService的Pigeon客户端
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class ReviewVectorSearchAcl {

    @MdpPigeonClient(url = "com.sankuai.dzim.pilot.api.VectorSearchService", timeout = 3000)
    private VectorSearchService vectorSearchService;

    /**
     * 搜索评价
     *
     * @param request 搜索请求
     * @return 搜索结果，异常时返回空结果避免影响业务流程
     */
    public VectorSearchResponse<ReviewDTO> searchReview(ReviewVectorSearchRequest request) {
        try {
            VectorSearchResponse<ReviewDTO> response = vectorSearchService.searchReview(request);
            return response;
        } catch (Exception e) {
            log.error("向量搜索评价服务调用失败: error={}", e.getMessage(), e);
            VectorSearchResponse<ReviewDTO> emptyResponse = new VectorSearchResponse<>();
            emptyResponse.setDataList(new ArrayList<>());
            return emptyResponse;
        }
    }
}

