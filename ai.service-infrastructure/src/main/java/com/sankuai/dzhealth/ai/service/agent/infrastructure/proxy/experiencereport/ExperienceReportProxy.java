package com.sankuai.dzhealth.ai.service.agent.infrastructure.proxy.experiencereport;

import com.meituan.mdp.boot.starter.pigeon.annotation.MdpPigeonClient;
import com.meituan.mdp.boot.starter.thrift.annotation.MdpThriftClient;
import com.sankuai.beautycontent.experience.api.ExperienceReportService;
import com.sankuai.beautycontent.experience.dto.ExperienceReportDTOResponse;
import com.sankuai.beautycontent.experience.dto.ExperienceReportVO;
import com.sankuai.beautycontent.experience.request.ExperienceReportRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.stereotype.Service;

/**
 * 体验报告模块 RPC 代理
 * <p>
 * 基础设施层代理，负责封装对 {@link ExperienceReportService#experienceReport(ExperienceReportRequest)} 的 RPC 调用，
 * 处理响应状态并返回业务数据。
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class ExperienceReportProxy {

    /**
     * 远程 RPC 客户端
     * <p>remoteAppKey 使用 experience-function 服务提供方的应用标识</p>
     */
    @MdpThriftClient(remoteAppKey = "com.sankuai.beautycontent.function",timeout = 5000)
    private ExperienceReportService experienceReportService;

    /**
     * 查询体验报告概要信息
     * <p>
     * 调用远程RPC服务，处理响应状态，成功时返回业务数据，失败时返回null
     *
     * @param request 请求参数
     * @return 体验报告业务数据；若调用异常、响应失败或数据为空，则返回 null
     */
    public ExperienceReportVO experienceReport(ExperienceReportRequest request) {
        log.info("ExperienceReportProxy.experienceReport 开始调用RPC服务, request={}", request);

        if (request == null) {
            log.warn("ExperienceReportProxy.experienceReport 请求参数为空");
            return null;
        }

        try {
            ExperienceReportDTOResponse response = experienceReportService.experienceReport(request);
            log.debug("ExperienceReportProxy.experienceReport RPC调用完成, response={}", response);

            if (response == null) {
                log.warn("ExperienceReportProxy.experienceReport RPC响应为空");
                return null;
            }

            // 检查响应状态
            if (!BooleanUtils.isTrue(response.getSuccess())) {
                log.warn("ExperienceReportProxy.experienceReport RPC调用失败, code={}, msg={}",
                        response.getCode(), response.getMsg());
                return null;
            }

            // 返回业务数据
            ExperienceReportVO data = response.getData();
            log.info("ExperienceReportProxy.experienceReport 调用成功, data={}", data);
            return data;

        } catch (Exception e) {
            log.error("ExperienceReportProxy.experienceReport RPC调用异常, request={}", request, e);
            return null;
        }
    }
} 