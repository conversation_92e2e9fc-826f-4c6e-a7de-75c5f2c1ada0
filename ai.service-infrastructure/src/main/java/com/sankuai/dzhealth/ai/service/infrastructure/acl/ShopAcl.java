package com.sankuai.dzhealth.ai.service.infrastructure.acl;

import com.alibaba.fastjson.JSON;
import com.dianping.poi.relation.service.api.PoiRelationService;
import com.dianping.poi.relation.service.dto.AdvancedPoiPairDTO;
import com.meituan.mdp.boot.starter.pigeon.annotation.MdpPigeonClient;
import com.meituan.mdp.boot.starter.thrift.annotation.MdpThriftClient;
import com.sankuai.sinai.data.api.dto.MtPoiDTO;
import com.sankuai.sinai.data.api.service.MtPoiService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * @author:chenwei
 * @time: 2025/4/3 10:20
 * @version: 0.0.1
 */

@Service
@Slf4j
public class ShopAcl {

    @MdpPigeonClient(url = "com.dianping.poi.relation.service.api.PoiRelationService", testTimeout = 5000, timeout = 1000)
    private PoiRelationService poiRelationService;

    @MdpThriftClient(remoteAppKey = "com.sankuai.sinai.data.query", testTimeout = 5000, timeout = 1000)
    private MtPoiService mtPoiService;

    private static final List<String> FIELDS = Arrays.asList("mtPoiId", "name", "dpCityId", "dpPoiId", "poiType", "address", "branchName", "closeStatus","latitude","longitude","businessHours");


    public Map<Long, MtPoiDTO> getMtShops(List<Long> mtShopIds) {
        try {
            if (CollectionUtils.isEmpty(mtShopIds)) {
                return Collections.emptyMap();
            }
            if (mtShopIds.size() > 50) {
                throw new RuntimeException("shopIds size must less than 50");
            }
            return mtPoiService.findPoisById(mtShopIds, FIELDS);
        } catch (Exception e) {
            log.error("getMtShops, mtShopIds={}", JSON.toJSONString(mtShopIds), e);
            return new HashMap<>();
        }
    }

    public MtPoiDTO getMtShop(Long mtShopId) {
        Map<Long, MtPoiDTO> mtShops = getMtShops(Collections.singletonList(mtShopId));
        if (MapUtils.isEmpty(mtShops)) {
            log.info("getMtShop is null, mtShopId = {}", mtShopId);
            return null;
        }
        return mtShops.get(mtShopId);
    }

    public Long queryMtIdByDpId(Long dpShopId) {
        try {
            AdvancedPoiPairDTO advancedPoiPairDTO = poiRelationService.queryPoiPairByDpIdL(dpShopId);
            if (Objects.isNull(advancedPoiPairDTO)) {
                throw new Exception("queryMtIdByDpId error. 该商户不存在");
            }
            return advancedPoiPairDTO.getMtId();
        } catch (Exception e) {
            log.error("[ShopAcl.queryMtIdByDpId] dpShopId={}", dpShopId, e);
        }
        return 0L;
    }

    /**
     * 获取医院名称，作为海马配置的兜底
     * @param mtShopId 美团商户ID
     * @return 医院名称，如果获取失败返回null
     */
    public String getHospitalName(Long mtShopId) {
        try {
            if (mtShopId == null || mtShopId <= 0) {
                log.warn("getHospitalName: invalid mtShopId={}", mtShopId);
                return null;
            }

            MtPoiDTO mtShop = getMtShop(mtShopId);
            if (mtShop != null && mtShop.getName() != null) {
                log.info("通过ShopAcl获取到医院名称: mtShopId={}, name={}", mtShopId, mtShop.getName());
                return mtShop.getName();
            } else {
                log.warn("ShopAcl未获取到医院信息: mtShopId={}", mtShopId);
                return null;
            }
        } catch (Exception e) {
            log.error("getHospitalName error: mtShopId={}", mtShopId, e);
            return null;
        }
    }
}
