package com.sankuai.dzhealth.ai.service.agent.infrastructure.dal.entity;

import lombok.*;
import lombok.experimental.SuperBuilder;

/**
 *
 *   表名: chat_session_message
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
public class ChatSessionMessageDOWithBLOBs extends ChatSessionMessageDO {
    /**
     *   字段: content
     *   说明: 消息内容
     */
    private String content;

    /**
     *   字段: memory_snapshot
     *   说明: 记忆快照
     */
    private String memorySnapshot;

    /**
     *   字段: extra
     *   说明: 扩展数据
     */
    private String extra;
}