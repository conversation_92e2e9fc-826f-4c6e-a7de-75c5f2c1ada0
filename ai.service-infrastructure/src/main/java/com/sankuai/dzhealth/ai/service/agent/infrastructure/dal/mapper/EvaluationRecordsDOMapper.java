package com.sankuai.dzhealth.ai.service.agent.infrastructure.dal.mapper;

import com.meituan.mdp.mybatis.mapper.MybatisBLOBsMapper;
import com.sankuai.dzhealth.ai.service.agent.infrastructure.dal.entity.EvaluationRecordsDO;
import com.sankuai.dzhealth.ai.service.agent.infrastructure.dal.example.EvaluationRecordsDOExample;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface EvaluationRecordsDOMapper extends MybatisBLOBsMapper<EvaluationRecordsDO, EvaluationRecordsDOExample, Long> {
    /**
     * 按创建时间倒序查询测评列表
     * @param limit 限制返回记录数
     * @param offset 偏移量
     * @return 测评记录列表
     */
    List<EvaluationRecordsDO> selectAllOrderByCreateTimeDesc(@Param("limit") int limit, @Param("offset") int offset);
}