package com.sankuai.dzhealth.ai.service.agent.infrastructure.dal.mapper;

import com.meituan.mdp.mybatis.mapper.MybatisBLOBsMapper;
import com.sankuai.dzhealth.ai.service.agent.infrastructure.dal.entity.ChatSessionDO;
import com.sankuai.dzhealth.ai.service.agent.infrastructure.dal.entity.ChatSessionDOWithBLOBs;
import com.sankuai.dzhealth.ai.service.agent.infrastructure.dal.example.ChatSessionDOExample;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ChatSessionDOMapper extends MybatisBLOBsMapper<ChatSessionDO, ChatSessionDOExample, Long> {

    int updateByExampleSelective(@Param("row") ChatSessionDO row, @Param("example") ChatSessionDOExample example);

    int batchInsert(@Param("list") List<ChatSessionDOWithBLOBs> rows);
}