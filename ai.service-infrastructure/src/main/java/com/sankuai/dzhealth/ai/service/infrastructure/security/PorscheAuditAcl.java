package com.sankuai.dzhealth.ai.service.infrastructure.security;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.dianping.credit.access.api.RiskInfoMultiService;
import com.meituan.mdp.boot.starter.pigeon.annotation.MdpPigeonClient;
import com.meituan.mdp.boot.starter.thrift.annotation.MdpThriftClient;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.sankuai.dzhealth.ai.service.infrastructure.observability.Span;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR> youtianyu
 * @version : 0.1
 * @since : 2025/3/31
 */
@Component
@Slf4j
public class PorscheAuditAcl {
    @MdpPigeonClient(url = "com.dianping.credit.access.api.RiskInfoMultiService", timeout = 1000)
    private RiskInfoMultiService riskInfoMultiService;

    @MethodDoc(name = "保时洁审核", description = "报错默认返回通过")
    public AuditResult audit(AuditRequest request) {
        Transaction transaction = Cat.newTransaction("PorscheAuditAcl", "audit");
        transaction.setSuccessStatus();
        long start = System.currentTimeMillis();
        try {
            String requestJson = JSON.toJSONString(request);
            Map<String, Object> riskInfo = riskInfoMultiService.riskInfo(JSON.parseObject(requestJson));
            String resultJson = JSON.toJSONString(riskInfo);
            log.info("audit,request={},result={}", requestJson, resultJson);
            if (!Objects.equals(riskInfo.get("code"), 200)) {
                throw new IllegalStateException("保时洁审核失败:" + riskInfo.get("msg"));
            }
            JSONObject result = JSON.parseObject(resultJson);
            Span span = Span.builder()
                    .key("auditResult")
                    .value(JSON.toJSONString(result))
                    .duration(System.currentTimeMillis() - start)
                    .build();
            if (result.getJSONObject("result") == null) {
                throw new IllegalStateException("保时洁审核结果为空");
            }
            if (Objects.equals(1, result.getJSONObject("result").getInteger("status"))) {
                return AuditResult.builder().advice(2).msg("保时洁人审中").span(span).build();
            }
            if (result.getJSONObject("result").getInteger("advice") == null ||
                !Arrays.asList(1, 2).contains(result.getJSONObject("result").getInteger("advice"))) {
                throw new IllegalStateException("保时洁审核结果为空");
            }
            return AuditResult.builder()
                    .advice(result.getJSONObject("result").getInteger("advice"))
                    .msg(result.getString("msg"))
                    .span(span)
                    .build();
        } catch (Exception e) {
            log.error("audit,request={},e={}", JSON.toJSONString(request), e);
            transaction.setStatus(e);
            return AuditResult.builder().advice(1).msg("保时洁审核异常:" + e.getMessage()).build();
        } finally {
            transaction.complete();
        }
    }
}
