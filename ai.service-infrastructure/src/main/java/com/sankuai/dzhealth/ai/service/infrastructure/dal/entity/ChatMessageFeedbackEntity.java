package com.sankuai.dzhealth.ai.service.infrastructure.dal.entity;

import java.util.Date;
import lombok.*;

/**
 *
 *   表名: chat_message_feedback
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ChatMessageFeedbackEntity {
    /**
     *   字段: id
     *   说明: 主键
     */
    private Long id;

    /**
     *   字段: message_id
     *   说明: 消息ID
     */
    private String messageId;

    /**
     *   字段: user_id
     *   说明: 用户ID
     */
    private String userId;

    /**
     *   字段: feedback_type
     *   说明: 反馈类型(1:赞 2:踩)
     */
    private Byte feedbackType;

    /**
     *   字段: created_at
     */
    private Date createdAt;
}