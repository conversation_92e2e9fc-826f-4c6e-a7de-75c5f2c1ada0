package com.sankuai.dzhealth.ai.service.infrastructure.dal.mapper;

import com.meituan.mdp.mybatis.mapper.MybatisBaseMapper;
import com.sankuai.dzhealth.ai.service.infrastructure.dal.entity.DecisionNodeDO;
import com.sankuai.dzhealth.ai.service.infrastructure.dal.example.DecisionNodeEntityExample;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface DecisionNodeEntityMapper extends MybatisBaseMapper<DecisionNodeDO, DecisionNodeEntityExample, Long> {

    /**
     * 批量插入节点
     */
    int batchInsert(@Param("list") List<DecisionNodeDO> entities);

    /**
     * 批量更新节点（根据nodeId）
     */
    int batchUpdateByNodeId(@Param("list") List<DecisionNodeDO> entities);
}