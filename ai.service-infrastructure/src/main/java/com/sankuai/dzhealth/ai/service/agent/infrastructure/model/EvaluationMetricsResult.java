package com.sankuai.dzhealth.ai.service.agent.infrastructure.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.math.BigDecimal;
import java.util.List;

/**
 * 评估指标结果
 */
@SuperBuilder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class EvaluationMetricsResult {

    private List<Metric> metrics;

    /**
     * 评估指标
     */
    @SuperBuilder
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Metric {
        /**
         * 评估项
         */
        private String item;
        /**
         * 评估项名称
         */
        private String description;
        /**
         * 理由
         */
        private String reason;
        /**
         * 分值
         */
        private BigDecimal score;
        /**
         * 是否通过
         */
        private Boolean pass;
    }
}
