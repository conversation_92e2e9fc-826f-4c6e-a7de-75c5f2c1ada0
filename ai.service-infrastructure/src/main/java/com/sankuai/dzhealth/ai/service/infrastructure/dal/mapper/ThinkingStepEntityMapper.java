package com.sankuai.dzhealth.ai.service.infrastructure.dal.mapper;

import com.meituan.mdp.mybatis.mapper.MybatisBaseMapper;
import com.sankuai.dzhealth.ai.service.infrastructure.dal.entity.ThinkingStepEntity;
import com.sankuai.dzhealth.ai.service.infrastructure.dal.example.ThinkingStepEntityExample;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ThinkingStepEntityMapper extends MybatisBaseMapper<ThinkingStepEntity, ThinkingStepEntityExample, Long> {
    long countByExample(ThinkingStepEntityExample example);

    int deleteByExample(ThinkingStepEntityExample example);

    int deleteByPrimaryKey(Long id);

    int insert(ThinkingStepEntity record);

    int insertSelective(ThinkingStepEntity record);

    List<ThinkingStepEntity> selectByExample(ThinkingStepEntityExample example);

    ThinkingStepEntity selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") ThinkingStepEntity record, @Param("example") ThinkingStepEntityExample example);

    int updateByExample(@Param("record") ThinkingStepEntity record, @Param("example") ThinkingStepEntityExample example);

    int updateByPrimaryKeySelective(ThinkingStepEntity record);

    int updateByPrimaryKey(ThinkingStepEntity record);
    /**
     * 根据会话ID查询思考步骤列表
     *
     * @param sessionId 会话ID
     * @return 思考步骤列表
     */
    default List<ThinkingStepEntity> selectBySessionId(Long sessionId) {
        ThinkingStepEntityExample example = new ThinkingStepEntityExample();
        example.createCriteria().andSessionIdEqualTo(sessionId);
        example.setOrderByClause("step_number ASC");
        return selectByExample(example);
    }
}