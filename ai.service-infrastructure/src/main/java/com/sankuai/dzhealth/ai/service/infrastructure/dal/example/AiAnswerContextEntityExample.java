package com.sankuai.dzhealth.ai.service.infrastructure.dal.example;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class AiAnswerContextEntityExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public AiAnswerContextEntityExample() {
        oredCriteria = new ArrayList<>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andUseridIsNull() {
            addCriterion("userId is null");
            return (Criteria) this;
        }

        public Criteria andUseridIsNotNull() {
            addCriterion("userId is not null");
            return (Criteria) this;
        }

        public Criteria andUseridEqualTo(String value) {
            addCriterion("userId =", value, "userid");
            return (Criteria) this;
        }

        public Criteria andUseridNotEqualTo(String value) {
            addCriterion("userId <>", value, "userid");
            return (Criteria) this;
        }

        public Criteria andUseridGreaterThan(String value) {
            addCriterion("userId >", value, "userid");
            return (Criteria) this;
        }

        public Criteria andUseridGreaterThanOrEqualTo(String value) {
            addCriterion("userId >=", value, "userid");
            return (Criteria) this;
        }

        public Criteria andUseridLessThan(String value) {
            addCriterion("userId <", value, "userid");
            return (Criteria) this;
        }

        public Criteria andUseridLessThanOrEqualTo(String value) {
            addCriterion("userId <=", value, "userid");
            return (Criteria) this;
        }

        public Criteria andUseridLike(String value) {
            addCriterion("userId like", value, "userid");
            return (Criteria) this;
        }

        public Criteria andUseridNotLike(String value) {
            addCriterion("userId not like", value, "userid");
            return (Criteria) this;
        }

        public Criteria andUseridIn(List<String> values) {
            addCriterion("userId in", values, "userid");
            return (Criteria) this;
        }

        public Criteria andUseridNotIn(List<String> values) {
            addCriterion("userId not in", values, "userid");
            return (Criteria) this;
        }

        public Criteria andUseridBetween(String value1, String value2) {
            addCriterion("userId between", value1, value2, "userid");
            return (Criteria) this;
        }

        public Criteria andUseridNotBetween(String value1, String value2) {
            addCriterion("userId not between", value1, value2, "userid");
            return (Criteria) this;
        }

        public Criteria andLatIsNull() {
            addCriterion("lat is null");
            return (Criteria) this;
        }

        public Criteria andLatIsNotNull() {
            addCriterion("lat is not null");
            return (Criteria) this;
        }

        public Criteria andLatEqualTo(Double value) {
            addCriterion("lat =", value, "lat");
            return (Criteria) this;
        }

        public Criteria andLatNotEqualTo(Double value) {
            addCriterion("lat <>", value, "lat");
            return (Criteria) this;
        }

        public Criteria andLatGreaterThan(Double value) {
            addCriterion("lat >", value, "lat");
            return (Criteria) this;
        }

        public Criteria andLatGreaterThanOrEqualTo(Double value) {
            addCriterion("lat >=", value, "lat");
            return (Criteria) this;
        }

        public Criteria andLatLessThan(Double value) {
            addCriterion("lat <", value, "lat");
            return (Criteria) this;
        }

        public Criteria andLatLessThanOrEqualTo(Double value) {
            addCriterion("lat <=", value, "lat");
            return (Criteria) this;
        }

        public Criteria andLatIn(List<Double> values) {
            addCriterion("lat in", values, "lat");
            return (Criteria) this;
        }

        public Criteria andLatNotIn(List<Double> values) {
            addCriterion("lat not in", values, "lat");
            return (Criteria) this;
        }

        public Criteria andLatBetween(Double value1, Double value2) {
            addCriterion("lat between", value1, value2, "lat");
            return (Criteria) this;
        }

        public Criteria andLatNotBetween(Double value1, Double value2) {
            addCriterion("lat not between", value1, value2, "lat");
            return (Criteria) this;
        }

        public Criteria andLngIsNull() {
            addCriterion("lng is null");
            return (Criteria) this;
        }

        public Criteria andLngIsNotNull() {
            addCriterion("lng is not null");
            return (Criteria) this;
        }

        public Criteria andLngEqualTo(Double value) {
            addCriterion("lng =", value, "lng");
            return (Criteria) this;
        }

        public Criteria andLngNotEqualTo(Double value) {
            addCriterion("lng <>", value, "lng");
            return (Criteria) this;
        }

        public Criteria andLngGreaterThan(Double value) {
            addCriterion("lng >", value, "lng");
            return (Criteria) this;
        }

        public Criteria andLngGreaterThanOrEqualTo(Double value) {
            addCriterion("lng >=", value, "lng");
            return (Criteria) this;
        }

        public Criteria andLngLessThan(Double value) {
            addCriterion("lng <", value, "lng");
            return (Criteria) this;
        }

        public Criteria andLngLessThanOrEqualTo(Double value) {
            addCriterion("lng <=", value, "lng");
            return (Criteria) this;
        }

        public Criteria andLngIn(List<Double> values) {
            addCriterion("lng in", values, "lng");
            return (Criteria) this;
        }

        public Criteria andLngNotIn(List<Double> values) {
            addCriterion("lng not in", values, "lng");
            return (Criteria) this;
        }

        public Criteria andLngBetween(Double value1, Double value2) {
            addCriterion("lng between", value1, value2, "lng");
            return (Criteria) this;
        }

        public Criteria andLngNotBetween(Double value1, Double value2) {
            addCriterion("lng not between", value1, value2, "lng");
            return (Criteria) this;
        }

        public Criteria andClienttypeIsNull() {
            addCriterion("clientType is null");
            return (Criteria) this;
        }

        public Criteria andClienttypeIsNotNull() {
            addCriterion("clientType is not null");
            return (Criteria) this;
        }

        public Criteria andClienttypeEqualTo(String value) {
            addCriterion("clientType =", value, "clienttype");
            return (Criteria) this;
        }

        public Criteria andClienttypeNotEqualTo(String value) {
            addCriterion("clientType <>", value, "clienttype");
            return (Criteria) this;
        }

        public Criteria andClienttypeGreaterThan(String value) {
            addCriterion("clientType >", value, "clienttype");
            return (Criteria) this;
        }

        public Criteria andClienttypeGreaterThanOrEqualTo(String value) {
            addCriterion("clientType >=", value, "clienttype");
            return (Criteria) this;
        }

        public Criteria andClienttypeLessThan(String value) {
            addCriterion("clientType <", value, "clienttype");
            return (Criteria) this;
        }

        public Criteria andClienttypeLessThanOrEqualTo(String value) {
            addCriterion("clientType <=", value, "clienttype");
            return (Criteria) this;
        }

        public Criteria andClienttypeLike(String value) {
            addCriterion("clientType like", value, "clienttype");
            return (Criteria) this;
        }

        public Criteria andClienttypeNotLike(String value) {
            addCriterion("clientType not like", value, "clienttype");
            return (Criteria) this;
        }

        public Criteria andClienttypeIn(List<String> values) {
            addCriterion("clientType in", values, "clienttype");
            return (Criteria) this;
        }

        public Criteria andClienttypeNotIn(List<String> values) {
            addCriterion("clientType not in", values, "clienttype");
            return (Criteria) this;
        }

        public Criteria andClienttypeBetween(String value1, String value2) {
            addCriterion("clientType between", value1, value2, "clienttype");
            return (Criteria) this;
        }

        public Criteria andClienttypeNotBetween(String value1, String value2) {
            addCriterion("clientType not between", value1, value2, "clienttype");
            return (Criteria) this;
        }

        public Criteria andUuidIsNull() {
            addCriterion("uuid is null");
            return (Criteria) this;
        }

        public Criteria andUuidIsNotNull() {
            addCriterion("uuid is not null");
            return (Criteria) this;
        }

        public Criteria andUuidEqualTo(String value) {
            addCriterion("uuid =", value, "uuid");
            return (Criteria) this;
        }

        public Criteria andUuidNotEqualTo(String value) {
            addCriterion("uuid <>", value, "uuid");
            return (Criteria) this;
        }

        public Criteria andUuidGreaterThan(String value) {
            addCriterion("uuid >", value, "uuid");
            return (Criteria) this;
        }

        public Criteria andUuidGreaterThanOrEqualTo(String value) {
            addCriterion("uuid >=", value, "uuid");
            return (Criteria) this;
        }

        public Criteria andUuidLessThan(String value) {
            addCriterion("uuid <", value, "uuid");
            return (Criteria) this;
        }

        public Criteria andUuidLessThanOrEqualTo(String value) {
            addCriterion("uuid <=", value, "uuid");
            return (Criteria) this;
        }

        public Criteria andUuidLike(String value) {
            addCriterion("uuid like", value, "uuid");
            return (Criteria) this;
        }

        public Criteria andUuidNotLike(String value) {
            addCriterion("uuid not like", value, "uuid");
            return (Criteria) this;
        }

        public Criteria andUuidIn(List<String> values) {
            addCriterion("uuid in", values, "uuid");
            return (Criteria) this;
        }

        public Criteria andUuidNotIn(List<String> values) {
            addCriterion("uuid not in", values, "uuid");
            return (Criteria) this;
        }

        public Criteria andUuidBetween(String value1, String value2) {
            addCriterion("uuid between", value1, value2, "uuid");
            return (Criteria) this;
        }

        public Criteria andUuidNotBetween(String value1, String value2) {
            addCriterion("uuid not between", value1, value2, "uuid");
            return (Criteria) this;
        }

        public Criteria andAppversionIsNull() {
            addCriterion("appVersion is null");
            return (Criteria) this;
        }

        public Criteria andAppversionIsNotNull() {
            addCriterion("appVersion is not null");
            return (Criteria) this;
        }

        public Criteria andAppversionEqualTo(String value) {
            addCriterion("appVersion =", value, "appversion");
            return (Criteria) this;
        }

        public Criteria andAppversionNotEqualTo(String value) {
            addCriterion("appVersion <>", value, "appversion");
            return (Criteria) this;
        }

        public Criteria andAppversionGreaterThan(String value) {
            addCriterion("appVersion >", value, "appversion");
            return (Criteria) this;
        }

        public Criteria andAppversionGreaterThanOrEqualTo(String value) {
            addCriterion("appVersion >=", value, "appversion");
            return (Criteria) this;
        }

        public Criteria andAppversionLessThan(String value) {
            addCriterion("appVersion <", value, "appversion");
            return (Criteria) this;
        }

        public Criteria andAppversionLessThanOrEqualTo(String value) {
            addCriterion("appVersion <=", value, "appversion");
            return (Criteria) this;
        }

        public Criteria andAppversionLike(String value) {
            addCriterion("appVersion like", value, "appversion");
            return (Criteria) this;
        }

        public Criteria andAppversionNotLike(String value) {
            addCriterion("appVersion not like", value, "appversion");
            return (Criteria) this;
        }

        public Criteria andAppversionIn(List<String> values) {
            addCriterion("appVersion in", values, "appversion");
            return (Criteria) this;
        }

        public Criteria andAppversionNotIn(List<String> values) {
            addCriterion("appVersion not in", values, "appversion");
            return (Criteria) this;
        }

        public Criteria andAppversionBetween(String value1, String value2) {
            addCriterion("appVersion between", value1, value2, "appversion");
            return (Criteria) this;
        }

        public Criteria andAppversionNotBetween(String value1, String value2) {
            addCriterion("appVersion not between", value1, value2, "appversion");
            return (Criteria) this;
        }

        public Criteria andCityidIsNull() {
            addCriterion("cityId is null");
            return (Criteria) this;
        }

        public Criteria andCityidIsNotNull() {
            addCriterion("cityId is not null");
            return (Criteria) this;
        }

        public Criteria andCityidEqualTo(Integer value) {
            addCriterion("cityId =", value, "cityid");
            return (Criteria) this;
        }

        public Criteria andCityidNotEqualTo(Integer value) {
            addCriterion("cityId <>", value, "cityid");
            return (Criteria) this;
        }

        public Criteria andCityidGreaterThan(Integer value) {
            addCriterion("cityId >", value, "cityid");
            return (Criteria) this;
        }

        public Criteria andCityidGreaterThanOrEqualTo(Integer value) {
            addCriterion("cityId >=", value, "cityid");
            return (Criteria) this;
        }

        public Criteria andCityidLessThan(Integer value) {
            addCriterion("cityId <", value, "cityid");
            return (Criteria) this;
        }

        public Criteria andCityidLessThanOrEqualTo(Integer value) {
            addCriterion("cityId <=", value, "cityid");
            return (Criteria) this;
        }

        public Criteria andCityidIn(List<Integer> values) {
            addCriterion("cityId in", values, "cityid");
            return (Criteria) this;
        }

        public Criteria andCityidNotIn(List<Integer> values) {
            addCriterion("cityId not in", values, "cityid");
            return (Criteria) this;
        }

        public Criteria andCityidBetween(Integer value1, Integer value2) {
            addCriterion("cityId between", value1, value2, "cityid");
            return (Criteria) this;
        }

        public Criteria andCityidNotBetween(Integer value1, Integer value2) {
            addCriterion("cityId not between", value1, value2, "cityid");
            return (Criteria) this;
        }

        public Criteria andSessionidIsNull() {
            addCriterion("sessionId is null");
            return (Criteria) this;
        }

        public Criteria andSessionidIsNotNull() {
            addCriterion("sessionId is not null");
            return (Criteria) this;
        }

        public Criteria andSessionidEqualTo(Long value) {
            addCriterion("sessionId =", value, "sessionid");
            return (Criteria) this;
        }

        public Criteria andSessionidNotEqualTo(Long value) {
            addCriterion("sessionId <>", value, "sessionid");
            return (Criteria) this;
        }

        public Criteria andSessionidGreaterThan(Long value) {
            addCriterion("sessionId >", value, "sessionid");
            return (Criteria) this;
        }

        public Criteria andSessionidGreaterThanOrEqualTo(Long value) {
            addCriterion("sessionId >=", value, "sessionid");
            return (Criteria) this;
        }

        public Criteria andSessionidLessThan(Long value) {
            addCriterion("sessionId <", value, "sessionid");
            return (Criteria) this;
        }

        public Criteria andSessionidLessThanOrEqualTo(Long value) {
            addCriterion("sessionId <=", value, "sessionid");
            return (Criteria) this;
        }

        public Criteria andSessionidIn(List<Long> values) {
            addCriterion("sessionId in", values, "sessionid");
            return (Criteria) this;
        }

        public Criteria andSessionidNotIn(List<Long> values) {
            addCriterion("sessionId not in", values, "sessionid");
            return (Criteria) this;
        }

        public Criteria andSessionidBetween(Long value1, Long value2) {
            addCriterion("sessionId between", value1, value2, "sessionid");
            return (Criteria) this;
        }

        public Criteria andSessionidNotBetween(Long value1, Long value2) {
            addCriterion("sessionId not between", value1, value2, "sessionid");
            return (Criteria) this;
        }

        public Criteria andMsgidIsNull() {
            addCriterion("msgId is null");
            return (Criteria) this;
        }

        public Criteria andMsgidIsNotNull() {
            addCriterion("msgId is not null");
            return (Criteria) this;
        }

        public Criteria andMsgidEqualTo(Long value) {
            addCriterion("msgId =", value, "msgid");
            return (Criteria) this;
        }

        public Criteria andMsgidNotEqualTo(Long value) {
            addCriterion("msgId <>", value, "msgid");
            return (Criteria) this;
        }

        public Criteria andMsgidGreaterThan(Long value) {
            addCriterion("msgId >", value, "msgid");
            return (Criteria) this;
        }

        public Criteria andMsgidGreaterThanOrEqualTo(Long value) {
            addCriterion("msgId >=", value, "msgid");
            return (Criteria) this;
        }

        public Criteria andMsgidLessThan(Long value) {
            addCriterion("msgId <", value, "msgid");
            return (Criteria) this;
        }

        public Criteria andMsgidLessThanOrEqualTo(Long value) {
            addCriterion("msgId <=", value, "msgid");
            return (Criteria) this;
        }

        public Criteria andMsgidIn(List<Long> values) {
            addCriterion("msgId in", values, "msgid");
            return (Criteria) this;
        }

        public Criteria andMsgidNotIn(List<Long> values) {
            addCriterion("msgId not in", values, "msgid");
            return (Criteria) this;
        }

        public Criteria andMsgidBetween(Long value1, Long value2) {
            addCriterion("msgId between", value1, value2, "msgid");
            return (Criteria) this;
        }

        public Criteria andMsgidNotBetween(Long value1, Long value2) {
            addCriterion("msgId not between", value1, value2, "msgid");
            return (Criteria) this;
        }

        public Criteria andRequestidIsNull() {
            addCriterion("requestId is null");
            return (Criteria) this;
        }

        public Criteria andRequestidIsNotNull() {
            addCriterion("requestId is not null");
            return (Criteria) this;
        }

        public Criteria andRequestidEqualTo(String value) {
            addCriterion("requestId =", value, "requestid");
            return (Criteria) this;
        }

        public Criteria andRequestidNotEqualTo(String value) {
            addCriterion("requestId <>", value, "requestid");
            return (Criteria) this;
        }

        public Criteria andRequestidGreaterThan(String value) {
            addCriterion("requestId >", value, "requestid");
            return (Criteria) this;
        }

        public Criteria andRequestidGreaterThanOrEqualTo(String value) {
            addCriterion("requestId >=", value, "requestid");
            return (Criteria) this;
        }

        public Criteria andRequestidLessThan(String value) {
            addCriterion("requestId <", value, "requestid");
            return (Criteria) this;
        }

        public Criteria andRequestidLessThanOrEqualTo(String value) {
            addCriterion("requestId <=", value, "requestid");
            return (Criteria) this;
        }

        public Criteria andRequestidLike(String value) {
            addCriterion("requestId like", value, "requestid");
            return (Criteria) this;
        }

        public Criteria andRequestidNotLike(String value) {
            addCriterion("requestId not like", value, "requestid");
            return (Criteria) this;
        }

        public Criteria andRequestidIn(List<String> values) {
            addCriterion("requestId in", values, "requestid");
            return (Criteria) this;
        }

        public Criteria andRequestidNotIn(List<String> values) {
            addCriterion("requestId not in", values, "requestid");
            return (Criteria) this;
        }

        public Criteria andRequestidBetween(String value1, String value2) {
            addCriterion("requestId between", value1, value2, "requestid");
            return (Criteria) this;
        }

        public Criteria andRequestidNotBetween(String value1, String value2) {
            addCriterion("requestId not between", value1, value2, "requestid");
            return (Criteria) this;
        }

        public Criteria andExtensionIsNull() {
            addCriterion("extension is null");
            return (Criteria) this;
        }

        public Criteria andExtensionIsNotNull() {
            addCriterion("extension is not null");
            return (Criteria) this;
        }

        public Criteria andExtensionEqualTo(String value) {
            addCriterion("extension =", value, "extension");
            return (Criteria) this;
        }

        public Criteria andExtensionNotEqualTo(String value) {
            addCriterion("extension <>", value, "extension");
            return (Criteria) this;
        }

        public Criteria andExtensionGreaterThan(String value) {
            addCriterion("extension >", value, "extension");
            return (Criteria) this;
        }

        public Criteria andExtensionGreaterThanOrEqualTo(String value) {
            addCriterion("extension >=", value, "extension");
            return (Criteria) this;
        }

        public Criteria andExtensionLessThan(String value) {
            addCriterion("extension <", value, "extension");
            return (Criteria) this;
        }

        public Criteria andExtensionLessThanOrEqualTo(String value) {
            addCriterion("extension <=", value, "extension");
            return (Criteria) this;
        }

        public Criteria andExtensionLike(String value) {
            addCriterion("extension like", value, "extension");
            return (Criteria) this;
        }

        public Criteria andExtensionNotLike(String value) {
            addCriterion("extension not like", value, "extension");
            return (Criteria) this;
        }

        public Criteria andExtensionIn(List<String> values) {
            addCriterion("extension in", values, "extension");
            return (Criteria) this;
        }

        public Criteria andExtensionNotIn(List<String> values) {
            addCriterion("extension not in", values, "extension");
            return (Criteria) this;
        }

        public Criteria andExtensionBetween(String value1, String value2) {
            addCriterion("extension between", value1, value2, "extension");
            return (Criteria) this;
        }

        public Criteria andExtensionNotBetween(String value1, String value2) {
            addCriterion("extension not between", value1, value2, "extension");
            return (Criteria) this;
        }

        public Criteria andBizsceneIsNull() {
            addCriterion("bizScene is null");
            return (Criteria) this;
        }

        public Criteria andBizsceneIsNotNull() {
            addCriterion("bizScene is not null");
            return (Criteria) this;
        }

        public Criteria andBizsceneEqualTo(String value) {
            addCriterion("bizScene =", value, "bizscene");
            return (Criteria) this;
        }

        public Criteria andBizsceneNotEqualTo(String value) {
            addCriterion("bizScene <>", value, "bizscene");
            return (Criteria) this;
        }

        public Criteria andBizsceneGreaterThan(String value) {
            addCriterion("bizScene >", value, "bizscene");
            return (Criteria) this;
        }

        public Criteria andBizsceneGreaterThanOrEqualTo(String value) {
            addCriterion("bizScene >=", value, "bizscene");
            return (Criteria) this;
        }

        public Criteria andBizsceneLessThan(String value) {
            addCriterion("bizScene <", value, "bizscene");
            return (Criteria) this;
        }

        public Criteria andBizsceneLessThanOrEqualTo(String value) {
            addCriterion("bizScene <=", value, "bizscene");
            return (Criteria) this;
        }

        public Criteria andBizsceneLike(String value) {
            addCriterion("bizScene like", value, "bizscene");
            return (Criteria) this;
        }

        public Criteria andBizsceneNotLike(String value) {
            addCriterion("bizScene not like", value, "bizscene");
            return (Criteria) this;
        }

        public Criteria andBizsceneIn(List<String> values) {
            addCriterion("bizScene in", values, "bizscene");
            return (Criteria) this;
        }

        public Criteria andBizsceneNotIn(List<String> values) {
            addCriterion("bizScene not in", values, "bizscene");
            return (Criteria) this;
        }

        public Criteria andBizsceneBetween(String value1, String value2) {
            addCriterion("bizScene between", value1, value2, "bizscene");
            return (Criteria) this;
        }

        public Criteria andBizsceneNotBetween(String value1, String value2) {
            addCriterion("bizScene not between", value1, value2, "bizscene");
            return (Criteria) this;
        }

        public Criteria andRequesttimeIsNull() {
            addCriterion("requestTime is null");
            return (Criteria) this;
        }

        public Criteria andRequesttimeIsNotNull() {
            addCriterion("requestTime is not null");
            return (Criteria) this;
        }

        public Criteria andRequesttimeEqualTo(String value) {
            addCriterion("requestTime =", value, "requesttime");
            return (Criteria) this;
        }

        public Criteria andRequesttimeNotEqualTo(String value) {
            addCriterion("requestTime <>", value, "requesttime");
            return (Criteria) this;
        }

        public Criteria andRequesttimeGreaterThan(String value) {
            addCriterion("requestTime >", value, "requesttime");
            return (Criteria) this;
        }

        public Criteria andRequesttimeGreaterThanOrEqualTo(String value) {
            addCriterion("requestTime >=", value, "requesttime");
            return (Criteria) this;
        }

        public Criteria andRequesttimeLessThan(String value) {
            addCriterion("requestTime <", value, "requesttime");
            return (Criteria) this;
        }

        public Criteria andRequesttimeLessThanOrEqualTo(String value) {
            addCriterion("requestTime <=", value, "requesttime");
            return (Criteria) this;
        }

        public Criteria andRequesttimeLike(String value) {
            addCriterion("requestTime like", value, "requesttime");
            return (Criteria) this;
        }

        public Criteria andRequesttimeNotLike(String value) {
            addCriterion("requestTime not like", value, "requesttime");
            return (Criteria) this;
        }

        public Criteria andRequesttimeIn(List<String> values) {
            addCriterion("requestTime in", values, "requesttime");
            return (Criteria) this;
        }

        public Criteria andRequesttimeNotIn(List<String> values) {
            addCriterion("requestTime not in", values, "requesttime");
            return (Criteria) this;
        }

        public Criteria andRequesttimeBetween(String value1, String value2) {
            addCriterion("requestTime between", value1, value2, "requesttime");
            return (Criteria) this;
        }

        public Criteria andRequesttimeNotBetween(String value1, String value2) {
            addCriterion("requestTime not between", value1, value2, "requesttime");
            return (Criteria) this;
        }

        public Criteria andTypeIsNull() {
            addCriterion("type is null");
            return (Criteria) this;
        }

        public Criteria andTypeIsNotNull() {
            addCriterion("type is not null");
            return (Criteria) this;
        }

        public Criteria andTypeEqualTo(Integer value) {
            addCriterion("type =", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotEqualTo(Integer value) {
            addCriterion("type <>", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeGreaterThan(Integer value) {
            addCriterion("type >", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("type >=", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeLessThan(Integer value) {
            addCriterion("type <", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeLessThanOrEqualTo(Integer value) {
            addCriterion("type <=", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeIn(List<Integer> values) {
            addCriterion("type in", values, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotIn(List<Integer> values) {
            addCriterion("type not in", values, "type");
            return (Criteria) this;
        }

        public Criteria andTypeBetween(Integer value1, Integer value2) {
            addCriterion("type between", value1, value2, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("type not between", value1, value2, "type");
            return (Criteria) this;
        }

        public Criteria andRoleIsNull() {
            addCriterion("role is null");
            return (Criteria) this;
        }

        public Criteria andRoleIsNotNull() {
            addCriterion("role is not null");
            return (Criteria) this;
        }

        public Criteria andRoleEqualTo(String value) {
            addCriterion("role =", value, "role");
            return (Criteria) this;
        }

        public Criteria andRoleNotEqualTo(String value) {
            addCriterion("role <>", value, "role");
            return (Criteria) this;
        }

        public Criteria andRoleGreaterThan(String value) {
            addCriterion("role >", value, "role");
            return (Criteria) this;
        }

        public Criteria andRoleGreaterThanOrEqualTo(String value) {
            addCriterion("role >=", value, "role");
            return (Criteria) this;
        }

        public Criteria andRoleLessThan(String value) {
            addCriterion("role <", value, "role");
            return (Criteria) this;
        }

        public Criteria andRoleLessThanOrEqualTo(String value) {
            addCriterion("role <=", value, "role");
            return (Criteria) this;
        }

        public Criteria andRoleLike(String value) {
            addCriterion("role like", value, "role");
            return (Criteria) this;
        }

        public Criteria andRoleNotLike(String value) {
            addCriterion("role not like", value, "role");
            return (Criteria) this;
        }

        public Criteria andRoleIn(List<String> values) {
            addCriterion("role in", values, "role");
            return (Criteria) this;
        }

        public Criteria andRoleNotIn(List<String> values) {
            addCriterion("role not in", values, "role");
            return (Criteria) this;
        }

        public Criteria andRoleBetween(String value1, String value2) {
            addCriterion("role between", value1, value2, "role");
            return (Criteria) this;
        }

        public Criteria andRoleNotBetween(String value1, String value2) {
            addCriterion("role not between", value1, value2, "role");
            return (Criteria) this;
        }

        public Criteria andMsgcontextIsNull() {
            addCriterion("msgContext is null");
            return (Criteria) this;
        }

        public Criteria andMsgcontextIsNotNull() {
            addCriterion("msgContext is not null");
            return (Criteria) this;
        }

        public Criteria andMsgcontextEqualTo(String value) {
            addCriterion("msgContext =", value, "msgcontext");
            return (Criteria) this;
        }

        public Criteria andMsgcontextNotEqualTo(String value) {
            addCriterion("msgContext <>", value, "msgcontext");
            return (Criteria) this;
        }

        public Criteria andMsgcontextGreaterThan(String value) {
            addCriterion("msgContext >", value, "msgcontext");
            return (Criteria) this;
        }

        public Criteria andMsgcontextGreaterThanOrEqualTo(String value) {
            addCriterion("msgContext >=", value, "msgcontext");
            return (Criteria) this;
        }

        public Criteria andMsgcontextLessThan(String value) {
            addCriterion("msgContext <", value, "msgcontext");
            return (Criteria) this;
        }

        public Criteria andMsgcontextLessThanOrEqualTo(String value) {
            addCriterion("msgContext <=", value, "msgcontext");
            return (Criteria) this;
        }

        public Criteria andMsgcontextLike(String value) {
            addCriterion("msgContext like", value, "msgcontext");
            return (Criteria) this;
        }

        public Criteria andMsgcontextNotLike(String value) {
            addCriterion("msgContext not like", value, "msgcontext");
            return (Criteria) this;
        }

        public Criteria andMsgcontextIn(List<String> values) {
            addCriterion("msgContext in", values, "msgcontext");
            return (Criteria) this;
        }

        public Criteria andMsgcontextNotIn(List<String> values) {
            addCriterion("msgContext not in", values, "msgcontext");
            return (Criteria) this;
        }

        public Criteria andMsgcontextBetween(String value1, String value2) {
            addCriterion("msgContext between", value1, value2, "msgcontext");
            return (Criteria) this;
        }

        public Criteria andMsgcontextNotBetween(String value1, String value2) {
            addCriterion("msgContext not between", value1, value2, "msgcontext");
            return (Criteria) this;
        }

        public Criteria andShopidIsNull() {
            addCriterion("shopId is null");
            return (Criteria) this;
        }

        public Criteria andShopidIsNotNull() {
            addCriterion("shopId is not null");
            return (Criteria) this;
        }

        public Criteria andShopidEqualTo(Long value) {
            addCriterion("shopId =", value, "shopid");
            return (Criteria) this;
        }

        public Criteria andShopidNotEqualTo(Long value) {
            addCriterion("shopId <>", value, "shopid");
            return (Criteria) this;
        }

        public Criteria andShopidGreaterThan(Long value) {
            addCriterion("shopId >", value, "shopid");
            return (Criteria) this;
        }

        public Criteria andShopidGreaterThanOrEqualTo(Long value) {
            addCriterion("shopId >=", value, "shopid");
            return (Criteria) this;
        }

        public Criteria andShopidLessThan(Long value) {
            addCriterion("shopId <", value, "shopid");
            return (Criteria) this;
        }

        public Criteria andShopidLessThanOrEqualTo(Long value) {
            addCriterion("shopId <=", value, "shopid");
            return (Criteria) this;
        }

        public Criteria andShopidIn(List<Long> values) {
            addCriterion("shopId in", values, "shopid");
            return (Criteria) this;
        }

        public Criteria andShopidNotIn(List<Long> values) {
            addCriterion("shopId not in", values, "shopid");
            return (Criteria) this;
        }

        public Criteria andShopidBetween(Long value1, Long value2) {
            addCriterion("shopId between", value1, value2, "shopid");
            return (Criteria) this;
        }

        public Criteria andShopidNotBetween(Long value1, Long value2) {
            addCriterion("shopId not between", value1, value2, "shopid");
            return (Criteria) this;
        }

        public Criteria andMtshopidIsNull() {
            addCriterion("mtShopId is null");
            return (Criteria) this;
        }

        public Criteria andMtshopidIsNotNull() {
            addCriterion("mtShopId is not null");
            return (Criteria) this;
        }

        public Criteria andMtshopidEqualTo(Long value) {
            addCriterion("mtShopId =", value, "mtshopid");
            return (Criteria) this;
        }

        public Criteria andMtshopidNotEqualTo(Long value) {
            addCriterion("mtShopId <>", value, "mtshopid");
            return (Criteria) this;
        }

        public Criteria andMtshopidGreaterThan(Long value) {
            addCriterion("mtShopId >", value, "mtshopid");
            return (Criteria) this;
        }

        public Criteria andMtshopidGreaterThanOrEqualTo(Long value) {
            addCriterion("mtShopId >=", value, "mtshopid");
            return (Criteria) this;
        }

        public Criteria andMtshopidLessThan(Long value) {
            addCriterion("mtShopId <", value, "mtshopid");
            return (Criteria) this;
        }

        public Criteria andMtshopidLessThanOrEqualTo(Long value) {
            addCriterion("mtShopId <=", value, "mtshopid");
            return (Criteria) this;
        }

        public Criteria andMtshopidIn(List<Long> values) {
            addCriterion("mtShopId in", values, "mtshopid");
            return (Criteria) this;
        }

        public Criteria andMtshopidNotIn(List<Long> values) {
            addCriterion("mtShopId not in", values, "mtshopid");
            return (Criteria) this;
        }

        public Criteria andMtshopidBetween(Long value1, Long value2) {
            addCriterion("mtShopId between", value1, value2, "mtshopid");
            return (Criteria) this;
        }

        public Criteria andMtshopidNotBetween(Long value1, Long value2) {
            addCriterion("mtShopId not between", value1, value2, "mtshopid");
            return (Criteria) this;
        }

        public Criteria andStreamIsNull() {
            addCriterion("stream is null");
            return (Criteria) this;
        }

        public Criteria andStreamIsNotNull() {
            addCriterion("stream is not null");
            return (Criteria) this;
        }

        public Criteria andStreamEqualTo(Boolean value) {
            addCriterion("stream =", value, "stream");
            return (Criteria) this;
        }

        public Criteria andStreamNotEqualTo(Boolean value) {
            addCriterion("stream <>", value, "stream");
            return (Criteria) this;
        }

        public Criteria andStreamGreaterThan(Boolean value) {
            addCriterion("stream >", value, "stream");
            return (Criteria) this;
        }

        public Criteria andStreamGreaterThanOrEqualTo(Boolean value) {
            addCriterion("stream >=", value, "stream");
            return (Criteria) this;
        }

        public Criteria andStreamLessThan(Boolean value) {
            addCriterion("stream <", value, "stream");
            return (Criteria) this;
        }

        public Criteria andStreamLessThanOrEqualTo(Boolean value) {
            addCriterion("stream <=", value, "stream");
            return (Criteria) this;
        }

        public Criteria andStreamIn(List<Boolean> values) {
            addCriterion("stream in", values, "stream");
            return (Criteria) this;
        }

        public Criteria andStreamNotIn(List<Boolean> values) {
            addCriterion("stream not in", values, "stream");
            return (Criteria) this;
        }

        public Criteria andStreamBetween(Boolean value1, Boolean value2) {
            addCriterion("stream between", value1, value2, "stream");
            return (Criteria) this;
        }

        public Criteria andStreamNotBetween(Boolean value1, Boolean value2) {
            addCriterion("stream not between", value1, value2, "stream");
            return (Criteria) this;
        }

        public Criteria andPlatformIsNull() {
            addCriterion("platform is null");
            return (Criteria) this;
        }

        public Criteria andPlatformIsNotNull() {
            addCriterion("platform is not null");
            return (Criteria) this;
        }

        public Criteria andPlatformEqualTo(Integer value) {
            addCriterion("platform =", value, "platform");
            return (Criteria) this;
        }

        public Criteria andPlatformNotEqualTo(Integer value) {
            addCriterion("platform <>", value, "platform");
            return (Criteria) this;
        }

        public Criteria andPlatformGreaterThan(Integer value) {
            addCriterion("platform >", value, "platform");
            return (Criteria) this;
        }

        public Criteria andPlatformGreaterThanOrEqualTo(Integer value) {
            addCriterion("platform >=", value, "platform");
            return (Criteria) this;
        }

        public Criteria andPlatformLessThan(Integer value) {
            addCriterion("platform <", value, "platform");
            return (Criteria) this;
        }

        public Criteria andPlatformLessThanOrEqualTo(Integer value) {
            addCriterion("platform <=", value, "platform");
            return (Criteria) this;
        }

        public Criteria andPlatformIn(List<Integer> values) {
            addCriterion("platform in", values, "platform");
            return (Criteria) this;
        }

        public Criteria andPlatformNotIn(List<Integer> values) {
            addCriterion("platform not in", values, "platform");
            return (Criteria) this;
        }

        public Criteria andPlatformBetween(Integer value1, Integer value2) {
            addCriterion("platform between", value1, value2, "platform");
            return (Criteria) this;
        }

        public Criteria andPlatformNotBetween(Integer value1, Integer value2) {
            addCriterion("platform not between", value1, value2, "platform");
            return (Criteria) this;
        }

        public Criteria andConversationidstrIsNull() {
            addCriterion("conversationIdStr is null");
            return (Criteria) this;
        }

        public Criteria andConversationidstrIsNotNull() {
            addCriterion("conversationIdStr is not null");
            return (Criteria) this;
        }

        public Criteria andConversationidstrEqualTo(String value) {
            addCriterion("conversationIdStr =", value, "conversationidstr");
            return (Criteria) this;
        }

        public Criteria andConversationidstrNotEqualTo(String value) {
            addCriterion("conversationIdStr <>", value, "conversationidstr");
            return (Criteria) this;
        }

        public Criteria andConversationidstrGreaterThan(String value) {
            addCriterion("conversationIdStr >", value, "conversationidstr");
            return (Criteria) this;
        }

        public Criteria andConversationidstrGreaterThanOrEqualTo(String value) {
            addCriterion("conversationIdStr >=", value, "conversationidstr");
            return (Criteria) this;
        }

        public Criteria andConversationidstrLessThan(String value) {
            addCriterion("conversationIdStr <", value, "conversationidstr");
            return (Criteria) this;
        }

        public Criteria andConversationidstrLessThanOrEqualTo(String value) {
            addCriterion("conversationIdStr <=", value, "conversationidstr");
            return (Criteria) this;
        }

        public Criteria andConversationidstrLike(String value) {
            addCriterion("conversationIdStr like", value, "conversationidstr");
            return (Criteria) this;
        }

        public Criteria andConversationidstrNotLike(String value) {
            addCriterion("conversationIdStr not like", value, "conversationidstr");
            return (Criteria) this;
        }

        public Criteria andConversationidstrIn(List<String> values) {
            addCriterion("conversationIdStr in", values, "conversationidstr");
            return (Criteria) this;
        }

        public Criteria andConversationidstrNotIn(List<String> values) {
            addCriterion("conversationIdStr not in", values, "conversationidstr");
            return (Criteria) this;
        }

        public Criteria andConversationidstrBetween(String value1, String value2) {
            addCriterion("conversationIdStr between", value1, value2, "conversationidstr");
            return (Criteria) this;
        }

        public Criteria andConversationidstrNotBetween(String value1, String value2) {
            addCriterion("conversationIdStr not between", value1, value2, "conversationidstr");
            return (Criteria) this;
        }

        public Criteria andMessageidstrIsNull() {
            addCriterion("messageIdStr is null");
            return (Criteria) this;
        }

        public Criteria andMessageidstrIsNotNull() {
            addCriterion("messageIdStr is not null");
            return (Criteria) this;
        }

        public Criteria andMessageidstrEqualTo(String value) {
            addCriterion("messageIdStr =", value, "messageidstr");
            return (Criteria) this;
        }

        public Criteria andMessageidstrNotEqualTo(String value) {
            addCriterion("messageIdStr <>", value, "messageidstr");
            return (Criteria) this;
        }

        public Criteria andMessageidstrGreaterThan(String value) {
            addCriterion("messageIdStr >", value, "messageidstr");
            return (Criteria) this;
        }

        public Criteria andMessageidstrGreaterThanOrEqualTo(String value) {
            addCriterion("messageIdStr >=", value, "messageidstr");
            return (Criteria) this;
        }

        public Criteria andMessageidstrLessThan(String value) {
            addCriterion("messageIdStr <", value, "messageidstr");
            return (Criteria) this;
        }

        public Criteria andMessageidstrLessThanOrEqualTo(String value) {
            addCriterion("messageIdStr <=", value, "messageidstr");
            return (Criteria) this;
        }

        public Criteria andMessageidstrLike(String value) {
            addCriterion("messageIdStr like", value, "messageidstr");
            return (Criteria) this;
        }

        public Criteria andMessageidstrNotLike(String value) {
            addCriterion("messageIdStr not like", value, "messageidstr");
            return (Criteria) this;
        }

        public Criteria andMessageidstrIn(List<String> values) {
            addCriterion("messageIdStr in", values, "messageidstr");
            return (Criteria) this;
        }

        public Criteria andMessageidstrNotIn(List<String> values) {
            addCriterion("messageIdStr not in", values, "messageidstr");
            return (Criteria) this;
        }

        public Criteria andMessageidstrBetween(String value1, String value2) {
            addCriterion("messageIdStr between", value1, value2, "messageidstr");
            return (Criteria) this;
        }

        public Criteria andMessageidstrNotBetween(String value1, String value2) {
            addCriterion("messageIdStr not between", value1, value2, "messageidstr");
            return (Criteria) this;
        }

        public Criteria andTraceidIsNull() {
            addCriterion("traceId is null");
            return (Criteria) this;
        }

        public Criteria andTraceidIsNotNull() {
            addCriterion("traceId is not null");
            return (Criteria) this;
        }

        public Criteria andTraceidEqualTo(String value) {
            addCriterion("traceId =", value, "traceid");
            return (Criteria) this;
        }

        public Criteria andTraceidNotEqualTo(String value) {
            addCriterion("traceId <>", value, "traceid");
            return (Criteria) this;
        }

        public Criteria andTraceidGreaterThan(String value) {
            addCriterion("traceId >", value, "traceid");
            return (Criteria) this;
        }

        public Criteria andTraceidGreaterThanOrEqualTo(String value) {
            addCriterion("traceId >=", value, "traceid");
            return (Criteria) this;
        }

        public Criteria andTraceidLessThan(String value) {
            addCriterion("traceId <", value, "traceid");
            return (Criteria) this;
        }

        public Criteria andTraceidLessThanOrEqualTo(String value) {
            addCriterion("traceId <=", value, "traceid");
            return (Criteria) this;
        }

        public Criteria andTraceidLike(String value) {
            addCriterion("traceId like", value, "traceid");
            return (Criteria) this;
        }

        public Criteria andTraceidNotLike(String value) {
            addCriterion("traceId not like", value, "traceid");
            return (Criteria) this;
        }

        public Criteria andTraceidIn(List<String> values) {
            addCriterion("traceId in", values, "traceid");
            return (Criteria) this;
        }

        public Criteria andTraceidNotIn(List<String> values) {
            addCriterion("traceId not in", values, "traceid");
            return (Criteria) this;
        }

        public Criteria andTraceidBetween(String value1, String value2) {
            addCriterion("traceId between", value1, value2, "traceid");
            return (Criteria) this;
        }

        public Criteria andTraceidNotBetween(String value1, String value2) {
            addCriterion("traceId not between", value1, value2, "traceid");
            return (Criteria) this;
        }

        public Criteria andSpansIsNull() {
            addCriterion("spans is null");
            return (Criteria) this;
        }

        public Criteria andSpansIsNotNull() {
            addCriterion("spans is not null");
            return (Criteria) this;
        }

        public Criteria andSpansEqualTo(String value) {
            addCriterion("spans =", value, "spans");
            return (Criteria) this;
        }

        public Criteria andSpansNotEqualTo(String value) {
            addCriterion("spans <>", value, "spans");
            return (Criteria) this;
        }

        public Criteria andSpansGreaterThan(String value) {
            addCriterion("spans >", value, "spans");
            return (Criteria) this;
        }

        public Criteria andSpansGreaterThanOrEqualTo(String value) {
            addCriterion("spans >=", value, "spans");
            return (Criteria) this;
        }

        public Criteria andSpansLessThan(String value) {
            addCriterion("spans <", value, "spans");
            return (Criteria) this;
        }

        public Criteria andSpansLessThanOrEqualTo(String value) {
            addCriterion("spans <=", value, "spans");
            return (Criteria) this;
        }

        public Criteria andSpansLike(String value) {
            addCriterion("spans like", value, "spans");
            return (Criteria) this;
        }

        public Criteria andSpansNotLike(String value) {
            addCriterion("spans not like", value, "spans");
            return (Criteria) this;
        }

        public Criteria andSpansIn(List<String> values) {
            addCriterion("spans in", values, "spans");
            return (Criteria) this;
        }

        public Criteria andSpansNotIn(List<String> values) {
            addCriterion("spans not in", values, "spans");
            return (Criteria) this;
        }

        public Criteria andSpansBetween(String value1, String value2) {
            addCriterion("spans between", value1, value2, "spans");
            return (Criteria) this;
        }

        public Criteria andSpansNotBetween(String value1, String value2) {
            addCriterion("spans not between", value1, value2, "spans");
            return (Criteria) this;
        }

        public Criteria andBusinesstypeIsNull() {
            addCriterion("businessType is null");
            return (Criteria) this;
        }

        public Criteria andBusinesstypeIsNotNull() {
            addCriterion("businessType is not null");
            return (Criteria) this;
        }

        public Criteria andBusinesstypeEqualTo(String value) {
            addCriterion("businessType =", value, "businesstype");
            return (Criteria) this;
        }

        public Criteria andBusinesstypeNotEqualTo(String value) {
            addCriterion("businessType <>", value, "businesstype");
            return (Criteria) this;
        }

        public Criteria andBusinesstypeGreaterThan(String value) {
            addCriterion("businessType >", value, "businesstype");
            return (Criteria) this;
        }

        public Criteria andBusinesstypeGreaterThanOrEqualTo(String value) {
            addCriterion("businessType >=", value, "businesstype");
            return (Criteria) this;
        }

        public Criteria andBusinesstypeLessThan(String value) {
            addCriterion("businessType <", value, "businesstype");
            return (Criteria) this;
        }

        public Criteria andBusinesstypeLessThanOrEqualTo(String value) {
            addCriterion("businessType <=", value, "businesstype");
            return (Criteria) this;
        }

        public Criteria andBusinesstypeLike(String value) {
            addCriterion("businessType like", value, "businesstype");
            return (Criteria) this;
        }

        public Criteria andBusinesstypeNotLike(String value) {
            addCriterion("businessType not like", value, "businesstype");
            return (Criteria) this;
        }

        public Criteria andBusinesstypeIn(List<String> values) {
            addCriterion("businessType in", values, "businesstype");
            return (Criteria) this;
        }

        public Criteria andBusinesstypeNotIn(List<String> values) {
            addCriterion("businessType not in", values, "businesstype");
            return (Criteria) this;
        }

        public Criteria andBusinesstypeBetween(String value1, String value2) {
            addCriterion("businessType between", value1, value2, "businesstype");
            return (Criteria) this;
        }

        public Criteria andBusinesstypeNotBetween(String value1, String value2) {
            addCriterion("businessType not between", value1, value2, "businesstype");
            return (Criteria) this;
        }

        public Criteria andBusinessidIsNull() {
            addCriterion("businessId is null");
            return (Criteria) this;
        }

        public Criteria andBusinessidIsNotNull() {
            addCriterion("businessId is not null");
            return (Criteria) this;
        }

        public Criteria andBusinessidEqualTo(String value) {
            addCriterion("businessId =", value, "businessid");
            return (Criteria) this;
        }

        public Criteria andBusinessidNotEqualTo(String value) {
            addCriterion("businessId <>", value, "businessid");
            return (Criteria) this;
        }

        public Criteria andBusinessidGreaterThan(String value) {
            addCriterion("businessId >", value, "businessid");
            return (Criteria) this;
        }

        public Criteria andBusinessidGreaterThanOrEqualTo(String value) {
            addCriterion("businessId >=", value, "businessid");
            return (Criteria) this;
        }

        public Criteria andBusinessidLessThan(String value) {
            addCriterion("businessId <", value, "businessid");
            return (Criteria) this;
        }

        public Criteria andBusinessidLessThanOrEqualTo(String value) {
            addCriterion("businessId <=", value, "businessid");
            return (Criteria) this;
        }

        public Criteria andBusinessidLike(String value) {
            addCriterion("businessId like", value, "businessid");
            return (Criteria) this;
        }

        public Criteria andBusinessidNotLike(String value) {
            addCriterion("businessId not like", value, "businessid");
            return (Criteria) this;
        }

        public Criteria andBusinessidIn(List<String> values) {
            addCriterion("businessId in", values, "businessid");
            return (Criteria) this;
        }

        public Criteria andBusinessidNotIn(List<String> values) {
            addCriterion("businessId not in", values, "businessid");
            return (Criteria) this;
        }

        public Criteria andBusinessidBetween(String value1, String value2) {
            addCriterion("businessId between", value1, value2, "businessid");
            return (Criteria) this;
        }

        public Criteria andBusinessidNotBetween(String value1, String value2) {
            addCriterion("businessId not between", value1, value2, "businessid");
            return (Criteria) this;
        }

        public Criteria andDepartname2idmapIsNull() {
            addCriterion("departName2IdMap is null");
            return (Criteria) this;
        }

        public Criteria andDepartname2idmapIsNotNull() {
            addCriterion("departName2IdMap is not null");
            return (Criteria) this;
        }

        public Criteria andDepartname2idmapEqualTo(String value) {
            addCriterion("departName2IdMap =", value, "departname2idmap");
            return (Criteria) this;
        }

        public Criteria andDepartname2idmapNotEqualTo(String value) {
            addCriterion("departName2IdMap <>", value, "departname2idmap");
            return (Criteria) this;
        }

        public Criteria andDepartname2idmapGreaterThan(String value) {
            addCriterion("departName2IdMap >", value, "departname2idmap");
            return (Criteria) this;
        }

        public Criteria andDepartname2idmapGreaterThanOrEqualTo(String value) {
            addCriterion("departName2IdMap >=", value, "departname2idmap");
            return (Criteria) this;
        }

        public Criteria andDepartname2idmapLessThan(String value) {
            addCriterion("departName2IdMap <", value, "departname2idmap");
            return (Criteria) this;
        }

        public Criteria andDepartname2idmapLessThanOrEqualTo(String value) {
            addCriterion("departName2IdMap <=", value, "departname2idmap");
            return (Criteria) this;
        }

        public Criteria andDepartname2idmapLike(String value) {
            addCriterion("departName2IdMap like", value, "departname2idmap");
            return (Criteria) this;
        }

        public Criteria andDepartname2idmapNotLike(String value) {
            addCriterion("departName2IdMap not like", value, "departname2idmap");
            return (Criteria) this;
        }

        public Criteria andDepartname2idmapIn(List<String> values) {
            addCriterion("departName2IdMap in", values, "departname2idmap");
            return (Criteria) this;
        }

        public Criteria andDepartname2idmapNotIn(List<String> values) {
            addCriterion("departName2IdMap not in", values, "departname2idmap");
            return (Criteria) this;
        }

        public Criteria andDepartname2idmapBetween(String value1, String value2) {
            addCriterion("departName2IdMap between", value1, value2, "departname2idmap");
            return (Criteria) this;
        }

        public Criteria andDepartname2idmapNotBetween(String value1, String value2) {
            addCriterion("departName2IdMap not between", value1, value2, "departname2idmap");
            return (Criteria) this;
        }

        public Criteria andStddepartname2idmapIsNull() {
            addCriterion("stdDepartName2IdMap is null");
            return (Criteria) this;
        }

        public Criteria andStddepartname2idmapIsNotNull() {
            addCriterion("stdDepartName2IdMap is not null");
            return (Criteria) this;
        }

        public Criteria andStddepartname2idmapEqualTo(String value) {
            addCriterion("stdDepartName2IdMap =", value, "stddepartname2idmap");
            return (Criteria) this;
        }

        public Criteria andStddepartname2idmapNotEqualTo(String value) {
            addCriterion("stdDepartName2IdMap <>", value, "stddepartname2idmap");
            return (Criteria) this;
        }

        public Criteria andStddepartname2idmapGreaterThan(String value) {
            addCriterion("stdDepartName2IdMap >", value, "stddepartname2idmap");
            return (Criteria) this;
        }

        public Criteria andStddepartname2idmapGreaterThanOrEqualTo(String value) {
            addCriterion("stdDepartName2IdMap >=", value, "stddepartname2idmap");
            return (Criteria) this;
        }

        public Criteria andStddepartname2idmapLessThan(String value) {
            addCriterion("stdDepartName2IdMap <", value, "stddepartname2idmap");
            return (Criteria) this;
        }

        public Criteria andStddepartname2idmapLessThanOrEqualTo(String value) {
            addCriterion("stdDepartName2IdMap <=", value, "stddepartname2idmap");
            return (Criteria) this;
        }

        public Criteria andStddepartname2idmapLike(String value) {
            addCriterion("stdDepartName2IdMap like", value, "stddepartname2idmap");
            return (Criteria) this;
        }

        public Criteria andStddepartname2idmapNotLike(String value) {
            addCriterion("stdDepartName2IdMap not like", value, "stddepartname2idmap");
            return (Criteria) this;
        }

        public Criteria andStddepartname2idmapIn(List<String> values) {
            addCriterion("stdDepartName2IdMap in", values, "stddepartname2idmap");
            return (Criteria) this;
        }

        public Criteria andStddepartname2idmapNotIn(List<String> values) {
            addCriterion("stdDepartName2IdMap not in", values, "stddepartname2idmap");
            return (Criteria) this;
        }

        public Criteria andStddepartname2idmapBetween(String value1, String value2) {
            addCriterion("stdDepartName2IdMap between", value1, value2, "stddepartname2idmap");
            return (Criteria) this;
        }

        public Criteria andStddepartname2idmapNotBetween(String value1, String value2) {
            addCriterion("stdDepartName2IdMap not between", value1, value2, "stddepartname2idmap");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("status is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("status is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(String value) {
            addCriterion("status =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(String value) {
            addCriterion("status <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(String value) {
            addCriterion("status >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(String value) {
            addCriterion("status >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(String value) {
            addCriterion("status <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(String value) {
            addCriterion("status <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLike(String value) {
            addCriterion("status like", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotLike(String value) {
            addCriterion("status not like", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<String> values) {
            addCriterion("status in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<String> values) {
            addCriterion("status not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(String value1, String value2) {
            addCriterion("status between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(String value1, String value2) {
            addCriterion("status not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andSearchinfoIsNull() {
            addCriterion("searchInfo is null");
            return (Criteria) this;
        }

        public Criteria andSearchinfoIsNotNull() {
            addCriterion("searchInfo is not null");
            return (Criteria) this;
        }

        public Criteria andSearchinfoEqualTo(String value) {
            addCriterion("searchInfo =", value, "searchinfo");
            return (Criteria) this;
        }

        public Criteria andSearchinfoNotEqualTo(String value) {
            addCriterion("searchInfo <>", value, "searchinfo");
            return (Criteria) this;
        }

        public Criteria andSearchinfoGreaterThan(String value) {
            addCriterion("searchInfo >", value, "searchinfo");
            return (Criteria) this;
        }

        public Criteria andSearchinfoGreaterThanOrEqualTo(String value) {
            addCriterion("searchInfo >=", value, "searchinfo");
            return (Criteria) this;
        }

        public Criteria andSearchinfoLessThan(String value) {
            addCriterion("searchInfo <", value, "searchinfo");
            return (Criteria) this;
        }

        public Criteria andSearchinfoLessThanOrEqualTo(String value) {
            addCriterion("searchInfo <=", value, "searchinfo");
            return (Criteria) this;
        }

        public Criteria andSearchinfoLike(String value) {
            addCriterion("searchInfo like", value, "searchinfo");
            return (Criteria) this;
        }

        public Criteria andSearchinfoNotLike(String value) {
            addCriterion("searchInfo not like", value, "searchinfo");
            return (Criteria) this;
        }

        public Criteria andSearchinfoIn(List<String> values) {
            addCriterion("searchInfo in", values, "searchinfo");
            return (Criteria) this;
        }

        public Criteria andSearchinfoNotIn(List<String> values) {
            addCriterion("searchInfo not in", values, "searchinfo");
            return (Criteria) this;
        }

        public Criteria andSearchinfoBetween(String value1, String value2) {
            addCriterion("searchInfo between", value1, value2, "searchinfo");
            return (Criteria) this;
        }

        public Criteria andSearchinfoNotBetween(String value1, String value2) {
            addCriterion("searchInfo not between", value1, value2, "searchinfo");
            return (Criteria) this;
        }

        public Criteria andRaginfoIsNull() {
            addCriterion("ragInfo is null");
            return (Criteria) this;
        }

        public Criteria andRaginfoIsNotNull() {
            addCriterion("ragInfo is not null");
            return (Criteria) this;
        }

        public Criteria andRaginfoEqualTo(String value) {
            addCriterion("ragInfo =", value, "raginfo");
            return (Criteria) this;
        }

        public Criteria andRaginfoNotEqualTo(String value) {
            addCriterion("ragInfo <>", value, "raginfo");
            return (Criteria) this;
        }

        public Criteria andRaginfoGreaterThan(String value) {
            addCriterion("ragInfo >", value, "raginfo");
            return (Criteria) this;
        }

        public Criteria andRaginfoGreaterThanOrEqualTo(String value) {
            addCriterion("ragInfo >=", value, "raginfo");
            return (Criteria) this;
        }

        public Criteria andRaginfoLessThan(String value) {
            addCriterion("ragInfo <", value, "raginfo");
            return (Criteria) this;
        }

        public Criteria andRaginfoLessThanOrEqualTo(String value) {
            addCriterion("ragInfo <=", value, "raginfo");
            return (Criteria) this;
        }

        public Criteria andRaginfoLike(String value) {
            addCriterion("ragInfo like", value, "raginfo");
            return (Criteria) this;
        }

        public Criteria andRaginfoNotLike(String value) {
            addCriterion("ragInfo not like", value, "raginfo");
            return (Criteria) this;
        }

        public Criteria andRaginfoIn(List<String> values) {
            addCriterion("ragInfo in", values, "raginfo");
            return (Criteria) this;
        }

        public Criteria andRaginfoNotIn(List<String> values) {
            addCriterion("ragInfo not in", values, "raginfo");
            return (Criteria) this;
        }

        public Criteria andRaginfoBetween(String value1, String value2) {
            addCriterion("ragInfo between", value1, value2, "raginfo");
            return (Criteria) this;
        }

        public Criteria andRaginfoNotBetween(String value1, String value2) {
            addCriterion("ragInfo not between", value1, value2, "raginfo");
            return (Criteria) this;
        }

        public Criteria andCreatedAtIsNull() {
            addCriterion("created_at is null");
            return (Criteria) this;
        }

        public Criteria andCreatedAtIsNotNull() {
            addCriterion("created_at is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedAtEqualTo(Date value) {
            addCriterion("created_at =", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtNotEqualTo(Date value) {
            addCriterion("created_at <>", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtGreaterThan(Date value) {
            addCriterion("created_at >", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtGreaterThanOrEqualTo(Date value) {
            addCriterion("created_at >=", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtLessThan(Date value) {
            addCriterion("created_at <", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtLessThanOrEqualTo(Date value) {
            addCriterion("created_at <=", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtIn(List<Date> values) {
            addCriterion("created_at in", values, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtNotIn(List<Date> values) {
            addCriterion("created_at not in", values, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtBetween(Date value1, Date value2) {
            addCriterion("created_at between", value1, value2, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtNotBetween(Date value1, Date value2) {
            addCriterion("created_at not between", value1, value2, "createdAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtIsNull() {
            addCriterion("updated_at is null");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtIsNotNull() {
            addCriterion("updated_at is not null");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtEqualTo(Date value) {
            addCriterion("updated_at =", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtNotEqualTo(Date value) {
            addCriterion("updated_at <>", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtGreaterThan(Date value) {
            addCriterion("updated_at >", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtGreaterThanOrEqualTo(Date value) {
            addCriterion("updated_at >=", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtLessThan(Date value) {
            addCriterion("updated_at <", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtLessThanOrEqualTo(Date value) {
            addCriterion("updated_at <=", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtIn(List<Date> values) {
            addCriterion("updated_at in", values, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtNotIn(List<Date> values) {
            addCriterion("updated_at not in", values, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtBetween(Date value1, Date value2) {
            addCriterion("updated_at between", value1, value2, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtNotBetween(Date value1, Date value2) {
            addCriterion("updated_at not between", value1, value2, "updatedAt");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}