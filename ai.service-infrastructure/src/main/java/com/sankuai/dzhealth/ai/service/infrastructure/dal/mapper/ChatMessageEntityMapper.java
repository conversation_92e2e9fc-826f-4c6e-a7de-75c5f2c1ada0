package com.sankuai.dzhealth.ai.service.infrastructure.dal.mapper;

import com.meituan.mdp.mybatis.mapper.MybatisBLOBsMapper;
import com.sankuai.dzhealth.ai.service.infrastructure.dal.entity.ChatMessageEntity;
import com.sankuai.dzhealth.ai.service.infrastructure.dal.example.ChatMessageEntityExample;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ChatMessageEntityMapper extends MybatisBLOBsMapper<ChatMessageEntity, ChatMessageEntityExample, Long> {
    List<ChatMessageEntity> selectLatestNByConversationId(@Param("conversationId") String conversationId,
                                                         @Param("n") Integer n);

    ChatMessageEntity selectFirstUserMessageByConversationId(@Param("conversationId") String conversationId);

    List<ChatMessageEntity> selectByExampleWithPage(@Param("example") ChatMessageEntityExample example,
                                                   @Param("offset") int offset,
                                                   @Param("limit") int limit);

    int updateByExampleSelective(@Param("row") ChatMessageEntity record, @Param("example") ChatMessageEntityExample example);
}