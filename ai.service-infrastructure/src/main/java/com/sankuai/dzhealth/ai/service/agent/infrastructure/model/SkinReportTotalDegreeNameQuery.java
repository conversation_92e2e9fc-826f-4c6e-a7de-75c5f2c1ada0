package com.sankuai.dzhealth.ai.service.agent.infrastructure.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 测肤报告查询
 *
 * @user: x<PERSON><PERSON><PERSON><PERSON>
 * @since: 2025/7/7 19:52
 * @version: 1.0
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SkinReportTotalDegreeNameQuery {
    /**
     * 用户id，必传
     */
    private Long userId;

    /**
     * 平台标识 0-MT 1-DP，必填
     *
     * {@link com.meituan.beauty.narcissus.constant.SourceEnum}
     */
    private Integer platform;
    /**
     * 固定传 4，必填
     *
     * {@link com.meituan.beauty.narcissus.constant.BusinessTypeEnum}
     */
    private Integer businessType;

    /**
     * 报告uuid，必填
     */
    private String reportUuid;
}
