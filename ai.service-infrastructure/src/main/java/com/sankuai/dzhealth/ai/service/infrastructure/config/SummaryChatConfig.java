package com.sankuai.dzhealth.ai.service.infrastructure.config;

import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.openai.OpenAiChatModel;
import org.springframework.ai.openai.OpenAiChatOptions;
import org.springframework.ai.openai.api.OpenAiApi;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * @Author: zhongchangze
 * @Date: 2025/3/24 10:59
 * @Description:
 */
@Configuration
public class SummaryChatConfig {

    @Bean
    public ChatClient.Builder summaryChatClientBuilder() {
        OpenAiApi openAiApi = new OpenAiApi.Builder()
                .completionsPath("/chat/completions")
                .baseUrl("https://aigc.sankuai.com/v1/openai/native")
                .apiKey("1905090488731074567")
                .build();
        OpenAiChatOptions openAiChatOptions = OpenAiChatOptions.builder()
                .model("deepseek-v3-friday")
                .temperature(0.0)
                .maxTokens(1000)
                .build();
        OpenAiChatModel chatModel = OpenAiChatModel.builder()
                .openAiApi(openAiApi)
                .defaultOptions(openAiChatOptions)
                .build();
        return ChatClient.builder(chatModel);
    }
}