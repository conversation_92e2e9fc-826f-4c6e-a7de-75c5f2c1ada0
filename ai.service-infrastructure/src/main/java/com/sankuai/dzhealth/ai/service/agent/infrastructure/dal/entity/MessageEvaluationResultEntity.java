package com.sankuai.dzhealth.ai.service.agent.infrastructure.dal.entity;

import java.math.BigDecimal;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;

/**
 * 表名: message_evaluation_result
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MessageEvaluationResultEntity {
    /**
     * 字段: id
     * 说明: 主键ID
     */
    private Long id;

    /**
     * 字段: biz_scene
     * 说明: 业务场景
     */
    @JsonProperty("biz_scene")
    private String bizScene;

    /**
     * 字段: session_id
     * 说明: 会话ID
     */
    @JsonProperty("session_id")
    private String sessionId;

    /**
     * 字段: message_id
     * 说明: 消息ID
     */
    @JsonProperty("message_id")
    private String messageId;

    /**
     * 字段: model_scene
     * 说明: 模型场景
     */
    @JsonProperty("model_scene")
    private String modelScene;

    /**
     * 字段: item
     * 说明: 指标项
     */
    private String item;

    /**
     * 字段: description
     * 说明: 指标名称，例如召回覆盖度、幻觉概率等
     */
    private String description;

    /**
     * 字段: reason
     * 说明: 评估理由
     */
    private String reason;

    /**
     * 字段: score
     * 说明: 分数
     */
    private BigDecimal score;

    /**
     * 字段: type
     * 说明: 指标类型，0=单轮，1=增量多轮
     */
    private Integer type;

    /**
     * 字段: source
     * 说明: 来源，0=用户，1=测试
     */
    private Integer source;

    /**
     * 字段: add_time
     * 说明: 添加时间
     */
    @JsonProperty("add_time")
    private Date addTime;

    /**
     * 字段: update_time
     * 说明: 更新时间
     */
    @JsonProperty("update_time")
    private Date updateTime;

    /**
     *   字段: evaluation_id
     *   说明: 评测集id
     */
    private Long evaluationId;

    /**
     *   字段: evaluation_ver
     *   说明: 评测集版本
     */
    private Integer evaluationVer;
}