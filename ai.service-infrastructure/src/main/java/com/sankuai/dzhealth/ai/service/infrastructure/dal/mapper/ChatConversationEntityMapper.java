package com.sankuai.dzhealth.ai.service.infrastructure.dal.mapper;

import com.meituan.mdp.mybatis.mapper.MybatisBaseMapper;
import com.sankuai.dzhealth.ai.service.infrastructure.dal.entity.ChatConversationEntity;
import com.sankuai.dzhealth.ai.service.infrastructure.dal.example.ChatConversationEntityExample;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ChatConversationEntityMapper extends MybatisBaseMapper<ChatConversationEntity, ChatConversationEntityExample, Long> {
    List<ChatConversationEntity> selectByExampleWithPage(@Param("example") ChatConversationEntityExample example,
                                                         @Param("offset") int offset,
                                                         @Param("limit") int limit);

    int updateByExampleSelective(@Param("row") ChatConversationEntity row, @Param("example") ChatConversationEntityExample example);
}