package com.sankuai.dzhealth.ai.service.infrastructure.dal.example;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class DeepSearchResultEntityExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public DeepSearchResultEntityExample() {
        oredCriteria = new ArrayList<>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andBusinessSourceIsNull() {
            addCriterion("business_source is null");
            return (Criteria) this;
        }

        public Criteria andBusinessSourceIsNotNull() {
            addCriterion("business_source is not null");
            return (Criteria) this;
        }

        public Criteria andBusinessSourceEqualTo(String value) {
            addCriterion("business_source =", value, "businessSource");
            return (Criteria) this;
        }

        public Criteria andBusinessSourceNotEqualTo(String value) {
            addCriterion("business_source <>", value, "businessSource");
            return (Criteria) this;
        }

        public Criteria andBusinessSourceGreaterThan(String value) {
            addCriterion("business_source >", value, "businessSource");
            return (Criteria) this;
        }

        public Criteria andBusinessSourceGreaterThanOrEqualTo(String value) {
            addCriterion("business_source >=", value, "businessSource");
            return (Criteria) this;
        }

        public Criteria andBusinessSourceLessThan(String value) {
            addCriterion("business_source <", value, "businessSource");
            return (Criteria) this;
        }

        public Criteria andBusinessSourceLessThanOrEqualTo(String value) {
            addCriterion("business_source <=", value, "businessSource");
            return (Criteria) this;
        }

        public Criteria andBusinessSourceLike(String value) {
            addCriterion("business_source like", value, "businessSource");
            return (Criteria) this;
        }

        public Criteria andBusinessSourceNotLike(String value) {
            addCriterion("business_source not like", value, "businessSource");
            return (Criteria) this;
        }

        public Criteria andBusinessSourceIn(List<String> values) {
            addCriterion("business_source in", values, "businessSource");
            return (Criteria) this;
        }

        public Criteria andBusinessSourceNotIn(List<String> values) {
            addCriterion("business_source not in", values, "businessSource");
            return (Criteria) this;
        }

        public Criteria andBusinessSourceBetween(String value1, String value2) {
            addCriterion("business_source between", value1, value2, "businessSource");
            return (Criteria) this;
        }

        public Criteria andBusinessSourceNotBetween(String value1, String value2) {
            addCriterion("business_source not between", value1, value2, "businessSource");
            return (Criteria) this;
        }

        public Criteria andQuestionIsNull() {
            addCriterion("question is null");
            return (Criteria) this;
        }

        public Criteria andQuestionIsNotNull() {
            addCriterion("question is not null");
            return (Criteria) this;
        }

        public Criteria andQuestionEqualTo(String value) {
            addCriterion("question =", value, "question");
            return (Criteria) this;
        }

        public Criteria andQuestionNotEqualTo(String value) {
            addCriterion("question <>", value, "question");
            return (Criteria) this;
        }

        public Criteria andQuestionGreaterThan(String value) {
            addCriterion("question >", value, "question");
            return (Criteria) this;
        }

        public Criteria andQuestionGreaterThanOrEqualTo(String value) {
            addCriterion("question >=", value, "question");
            return (Criteria) this;
        }

        public Criteria andQuestionLessThan(String value) {
            addCriterion("question <", value, "question");
            return (Criteria) this;
        }

        public Criteria andQuestionLessThanOrEqualTo(String value) {
            addCriterion("question <=", value, "question");
            return (Criteria) this;
        }

        public Criteria andQuestionLike(String value) {
            addCriterion("question like", value, "question");
            return (Criteria) this;
        }

        public Criteria andQuestionNotLike(String value) {
            addCriterion("question not like", value, "question");
            return (Criteria) this;
        }

        public Criteria andQuestionIn(List<String> values) {
            addCriterion("question in", values, "question");
            return (Criteria) this;
        }

        public Criteria andQuestionNotIn(List<String> values) {
            addCriterion("question not in", values, "question");
            return (Criteria) this;
        }

        public Criteria andQuestionBetween(String value1, String value2) {
            addCriterion("question between", value1, value2, "question");
            return (Criteria) this;
        }

        public Criteria andQuestionNotBetween(String value1, String value2) {
            addCriterion("question not between", value1, value2, "question");
            return (Criteria) this;
        }

        public Criteria andSourceUrlsIsNull() {
            addCriterion("source_urls is null");
            return (Criteria) this;
        }

        public Criteria andSourceUrlsIsNotNull() {
            addCriterion("source_urls is not null");
            return (Criteria) this;
        }

        public Criteria andSourceUrlsEqualTo(String value) {
            addCriterion("source_urls =", value, "sourceUrls");
            return (Criteria) this;
        }

        public Criteria andSourceUrlsNotEqualTo(String value) {
            addCriterion("source_urls <>", value, "sourceUrls");
            return (Criteria) this;
        }

        public Criteria andSourceUrlsGreaterThan(String value) {
            addCriterion("source_urls >", value, "sourceUrls");
            return (Criteria) this;
        }

        public Criteria andSourceUrlsGreaterThanOrEqualTo(String value) {
            addCriterion("source_urls >=", value, "sourceUrls");
            return (Criteria) this;
        }

        public Criteria andSourceUrlsLessThan(String value) {
            addCriterion("source_urls <", value, "sourceUrls");
            return (Criteria) this;
        }

        public Criteria andSourceUrlsLessThanOrEqualTo(String value) {
            addCriterion("source_urls <=", value, "sourceUrls");
            return (Criteria) this;
        }

        public Criteria andSourceUrlsLike(String value) {
            addCriterion("source_urls like", value, "sourceUrls");
            return (Criteria) this;
        }

        public Criteria andSourceUrlsNotLike(String value) {
            addCriterion("source_urls not like", value, "sourceUrls");
            return (Criteria) this;
        }

        public Criteria andSourceUrlsIn(List<String> values) {
            addCriterion("source_urls in", values, "sourceUrls");
            return (Criteria) this;
        }

        public Criteria andSourceUrlsNotIn(List<String> values) {
            addCriterion("source_urls not in", values, "sourceUrls");
            return (Criteria) this;
        }

        public Criteria andSourceUrlsBetween(String value1, String value2) {
            addCriterion("source_urls between", value1, value2, "sourceUrls");
            return (Criteria) this;
        }

        public Criteria andSourceUrlsNotBetween(String value1, String value2) {
            addCriterion("source_urls not between", value1, value2, "sourceUrls");
            return (Criteria) this;
        }

        public Criteria andConfidenceScoreIsNull() {
            addCriterion("confidence_score is null");
            return (Criteria) this;
        }

        public Criteria andConfidenceScoreIsNotNull() {
            addCriterion("confidence_score is not null");
            return (Criteria) this;
        }

        public Criteria andConfidenceScoreEqualTo(BigDecimal value) {
            addCriterion("confidence_score =", value, "confidenceScore");
            return (Criteria) this;
        }

        public Criteria andConfidenceScoreNotEqualTo(BigDecimal value) {
            addCriterion("confidence_score <>", value, "confidenceScore");
            return (Criteria) this;
        }

        public Criteria andConfidenceScoreGreaterThan(BigDecimal value) {
            addCriterion("confidence_score >", value, "confidenceScore");
            return (Criteria) this;
        }

        public Criteria andConfidenceScoreGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("confidence_score >=", value, "confidenceScore");
            return (Criteria) this;
        }

        public Criteria andConfidenceScoreLessThan(BigDecimal value) {
            addCriterion("confidence_score <", value, "confidenceScore");
            return (Criteria) this;
        }

        public Criteria andConfidenceScoreLessThanOrEqualTo(BigDecimal value) {
            addCriterion("confidence_score <=", value, "confidenceScore");
            return (Criteria) this;
        }

        public Criteria andConfidenceScoreIn(List<BigDecimal> values) {
            addCriterion("confidence_score in", values, "confidenceScore");
            return (Criteria) this;
        }

        public Criteria andConfidenceScoreNotIn(List<BigDecimal> values) {
            addCriterion("confidence_score not in", values, "confidenceScore");
            return (Criteria) this;
        }

        public Criteria andConfidenceScoreBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("confidence_score between", value1, value2, "confidenceScore");
            return (Criteria) this;
        }

        public Criteria andConfidenceScoreNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("confidence_score not between", value1, value2, "confidenceScore");
            return (Criteria) this;
        }

        public Criteria andAddTimeIsNull() {
            addCriterion("add_time is null");
            return (Criteria) this;
        }

        public Criteria andAddTimeIsNotNull() {
            addCriterion("add_time is not null");
            return (Criteria) this;
        }

        public Criteria andAddTimeEqualTo(Date value) {
            addCriterion("add_time =", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotEqualTo(Date value) {
            addCriterion("add_time <>", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeGreaterThan(Date value) {
            addCriterion("add_time >", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("add_time >=", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeLessThan(Date value) {
            addCriterion("add_time <", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeLessThanOrEqualTo(Date value) {
            addCriterion("add_time <=", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeIn(List<Date> values) {
            addCriterion("add_time in", values, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotIn(List<Date> values) {
            addCriterion("add_time not in", values, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeBetween(Date value1, Date value2) {
            addCriterion("add_time between", value1, value2, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotBetween(Date value1, Date value2) {
            addCriterion("add_time not between", value1, value2, "addTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}