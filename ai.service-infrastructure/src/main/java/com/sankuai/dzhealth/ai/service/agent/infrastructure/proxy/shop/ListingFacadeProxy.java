package com.sankuai.dzhealth.ai.service.agent.infrastructure.proxy.shop;

import com.alibaba.fastjson.JSON;
import com.dianping.lion.client.Lion;
import com.dianping.rhino.Rhino;
import com.dianping.rhino.threadpool.DefaultThreadPoolProperties;
import com.dianping.rhino.threadpool.ThreadPool;
import com.sankuai.dzhealth.ai.service.agent.request.FillInfoRequest;
import com.sankuai.dzhealth.ai.service.agent.request.GoodsQry;
import com.sankuai.dzhealth.ai.service.agent.request.ShopQry;
import com.sankuai.medicalcosmetology.mainpath.listingapi.common.ResponseDTO;
import com.sankuai.medicalcosmetology.mainpath.listingapi.dto.*;
import com.sankuai.medicalcosmetology.mainpath.listingapi.request.FillInfoQry;
import com.sankuai.medicalcosmetology.mainpath.listingapi.service.ListingFacade;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

import static java.util.Map.entry;

@Component
@Slf4j
public class ListingFacadeProxy {

    @Autowired
    private ListingFacade listingFacade;


    public static final ThreadPool PROXY_POOL = Rhino.newThreadPool("proxyPool",
            DefaultThreadPoolProperties.Setter().withCoreSize(100).withMaxSize(200).withMaxQueueSize(500));
    
    // 每批次最大商品数量
    private static final int BATCH_SIZE = 20;

    Map<String, String> cpvTranslationMap = Map.<String, String>ofEntries(
            entry("MaterialModel", "材料型号"),
            entry("DoesItContainSandBlasting", "是否含喷砂"),
            entry("InstrumentationEquipment", "仪器器械"),
            entry("implant_doctor1", "医生"),
            entry("MaterialBrand", "材料品牌"),
            entry("implant_doctor2", "医生"),
            entry("IsContainPoreCore", "是否含桩核"),
            entry("Count", "次数"),
            entry("implant_crown", "牙冠"),
            entry("implant_service1", "服务内容"),
            entry("WhiteningAgentConcentration", "美白剂浓度"),
            entry("Operators", "操作人员"),
            entry("Pilecore", "桩核"),
            entry("SandblastingPowderProducingRegion", "喷砂粉产地"),
            entry("DesirableDuration", "可取时长"),
            entry("customName", "名称"),
            entry("implant_brand", "种植体品牌"),
            entry("tooth_suit_people", "适用人群"),
            entry("Project", "项目"),
            entry("implant_amount", "植牙数量"),
            entry("GuaranteeType", "质保类型"),
            entry("ResetWay", "复位方式"),
            entry("MaterialOrigination", "材料产地"),
            entry("DecryptionMethod", "脱敏方式"),
            entry("Technique", "术式"),
            entry("applicable_age_max", "适用人群年龄最大值"),
            entry("ToothType", "牙齿类型"),
            entry("QualityAssuranceType", "质保类型"),
            entry("Material2", "材料"),
            entry("implant_advantage1", "服务亮点"),
            entry("Dentalcleaningmachinebrand", "洁牙机品牌"),
            entry("ApplicableTime", "适用时间"),
            entry("DentalCrownOriginated", "牙冠产地"),
            entry("Biteassurance", "咬合保障"),
            entry("AntiColorGuarantee2", "反色保障"),
            entry("DoesItContainPolishing", "是否含抛光"),
            entry("applicable_age_min", "适用人群年龄最小值"),
            entry("SensitiveRelief2", "敏感缓解"),
            entry("get", "领取方式"),
            entry("implant_abutment", "基台"),
            entry("SecondaryTreatmentAssurance4", "二次治疗保障"),
            entry("Defectiveguarantee", "脱落保障"),
            entry("DentalCleaningMachineorigin", "洁牙机产地"),
            entry("ColorEnsure", "颜色保障"),
            entry("Postsaleswarranty", "售后质保"),
            entry("Sandblasting", "喷砂粉品牌"),
            entry("TreatmentScope", "治疗范围"),
            entry("SpecifyDuration", "指定可取时长"),
            entry("Oralexamination2", "服务内容"),
            entry("PackageIncludes", "套餐包含"),
            entry("ServiceHighlights", "服务亮点"),
            entry("DentalCrownBrand", "牙冠品牌"),
            entry("OperatingPersonType", "操作人员类型"),
            entry("Type", "类型"),
            entry("Oralexamination", "服务内容"),
            entry("TweakingApproach", "修整方式"),
            entry("BustCeramicInsurance", "崩瓷保障"),
            entry("Quantity3", "数量"),
            entry("Applicabledentalpositions", "适用牙位"),
            entry("equipment", "使用仪器"),
            entry("selectedBodyName", "适用部位"),
            entry("material", "使用材料"),
            entry("drug", "使用药品"),
            entry("follicularPrice", "毛囊单价"),
            entry("chooseSpu", "选择的标品信息"),
            entry("drugNum", "药品数量"),
            entry("drugDosage", "药品剂量"),
            entry("equipmentBrand", "仪器品牌"),
            entry("technology", "使用技术"),
            entry("applyBody", "适用部位"),
            entry("follicularNum", "毛囊数量"),
            entry("selectedNumberName", "适用发数"),
            entry("applysex", "适用性别"),
            entry("pointNum", "点位数"),
            entry("product", "使用产品"),
            entry("integratedProject", "综合项目"),
            entry("spotArea", "疤痕面积"),
            entry("hairRemovalEnvironment", "脱毛环境"),
            entry("maxAmountOfLiposuction", "最大抽脂量"),
            entry("dose", "剂量"),
            entry("numberOfUsageObject", "使用次数（对象）"),
            entry("hygiene", "卫生情况"),
            entry("privacy", "隐私情况"),
            entry("numberOfSpotNevus", "祛痣数量"),
            entry("chargeFee", "收费标准"),
            entry("mode", "模式"),
            entry("useLimitObject", "使用期限(对象)")
    );

//    @LionConfig(appkey = "com.sankuai.dzhealth.ai.service", key = "cpv.translation.map", defaultValue = "{}", converter = DefaultJsonConfigConvert.class)
//    private Map<String, String> translationMap;

    public String fillInfo(FillInfoRequest fillInfoRequest) {
        try {
            List<GoodsQry> goodsQryList = fillInfoRequest.getGoodsQryList();
            
            // 如果商品列表为空或数量小于等于批次大小，直接调用原方法
            if (CollectionUtils.isEmpty(goodsQryList) || goodsQryList.size() <= BATCH_SIZE) {
                return processSingleBatch(fillInfoRequest);
            }
            
            // 分批处理并并行调用
            return processBatchesInParallel(fillInfoRequest);
            
        } catch (Exception e) {
            log.error("ListingFacadeProxy.fillInfo error, request:{}", fillInfoRequest, e);
            return "{}";
        }
    }
    
    /**
     * 处理单个批次的fillInfo调用
     */
    private String processSingleBatch(FillInfoRequest fillInfoRequest) {
        try {
            // 通过 JSON 实现深拷贝转换
            FillInfoQry fillInfoQry = convertByJson(fillInfoRequest, FillInfoQry.class);

            ResponseDTO<FillInfoDTO> fillInfoDTOResponseDTO = listingFacade.fillInfo(fillInfoQry);

            log.info("fillInfoDTOResponseDTO: {}", fillInfoDTOResponseDTO);

            if (fillInfoDTOResponseDTO != null && fillInfoDTOResponseDTO.isSuccess() && fillInfoDTOResponseDTO.getData() != null) {
                // 转换为大模型友好的格式
                return convertToAIFriendlyFormat(fillInfoDTOResponseDTO.getData(), fillInfoRequest.getGoodsQryList());
            }

            return "{}"; // 返回空JSON
        } catch (Exception e) {
            log.error("ListingFacadeProxy.processSingleBatch error, request:{}", fillInfoRequest, e);
            throw e;
        }
    }
    
    /**
     * 分批并行处理fillInfo调用
     */
    private String processBatchesInParallel(FillInfoRequest fillInfoRequest) {
        List<GoodsQry> goodsQryList = fillInfoRequest.getGoodsQryList();
        
        // 获取唯一的商户列表，避免重复请求
        List<ShopQry> uniqueShops = getUniqueShops(fillInfoRequest.getShopQryList());
        
        // 将商品列表分批
        List<List<GoodsQry>> batches = splitIntoBatches(goodsQryList, BATCH_SIZE);
        
        log.info("将{}个商品分为{}批进行并行处理，涉及{}个唯一商户", goodsQryList.size(), batches.size(), uniqueShops.size());
        
        // 创建并行任务
        List<CompletableFuture<FillInfoDTO>> futures = batches.stream()
                .map(batch -> createBatchTask(fillInfoRequest, batch, uniqueShops))
                .collect(Collectors.toList());
        
        // 等待所有任务完成并合并结果
        try {
            CompletableFuture<Void> allOf = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));
            allOf.join(); // 等待所有任务完成
            
            // 收集所有结果
            List<FillInfoDTO> results = futures.stream()
                    .map(CompletableFuture::join)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            
            // 合并所有结果
            FillInfoDTO mergedResult = mergeResults(results);
            
            // 转换为大模型友好的格式
            return convertToAIFriendlyFormat(mergedResult, goodsQryList);
            
        } catch (Exception e) {
            log.error("并行处理fillInfo调用失败", e);
            throw new RuntimeException("并行处理fillInfo调用失败", e);
        }
    }
    
    /**
     * 创建单个批次的异步任务
     */
    private CompletableFuture<FillInfoDTO> createBatchTask(FillInfoRequest originalRequest, List<GoodsQry> batch, List<ShopQry> uniqueShops) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                // 创建当前批次的请求对象
                FillInfoRequest batchRequest = createBatchRequest(originalRequest, batch, uniqueShops);
                FillInfoQry fillInfoQry = convertByJson(batchRequest, FillInfoQry.class);
                
                log.info("开始处理批次，商品数量: {}", batch.size());
                
                ResponseDTO<FillInfoDTO> response = listingFacade.fillInfo(fillInfoQry);
                
                if (response != null && response.isSuccess() && response.getData() != null) {
                    log.info("批次处理成功，返回商品数量: {}, 店铺数量: {}", 
                            response.getData().getGoodsInfoDTOs() != null ? response.getData().getGoodsInfoDTOs().size() : 0,
                            response.getData().getShopInfos() != null ? response.getData().getShopInfos().size() : 0);
                    return response.getData();
                } else {
                    log.warn("批次处理失败或返回数据为空: {}", response);
                    return null;
                }
                
            } catch (Exception e) {
                log.error("处理批次失败，批次大小: {}", batch.size(), e);
                return null;
            }
        }, PROXY_POOL.getExecutor());
    }
    
    /**
     * 获取唯一的商户列表
     */
    private List<ShopQry> getUniqueShops(List<ShopQry> shopQryList) {
        if (CollectionUtils.isEmpty(shopQryList)) {
            return new ArrayList<>();
        }
        
        return shopQryList.stream()
                .filter(shop -> shop != null && shop.getShopId() != null)
                .collect(Collectors.toMap(
                        shop -> shop.getShopId(),
                        shop -> shop,
                        (existing, replacement) -> existing // 保留第一个遇到的商户
                ))
                .values()
                .stream()
                .collect(Collectors.toList());
    }
    
    /**
     * 创建批次请求对象
     */
    private FillInfoRequest createBatchRequest(FillInfoRequest originalRequest, List<GoodsQry> batch, List<ShopQry> uniqueShops) {
        FillInfoRequest batchRequest = convertByJson(originalRequest, FillInfoRequest.class);
        batchRequest.setGoodsQryList(batch);
        // 每个批次都使用完整的唯一商户列表，确保商户信息的完整性
        batchRequest.setShopQryList(uniqueShops);
        return batchRequest;
    }
    
    /**
     * 将列表分批
     */
    private <T> List<List<T>> splitIntoBatches(List<T> list, int batchSize) {
        List<List<T>> batches = new ArrayList<>();
        for (int i = 0; i < list.size(); i += batchSize) {
            int end = Math.min(i + batchSize, list.size());
            batches.add(new ArrayList<>(list.subList(i, end)));
        }
        return batches;
    }
    
    /**
     * 合并多个FillInfoDTO结果
     */
    private FillInfoDTO mergeResults(List<FillInfoDTO> results) {
        if (CollectionUtils.isEmpty(results)) {
            return new FillInfoDTO();
        }
        
        if (results.size() == 1) {
            return results.get(0);
        }
        
        FillInfoDTO merged = new FillInfoDTO();
        List<GoodsInfoDTO> allGoods = new ArrayList<>();
        List<ShopInfoDTO> allShops = new ArrayList<>();
        
        // 合并所有商品信息
        for (FillInfoDTO result : results) {
            if (result.getGoodsInfoDTOs() != null) {
                allGoods.addAll(result.getGoodsInfoDTOs());
            }
            if (result.getShopInfos() != null) {
                allShops.addAll(result.getShopInfos());
            }
        }
        
        // 去重店铺信息（基于shopId）
        Map<Long, ShopInfoDTO> uniqueShops = allShops.stream()
                .filter(shop -> shop.getShopId() != null)
                .collect(Collectors.toMap(ShopInfoDTO::getShopId, shop -> shop, (existing, replacement) -> existing));
        
        merged.setGoodsInfoDTOs(allGoods);
        merged.setShopInfos(new ArrayList<>(uniqueShops.values()));
        
        log.info("合并结果完成，总商品数: {}, 总店铺数: {}", allGoods.size(), uniqueShops.size());
        
        return merged;
    }

    /**
     * 转换为大模型友好的JSON格式
     */
    private String convertToAIFriendlyFormat(FillInfoDTO fillInfoDTO, List<GoodsQry> goodsQryList) {
        if (fillInfoDTO == null) {
            return "{}";
        }

        Map<String, Object> result = new HashMap<>();

        // 构建商品ID到商户ID列表的映射关系（一个商品可能属于多个商户）
        Map<Long, List<Long>> goodsToShopMap = new HashMap<>();
        if (goodsQryList != null) {
            for (GoodsQry goodsQry : goodsQryList) {
                if (goodsQry.getGoodsId() != null && goodsQry.getShopQry() != null && goodsQry.getShopQry().getShopId() != null) {
                    goodsToShopMap.computeIfAbsent(goodsQry.getGoodsId(), k -> new ArrayList<>())
                            .add(goodsQry.getShopQry().getShopId());
                }
            }
        }

        // 将商品按商户ID分组
        Map<Long, List<GoodsInfoDTO>> goodsByShopMap = new HashMap<>();
        if (fillInfoDTO.getGoodsInfoDTOs() != null) {
            for (GoodsInfoDTO goods : fillInfoDTO.getGoodsInfoDTOs()) {
                List<Long> shopIds = goodsToShopMap.get(goods.getGoodsId());
                if (CollectionUtils.isNotEmpty(shopIds)) {
                    for (Long shopId : shopIds) {
                        goodsByShopMap.computeIfAbsent(shopId, k -> new ArrayList<>()).add(goods);
                    }
                }
            }
        }

        // 转换店铺信息，并将对应的商品放入其中
        if (fillInfoDTO.getShopInfos() != null && !fillInfoDTO.getShopInfos().isEmpty()) {
            List<Map<String, Object>> shopList = fillInfoDTO.getShopInfos().stream()
                .map(shop -> {
                    Map<String, Object> shopMap = convertShopToMap(shop);

                    // 获取该商户下的商品列表
                    List<GoodsInfoDTO> shopGoods = goodsByShopMap.get(shop.getShopId());
                    if (shopGoods != null && !shopGoods.isEmpty()) {
                        List<Map<String, Object>> goodsList = shopGoods.stream()
                            .map(this::convertGoodsToMap)
                            .collect(Collectors.toList());
                        shopMap.put("商品信息列表", goodsList);
                    }

                    return shopMap;
                })
                .collect(Collectors.toList());
            result.put("商户信息列表", shopList);
        }

        return JSON.toJSONString(result);
    }

    /**
     * 将店铺信息转换为Map格式，便于大模型理解
     */
    private Map<String, Object> convertShopToMap(ShopInfoDTO shop) {
        Map<String, Object> shopMap = new HashMap<>();

        shopMap.put("商户ID", shop.getShopId());
        shopMap.put("商户名称（含分店）", shop.getShopName());
        shopMap.put("星级", shop.getShopStarScore());
        shopMap.put("评价数", shop.getShopEvaluationCount());
        shopMap.put("距离", shop.getShopDistance());

        // 转换B端信息
        if (shop.getShopInfoBSideDTO() != null) {
            shopMap.put("商户信息（B端）", convertShopBSideToMap(shop.getShopInfoBSideDTO()));
        }

        return shopMap;
    }

    /**
     * 将商品信息转换为Map格式，便于大模型理解
     */
    private Map<String, Object> convertGoodsToMap(GoodsInfoDTO goods) {
        Map<String, Object> goodsMap = new HashMap<>();

        goodsMap.put("商品ID", goods.getGoodsId());
        goodsMap.put("商品名称", goods.getGoodsName());
        goodsMap.put("到手价", goods.getFinalPrice());
        goodsMap.put("核销量", goods.getVerifiedSales());
//        goodsMap.put("是否是百亿补贴商品", "inTenBillionActivity".equals(goods.getGoodsBillionSubsidyStr()) ? "是" : "否");
//        goodsMap.put("是否是放心美商品", "EasyBeauty".equals(goods.getEasyBeautyTag()) ? "是" : "否");
//        goodsMap.put("是否是无忧商品", "NoWorry".equals(goods.getNoWorryTag()) ? "是" : "否");

        // 转换B端信息
        if (goods.getGoodsInfoBSideDTO() != null) {
            goodsMap.put("商品cpv信息", convertGoodsBSideToMap(goods.getGoodsInfoBSideDTO()));
        }

        return goodsMap;
    }

    /**
     * 转换店铺B端信息为更友好的格式
     */
    private Map<String, Object> convertShopBSideToMap(ShopInfoBSideDTO bSide) {
        Map<String, Object> bSideMap = new HashMap<>();
        bSideMap.put("三个月回头客率", bSide.getThreeMonthReturnRate());
        bSideMap.put("医疗专业力分数", bSide.getMedicalProfessionalism());
        bSideMap.put("服务体验分数", bSide.getServiceExperience());
        bSideMap.put("好评数", bSide.getGoodReviewCount());
        bSideMap.put("医生面诊分数", bSide.getFaceToFaceConsultation());
        bSideMap.put("效果满意度分数", bSide.getEffectSatisfaction());
        return bSideMap;
    }

    /**
     * 转换商品B端信息为更友好的格式
     */
    private Map<String, Object> convertGoodsBSideToMap(GoodsInfoBSideDTO bSide) {
        Map<String, Object> bSideMap = new HashMap<>();

        Map<String,String> translationMap = Lion.getMap("com.sankuai.dzhealth.ai.service", "cpv.translation.map", String.class, cpvTranslationMap);

        if (bSide.getCpvAttrList() != null && !bSide.getCpvAttrList().isEmpty()) {
            Map<String, Object> attrMap = translateCpvAttr(bSide, translationMap);

            bSideMap.put("属性列表", attrMap);
        }

        return bSideMap;
    }

    @NotNull
    private static Map<String, Object> translateCpvAttr(GoodsInfoBSideDTO bSide, Map<String, String> translationMap) {
        return bSide.getCpvAttrList().stream()
                .filter(attr -> translationMap.containsKey(attr.getKey()))
                .collect(Collectors.toMap(attr -> translationMap.get(attr.getKey()), attr -> attr.getValue() != null ? attr.getValue() : attr.getValues()));
    }

    /**
     * 通过 JSON 序列化实现对象转换（深拷贝）
     * @param source 源对象
     * @param targetClass 目标类型
     * @return 转换后的对象
     */
    private <T> T convertByJson(Object source, Class<T> targetClass) {
        if (source == null) {
            return null;
        }

        try {
            // 先序列化为 JSON 字符串
            String jsonString = JSON.toJSONString(source);
            // 再反序列化为目标类型
            return JSON.parseObject(jsonString, targetClass);
        } catch (Exception e) {
            log.error("Failed to convert object by JSON, source: {}, targetClass: {}",
                     source.getClass().getSimpleName(), targetClass.getSimpleName(), e);
            throw new RuntimeException("对象转换失败", e);
        }
    }
}
