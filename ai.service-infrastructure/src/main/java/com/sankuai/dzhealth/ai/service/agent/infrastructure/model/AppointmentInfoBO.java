package com.sankuai.dzhealth.ai.service.agent.infrastructure.model;

import com.sankuai.dzhealth.ai.service.agent.infrastructure.enums.AppointmentStatusEnum;
import com.sankuai.dzhealth.ai.service.agent.infrastructure.enums.ProductNameEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * 预约信息业务对象
 *
 * @author: jiyizhou
 * @time: 2025/7/10 21:00
 * @version: 0.0.1
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AppointmentInfoBO {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 用户编号
     */
    private Long userId;

    /**
     * 会话编号
     */
    private String sessionId;

    /**
     * 消息编号
     */
    private String msgId;

    /**
     * 帮约消息编号
     */
    private String reservedMsgId;

    /**
     * 预约项目
     */
    private ProductNameEnum productName;

    /**
     * 预约开始时间
     */
    private Date appointmentStartTime;

    /**
     * 预约结束时间
     */
    private Date appointmentEndTime;

    /**
     * 地址文字
     */
    private String positionTxt;

    /**
     * 地址经度
     */
    private Double lng;

    /**
     * 地址纬度
     */
    private Double lat;

    /**
     * 人数描述
     */
    private String personDesc;

    /**
     * 筛选项
     */
    private String filterItems;

    /**
     * 联系方式
     */
    private String phone;

    /**
     * 预约成功商户
     */
    private Long successShopId;

    /**
     * 外呼任务编号
     */
    private Long taskId;

    /**
     * 卡片信息
     */
    private String cardInfo;

    /**
     * 状态
     */
    private AppointmentStatusEnum status;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 预约单id
     */
    private Long leadId;

    /**
     * 备注信息
     */
    private String remark;

    /**
     * 商户列表
     */
    private List<Long> shopIdList;

    /**
     * 城市ID
     */
    private Integer cityId;

    /**
     * 是否提及项目
     */
    private Boolean isMentioned;

    /**
     * 搜索关键字
     */
    private String searchword;

    /**
     * 距离，默认5000
     */
    private Double distance;

    /**
     * 价格区间，示例 R100.0:200.0
     */
    private String price;
}

