package com.sankuai.dzhealth.ai.service.agent.infrastructure.dal.repository.evaluation;

import com.sankuai.dzhealth.ai.service.agent.infrastructure.dal.entity.MessageEvaluationResultEntity;
import com.sankuai.dzhealth.ai.service.agent.infrastructure.dal.example.MessageEvaluationResultEntityExample;
import com.sankuai.dzhealth.ai.service.agent.infrastructure.dal.mapper.MessageEvaluationResultEntityMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import javax.transaction.Transactional;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR> xia<PERSON><PERSON><PERSON>
 * @since : 2025/7/11 11:35
 */
@Repository
@Slf4j
public class MessageEvaluationResultRepository {

    @Autowired
    private MessageEvaluationResultEntityMapper messageEvaluationResultEntityMapper;

    /**
     * evaluationId + evaluationVer + sessionId + messageId + bizScene + modelScene + item 唯一确定一条记录
     * <p>
     * evaluation评测会对一个message的相同指标评测多次
     *
     * @param messageResults
     */
    @Transactional
    public void batchInsert(List<MessageEvaluationResultEntity> messageResults) {
        for (MessageEvaluationResultEntity messageResult : messageResults) {
            MessageEvaluationResultEntityExample example = new MessageEvaluationResultEntityExample();
            MessageEvaluationResultEntityExample.Criteria criteria = example.createCriteria();
            criteria.andEvaluationIdEqualTo(messageResult.getEvaluationId())
                    .andEvaluationVerEqualTo(messageResult.getEvaluationVer())
                    .andSessionIdEqualTo(messageResult.getSessionId()).andMessageIdEqualTo(messageResult.getMessageId())
                    .andBizSceneEqualTo(messageResult.getBizScene()).andModelSceneEqualTo(messageResult.getModelScene())
                    .andItemEqualTo(messageResult.getItem());
            List<MessageEvaluationResultEntity> messageEvaluationResultEntities = messageEvaluationResultEntityMapper
                    .selectByExample(example);
            if (!messageEvaluationResultEntities.isEmpty()) {
                if (messageEvaluationResultEntities.size() == 1) {
                    MessageEvaluationResultEntity exists = messageEvaluationResultEntities.get(0);
                    messageResult.setId(exists.getId());
                    int count = messageEvaluationResultEntityMapper.updateByPrimaryKeySelective(messageResult);
                    log.info("update message evaluation result count:{}, result:{}", count, messageResult);
                } else {
                    log.warn(
                            "Duplicate message evaluation result,evaluationId:{}, ver:{}, sessionId:{}, messageId:{}, bizScene:{}, modelScene:{}, item: {}",
                            messageResult.getEvaluationId(), messageResult.getEvaluationVer(),
                            messageResult.getSessionId(), messageResult.getMessageId(), messageResult.getBizScene(),
                            messageResult.getModelScene(), messageResult.getItem());
                }
            } else {
                messageEvaluationResultEntityMapper.insert(messageResult);
            }
        }
    }

    public List<MessageEvaluationResultEntity> selectBySessionIdAndMessageId(String messageId) {
        log.info("selectBySessionIdAndMessageId  messageId:{}", messageId);
        MessageEvaluationResultEntityExample example = new MessageEvaluationResultEntityExample();
        MessageEvaluationResultEntityExample.Criteria criteria = example.createCriteria();
//        if (StringUtils.isNotBlank(sessionId)) {
//            criteria.andSessionIdEqualTo(sessionId);
//        } else {
//            return Collections.emptyList();
//        }
        if (StringUtils.isNotBlank(messageId)) {
            criteria.andMessageIdEqualTo(messageId);
        }

        List<MessageEvaluationResultEntity> messageEvaluationResultEntities = messageEvaluationResultEntityMapper
                .selectByExample(example);
        log.info("selectBySessionIdAndMessageId size:{}, result:{}",
                Optional.of(messageEvaluationResultEntities.size()), messageEvaluationResultEntities);
        return messageEvaluationResultEntities;
    }
}
