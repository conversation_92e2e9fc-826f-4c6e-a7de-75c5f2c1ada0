package com.sankuai.dzhealth.ai.service.infrastructure.repository.deepsearch;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.sankuai.dzhealth.ai.service.infrastructure.dal.entity.DeepSearchResultEntity;
import com.sankuai.dzhealth.ai.service.infrastructure.dal.example.DeepSearchResultEntityExample;
import com.sankuai.dzhealth.ai.service.infrastructure.dal.mapper.DeepSearchResultEntityMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;

/**
 * 深度搜索结果仓储
 */
@Slf4j
@Repository
@RequiredArgsConstructor
public class DeepSearchResultRepository {

    private final DeepSearchResultEntityMapper deepSearchResultEntityMapper;
    private final ObjectMapper objectMapper;

    /**
     * 保存深度搜索结果
     *
     * @param businessSource 业务来源标识
     * @param question 用户问题
     * @param answer 深度搜索生成的答案
     * @param sourceUrls 信息来源URL列表
     * @param confidenceScore 置信度评分
     * @return 记录ID
     */
    public Long save(String businessSource, String question, String answer,
                    List<String> sourceUrls, BigDecimal confidenceScore) {
        DeepSearchResultEntity entity = new DeepSearchResultEntity();
        entity.setBusinessSource(businessSource);
        entity.setQuestion(question);
        entity.setAnswer(answer);
        entity.setConfidenceScore(confidenceScore);

        try {
            if (sourceUrls != null && !sourceUrls.isEmpty()) {
                entity.setSourceUrls(objectMapper.writeValueAsString(sourceUrls));
            }
        } catch (JsonProcessingException e) {
            log.error("序列化sourceUrls失败", e);
            throw new RuntimeException("序列化sourceUrls失败", e);
        }

        deepSearchResultEntityMapper.insertSelective(entity);
        return entity.getId();
    }

    /**
     * 根据ID获取深度搜索结果
     *
     * @param resultId 结果ID
     * @return 深度搜索结果实体
     */
    public DeepSearchResultEntity findById(Long resultId) {
        return deepSearchResultEntityMapper.selectByPrimaryKey(resultId);
    }

    /**
     * 根据业务来源获取结果列表
     *
     * @param businessSource 业务来源
     * @return 结果列表
     */
    public List<DeepSearchResultEntity> findByBusinessSource(String businessSource) {
        DeepSearchResultEntityExample example = new DeepSearchResultEntityExample();
        example.createCriteria().andBusinessSourceEqualTo(businessSource);
        example.setOrderByClause("add_time DESC");

        return deepSearchResultEntityMapper.selectByExampleWithBLOBs(example);
    }

    /**
     * 更新深度搜索结果
     *
     * @param resultId 结果ID
     * @param answer 答案
     * @param sourceUrls 来源URL列表
     * @param confidenceScore 置信度评分
     */
    public void update(Long resultId, String answer, List<String> sourceUrls,
                      BigDecimal confidenceScore) {
        DeepSearchResultEntity entity = new DeepSearchResultEntity();
        entity.setId(resultId);
        entity.setAnswer(answer);
        entity.setConfidenceScore(confidenceScore);

        try {
            if (sourceUrls != null) {
                entity.setSourceUrls(objectMapper.writeValueAsString(sourceUrls));
            }
        } catch (JsonProcessingException e) {
            log.error("序列化sourceUrls失败", e);
            throw new RuntimeException("序列化sourceUrls失败", e);
        }

        deepSearchResultEntityMapper.updateByPrimaryKeySelective(entity);
    }

    /**
     * 根据置信度阈值获取高质量结果
     *
     * @param businessSource 业务来源
     * @param confidenceThreshold 置信度阈值
     * @return 高质量结果列表
     */
    public List<DeepSearchResultEntity> findHighQualityResults(String businessSource, BigDecimal confidenceThreshold) {
        DeepSearchResultEntityExample example = new DeepSearchResultEntityExample();
        example.createCriteria()
                .andBusinessSourceEqualTo(businessSource)
                .andConfidenceScoreGreaterThanOrEqualTo(confidenceThreshold);
        example.setOrderByClause("confidence_score DESC, add_time DESC");

        return deepSearchResultEntityMapper.selectByExampleWithBLOBs(example);
    }
} 