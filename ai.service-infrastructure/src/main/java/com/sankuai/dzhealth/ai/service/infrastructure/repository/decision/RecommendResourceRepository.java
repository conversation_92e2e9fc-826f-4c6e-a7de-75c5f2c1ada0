package com.sankuai.dzhealth.ai.service.infrastructure.repository.decision;

import com.sankuai.dzhealth.ai.service.enums.DecisionFlowElementStatusEnum;
import com.sankuai.dzhealth.ai.service.infrastructure.dal.entity.RecommendResourceDO;
import com.sankuai.dzhealth.ai.service.infrastructure.dal.example.RecommendResourceEntityExample;
import com.sankuai.dzhealth.ai.service.infrastructure.dal.mapper.RecommendResourceEntityMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 推荐资源仓储
 */
@Repository
@RequiredArgsConstructor
public class RecommendResourceRepository {

    private final RecommendResourceEntityMapper resourceMapper;

    private static final String STATUS_ONLINE_DELETE = DecisionFlowElementStatusEnum.ONLINE_DELETE.code();
    private static final String STATUS_GRAY_DELETE = DecisionFlowElementStatusEnum.GRAY_DELETE.code();

    public RecommendResourceDO findByResourceId(String resourceId) {
        RecommendResourceEntityExample example = new RecommendResourceEntityExample();
        example.createCriteria().andResourceIdEqualTo(resourceId);
        List<RecommendResourceDO> list = resourceMapper.selectByExample(example);
        return list.isEmpty() ? null : list.get(0);
    }

    /**
     * 插入资源记录
     */
    public int insert(RecommendResourceDO entity) {
        return resourceMapper.insertSelective(entity);
    }

    /**
     * 更新资源记录
     */
    public int update(RecommendResourceDO entity) {
        return resourceMapper.updateByPrimaryKeySelective(entity);
    }

    /**
     * 批量插入资源
     */
    public int batchInsert(List<RecommendResourceDO> entities){
        if(entities==null || entities.isEmpty()) return 0;
        return resourceMapper.insertList(entities);
    }

    /**
     * 根据resourceId更新资源记录
     */
    public int updateByResourceId(RecommendResourceDO entity) {
        RecommendResourceEntityExample example = new RecommendResourceEntityExample();
        example.createCriteria().andResourceIdEqualTo(entity.getResourceId());
        return resourceMapper.updateByExampleSelective(entity, example);
    }

    /**
     * 根据resourceId删除资源记录（软删除）
     */
    public int deleteByResourceId(String resourceId) {
        RecommendResourceDO entity = new RecommendResourceDO();
        entity.setStatus(STATUS_ONLINE_DELETE);
        RecommendResourceEntityExample example = new RecommendResourceEntityExample();
        example.createCriteria().andResourceIdEqualTo(resourceId);
        return resourceMapper.updateByExampleSelective(entity, example);
    }

    /**
     * 查询指定资源类型的所有在线资源
     */
    public List<RecommendResourceDO> listByResourceType(String resourceType) {
        RecommendResourceEntityExample example = new RecommendResourceEntityExample();
        example.createCriteria()
                .andResourceTypeEqualTo(resourceType)
                .andStatusEqualTo(DecisionFlowElementStatusEnum.ONLINE.code());
        return resourceMapper.selectByExample(example);
    }

    /**
     * 基于resourceName和resourceType查找resource（用于去重）
     */
    public RecommendResourceDO findByNameAndType(String resourceName, String resourceType) {
        RecommendResourceEntityExample example = new RecommendResourceEntityExample();
        example.createCriteria()
                .andResourceNameEqualTo(resourceName)
                .andResourceTypeEqualTo(resourceType)
                .andStatusNotIn(java.util.List.of(STATUS_ONLINE_DELETE, STATUS_GRAY_DELETE));
        List<RecommendResourceDO> list = resourceMapper.selectByExample(example);
        return list.isEmpty() ? null : list.get(0);
    }

    /**
     * 按状态查询资源（可选 resourceType 为 null 代表全部类型）
     */
    public List<RecommendResourceDO> listByResourceTypeAndStatus(String resourceType, String status) {
        RecommendResourceEntityExample example = new RecommendResourceEntityExample();
        RecommendResourceEntityExample.Criteria c = example.createCriteria();
        if (resourceType != null) {
            c.andResourceTypeEqualTo(resourceType);
        }
        c.andStatusEqualTo(status);
        return resourceMapper.selectByExample(example);
    }

    /**
     * 按状态查询全部资源
     */
    public List<RecommendResourceDO> listByStatus(String status) {
        RecommendResourceEntityExample example = new RecommendResourceEntityExample();
        example.createCriteria().andStatusEqualTo(status);
        return resourceMapper.selectByExample(example);
    }

    /**
     * 更新状态
     */
    public int updateStatusByResourceId(String resourceId, String status) {
        RecommendResourceDO entity = new RecommendResourceDO();
        entity.setStatus(status);
        RecommendResourceEntityExample example = new RecommendResourceEntityExample();
        example.createCriteria().andResourceIdEqualTo(resourceId);
        return resourceMapper.updateByExampleSelective(entity, example);
    }

    /**
     * 根据业务场景和状态查询资源
     */
    public List<RecommendResourceDO> listByBizSceneAndStatus(String bizScene, String status) {
        RecommendResourceEntityExample example = new RecommendResourceEntityExample();
        example.createCriteria()
                .andBizSceneEqualTo(bizScene)
                .andStatusEqualTo(status);
        return resourceMapper.selectByExample(example);
    }

    /**
     * 根据业务场景和资源类型查询在线资源
     */
    public List<RecommendResourceDO> listByBizSceneAndResourceType(String bizScene, String resourceType) {
        RecommendResourceEntityExample example = new RecommendResourceEntityExample();
        example.createCriteria()
                .andBizSceneEqualTo(bizScene)
                .andResourceTypeEqualTo(resourceType)
                .andStatusEqualTo(DecisionFlowElementStatusEnum.ONLINE.code());
        return resourceMapper.selectByExample(example);
    }

    /**
     * 根据业务场景、资源类型和状态查询资源
     */
    public List<RecommendResourceDO> listByBizSceneAndResourceTypeAndStatus(String bizScene, String resourceType, String status) {
        RecommendResourceEntityExample example = new RecommendResourceEntityExample();
        example.createCriteria()
                .andBizSceneEqualTo(bizScene)
                .andResourceTypeEqualTo(resourceType)
                .andStatusEqualTo(status);
        return resourceMapper.selectByExample(example);
    }

    /**
     * 根据业务场景、资源名称和类型查找资源（用于去重）
     */
    public RecommendResourceDO findByBizSceneAndNameAndType(String bizScene, String resourceName, String resourceType) {
        RecommendResourceEntityExample example = new RecommendResourceEntityExample();
        example.createCriteria()
                .andBizSceneEqualTo(bizScene)
                .andResourceNameEqualTo(resourceName)
                .andResourceTypeEqualTo(resourceType)
                .andStatusNotIn(java.util.List.of(STATUS_ONLINE_DELETE, STATUS_GRAY_DELETE));
        List<RecommendResourceDO> list = resourceMapper.selectByExample(example);
        return list.isEmpty() ? null : list.get(0);
    }

    /**
     * 根据业务场景查询所有在线资源
     */
    public List<RecommendResourceDO> listByBizScene(String bizScene) {
        RecommendResourceEntityExample example = new RecommendResourceEntityExample();
        example.createCriteria()
                .andBizSceneEqualTo(bizScene)
                .andStatusEqualTo(DecisionFlowElementStatusEnum.ONLINE.code());
        return resourceMapper.selectByExample(example);
    }

    /**
     * 根据业务场景删除资源记录（软删除）
     */
    public int deleteByBizScene(String bizScene) {
        RecommendResourceDO entity = new RecommendResourceDO();
        entity.setStatus(STATUS_ONLINE_DELETE);
        RecommendResourceEntityExample example = new RecommendResourceEntityExample();
        example.createCriteria().andBizSceneEqualTo(bizScene);
        return resourceMapper.updateByExampleSelective(entity, example);
    }

    /**
     * 批量更新资源状态
     */
    public int batchUpdateByResourceIds(List<RecommendResourceDO> entities){
        if(entities==null || entities.isEmpty()) return 0;
        int cnt=0;
        for(RecommendResourceDO e:entities){
            cnt += updateByResourceId(e);
        }
        return cnt;
    }

    public int batchUpdateStatusByResourceIds(List<String> resourceIds, String status) {
        if (resourceIds == null || resourceIds.isEmpty()) {
            return 0;
        }
        RecommendResourceDO entity = new RecommendResourceDO();
        entity.setStatus(status);
        RecommendResourceEntityExample example = new RecommendResourceEntityExample();
        example.createCriteria().andResourceIdIn(resourceIds);
        return resourceMapper.updateByExampleSelective(entity, example);
    }

    /**
     * 批量删除资源记录（软删除）
     */
    public int batchDeleteByResourceIds(List<String> resourceIds) {
        if (resourceIds == null || resourceIds.isEmpty()) {
            return 0;
        }
        RecommendResourceDO entity = new RecommendResourceDO();
        entity.setStatus(STATUS_ONLINE_DELETE);
        RecommendResourceEntityExample example = new RecommendResourceEntityExample();
        example.createCriteria().andResourceIdIn(resourceIds);
        return resourceMapper.updateByExampleSelective(entity, example);
    }

    /**
     * 物理删除指定业务场景下的所有资源
     */
    public int hardDeleteByBizScene(String bizScene) {
        RecommendResourceEntityExample example = new RecommendResourceEntityExample();
        example.createCriteria().andBizSceneEqualTo(bizScene);
        return resourceMapper.deleteByExample(example);
    }
} 