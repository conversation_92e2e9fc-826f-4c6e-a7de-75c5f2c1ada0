package com.sankuai.dzhealth.ai.service.infrastructure.dal.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 *
 *   表名: answer_context
 */
@SuperBuilder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AiAnswerContextEntityWithBLOBs extends AiAnswerContextEntity {
    /**
     *   字段: content
     *   说明: 请求的具体内容
     */
    private String content;

    /**
     *   字段: history
     *   说明: 历史上下文的摘要信息
     */
    private String history;

    /**
     *   字段: assistantVisibleContent
     *   说明: 回复的可见内容
     */
    private String assistantvisiblecontent;

    /**
     *   字段: assistantContent
     *   说明: 回复的完整内容
     */
    private String assistantcontent;
}