package com.sankuai.dzhealth.ai.service.infrastructure.dal.example;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class ThinkingSessionEntityExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public ThinkingSessionEntityExample() {
        oredCriteria = new ArrayList<>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andQueryIsNull() {
            addCriterion("query is null");
            return (Criteria) this;
        }

        public Criteria andQueryIsNotNull() {
            addCriterion("query is not null");
            return (Criteria) this;
        }

        public Criteria andQueryEqualTo(String value) {
            addCriterion("query =", value, "query");
            return (Criteria) this;
        }

        public Criteria andQueryNotEqualTo(String value) {
            addCriterion("query <>", value, "query");
            return (Criteria) this;
        }

        public Criteria andQueryGreaterThan(String value) {
            addCriterion("query >", value, "query");
            return (Criteria) this;
        }

        public Criteria andQueryGreaterThanOrEqualTo(String value) {
            addCriterion("query >=", value, "query");
            return (Criteria) this;
        }

        public Criteria andQueryLessThan(String value) {
            addCriterion("query <", value, "query");
            return (Criteria) this;
        }

        public Criteria andQueryLessThanOrEqualTo(String value) {
            addCriterion("query <=", value, "query");
            return (Criteria) this;
        }

        public Criteria andQueryLike(String value) {
            addCriterion("query like", value, "query");
            return (Criteria) this;
        }

        public Criteria andQueryNotLike(String value) {
            addCriterion("query not like", value, "query");
            return (Criteria) this;
        }

        public Criteria andQueryIn(List<String> values) {
            addCriterion("query in", values, "query");
            return (Criteria) this;
        }

        public Criteria andQueryNotIn(List<String> values) {
            addCriterion("query not in", values, "query");
            return (Criteria) this;
        }

        public Criteria andQueryBetween(String value1, String value2) {
            addCriterion("query between", value1, value2, "query");
            return (Criteria) this;
        }

        public Criteria andQueryNotBetween(String value1, String value2) {
            addCriterion("query not between", value1, value2, "query");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("status is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("status is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(String value) {
            addCriterion("status =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(String value) {
            addCriterion("status <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(String value) {
            addCriterion("status >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(String value) {
            addCriterion("status >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(String value) {
            addCriterion("status <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(String value) {
            addCriterion("status <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLike(String value) {
            addCriterion("status like", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotLike(String value) {
            addCriterion("status not like", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<String> values) {
            addCriterion("status in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<String> values) {
            addCriterion("status not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(String value1, String value2) {
            addCriterion("status between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(String value1, String value2) {
            addCriterion("status not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andSearchResultsIsNull() {
            addCriterion("search_results is null");
            return (Criteria) this;
        }

        public Criteria andSearchResultsIsNotNull() {
            addCriterion("search_results is not null");
            return (Criteria) this;
        }

        public Criteria andSearchResultsEqualTo(String value) {
            addCriterion("search_results =", value, "searchResults");
            return (Criteria) this;
        }

        public Criteria andSearchResultsNotEqualTo(String value) {
            addCriterion("search_results <>", value, "searchResults");
            return (Criteria) this;
        }

        public Criteria andSearchResultsGreaterThan(String value) {
            addCriterion("search_results >", value, "searchResults");
            return (Criteria) this;
        }

        public Criteria andSearchResultsGreaterThanOrEqualTo(String value) {
            addCriterion("search_results >=", value, "searchResults");
            return (Criteria) this;
        }

        public Criteria andSearchResultsLessThan(String value) {
            addCriterion("search_results <", value, "searchResults");
            return (Criteria) this;
        }

        public Criteria andSearchResultsLessThanOrEqualTo(String value) {
            addCriterion("search_results <=", value, "searchResults");
            return (Criteria) this;
        }

        public Criteria andSearchResultsLike(String value) {
            addCriterion("search_results like", value, "searchResults");
            return (Criteria) this;
        }

        public Criteria andSearchResultsNotLike(String value) {
            addCriterion("search_results not like", value, "searchResults");
            return (Criteria) this;
        }

        public Criteria andSearchResultsIn(List<String> values) {
            addCriterion("search_results in", values, "searchResults");
            return (Criteria) this;
        }

        public Criteria andSearchResultsNotIn(List<String> values) {
            addCriterion("search_results not in", values, "searchResults");
            return (Criteria) this;
        }

        public Criteria andSearchResultsBetween(String value1, String value2) {
            addCriterion("search_results between", value1, value2, "searchResults");
            return (Criteria) this;
        }

        public Criteria andSearchResultsNotBetween(String value1, String value2) {
            addCriterion("search_results not between", value1, value2, "searchResults");
            return (Criteria) this;
        }

        public Criteria andSearchConfigIsNull() {
            addCriterion("search_config is null");
            return (Criteria) this;
        }

        public Criteria andSearchConfigIsNotNull() {
            addCriterion("search_config is not null");
            return (Criteria) this;
        }

        public Criteria andSearchConfigEqualTo(String value) {
            addCriterion("search_config =", value, "searchConfig");
            return (Criteria) this;
        }

        public Criteria andSearchConfigNotEqualTo(String value) {
            addCriterion("search_config <>", value, "searchConfig");
            return (Criteria) this;
        }

        public Criteria andSearchConfigGreaterThan(String value) {
            addCriterion("search_config >", value, "searchConfig");
            return (Criteria) this;
        }

        public Criteria andSearchConfigGreaterThanOrEqualTo(String value) {
            addCriterion("search_config >=", value, "searchConfig");
            return (Criteria) this;
        }

        public Criteria andSearchConfigLessThan(String value) {
            addCriterion("search_config <", value, "searchConfig");
            return (Criteria) this;
        }

        public Criteria andSearchConfigLessThanOrEqualTo(String value) {
            addCriterion("search_config <=", value, "searchConfig");
            return (Criteria) this;
        }

        public Criteria andSearchConfigLike(String value) {
            addCriterion("search_config like", value, "searchConfig");
            return (Criteria) this;
        }

        public Criteria andSearchConfigNotLike(String value) {
            addCriterion("search_config not like", value, "searchConfig");
            return (Criteria) this;
        }

        public Criteria andSearchConfigIn(List<String> values) {
            addCriterion("search_config in", values, "searchConfig");
            return (Criteria) this;
        }

        public Criteria andSearchConfigNotIn(List<String> values) {
            addCriterion("search_config not in", values, "searchConfig");
            return (Criteria) this;
        }

        public Criteria andSearchConfigBetween(String value1, String value2) {
            addCriterion("search_config between", value1, value2, "searchConfig");
            return (Criteria) this;
        }

        public Criteria andSearchConfigNotBetween(String value1, String value2) {
            addCriterion("search_config not between", value1, value2, "searchConfig");
            return (Criteria) this;
        }

        public Criteria andTotalPlannedStepsIsNull() {
            addCriterion("total_planned_steps is null");
            return (Criteria) this;
        }

        public Criteria andTotalPlannedStepsIsNotNull() {
            addCriterion("total_planned_steps is not null");
            return (Criteria) this;
        }

        public Criteria andTotalPlannedStepsEqualTo(Long value) {
            addCriterion("total_planned_steps =", value, "totalPlannedSteps");
            return (Criteria) this;
        }

        public Criteria andTotalPlannedStepsNotEqualTo(Long value) {
            addCriterion("total_planned_steps <>", value, "totalPlannedSteps");
            return (Criteria) this;
        }

        public Criteria andTotalPlannedStepsGreaterThan(Long value) {
            addCriterion("total_planned_steps >", value, "totalPlannedSteps");
            return (Criteria) this;
        }

        public Criteria andTotalPlannedStepsGreaterThanOrEqualTo(Long value) {
            addCriterion("total_planned_steps >=", value, "totalPlannedSteps");
            return (Criteria) this;
        }

        public Criteria andTotalPlannedStepsLessThan(Long value) {
            addCriterion("total_planned_steps <", value, "totalPlannedSteps");
            return (Criteria) this;
        }

        public Criteria andTotalPlannedStepsLessThanOrEqualTo(Long value) {
            addCriterion("total_planned_steps <=", value, "totalPlannedSteps");
            return (Criteria) this;
        }

        public Criteria andTotalPlannedStepsIn(List<Long> values) {
            addCriterion("total_planned_steps in", values, "totalPlannedSteps");
            return (Criteria) this;
        }

        public Criteria andTotalPlannedStepsNotIn(List<Long> values) {
            addCriterion("total_planned_steps not in", values, "totalPlannedSteps");
            return (Criteria) this;
        }

        public Criteria andTotalPlannedStepsBetween(Long value1, Long value2) {
            addCriterion("total_planned_steps between", value1, value2, "totalPlannedSteps");
            return (Criteria) this;
        }

        public Criteria andTotalPlannedStepsNotBetween(Long value1, Long value2) {
            addCriterion("total_planned_steps not between", value1, value2, "totalPlannedSteps");
            return (Criteria) this;
        }

        public Criteria andActualStepsIsNull() {
            addCriterion("actual_steps is null");
            return (Criteria) this;
        }

        public Criteria andActualStepsIsNotNull() {
            addCriterion("actual_steps is not null");
            return (Criteria) this;
        }

        public Criteria andActualStepsEqualTo(Long value) {
            addCriterion("actual_steps =", value, "actualSteps");
            return (Criteria) this;
        }

        public Criteria andActualStepsNotEqualTo(Long value) {
            addCriterion("actual_steps <>", value, "actualSteps");
            return (Criteria) this;
        }

        public Criteria andActualStepsGreaterThan(Long value) {
            addCriterion("actual_steps >", value, "actualSteps");
            return (Criteria) this;
        }

        public Criteria andActualStepsGreaterThanOrEqualTo(Long value) {
            addCriterion("actual_steps >=", value, "actualSteps");
            return (Criteria) this;
        }

        public Criteria andActualStepsLessThan(Long value) {
            addCriterion("actual_steps <", value, "actualSteps");
            return (Criteria) this;
        }

        public Criteria andActualStepsLessThanOrEqualTo(Long value) {
            addCriterion("actual_steps <=", value, "actualSteps");
            return (Criteria) this;
        }

        public Criteria andActualStepsIn(List<Long> values) {
            addCriterion("actual_steps in", values, "actualSteps");
            return (Criteria) this;
        }

        public Criteria andActualStepsNotIn(List<Long> values) {
            addCriterion("actual_steps not in", values, "actualSteps");
            return (Criteria) this;
        }

        public Criteria andActualStepsBetween(Long value1, Long value2) {
            addCriterion("actual_steps between", value1, value2, "actualSteps");
            return (Criteria) this;
        }

        public Criteria andActualStepsNotBetween(Long value1, Long value2) {
            addCriterion("actual_steps not between", value1, value2, "actualSteps");
            return (Criteria) this;
        }

        public Criteria andStartTimeIsNull() {
            addCriterion("start_time is null");
            return (Criteria) this;
        }

        public Criteria andStartTimeIsNotNull() {
            addCriterion("start_time is not null");
            return (Criteria) this;
        }

        public Criteria andStartTimeEqualTo(Date value) {
            addCriterion("start_time =", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeNotEqualTo(Date value) {
            addCriterion("start_time <>", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeGreaterThan(Date value) {
            addCriterion("start_time >", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("start_time >=", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeLessThan(Date value) {
            addCriterion("start_time <", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeLessThanOrEqualTo(Date value) {
            addCriterion("start_time <=", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeIn(List<Date> values) {
            addCriterion("start_time in", values, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeNotIn(List<Date> values) {
            addCriterion("start_time not in", values, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeBetween(Date value1, Date value2) {
            addCriterion("start_time between", value1, value2, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeNotBetween(Date value1, Date value2) {
            addCriterion("start_time not between", value1, value2, "startTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeIsNull() {
            addCriterion("end_time is null");
            return (Criteria) this;
        }

        public Criteria andEndTimeIsNotNull() {
            addCriterion("end_time is not null");
            return (Criteria) this;
        }

        public Criteria andEndTimeEqualTo(Date value) {
            addCriterion("end_time =", value, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeNotEqualTo(Date value) {
            addCriterion("end_time <>", value, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeGreaterThan(Date value) {
            addCriterion("end_time >", value, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("end_time >=", value, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeLessThan(Date value) {
            addCriterion("end_time <", value, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeLessThanOrEqualTo(Date value) {
            addCriterion("end_time <=", value, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeIn(List<Date> values) {
            addCriterion("end_time in", values, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeNotIn(List<Date> values) {
            addCriterion("end_time not in", values, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeBetween(Date value1, Date value2) {
            addCriterion("end_time between", value1, value2, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeNotBetween(Date value1, Date value2) {
            addCriterion("end_time not between", value1, value2, "endTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeIsNull() {
            addCriterion("add_time is null");
            return (Criteria) this;
        }

        public Criteria andAddTimeIsNotNull() {
            addCriterion("add_time is not null");
            return (Criteria) this;
        }

        public Criteria andAddTimeEqualTo(Date value) {
            addCriterion("add_time =", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotEqualTo(Date value) {
            addCriterion("add_time <>", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeGreaterThan(Date value) {
            addCriterion("add_time >", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("add_time >=", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeLessThan(Date value) {
            addCriterion("add_time <", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeLessThanOrEqualTo(Date value) {
            addCriterion("add_time <=", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeIn(List<Date> values) {
            addCriterion("add_time in", values, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotIn(List<Date> values) {
            addCriterion("add_time not in", values, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeBetween(Date value1, Date value2) {
            addCriterion("add_time between", value1, value2, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotBetween(Date value1, Date value2) {
            addCriterion("add_time not between", value1, value2, "addTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}