package com.sankuai.dzhealth.ai.service.infrastructure.utils;

import com.meituan.mdp.boot.starter.leaf.MdpLeafClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.UUID;

/**
 * 通用唯一 ID 生成工具
 * 按照 Leaf 官方接入文档实现：https://km.sankuai.com/collabpage/28286983
 */
@Slf4j
@Component
public class UidUtils {

    /**
     * Leaf Tag 常量定义
     */
    public static final String DECISION_FLOW_TAG = "medicine.dzhealth.aiservice.decisionflow";

    public static final String CHAT_EVALUATION_TAG = "medical.health.chat.evaluation";

    public static final String CHAT_MESSAGE_LEAF_KEY = "medical.health.chat.message";

    public static final String CHAT_SESSION_LEAF_KEY = "medical.health.chat.session";


    @Autowired
    @Qualifier("mdpLeafClient0")
    private MdpLeafClient mdpLeafClient;

    /**
     * 生成分布式唯一 ID（使用 Leaf Snowflake 模式）
     * MdpLeafClient 内部已实现重试逻辑，由配置 mdp.leaf.leafRetryTimes 控制
     *
     * @param tag Leaf 标签，需要在 Leaf 管理平台申请
     * @return 分布式唯一 ID
     */
    public long nextSnowflakeId(String tag) {
        try {
            // MdpLeafClient.getSnowFlake() 内部已实现重试逻辑，只对超时异常重试
            return mdpLeafClient.getSnowFlake(tag);
        } catch (Exception e) {
            // MdpLeafClient 重试失败后会抛出 MdpLeafException，进行降级处理
            log.error("Leaf Snowflake ID 生成最终失败，使用降级方案，tag={}", tag, e);
            return Math.abs(UUID.randomUUID().toString().hashCode());
        }
    }

    /**
     * 批量生成分布式唯一 ID（使用 Leaf Snowflake 模式）
     * 推荐高 QPS 场景使用，一次最多获取 100 个 ID
     * MdpLeafClient 内部已实现重试逻辑，由配置 mdp.leaf.leafRetryTimes 控制
     *
     * @param tag  Leaf 标签
     * @param size 需要获取的 ID 数量，最大 100
     * @return ID 列表
     */
    public List<Long> nextSnowflakeIdBatch(String tag, int size) {
        if (size <= 0) {
            throw new IllegalArgumentException("size 必须大于 0");
        }
        if (size > 100) {
            size = 100; // 限制最大批量数量
            log.warn("请求的 ID 数量超过 100，已限制为 100，tag={}, requestedSize={}", tag, size);
        }

        try {
            List<Long> results = mdpLeafClient.getSnowFlakeBatch(tag, size);
            if (results != null && !results.isEmpty()) {
                return results;
            } else {
                log.error("Leaf 批量 ID 生成异常，返回空列表，tag={}, size={}", tag, size);
                throw new RuntimeException("Leaf 批量 ID 生成异常，返回空列表");
            }
        } catch (Exception e) {
            log.error("Leaf Snowflake 批量 ID 生成最终失败，使用降级方案，tag={}, size={}", tag, size, e);
            // 降级方案：生成指定数量的 UUID
            return java.util.stream.IntStream.range(0, size)
                    .mapToObj(i -> (long) Math.abs(UUID.randomUUID().toString().hashCode()))
                    .collect(java.util.stream.Collectors.toList());
        }
    }

    /**
     * 生成决策流程唯一 ID（Snowflake 模式）
     *
     * @return 决策流程唯一 ID
     */
    public long nextDecisionFlowSnowflakeId() {
        return nextSnowflakeId(DECISION_FLOW_TAG);
    }

    /**
     * 批量生成决策流程唯一 ID（Snowflake 模式）
     *
     * @param size 需要获取的 ID 数量，最大 100
     * @return 决策流程 ID 列表
     */
    public List<Long> nextDecisionFlowSnowflakeIdBatch(int size) {
        return nextSnowflakeIdBatch(DECISION_FLOW_TAG, size);
    }


    public long getNextId(String key) {
        // MdpLeafClient.getSnowFlake() 内部已实现重试逻辑，只对超时异常重试
        return mdpLeafClient.get(key);

    }


    public String getRandomCardHashKey() {
        return UUID.randomUUID().toString().replaceAll("-", "");
    }
}
