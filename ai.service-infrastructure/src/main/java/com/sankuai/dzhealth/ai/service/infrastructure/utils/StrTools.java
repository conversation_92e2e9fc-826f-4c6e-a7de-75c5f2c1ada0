package com.sankuai.dzhealth.ai.service.infrastructure.utils;

import org.apache.commons.lang3.StringUtils;

public class StrTools {
    public static String trimMKjson(String markdown) {
        if (StringUtils.isBlank(markdown)) {
            return "";
        }
        if (markdown.startsWith("```json")) {
            markdown = markdown.substring(7);
        }

        if (markdown.endsWith("```")) {
            markdown = markdown.substring(0, markdown.length() - 3);

        }

        return markdown;
    }

    public static boolean isValidLong(String str) {
        try {
            Long.parseLong(str);
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }
}
