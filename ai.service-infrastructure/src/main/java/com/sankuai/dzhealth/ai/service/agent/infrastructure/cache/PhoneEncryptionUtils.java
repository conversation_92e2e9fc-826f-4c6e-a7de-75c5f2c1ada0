package com.sankuai.dzhealth.ai.service.agent.infrastructure.cache;


import com.sankuai.inf.kms.pangolin.api.model.EncryptionRequest;
import com.sankuai.inf.kms.pangolin.api.service.EncryptServiceFactory;
import com.sankuai.inf.kms.pangolin.api.service.IEncryptService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.security.GeneralSecurityException;

/**
 * 手机号加解密服务
 *
 * @author: jiyizhou
 * @time: 2025/7/14 14:00
 * @version: 0.0.1
 */
@Slf4j
@Service
public class PhoneEncryptionUtils {

    /**
     * KMS密钥名称
     */
    private static final String KEY_NAME = "phone";

    /**
     * 加密数据前缀标识
     */
    private static final String ENCRYPTED_PREFIX = "enc:";

    /**
     * 应用名称
     */
    private static final String APP_NAME = "com.sankuai.dzhealth.ai.service";

    /**
     * KMS加解密服务实例
     */
    private IEncryptService encryptService;

    /**
     * 初始化加解密服务
     */
    @PostConstruct
    public void init() {
        try {
            log.info("开始初始化手机号加密服务，密钥名称: {}", KEY_NAME);

            EncryptionRequest request = EncryptionRequest.Builder.anEncryptionRequest()
                    .withNamespace(APP_NAME)
                    .withKeyName(KEY_NAME)
                    .build();

            this.encryptService = EncryptServiceFactory.create(request);

            log.info("手机号加密服务初始化成功");
        } catch (Exception e) {
            log.error("手机号加密服务初始化失败", e);
        }
    }

    /**
     * 加密手机号
     *
     * @param plainText 明文手机号
     * @return 加密后的手机号（带enc:前缀）
     */
    public String encrypt(String plainText) {
        if (StringUtils.isBlank(plainText)) {
            return plainText;
        }

        try {
            String encrypted = encryptService.encryptUTF8String(plainText);
            String result = ENCRYPTED_PREFIX + encrypted;
            log.debug("手机号加密成功，原始长度: {}, 加密后长度: {}", plainText.length(), result.length());
            return result;
        } catch (GeneralSecurityException e) {
            log.error("手机号加密失败，原始数据长度: {}", plainText.length(), e);
            return null;
        }
    }

    /**
     * 解密手机号（兼容新旧数据）
     *
     * @param cipherText 密文手机号
     * @return 解密后的明文手机号
     */
    public String decrypt(String cipherText) {
        if (StringUtils.isBlank(cipherText)) {
            return cipherText;
        }

        // 判断是否为加密数据
        if (!cipherText.startsWith(ENCRYPTED_PREFIX)) {
            // 明文数据，直接返回（向后兼容）
            log.debug("检测到明文手机号数据，直接返回，数据长度: {}", cipherText.length());
            return cipherText;
        }

        try {
            // 去除前缀后解密
            String encryptedData = cipherText.substring(ENCRYPTED_PREFIX.length());
            String decrypted = encryptService.decryptUTF8String(encryptedData);
            log.debug("手机号解密成功，密文长度: {}, 明文长度: {}", cipherText.length(), decrypted.length());
            return decrypted;
        } catch (GeneralSecurityException e) {
            log.error("手机号解密失败，密文数据长度: {}", cipherText.length(), e);
            // 解密失败时返回null，避免暴露敏感信息
            return null;
        }
    }

    /**
     * 判断是否为加密数据
     *
     * @param data 待判断的数据
     * @return true-已加密, false-明文
     */
    public boolean isEncrypted(String data) {
        return StringUtils.isNotBlank(data) && data.startsWith(ENCRYPTED_PREFIX);
    }
}

