package com.sankuai.dzhealth.ai.service.infrastructure.dal.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * 表名: rag_evaluation_response
 */
@SuperBuilder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class RagEvaluationResponseEntityWithBLOBs extends RagEvaluationResponseEntity {
    /**
     * 字段: feedback
     * 说明: 评估反馈
     */
    private String feedback;

    /**
     * 字段: metadata
     * 说明: 元数据(JSON格式)
     */
    private String metadata;
}