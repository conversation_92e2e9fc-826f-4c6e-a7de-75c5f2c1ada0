package com.sankuai.dzhealth.ai.service.infrastructure.repository.decision;

import com.sankuai.dzhealth.ai.service.enums.DecisionFlowElementStatusEnum;
import com.sankuai.dzhealth.ai.service.infrastructure.dal.entity.DecisionNodeDO;
import com.sankuai.dzhealth.ai.service.infrastructure.dal.example.DecisionNodeEntityExample;
import com.sankuai.dzhealth.ai.service.infrastructure.dal.mapper.DecisionNodeEntityMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 决策步骤仓储
 * <p>
 * 主要负责 decision_node 表的 CRUD 操作，供领域层调用。
 */
@Repository
@RequiredArgsConstructor
public class DecisionNodeRepository {

    private final DecisionNodeEntityMapper nodeMapper;

    private static final String STATUS_ONLINE_DELETE = DecisionFlowElementStatusEnum.ONLINE_DELETE.code();
    private static final String STATUS_GRAY_DELETE   = DecisionFlowElementStatusEnum.GRAY_DELETE.code();

    /**
     * 根据主键查询
     */
    public DecisionNodeDO findById(Long id) {
        return nodeMapper.selectByPrimaryKey(id);
    }

    /**
     * 根据业务 nodeId 查询
     */
    public DecisionNodeDO findByNodeId(String nodeId) {
        DecisionNodeEntityExample example = new DecisionNodeEntityExample();
        example.createCriteria()
                .andNodeIdEqualTo(nodeId)
                .andStatusNotIn(Arrays.asList(STATUS_ONLINE_DELETE, STATUS_GRAY_DELETE));
        List<DecisionNodeDO> list = nodeMapper.selectByExample(example);
        return list.isEmpty() ? null : list.get(0);
    }

    /**
     * 查询指定业务场景下全部决策步骤
     */
    public List<DecisionNodeDO> listByBizScene(String bizScene) {
        DecisionNodeEntityExample example = new DecisionNodeEntityExample();
        example.createCriteria()
                .andBizSceneEqualTo(bizScene)
                .andStatusNotIn(Arrays.asList(STATUS_ONLINE_DELETE, STATUS_GRAY_DELETE));
        return nodeMapper.selectByExample(example);
    }

    /**
     * 查询指定场景且指定状态的节点
     */
    public List<DecisionNodeDO> listByBizSceneAndStatus(String bizScene, String status) {
        DecisionNodeEntityExample example = new DecisionNodeEntityExample();
        example.createCriteria()
                .andBizSceneEqualTo(bizScene)
                .andStatusEqualTo(status);
        return nodeMapper.selectByExample(example);
    }

    /**
     * 插入记录
     */
    public int insert(DecisionNodeDO entity) {
        // 设置 ext 字段默认值为空 JSON 对象，避免 MySQL JSON 字段报错
        if (entity.getExt() == null || entity.getExt().trim().isEmpty()) {
            entity.setExt("{}");
        }
        // 设置 assessment_img 字段默认值，避免 NOT NULL 约束报错
        if (entity.getAssessmentImg() == null) {
            entity.setAssessmentImg("");
        }
        return nodeMapper.insertSelective(entity);
    }
    /**
     * 批量插入记录
     */
    public int batchInsert(List<DecisionNodeDO> entities) {
        if (entities == null || entities.isEmpty()) {
            return 0;
        }

        // 设置默认值
        entities.forEach(entity -> {
            if (entity.getAddTime() == null) {
                entity.setAddTime(new java.util.Date());
            }
            if (entity.getUpdateTime() == null) {
                entity.setUpdateTime(new java.util.Date());
            }
            // 设置 ext 字段默认值为空 JSON 对象，避免 MySQL JSON 字段报错
            if (entity.getExt() == null || entity.getExt().trim().isEmpty()) {
                entity.setExt("{}");
            }
        });

        return nodeMapper.batchInsert(entities);
    }

    /**
     * 批量更新节点内容
     */
    public int batchUpdateByNodeId(List<DecisionNodeDO> entities) {
        if (entities == null || entities.isEmpty()) {
            return 0;
        }

        // 设置更新时间
        entities.forEach(entity -> {
            if (entity.getUpdateTime() == null) {
                entity.setUpdateTime(new java.util.Date());
            }
        });

        return nodeMapper.batchUpdateByNodeId(entities);
    }

    /**
     * 批量更新节点状态
     */
    public int batchUpdateStatusByNodeIds(List<String> nodeIds, String status) {
        if (nodeIds == null || nodeIds.isEmpty()) {
            return 0;
        }
        DecisionNodeDO entity = new DecisionNodeDO();
        entity.setStatus(status);
        DecisionNodeEntityExample example = new DecisionNodeEntityExample();
        example.createCriteria().andNodeIdIn(nodeIds);
        return nodeMapper.updateByExampleSelective(entity, example);
    }

    /**
     * 批量精准更新节点状态（限定 bizScene 与来源状态），避免跨状态覆盖
     */
    public int batchUpdateStatusByNodeIds(String bizScene, List<String> nodeIds, String fromStatus, String toStatus) {
        if (bizScene == null || bizScene.trim().isEmpty() || nodeIds == null || nodeIds.isEmpty()) {
            return 0;
        }
        DecisionNodeDO entity = new DecisionNodeDO();
        entity.setStatus(toStatus);
        DecisionNodeEntityExample example = new DecisionNodeEntityExample();
        example.createCriteria()
                .andBizSceneEqualTo(bizScene)
                .andNodeIdIn(nodeIds)
                .andStatusEqualTo(fromStatus);
        return nodeMapper.updateByExampleSelective(entity, example);
    }

    /**
     * 更新记录
     */
    public int update(DecisionNodeDO entity) {
        return nodeMapper.updateByPrimaryKeySelective(entity);
    }

    public int updateByNodeId(DecisionNodeDO entity){
        DecisionNodeEntityExample example=new DecisionNodeEntityExample();
        example.createCriteria().andNodeIdEqualTo(entity.getNodeId());
        return nodeMapper.updateByExampleSelective(entity,example);
    }

    /**
     * 根据主键删除
     */
    public int delete(Long id) {
        DecisionNodeDO entity = new DecisionNodeDO();
        entity.setId(id);
        entity.setStatus(STATUS_ONLINE_DELETE);
        return nodeMapper.updateByPrimaryKeySelective(entity);
    }

    /**
     * 软删除指定业务场景下的所有节点（设置为ONLINE_DELETE状态）
     */
    public int deleteByBizScene(String bizScene) {
        DecisionNodeDO entity = new DecisionNodeDO();
        entity.setStatus(DecisionFlowElementStatusEnum.ONLINE_DELETE.code());
        DecisionNodeEntityExample example = new DecisionNodeEntityExample();
        example.createCriteria().andBizSceneEqualTo(bizScene);
        return nodeMapper.updateByExampleSelective(entity, example);
    }

    /**
     * 灰度删除指定业务场景下的所有节点（设置为GRAY_DELETE状态）
     */
    public int grayDeleteByBizScene(String bizScene) {
        DecisionNodeDO entity = new DecisionNodeDO();
        entity.setStatus(DecisionFlowElementStatusEnum.GRAY_DELETE.code());
        DecisionNodeEntityExample example = new DecisionNodeEntityExample();
        example.createCriteria().andBizSceneEqualTo(bizScene);
        return nodeMapper.updateByExampleSelective(entity, example);
    }

    /**
     * 物理删除指定业务场景下的所有节点
     */
    public int hardDeleteByBizScene(String bizScene) {
        DecisionNodeEntityExample example = new DecisionNodeEntityExample();
        example.createCriteria().andBizSceneEqualTo(bizScene);
        return nodeMapper.deleteByExample(example);
    }

    /**
     * 物理删除：按业务场景 + nodeId 列表 + 状态 精确删除，避免唯一键冲突
     */
    public int hardDeleteByNodeIdsAndStatus(String bizScene, List<String> nodeIds, String status) {
        if (bizScene == null || bizScene.trim().isEmpty() || nodeIds == null || nodeIds.isEmpty() || status == null) {
            return 0;
        }
        DecisionNodeEntityExample example = new DecisionNodeEntityExample();
        example.createCriteria()
                .andBizSceneEqualTo(bizScene)
                .andNodeIdIn(nodeIds)
                .andStatusEqualTo(status);
        return nodeMapper.deleteByExample(example);
    }

    public int deleteByNodeId(String nodeId){
        DecisionNodeDO entity = new DecisionNodeDO();
        entity.setStatus(STATUS_ONLINE_DELETE);
        DecisionNodeEntityExample example=new DecisionNodeEntityExample();
        example.createCriteria().andNodeIdEqualTo(nodeId);
        return nodeMapper.updateByExampleSelective(entity, example);
    }

    /**
     * 获取所有不同的业务场景
     */
    public List<String> listAllBizScenes() {
        DecisionNodeEntityExample example = new DecisionNodeEntityExample();
        // 仅排除 ONLINE_DELETE，保留 GRAY_DELETE 以便正在删除中的场景仍能被召回
        example.createCriteria().andStatusNotEqualTo(STATUS_ONLINE_DELETE);
        example.setDistinct(true);

        List<DecisionNodeDO> nodes = nodeMapper.selectByExample(example);
        return nodes.stream()
                .map(DecisionNodeDO::getBizScene)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());
    }
} 