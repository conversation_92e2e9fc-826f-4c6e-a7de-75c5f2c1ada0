package com.sankuai.dzhealth.ai.service.infrastructure.repository.conversation;

import com.google.common.collect.Lists;
import com.sankuai.dzhealth.ai.service.enums.BusinessTypeEnum;
import com.sankuai.dzhealth.ai.service.infrastructure.dal.entity.ChatConversationEntity;
import com.sankuai.dzhealth.ai.service.infrastructure.dal.entity.ChatMessageEntity;
import com.sankuai.dzhealth.ai.service.infrastructure.dal.entity.ChatMessageFeedbackEntity;
import com.sankuai.dzhealth.ai.service.infrastructure.dal.example.ChatConversationEntityExample;
import com.sankuai.dzhealth.ai.service.infrastructure.dal.example.ChatMessageEntityExample;
import com.sankuai.dzhealth.ai.service.infrastructure.dal.example.ChatMessageFeedbackEntityExample;
import com.sankuai.dzhealth.ai.service.infrastructure.dal.mapper.ChatConversationEntityMapper;
import com.sankuai.dzhealth.ai.service.infrastructure.dal.mapper.ChatMessageEntityMapper;
import com.sankuai.dzhealth.ai.service.infrastructure.dal.mapper.ChatMessageFeedbackEntityMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import static com.sankuai.dzhealth.ai.service.enums.BizSceneEnum.PUBLIC_HOSPITAL;

/**
 * @author: duanxiaowen
 * @date: 2025/3/19
 */
@Repository
@Slf4j
public class ChatMessageRepository {

    @Resource
    private ChatMessageEntityMapper messageMapper;

    @Resource
    private ChatConversationEntityMapper conversationMapper;

    @Resource
    private ChatMessageFeedbackEntityMapper feedbackMapper;

    public void saveAll(List<ChatMessageEntity> entities) {
        if (CollectionUtils.isEmpty(entities)) {
            return;
        }

        // 获取会话信息
        Long sessionId = Long.valueOf(entities.get(0).getConversationId());
        ChatConversationEntityExample conversationExample = new ChatConversationEntityExample();
        conversationExample.createCriteria().andIdEqualTo(sessionId);
        List<ChatConversationEntity> conversations = conversationMapper.selectByExample(conversationExample);

        ChatConversationEntity conversation;
        if (CollectionUtils.isEmpty(conversations)) {
            // 创建新会话
            conversation = ChatConversationEntity.builder()
                    .conversationId(generateConversationId())  // 生成业务ID
                    .conversationType((byte)1)
                    .bizScene(PUBLIC_HOSPITAL.getBizScene())
                    .creatorId("test_user")
                    .status((byte)1)
                    .businessType(BusinessTypeEnum.SHOP.getType()) // 默认业务类型为SHOP
                    .createdAt(new Date())
                    .updatedAt(new Date())
                    .build();
            conversationMapper.insert(conversation);
            // 更新消息实体中的会话ID为主键ID
            for (ChatMessageEntity entity : entities) {
                entity.setConversationId(String.valueOf(conversation.getId()));
            }
        } else {
            conversation = conversations.get(0);
        }

        // 保存消息
        for (ChatMessageEntity entity : entities) {
            entity.setCreatedAt(new Date());
            entity.setSequence(System.currentTimeMillis());
            messageMapper.insert(entity);

            // 更新会话的最后消息ID
            conversation.setLastMessageId(entity.getMessageId());
            if (conversation.getFirstMessageId() == null) {
                conversation.setFirstMessageId(entity.getMessageId());
            }
        }

        // 更新会话
        conversation.setUpdatedAt(new Date());
        conversationMapper.updateByPrimaryKey(conversation);
    }


    //根据id更新保存会话
    public boolean saveMsgByPrimaryId(ChatMessageEntity entity) {

        if (StringUtils.isBlank(entity.getConversationId())) {
            return false;
        }
        // 获取会话信息
        Long conversationId = Long.valueOf(entity.getConversationId());
        ChatConversationEntityExample conversationExample = new ChatConversationEntityExample();
        conversationExample.createCriteria().andIdEqualTo(conversationId);
        List<ChatConversationEntity> conversations = conversationMapper.selectByExample(conversationExample);

        ChatConversationEntity conversation;
        if (CollectionUtils.isEmpty(conversations)) {
            return false;
        }
        conversation = conversations.get(0);

        // 保存消息
        entity.setCreatedAt(new Date());
        entity.setSequence(System.currentTimeMillis());
        if (entity.getId() != null && entity.getId() > 0) {
            messageMapper.updateByPrimaryKeySelective(entity);
        } else {
            messageMapper.insert(entity);
        }


        // 更新会话的最后消息ID
        conversation.setLastMessageId(String.valueOf(entity.getId()));
        if (conversation.getFirstMessageId() == null) {
            conversation.setFirstMessageId(String.valueOf(entity.getId()));
        }


        // 更新会话
        conversation.setUpdatedAt(new Date());
        conversationMapper.updateByPrimaryKey(conversation);
        return true;
    }


    public List<ChatMessageEntity> findLatestMessages(String conversationId, int n) {
        return messageMapper.selectLatestNByConversationId(conversationId, n);
    }


    public void deleteByConversationId(String conversationId) {
        Long sessionId = Long.valueOf(conversationId);
        // 删除消息
        ChatMessageEntityExample messageExample = new ChatMessageEntityExample();
        messageExample.createCriteria()
                .andConversationIdEqualTo(conversationId);
        messageMapper.deleteByExample(messageExample);

        // 删除会话
        conversationMapper.deleteByPrimaryKey(sessionId);

        // 删除反馈
        ChatMessageFeedbackEntityExample feedbackExample = new ChatMessageFeedbackEntityExample();
        feedbackExample.createCriteria()
                .andMessageIdIn(getMessageIdsByConversationId(conversationId));
        feedbackMapper.deleteByExample(feedbackExample);
    }

    private List<String> getMessageIdsByConversationId(String conversationId) {
        ChatMessageEntityExample example = new ChatMessageEntityExample();
        example.createCriteria()
                .andConversationIdEqualTo(conversationId);
        return messageMapper.selectByExample(example).stream()
                .map(ChatMessageEntity::getMessageId)
                .collect(Collectors.toList());
    }

    public ChatMessageEntity findByMessageId(String messageId) {
        ChatMessageEntityExample example = new ChatMessageEntityExample();
        example.createCriteria()
                .andMessageIdEqualTo(messageId);
        List<ChatMessageEntity> result = messageMapper.selectByExampleWithBLOBs(example);
        return CollectionUtils.isEmpty(result) ? null : result.get(0);
    }

    public ChatMessageEntity findByMessageId(Long messageId) {
        return messageMapper.selectByPrimaryKey(messageId);
    }

    public List<ChatMessageEntity> findByConversationId(String conversationId) {
        ChatMessageEntityExample example = new ChatMessageEntityExample();
        example.createCriteria()
                .andConversationIdEqualTo(conversationId);
        example.setOrderByClause("sequence ASC");
        return messageMapper.selectByExampleWithBLOBs(example);
    }

    public List<ChatConversationEntity> findConversationById(Long sessionId, String creatorId) {
        ChatConversationEntityExample example = new ChatConversationEntityExample();
        example.createCriteria().andIdEqualTo(sessionId).andCreatorIdEqualTo(creatorId);
        return conversationMapper.selectByExample(example);
    }


    public List<ChatConversationEntity> findConversationById(Long sessionId) {
        ChatConversationEntityExample example = new ChatConversationEntityExample();
        example.createCriteria().andIdEqualTo(sessionId);
        return conversationMapper.selectByExample(example);
    }

    // 添加反馈
    public int addFeedback(String messageId, String userId, byte feedbackType) {
        ChatMessageFeedbackEntity feedback = ChatMessageFeedbackEntity.builder()
                .messageId(messageId)
                .userId(userId)
                .feedbackType(feedbackType)
                .createdAt(new Date())
                .build();
        return feedbackMapper.insert(feedback);
    }

    /**
     * 创建新会话
     */
    public ChatConversationEntity createConversation(String userId, String bizScene, String title, String businessId) {
        return createConversation(userId, bizScene, title, BusinessTypeEnum.SHOP.getType(), businessId);
    }

    /**
     * 创建新会话（带业务关联信息）
     */
    public ChatConversationEntity createConversation(String userId, String bizScene, String title,
                                                    String businessType, String businessId) {
        ChatConversationEntity conversation = ChatConversationEntity.builder()
                .conversationId(generateConversationId())  // 生成业务ID
                .conversationType((byte)1)
                .bizScene(bizScene)
                .title(title)
                .creatorId(userId)
                .status((byte)1)
                .businessType(businessType)
                .businessId(businessId)
                .createdAt(new Date())
                .updatedAt(new Date())
                .build();
        conversationMapper.insert(conversation);
        return conversation;
    }

    /**
     * 更新会话状态
     */
    public boolean updateSession(Long sessionId, byte status, String title) {
        ChatConversationEntity conversation = new ChatConversationEntity();
        conversation.setStatus(status);
        if (StringUtils.isNotBlank(title)) {
            conversation.setTitle(title);
        }
        conversation.setUpdatedAt(new Date());
        conversation.setId(sessionId);

        return conversationMapper.updateByPrimaryKeySelective(conversation) > 0;

    }

    public boolean updateSessionByUserId(String userId, byte status) {
        ChatConversationEntity conversation = new ChatConversationEntity();
        conversation.setStatus(status);
        conversation.setUpdatedAt(new Date());

        ChatConversationEntityExample example = new ChatConversationEntityExample();
        example.createCriteria().andCreatorIdEqualTo(userId);

        return conversationMapper.updateByExampleSelective(conversation, example) > 0;

    }


    public void updateConversationStatus(String conversationId, byte status, String title) {
        ChatConversationEntity conversation = new ChatConversationEntity();
        conversation.setStatus(status);
        conversation.setUpdatedAt(new Date());
        conversation.setTitle(title);

        ChatConversationEntityExample example = new ChatConversationEntityExample();
        example.createCriteria().andConversationIdEqualTo(conversationId);
        conversationMapper.updateByExampleSelective(conversation, example);
    }

    /**
     * 分页查询会话列表
     */
    public List<ChatConversationEntity> findConversations(String userId, int offset, int limit, String businessId, String businessType) {
        ChatConversationEntityExample example = new ChatConversationEntityExample();
        ChatConversationEntityExample.Criteria criteria = example.createCriteria();
        criteria.andBusinessIdEqualTo(businessId)
                .andCreatorIdEqualTo(userId)
                .andTitleNotEqualTo(StringUtils.EMPTY)
                .andStatusIn(Lists.newArrayList((byte) 1, (byte) 2, (byte)3));
        if (StringUtils.isNotBlank(businessType)) {
            criteria.andBusinessTypeEqualTo(businessType);
        }
        example.setOrderByClause("updated_at DESC");

        // 使用新的分页查询方法
        return conversationMapper.selectByExampleWithPage(example, offset, limit);
    }

    public List<ChatConversationEntity> findConversationsWithEmptyTitle(String userId, int offset, int limit, String businessId, String businessType) {
        ChatConversationEntityExample example = new ChatConversationEntityExample();
        ChatConversationEntityExample.Criteria criteria = example.createCriteria();
        criteria.andBusinessIdEqualTo(businessId)
                .andCreatorIdEqualTo(userId)
                .andStatusIn(Lists.newArrayList((byte) 1, (byte) 2, (byte)3));
        if (StringUtils.isNotBlank(businessType)) {
            criteria.andBusinessTypeEqualTo(businessType);
        }
        example.setOrderByClause("updated_at DESC");

        // 使用新的分页查询方法
        return conversationMapper.selectByExampleWithPage(example, offset, limit);
    }

    public List<ChatMessageEntity> findMessagesByConversationId(String conversationId, String userId, int offset, int limit) {
        ChatMessageEntityExample example = new ChatMessageEntityExample();
        example.createCriteria()
                .andConversationIdEqualTo(conversationId)
                .andRoleIn(Lists.newArrayList("client", "robot"))
                .andSenderIdEqualTo(userId);
        example.setOrderByClause("created_at DESC");

        // 使用新的分页查询方法
        return messageMapper.selectByExampleWithPage(example, offset, limit);
    }

    /**
     * 查询超时会话
     */
    public List<ChatConversationEntity> findTimeoutConversations(int timeoutHours) {
        Date timeoutTime = new Date(System.currentTimeMillis() - timeoutHours * 3600 * 1000L);
        ChatConversationEntityExample example = new ChatConversationEntityExample();
        example.createCriteria()
                .andStatusEqualTo((byte)1)
                .andUpdatedAtLessThan(timeoutTime);
        return conversationMapper.selectByExample(example);
    }

    /**
     * 查询会话的第一条用户消息
     */
    public ChatMessageEntity findFirstUserMessage(String conversationId) {
        ChatMessageEntity message = messageMapper.selectFirstUserMessageByConversationId(conversationId);
        return message;
    }

    public ChatMessageFeedbackEntity saveFeedback(Long msgId, String userId) {
        ChatMessageFeedbackEntity entity = ChatMessageFeedbackEntity.builder()
                .messageId(String.valueOf(msgId))
                .userId(String.valueOf(userId))
                .feedbackType((byte)0)
                .createdAt(new Date())
                .build();
        feedbackMapper.insert(entity);
        return entity;
    }



    public boolean updateFeedback(String messageId, String userId, byte feedbackType) {
        ChatMessageFeedbackEntity entity = ChatMessageFeedbackEntity.builder()
                .messageId(messageId)
                .userId(userId)
                .feedbackType(feedbackType)
                .createdAt(new Date())
                .build();

        ChatMessageFeedbackEntityExample example = new ChatMessageFeedbackEntityExample();
        example.createCriteria()
                .andMessageIdEqualTo(messageId)
                .andUserIdEqualTo(userId);

        return feedbackMapper.updateByExampleSelective(entity, example) > 0;


    }

    /**
     * 检查用户是否已对消息进行过反馈
     */
    public List<ChatMessageFeedbackEntity> queryFeedback(List<String> messageIds, String userId) {
        ChatMessageFeedbackEntityExample example = new ChatMessageFeedbackEntityExample();
        example.createCriteria()
                .andMessageIdIn(messageIds)
                .andUserIdEqualTo(userId);
        return feedbackMapper.selectByExample(example);
    }

    /**
     * 通过userId、conversationId和messageId查找并增量更新消息内容
     *
     * @param userId 用户ID
     * @param conversationId 会话ID
     * @param messageId 消息ID
     * @param content 新的消息内容
     * @return 是否更新成功
     */
    public boolean updateMessageContentByUserAndConversation(String conversationId,Long messageId, String content) {
        if (StringUtils.isBlank(conversationId) ||
            messageId==null|| StringUtils.isBlank(content)) {
            log.warn("更新消息内容参数不完整:  conversationId={}, messageId={}",conversationId, messageId);
            return false;
        }
        // 先查询消息是否存在
        ChatMessageEntityExample queryExample = new ChatMessageEntityExample();
        queryExample.createCriteria()
                .andConversationIdEqualTo(conversationId)
                .andIdEqualTo(messageId);

        List<ChatMessageEntity> messages = messageMapper.selectByExampleWithBLOBs(queryExample);
        if (CollectionUtils.isEmpty(messages)) {
            log.warn("未找到符合条件的消息:  conversationId={}, messageId={}", conversationId, messageId);
            return false;
        }

        // 创建更新实体
        ChatMessageEntity updateEntity = new ChatMessageEntity();
        updateEntity.setContent(content);

        // 执行更新操作
        int result = messageMapper.updateByExampleSelective(updateEntity, queryExample);
        return result > 0;
    }

    /**
     * 更新会话业务关联信息
     */
    public boolean updateSessionBusinessInfo(Long sessionId, String businessType, String businessId) {
        ChatConversationEntity conversation = new ChatConversationEntity();
        conversation.setBusinessType(businessType);
        conversation.setBusinessId(businessId);
        conversation.setUpdatedAt(new Date());
        conversation.setId(sessionId);

        return conversationMapper.updateByPrimaryKeySelective(conversation) > 0;
    }

    /**
     * 根据业务类型和业务ID查询会话
     */
    public List<ChatConversationEntity> findConversationsByBusiness(String businessType, String businessId) {
        ChatConversationEntityExample example = new ChatConversationEntityExample();
        example.createCriteria()
                .andBusinessTypeEqualTo(businessType)
                .andBusinessIdEqualTo(businessId)
                .andStatusIn(Lists.newArrayList((byte)1, (byte)2));
        example.setOrderByClause("updated_at DESC");
        return conversationMapper.selectByExample(example);
    }

    private String generateConversationId() {
        return "conv_" + java.util.UUID.randomUUID().toString().replace("-", "");
    }

    /**
     * 直接根据消息ID查询消息
     * @param msgId 消息ID
     * @param userId 用户ID
     * @return 消息列表
     */
    public List<ChatMessageEntity> findMessageByMsgId(Long msgId, String userId) {
        if (msgId == null || msgId <= 0) {
            return Lists.newArrayList();
        }
        
        ChatMessageEntityExample example = new ChatMessageEntityExample();
        example.createCriteria()
                .andIdEqualTo(msgId)
                .andSenderIdEqualTo(userId);
        
        return messageMapper.selectByExampleWithBLOBs(example);
    }
}

