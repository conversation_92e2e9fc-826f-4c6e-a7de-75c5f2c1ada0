package com.sankuai.dzhealth.ai.service.agent.infrastructure.model;

import com.sankuai.dzhealth.ai.service.agent.infrastructure.dal.entity.MessageEvaluationResultEntity;
import com.sankuai.dzhealth.ai.service.agent.infrastructure.dal.entity.SessionEvaluationResultEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class EvaluationResultsResponse {
    private List<MessageEvaluationResultEntity> messageResults;
    private List<SessionEvaluationResultEntity> sessionResults;
}

