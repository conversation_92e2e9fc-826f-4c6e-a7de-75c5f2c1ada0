package com.sankuai.dzhealth.ai.service.agent.infrastructure.dal.entity;

import java.util.Date;

import lombok.*;

/**
 * 表名: evaluation_records
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class EvaluationRecordsDO {
    /**
     * 字段: id
     * 说明: 主键
     */
    private Long id;

    /**
     * 字段: evaluation_id
     * 说明: 测评id
     */
    private String evaluationId;

    /**
     * 字段: evaluation_name
     * 说明: 测评名称
     */
    private String evaluationName;

    /**
     * 字段: status
     * 说明: 测评状态,0-开始,1-失败,2-成功,3-人工评测完成
     */
    private Integer status;

    /**
     * 字段: create_time
     * 说明: 创建时间
     */
    private Date createTime;

    /**
     * 字段: end_time
     * 说明: 结束时间
     */
    private Date endTime;

    /**
     * 字段: update_time
     * 说明: 更新时间
     */
    private Date updateTime;

    /**
     * 字段: evaluation_result
     * 说明: 评测结果
     */
    private String evaluationResult;


    public enum StatusEnum {
        STATUS_UNKNOWN(-1, "UNKNOWN"),
        STATUS_START(0, "开始"),
        STATUS_FAIL(1, "失败"),
        STATUS_SUCCESS(2, "成功"),
        STATUS_MARKED(3, "人工评测完成");

        public final int code;
        public final String msg;

        StatusEnum(int code, String msg) {
            this.code = code;
            this.msg = msg;
        }


        // 根据数字值获取枚举实例
        public static StatusEnum getByCode(int code) {
            for (StatusEnum status : values()) {
                if (status.code == code) {
                    return status;
                }
            }
            return STATUS_UNKNOWN;
        }
    }
}