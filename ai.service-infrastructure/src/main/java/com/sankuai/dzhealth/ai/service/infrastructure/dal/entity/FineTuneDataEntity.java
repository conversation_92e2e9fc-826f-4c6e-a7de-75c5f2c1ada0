package com.sankuai.dzhealth.ai.service.infrastructure.dal.entity;

import java.util.Date;
import lombok.*;
import lombok.experimental.SuperBuilder;

/**
 *
 *   表名: fine_tune_data
 */
@SuperBuilder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class FineTuneDataEntity {
    /**
     *   字段: id
     *   说明: 主键
     */
    private Long id;

    /**
     *   字段: session_id
     *   说明: 会话ID
     */
    private Long sessionId;

    /**
     *   字段: msg_id
     *   说明: 消息ID
     */
    private Long msgId;

    /**
     *   字段: data_key
     *   说明: 评估项标识
     */
    private String dataKey;

    /**
     *   字段: description
     *   说明: 评估项描述
     */
    private String description;

    /**
     *   字段: success
     *   说明: 是否成功(1:成功,0:失败)
     */
    private Boolean success;

    /**
     *   字段: created_at
     *   说明: 创建时间
     */
    private Date createdAt;

    /**
     *   字段: updated_at
     *   说明: 更新时间
     */
    private Date updatedAt;
}