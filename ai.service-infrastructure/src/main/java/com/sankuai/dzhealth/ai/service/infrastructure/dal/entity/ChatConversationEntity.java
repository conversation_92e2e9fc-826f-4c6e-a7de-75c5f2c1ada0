package com.sankuai.dzhealth.ai.service.infrastructure.dal.entity;

import java.util.Date;
import lombok.*;

/**
 *
 *   表名: chat_conversation
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ChatConversationEntity {
    /**
     *   字段: id
     *   说明: 主键
     */
    private Long id;

    /**
     *   字段: conversation_id
     *   说明: 会话唯一标识
     */
    private String conversationId;

    /**
     *   字段: conversation_type
     *   说明: 会话类型(1:私聊 2:群聊)
     */
    private Byte conversationType;

    /**
     *   字段: biz_scene
     *   说明: 业务场景
     */
    private String bizScene;

    /**
     *   字段: title
     *   说明: 会话标题
     */
    private String title;

    /**
     *   字段: group_id
     *   说明: 群组ID
     */
    private String groupId;

    /**
     *   字段: creator_id
     *   说明: 创建者ID
     */
    private String creatorId;

    /**
     *   字段: first_message_id
     *   说明: 首条消息ID
     */
    private String firstMessageId;

    /**
     *   字段: last_message_id
     *   说明: 最后消息ID
     */
    private String lastMessageId;

    /**
     *   字段: status
     *   说明: 状态(2:结束 1:正常 0:删除)
     */
    private Byte status;

    /**
     *   字段: created_at
     */
    private Date createdAt;

    /**
     *   字段: updated_at
     */
    private Date updatedAt;

    /**
     *   字段: business_type
     *   说明: 业务关联类型(SHOP/PRODUCT/ORDER/ACTIVITY)
     */
    private String businessType;

    /**
     *   字段: business_id
     *   说明: 关联业务ID
     */
    private String businessId;
}