package com.sankuai.dzhealth.ai.service.agent.infrastructure.dal.entity;

import java.util.Date;
import lombok.*;

/**
 *
 *   表名: appointment_info
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AppointmentInfoDO {
    /**
     *   字段: id
     *   说明: 自增主键
     */
    private Long id;

    /**
     *   字段: user_id
     *   说明: 用户编号
     */
    private Long userId;

    /**
     *   字段: session_id
     *   说明: 会话编号
     */
    private String sessionId;

    /**
     *   字段: msg_id
     *   说明: 预约消息编号
     */
    private String msgId;

    /**
     *   字段: reserved_msg_id
     *   说明: 帮约消息编号
     */
    private String reservedMsgId;

    /**
     *   字段: product_name
     *   说明: 预约项目(枚举)
     */
    private Integer productName;

    /**
     *   字段: appointment_start_time
     *   说明: 预约开始时间
     */
    private Date appointmentStartTime;

    /**
     *   字段: appointment_end_time
     *   说明: 预约结束时间
     */
    private Date appointmentEndTime;

    /**
     *   字段: position_txt
     *   说明: 地址文字
     */
    private String positionTxt;

    /**
     *   字段: lng
     *   说明: 地址经度
     */
    private Double lng;

    /**
     *   字段: lat
     *   说明: 地址纬度
     */
    private Double lat;

    /**
     *   字段: person_desc
     *   说明: 人数
     */
    private String personDesc;

    /**
     *   字段: filter_items
     *   说明: 筛选项
     */
    private String filterItems;

    /**
     *   字段: phone
     *   说明: 联系方式
     */
    private String phone;

    /**
     *   字段: success_shop_id
     *   说明: 预约成功商户
     */
    private Long successShopId;

    /**
     *   字段: task_id
     *   说明: 外呼任务编号
     */
    private Long taskId;

    /**
     *   字段: card_info
     *   说明: 卡片信息
     */
    private String cardInfo;

    /**
     *   字段: status
     *   说明: 状态(枚举)
     */
    private Byte status;

    /**
     *   字段: create_time
     *   说明: 创建时间
     */
    private Date createTime;

    /**
     *   字段: update_time
     *   说明: 更新时间
     */
    private Date updateTime;

    /**
     *   字段: lead_id
     *   说明: 预约单id
     */
    private Long leadId;

    /**
     *   字段: remark
     *   说明: 备注信息
     */
    private String remark;

    /**
     *   字段: cityId
     *   说明: 城市编号
     */
    private Integer cityid;

    /**
     *   字段: distance
     *   说明: 距离(默认5000)
     */
    private Double distance;

    /**
     *   字段: price
     *   说明: 价格(示例：R100.0:200.0)
     */
    private String price;

    /**
     *   字段: searchword
     *   说明: 搜索关键字
     */
    private String searchword;

    /**
     *   字段: shop_id_list
     *   说明: 商户列表(以、分隔)
     */
    private String shopIdList;
}