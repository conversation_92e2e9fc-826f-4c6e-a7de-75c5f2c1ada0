package com.sankuai.dzhealth.ai.service.agent.infrastructure.dal.repository.evaluation;

import com.sankuai.dzhealth.ai.service.agent.infrastructure.dal.entity.EvaluationSessionRecordsDO;
import com.sankuai.dzhealth.ai.service.agent.infrastructure.dal.example.EvaluationSessionRecordsDOExample;
import com.sankuai.dzhealth.ai.service.agent.infrastructure.dal.mapper.EvaluationSessionRecordsDOMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

@Repository
@RequiredArgsConstructor
public class EvaluationSessionRecordsRepository {
    @Resource
    private EvaluationSessionRecordsDOMapper evaluationSessionRecordsDOMapper;

    public void insert(EvaluationSessionRecordsDO evaluationSessionRecordsDO) {
        evaluationSessionRecordsDOMapper.insert(evaluationSessionRecordsDO);
    }


    public int delete(List<String> sessionIDs) {
        return evaluationSessionRecordsDOMapper.deleteBySessionIDs(sessionIDs);
    }


    public List<EvaluationSessionRecordsDO> findByEvaluationIdOrderByCreateTimeDesc(String evaluationID, int limit, int offset) {
        EvaluationSessionRecordsDOExample example = new EvaluationSessionRecordsDOExample();
        example.createCriteria()
                .andEvaluationIdEqualTo(evaluationID);
        // 设置排序规则
        example.setOrderByClause("create_time desc");
        return evaluationSessionRecordsDOMapper.selectByExampleWithBLOBsWithPage(example, offset, limit);
    }


    public EvaluationSessionRecordsDO findBySessionId(String sessionID) {
        EvaluationSessionRecordsDOExample example = new EvaluationSessionRecordsDOExample();
        example.createCriteria()
                .andSessionIdEqualTo(sessionID);
        // 使用RowBounds实现分页限制
        return evaluationSessionRecordsDOMapper.selectByExampleWithBLOBsWithPage(example, 0, 1).stream().findAny().orElse(null);
    }


    public List<EvaluationSessionRecordsDO> findByEvaluationId(String evaluationId) {
        EvaluationSessionRecordsDOExample example = new EvaluationSessionRecordsDOExample();
        example.createCriteria()
                .andEvaluationIdEqualTo(evaluationId);
        // 使用RowBounds实现分页限制
        return evaluationSessionRecordsDOMapper.selectByExampleWithBLOBsWithPage(example, 0, 1000);
    }

    /**
     * 根据evaluationId和sessionIds列表查询EvaluationSessionRecordsDO
     */
    public List<EvaluationSessionRecordsDO> findByEvaluationIdAndSessionIds(String evaluationId, List<String> sessionIds) {
        EvaluationSessionRecordsDOExample example = new EvaluationSessionRecordsDOExample();
        example.createCriteria()
                .andEvaluationIdEqualTo(evaluationId)
                .andSessionIdIn(sessionIds);
        // 使用RowBounds实现分页限制
        return evaluationSessionRecordsDOMapper.selectByExampleWithBLOBsWithPage(example, 0, 1000);
    }
}