package com.sankuai.dzhealth.ai.service.agent.infrastructure.proxy.skinreport;

import com.meituan.beauty.narcissus.constant.skin.ChannelEnum;
import com.meituan.beauty.narcissus.dto.report.acl.SkinReportQueryRequest;
import com.meituan.beauty.narcissus.dto.report.acl.SkinReportQueryResponse;
import com.meituan.beauty.narcissus.dto.report.acl.info.SkinMetricDetail;
import com.meituan.beauty.narcissus.service.report.acl.ReportAclService;
import com.meituan.beauty.narcissus.util.Response;
import com.meituan.mdp.boot.starter.pigeon.annotation.MdpPigeonClient;
import com.sankuai.dzhealth.ai.service.agent.infrastructure.model.SkinReportResponse;
import com.sankuai.dzhealth.ai.service.agent.infrastructure.model.SkinReportTotalDegreeNameQuery;
import com.sankuai.dzhealth.ai.service.agent.infrastructure.model.SkinReportWithPart;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 获取测肤结果
 *
 * @user: xiafangyuan
 * @since: 2025/7/7 19:52
 * @version: 1.0
 */
@Component
@Slf4j
public class BeautyNarcissusSkinReportAcl {

    @MdpPigeonClient(timeout = 5000)
    private ReportAclService reportAclService;

    /**
     * 查询皮肤报告中的指标列表
     *
     * @param skinReportTotalDegreeNameQuery 报告查询请求参数
     * @return 指标名称列表 <code>["无黑眼圈","重度痘痘"]</code>
     */
    public List<String> queryTotalDegreeName(SkinReportTotalDegreeNameQuery skinReportTotalDegreeNameQuery) {
        log.info("queryTotalDegreeName, skinReportTotalDegreeNameQuery:{}", skinReportTotalDegreeNameQuery);
        // 校验参数
        if (skinReportTotalDegreeNameQuery == null || skinReportTotalDegreeNameQuery.getUserId() == null || skinReportTotalDegreeNameQuery.getPlatform() == null
                || skinReportTotalDegreeNameQuery.getBusinessType() == null || StringUtils.isBlank(skinReportTotalDegreeNameQuery.getReportUuid())) {
            return Collections.emptyList();
        }

        SkinReportQueryRequest skinReportQueryRequest = new SkinReportQueryRequest();
        skinReportQueryRequest.setUserId(skinReportTotalDegreeNameQuery.getUserId());
        skinReportQueryRequest.setPlatform(skinReportTotalDegreeNameQuery.getPlatform());
        skinReportQueryRequest.setBusinessType(skinReportTotalDegreeNameQuery.getBusinessType());
        skinReportQueryRequest.setReportUuid(skinReportTotalDegreeNameQuery.getReportUuid());
        skinReportQueryRequest.setSource(ChannelEnum.AI_BACKEND.code);
        Response<SkinReportQueryResponse> skinReportQueryResponseResponse = reportAclService
                .queryReport(skinReportQueryRequest);
        if (skinReportQueryResponseResponse.isSuccess()) {
            SkinReportQueryResponse data = skinReportQueryResponseResponse.getData();
            return data.getSkinMetricList().stream().map(SkinMetricDetail::getTotalDegreeName).collect(Collectors.toList());
        } else {
            return Collections.emptyList();
        }
    }


    /**
     * 查询皮肤报告中的指标列表（包含部位）
     *
     * <p>方法返回json数据结构，包含更细的信息，更加可读
     *
     * @param skinReportTotalDegreeNameQuery 报告查询请求参数
     * @return 指标名称列表
     * <pre>
     * <code>
     * [
     *      {
     *           "name": "黑眼圈",
     *           "totalDegreeName": "中度黑眼圈",
     *           "subDegreeList": [
     *                {
     *                     "name": "色素型",
     *                     "partDegreeName": "中度色素型"
     *                },
     *                {
     *                     "name": "血管型",
     *                     "partDegreeName": "中度血管型"
     *                },
     *                {
     *                     "name": "结构型",
     *                     "partDegreeName": "轻度结构型"
     *                }
     *           ]
     *      }
     * ]
     * </code>
     * </pre>
     */
    public SkinReportResponse queryTotalDegreeNameWithJson(SkinReportTotalDegreeNameQuery skinReportTotalDegreeNameQuery) {
        log.info("queryTotalDegreeNameWithJson, skinReportTotalDegreeNameQuery:{}", skinReportTotalDegreeNameQuery);
        // 校验参数
        if (skinReportTotalDegreeNameQuery == null || skinReportTotalDegreeNameQuery.getUserId() == null || skinReportTotalDegreeNameQuery.getPlatform() == null
                || skinReportTotalDegreeNameQuery.getBusinessType() == null || StringUtils.isBlank(skinReportTotalDegreeNameQuery.getReportUuid())) {
            return SkinReportResponse.builder().reportList(Collections.emptyList()).build();
        }

        SkinReportQueryRequest skinReportQueryRequest = new SkinReportQueryRequest();
        skinReportQueryRequest.setUserId(skinReportTotalDegreeNameQuery.getUserId());
        skinReportQueryRequest.setPlatform(skinReportTotalDegreeNameQuery.getPlatform());
        skinReportQueryRequest.setBusinessType(skinReportTotalDegreeNameQuery.getBusinessType());
        skinReportQueryRequest.setReportUuid(skinReportTotalDegreeNameQuery.getReportUuid());
        skinReportQueryRequest.setSource(ChannelEnum.AI_BACKEND.code);
        Response<SkinReportQueryResponse> skinReportQueryResponseResponse = reportAclService
                .queryReport(skinReportQueryRequest);
        if (skinReportQueryResponseResponse.isSuccess()) {
            SkinReportQueryResponse data = skinReportQueryResponseResponse.getData();
            return buildSkinReportResponse(data);
        } else {
            return SkinReportResponse.builder().reportList(Collections.emptyList()).build();
        }
    }

    /**
     * 构建皮肤报告响应数据
     * 将SkinReportQueryResponse转换为SkinReportResponse
     *
     * @param data 原始皮肤报告查询响应
     * @return 格式化后的皮肤报告响应
     */
    private SkinReportResponse buildSkinReportResponse(SkinReportQueryResponse data) {
        if (data == null || data.getSkinMetricList() == null || data.getSkinMetricList().isEmpty()) {
            return SkinReportResponse.builder().reportList(Collections.emptyList()).build();
        }

        List<SkinReportWithPart> reportList = data.getSkinMetricList().stream()
                .map(this::convertToSkinReportWithPart)
                .collect(Collectors.toList());

        return SkinReportResponse.builder()
                .reportList(reportList)
                .build();
    }

    /**
     * 将SkinMetricDetail转换为SkinReportWithPart
     *
     * @param skinMetricDetail 原始皮肤指标详情
     * @return 转换后的皮肤报告详细信息
     */
    private SkinReportWithPart convertToSkinReportWithPart(SkinMetricDetail skinMetricDetail) {
        if (skinMetricDetail == null) {
            return null;
        }

        List<SkinReportWithPart.SubDegreeInfo> subDegreeList = skinMetricDetail.getSubDegreeList().stream()
                .map(subDegree -> SkinReportWithPart.SubDegreeInfo.builder()
                        .name(subDegree.getName())
                        .partDegreeName(subDegree.getPartDegreeName())
                        .build())
                .collect(Collectors.toList());

        return SkinReportWithPart.builder()
                .name(skinMetricDetail.getName())
                .totalDegreeName(skinMetricDetail.getTotalDegreeName())
                .subDegreeList(subDegreeList)
                .build();
    }


}
