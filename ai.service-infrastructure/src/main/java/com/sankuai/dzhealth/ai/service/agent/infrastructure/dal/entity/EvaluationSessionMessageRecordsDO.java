package com.sankuai.dzhealth.ai.service.agent.infrastructure.dal.entity;

import java.util.Date;

import lombok.*;

/**
 * 表名: evaluation_session_message_records
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class EvaluationSessionMessageRecordsDO {
    /**
     * 字段: id
     * 说明: 主键
     */
    private Long id;

    /**
     * 字段: session_id
     * 说明: 测评id
     */
    private String sessionId;

    /**
     * 字段: message_id
     * 说明: 问答对id
     */
    private String messageId;

    /**
     * 字段: status
     * 说明: 测评状态,0-开始,1-失败,2-成功,3-人工评测完成
     */
    private Integer status;

    /**
     * 字段: create_time
     * 说明: 创建时间
     */
    private Date createTime;

    /**
     * 字段: update_time
     * 说明: 更新时间
     */
    private Date updateTime;

    /**
     * 字段: evaluation_result
     * 说明: 测评结果
     */
    private String evaluationResult;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class EvaluationResult {
        private String score;
        private String reason;
    }
}