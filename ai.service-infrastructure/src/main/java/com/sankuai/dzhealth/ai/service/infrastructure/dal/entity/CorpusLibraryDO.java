package com.sankuai.dzhealth.ai.service.infrastructure.dal.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 *
 *   表名: corpus_library
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CorpusLibraryDO {
    /**
     *   字段: id
     */
    private Long id;

    /**
     *   字段: sort_order
     *   说明: 排序
     */
    private Integer sortOrder;

    /**
     *   字段: prev_corpus_id
     *   说明: 上一段语料ID
     */
    private Long prevCorpusId;

    /**
     *   字段: resource_id
     *   说明: 资源库ID
     */
    private Long resourceId;

    /**
     *   字段: resource_channel
     *   说明: 资源库渠道
     */
    private String resourceChannel;

    /**
     *   字段: resource_uri
     *   说明: 资源库uri
     */
    private String resourceUri;

    /**
     *   字段: corpus_type
     *   说明: 语料类型,1-文本,2-图片
     */
    private Integer corpusType;

    /**
     *   字段: is_content_quantified
     *   说明: 内容是否量化
     */
    private Boolean isContentQuantified;

    /**
     *   字段: priority
     *   说明: 优先级
     */
    private Integer priority;

    /**
     *   字段: confidence
     *   说明: 置信度
     */
    private Float confidence;

    /**
     *   字段: publish_time
     *   说明: 信息发布时间
     */
    private Date publishTime;

    /**
     *   字段: created_time
     *   说明: 创建时间
     */
    private Date createdTime;

    /**
     *   字段: updated_time
     *   说明: 更新时间
     */
    private Date updatedTime;

    /**
     *   字段: mt_shop_id
     *   说明: 美团门店ID
     */
    private Long mtShopId;
}