package com.sankuai.dzhealth.ai.service.agent.infrastructure.dal.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.Date;

/**
 *
 *   表名: chat_session_message
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
public class ChatSessionMessageDO {
    /**
     *   字段: id
     *   说明: 主键
     */
    private Long id;

    /**
     *   字段: message_id
     *   说明: 消息ID
     */
    private String messageId;

    /**
     *   字段: session_id
     *   说明: 会话ID
     */
    private String sessionId;

    /**
     *   字段: role
     *   说明: 角色
     */
    private String role;

    /**
     *   字段: user_id
     *   说明: 用户ID
     */
    private Long userId;

    /**
     *   字段: platform
     *   说明: 平台
     */
    private Integer platform;

    /**
     *   字段: type
     *   说明: 内容类型,1-文本
     */
    private Integer type;

    /**
     *   字段: status
     *   说明: 审核状态,0-通过,1-审核不通过,2-删除,3-审核中
     */
    private Integer status;

    /**
     *   字段: feedback
     *   说明: 消息点赞取消状态, 0-无操作,1-点赞,2-点踩
     */
    private Integer feedback;

    /**
     *   字段: create_time
     *   说明: 创建时间
     */
    private Date createTime;

    /**
     *   字段: update_time
     *   说明: 更新时间
     */
    private Date updateTime;
}