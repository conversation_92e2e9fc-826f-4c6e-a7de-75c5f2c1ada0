package com.sankuai.dzhealth.ai.service.infrastructure.repository;

import com.sankuai.dzhealth.ai.service.infrastructure.converter.CorpusLibraryConverter;
import com.sankuai.dzhealth.ai.service.infrastructure.dal.entity.CorpusLibraryDOWithBLOBs;
import com.sankuai.dzhealth.ai.service.infrastructure.dal.mapper.CorpusLibraryDOMapper;
import com.sankuai.dzhealth.ai.service.infrastructure.model.CorpusLibrary;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Optional;

@Slf4j
@Service
public class CorpusLibraryRepository {

    @Autowired
    private CorpusLibraryDOMapper corpusLibraryDOMapper;

    public Long save(CorpusLibraryDOWithBLOBs corpusLibrary) {
        try {
            if (corpusLibrary.getId() != null) {
                corpusLibraryDOMapper.updateByPrimaryKeySelective(corpusLibrary);
            } else {
                corpusLibraryDOMapper.insertSelective(corpusLibrary);
            }
            return corpusLibrary.getId();
        } catch (Exception e) {
            log.error("Save corpus library failed, corpus: {}", corpusLibrary, e);
            return null;
        }
    }

    /**
     * 根据ID查询语料
     *
     * @param id 语料ID
     * @return 语料信息
     */
    public Optional<CorpusLibrary> findById(Long id) {
        if (id == null) {
            return Optional.empty();
        }

        try {
            CorpusLibraryDOWithBLOBs result = (CorpusLibraryDOWithBLOBs) corpusLibraryDOMapper.selectByPrimaryKey(id);
            return Optional.ofNullable(result).map(CorpusLibraryConverter::toDomain);
        } catch (Exception e) {
            log.error("Find corpus library by id failed, id: {}", id, e);
            return Optional.empty();
        }
    }
}
