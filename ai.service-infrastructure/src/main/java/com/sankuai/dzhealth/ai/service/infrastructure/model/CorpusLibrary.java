package com.sankuai.dzhealth.ai.service.infrastructure.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CorpusLibrary {
    /**
     * 语料ID
     */
    private Long id;

    /**
     * 排序
     */
    private Integer sortOrder;

    /**
     * 上一段语料ID
     */
    private Long prevCorpusId;

    /**
     * 资源库ID
     */
    private Long resourceId;

    /**
     * 资源库渠道
     */
    private String resourceChannel;

    /**
     * 资源库uri
     */
    private String resourceUri;

    /**
     * 语料类型,1-文本,2-图片
     */
    private Integer corpusType;

    /**
     * 内容是否量化
     */
    private Boolean isContentQuantified;

    /**
     * 优先级
     */
    private Integer priority;

    /**
     * 置信度
     */
    private Float confidence;

    /**
     * 信息发布时间
     */
    private Date publishTime;

    /**
     * 创建时间
     */
    private Date createdTime;

    /**
     * 更新时间
     */
    private Date updatedTime;

    /**
     * 语料摘要
     */
    private String corpusSummary;

    /**
     * 语料内容
     */
    private String corpusContent;

    /**
     * 美团门店ID
     */
    private Long mtShopId;
}
