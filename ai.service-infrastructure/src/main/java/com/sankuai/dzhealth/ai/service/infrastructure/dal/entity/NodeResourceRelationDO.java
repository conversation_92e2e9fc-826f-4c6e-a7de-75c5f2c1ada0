package com.sankuai.dzhealth.ai.service.infrastructure.dal.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 *
 *   表名: node_resource_relation
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class NodeResourceRelationDO {
    /**
     *   字段: id
     *   说明: PK
     */
    private Long id;

    /**
     *   字段: biz_scene
     *   说明: 业务场景
     */
    private String bizScene;

    /**
     *   字段: node_id
     *   说明: 节点 ID
     */
    private String nodeId;

    /**
     *   字段: resource_id
     *   说明: 资源 ID
     */
    private String resourceId;

    /**
     *   字段: resource_type
     *   说明: 冗余资源类型
     */
    private String resourceType;

    /**
     *   字段: rationale
     *   说明: 推荐理由
     */
    private String rationale;

    /**
     *   字段: sort_order
     *   说明: 展示顺序，越小越前
     */
    private Long sortOrder;

    /**
     *   字段: status
     *   说明: 关联状态：ONLINE / OFFLINE / DRAFT / …
     */
    private String status;

    /**
     *   字段: ext
     *   说明: 扩展字段
     */
    private String ext;

    /**
     *   字段: add_time
     */
    private Date addTime;

    /**
     *   字段: update_time
     */
    private Date updateTime;
}