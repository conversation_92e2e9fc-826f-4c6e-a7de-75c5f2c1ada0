package com.sankuai.dzhealth.ai.service.agent.infrastructure.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 预约状态枚举
 *
 * @author: jiyizhou
 * @time: 2025/7/10 21:00
 * @version: 0.0.1
 */
@Getter
@AllArgsConstructor
public enum AppointmentStatusEnum {

    PENDING( (byte) 1, "预约信息未确认"),
    CONFIRMED( (byte)2, "预约发起"),
    IN_PROGRESS( (byte)3, "帮约任务进行中"),
    TASK_CANCELLED( (byte)4, "帮约任务取消"),
    TASK_SUCCESS( (byte)5, "帮约任务成功"),
    // 预约单取消
    RESERVATION_CANCELLED( (byte)6, "预约单取消"),
    TASK_FAILED((byte)7,"任务失败"),
    NO_SHOP((byte)8,"无接受商户");

    private final Byte code;
    private final String desc;

    /**
     * 根据code获取枚举
     */
    public static AppointmentStatusEnum fromCode(Byte code) {
        if (code == null) {
            return null;
        }
        for (AppointmentStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }
}

