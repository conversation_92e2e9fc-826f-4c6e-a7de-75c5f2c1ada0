package com.sankuai.dzhealth.ai.service.agent.infrastructure.dal.mapper;

import com.meituan.mdp.mybatis.mapper.MybatisBLOBsMapper;
import com.sankuai.dzhealth.ai.service.agent.infrastructure.dal.entity.EvaluationSessionMessageRecordsDO;
import com.sankuai.dzhealth.ai.service.agent.infrastructure.dal.example.EvaluationSessionMessageRecordsDOExample;
import com.sankuai.dzhealth.ai.service.infrastructure.dal.entity.ChatMessageEntity;
import com.sankuai.dzhealth.ai.service.infrastructure.dal.example.ChatMessageEntityExample;
import org.apache.ibatis.annotations.Param;

public interface EvaluationSessionMessageRecordsDOMapper extends MybatisBLOBsMapper<EvaluationSessionMessageRecordsDO, EvaluationSessionMessageRecordsDOExample, Long> {
    int updateByExampleSelective(@Param("row") EvaluationSessionMessageRecordsDO record, @Param("example") EvaluationSessionMessageRecordsDOExample example);

    int insertOrUpdate(@Param("row") EvaluationSessionMessageRecordsDO record);
}