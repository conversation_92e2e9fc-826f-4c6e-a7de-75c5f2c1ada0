package com.sankuai.dzhealth.ai.service.agent.infrastructure.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 预约项目枚举
 *
 * @author: jiyizhou
 * @time: 2025/7/10 21:00
 * @version: 0.0.1
 */
@Getter
@AllArgsConstructor
public enum ProductNameEnum {

    ORAL_EXAMINATION(1, "口腔检查"),
    DENTAL_IMPLANT(2, "种植牙"),
    ORTHODONTICS(3, "牙齿矫正"),
    FILLING(4, "补牙"),
    EXTRACTION(5, "拔牙"),
    WHITENING(6, "美白"),
    CLEANING(7, "洗牙");

    private final Integer code;
    private final String desc;

    /**
     * 根据code获取枚举
     */
    public static ProductNameEnum fromCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (ProductNameEnum productName : values()) {
            if (productName.getCode().equals(code)) {
                return productName;
            }
        }
        return null;
    }

    /**
     * 根据desc获取枚举
     */
    public static ProductNameEnum fromDesc(String desc) {
        if (desc == null || desc.trim().isEmpty()) {
            return null;
        }
        for (ProductNameEnum productName : values()) {
            if (productName.getDesc().equals(desc.trim())) {
                return productName;
            }
        }
        return null;
    }

}

