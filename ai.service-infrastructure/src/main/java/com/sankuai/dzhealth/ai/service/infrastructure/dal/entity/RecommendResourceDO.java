package com.sankuai.dzhealth.ai.service.infrastructure.dal.entity;

import java.util.Date;
import lombok.*;

/**
 *
 *   表名: recommend_resource
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class RecommendResourceDO {
    /**
     *   字段: id
     *   说明: PK
     */
    private Long id;

    /**
     *   字段: biz_scene
     *   说明: 业务场景
     */
    private String bizScene;

    /**
     *   字段: resource_id
     *   说明: 资源业务 ID
     */
    private String resourceId;

    /**
     *   字段: resource_type
     *   说明: PROJECT / DOCTOR / MERCHANT / …
     */
    private String resourceType;

    /**
     *   字段: resource_name
     *   说明: 资源名称
     */
    private String resourceName;

    /**
     *   字段: short_desc
     *   说明: 简要描述
     */
    private String shortDesc;

    /**
     *   字段: status
     *   说明: 资源状态：ONLINE / OFFLINE / DRAFT / …
     */
    private String status;

    /**
     *   字段: attributes
     *   说明: 结构化属性
     */
    private String attributes;

    /**
     *   字段: tags
     *   说明: 标签 JSON
     */
    private String tags;

    /**
     *   字段: ext
     *   说明: 扩展字段
     */
    private String ext;

    /**
     *   字段: add_time
     */
    private Date addTime;

    /**
     *   字段: update_time
     */
    private Date updateTime;
}