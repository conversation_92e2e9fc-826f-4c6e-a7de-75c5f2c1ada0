package com.sankuai.dzhealth.ai.service.agent.infrastructure.dal.repository.evaluation;

import com.sankuai.dzhealth.ai.service.agent.infrastructure.dal.entity.EvaluationSessionMessageRecordsDO;
import com.sankuai.dzhealth.ai.service.agent.infrastructure.dal.example.EvaluationSessionMessageRecordsDOExample;
import com.sankuai.dzhealth.ai.service.agent.infrastructure.dal.mapper.EvaluationSessionMessageRecordsDOMapper;
import lombok.RequiredArgsConstructor;
import org.apache.ibatis.session.RowBounds;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

@Repository
@RequiredArgsConstructor
public class EvaluationSessionMessageRecordsRepository {

    @Resource
    private EvaluationSessionMessageRecordsDOMapper evaluationSessionMessageRecordsDOMapper;

    public void insert(EvaluationSessionMessageRecordsDO record) {
        evaluationSessionMessageRecordsDOMapper.insert(record);
    }

    /**
     * 根据测评ID和会话ID查询消息列表，按创建时间倒序排列
     *
     * @return 消息记录列表
     */
    public EvaluationSessionMessageRecordsDO findByMessageId(String messageId) {
        EvaluationSessionMessageRecordsDOExample example = new EvaluationSessionMessageRecordsDOExample();
        example.createCriteria().andMessageIdEqualTo(messageId);

        List<EvaluationSessionMessageRecordsDO> evaluationSessionMessageRecordsDOS = evaluationSessionMessageRecordsDOMapper.selectByExampleWithBLOBs(example);
        return evaluationSessionMessageRecordsDOS.stream().findFirst().orElse(null);
    }


    public int updateByExampleSelective(String messageId, String evaluationResult) {
        EvaluationSessionMessageRecordsDOExample example = new EvaluationSessionMessageRecordsDOExample();
        example.createCriteria().andMessageIdEqualTo(messageId);
        EvaluationSessionMessageRecordsDO record = EvaluationSessionMessageRecordsDO.builder().evaluationResult(evaluationResult).build();
        return evaluationSessionMessageRecordsDOMapper.updateByExampleSelective(record, example);
    }


    public int InsertOrUpdate(EvaluationSessionMessageRecordsDO record) {
        return evaluationSessionMessageRecordsDOMapper.insertOrUpdate(record);
    }


    /**
     * 根据测评ID查询消息列表，按创建时间倒序排列
     * @param evaluationId 测评ID
     * @param limit 限制条数
     * @param offset 偏移量
     * @return 消息记录列表
     */
//    public List<EvaluationSessionMessageRecordsDO> findByEvaluationIdOrderByCreateTimeDesc(
//            String evaluationId, int limit, int offset) {
//        return findByEvaluationIdAndSessionIdOrderByCreateTimeDesc(evaluationId, null, limit, offset);
//    }
//
//    /**
//     * 根据会话ID查询消息列表，按创建时间倒序排列
//     * @param sessionId 会话ID
//     * @param limit 限制条数
//     * @param offset 偏移量
//     * @return 消息记录列表
//     */
//    public List<EvaluationSessionMessageRecordsDO> findBySessionIdOrderByCreateTimeDesc(
//            String sessionId, int limit, int offset) {
//        return findByEvaluationIdAndSessionIdOrderByCreateTimeDesc(null, sessionId, limit, offset);
//    }
}

