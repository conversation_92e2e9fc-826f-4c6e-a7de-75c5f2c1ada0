package com.sankuai.dzhealth.ai.service.infrastructure.dal.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.Date;

/**
 * 表名: rag_evaluation_response
 */
@SuperBuilder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class RagEvaluationResponseEntity {
    /**
     * 字段: id
     * 说明: 主键
     */
    private Long id;

    /**
     * 字段: session_id
     * 说明: 会话ID
     */
    private Long sessionId;

    /**
     * 字段: msg_id
     * 说明: 消息ID
     */
    private Long msgId;

    /**
     * 字段: evaluation_key
     * 说明: 评估项标识
     */
    private String evaluationKey;

    /**
     * 字段: description
     * 说明: 评估项描述
     */
    private String description;

    /**
     * 字段: pass
     * 说明: 是否通过(1:通过,0:不通过)
     */
    private Boolean pass;

    /**
     * 字段: score
     * 说明: 评估分数
     */
    private Float score;

    /**
     * 字段: success
     * 说明: 是否成功(1:成功,0:失败)
     */
    private Boolean success;

    /**
     * 字段: created_at
     * 说明: 创建时间
     */
    private Date createdAt;

    /**
     * 字段: updated_at
     * 说明: 更新时间
     */
    private Date updatedAt;

    /**
     * 字段: bizScene
     * 说明: 业务场景标识
     */
    private String bizscene;

    /**
     * 字段: modelScene
     * 说明: 模型场景标识
     */
    private String modelscene;
}