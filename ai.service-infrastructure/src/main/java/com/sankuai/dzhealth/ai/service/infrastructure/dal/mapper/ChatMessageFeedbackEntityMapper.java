package com.sankuai.dzhealth.ai.service.infrastructure.dal.mapper;

import com.meituan.mdp.mybatis.mapper.MybatisBaseMapper;
import com.sankuai.dzhealth.ai.service.infrastructure.dal.entity.ChatMessageFeedbackEntity;
import com.sankuai.dzhealth.ai.service.infrastructure.dal.example.ChatMessageFeedbackEntityExample;
import org.apache.ibatis.annotations.Param;

public interface ChatMessageFeedbackEntityMapper extends MybatisBaseMapper<ChatMessageFeedbackEntity, ChatMessageFeedbackEntityExample, Long> {

    int updateByExampleSelective(@Param("row") ChatMessageFeedbackEntity record, @Param("example") ChatMessageFeedbackEntityExample example);
}