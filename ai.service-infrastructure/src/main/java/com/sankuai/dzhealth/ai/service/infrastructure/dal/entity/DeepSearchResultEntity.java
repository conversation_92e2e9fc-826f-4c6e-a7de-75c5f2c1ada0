package com.sankuai.dzhealth.ai.service.infrastructure.dal.entity;

import java.math.BigDecimal;
import java.util.Date;
import lombok.*;

/**
 *
 *   表名: deep_search_results
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DeepSearchResultEntity {
    /**
     *   字段: id
     *   说明: 深度搜索结果ID
     */
    private Long id;

    /**
     *   字段: business_source
     *   说明: 业务来源标识（如：public_hospital, private_clinic等）
     */
    private String businessSource;

    /**
     *   字段: question
     *   说明: 用户问题
     */
    private String question;

    /**
     *   字段: source_urls
     *   说明: 信息来源URL列表
     */
    private String sourceUrls;

    /**
     *   字段: confidence_score
     *   说明: 置信度评分（0.0-10.0）
     */
    private BigDecimal confidenceScore;

    /**
     *   字段: add_time
     *   说明: 创建时间
     */
    private Date addTime;

    /**
     *   字段: update_time
     *   说明: 更新时间
     */
    private Date updateTime;

    /**
     *   字段: answer
     *   说明: 深度搜索生成的答案
     */
    private String answer;
}