package com.sankuai.dzhealth.ai.service.agent.infrastructure.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 皮肤报告响应数据
 * 包装皮肤报告详细信息列表
 *
 * @user: xia<PERSON><PERSON><PERSON>
 * @since: 2025/7/7 19:52
 * @version: 1.0
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SkinReportResponse {

    /**
     * 皮肤报告详细信息列表
     */
    private List<SkinReportWithPart> reportList;

    /**
     * 创建单个报告的响应
     * @param report 单个皮肤报告
     * @return 响应对象
     */
    public static SkinReportResponse of(SkinReportWithPart report) {
        return SkinReportResponse.builder()
                .reportList(List.of(report))
                .build();
    }

    /**
     * 创建多个报告的响应
     * @param reports 多个皮肤报告
     * @return 响应对象
     */
    public static SkinReportResponse of(List<SkinReportWithPart> reports) {
        return SkinReportResponse.builder()
                .reportList(reports)
                .build();
    }
}

