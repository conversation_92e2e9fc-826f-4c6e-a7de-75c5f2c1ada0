package com.sankuai.dzhealth.ai.service.agent.infrastructure.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import org.springframework.ai.document.Document;

import java.beans.Transient;
import java.io.Serializable;
import java.util.List;

/**
 * 多轮对话评估请求
 *
 * <AUTHOR>
 * @since 2025/1/15
 */
@SuperBuilder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MultiEvaluationRequest implements Serializable {

    /**
     * 业务场景
     */
    private String bizScene;

    /**
     * 会话ID
     */
    private String sessionId;

    /**
     * 消息ID
     */
    private String messageId;

    /**
     * 模型场景
     */
    private String modelScene;

    /**
     * 对话
     */
    private Message dialog;

    /**
     * 上下文
     */
    private String context;

    /**
     * 评估来源
     * 0-真实用户 1-模拟用户
     */
    private int source;

    /**
     * 评测集id，仅在 {@code MultiEvaluationService} 中动态添加
     */
    private Long evaluationId;

    /**
     * 评测集版本，仅在 {@code MultiEvaluationService} 中动态添加
     */
    private Integer evaluationVer;

    /**
     * 参考答案，仅在 {@code MultiEvaluationService} 中动态添加
     */
    private String referenceAnswer;

    /**
     * 搜索请求次数
     */
    private Integer searchRequestCount;

    /**
     * 搜索请求成功次数
     */
    private Integer searchRequestSuccessCount;

    /**
     * 对话消息
     */
    @SuperBuilder
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Message implements Serializable {

        /**
         * 被评测的系统提示
         */
        private String system;

        /**
         * 用户问题
         */
        private String userQuery;

        /**
         * agent的回答
         */
        private String assistantAnswer;

        /**
         * 召回内容列表
         */
        private List<Document> ragInfo;
    }
}

