package com.sankuai.dzhealth.ai.service.agent.infrastructure.dal.example;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class AppointmentInfoDOExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public AppointmentInfoDOExample() {
        oredCriteria = new ArrayList<>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andUserIdIsNull() {
            addCriterion("user_id is null");
            return (Criteria) this;
        }

        public Criteria andUserIdIsNotNull() {
            addCriterion("user_id is not null");
            return (Criteria) this;
        }

        public Criteria andUserIdEqualTo(Long value) {
            addCriterion("user_id =", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdNotEqualTo(Long value) {
            addCriterion("user_id <>", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdGreaterThan(Long value) {
            addCriterion("user_id >", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdGreaterThanOrEqualTo(Long value) {
            addCriterion("user_id >=", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdLessThan(Long value) {
            addCriterion("user_id <", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdLessThanOrEqualTo(Long value) {
            addCriterion("user_id <=", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdIn(List<Long> values) {
            addCriterion("user_id in", values, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdNotIn(List<Long> values) {
            addCriterion("user_id not in", values, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdBetween(Long value1, Long value2) {
            addCriterion("user_id between", value1, value2, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdNotBetween(Long value1, Long value2) {
            addCriterion("user_id not between", value1, value2, "userId");
            return (Criteria) this;
        }

        public Criteria andSessionIdIsNull() {
            addCriterion("session_id is null");
            return (Criteria) this;
        }

        public Criteria andSessionIdIsNotNull() {
            addCriterion("session_id is not null");
            return (Criteria) this;
        }

        public Criteria andSessionIdEqualTo(String value) {
            addCriterion("session_id =", value, "sessionId");
            return (Criteria) this;
        }

        public Criteria andSessionIdNotEqualTo(String value) {
            addCriterion("session_id <>", value, "sessionId");
            return (Criteria) this;
        }

        public Criteria andSessionIdGreaterThan(String value) {
            addCriterion("session_id >", value, "sessionId");
            return (Criteria) this;
        }

        public Criteria andSessionIdGreaterThanOrEqualTo(String value) {
            addCriterion("session_id >=", value, "sessionId");
            return (Criteria) this;
        }

        public Criteria andSessionIdLessThan(String value) {
            addCriterion("session_id <", value, "sessionId");
            return (Criteria) this;
        }

        public Criteria andSessionIdLessThanOrEqualTo(String value) {
            addCriterion("session_id <=", value, "sessionId");
            return (Criteria) this;
        }

        public Criteria andSessionIdLike(String value) {
            addCriterion("session_id like", value, "sessionId");
            return (Criteria) this;
        }

        public Criteria andSessionIdNotLike(String value) {
            addCriterion("session_id not like", value, "sessionId");
            return (Criteria) this;
        }

        public Criteria andSessionIdIn(List<String> values) {
            addCriterion("session_id in", values, "sessionId");
            return (Criteria) this;
        }

        public Criteria andSessionIdNotIn(List<String> values) {
            addCriterion("session_id not in", values, "sessionId");
            return (Criteria) this;
        }

        public Criteria andSessionIdBetween(String value1, String value2) {
            addCriterion("session_id between", value1, value2, "sessionId");
            return (Criteria) this;
        }

        public Criteria andSessionIdNotBetween(String value1, String value2) {
            addCriterion("session_id not between", value1, value2, "sessionId");
            return (Criteria) this;
        }

        public Criteria andMsgIdIsNull() {
            addCriterion("msg_id is null");
            return (Criteria) this;
        }

        public Criteria andMsgIdIsNotNull() {
            addCriterion("msg_id is not null");
            return (Criteria) this;
        }

        public Criteria andMsgIdEqualTo(String value) {
            addCriterion("msg_id =", value, "msgId");
            return (Criteria) this;
        }

        public Criteria andMsgIdNotEqualTo(String value) {
            addCriterion("msg_id <>", value, "msgId");
            return (Criteria) this;
        }

        public Criteria andMsgIdGreaterThan(String value) {
            addCriterion("msg_id >", value, "msgId");
            return (Criteria) this;
        }

        public Criteria andMsgIdGreaterThanOrEqualTo(String value) {
            addCriterion("msg_id >=", value, "msgId");
            return (Criteria) this;
        }

        public Criteria andMsgIdLessThan(String value) {
            addCriterion("msg_id <", value, "msgId");
            return (Criteria) this;
        }

        public Criteria andMsgIdLessThanOrEqualTo(String value) {
            addCriterion("msg_id <=", value, "msgId");
            return (Criteria) this;
        }

        public Criteria andMsgIdLike(String value) {
            addCriterion("msg_id like", value, "msgId");
            return (Criteria) this;
        }

        public Criteria andMsgIdNotLike(String value) {
            addCriterion("msg_id not like", value, "msgId");
            return (Criteria) this;
        }

        public Criteria andMsgIdIn(List<String> values) {
            addCriterion("msg_id in", values, "msgId");
            return (Criteria) this;
        }

        public Criteria andMsgIdNotIn(List<String> values) {
            addCriterion("msg_id not in", values, "msgId");
            return (Criteria) this;
        }

        public Criteria andMsgIdBetween(String value1, String value2) {
            addCriterion("msg_id between", value1, value2, "msgId");
            return (Criteria) this;
        }

        public Criteria andMsgIdNotBetween(String value1, String value2) {
            addCriterion("msg_id not between", value1, value2, "msgId");
            return (Criteria) this;
        }

        public Criteria andReservedMsgIdIsNull() {
            addCriterion("reserved_msg_id is null");
            return (Criteria) this;
        }

        public Criteria andReservedMsgIdIsNotNull() {
            addCriterion("reserved_msg_id is not null");
            return (Criteria) this;
        }

        public Criteria andReservedMsgIdEqualTo(String value) {
            addCriterion("reserved_msg_id =", value, "reservedMsgId");
            return (Criteria) this;
        }

        public Criteria andReservedMsgIdNotEqualTo(String value) {
            addCriterion("reserved_msg_id <>", value, "reservedMsgId");
            return (Criteria) this;
        }

        public Criteria andReservedMsgIdGreaterThan(String value) {
            addCriterion("reserved_msg_id >", value, "reservedMsgId");
            return (Criteria) this;
        }

        public Criteria andReservedMsgIdGreaterThanOrEqualTo(String value) {
            addCriterion("reserved_msg_id >=", value, "reservedMsgId");
            return (Criteria) this;
        }

        public Criteria andReservedMsgIdLessThan(String value) {
            addCriterion("reserved_msg_id <", value, "reservedMsgId");
            return (Criteria) this;
        }

        public Criteria andReservedMsgIdLessThanOrEqualTo(String value) {
            addCriterion("reserved_msg_id <=", value, "reservedMsgId");
            return (Criteria) this;
        }

        public Criteria andReservedMsgIdLike(String value) {
            addCriterion("reserved_msg_id like", value, "reservedMsgId");
            return (Criteria) this;
        }

        public Criteria andReservedMsgIdNotLike(String value) {
            addCriterion("reserved_msg_id not like", value, "reservedMsgId");
            return (Criteria) this;
        }

        public Criteria andReservedMsgIdIn(List<String> values) {
            addCriterion("reserved_msg_id in", values, "reservedMsgId");
            return (Criteria) this;
        }

        public Criteria andReservedMsgIdNotIn(List<String> values) {
            addCriterion("reserved_msg_id not in", values, "reservedMsgId");
            return (Criteria) this;
        }

        public Criteria andReservedMsgIdBetween(String value1, String value2) {
            addCriterion("reserved_msg_id between", value1, value2, "reservedMsgId");
            return (Criteria) this;
        }

        public Criteria andReservedMsgIdNotBetween(String value1, String value2) {
            addCriterion("reserved_msg_id not between", value1, value2, "reservedMsgId");
            return (Criteria) this;
        }

        public Criteria andProductNameIsNull() {
            addCriterion("product_name is null");
            return (Criteria) this;
        }

        public Criteria andProductNameIsNotNull() {
            addCriterion("product_name is not null");
            return (Criteria) this;
        }

        public Criteria andProductNameEqualTo(Integer value) {
            addCriterion("product_name =", value, "productName");
            return (Criteria) this;
        }

        public Criteria andProductNameNotEqualTo(Integer value) {
            addCriterion("product_name <>", value, "productName");
            return (Criteria) this;
        }

        public Criteria andProductNameGreaterThan(Integer value) {
            addCriterion("product_name >", value, "productName");
            return (Criteria) this;
        }

        public Criteria andProductNameGreaterThanOrEqualTo(Integer value) {
            addCriterion("product_name >=", value, "productName");
            return (Criteria) this;
        }

        public Criteria andProductNameLessThan(Integer value) {
            addCriterion("product_name <", value, "productName");
            return (Criteria) this;
        }

        public Criteria andProductNameLessThanOrEqualTo(Integer value) {
            addCriterion("product_name <=", value, "productName");
            return (Criteria) this;
        }

        public Criteria andProductNameIn(List<Integer> values) {
            addCriterion("product_name in", values, "productName");
            return (Criteria) this;
        }

        public Criteria andProductNameNotIn(List<Integer> values) {
            addCriterion("product_name not in", values, "productName");
            return (Criteria) this;
        }

        public Criteria andProductNameBetween(Integer value1, Integer value2) {
            addCriterion("product_name between", value1, value2, "productName");
            return (Criteria) this;
        }

        public Criteria andProductNameNotBetween(Integer value1, Integer value2) {
            addCriterion("product_name not between", value1, value2, "productName");
            return (Criteria) this;
        }

        public Criteria andAppointmentStartTimeIsNull() {
            addCriterion("appointment_start_time is null");
            return (Criteria) this;
        }

        public Criteria andAppointmentStartTimeIsNotNull() {
            addCriterion("appointment_start_time is not null");
            return (Criteria) this;
        }

        public Criteria andAppointmentStartTimeEqualTo(Date value) {
            addCriterion("appointment_start_time =", value, "appointmentStartTime");
            return (Criteria) this;
        }

        public Criteria andAppointmentStartTimeNotEqualTo(Date value) {
            addCriterion("appointment_start_time <>", value, "appointmentStartTime");
            return (Criteria) this;
        }

        public Criteria andAppointmentStartTimeGreaterThan(Date value) {
            addCriterion("appointment_start_time >", value, "appointmentStartTime");
            return (Criteria) this;
        }

        public Criteria andAppointmentStartTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("appointment_start_time >=", value, "appointmentStartTime");
            return (Criteria) this;
        }

        public Criteria andAppointmentStartTimeLessThan(Date value) {
            addCriterion("appointment_start_time <", value, "appointmentStartTime");
            return (Criteria) this;
        }

        public Criteria andAppointmentStartTimeLessThanOrEqualTo(Date value) {
            addCriterion("appointment_start_time <=", value, "appointmentStartTime");
            return (Criteria) this;
        }

        public Criteria andAppointmentStartTimeIn(List<Date> values) {
            addCriterion("appointment_start_time in", values, "appointmentStartTime");
            return (Criteria) this;
        }

        public Criteria andAppointmentStartTimeNotIn(List<Date> values) {
            addCriterion("appointment_start_time not in", values, "appointmentStartTime");
            return (Criteria) this;
        }

        public Criteria andAppointmentStartTimeBetween(Date value1, Date value2) {
            addCriterion("appointment_start_time between", value1, value2, "appointmentStartTime");
            return (Criteria) this;
        }

        public Criteria andAppointmentStartTimeNotBetween(Date value1, Date value2) {
            addCriterion("appointment_start_time not between", value1, value2, "appointmentStartTime");
            return (Criteria) this;
        }

        public Criteria andAppointmentEndTimeIsNull() {
            addCriterion("appointment_end_time is null");
            return (Criteria) this;
        }

        public Criteria andAppointmentEndTimeIsNotNull() {
            addCriterion("appointment_end_time is not null");
            return (Criteria) this;
        }

        public Criteria andAppointmentEndTimeEqualTo(Date value) {
            addCriterion("appointment_end_time =", value, "appointmentEndTime");
            return (Criteria) this;
        }

        public Criteria andAppointmentEndTimeNotEqualTo(Date value) {
            addCriterion("appointment_end_time <>", value, "appointmentEndTime");
            return (Criteria) this;
        }

        public Criteria andAppointmentEndTimeGreaterThan(Date value) {
            addCriterion("appointment_end_time >", value, "appointmentEndTime");
            return (Criteria) this;
        }

        public Criteria andAppointmentEndTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("appointment_end_time >=", value, "appointmentEndTime");
            return (Criteria) this;
        }

        public Criteria andAppointmentEndTimeLessThan(Date value) {
            addCriterion("appointment_end_time <", value, "appointmentEndTime");
            return (Criteria) this;
        }

        public Criteria andAppointmentEndTimeLessThanOrEqualTo(Date value) {
            addCriterion("appointment_end_time <=", value, "appointmentEndTime");
            return (Criteria) this;
        }

        public Criteria andAppointmentEndTimeIn(List<Date> values) {
            addCriterion("appointment_end_time in", values, "appointmentEndTime");
            return (Criteria) this;
        }

        public Criteria andAppointmentEndTimeNotIn(List<Date> values) {
            addCriterion("appointment_end_time not in", values, "appointmentEndTime");
            return (Criteria) this;
        }

        public Criteria andAppointmentEndTimeBetween(Date value1, Date value2) {
            addCriterion("appointment_end_time between", value1, value2, "appointmentEndTime");
            return (Criteria) this;
        }

        public Criteria andAppointmentEndTimeNotBetween(Date value1, Date value2) {
            addCriterion("appointment_end_time not between", value1, value2, "appointmentEndTime");
            return (Criteria) this;
        }

        public Criteria andPositionTxtIsNull() {
            addCriterion("position_txt is null");
            return (Criteria) this;
        }

        public Criteria andPositionTxtIsNotNull() {
            addCriterion("position_txt is not null");
            return (Criteria) this;
        }

        public Criteria andPositionTxtEqualTo(String value) {
            addCriterion("position_txt =", value, "positionTxt");
            return (Criteria) this;
        }

        public Criteria andPositionTxtNotEqualTo(String value) {
            addCriterion("position_txt <>", value, "positionTxt");
            return (Criteria) this;
        }

        public Criteria andPositionTxtGreaterThan(String value) {
            addCriterion("position_txt >", value, "positionTxt");
            return (Criteria) this;
        }

        public Criteria andPositionTxtGreaterThanOrEqualTo(String value) {
            addCriterion("position_txt >=", value, "positionTxt");
            return (Criteria) this;
        }

        public Criteria andPositionTxtLessThan(String value) {
            addCriterion("position_txt <", value, "positionTxt");
            return (Criteria) this;
        }

        public Criteria andPositionTxtLessThanOrEqualTo(String value) {
            addCriterion("position_txt <=", value, "positionTxt");
            return (Criteria) this;
        }

        public Criteria andPositionTxtLike(String value) {
            addCriterion("position_txt like", value, "positionTxt");
            return (Criteria) this;
        }

        public Criteria andPositionTxtNotLike(String value) {
            addCriterion("position_txt not like", value, "positionTxt");
            return (Criteria) this;
        }

        public Criteria andPositionTxtIn(List<String> values) {
            addCriterion("position_txt in", values, "positionTxt");
            return (Criteria) this;
        }

        public Criteria andPositionTxtNotIn(List<String> values) {
            addCriterion("position_txt not in", values, "positionTxt");
            return (Criteria) this;
        }

        public Criteria andPositionTxtBetween(String value1, String value2) {
            addCriterion("position_txt between", value1, value2, "positionTxt");
            return (Criteria) this;
        }

        public Criteria andPositionTxtNotBetween(String value1, String value2) {
            addCriterion("position_txt not between", value1, value2, "positionTxt");
            return (Criteria) this;
        }

        public Criteria andLngIsNull() {
            addCriterion("lng is null");
            return (Criteria) this;
        }

        public Criteria andLngIsNotNull() {
            addCriterion("lng is not null");
            return (Criteria) this;
        }

        public Criteria andLngEqualTo(Double value) {
            addCriterion("lng =", value, "lng");
            return (Criteria) this;
        }

        public Criteria andLngNotEqualTo(Double value) {
            addCriterion("lng <>", value, "lng");
            return (Criteria) this;
        }

        public Criteria andLngGreaterThan(Double value) {
            addCriterion("lng >", value, "lng");
            return (Criteria) this;
        }

        public Criteria andLngGreaterThanOrEqualTo(Double value) {
            addCriterion("lng >=", value, "lng");
            return (Criteria) this;
        }

        public Criteria andLngLessThan(Double value) {
            addCriterion("lng <", value, "lng");
            return (Criteria) this;
        }

        public Criteria andLngLessThanOrEqualTo(Double value) {
            addCriterion("lng <=", value, "lng");
            return (Criteria) this;
        }

        public Criteria andLngIn(List<Double> values) {
            addCriterion("lng in", values, "lng");
            return (Criteria) this;
        }

        public Criteria andLngNotIn(List<Double> values) {
            addCriterion("lng not in", values, "lng");
            return (Criteria) this;
        }

        public Criteria andLngBetween(Double value1, Double value2) {
            addCriterion("lng between", value1, value2, "lng");
            return (Criteria) this;
        }

        public Criteria andLngNotBetween(Double value1, Double value2) {
            addCriterion("lng not between", value1, value2, "lng");
            return (Criteria) this;
        }

        public Criteria andLatIsNull() {
            addCriterion("lat is null");
            return (Criteria) this;
        }

        public Criteria andLatIsNotNull() {
            addCriterion("lat is not null");
            return (Criteria) this;
        }

        public Criteria andLatEqualTo(Double value) {
            addCriterion("lat =", value, "lat");
            return (Criteria) this;
        }

        public Criteria andLatNotEqualTo(Double value) {
            addCriterion("lat <>", value, "lat");
            return (Criteria) this;
        }

        public Criteria andLatGreaterThan(Double value) {
            addCriterion("lat >", value, "lat");
            return (Criteria) this;
        }

        public Criteria andLatGreaterThanOrEqualTo(Double value) {
            addCriterion("lat >=", value, "lat");
            return (Criteria) this;
        }

        public Criteria andLatLessThan(Double value) {
            addCriterion("lat <", value, "lat");
            return (Criteria) this;
        }

        public Criteria andLatLessThanOrEqualTo(Double value) {
            addCriterion("lat <=", value, "lat");
            return (Criteria) this;
        }

        public Criteria andLatIn(List<Double> values) {
            addCriterion("lat in", values, "lat");
            return (Criteria) this;
        }

        public Criteria andLatNotIn(List<Double> values) {
            addCriterion("lat not in", values, "lat");
            return (Criteria) this;
        }

        public Criteria andLatBetween(Double value1, Double value2) {
            addCriterion("lat between", value1, value2, "lat");
            return (Criteria) this;
        }

        public Criteria andLatNotBetween(Double value1, Double value2) {
            addCriterion("lat not between", value1, value2, "lat");
            return (Criteria) this;
        }

        public Criteria andPersonDescIsNull() {
            addCriterion("person_desc is null");
            return (Criteria) this;
        }

        public Criteria andPersonDescIsNotNull() {
            addCriterion("person_desc is not null");
            return (Criteria) this;
        }

        public Criteria andPersonDescEqualTo(String value) {
            addCriterion("person_desc =", value, "personDesc");
            return (Criteria) this;
        }

        public Criteria andPersonDescNotEqualTo(String value) {
            addCriterion("person_desc <>", value, "personDesc");
            return (Criteria) this;
        }

        public Criteria andPersonDescGreaterThan(String value) {
            addCriterion("person_desc >", value, "personDesc");
            return (Criteria) this;
        }

        public Criteria andPersonDescGreaterThanOrEqualTo(String value) {
            addCriterion("person_desc >=", value, "personDesc");
            return (Criteria) this;
        }

        public Criteria andPersonDescLessThan(String value) {
            addCriterion("person_desc <", value, "personDesc");
            return (Criteria) this;
        }

        public Criteria andPersonDescLessThanOrEqualTo(String value) {
            addCriterion("person_desc <=", value, "personDesc");
            return (Criteria) this;
        }

        public Criteria andPersonDescLike(String value) {
            addCriterion("person_desc like", value, "personDesc");
            return (Criteria) this;
        }

        public Criteria andPersonDescNotLike(String value) {
            addCriterion("person_desc not like", value, "personDesc");
            return (Criteria) this;
        }

        public Criteria andPersonDescIn(List<String> values) {
            addCriterion("person_desc in", values, "personDesc");
            return (Criteria) this;
        }

        public Criteria andPersonDescNotIn(List<String> values) {
            addCriterion("person_desc not in", values, "personDesc");
            return (Criteria) this;
        }

        public Criteria andPersonDescBetween(String value1, String value2) {
            addCriterion("person_desc between", value1, value2, "personDesc");
            return (Criteria) this;
        }

        public Criteria andPersonDescNotBetween(String value1, String value2) {
            addCriterion("person_desc not between", value1, value2, "personDesc");
            return (Criteria) this;
        }

        public Criteria andFilterItemsIsNull() {
            addCriterion("filter_items is null");
            return (Criteria) this;
        }

        public Criteria andFilterItemsIsNotNull() {
            addCriterion("filter_items is not null");
            return (Criteria) this;
        }

        public Criteria andFilterItemsEqualTo(String value) {
            addCriterion("filter_items =", value, "filterItems");
            return (Criteria) this;
        }

        public Criteria andFilterItemsNotEqualTo(String value) {
            addCriterion("filter_items <>", value, "filterItems");
            return (Criteria) this;
        }

        public Criteria andFilterItemsGreaterThan(String value) {
            addCriterion("filter_items >", value, "filterItems");
            return (Criteria) this;
        }

        public Criteria andFilterItemsGreaterThanOrEqualTo(String value) {
            addCriterion("filter_items >=", value, "filterItems");
            return (Criteria) this;
        }

        public Criteria andFilterItemsLessThan(String value) {
            addCriterion("filter_items <", value, "filterItems");
            return (Criteria) this;
        }

        public Criteria andFilterItemsLessThanOrEqualTo(String value) {
            addCriterion("filter_items <=", value, "filterItems");
            return (Criteria) this;
        }

        public Criteria andFilterItemsLike(String value) {
            addCriterion("filter_items like", value, "filterItems");
            return (Criteria) this;
        }

        public Criteria andFilterItemsNotLike(String value) {
            addCriterion("filter_items not like", value, "filterItems");
            return (Criteria) this;
        }

        public Criteria andFilterItemsIn(List<String> values) {
            addCriterion("filter_items in", values, "filterItems");
            return (Criteria) this;
        }

        public Criteria andFilterItemsNotIn(List<String> values) {
            addCriterion("filter_items not in", values, "filterItems");
            return (Criteria) this;
        }

        public Criteria andFilterItemsBetween(String value1, String value2) {
            addCriterion("filter_items between", value1, value2, "filterItems");
            return (Criteria) this;
        }

        public Criteria andFilterItemsNotBetween(String value1, String value2) {
            addCriterion("filter_items not between", value1, value2, "filterItems");
            return (Criteria) this;
        }

        public Criteria andPhoneIsNull() {
            addCriterion("phone is null");
            return (Criteria) this;
        }

        public Criteria andPhoneIsNotNull() {
            addCriterion("phone is not null");
            return (Criteria) this;
        }

        public Criteria andPhoneEqualTo(String value) {
            addCriterion("phone =", value, "phone");
            return (Criteria) this;
        }

        public Criteria andPhoneNotEqualTo(String value) {
            addCriterion("phone <>", value, "phone");
            return (Criteria) this;
        }

        public Criteria andPhoneGreaterThan(String value) {
            addCriterion("phone >", value, "phone");
            return (Criteria) this;
        }

        public Criteria andPhoneGreaterThanOrEqualTo(String value) {
            addCriterion("phone >=", value, "phone");
            return (Criteria) this;
        }

        public Criteria andPhoneLessThan(String value) {
            addCriterion("phone <", value, "phone");
            return (Criteria) this;
        }

        public Criteria andPhoneLessThanOrEqualTo(String value) {
            addCriterion("phone <=", value, "phone");
            return (Criteria) this;
        }

        public Criteria andPhoneLike(String value) {
            addCriterion("phone like", value, "phone");
            return (Criteria) this;
        }

        public Criteria andPhoneNotLike(String value) {
            addCriterion("phone not like", value, "phone");
            return (Criteria) this;
        }

        public Criteria andPhoneIn(List<String> values) {
            addCriterion("phone in", values, "phone");
            return (Criteria) this;
        }

        public Criteria andPhoneNotIn(List<String> values) {
            addCriterion("phone not in", values, "phone");
            return (Criteria) this;
        }

        public Criteria andPhoneBetween(String value1, String value2) {
            addCriterion("phone between", value1, value2, "phone");
            return (Criteria) this;
        }

        public Criteria andPhoneNotBetween(String value1, String value2) {
            addCriterion("phone not between", value1, value2, "phone");
            return (Criteria) this;
        }

        public Criteria andSuccessShopIdIsNull() {
            addCriterion("success_shop_id is null");
            return (Criteria) this;
        }

        public Criteria andSuccessShopIdIsNotNull() {
            addCriterion("success_shop_id is not null");
            return (Criteria) this;
        }

        public Criteria andSuccessShopIdEqualTo(Long value) {
            addCriterion("success_shop_id =", value, "successShopId");
            return (Criteria) this;
        }

        public Criteria andSuccessShopIdNotEqualTo(Long value) {
            addCriterion("success_shop_id <>", value, "successShopId");
            return (Criteria) this;
        }

        public Criteria andSuccessShopIdGreaterThan(Long value) {
            addCriterion("success_shop_id >", value, "successShopId");
            return (Criteria) this;
        }

        public Criteria andSuccessShopIdGreaterThanOrEqualTo(Long value) {
            addCriterion("success_shop_id >=", value, "successShopId");
            return (Criteria) this;
        }

        public Criteria andSuccessShopIdLessThan(Long value) {
            addCriterion("success_shop_id <", value, "successShopId");
            return (Criteria) this;
        }

        public Criteria andSuccessShopIdLessThanOrEqualTo(Long value) {
            addCriterion("success_shop_id <=", value, "successShopId");
            return (Criteria) this;
        }

        public Criteria andSuccessShopIdIn(List<Long> values) {
            addCriterion("success_shop_id in", values, "successShopId");
            return (Criteria) this;
        }

        public Criteria andSuccessShopIdNotIn(List<Long> values) {
            addCriterion("success_shop_id not in", values, "successShopId");
            return (Criteria) this;
        }

        public Criteria andSuccessShopIdBetween(Long value1, Long value2) {
            addCriterion("success_shop_id between", value1, value2, "successShopId");
            return (Criteria) this;
        }

        public Criteria andSuccessShopIdNotBetween(Long value1, Long value2) {
            addCriterion("success_shop_id not between", value1, value2, "successShopId");
            return (Criteria) this;
        }

        public Criteria andTaskIdIsNull() {
            addCriterion("task_id is null");
            return (Criteria) this;
        }

        public Criteria andTaskIdIsNotNull() {
            addCriterion("task_id is not null");
            return (Criteria) this;
        }

        public Criteria andTaskIdEqualTo(Long value) {
            addCriterion("task_id =", value, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdNotEqualTo(Long value) {
            addCriterion("task_id <>", value, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdGreaterThan(Long value) {
            addCriterion("task_id >", value, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdGreaterThanOrEqualTo(Long value) {
            addCriterion("task_id >=", value, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdLessThan(Long value) {
            addCriterion("task_id <", value, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdLessThanOrEqualTo(Long value) {
            addCriterion("task_id <=", value, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdIn(List<Long> values) {
            addCriterion("task_id in", values, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdNotIn(List<Long> values) {
            addCriterion("task_id not in", values, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdBetween(Long value1, Long value2) {
            addCriterion("task_id between", value1, value2, "taskId");
            return (Criteria) this;
        }

        public Criteria andTaskIdNotBetween(Long value1, Long value2) {
            addCriterion("task_id not between", value1, value2, "taskId");
            return (Criteria) this;
        }

        public Criteria andCardInfoIsNull() {
            addCriterion("card_info is null");
            return (Criteria) this;
        }

        public Criteria andCardInfoIsNotNull() {
            addCriterion("card_info is not null");
            return (Criteria) this;
        }

        public Criteria andCardInfoEqualTo(String value) {
            addCriterion("card_info =", value, "cardInfo");
            return (Criteria) this;
        }

        public Criteria andCardInfoNotEqualTo(String value) {
            addCriterion("card_info <>", value, "cardInfo");
            return (Criteria) this;
        }

        public Criteria andCardInfoGreaterThan(String value) {
            addCriterion("card_info >", value, "cardInfo");
            return (Criteria) this;
        }

        public Criteria andCardInfoGreaterThanOrEqualTo(String value) {
            addCriterion("card_info >=", value, "cardInfo");
            return (Criteria) this;
        }

        public Criteria andCardInfoLessThan(String value) {
            addCriterion("card_info <", value, "cardInfo");
            return (Criteria) this;
        }

        public Criteria andCardInfoLessThanOrEqualTo(String value) {
            addCriterion("card_info <=", value, "cardInfo");
            return (Criteria) this;
        }

        public Criteria andCardInfoLike(String value) {
            addCriterion("card_info like", value, "cardInfo");
            return (Criteria) this;
        }

        public Criteria andCardInfoNotLike(String value) {
            addCriterion("card_info not like", value, "cardInfo");
            return (Criteria) this;
        }

        public Criteria andCardInfoIn(List<String> values) {
            addCriterion("card_info in", values, "cardInfo");
            return (Criteria) this;
        }

        public Criteria andCardInfoNotIn(List<String> values) {
            addCriterion("card_info not in", values, "cardInfo");
            return (Criteria) this;
        }

        public Criteria andCardInfoBetween(String value1, String value2) {
            addCriterion("card_info between", value1, value2, "cardInfo");
            return (Criteria) this;
        }

        public Criteria andCardInfoNotBetween(String value1, String value2) {
            addCriterion("card_info not between", value1, value2, "cardInfo");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("status is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("status is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(Byte value) {
            addCriterion("status =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(Byte value) {
            addCriterion("status <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(Byte value) {
            addCriterion("status >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(Byte value) {
            addCriterion("status >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(Byte value) {
            addCriterion("status <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(Byte value) {
            addCriterion("status <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<Byte> values) {
            addCriterion("status in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<Byte> values) {
            addCriterion("status not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(Byte value1, Byte value2) {
            addCriterion("status between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(Byte value1, Byte value2) {
            addCriterion("status not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andLeadIdIsNull() {
            addCriterion("lead_id is null");
            return (Criteria) this;
        }

        public Criteria andLeadIdIsNotNull() {
            addCriterion("lead_id is not null");
            return (Criteria) this;
        }

        public Criteria andLeadIdEqualTo(Long value) {
            addCriterion("lead_id =", value, "leadId");
            return (Criteria) this;
        }

        public Criteria andLeadIdNotEqualTo(Long value) {
            addCriterion("lead_id <>", value, "leadId");
            return (Criteria) this;
        }

        public Criteria andLeadIdGreaterThan(Long value) {
            addCriterion("lead_id >", value, "leadId");
            return (Criteria) this;
        }

        public Criteria andLeadIdGreaterThanOrEqualTo(Long value) {
            addCriterion("lead_id >=", value, "leadId");
            return (Criteria) this;
        }

        public Criteria andLeadIdLessThan(Long value) {
            addCriterion("lead_id <", value, "leadId");
            return (Criteria) this;
        }

        public Criteria andLeadIdLessThanOrEqualTo(Long value) {
            addCriterion("lead_id <=", value, "leadId");
            return (Criteria) this;
        }

        public Criteria andLeadIdIn(List<Long> values) {
            addCriterion("lead_id in", values, "leadId");
            return (Criteria) this;
        }

        public Criteria andLeadIdNotIn(List<Long> values) {
            addCriterion("lead_id not in", values, "leadId");
            return (Criteria) this;
        }

        public Criteria andLeadIdBetween(Long value1, Long value2) {
            addCriterion("lead_id between", value1, value2, "leadId");
            return (Criteria) this;
        }

        public Criteria andLeadIdNotBetween(Long value1, Long value2) {
            addCriterion("lead_id not between", value1, value2, "leadId");
            return (Criteria) this;
        }

        public Criteria andRemarkIsNull() {
            addCriterion("remark is null");
            return (Criteria) this;
        }

        public Criteria andRemarkIsNotNull() {
            addCriterion("remark is not null");
            return (Criteria) this;
        }

        public Criteria andRemarkEqualTo(String value) {
            addCriterion("remark =", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotEqualTo(String value) {
            addCriterion("remark <>", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkGreaterThan(String value) {
            addCriterion("remark >", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkGreaterThanOrEqualTo(String value) {
            addCriterion("remark >=", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLessThan(String value) {
            addCriterion("remark <", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLessThanOrEqualTo(String value) {
            addCriterion("remark <=", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLike(String value) {
            addCriterion("remark like", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotLike(String value) {
            addCriterion("remark not like", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkIn(List<String> values) {
            addCriterion("remark in", values, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotIn(List<String> values) {
            addCriterion("remark not in", values, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkBetween(String value1, String value2) {
            addCriterion("remark between", value1, value2, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotBetween(String value1, String value2) {
            addCriterion("remark not between", value1, value2, "remark");
            return (Criteria) this;
        }

        public Criteria andCityidIsNull() {
            addCriterion("cityId is null");
            return (Criteria) this;
        }

        public Criteria andCityidIsNotNull() {
            addCriterion("cityId is not null");
            return (Criteria) this;
        }

        public Criteria andCityidEqualTo(Integer value) {
            addCriterion("cityId =", value, "cityid");
            return (Criteria) this;
        }

        public Criteria andCityidNotEqualTo(Integer value) {
            addCriterion("cityId <>", value, "cityid");
            return (Criteria) this;
        }

        public Criteria andCityidGreaterThan(Integer value) {
            addCriterion("cityId >", value, "cityid");
            return (Criteria) this;
        }

        public Criteria andCityidGreaterThanOrEqualTo(Integer value) {
            addCriterion("cityId >=", value, "cityid");
            return (Criteria) this;
        }

        public Criteria andCityidLessThan(Integer value) {
            addCriterion("cityId <", value, "cityid");
            return (Criteria) this;
        }

        public Criteria andCityidLessThanOrEqualTo(Integer value) {
            addCriterion("cityId <=", value, "cityid");
            return (Criteria) this;
        }

        public Criteria andCityidIn(List<Integer> values) {
            addCriterion("cityId in", values, "cityid");
            return (Criteria) this;
        }

        public Criteria andCityidNotIn(List<Integer> values) {
            addCriterion("cityId not in", values, "cityid");
            return (Criteria) this;
        }

        public Criteria andCityidBetween(Integer value1, Integer value2) {
            addCriterion("cityId between", value1, value2, "cityid");
            return (Criteria) this;
        }

        public Criteria andCityidNotBetween(Integer value1, Integer value2) {
            addCriterion("cityId not between", value1, value2, "cityid");
            return (Criteria) this;
        }

        public Criteria andDistanceIsNull() {
            addCriterion("distance is null");
            return (Criteria) this;
        }

        public Criteria andDistanceIsNotNull() {
            addCriterion("distance is not null");
            return (Criteria) this;
        }

        public Criteria andDistanceEqualTo(Double value) {
            addCriterion("distance =", value, "distance");
            return (Criteria) this;
        }

        public Criteria andDistanceNotEqualTo(Double value) {
            addCriterion("distance <>", value, "distance");
            return (Criteria) this;
        }

        public Criteria andDistanceGreaterThan(Double value) {
            addCriterion("distance >", value, "distance");
            return (Criteria) this;
        }

        public Criteria andDistanceGreaterThanOrEqualTo(Double value) {
            addCriterion("distance >=", value, "distance");
            return (Criteria) this;
        }

        public Criteria andDistanceLessThan(Double value) {
            addCriterion("distance <", value, "distance");
            return (Criteria) this;
        }

        public Criteria andDistanceLessThanOrEqualTo(Double value) {
            addCriterion("distance <=", value, "distance");
            return (Criteria) this;
        }

        public Criteria andDistanceIn(List<Double> values) {
            addCriterion("distance in", values, "distance");
            return (Criteria) this;
        }

        public Criteria andDistanceNotIn(List<Double> values) {
            addCriterion("distance not in", values, "distance");
            return (Criteria) this;
        }

        public Criteria andDistanceBetween(Double value1, Double value2) {
            addCriterion("distance between", value1, value2, "distance");
            return (Criteria) this;
        }

        public Criteria andDistanceNotBetween(Double value1, Double value2) {
            addCriterion("distance not between", value1, value2, "distance");
            return (Criteria) this;
        }

        public Criteria andPriceIsNull() {
            addCriterion("price is null");
            return (Criteria) this;
        }

        public Criteria andPriceIsNotNull() {
            addCriterion("price is not null");
            return (Criteria) this;
        }

        public Criteria andPriceEqualTo(String value) {
            addCriterion("price =", value, "price");
            return (Criteria) this;
        }

        public Criteria andPriceNotEqualTo(String value) {
            addCriterion("price <>", value, "price");
            return (Criteria) this;
        }

        public Criteria andPriceGreaterThan(String value) {
            addCriterion("price >", value, "price");
            return (Criteria) this;
        }

        public Criteria andPriceGreaterThanOrEqualTo(String value) {
            addCriterion("price >=", value, "price");
            return (Criteria) this;
        }

        public Criteria andPriceLessThan(String value) {
            addCriterion("price <", value, "price");
            return (Criteria) this;
        }

        public Criteria andPriceLessThanOrEqualTo(String value) {
            addCriterion("price <=", value, "price");
            return (Criteria) this;
        }

        public Criteria andPriceLike(String value) {
            addCriterion("price like", value, "price");
            return (Criteria) this;
        }

        public Criteria andPriceNotLike(String value) {
            addCriterion("price not like", value, "price");
            return (Criteria) this;
        }

        public Criteria andPriceIn(List<String> values) {
            addCriterion("price in", values, "price");
            return (Criteria) this;
        }

        public Criteria andPriceNotIn(List<String> values) {
            addCriterion("price not in", values, "price");
            return (Criteria) this;
        }

        public Criteria andPriceBetween(String value1, String value2) {
            addCriterion("price between", value1, value2, "price");
            return (Criteria) this;
        }

        public Criteria andPriceNotBetween(String value1, String value2) {
            addCriterion("price not between", value1, value2, "price");
            return (Criteria) this;
        }

        public Criteria andSearchwordIsNull() {
            addCriterion("searchword is null");
            return (Criteria) this;
        }

        public Criteria andSearchwordIsNotNull() {
            addCriterion("searchword is not null");
            return (Criteria) this;
        }

        public Criteria andSearchwordEqualTo(String value) {
            addCriterion("searchword =", value, "searchword");
            return (Criteria) this;
        }

        public Criteria andSearchwordNotEqualTo(String value) {
            addCriterion("searchword <>", value, "searchword");
            return (Criteria) this;
        }

        public Criteria andSearchwordGreaterThan(String value) {
            addCriterion("searchword >", value, "searchword");
            return (Criteria) this;
        }

        public Criteria andSearchwordGreaterThanOrEqualTo(String value) {
            addCriterion("searchword >=", value, "searchword");
            return (Criteria) this;
        }

        public Criteria andSearchwordLessThan(String value) {
            addCriterion("searchword <", value, "searchword");
            return (Criteria) this;
        }

        public Criteria andSearchwordLessThanOrEqualTo(String value) {
            addCriterion("searchword <=", value, "searchword");
            return (Criteria) this;
        }

        public Criteria andSearchwordLike(String value) {
            addCriterion("searchword like", value, "searchword");
            return (Criteria) this;
        }

        public Criteria andSearchwordNotLike(String value) {
            addCriterion("searchword not like", value, "searchword");
            return (Criteria) this;
        }

        public Criteria andSearchwordIn(List<String> values) {
            addCriterion("searchword in", values, "searchword");
            return (Criteria) this;
        }

        public Criteria andSearchwordNotIn(List<String> values) {
            addCriterion("searchword not in", values, "searchword");
            return (Criteria) this;
        }

        public Criteria andSearchwordBetween(String value1, String value2) {
            addCriterion("searchword between", value1, value2, "searchword");
            return (Criteria) this;
        }

        public Criteria andSearchwordNotBetween(String value1, String value2) {
            addCriterion("searchword not between", value1, value2, "searchword");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}