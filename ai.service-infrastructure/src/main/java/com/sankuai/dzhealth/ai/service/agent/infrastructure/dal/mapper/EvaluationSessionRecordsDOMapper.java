package com.sankuai.dzhealth.ai.service.agent.infrastructure.dal.mapper;

import com.meituan.mdp.mybatis.mapper.MybatisBLOBsMapper;
import com.sankuai.dzhealth.ai.service.agent.infrastructure.dal.entity.EvaluationSessionRecordsDO;
import com.sankuai.dzhealth.ai.service.agent.infrastructure.dal.example.EvaluationSessionRecordsDOExample;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface EvaluationSessionRecordsDOMapper extends MybatisBLOBsMapper<EvaluationSessionRecordsDO, EvaluationSessionRecordsDOExample, Long> {
    List<EvaluationSessionRecordsDO> selectByExampleWithBLOBsWithPage(@Param("example") EvaluationSessionRecordsDOExample example, @Param("offset") int offset, @Param("limit") int limit);

    /**
     * 根据caseId列表批量删除记录
     * @param sessionIDs case ID列表
     * @return 删除的记录数
     */
    int deleteBySessionIDs(@Param("list") List<String> sessionIDs);
}