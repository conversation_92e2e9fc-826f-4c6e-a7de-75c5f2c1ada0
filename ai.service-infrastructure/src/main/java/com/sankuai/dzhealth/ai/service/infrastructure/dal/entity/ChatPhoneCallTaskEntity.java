package com.sankuai.dzhealth.ai.service.infrastructure.dal.entity;

import java.util.Date;
import lombok.*;

/**
 *
 *   表名: chat_phonecall_task
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ChatPhoneCallTaskEntity {
    /**
     *   字段: id
     *   说明: 任务编号
     */
    private Long id;

    /**
     *   字段: contact_id
     *   说明: 联络标识（木星平台唯一ID）
     */
    private String contactId;

    /**
     *   字段: message_id
     *   说明: 绑定消息ID
     */
    private Long messageId;

    /**
     *   字段: session_id
     *   说明: 绑定会话ID
     */
    private Long sessionId;

    /**
     *   字段: status
     *   说明: 状态: 1=初始化, 2=外呼中, 3=成功, 4=失败
     */
    private Integer status;

    /**
     *   字段: call_count
     *   说明: 外呼次数
     */
    private Integer callCount;

    /**
     *   字段: mt_shop_id
     *   说明: 美团商户ID
     */
    private Long mtShopId;

    /**
     *   字段: start_time
     *   说明: 外呼开始时间
     */
    private Date startTime;

    /**
     *   字段: end_time
     *   说明: 外呼结束时间
     */
    private Date endTime;

    /**
     *   字段: talking_time_len
     *   说明: 通话时长(ms)
     */
    private Integer talkingTimeLen;

    /**
     *   字段: release_reason
     *   说明: 挂断原因: 0=系统异常, 1=坐席挂断, 2=客户挂断, ..., 17=语音信箱
     */
    private Integer releaseReason;

    /**
     *   字段: question
     *   说明: 问题列表(JSON格式)
     */
    private String question;

    /**
     *   字段: mediatxt
     *   说明: 匹配的问题模板
     */
    private String mediatxt;

    /**
     *   字段: dialog_raw_data
     *   说明: 原始对话记录(JSON格式)
     */
    private String dialogRawData;

    /**
     *   字段: dialog_refined_data
     *   说明: 提炼对话记录(JSON格式)
     */
    private String dialogRefinedData;
}