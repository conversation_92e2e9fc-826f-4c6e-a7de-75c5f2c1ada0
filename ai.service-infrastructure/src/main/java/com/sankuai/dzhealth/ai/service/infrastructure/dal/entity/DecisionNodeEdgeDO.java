package com.sankuai.dzhealth.ai.service.infrastructure.dal.entity;

import java.util.Date;
import lombok.*;

/**
 *
 *   表名: decision_node_edge
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DecisionNodeEdgeDO {
    /**
     *   字段: id
     *   说明: PK
     */
    private Long id;

    /**
     *   字段: biz_scene
     *   说明: 业务场景
     */
    private String bizScene;

    /**
     *   字段: edge_id
     *   说明: 业务边 ID
     */
    private String edgeId;

    /**
     *   字段: parent_id
     *   说明: 父节点 ID
     */
    private String parentId;

    /**
     *   字段: child_id
     *   说明: 子节点 ID
     */
    private String childId;

    /**
     *   字段: edge_type
     *   说明: 决策方式：AI / OPTION / DEFAULT / …
     */
    private String edgeType;

    /**
     *   字段: edge_desc
     *   说明: 匹配值或按钮文案
     */
    private String edgeDesc;

    /**
     *   字段: sort_order
     *   说明: 同父节点下优先级，越小越前
     */
    private Long sortOrder;

    /**
     *   字段: status
     *   说明: ONLINE/GRAY/ONLINE_DELETE/GRAY_DELETE/OFFLINE
     */
    private String status;

    /**
     *   字段: ext
     *   说明: 扩展字段
     */
    private String ext;

    /**
     *   字段: add_time
     */
    private Date addTime;

    /**
     *   字段: update_time
     */
    private Date updateTime;
}