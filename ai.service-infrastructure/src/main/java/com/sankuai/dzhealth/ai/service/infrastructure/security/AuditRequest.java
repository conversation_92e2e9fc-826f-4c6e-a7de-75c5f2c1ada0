package com.sankuai.dzhealth.ai.service.infrastructure.security;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.time.DateFormatUtils;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> youtianyu
 * @version : 0.1
 * @since : 2025/3/31
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TypeDoc(name = "保时洁审核请求", description = "https://km.sankuai.com/collabpage/**********")
public class AuditRequest {
    private Integer type;

    private String transId;

    private Long bizId;

    private List<TextBody> textBody;

    private Long userId;

    private String userIP;

    private String assistantId;

    @FieldDoc(name = "用户来源", description = "1:美团平台C端,2点评平台C端")
    private Integer userSource;

    @FieldDoc(name = "数据来源", description = "2:C端用户,9:算法生成")
    private Integer dataSource;

    private Long shopId;

    private Integer shopSource;

    @Builder.Default()
    private String datetime = DateFormatUtils.format(new Date(), "yyyy-MM-dd HH:mm:ss");

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TextBody {

        private String name;

        private String value;


        @FieldDoc(name = "value的类型", description = "USER:用户生成文本,AIGCText:算法生成文本")
        private String desc;
    }
}
