package com.sankuai.dzhealth.ai.service.infrastructure.dal.entity;

import java.util.Date;
import lombok.*;

/**
 *
 *   表名: chat_message
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ChatMessageEntity {
    /**
     *   字段: id
     *   说明: 主键
     */
    private Long id;

    /**
     *   字段: message_id
     *   说明: 消息唯一标识
     */
    private String messageId;

    /**
     *   字段: conversation_id
     *   说明: 会话ID
     */
    private String conversationId;

    /**
     *   字段: role
     *   说明: 角色
     */
    private String role;

    /**
     *   字段: sender_id
     *   说明: 发送者ID
     */
    private String senderId;

    /**
     *   字段: content_type
     *   说明: 内容类型
     */
    private Byte contentType;

    /**
     *   字段: ttft
     *   说明: 首字输出时间(ms)
     */
    private Long ttft;

    /**
     *   字段: e2e_time
     *   说明: 端到端耗时(ms)
     */
    private Long e2eTime;

    /**
     *   字段: parent_message_id
     *   说明: 父消息ID
     */
    private String parentMessageId;

    /**
     *   字段: sequence
     *   说明: 消息序号
     */
    private Long sequence;

    /**
     *   字段: audit_status
     *   说明: 审核状态
     */
    private Byte auditStatus;

    /**
     *   字段: extra_data
     *   说明: 扩展数据
     */
    private String extraData;

    /**
     *   字段: created_at
     */
    private Date createdAt;

    /**
     *   字段: content
     *   说明: 消息内容
     */
    private String content;
}