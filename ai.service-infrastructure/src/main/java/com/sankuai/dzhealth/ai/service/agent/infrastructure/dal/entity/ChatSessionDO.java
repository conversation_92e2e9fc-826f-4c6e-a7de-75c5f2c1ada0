package com.sankuai.dzhealth.ai.service.agent.infrastructure.dal.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.Date;

/**
 *
 *   表名: chat_session
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
public class ChatSessionDO {
    /**
     *   字段: id
     *   说明: 主键
     */
    private Long id;

    /**
     *   字段: session_id
     *   说明: 会话ID
     */
    private String sessionId;

    /**
     *   字段: biz_type
     *   说明: 业务场景
     */
    private String bizType;

    /**
     *   字段: biz_id
     *   说明: 关联业务ID
     */
    private String bizId;

    /**
     *   字段: title
     *   说明: 会话标题
     */
    private String title;

    /**
     *   字段: digest
     *   说明: 会话总结摘要
     */
    private String digest;

    /**
     *   字段: user_id
     *   说明: 用户id
     */
    private Long userId;

    /**
     *   字段: platform
     *   说明: 平台
     */
    private Integer platform;

    /**
     *   字段: status
     *   说明: 状态(0:正常 1:删除)
     */
    private Integer status;

    /**
     *   字段: create_time
     *   说明: 创建时间
     */
    private Date createTime;

    /**
     *   字段: update_time
     *   说明: 创建时间
     */
    private Date updateTime;
}