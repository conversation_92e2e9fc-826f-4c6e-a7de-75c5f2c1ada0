package com.sankuai.dzhealth.ai.service.agent.infrastructure.proxy.product;

import com.google.common.collect.Sets;
import com.meituan.mdp.boot.starter.thrift.annotation.MdpThriftClient;
import com.sankuai.general.product.query.center.client.builder.model.DealGroupBasicInfoBuilder;
import com.sankuai.general.product.query.center.client.builder.model.DealGroupCategoryBuilder;
import com.sankuai.general.product.query.center.client.builder.model.DealGroupTagBuilder;
import com.sankuai.general.product.query.center.client.builder.requset.QueryByDealGroupIdRequestBuilder;
import com.sankuai.general.product.query.center.client.enums.AttrSubjectEnum;
import com.sankuai.general.product.query.center.client.enums.IdTypeEnum;
import com.sankuai.general.product.query.center.client.request.QueryByDealGroupIdRequest;
import com.sankuai.general.product.query.center.client.response.QueryDealGroupListResponse;
import com.sankuai.general.product.query.center.client.service.DealGroupQueryService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 商品查询中心RPC调用代理
 * 纯粹的基础设施层代理，只负责封装RPC调用，不包含业务逻辑
 */
@Slf4j
@Service
public class ProductQueryProxy {

    @MdpThriftClient(timeout = 300, testTimeout = 5000, remoteAppKey = "com.sankuai.productuser.query.center", async = false)
    private DealGroupQueryService dealGroupQueryService;

    /**
     * 查询团单商品信息（点评/美团团购商品）
     * 适用于IdTypeEnum.DP和IdTypeEnum.MT
     */
    public QueryDealGroupListResponse queryDealGroupInfo(Long productId, IdTypeEnum idType) {
        try {
            QueryByDealGroupIdRequest request = QueryByDealGroupIdRequestBuilder.builder()
                    .dealGroupIds(Sets.newHashSet(productId), idType)
                    .basicInfo(DealGroupBasicInfoBuilder.builder().all())  // 添加基本信息查询
                    .dealGroupTag(DealGroupTagBuilder.builder().all())
                    .category(DealGroupCategoryBuilder.builder().categoryId().serviceTypeId())
                    .build();

            return dealGroupQueryService.queryByDealGroupIds(request);
        } catch (Exception e) {
            log.error("queryDealGroupInfo exception, productId={}, idType={}", productId, idType, e);
            return null;
        }
    }

    /**
     * 查询泛商品信息（包含扩展属性）
     * 适用于IdTypeEnum.BIZ_PRODUCT
     */
    public QueryDealGroupListResponse queryBizProductInfo(Long productId) {
        try {
            QueryByDealGroupIdRequest request = QueryByDealGroupIdRequestBuilder.builder()
                    .dealGroupIds(Sets.newHashSet(productId), IdTypeEnum.BIZ_PRODUCT)
                    .basicInfo(DealGroupBasicInfoBuilder.builder().all())  // 添加基本信息查询
                    .dealGroupTag(DealGroupTagBuilder.builder().all())
                    .category(DealGroupCategoryBuilder.builder().categoryId().serviceTypeId())
                    .attrsByKey(AttrSubjectEnum.DEAL_GROUP, "category3", "category4", "category5")
                    .build();

            return dealGroupQueryService.queryByDealGroupIds(request);
        } catch (Exception e) {
            log.error("queryBizProductInfo exception, productId={}", productId, e);
            return null;
        }
    }

}

