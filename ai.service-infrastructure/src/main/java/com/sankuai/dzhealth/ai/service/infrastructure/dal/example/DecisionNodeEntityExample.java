package com.sankuai.dzhealth.ai.service.infrastructure.dal.example;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class DecisionNodeEntityExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public DecisionNodeEntityExample() {
        oredCriteria = new ArrayList<>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andBizSceneIsNull() {
            addCriterion("biz_scene is null");
            return (Criteria) this;
        }

        public Criteria andBizSceneIsNotNull() {
            addCriterion("biz_scene is not null");
            return (Criteria) this;
        }

        public Criteria andBizSceneEqualTo(String value) {
            addCriterion("biz_scene =", value, "bizScene");
            return (Criteria) this;
        }

        public Criteria andBizSceneNotEqualTo(String value) {
            addCriterion("biz_scene <>", value, "bizScene");
            return (Criteria) this;
        }

        public Criteria andBizSceneGreaterThan(String value) {
            addCriterion("biz_scene >", value, "bizScene");
            return (Criteria) this;
        }

        public Criteria andBizSceneGreaterThanOrEqualTo(String value) {
            addCriterion("biz_scene >=", value, "bizScene");
            return (Criteria) this;
        }

        public Criteria andBizSceneLessThan(String value) {
            addCriterion("biz_scene <", value, "bizScene");
            return (Criteria) this;
        }

        public Criteria andBizSceneLessThanOrEqualTo(String value) {
            addCriterion("biz_scene <=", value, "bizScene");
            return (Criteria) this;
        }

        public Criteria andBizSceneLike(String value) {
            addCriterion("biz_scene like", value, "bizScene");
            return (Criteria) this;
        }

        public Criteria andBizSceneNotLike(String value) {
            addCriterion("biz_scene not like", value, "bizScene");
            return (Criteria) this;
        }

        public Criteria andBizSceneIn(List<String> values) {
            addCriterion("biz_scene in", values, "bizScene");
            return (Criteria) this;
        }

        public Criteria andBizSceneNotIn(List<String> values) {
            addCriterion("biz_scene not in", values, "bizScene");
            return (Criteria) this;
        }

        public Criteria andBizSceneBetween(String value1, String value2) {
            addCriterion("biz_scene between", value1, value2, "bizScene");
            return (Criteria) this;
        }

        public Criteria andBizSceneNotBetween(String value1, String value2) {
            addCriterion("biz_scene not between", value1, value2, "bizScene");
            return (Criteria) this;
        }

        public Criteria andNodeIdIsNull() {
            addCriterion("node_id is null");
            return (Criteria) this;
        }

        public Criteria andNodeIdIsNotNull() {
            addCriterion("node_id is not null");
            return (Criteria) this;
        }

        public Criteria andNodeIdEqualTo(String value) {
            addCriterion("node_id =", value, "nodeId");
            return (Criteria) this;
        }

        public Criteria andNodeIdNotEqualTo(String value) {
            addCriterion("node_id <>", value, "nodeId");
            return (Criteria) this;
        }

        public Criteria andNodeIdGreaterThan(String value) {
            addCriterion("node_id >", value, "nodeId");
            return (Criteria) this;
        }

        public Criteria andNodeIdGreaterThanOrEqualTo(String value) {
            addCriterion("node_id >=", value, "nodeId");
            return (Criteria) this;
        }

        public Criteria andNodeIdLessThan(String value) {
            addCriterion("node_id <", value, "nodeId");
            return (Criteria) this;
        }

        public Criteria andNodeIdLessThanOrEqualTo(String value) {
            addCriterion("node_id <=", value, "nodeId");
            return (Criteria) this;
        }

        public Criteria andNodeIdLike(String value) {
            addCriterion("node_id like", value, "nodeId");
            return (Criteria) this;
        }

        public Criteria andNodeIdNotLike(String value) {
            addCriterion("node_id not like", value, "nodeId");
            return (Criteria) this;
        }

        public Criteria andNodeIdIn(List<String> values) {
            addCriterion("node_id in", values, "nodeId");
            return (Criteria) this;
        }

        public Criteria andNodeIdNotIn(List<String> values) {
            addCriterion("node_id not in", values, "nodeId");
            return (Criteria) this;
        }

        public Criteria andNodeIdBetween(String value1, String value2) {
            addCriterion("node_id between", value1, value2, "nodeId");
            return (Criteria) this;
        }

        public Criteria andNodeIdNotBetween(String value1, String value2) {
            addCriterion("node_id not between", value1, value2, "nodeId");
            return (Criteria) this;
        }

        public Criteria andNodeNameIsNull() {
            addCriterion("node_name is null");
            return (Criteria) this;
        }

        public Criteria andNodeNameIsNotNull() {
            addCriterion("node_name is not null");
            return (Criteria) this;
        }

        public Criteria andNodeNameEqualTo(String value) {
            addCriterion("node_name =", value, "nodeName");
            return (Criteria) this;
        }

        public Criteria andNodeNameNotEqualTo(String value) {
            addCriterion("node_name <>", value, "nodeName");
            return (Criteria) this;
        }

        public Criteria andNodeNameGreaterThan(String value) {
            addCriterion("node_name >", value, "nodeName");
            return (Criteria) this;
        }

        public Criteria andNodeNameGreaterThanOrEqualTo(String value) {
            addCriterion("node_name >=", value, "nodeName");
            return (Criteria) this;
        }

        public Criteria andNodeNameLessThan(String value) {
            addCriterion("node_name <", value, "nodeName");
            return (Criteria) this;
        }

        public Criteria andNodeNameLessThanOrEqualTo(String value) {
            addCriterion("node_name <=", value, "nodeName");
            return (Criteria) this;
        }

        public Criteria andNodeNameLike(String value) {
            addCriterion("node_name like", value, "nodeName");
            return (Criteria) this;
        }

        public Criteria andNodeNameNotLike(String value) {
            addCriterion("node_name not like", value, "nodeName");
            return (Criteria) this;
        }

        public Criteria andNodeNameIn(List<String> values) {
            addCriterion("node_name in", values, "nodeName");
            return (Criteria) this;
        }

        public Criteria andNodeNameNotIn(List<String> values) {
            addCriterion("node_name not in", values, "nodeName");
            return (Criteria) this;
        }

        public Criteria andNodeNameBetween(String value1, String value2) {
            addCriterion("node_name between", value1, value2, "nodeName");
            return (Criteria) this;
        }

        public Criteria andNodeNameNotBetween(String value1, String value2) {
            addCriterion("node_name not between", value1, value2, "nodeName");
            return (Criteria) this;
        }

        public Criteria andAssessmentTextIsNull() {
            addCriterion("assessment_text is null");
            return (Criteria) this;
        }

        public Criteria andAssessmentTextIsNotNull() {
            addCriterion("assessment_text is not null");
            return (Criteria) this;
        }

        public Criteria andAssessmentTextEqualTo(String value) {
            addCriterion("assessment_text =", value, "assessmentText");
            return (Criteria) this;
        }

        public Criteria andAssessmentTextNotEqualTo(String value) {
            addCriterion("assessment_text <>", value, "assessmentText");
            return (Criteria) this;
        }

        public Criteria andAssessmentTextGreaterThan(String value) {
            addCriterion("assessment_text >", value, "assessmentText");
            return (Criteria) this;
        }

        public Criteria andAssessmentTextGreaterThanOrEqualTo(String value) {
            addCriterion("assessment_text >=", value, "assessmentText");
            return (Criteria) this;
        }

        public Criteria andAssessmentTextLessThan(String value) {
            addCriterion("assessment_text <", value, "assessmentText");
            return (Criteria) this;
        }

        public Criteria andAssessmentTextLessThanOrEqualTo(String value) {
            addCriterion("assessment_text <=", value, "assessmentText");
            return (Criteria) this;
        }

        public Criteria andAssessmentTextLike(String value) {
            addCriterion("assessment_text like", value, "assessmentText");
            return (Criteria) this;
        }

        public Criteria andAssessmentTextNotLike(String value) {
            addCriterion("assessment_text not like", value, "assessmentText");
            return (Criteria) this;
        }

        public Criteria andAssessmentTextIn(List<String> values) {
            addCriterion("assessment_text in", values, "assessmentText");
            return (Criteria) this;
        }

        public Criteria andAssessmentTextNotIn(List<String> values) {
            addCriterion("assessment_text not in", values, "assessmentText");
            return (Criteria) this;
        }

        public Criteria andAssessmentTextBetween(String value1, String value2) {
            addCriterion("assessment_text between", value1, value2, "assessmentText");
            return (Criteria) this;
        }

        public Criteria andAssessmentTextNotBetween(String value1, String value2) {
            addCriterion("assessment_text not between", value1, value2, "assessmentText");
            return (Criteria) this;
        }

        public Criteria andAssessmentImgIsNull() {
            addCriterion("assessment_img is null");
            return (Criteria) this;
        }

        public Criteria andAssessmentImgIsNotNull() {
            addCriterion("assessment_img is not null");
            return (Criteria) this;
        }

        public Criteria andAssessmentImgEqualTo(String value) {
            addCriterion("assessment_img =", value, "assessmentImg");
            return (Criteria) this;
        }

        public Criteria andAssessmentImgNotEqualTo(String value) {
            addCriterion("assessment_img <>", value, "assessmentImg");
            return (Criteria) this;
        }

        public Criteria andAssessmentImgGreaterThan(String value) {
            addCriterion("assessment_img >", value, "assessmentImg");
            return (Criteria) this;
        }

        public Criteria andAssessmentImgGreaterThanOrEqualTo(String value) {
            addCriterion("assessment_img >=", value, "assessmentImg");
            return (Criteria) this;
        }

        public Criteria andAssessmentImgLessThan(String value) {
            addCriterion("assessment_img <", value, "assessmentImg");
            return (Criteria) this;
        }

        public Criteria andAssessmentImgLessThanOrEqualTo(String value) {
            addCriterion("assessment_img <=", value, "assessmentImg");
            return (Criteria) this;
        }

        public Criteria andAssessmentImgLike(String value) {
            addCriterion("assessment_img like", value, "assessmentImg");
            return (Criteria) this;
        }

        public Criteria andAssessmentImgNotLike(String value) {
            addCriterion("assessment_img not like", value, "assessmentImg");
            return (Criteria) this;
        }

        public Criteria andAssessmentImgIn(List<String> values) {
            addCriterion("assessment_img in", values, "assessmentImg");
            return (Criteria) this;
        }

        public Criteria andAssessmentImgNotIn(List<String> values) {
            addCriterion("assessment_img not in", values, "assessmentImg");
            return (Criteria) this;
        }

        public Criteria andAssessmentImgBetween(String value1, String value2) {
            addCriterion("assessment_img between", value1, value2, "assessmentImg");
            return (Criteria) this;
        }

        public Criteria andAssessmentImgNotBetween(String value1, String value2) {
            addCriterion("assessment_img not between", value1, value2, "assessmentImg");
            return (Criteria) this;
        }

        public Criteria andGuidanceTextIsNull() {
            addCriterion("guidance_text is null");
            return (Criteria) this;
        }

        public Criteria andGuidanceTextIsNotNull() {
            addCriterion("guidance_text is not null");
            return (Criteria) this;
        }

        public Criteria andGuidanceTextEqualTo(String value) {
            addCriterion("guidance_text =", value, "guidanceText");
            return (Criteria) this;
        }

        public Criteria andGuidanceTextNotEqualTo(String value) {
            addCriterion("guidance_text <>", value, "guidanceText");
            return (Criteria) this;
        }

        public Criteria andGuidanceTextGreaterThan(String value) {
            addCriterion("guidance_text >", value, "guidanceText");
            return (Criteria) this;
        }

        public Criteria andGuidanceTextGreaterThanOrEqualTo(String value) {
            addCriterion("guidance_text >=", value, "guidanceText");
            return (Criteria) this;
        }

        public Criteria andGuidanceTextLessThan(String value) {
            addCriterion("guidance_text <", value, "guidanceText");
            return (Criteria) this;
        }

        public Criteria andGuidanceTextLessThanOrEqualTo(String value) {
            addCriterion("guidance_text <=", value, "guidanceText");
            return (Criteria) this;
        }

        public Criteria andGuidanceTextLike(String value) {
            addCriterion("guidance_text like", value, "guidanceText");
            return (Criteria) this;
        }

        public Criteria andGuidanceTextNotLike(String value) {
            addCriterion("guidance_text not like", value, "guidanceText");
            return (Criteria) this;
        }

        public Criteria andGuidanceTextIn(List<String> values) {
            addCriterion("guidance_text in", values, "guidanceText");
            return (Criteria) this;
        }

        public Criteria andGuidanceTextNotIn(List<String> values) {
            addCriterion("guidance_text not in", values, "guidanceText");
            return (Criteria) this;
        }

        public Criteria andGuidanceTextBetween(String value1, String value2) {
            addCriterion("guidance_text between", value1, value2, "guidanceText");
            return (Criteria) this;
        }

        public Criteria andGuidanceTextNotBetween(String value1, String value2) {
            addCriterion("guidance_text not between", value1, value2, "guidanceText");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("status is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("status is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(String value) {
            addCriterion("status =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(String value) {
            addCriterion("status <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(String value) {
            addCriterion("status >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(String value) {
            addCriterion("status >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(String value) {
            addCriterion("status <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(String value) {
            addCriterion("status <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLike(String value) {
            addCriterion("status like", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotLike(String value) {
            addCriterion("status not like", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<String> values) {
            addCriterion("status in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<String> values) {
            addCriterion("status not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(String value1, String value2) {
            addCriterion("status between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(String value1, String value2) {
            addCriterion("status not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andExtIsNull() {
            addCriterion("ext is null");
            return (Criteria) this;
        }

        public Criteria andExtIsNotNull() {
            addCriterion("ext is not null");
            return (Criteria) this;
        }

        public Criteria andExtEqualTo(String value) {
            addCriterion("ext =", value, "ext");
            return (Criteria) this;
        }

        public Criteria andExtNotEqualTo(String value) {
            addCriterion("ext <>", value, "ext");
            return (Criteria) this;
        }

        public Criteria andExtGreaterThan(String value) {
            addCriterion("ext >", value, "ext");
            return (Criteria) this;
        }

        public Criteria andExtGreaterThanOrEqualTo(String value) {
            addCriterion("ext >=", value, "ext");
            return (Criteria) this;
        }

        public Criteria andExtLessThan(String value) {
            addCriterion("ext <", value, "ext");
            return (Criteria) this;
        }

        public Criteria andExtLessThanOrEqualTo(String value) {
            addCriterion("ext <=", value, "ext");
            return (Criteria) this;
        }

        public Criteria andExtLike(String value) {
            addCriterion("ext like", value, "ext");
            return (Criteria) this;
        }

        public Criteria andExtNotLike(String value) {
            addCriterion("ext not like", value, "ext");
            return (Criteria) this;
        }

        public Criteria andExtIn(List<String> values) {
            addCriterion("ext in", values, "ext");
            return (Criteria) this;
        }

        public Criteria andExtNotIn(List<String> values) {
            addCriterion("ext not in", values, "ext");
            return (Criteria) this;
        }

        public Criteria andExtBetween(String value1, String value2) {
            addCriterion("ext between", value1, value2, "ext");
            return (Criteria) this;
        }

        public Criteria andExtNotBetween(String value1, String value2) {
            addCriterion("ext not between", value1, value2, "ext");
            return (Criteria) this;
        }

        public Criteria andAddTimeIsNull() {
            addCriterion("add_time is null");
            return (Criteria) this;
        }

        public Criteria andAddTimeIsNotNull() {
            addCriterion("add_time is not null");
            return (Criteria) this;
        }

        public Criteria andAddTimeEqualTo(Date value) {
            addCriterion("add_time =", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotEqualTo(Date value) {
            addCriterion("add_time <>", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeGreaterThan(Date value) {
            addCriterion("add_time >", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("add_time >=", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeLessThan(Date value) {
            addCriterion("add_time <", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeLessThanOrEqualTo(Date value) {
            addCriterion("add_time <=", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeIn(List<Date> values) {
            addCriterion("add_time in", values, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotIn(List<Date> values) {
            addCriterion("add_time not in", values, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeBetween(Date value1, Date value2) {
            addCriterion("add_time between", value1, value2, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotBetween(Date value1, Date value2) {
            addCriterion("add_time not between", value1, value2, "addTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}