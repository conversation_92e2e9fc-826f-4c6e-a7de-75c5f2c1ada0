package com.sankuai.dzhealth.ai.service.agent.infrastructure.proxy.avoidguide;

import com.meituan.mdp.boot.starter.pigeon.annotation.MdpPigeonClient;
import com.sankuai.beautycontent.beautylaunchapi.guide.dto.GuideModel;
import com.sankuai.beautycontent.beautylaunchapi.guide.request.AvoidGuideQuery;
import com.sankuai.beautycontent.beautylaunchapi.guide.service.AvoidGuideAggregateService;
import com.sankuai.beautycontent.beautylaunchapi.model.dto.RemoteResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * 基于 launch-api 的 AvoidGuideAggregateService 封装。
 * 仅聚焦商品详情页（source=product）场景，按商品标签/类目获取对应指南。
 */
@Slf4j
@Service
public class GuideAggregateProxy {

    @MdpPigeonClient(url = "com.sankuai.beautycontent.beautylaunchapi.guide.service.AvoidGuideAggregateService", timeout = 3000L)
    private AvoidGuideAggregateService aggregateService;

    /**
     * 直接透传查询，保持 Anti-Corruption：不做任何业务字段拼接。
     */
    public List<GuideModel> queryGuide(AvoidGuideQuery query) {
        try {
            RemoteResponse<List<GuideModel>> response = aggregateService.queryGuide(query);
            if (response != null && response.isSuccess() && response.getData() != null) {
                return response.getData();
            }
            log.warn("AvoidGuideAggregateService.queryGuide 返回异常, query={}, resp={}", query, response);
        } catch (Exception e) {
            log.error("调用 AvoidGuideAggregateService 失败, query={}", query, e);
        }
        return Collections.emptyList();
    }
} 