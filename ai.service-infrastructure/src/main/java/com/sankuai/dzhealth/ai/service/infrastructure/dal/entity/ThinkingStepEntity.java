package com.sankuai.dzhealth.ai.service.infrastructure.dal.entity;

import java.math.BigDecimal;
import java.util.Date;
import lombok.*;

/**
 *
 *   表名: thinking_steps
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ThinkingStepEntity {
    /**
     *   字段: id
     *   说明: 步骤ID
     */
    private Long id;

    /**
     *   字段: session_id
     *   说明: 关联会话ID
     */
    private Long sessionId;

    /**
     *   字段: step_number
     *   说明: 步骤序号
     */
    private Long stepNumber;

    /**
     *   字段: total_steps
     *   说明: 总步骤数
     */
    private Long totalSteps;

    /**
     *   字段: thought
     *   说明: 思考内容
     */
    private String thought;

    /**
     *   字段: search_results
     *   说明: 步骤搜索结果
     */
    private String searchResults;

    /**
     *   字段: is_revision
     *   说明: 是否修订步骤（0否 1是）
     */
    private Boolean isRevision;

    /**
     *   字段: revises_step_number
     *   说明: 修订目标步骤
     */
    private Long revisesStepNumber;

    /**
     *   字段: next_thought_needed
     *   说明: 需后续思考（0否 1是）
     */
    private Boolean nextThoughtNeeded;

    /**
     *   字段: branch_from_step
     *   说明: 分支来源步骤ID
     */
    private Long branchFromStep;

    /**
     *   字段: branch_id
     *   说明: 分支标识符
     */
    private String branchId;

    /**
     *   字段: confidence_score
     *   说明: 步骤置信度
     */
    private BigDecimal confidenceScore;

    /**
     *   字段: add_time
     *   说明: 创建时间
     */
    private Date addTime;

    /**
     *   字段: update_time
     *   说明: 更新时间
     */
    private Date updateTime;
}