package com.sankuai.dzhealth.ai.service.infrastructure.vectorstore.config;

import com.dianping.lion.Environment;
import com.knuddels.jtokkit.api.EncodingType;
import com.meituan.service.inf.kms.client.Kms;
import com.meituan.service.inf.kms.utils.KmsResultNullException;
import com.sankuai.dzhealth.ai.service.infrastructure.chatmodel.MeituanEmbeddingModel;
import com.sankuai.dzhealth.ai.service.infrastructure.vectorstore.HybirdElasticsearchVectorStore;
import com.sankuai.meituan.poros.client.PorosApiClient;
import com.sankuai.meituan.poros.client.PorosApiClientBuilder;
import org.springframework.ai.document.MetadataMode;
import org.springframework.ai.embedding.TokenCountBatchingStrategy;
import org.springframework.ai.openai.OpenAiEmbeddingOptions;
import org.springframework.ai.openai.api.OpenAiApi;
import org.springframework.ai.retry.RetryUtils;
import org.springframework.ai.vectorstore.VectorStore;
import org.springframework.ai.vectorstore.elasticsearch.ElasticsearchVectorStoreOptions;
import org.springframework.ai.vectorstore.elasticsearch.SimilarityFunction;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR> youtianyu
 * @version : 0.1
 * @since : 2025/3/14
 */
@Configuration
public class EsVectorStoreConfig {

    public static final String INDEX_NAME = "rag_768";
    public static final String DECISION_FLOW_INDEX_NAME = "medicine_decision_flow";

    @Bean
    public MeituanEmbeddingModel embeddingModel() throws KmsResultNullException {
        OpenAiApi openAiApi = new OpenAiApi.Builder().embeddingsPath("/embeddings")
                .baseUrl("https://aigc.sankuai.com/v1/openai/native")
                .apiKey(Kms.getByName(Environment.getAppName(), "friday.search.appId"))
                .build();

        return new MeituanEmbeddingModel(openAiApi, MetadataMode.EMBED,
                OpenAiEmbeddingOptions.builder().model("text-embedding-miffy-002").build(),
                RetryUtils.DEFAULT_RETRY_TEMPLATE);
    }

    @Bean
    public PorosApiClient restClient() {
        return PorosApiClientBuilder.builder()
                .clusterName(Environment.isTestEnv() ? "dzu_medicalrag_default" : "dzu_eaglenode-es-medicalrag_default")
                .appKey(Environment.getAppName())
                .timeoutMillis(10_000) //全局超时时间。单位ms
                .build();
    }

    @Bean
    public VectorStore esVectorStore(PorosApiClient restClient, MeituanEmbeddingModel embeddingModel) {
        ElasticsearchVectorStoreOptions options = new ElasticsearchVectorStoreOptions();
        options.setIndexName(INDEX_NAME);    // Optional: defaults to "spring-ai-document-index"
        options.setSimilarity(SimilarityFunction.dot_product);           // Optional: defaults to COSINE
        options.setDimensions(768);             // Optional: defaults to model dimensions or 1536

        return HybirdElasticsearchVectorStore.builder(restClient.getRestClient(), embeddingModel)
                .options(options)                     // Optional: use custom options
                .initializeSchema(false)               // Optional: defaults to false
                .batchingStrategy(new TokenCountBatchingStrategy(EncodingType.CL100K_BASE,  // Specify the encoding type
                        2048,                      // Set the maximum input token count
                        0.1)) // Optional: defaults to TokenCountBatchingStrategy
                .build();
    }

    @Bean
    public VectorStore decisionFlowVectorStore(PorosApiClient restClient, MeituanEmbeddingModel embeddingModel) {
        ElasticsearchVectorStoreOptions options = new ElasticsearchVectorStoreOptions();
        options.setIndexName(DECISION_FLOW_INDEX_NAME);    // 使用决策流专用索引名
        options.setSimilarity(SimilarityFunction.dot_product);           // Optional: defaults to COSINE
        options.setDimensions(768);             // Optional: defaults to model dimensions or 1536

        return HybirdElasticsearchVectorStore.builder(restClient.getRestClient(), embeddingModel)
                .options(options)                     // Optional: use custom options
                .initializeSchema(false)               // Optional: defaults to false
                .batchingStrategy(new TokenCountBatchingStrategy(EncodingType.CL100K_BASE,  // Specify the encoding type
                        2048,                      // Set the maximum input token count
                        0.1)) // Optional: defaults to TokenCountBatchingStrategy
                .build();
    }
}
