package com.sankuai.dzhealth.ai.service.infrastructure.repository.decision;

import com.sankuai.dzhealth.ai.service.enums.DecisionFlowElementStatusEnum;
import com.sankuai.dzhealth.ai.service.enums.DocTypeEnum;
import com.sankuai.dzhealth.ai.service.enums.MetadataKeyEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.springframework.ai.document.Document;
import org.springframework.ai.vectorstore.SearchRequest;
import org.springframework.ai.vectorstore.VectorStore;
import org.springframework.ai.vectorstore.filter.FilterExpressionBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 决策网络向量检索仓储（基础设施层）
 * 1. 将节点、资源写入 ES 向量索引
 * 2. 根据查询语句检索匹配节点/资源
 */
@Repository
public class DecisionVectorStoreRepository {

    @Autowired
    @Qualifier("decisionFlowVectorStore")
    private VectorStore vectorStore;

    /* ---------- 写入 ---------- */
    public void indexNodes(List<NodeIndexData> nodes) {
        if (nodes == null || nodes.isEmpty()) return;
        List<Document> docs = nodes.stream()
                .map(n -> new Document.Builder()
                        .id(n.getNodeId())
                        .text(n.getConcatText())
                        .metadata(Map.of(
                                MetadataKeyEnum.DOC_TYPE.getKey(), DocTypeEnum.NODE.code(),
                                MetadataKeyEnum.BIZ_SCENE.getKey(), n.getBizScene(),
                                MetadataKeyEnum.STATUS.getKey(), n.getStatus()
                        ))
                        .build())
                .collect(Collectors.toList());
        vectorStore.add(docs);
    }

    public void indexResources(List<ResourceIndexData> resources) {
        if (resources == null || resources.isEmpty()) return;
        List<Document> docs = resources.stream()
                .map(r -> new Document.Builder()
                        .id(r.getResourceId())
                        .text(r.getConcatText())
                        .metadata(Map.of(
                                MetadataKeyEnum.DOC_TYPE.getKey(), DocTypeEnum.RESOURCE.code(),
                                MetadataKeyEnum.BIZ_SCENE.getKey(), r.getBizScene(),
                                MetadataKeyEnum.RESOURCE_TYPE.getKey(), r.getResourceType(),
                                MetadataKeyEnum.STATUS.getKey(), r.getStatus()
                        ))
                        .build())
                .collect(Collectors.toList());
        vectorStore.add(docs);
    }

    /* ---------- 检索：节点 ---------- */

    // 旧方法兼容：仅查 ONLINE
    public List<String> searchNodeIds(String bizScene, String query, int topK) {
        return searchNodeIds(bizScene, query, topK, Set.of(DecisionFlowElementStatusEnum.ONLINE));
    }

    // 灰度流量：ONLINE + GRAY
    public List<String> searchNodeIdsGray(String bizScene, String query, int topK) {
        return searchNodeIds(bizScene, query, topK,
                Set.of(DecisionFlowElementStatusEnum.ONLINE, DecisionFlowElementStatusEnum.GRAY));
    }

    // 可自定义 status 集合，但强制排除删除状态
    public List<String> searchNodeIds(String bizScene, String query, int topK, Set<DecisionFlowElementStatusEnum> statuses) {
        FilterExpressionBuilder.Op op = new FilterExpressionBuilder()
                .eq(MetadataKeyEnum.DOC_TYPE.getKey(), DocTypeEnum.NODE.code());
        op = new FilterExpressionBuilder().and(op,
                new FilterExpressionBuilder().eq(MetadataKeyEnum.BIZ_SCENE.getKey(), bizScene));

        // status IN (...) 但强制排除删除状态
        List<String> statusCodes = statuses.stream()
                .filter(status -> status != DecisionFlowElementStatusEnum.ONLINE_DELETE && status != DecisionFlowElementStatusEnum.GRAY_DELETE)
                .map(DecisionFlowElementStatusEnum::code)
                .toList();

        if (statusCodes.isEmpty()) {
            return List.of(); // 如果没有有效状态，返回空列表
        }

        // 根据状态值数量选择不同的过滤方法
        if (statusCodes.size() == 1) {
            op = new FilterExpressionBuilder().and(op,
                    new FilterExpressionBuilder().eq(MetadataKeyEnum.STATUS.getKey(), statusCodes.get(0)));
        } else {
            op = new FilterExpressionBuilder().and(op,
                    new FilterExpressionBuilder().in(MetadataKeyEnum.STATUS.getKey(), statusCodes));
        }

        List<Document> docs = vectorStore.similaritySearch(SearchRequest.builder()
                .query(query)
                .topK(topK)
                .similarityThresholdAll()
                .filterExpression(op.build())
                .build());
        return docs.stream().map(Document::getId).collect(Collectors.toList());
    }

    /* ---------- 检索：资源 ---------- */

    public List<String> searchResourceIds(String bizScene, String query, int topK) {
        return searchResourceIds(bizScene, query, topK, Set.of(DecisionFlowElementStatusEnum.ONLINE));
    }

    public List<String> searchResourceIdsGray(String bizScene, String query, int topK) {
        return searchResourceIds(bizScene, query, topK,
                Set.of(DecisionFlowElementStatusEnum.ONLINE, DecisionFlowElementStatusEnum.GRAY));
    }

    public List<String> searchResourceIds(String bizScene, String query, int topK, Set<DecisionFlowElementStatusEnum> statuses) {
        FilterExpressionBuilder.Op op = new FilterExpressionBuilder().eq(MetadataKeyEnum.DOC_TYPE.getKey(), DocTypeEnum.RESOURCE.code());
        op = new FilterExpressionBuilder().and(op, new FilterExpressionBuilder().eq(MetadataKeyEnum.BIZ_SCENE.getKey(), bizScene));

        // status IN (...) 但强制排除删除状态
        List<String> statusCodes = statuses.stream()
                .filter(status -> status != DecisionFlowElementStatusEnum.ONLINE_DELETE && status != DecisionFlowElementStatusEnum.GRAY_DELETE)
                .map(DecisionFlowElementStatusEnum::code)
                .toList();

        if (statusCodes.isEmpty()) {
            return List.of(); // 如果没有有效状态，返回空列表
        }

        // 根据状态值数量选择不同的过滤方法
        if (statusCodes.size() == 1) {
            op = new FilterExpressionBuilder().and(op,
                    new FilterExpressionBuilder().eq(MetadataKeyEnum.STATUS.getKey(), statusCodes.get(0)));
        } else {
            op = new FilterExpressionBuilder().and(op,
                    new FilterExpressionBuilder().in(MetadataKeyEnum.STATUS.getKey(), statusCodes));
        }

        List<Document> docs = vectorStore.similaritySearch(SearchRequest.builder()
                .query(query)
                .topK(topK)
                .similarityThresholdAll()
                .filterExpression(op.build())
                .build());
        return docs.stream().map(Document::getId).collect(Collectors.toList());
    }

    /* ---------- 删除 ---------- */
    public void deleteDocs(List<String> ids){
        if(ids==null||ids.isEmpty()) return;
        vectorStore.delete(ids);
    }

    /* ---------- 内部数据结构 ---------- */
    @Getter
    @AllArgsConstructor
    public static class NodeIndexData {
        private String bizScene;
        private String nodeId;
        private String concatText;
        private String status;
    }

    @Getter
    @AllArgsConstructor
    public static class ResourceIndexData {
        private String bizScene;
        private String resourceId;
        private String concatText;
        private String resourceType;
        private String status;
    }
} 