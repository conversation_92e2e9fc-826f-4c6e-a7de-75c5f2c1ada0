package com.sankuai.dzhealth.ai.service.agent.infrastructure.dal.mapper;

import com.meituan.mdp.mybatis.mapper.MybatisBLOBsMapper;
import com.sankuai.dzhealth.ai.service.agent.infrastructure.dal.entity.ChatSessionMessageDO;
import com.sankuai.dzhealth.ai.service.agent.infrastructure.dal.example.ChatSessionMessageDOExample;
import org.apache.ibatis.annotations.Param;

public interface ChatSessionMessageDOMapper extends MybatisBLOBsMapper<ChatSessionMessageDO, ChatSessionMessageDOExample, Long> {

    int updateByExampleSelective(@Param("row") ChatSessionMessageDO row, @Param("example") ChatSessionMessageDOExample example);
}