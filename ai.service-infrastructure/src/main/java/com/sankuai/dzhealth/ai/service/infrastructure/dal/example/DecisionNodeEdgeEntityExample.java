package com.sankuai.dzhealth.ai.service.infrastructure.dal.example;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class DecisionNodeEdgeEntityExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public DecisionNodeEdgeEntityExample() {
        oredCriteria = new ArrayList<>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andBizSceneIsNull() {
            addCriterion("biz_scene is null");
            return (Criteria) this;
        }

        public Criteria andBizSceneIsNotNull() {
            addCriterion("biz_scene is not null");
            return (Criteria) this;
        }

        public Criteria andBizSceneEqualTo(String value) {
            addCriterion("biz_scene =", value, "bizScene");
            return (Criteria) this;
        }

        public Criteria andBizSceneNotEqualTo(String value) {
            addCriterion("biz_scene <>", value, "bizScene");
            return (Criteria) this;
        }

        public Criteria andBizSceneGreaterThan(String value) {
            addCriterion("biz_scene >", value, "bizScene");
            return (Criteria) this;
        }

        public Criteria andBizSceneGreaterThanOrEqualTo(String value) {
            addCriterion("biz_scene >=", value, "bizScene");
            return (Criteria) this;
        }

        public Criteria andBizSceneLessThan(String value) {
            addCriterion("biz_scene <", value, "bizScene");
            return (Criteria) this;
        }

        public Criteria andBizSceneLessThanOrEqualTo(String value) {
            addCriterion("biz_scene <=", value, "bizScene");
            return (Criteria) this;
        }

        public Criteria andBizSceneLike(String value) {
            addCriterion("biz_scene like", value, "bizScene");
            return (Criteria) this;
        }

        public Criteria andBizSceneNotLike(String value) {
            addCriterion("biz_scene not like", value, "bizScene");
            return (Criteria) this;
        }

        public Criteria andBizSceneIn(List<String> values) {
            addCriterion("biz_scene in", values, "bizScene");
            return (Criteria) this;
        }

        public Criteria andBizSceneNotIn(List<String> values) {
            addCriterion("biz_scene not in", values, "bizScene");
            return (Criteria) this;
        }

        public Criteria andBizSceneBetween(String value1, String value2) {
            addCriterion("biz_scene between", value1, value2, "bizScene");
            return (Criteria) this;
        }

        public Criteria andBizSceneNotBetween(String value1, String value2) {
            addCriterion("biz_scene not between", value1, value2, "bizScene");
            return (Criteria) this;
        }

        public Criteria andEdgeIdIsNull() {
            addCriterion("edge_id is null");
            return (Criteria) this;
        }

        public Criteria andEdgeIdIsNotNull() {
            addCriterion("edge_id is not null");
            return (Criteria) this;
        }

        public Criteria andEdgeIdEqualTo(String value) {
            addCriterion("edge_id =", value, "edgeId");
            return (Criteria) this;
        }

        public Criteria andEdgeIdNotEqualTo(String value) {
            addCriterion("edge_id <>", value, "edgeId");
            return (Criteria) this;
        }

        public Criteria andEdgeIdGreaterThan(String value) {
            addCriterion("edge_id >", value, "edgeId");
            return (Criteria) this;
        }

        public Criteria andEdgeIdGreaterThanOrEqualTo(String value) {
            addCriterion("edge_id >=", value, "edgeId");
            return (Criteria) this;
        }

        public Criteria andEdgeIdLessThan(String value) {
            addCriterion("edge_id <", value, "edgeId");
            return (Criteria) this;
        }

        public Criteria andEdgeIdLessThanOrEqualTo(String value) {
            addCriterion("edge_id <=", value, "edgeId");
            return (Criteria) this;
        }

        public Criteria andEdgeIdLike(String value) {
            addCriterion("edge_id like", value, "edgeId");
            return (Criteria) this;
        }

        public Criteria andEdgeIdNotLike(String value) {
            addCriterion("edge_id not like", value, "edgeId");
            return (Criteria) this;
        }

        public Criteria andEdgeIdIn(List<String> values) {
            addCriterion("edge_id in", values, "edgeId");
            return (Criteria) this;
        }

        public Criteria andEdgeIdNotIn(List<String> values) {
            addCriterion("edge_id not in", values, "edgeId");
            return (Criteria) this;
        }

        public Criteria andEdgeIdBetween(String value1, String value2) {
            addCriterion("edge_id between", value1, value2, "edgeId");
            return (Criteria) this;
        }

        public Criteria andEdgeIdNotBetween(String value1, String value2) {
            addCriterion("edge_id not between", value1, value2, "edgeId");
            return (Criteria) this;
        }

        public Criteria andParentIdIsNull() {
            addCriterion("parent_id is null");
            return (Criteria) this;
        }

        public Criteria andParentIdIsNotNull() {
            addCriterion("parent_id is not null");
            return (Criteria) this;
        }

        public Criteria andParentIdEqualTo(String value) {
            addCriterion("parent_id =", value, "parentId");
            return (Criteria) this;
        }

        public Criteria andParentIdNotEqualTo(String value) {
            addCriterion("parent_id <>", value, "parentId");
            return (Criteria) this;
        }

        public Criteria andParentIdGreaterThan(String value) {
            addCriterion("parent_id >", value, "parentId");
            return (Criteria) this;
        }

        public Criteria andParentIdGreaterThanOrEqualTo(String value) {
            addCriterion("parent_id >=", value, "parentId");
            return (Criteria) this;
        }

        public Criteria andParentIdLessThan(String value) {
            addCriterion("parent_id <", value, "parentId");
            return (Criteria) this;
        }

        public Criteria andParentIdLessThanOrEqualTo(String value) {
            addCriterion("parent_id <=", value, "parentId");
            return (Criteria) this;
        }

        public Criteria andParentIdLike(String value) {
            addCriterion("parent_id like", value, "parentId");
            return (Criteria) this;
        }

        public Criteria andParentIdNotLike(String value) {
            addCriterion("parent_id not like", value, "parentId");
            return (Criteria) this;
        }

        public Criteria andParentIdIn(List<String> values) {
            addCriterion("parent_id in", values, "parentId");
            return (Criteria) this;
        }

        public Criteria andParentIdNotIn(List<String> values) {
            addCriterion("parent_id not in", values, "parentId");
            return (Criteria) this;
        }

        public Criteria andParentIdBetween(String value1, String value2) {
            addCriterion("parent_id between", value1, value2, "parentId");
            return (Criteria) this;
        }

        public Criteria andParentIdNotBetween(String value1, String value2) {
            addCriterion("parent_id not between", value1, value2, "parentId");
            return (Criteria) this;
        }

        public Criteria andChildIdIsNull() {
            addCriterion("child_id is null");
            return (Criteria) this;
        }

        public Criteria andChildIdIsNotNull() {
            addCriterion("child_id is not null");
            return (Criteria) this;
        }

        public Criteria andChildIdEqualTo(String value) {
            addCriterion("child_id =", value, "childId");
            return (Criteria) this;
        }

        public Criteria andChildIdNotEqualTo(String value) {
            addCriterion("child_id <>", value, "childId");
            return (Criteria) this;
        }

        public Criteria andChildIdGreaterThan(String value) {
            addCriterion("child_id >", value, "childId");
            return (Criteria) this;
        }

        public Criteria andChildIdGreaterThanOrEqualTo(String value) {
            addCriterion("child_id >=", value, "childId");
            return (Criteria) this;
        }

        public Criteria andChildIdLessThan(String value) {
            addCriterion("child_id <", value, "childId");
            return (Criteria) this;
        }

        public Criteria andChildIdLessThanOrEqualTo(String value) {
            addCriterion("child_id <=", value, "childId");
            return (Criteria) this;
        }

        public Criteria andChildIdLike(String value) {
            addCriterion("child_id like", value, "childId");
            return (Criteria) this;
        }

        public Criteria andChildIdNotLike(String value) {
            addCriterion("child_id not like", value, "childId");
            return (Criteria) this;
        }

        public Criteria andChildIdIn(List<String> values) {
            addCriterion("child_id in", values, "childId");
            return (Criteria) this;
        }

        public Criteria andChildIdNotIn(List<String> values) {
            addCriterion("child_id not in", values, "childId");
            return (Criteria) this;
        }

        public Criteria andChildIdBetween(String value1, String value2) {
            addCriterion("child_id between", value1, value2, "childId");
            return (Criteria) this;
        }

        public Criteria andChildIdNotBetween(String value1, String value2) {
            addCriterion("child_id not between", value1, value2, "childId");
            return (Criteria) this;
        }

        public Criteria andEdgeTypeIsNull() {
            addCriterion("edge_type is null");
            return (Criteria) this;
        }

        public Criteria andEdgeTypeIsNotNull() {
            addCriterion("edge_type is not null");
            return (Criteria) this;
        }

        public Criteria andEdgeTypeEqualTo(String value) {
            addCriterion("edge_type =", value, "edgeType");
            return (Criteria) this;
        }

        public Criteria andEdgeTypeNotEqualTo(String value) {
            addCriterion("edge_type <>", value, "edgeType");
            return (Criteria) this;
        }

        public Criteria andEdgeTypeGreaterThan(String value) {
            addCriterion("edge_type >", value, "edgeType");
            return (Criteria) this;
        }

        public Criteria andEdgeTypeGreaterThanOrEqualTo(String value) {
            addCriterion("edge_type >=", value, "edgeType");
            return (Criteria) this;
        }

        public Criteria andEdgeTypeLessThan(String value) {
            addCriterion("edge_type <", value, "edgeType");
            return (Criteria) this;
        }

        public Criteria andEdgeTypeLessThanOrEqualTo(String value) {
            addCriterion("edge_type <=", value, "edgeType");
            return (Criteria) this;
        }

        public Criteria andEdgeTypeLike(String value) {
            addCriterion("edge_type like", value, "edgeType");
            return (Criteria) this;
        }

        public Criteria andEdgeTypeNotLike(String value) {
            addCriterion("edge_type not like", value, "edgeType");
            return (Criteria) this;
        }

        public Criteria andEdgeTypeIn(List<String> values) {
            addCriterion("edge_type in", values, "edgeType");
            return (Criteria) this;
        }

        public Criteria andEdgeTypeNotIn(List<String> values) {
            addCriterion("edge_type not in", values, "edgeType");
            return (Criteria) this;
        }

        public Criteria andEdgeTypeBetween(String value1, String value2) {
            addCriterion("edge_type between", value1, value2, "edgeType");
            return (Criteria) this;
        }

        public Criteria andEdgeTypeNotBetween(String value1, String value2) {
            addCriterion("edge_type not between", value1, value2, "edgeType");
            return (Criteria) this;
        }

        public Criteria andEdgeDescIsNull() {
            addCriterion("edge_desc is null");
            return (Criteria) this;
        }

        public Criteria andEdgeDescIsNotNull() {
            addCriterion("edge_desc is not null");
            return (Criteria) this;
        }

        public Criteria andEdgeDescEqualTo(String value) {
            addCriterion("edge_desc =", value, "edgeDesc");
            return (Criteria) this;
        }

        public Criteria andEdgeDescNotEqualTo(String value) {
            addCriterion("edge_desc <>", value, "edgeDesc");
            return (Criteria) this;
        }

        public Criteria andEdgeDescGreaterThan(String value) {
            addCriterion("edge_desc >", value, "edgeDesc");
            return (Criteria) this;
        }

        public Criteria andEdgeDescGreaterThanOrEqualTo(String value) {
            addCriterion("edge_desc >=", value, "edgeDesc");
            return (Criteria) this;
        }

        public Criteria andEdgeDescLessThan(String value) {
            addCriterion("edge_desc <", value, "edgeDesc");
            return (Criteria) this;
        }

        public Criteria andEdgeDescLessThanOrEqualTo(String value) {
            addCriterion("edge_desc <=", value, "edgeDesc");
            return (Criteria) this;
        }

        public Criteria andEdgeDescLike(String value) {
            addCriterion("edge_desc like", value, "edgeDesc");
            return (Criteria) this;
        }

        public Criteria andEdgeDescNotLike(String value) {
            addCriterion("edge_desc not like", value, "edgeDesc");
            return (Criteria) this;
        }

        public Criteria andEdgeDescIn(List<String> values) {
            addCriterion("edge_desc in", values, "edgeDesc");
            return (Criteria) this;
        }

        public Criteria andEdgeDescNotIn(List<String> values) {
            addCriterion("edge_desc not in", values, "edgeDesc");
            return (Criteria) this;
        }

        public Criteria andEdgeDescBetween(String value1, String value2) {
            addCriterion("edge_desc between", value1, value2, "edgeDesc");
            return (Criteria) this;
        }

        public Criteria andEdgeDescNotBetween(String value1, String value2) {
            addCriterion("edge_desc not between", value1, value2, "edgeDesc");
            return (Criteria) this;
        }

        public Criteria andSortOrderIsNull() {
            addCriterion("sort_order is null");
            return (Criteria) this;
        }

        public Criteria andSortOrderIsNotNull() {
            addCriterion("sort_order is not null");
            return (Criteria) this;
        }

        public Criteria andSortOrderEqualTo(Long value) {
            addCriterion("sort_order =", value, "sortOrder");
            return (Criteria) this;
        }

        public Criteria andSortOrderNotEqualTo(Long value) {
            addCriterion("sort_order <>", value, "sortOrder");
            return (Criteria) this;
        }

        public Criteria andSortOrderGreaterThan(Long value) {
            addCriterion("sort_order >", value, "sortOrder");
            return (Criteria) this;
        }

        public Criteria andSortOrderGreaterThanOrEqualTo(Long value) {
            addCriterion("sort_order >=", value, "sortOrder");
            return (Criteria) this;
        }

        public Criteria andSortOrderLessThan(Long value) {
            addCriterion("sort_order <", value, "sortOrder");
            return (Criteria) this;
        }

        public Criteria andSortOrderLessThanOrEqualTo(Long value) {
            addCriterion("sort_order <=", value, "sortOrder");
            return (Criteria) this;
        }

        public Criteria andSortOrderIn(List<Long> values) {
            addCriterion("sort_order in", values, "sortOrder");
            return (Criteria) this;
        }

        public Criteria andSortOrderNotIn(List<Long> values) {
            addCriterion("sort_order not in", values, "sortOrder");
            return (Criteria) this;
        }

        public Criteria andSortOrderBetween(Long value1, Long value2) {
            addCriterion("sort_order between", value1, value2, "sortOrder");
            return (Criteria) this;
        }

        public Criteria andSortOrderNotBetween(Long value1, Long value2) {
            addCriterion("sort_order not between", value1, value2, "sortOrder");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("status is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("status is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(String value) {
            addCriterion("status =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(String value) {
            addCriterion("status <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(String value) {
            addCriterion("status >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(String value) {
            addCriterion("status >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(String value) {
            addCriterion("status <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(String value) {
            addCriterion("status <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLike(String value) {
            addCriterion("status like", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotLike(String value) {
            addCriterion("status not like", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<String> values) {
            addCriterion("status in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<String> values) {
            addCriterion("status not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(String value1, String value2) {
            addCriterion("status between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(String value1, String value2) {
            addCriterion("status not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andExtIsNull() {
            addCriterion("ext is null");
            return (Criteria) this;
        }

        public Criteria andExtIsNotNull() {
            addCriterion("ext is not null");
            return (Criteria) this;
        }

        public Criteria andExtEqualTo(String value) {
            addCriterion("ext =", value, "ext");
            return (Criteria) this;
        }

        public Criteria andExtNotEqualTo(String value) {
            addCriterion("ext <>", value, "ext");
            return (Criteria) this;
        }

        public Criteria andExtGreaterThan(String value) {
            addCriterion("ext >", value, "ext");
            return (Criteria) this;
        }

        public Criteria andExtGreaterThanOrEqualTo(String value) {
            addCriterion("ext >=", value, "ext");
            return (Criteria) this;
        }

        public Criteria andExtLessThan(String value) {
            addCriterion("ext <", value, "ext");
            return (Criteria) this;
        }

        public Criteria andExtLessThanOrEqualTo(String value) {
            addCriterion("ext <=", value, "ext");
            return (Criteria) this;
        }

        public Criteria andExtLike(String value) {
            addCriterion("ext like", value, "ext");
            return (Criteria) this;
        }

        public Criteria andExtNotLike(String value) {
            addCriterion("ext not like", value, "ext");
            return (Criteria) this;
        }

        public Criteria andExtIn(List<String> values) {
            addCriterion("ext in", values, "ext");
            return (Criteria) this;
        }

        public Criteria andExtNotIn(List<String> values) {
            addCriterion("ext not in", values, "ext");
            return (Criteria) this;
        }

        public Criteria andExtBetween(String value1, String value2) {
            addCriterion("ext between", value1, value2, "ext");
            return (Criteria) this;
        }

        public Criteria andExtNotBetween(String value1, String value2) {
            addCriterion("ext not between", value1, value2, "ext");
            return (Criteria) this;
        }

        public Criteria andAddTimeIsNull() {
            addCriterion("add_time is null");
            return (Criteria) this;
        }

        public Criteria andAddTimeIsNotNull() {
            addCriterion("add_time is not null");
            return (Criteria) this;
        }

        public Criteria andAddTimeEqualTo(Date value) {
            addCriterion("add_time =", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotEqualTo(Date value) {
            addCriterion("add_time <>", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeGreaterThan(Date value) {
            addCriterion("add_time >", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("add_time >=", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeLessThan(Date value) {
            addCriterion("add_time <", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeLessThanOrEqualTo(Date value) {
            addCriterion("add_time <=", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeIn(List<Date> values) {
            addCriterion("add_time in", values, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotIn(List<Date> values) {
            addCriterion("add_time not in", values, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeBetween(Date value1, Date value2) {
            addCriterion("add_time between", value1, value2, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotBetween(Date value1, Date value2) {
            addCriterion("add_time not between", value1, value2, "addTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}