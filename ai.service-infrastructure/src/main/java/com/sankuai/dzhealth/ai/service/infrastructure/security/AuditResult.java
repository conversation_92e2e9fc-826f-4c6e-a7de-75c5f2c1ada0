package com.sankuai.dzhealth.ai.service.infrastructure.security;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.sankuai.dzhealth.ai.service.infrastructure.observability.Span;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;

/**
 * <AUTHOR> you<PERSON><PERSON>
 * @version : 0.1
 * @since : 2025/3/31
 */
@Builder
@Data
public class AuditResult {

    @FieldDoc(name = "审核结果", description = "1:通过,2:不通过")
    private Integer advice;

    private String msg;

    private Span span;

    @Getter
    public enum Advice {
        PASSED(1, "通过"),
        NOT_PASSED(2, "不通过");

        private final int code;

        private final String advice;

        Advice(int code, String advice) {
            this.code = code;
            this.advice = advice;
        }
    }
}
