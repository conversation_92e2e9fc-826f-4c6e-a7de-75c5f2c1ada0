package com.sankuai.dzhealth.ai.service.infrastructure.dal.example;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class ThinkingStepEntityExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public ThinkingStepEntityExample() {
        oredCriteria = new ArrayList<>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andSessionIdIsNull() {
            addCriterion("session_id is null");
            return (Criteria) this;
        }

        public Criteria andSessionIdIsNotNull() {
            addCriterion("session_id is not null");
            return (Criteria) this;
        }

        public Criteria andSessionIdEqualTo(Long value) {
            addCriterion("session_id =", value, "sessionId");
            return (Criteria) this;
        }

        public Criteria andSessionIdNotEqualTo(Long value) {
            addCriterion("session_id <>", value, "sessionId");
            return (Criteria) this;
        }

        public Criteria andSessionIdGreaterThan(Long value) {
            addCriterion("session_id >", value, "sessionId");
            return (Criteria) this;
        }

        public Criteria andSessionIdGreaterThanOrEqualTo(Long value) {
            addCriterion("session_id >=", value, "sessionId");
            return (Criteria) this;
        }

        public Criteria andSessionIdLessThan(Long value) {
            addCriterion("session_id <", value, "sessionId");
            return (Criteria) this;
        }

        public Criteria andSessionIdLessThanOrEqualTo(Long value) {
            addCriterion("session_id <=", value, "sessionId");
            return (Criteria) this;
        }

        public Criteria andSessionIdIn(List<Long> values) {
            addCriterion("session_id in", values, "sessionId");
            return (Criteria) this;
        }

        public Criteria andSessionIdNotIn(List<Long> values) {
            addCriterion("session_id not in", values, "sessionId");
            return (Criteria) this;
        }

        public Criteria andSessionIdBetween(Long value1, Long value2) {
            addCriterion("session_id between", value1, value2, "sessionId");
            return (Criteria) this;
        }

        public Criteria andSessionIdNotBetween(Long value1, Long value2) {
            addCriterion("session_id not between", value1, value2, "sessionId");
            return (Criteria) this;
        }

        public Criteria andStepNumberIsNull() {
            addCriterion("step_number is null");
            return (Criteria) this;
        }

        public Criteria andStepNumberIsNotNull() {
            addCriterion("step_number is not null");
            return (Criteria) this;
        }

        public Criteria andStepNumberEqualTo(Long value) {
            addCriterion("step_number =", value, "stepNumber");
            return (Criteria) this;
        }

        public Criteria andStepNumberNotEqualTo(Long value) {
            addCriterion("step_number <>", value, "stepNumber");
            return (Criteria) this;
        }

        public Criteria andStepNumberGreaterThan(Long value) {
            addCriterion("step_number >", value, "stepNumber");
            return (Criteria) this;
        }

        public Criteria andStepNumberGreaterThanOrEqualTo(Long value) {
            addCriterion("step_number >=", value, "stepNumber");
            return (Criteria) this;
        }

        public Criteria andStepNumberLessThan(Long value) {
            addCriterion("step_number <", value, "stepNumber");
            return (Criteria) this;
        }

        public Criteria andStepNumberLessThanOrEqualTo(Long value) {
            addCriterion("step_number <=", value, "stepNumber");
            return (Criteria) this;
        }

        public Criteria andStepNumberIn(List<Long> values) {
            addCriterion("step_number in", values, "stepNumber");
            return (Criteria) this;
        }

        public Criteria andStepNumberNotIn(List<Long> values) {
            addCriterion("step_number not in", values, "stepNumber");
            return (Criteria) this;
        }

        public Criteria andStepNumberBetween(Long value1, Long value2) {
            addCriterion("step_number between", value1, value2, "stepNumber");
            return (Criteria) this;
        }

        public Criteria andStepNumberNotBetween(Long value1, Long value2) {
            addCriterion("step_number not between", value1, value2, "stepNumber");
            return (Criteria) this;
        }

        public Criteria andTotalStepsIsNull() {
            addCriterion("total_steps is null");
            return (Criteria) this;
        }

        public Criteria andTotalStepsIsNotNull() {
            addCriterion("total_steps is not null");
            return (Criteria) this;
        }

        public Criteria andTotalStepsEqualTo(Long value) {
            addCriterion("total_steps =", value, "totalSteps");
            return (Criteria) this;
        }

        public Criteria andTotalStepsNotEqualTo(Long value) {
            addCriterion("total_steps <>", value, "totalSteps");
            return (Criteria) this;
        }

        public Criteria andTotalStepsGreaterThan(Long value) {
            addCriterion("total_steps >", value, "totalSteps");
            return (Criteria) this;
        }

        public Criteria andTotalStepsGreaterThanOrEqualTo(Long value) {
            addCriterion("total_steps >=", value, "totalSteps");
            return (Criteria) this;
        }

        public Criteria andTotalStepsLessThan(Long value) {
            addCriterion("total_steps <", value, "totalSteps");
            return (Criteria) this;
        }

        public Criteria andTotalStepsLessThanOrEqualTo(Long value) {
            addCriterion("total_steps <=", value, "totalSteps");
            return (Criteria) this;
        }

        public Criteria andTotalStepsIn(List<Long> values) {
            addCriterion("total_steps in", values, "totalSteps");
            return (Criteria) this;
        }

        public Criteria andTotalStepsNotIn(List<Long> values) {
            addCriterion("total_steps not in", values, "totalSteps");
            return (Criteria) this;
        }

        public Criteria andTotalStepsBetween(Long value1, Long value2) {
            addCriterion("total_steps between", value1, value2, "totalSteps");
            return (Criteria) this;
        }

        public Criteria andTotalStepsNotBetween(Long value1, Long value2) {
            addCriterion("total_steps not between", value1, value2, "totalSteps");
            return (Criteria) this;
        }

        public Criteria andThoughtIsNull() {
            addCriterion("thought is null");
            return (Criteria) this;
        }

        public Criteria andThoughtIsNotNull() {
            addCriterion("thought is not null");
            return (Criteria) this;
        }

        public Criteria andThoughtEqualTo(String value) {
            addCriterion("thought =", value, "thought");
            return (Criteria) this;
        }

        public Criteria andThoughtNotEqualTo(String value) {
            addCriterion("thought <>", value, "thought");
            return (Criteria) this;
        }

        public Criteria andThoughtGreaterThan(String value) {
            addCriterion("thought >", value, "thought");
            return (Criteria) this;
        }

        public Criteria andThoughtGreaterThanOrEqualTo(String value) {
            addCriterion("thought >=", value, "thought");
            return (Criteria) this;
        }

        public Criteria andThoughtLessThan(String value) {
            addCriterion("thought <", value, "thought");
            return (Criteria) this;
        }

        public Criteria andThoughtLessThanOrEqualTo(String value) {
            addCriterion("thought <=", value, "thought");
            return (Criteria) this;
        }

        public Criteria andThoughtLike(String value) {
            addCriterion("thought like", value, "thought");
            return (Criteria) this;
        }

        public Criteria andThoughtNotLike(String value) {
            addCriterion("thought not like", value, "thought");
            return (Criteria) this;
        }

        public Criteria andThoughtIn(List<String> values) {
            addCriterion("thought in", values, "thought");
            return (Criteria) this;
        }

        public Criteria andThoughtNotIn(List<String> values) {
            addCriterion("thought not in", values, "thought");
            return (Criteria) this;
        }

        public Criteria andThoughtBetween(String value1, String value2) {
            addCriterion("thought between", value1, value2, "thought");
            return (Criteria) this;
        }

        public Criteria andThoughtNotBetween(String value1, String value2) {
            addCriterion("thought not between", value1, value2, "thought");
            return (Criteria) this;
        }

        public Criteria andSearchResultsIsNull() {
            addCriterion("search_results is null");
            return (Criteria) this;
        }

        public Criteria andSearchResultsIsNotNull() {
            addCriterion("search_results is not null");
            return (Criteria) this;
        }

        public Criteria andSearchResultsEqualTo(String value) {
            addCriterion("search_results =", value, "searchResults");
            return (Criteria) this;
        }

        public Criteria andSearchResultsNotEqualTo(String value) {
            addCriterion("search_results <>", value, "searchResults");
            return (Criteria) this;
        }

        public Criteria andSearchResultsGreaterThan(String value) {
            addCriterion("search_results >", value, "searchResults");
            return (Criteria) this;
        }

        public Criteria andSearchResultsGreaterThanOrEqualTo(String value) {
            addCriterion("search_results >=", value, "searchResults");
            return (Criteria) this;
        }

        public Criteria andSearchResultsLessThan(String value) {
            addCriterion("search_results <", value, "searchResults");
            return (Criteria) this;
        }

        public Criteria andSearchResultsLessThanOrEqualTo(String value) {
            addCriterion("search_results <=", value, "searchResults");
            return (Criteria) this;
        }

        public Criteria andSearchResultsLike(String value) {
            addCriterion("search_results like", value, "searchResults");
            return (Criteria) this;
        }

        public Criteria andSearchResultsNotLike(String value) {
            addCriterion("search_results not like", value, "searchResults");
            return (Criteria) this;
        }

        public Criteria andSearchResultsIn(List<String> values) {
            addCriterion("search_results in", values, "searchResults");
            return (Criteria) this;
        }

        public Criteria andSearchResultsNotIn(List<String> values) {
            addCriterion("search_results not in", values, "searchResults");
            return (Criteria) this;
        }

        public Criteria andSearchResultsBetween(String value1, String value2) {
            addCriterion("search_results between", value1, value2, "searchResults");
            return (Criteria) this;
        }

        public Criteria andSearchResultsNotBetween(String value1, String value2) {
            addCriterion("search_results not between", value1, value2, "searchResults");
            return (Criteria) this;
        }

        public Criteria andIsRevisionIsNull() {
            addCriterion("is_revision is null");
            return (Criteria) this;
        }

        public Criteria andIsRevisionIsNotNull() {
            addCriterion("is_revision is not null");
            return (Criteria) this;
        }

        public Criteria andIsRevisionEqualTo(Boolean value) {
            addCriterion("is_revision =", value, "isRevision");
            return (Criteria) this;
        }

        public Criteria andIsRevisionNotEqualTo(Boolean value) {
            addCriterion("is_revision <>", value, "isRevision");
            return (Criteria) this;
        }

        public Criteria andIsRevisionGreaterThan(Boolean value) {
            addCriterion("is_revision >", value, "isRevision");
            return (Criteria) this;
        }

        public Criteria andIsRevisionGreaterThanOrEqualTo(Boolean value) {
            addCriterion("is_revision >=", value, "isRevision");
            return (Criteria) this;
        }

        public Criteria andIsRevisionLessThan(Boolean value) {
            addCriterion("is_revision <", value, "isRevision");
            return (Criteria) this;
        }

        public Criteria andIsRevisionLessThanOrEqualTo(Boolean value) {
            addCriterion("is_revision <=", value, "isRevision");
            return (Criteria) this;
        }

        public Criteria andIsRevisionIn(List<Boolean> values) {
            addCriterion("is_revision in", values, "isRevision");
            return (Criteria) this;
        }

        public Criteria andIsRevisionNotIn(List<Boolean> values) {
            addCriterion("is_revision not in", values, "isRevision");
            return (Criteria) this;
        }

        public Criteria andIsRevisionBetween(Boolean value1, Boolean value2) {
            addCriterion("is_revision between", value1, value2, "isRevision");
            return (Criteria) this;
        }

        public Criteria andIsRevisionNotBetween(Boolean value1, Boolean value2) {
            addCriterion("is_revision not between", value1, value2, "isRevision");
            return (Criteria) this;
        }

        public Criteria andRevisesStepNumberIsNull() {
            addCriterion("revises_step_number is null");
            return (Criteria) this;
        }

        public Criteria andRevisesStepNumberIsNotNull() {
            addCriterion("revises_step_number is not null");
            return (Criteria) this;
        }

        public Criteria andRevisesStepNumberEqualTo(Long value) {
            addCriterion("revises_step_number =", value, "revisesStepNumber");
            return (Criteria) this;
        }

        public Criteria andRevisesStepNumberNotEqualTo(Long value) {
            addCriterion("revises_step_number <>", value, "revisesStepNumber");
            return (Criteria) this;
        }

        public Criteria andRevisesStepNumberGreaterThan(Long value) {
            addCriterion("revises_step_number >", value, "revisesStepNumber");
            return (Criteria) this;
        }

        public Criteria andRevisesStepNumberGreaterThanOrEqualTo(Long value) {
            addCriterion("revises_step_number >=", value, "revisesStepNumber");
            return (Criteria) this;
        }

        public Criteria andRevisesStepNumberLessThan(Long value) {
            addCriterion("revises_step_number <", value, "revisesStepNumber");
            return (Criteria) this;
        }

        public Criteria andRevisesStepNumberLessThanOrEqualTo(Long value) {
            addCriterion("revises_step_number <=", value, "revisesStepNumber");
            return (Criteria) this;
        }

        public Criteria andRevisesStepNumberIn(List<Long> values) {
            addCriterion("revises_step_number in", values, "revisesStepNumber");
            return (Criteria) this;
        }

        public Criteria andRevisesStepNumberNotIn(List<Long> values) {
            addCriterion("revises_step_number not in", values, "revisesStepNumber");
            return (Criteria) this;
        }

        public Criteria andRevisesStepNumberBetween(Long value1, Long value2) {
            addCriterion("revises_step_number between", value1, value2, "revisesStepNumber");
            return (Criteria) this;
        }

        public Criteria andRevisesStepNumberNotBetween(Long value1, Long value2) {
            addCriterion("revises_step_number not between", value1, value2, "revisesStepNumber");
            return (Criteria) this;
        }

        public Criteria andNextThoughtNeededIsNull() {
            addCriterion("next_thought_needed is null");
            return (Criteria) this;
        }

        public Criteria andNextThoughtNeededIsNotNull() {
            addCriterion("next_thought_needed is not null");
            return (Criteria) this;
        }

        public Criteria andNextThoughtNeededEqualTo(Boolean value) {
            addCriterion("next_thought_needed =", value, "nextThoughtNeeded");
            return (Criteria) this;
        }

        public Criteria andNextThoughtNeededNotEqualTo(Boolean value) {
            addCriterion("next_thought_needed <>", value, "nextThoughtNeeded");
            return (Criteria) this;
        }

        public Criteria andNextThoughtNeededGreaterThan(Boolean value) {
            addCriterion("next_thought_needed >", value, "nextThoughtNeeded");
            return (Criteria) this;
        }

        public Criteria andNextThoughtNeededGreaterThanOrEqualTo(Boolean value) {
            addCriterion("next_thought_needed >=", value, "nextThoughtNeeded");
            return (Criteria) this;
        }

        public Criteria andNextThoughtNeededLessThan(Boolean value) {
            addCriterion("next_thought_needed <", value, "nextThoughtNeeded");
            return (Criteria) this;
        }

        public Criteria andNextThoughtNeededLessThanOrEqualTo(Boolean value) {
            addCriterion("next_thought_needed <=", value, "nextThoughtNeeded");
            return (Criteria) this;
        }

        public Criteria andNextThoughtNeededIn(List<Boolean> values) {
            addCriterion("next_thought_needed in", values, "nextThoughtNeeded");
            return (Criteria) this;
        }

        public Criteria andNextThoughtNeededNotIn(List<Boolean> values) {
            addCriterion("next_thought_needed not in", values, "nextThoughtNeeded");
            return (Criteria) this;
        }

        public Criteria andNextThoughtNeededBetween(Boolean value1, Boolean value2) {
            addCriterion("next_thought_needed between", value1, value2, "nextThoughtNeeded");
            return (Criteria) this;
        }

        public Criteria andNextThoughtNeededNotBetween(Boolean value1, Boolean value2) {
            addCriterion("next_thought_needed not between", value1, value2, "nextThoughtNeeded");
            return (Criteria) this;
        }

        public Criteria andBranchFromStepIsNull() {
            addCriterion("branch_from_step is null");
            return (Criteria) this;
        }

        public Criteria andBranchFromStepIsNotNull() {
            addCriterion("branch_from_step is not null");
            return (Criteria) this;
        }

        public Criteria andBranchFromStepEqualTo(Long value) {
            addCriterion("branch_from_step =", value, "branchFromStep");
            return (Criteria) this;
        }

        public Criteria andBranchFromStepNotEqualTo(Long value) {
            addCriterion("branch_from_step <>", value, "branchFromStep");
            return (Criteria) this;
        }

        public Criteria andBranchFromStepGreaterThan(Long value) {
            addCriterion("branch_from_step >", value, "branchFromStep");
            return (Criteria) this;
        }

        public Criteria andBranchFromStepGreaterThanOrEqualTo(Long value) {
            addCriterion("branch_from_step >=", value, "branchFromStep");
            return (Criteria) this;
        }

        public Criteria andBranchFromStepLessThan(Long value) {
            addCriterion("branch_from_step <", value, "branchFromStep");
            return (Criteria) this;
        }

        public Criteria andBranchFromStepLessThanOrEqualTo(Long value) {
            addCriterion("branch_from_step <=", value, "branchFromStep");
            return (Criteria) this;
        }

        public Criteria andBranchFromStepIn(List<Long> values) {
            addCriterion("branch_from_step in", values, "branchFromStep");
            return (Criteria) this;
        }

        public Criteria andBranchFromStepNotIn(List<Long> values) {
            addCriterion("branch_from_step not in", values, "branchFromStep");
            return (Criteria) this;
        }

        public Criteria andBranchFromStepBetween(Long value1, Long value2) {
            addCriterion("branch_from_step between", value1, value2, "branchFromStep");
            return (Criteria) this;
        }

        public Criteria andBranchFromStepNotBetween(Long value1, Long value2) {
            addCriterion("branch_from_step not between", value1, value2, "branchFromStep");
            return (Criteria) this;
        }

        public Criteria andBranchIdIsNull() {
            addCriterion("branch_id is null");
            return (Criteria) this;
        }

        public Criteria andBranchIdIsNotNull() {
            addCriterion("branch_id is not null");
            return (Criteria) this;
        }

        public Criteria andBranchIdEqualTo(String value) {
            addCriterion("branch_id =", value, "branchId");
            return (Criteria) this;
        }

        public Criteria andBranchIdNotEqualTo(String value) {
            addCriterion("branch_id <>", value, "branchId");
            return (Criteria) this;
        }

        public Criteria andBranchIdGreaterThan(String value) {
            addCriterion("branch_id >", value, "branchId");
            return (Criteria) this;
        }

        public Criteria andBranchIdGreaterThanOrEqualTo(String value) {
            addCriterion("branch_id >=", value, "branchId");
            return (Criteria) this;
        }

        public Criteria andBranchIdLessThan(String value) {
            addCriterion("branch_id <", value, "branchId");
            return (Criteria) this;
        }

        public Criteria andBranchIdLessThanOrEqualTo(String value) {
            addCriterion("branch_id <=", value, "branchId");
            return (Criteria) this;
        }

        public Criteria andBranchIdLike(String value) {
            addCriterion("branch_id like", value, "branchId");
            return (Criteria) this;
        }

        public Criteria andBranchIdNotLike(String value) {
            addCriterion("branch_id not like", value, "branchId");
            return (Criteria) this;
        }

        public Criteria andBranchIdIn(List<String> values) {
            addCriterion("branch_id in", values, "branchId");
            return (Criteria) this;
        }

        public Criteria andBranchIdNotIn(List<String> values) {
            addCriterion("branch_id not in", values, "branchId");
            return (Criteria) this;
        }

        public Criteria andBranchIdBetween(String value1, String value2) {
            addCriterion("branch_id between", value1, value2, "branchId");
            return (Criteria) this;
        }

        public Criteria andBranchIdNotBetween(String value1, String value2) {
            addCriterion("branch_id not between", value1, value2, "branchId");
            return (Criteria) this;
        }

        public Criteria andConfidenceScoreIsNull() {
            addCriterion("confidence_score is null");
            return (Criteria) this;
        }

        public Criteria andConfidenceScoreIsNotNull() {
            addCriterion("confidence_score is not null");
            return (Criteria) this;
        }

        public Criteria andConfidenceScoreEqualTo(BigDecimal value) {
            addCriterion("confidence_score =", value, "confidenceScore");
            return (Criteria) this;
        }

        public Criteria andConfidenceScoreNotEqualTo(BigDecimal value) {
            addCriterion("confidence_score <>", value, "confidenceScore");
            return (Criteria) this;
        }

        public Criteria andConfidenceScoreGreaterThan(BigDecimal value) {
            addCriterion("confidence_score >", value, "confidenceScore");
            return (Criteria) this;
        }

        public Criteria andConfidenceScoreGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("confidence_score >=", value, "confidenceScore");
            return (Criteria) this;
        }

        public Criteria andConfidenceScoreLessThan(BigDecimal value) {
            addCriterion("confidence_score <", value, "confidenceScore");
            return (Criteria) this;
        }

        public Criteria andConfidenceScoreLessThanOrEqualTo(BigDecimal value) {
            addCriterion("confidence_score <=", value, "confidenceScore");
            return (Criteria) this;
        }

        public Criteria andConfidenceScoreIn(List<BigDecimal> values) {
            addCriterion("confidence_score in", values, "confidenceScore");
            return (Criteria) this;
        }

        public Criteria andConfidenceScoreNotIn(List<BigDecimal> values) {
            addCriterion("confidence_score not in", values, "confidenceScore");
            return (Criteria) this;
        }

        public Criteria andConfidenceScoreBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("confidence_score between", value1, value2, "confidenceScore");
            return (Criteria) this;
        }

        public Criteria andConfidenceScoreNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("confidence_score not between", value1, value2, "confidenceScore");
            return (Criteria) this;
        }

        public Criteria andAddTimeIsNull() {
            addCriterion("add_time is null");
            return (Criteria) this;
        }

        public Criteria andAddTimeIsNotNull() {
            addCriterion("add_time is not null");
            return (Criteria) this;
        }

        public Criteria andAddTimeEqualTo(Date value) {
            addCriterion("add_time =", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotEqualTo(Date value) {
            addCriterion("add_time <>", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeGreaterThan(Date value) {
            addCriterion("add_time >", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("add_time >=", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeLessThan(Date value) {
            addCriterion("add_time <", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeLessThanOrEqualTo(Date value) {
            addCriterion("add_time <=", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeIn(List<Date> values) {
            addCriterion("add_time in", values, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotIn(List<Date> values) {
            addCriterion("add_time not in", values, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeBetween(Date value1, Date value2) {
            addCriterion("add_time between", value1, value2, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotBetween(Date value1, Date value2) {
            addCriterion("add_time not between", value1, value2, "addTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}