package com.sankuai.dzhealth.ai.service.agent.infrastructure.dal.repository.evaluation;

import com.sankuai.dzhealth.ai.service.agent.infrastructure.dal.entity.SessionEvaluationResultEntity;
import com.sankuai.dzhealth.ai.service.agent.infrastructure.dal.example.SessionEvaluationResultEntityExample;
import com.sankuai.dzhealth.ai.service.agent.infrastructure.dal.mapper.SessionEvaluationResultEntityMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR> xiafangyuan
 * @since : 2025/7/11 11:35
 */
@Slf4j
@Repository
public class SessionEvaluationResultRepository {

    @Autowired
    private SessionEvaluationResultEntityMapper sessionEvaluationResultEntityMapper;

    public List<SessionEvaluationResultEntity> selectBySessionId(String sessionId) {
        log.info("selectBySessionId, sessionId:{}", sessionId);
        if (StringUtils.isBlank(sessionId)) {
            return Collections.emptyList();
        }
        SessionEvaluationResultEntityExample example = new SessionEvaluationResultEntityExample();
        example.createCriteria().andSessionIdEqualTo(sessionId);
        List<SessionEvaluationResultEntity> sessionEvaluationResultEntities = sessionEvaluationResultEntityMapper.selectByExample(example);
        log.info("selectBySessionId, size:{} ,sessionEvaluationResultEntities:{}", sessionEvaluationResultEntities.size(), sessionEvaluationResultEntities);
        return sessionEvaluationResultEntities;
    }

    public List<SessionEvaluationResultEntity> selectBySessionIdAndVersion(String sessionId, Integer version) {
        log.info("selectBySessionId, sessionId:{}", sessionId);
        if (StringUtils.isBlank(sessionId)) {
            return Collections.emptyList();
        }
        SessionEvaluationResultEntityExample example = new SessionEvaluationResultEntityExample();
        example.createCriteria().andSessionIdEqualTo(sessionId).andEvaluationVerEqualTo(version);
        List<SessionEvaluationResultEntity> sessionEvaluationResultEntities = sessionEvaluationResultEntityMapper.selectByExample(example);
        log.info("selectBySessionId, size:{} ,sessionEvaluationResultEntities:{}", sessionEvaluationResultEntities.size(), sessionEvaluationResultEntities);
        return sessionEvaluationResultEntities;
    }

    public void updateBySessionIdAndKey(SessionEvaluationResultEntity sessionResult) {
        SessionEvaluationResultEntityExample example = new SessionEvaluationResultEntityExample();
        example.createCriteria().andIdEqualTo(sessionResult.getId()).andSessionIdEqualTo(sessionResult.getSessionId()).andItemEqualTo(sessionResult.getItem());
        sessionEvaluationResultEntityMapper.updateByExampleSelective(sessionResult, example);
    }

    public void insert(SessionEvaluationResultEntity newSessionResult) {
        sessionEvaluationResultEntityMapper.insert(newSessionResult);
    }
}
