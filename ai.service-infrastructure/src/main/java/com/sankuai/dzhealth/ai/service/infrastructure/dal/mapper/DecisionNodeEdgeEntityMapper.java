package com.sankuai.dzhealth.ai.service.infrastructure.dal.mapper;

import com.meituan.mdp.mybatis.mapper.MybatisBaseMapper;
import com.sankuai.dzhealth.ai.service.infrastructure.dal.entity.DecisionNodeEdgeDO;
import com.sankuai.dzhealth.ai.service.infrastructure.dal.example.DecisionNodeEdgeEntityExample;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface DecisionNodeEdgeEntityMapper extends MybatisBaseMapper<DecisionNodeEdgeDO, DecisionNodeEdgeEntityExample, Long> {

    /**
     * 批量插入边
     */
    int batchInsert(@Param("list") List<DecisionNodeEdgeDO> entities);

    /**
     * 批量更新边（根据edgeId）
     */
    int batchUpdateByEdgeId(@Param("list") List<DecisionNodeEdgeDO> entities);
}