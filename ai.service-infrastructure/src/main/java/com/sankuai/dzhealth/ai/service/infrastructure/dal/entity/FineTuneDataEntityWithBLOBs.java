package com.sankuai.dzhealth.ai.service.infrastructure.dal.entity;

import lombok.*;
import lombok.experimental.SuperBuilder;

/**
 *
 *   表名: fine_tune_data
 */
@SuperBuilder
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class FineTuneDataEntityWithBLOBs extends FineTuneDataEntity {
    /**
     *   字段: feedback
     *   说明: 评估反馈
     */
    private String feedback;

    /**
     *   字段: metadata
     *   说明: 元数据(JSON格式)
     */
    private String metadata;
}