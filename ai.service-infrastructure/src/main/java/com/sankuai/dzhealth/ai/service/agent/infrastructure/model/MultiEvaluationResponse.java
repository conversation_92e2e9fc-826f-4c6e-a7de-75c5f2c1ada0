package com.sankuai.dzhealth.ai.service.agent.infrastructure.model;

import com.sankuai.dzhealth.ai.service.agent.infrastructure.dal.entity.MessageEvaluationResultEntity;
import com.sankuai.dzhealth.ai.service.agent.infrastructure.dal.entity.SessionEvaluationResultEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.List;

/**
 * 多轮对话评估响应
 *
 * <AUTHOR>
 * @since 2025/1/15
 */
@SuperBuilder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MultiEvaluationResponse {

    /**
     * 业务场景
     */
    private String bizScene;

    /**
     * 会话ID
     */
    private String sessionId;

    /**
     * 消息ID
     */
    private String messageId;

    /**
     * 模型场景
     */
    private String modelScene;

    /**
     * 对话评估结果集合
     */
    private List<MessageEvaluationResultEntity> messageResults;

    /**
     * 会话评估结果集合
     */
    private List<SessionEvaluationResultEntity> sessionResults;

    /**
     * 是否成功
     */
    private Boolean success;

    /**
     * 描述
     */
    private String msg;
}

