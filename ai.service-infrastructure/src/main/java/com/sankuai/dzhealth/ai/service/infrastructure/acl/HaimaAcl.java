package com.sankuai.dzhealth.ai.service.infrastructure.acl;

import com.alibaba.fastjson.JSON;
import com.dianping.haima.client.HaimaClient;
import com.dianping.haima.client.request.HaimaRequest;
import com.dianping.haima.client.response.HaimaResponse;
import com.dianping.haima.entity.haima.HaimaConfig;
import com.dianping.haima.entity.haima.HaimaContent;
import com.meituan.mtrace.scene.util.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 海马运营位访问控制层
 * 提供与海马系统交互的方法，用于获取运营配置内容
 *
 * @author:chenwei
 * @time: 2025/3/21 14:32
 * @version: 0.0.1
 */

@Service
@Slf4j
public class HaimaAcl {

    @Resource
    private HaimaClient haimaClient;



    public List<HaimaContent> getContent(String sceneKey, Map<String, String> fields) {
        if (StringUtils.isBlank(sceneKey)) {
            return Collections.emptyList();
        }
        HaimaRequest request = buildHaimaRequest(sceneKey, fields);
        return getHaimaContents(request);
    }


    private List<HaimaContent> getHaimaContents(HaimaRequest request) {
        try {
            if (null == request) {
                return Collections.emptyList();
            }
            HaimaResponse response = haimaClient.query(request);
            if (null == response || !response.isSuccess() || CollectionUtils.isEmpty(response.getData())) {
                log.info("ai_service query fail, haimaRequest:{}, msg:{}", JsonUtil.toJson(request),
                        null == response ? "null response" : response.getMessage());
                return Collections.emptyList();
            }
            for (HaimaConfig config : response.getData()) {
                if (null == config || CollectionUtils.isEmpty(config.getContents())) {
                    continue;
                }
                return Optional.ofNullable(config.getContents()).orElse(Collections.emptyList()).stream().filter(
                        Objects::nonNull).collect(Collectors.toList());
            }
            //未配置数据
            return Collections.emptyList();
        } catch (Exception e) {
            log.error("getHaimaContentConfig:" + JSON.toJSONString(request), e);
            return Collections.emptyList();
        }
    }
    public List<HaimaConfig> getHaimaConfigs(HaimaRequest request) {
        try {
            if (null == request) {
                return Collections.emptyList();
            }
            HaimaResponse response = haimaClient.query(request);
            if (null == response || !response.isSuccess() || CollectionUtils.isEmpty(response.getData())) {
                log.info("ai_service query fail, haimaRequest:{}, msg:{}", JsonUtil.toJson(request),
                        null == response ? "null response" : response.getMessage());
                return Collections.emptyList();
            }
            return response.getData();
        } catch (Exception e) {
            log.error("getHaimaContentConfig:" + JSON.toJSONString(request), e);
            return Collections.emptyList();
        }
    }

    private HaimaRequest buildHaimaRequest(String sceneKey, Map<String, String> fields) {
        HaimaRequest request = new HaimaRequest();
        // 业务方申请的运营位key
        request.setSceneKey(sceneKey);
        // 如果需要其他字段的过滤,可以设置过滤字段的K/V值
        if (MapUtils.isNotEmpty(fields)) {
            for (Map.Entry<String, String> entry : fields.entrySet()) {
                request.addField(entry.getKey(), entry.getValue());
            }
        }
        return request;
    }
}
