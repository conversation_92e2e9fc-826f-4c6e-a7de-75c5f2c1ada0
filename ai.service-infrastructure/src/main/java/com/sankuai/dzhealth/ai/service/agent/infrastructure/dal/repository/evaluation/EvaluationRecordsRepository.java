package com.sankuai.dzhealth.ai.service.agent.infrastructure.dal.repository.evaluation;

import com.sankuai.dzhealth.ai.service.agent.infrastructure.dal.entity.EvaluationRecordsDO;
import com.sankuai.dzhealth.ai.service.agent.infrastructure.dal.example.EvaluationRecordsDOExample;
import com.sankuai.dzhealth.ai.service.agent.infrastructure.dal.example.EvaluationSessionMessageRecordsDOExample;
import com.sankuai.dzhealth.ai.service.agent.infrastructure.dal.mapper.EvaluationRecordsDOMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

@Repository
@RequiredArgsConstructor
public class EvaluationRecordsRepository {
    @Resource
    private EvaluationRecordsDOMapper evaluationRecordsDOMapper;

    public void insert(EvaluationRecordsDO evaluationRecordsDO) {
        evaluationRecordsDOMapper.insert(evaluationRecordsDO);
    }

    public List<EvaluationRecordsDO> selectByExample(String evaluationID) {
        EvaluationRecordsDOExample example = new EvaluationRecordsDOExample();
        example.createCriteria().andEvaluationIdEqualTo(evaluationID);
        return evaluationRecordsDOMapper.selectByExample(example);
    }

    /**
     * 按创建时间倒序查询测评列表
     *
     * @param limit  限制返回记录数
     * @param offset 偏移量
     * @return 测评记录列表
     */
    public List<EvaluationRecordsDO> findAllOrderByCreateTimeDesc(int limit, int offset) {
        return evaluationRecordsDOMapper.selectAllOrderByCreateTimeDesc(limit, offset);
    }
}