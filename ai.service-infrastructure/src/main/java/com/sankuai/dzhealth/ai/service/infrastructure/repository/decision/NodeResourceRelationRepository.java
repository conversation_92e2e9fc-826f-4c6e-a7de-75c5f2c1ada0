package com.sankuai.dzhealth.ai.service.infrastructure.repository.decision;

import com.sankuai.dzhealth.ai.service.enums.RelationStatusEnum;
import com.sankuai.dzhealth.ai.service.infrastructure.dal.entity.NodeResourceRelationDO;
import com.sankuai.dzhealth.ai.service.infrastructure.dal.example.NodeResourceRelationEntityExample;
import com.sankuai.dzhealth.ai.service.infrastructure.dal.mapper.NodeResourceRelationEntityMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 节点-资源 关联关系仓储
 */
@Repository
@RequiredArgsConstructor
public class NodeResourceRelationRepository {

    private final NodeResourceRelationEntityMapper relationMapper;

    public List<NodeResourceRelationDO> listOnlineByNode(String nodeId, int limit) {
        NodeResourceRelationEntityExample example = new NodeResourceRelationEntityExample();
        example.createCriteria()
                .andNodeIdEqualTo(nodeId)
                .andStatusEqualTo(RelationStatusEnum.ONLINE.code());
        example.setOrderByClause("sort_order ASC");
        List<NodeResourceRelationDO> list = relationMapper.selectByExample(example);
        if (limit > 0 && list.size() > limit) {
            return list.subList(0, limit);
        }
        return list;
    }

    /**
     * 根据业务场景和节点ID查询在线关联关系
     */
    public List<NodeResourceRelationDO> listOnlineByBizSceneAndNode(String bizScene, String nodeId, int limit) {
        NodeResourceRelationEntityExample example = new NodeResourceRelationEntityExample();
        example.createCriteria()
                .andBizSceneEqualTo(bizScene)
                .andNodeIdEqualTo(nodeId)
                .andStatusEqualTo(RelationStatusEnum.ONLINE.code());
        example.setOrderByClause("sort_order ASC");
        List<NodeResourceRelationDO> list = relationMapper.selectByExample(example);
        if (limit > 0 && list.size() > limit) {
            return list.subList(0, limit);
        }
        return list;
    }

    public int deleteByBizScene(String bizScene){
        NodeResourceRelationDO entity=new NodeResourceRelationDO();
        entity.setStatus(RelationStatusEnum.ONLINE_DELETE.code());
        NodeResourceRelationEntityExample example=new NodeResourceRelationEntityExample();
        example.createCriteria().andBizSceneEqualTo(bizScene);
        return relationMapper.updateByExampleSelective(entity, example);
    }

    public int grayDeleteByBizScene(String bizScene){
        NodeResourceRelationDO entity=new NodeResourceRelationDO();
        entity.setStatus(RelationStatusEnum.GRAY_DELETE.code());
        NodeResourceRelationEntityExample example=new NodeResourceRelationEntityExample();
        example.createCriteria().andBizSceneEqualTo(bizScene);
        return relationMapper.updateByExampleSelective(entity, example);
    }

    public int insert(NodeResourceRelationDO entity){
        return relationMapper.insertSelective(entity);
    }

    /**
     * Batch insert relations to reduce DB round-trips
     */
    public int batchInsert(List<NodeResourceRelationDO> entities){
        if(entities == null || entities.isEmpty()){
            return 0;
        }
        // 使用 MyBatis 提供的批量插入能力
        return relationMapper.insertList(entities);
    }

    public int deleteById(Long id){
        NodeResourceRelationDO entity=new NodeResourceRelationDO();
        entity.setId(id);
        entity.setStatus(RelationStatusEnum.ONLINE_DELETE.code());
        return relationMapper.updateByPrimaryKeySelective(entity);
    }

    public int deleteByNodeId(String nodeId){
        NodeResourceRelationDO entity=new NodeResourceRelationDO();
        entity.setStatus(RelationStatusEnum.ONLINE_DELETE.code());
        NodeResourceRelationEntityExample example=new NodeResourceRelationEntityExample();
        example.createCriteria().andNodeIdEqualTo(nodeId);
        return relationMapper.updateByExampleSelective(entity, example);
    }

    /**
     * 根据 bizScene + status 查询关联
     */
    public List<NodeResourceRelationDO> listByStatus(String bizScene, String status) {
        NodeResourceRelationEntityExample example = new NodeResourceRelationEntityExample();
        example.createCriteria()
                .andBizSceneEqualTo(bizScene)
                .andStatusEqualTo(status);
        return relationMapper.selectByExample(example);
    }

    public int updateById(NodeResourceRelationDO entity) {
        return relationMapper.updateByPrimaryKeySelective(entity);
    }

    /**
     * 批量更新关系状态
     */
    public int batchUpdateStatusByIds(List<Long> relationIds, String status) {
        if (relationIds == null || relationIds.isEmpty()) {
            return 0;
        }
        NodeResourceRelationDO entity = new NodeResourceRelationDO();
        entity.setStatus(status);
        NodeResourceRelationEntityExample example = new NodeResourceRelationEntityExample();
        example.createCriteria().andIdIn(relationIds);
        return relationMapper.updateByExampleSelective(entity, example);
    }

    /**
     * 批量删除关系（软删除）
     */
    public int batchDeleteByIds(List<Long> relationIds) {
        if (relationIds == null || relationIds.isEmpty()) {
            return 0;
        }
        NodeResourceRelationDO entity = new NodeResourceRelationDO();
        entity.setStatus(RelationStatusEnum.ONLINE_DELETE.code());
        NodeResourceRelationEntityExample example = new NodeResourceRelationEntityExample();
        example.createCriteria().andIdIn(relationIds);
        return relationMapper.updateByExampleSelective(entity, example);
    }

    /**
     * 物理删除指定业务场景下的所有节点-资源关系
     */
    public int hardDeleteByBizScene(String bizScene){
        NodeResourceRelationEntityExample example=new NodeResourceRelationEntityExample();
        example.createCriteria().andBizSceneEqualTo(bizScene);
        return relationMapper.deleteByExample(example);
    }
} 