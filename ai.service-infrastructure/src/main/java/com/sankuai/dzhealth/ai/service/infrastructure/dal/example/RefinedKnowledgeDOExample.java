package com.sankuai.dzhealth.ai.service.infrastructure.dal.example;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class RefinedKnowledgeDOExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public RefinedKnowledgeDOExample() {
        oredCriteria = new ArrayList<>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andCorpusIdIsNull() {
            addCriterion("corpus_id is null");
            return (Criteria) this;
        }

        public Criteria andCorpusIdIsNotNull() {
            addCriterion("corpus_id is not null");
            return (Criteria) this;
        }

        public Criteria andCorpusIdEqualTo(Long value) {
            addCriterion("corpus_id =", value, "corpusId");
            return (Criteria) this;
        }

        public Criteria andCorpusIdNotEqualTo(Long value) {
            addCriterion("corpus_id <>", value, "corpusId");
            return (Criteria) this;
        }

        public Criteria andCorpusIdGreaterThan(Long value) {
            addCriterion("corpus_id >", value, "corpusId");
            return (Criteria) this;
        }

        public Criteria andCorpusIdGreaterThanOrEqualTo(Long value) {
            addCriterion("corpus_id >=", value, "corpusId");
            return (Criteria) this;
        }

        public Criteria andCorpusIdLessThan(Long value) {
            addCriterion("corpus_id <", value, "corpusId");
            return (Criteria) this;
        }

        public Criteria andCorpusIdLessThanOrEqualTo(Long value) {
            addCriterion("corpus_id <=", value, "corpusId");
            return (Criteria) this;
        }

        public Criteria andCorpusIdIn(List<Long> values) {
            addCriterion("corpus_id in", values, "corpusId");
            return (Criteria) this;
        }

        public Criteria andCorpusIdNotIn(List<Long> values) {
            addCriterion("corpus_id not in", values, "corpusId");
            return (Criteria) this;
        }

        public Criteria andCorpusIdBetween(Long value1, Long value2) {
            addCriterion("corpus_id between", value1, value2, "corpusId");
            return (Criteria) this;
        }

        public Criteria andCorpusIdNotBetween(Long value1, Long value2) {
            addCriterion("corpus_id not between", value1, value2, "corpusId");
            return (Criteria) this;
        }

        public Criteria andResourceIdIsNull() {
            addCriterion("resource_id is null");
            return (Criteria) this;
        }

        public Criteria andResourceIdIsNotNull() {
            addCriterion("resource_id is not null");
            return (Criteria) this;
        }

        public Criteria andResourceIdEqualTo(Long value) {
            addCriterion("resource_id =", value, "resourceId");
            return (Criteria) this;
        }

        public Criteria andResourceIdNotEqualTo(Long value) {
            addCriterion("resource_id <>", value, "resourceId");
            return (Criteria) this;
        }

        public Criteria andResourceIdGreaterThan(Long value) {
            addCriterion("resource_id >", value, "resourceId");
            return (Criteria) this;
        }

        public Criteria andResourceIdGreaterThanOrEqualTo(Long value) {
            addCriterion("resource_id >=", value, "resourceId");
            return (Criteria) this;
        }

        public Criteria andResourceIdLessThan(Long value) {
            addCriterion("resource_id <", value, "resourceId");
            return (Criteria) this;
        }

        public Criteria andResourceIdLessThanOrEqualTo(Long value) {
            addCriterion("resource_id <=", value, "resourceId");
            return (Criteria) this;
        }

        public Criteria andResourceIdIn(List<Long> values) {
            addCriterion("resource_id in", values, "resourceId");
            return (Criteria) this;
        }

        public Criteria andResourceIdNotIn(List<Long> values) {
            addCriterion("resource_id not in", values, "resourceId");
            return (Criteria) this;
        }

        public Criteria andResourceIdBetween(Long value1, Long value2) {
            addCriterion("resource_id between", value1, value2, "resourceId");
            return (Criteria) this;
        }

        public Criteria andResourceIdNotBetween(Long value1, Long value2) {
            addCriterion("resource_id not between", value1, value2, "resourceId");
            return (Criteria) this;
        }

        public Criteria andResourceChannelIsNull() {
            addCriterion("resource_channel is null");
            return (Criteria) this;
        }

        public Criteria andResourceChannelIsNotNull() {
            addCriterion("resource_channel is not null");
            return (Criteria) this;
        }

        public Criteria andResourceChannelEqualTo(String value) {
            addCriterion("resource_channel =", value, "resourceChannel");
            return (Criteria) this;
        }

        public Criteria andResourceChannelNotEqualTo(String value) {
            addCriterion("resource_channel <>", value, "resourceChannel");
            return (Criteria) this;
        }

        public Criteria andResourceChannelGreaterThan(String value) {
            addCriterion("resource_channel >", value, "resourceChannel");
            return (Criteria) this;
        }

        public Criteria andResourceChannelGreaterThanOrEqualTo(String value) {
            addCriterion("resource_channel >=", value, "resourceChannel");
            return (Criteria) this;
        }

        public Criteria andResourceChannelLessThan(String value) {
            addCriterion("resource_channel <", value, "resourceChannel");
            return (Criteria) this;
        }

        public Criteria andResourceChannelLessThanOrEqualTo(String value) {
            addCriterion("resource_channel <=", value, "resourceChannel");
            return (Criteria) this;
        }

        public Criteria andResourceChannelLike(String value) {
            addCriterion("resource_channel like", value, "resourceChannel");
            return (Criteria) this;
        }

        public Criteria andResourceChannelNotLike(String value) {
            addCriterion("resource_channel not like", value, "resourceChannel");
            return (Criteria) this;
        }

        public Criteria andResourceChannelIn(List<String> values) {
            addCriterion("resource_channel in", values, "resourceChannel");
            return (Criteria) this;
        }

        public Criteria andResourceChannelNotIn(List<String> values) {
            addCriterion("resource_channel not in", values, "resourceChannel");
            return (Criteria) this;
        }

        public Criteria andResourceChannelBetween(String value1, String value2) {
            addCriterion("resource_channel between", value1, value2, "resourceChannel");
            return (Criteria) this;
        }

        public Criteria andResourceChannelNotBetween(String value1, String value2) {
            addCriterion("resource_channel not between", value1, value2, "resourceChannel");
            return (Criteria) this;
        }

        public Criteria andResourceUriIsNull() {
            addCriterion("resource_uri is null");
            return (Criteria) this;
        }

        public Criteria andResourceUriIsNotNull() {
            addCriterion("resource_uri is not null");
            return (Criteria) this;
        }

        public Criteria andResourceUriEqualTo(String value) {
            addCriterion("resource_uri =", value, "resourceUri");
            return (Criteria) this;
        }

        public Criteria andResourceUriNotEqualTo(String value) {
            addCriterion("resource_uri <>", value, "resourceUri");
            return (Criteria) this;
        }

        public Criteria andResourceUriGreaterThan(String value) {
            addCriterion("resource_uri >", value, "resourceUri");
            return (Criteria) this;
        }

        public Criteria andResourceUriGreaterThanOrEqualTo(String value) {
            addCriterion("resource_uri >=", value, "resourceUri");
            return (Criteria) this;
        }

        public Criteria andResourceUriLessThan(String value) {
            addCriterion("resource_uri <", value, "resourceUri");
            return (Criteria) this;
        }

        public Criteria andResourceUriLessThanOrEqualTo(String value) {
            addCriterion("resource_uri <=", value, "resourceUri");
            return (Criteria) this;
        }

        public Criteria andResourceUriLike(String value) {
            addCriterion("resource_uri like", value, "resourceUri");
            return (Criteria) this;
        }

        public Criteria andResourceUriNotLike(String value) {
            addCriterion("resource_uri not like", value, "resourceUri");
            return (Criteria) this;
        }

        public Criteria andResourceUriIn(List<String> values) {
            addCriterion("resource_uri in", values, "resourceUri");
            return (Criteria) this;
        }

        public Criteria andResourceUriNotIn(List<String> values) {
            addCriterion("resource_uri not in", values, "resourceUri");
            return (Criteria) this;
        }

        public Criteria andResourceUriBetween(String value1, String value2) {
            addCriterion("resource_uri between", value1, value2, "resourceUri");
            return (Criteria) this;
        }

        public Criteria andResourceUriNotBetween(String value1, String value2) {
            addCriterion("resource_uri not between", value1, value2, "resourceUri");
            return (Criteria) this;
        }

        public Criteria andKnowledgeTypeIsNull() {
            addCriterion("knowledge_type is null");
            return (Criteria) this;
        }

        public Criteria andKnowledgeTypeIsNotNull() {
            addCriterion("knowledge_type is not null");
            return (Criteria) this;
        }

        public Criteria andKnowledgeTypeEqualTo(Integer value) {
            addCriterion("knowledge_type =", value, "knowledgeType");
            return (Criteria) this;
        }

        public Criteria andKnowledgeTypeNotEqualTo(Integer value) {
            addCriterion("knowledge_type <>", value, "knowledgeType");
            return (Criteria) this;
        }

        public Criteria andKnowledgeTypeGreaterThan(Integer value) {
            addCriterion("knowledge_type >", value, "knowledgeType");
            return (Criteria) this;
        }

        public Criteria andKnowledgeTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("knowledge_type >=", value, "knowledgeType");
            return (Criteria) this;
        }

        public Criteria andKnowledgeTypeLessThan(Integer value) {
            addCriterion("knowledge_type <", value, "knowledgeType");
            return (Criteria) this;
        }

        public Criteria andKnowledgeTypeLessThanOrEqualTo(Integer value) {
            addCriterion("knowledge_type <=", value, "knowledgeType");
            return (Criteria) this;
        }

        public Criteria andKnowledgeTypeIn(List<Integer> values) {
            addCriterion("knowledge_type in", values, "knowledgeType");
            return (Criteria) this;
        }

        public Criteria andKnowledgeTypeNotIn(List<Integer> values) {
            addCriterion("knowledge_type not in", values, "knowledgeType");
            return (Criteria) this;
        }

        public Criteria andKnowledgeTypeBetween(Integer value1, Integer value2) {
            addCriterion("knowledge_type between", value1, value2, "knowledgeType");
            return (Criteria) this;
        }

        public Criteria andKnowledgeTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("knowledge_type not between", value1, value2, "knowledgeType");
            return (Criteria) this;
        }

        public Criteria andIsSuccessIsNull() {
            addCriterion("is_success is null");
            return (Criteria) this;
        }

        public Criteria andIsSuccessIsNotNull() {
            addCriterion("is_success is not null");
            return (Criteria) this;
        }

        public Criteria andIsSuccessEqualTo(Boolean value) {
            addCriterion("is_success =", value, "isSuccess");
            return (Criteria) this;
        }

        public Criteria andIsSuccessNotEqualTo(Boolean value) {
            addCriterion("is_success <>", value, "isSuccess");
            return (Criteria) this;
        }

        public Criteria andIsSuccessGreaterThan(Boolean value) {
            addCriterion("is_success >", value, "isSuccess");
            return (Criteria) this;
        }

        public Criteria andIsSuccessGreaterThanOrEqualTo(Boolean value) {
            addCriterion("is_success >=", value, "isSuccess");
            return (Criteria) this;
        }

        public Criteria andIsSuccessLessThan(Boolean value) {
            addCriterion("is_success <", value, "isSuccess");
            return (Criteria) this;
        }

        public Criteria andIsSuccessLessThanOrEqualTo(Boolean value) {
            addCriterion("is_success <=", value, "isSuccess");
            return (Criteria) this;
        }

        public Criteria andIsSuccessIn(List<Boolean> values) {
            addCriterion("is_success in", values, "isSuccess");
            return (Criteria) this;
        }

        public Criteria andIsSuccessNotIn(List<Boolean> values) {
            addCriterion("is_success not in", values, "isSuccess");
            return (Criteria) this;
        }

        public Criteria andIsSuccessBetween(Boolean value1, Boolean value2) {
            addCriterion("is_success between", value1, value2, "isSuccess");
            return (Criteria) this;
        }

        public Criteria andIsSuccessNotBetween(Boolean value1, Boolean value2) {
            addCriterion("is_success not between", value1, value2, "isSuccess");
            return (Criteria) this;
        }

        public Criteria andIsContentQuantifiedIsNull() {
            addCriterion("is_content_quantified is null");
            return (Criteria) this;
        }

        public Criteria andIsContentQuantifiedIsNotNull() {
            addCriterion("is_content_quantified is not null");
            return (Criteria) this;
        }

        public Criteria andIsContentQuantifiedEqualTo(Boolean value) {
            addCriterion("is_content_quantified =", value, "isContentQuantified");
            return (Criteria) this;
        }

        public Criteria andIsContentQuantifiedNotEqualTo(Boolean value) {
            addCriterion("is_content_quantified <>", value, "isContentQuantified");
            return (Criteria) this;
        }

        public Criteria andIsContentQuantifiedGreaterThan(Boolean value) {
            addCriterion("is_content_quantified >", value, "isContentQuantified");
            return (Criteria) this;
        }

        public Criteria andIsContentQuantifiedGreaterThanOrEqualTo(Boolean value) {
            addCriterion("is_content_quantified >=", value, "isContentQuantified");
            return (Criteria) this;
        }

        public Criteria andIsContentQuantifiedLessThan(Boolean value) {
            addCriterion("is_content_quantified <", value, "isContentQuantified");
            return (Criteria) this;
        }

        public Criteria andIsContentQuantifiedLessThanOrEqualTo(Boolean value) {
            addCriterion("is_content_quantified <=", value, "isContentQuantified");
            return (Criteria) this;
        }

        public Criteria andIsContentQuantifiedIn(List<Boolean> values) {
            addCriterion("is_content_quantified in", values, "isContentQuantified");
            return (Criteria) this;
        }

        public Criteria andIsContentQuantifiedNotIn(List<Boolean> values) {
            addCriterion("is_content_quantified not in", values, "isContentQuantified");
            return (Criteria) this;
        }

        public Criteria andIsContentQuantifiedBetween(Boolean value1, Boolean value2) {
            addCriterion("is_content_quantified between", value1, value2, "isContentQuantified");
            return (Criteria) this;
        }

        public Criteria andIsContentQuantifiedNotBetween(Boolean value1, Boolean value2) {
            addCriterion("is_content_quantified not between", value1, value2, "isContentQuantified");
            return (Criteria) this;
        }

        public Criteria andPriorityIsNull() {
            addCriterion("priority is null");
            return (Criteria) this;
        }

        public Criteria andPriorityIsNotNull() {
            addCriterion("priority is not null");
            return (Criteria) this;
        }

        public Criteria andPriorityEqualTo(Integer value) {
            addCriterion("priority =", value, "priority");
            return (Criteria) this;
        }

        public Criteria andPriorityNotEqualTo(Integer value) {
            addCriterion("priority <>", value, "priority");
            return (Criteria) this;
        }

        public Criteria andPriorityGreaterThan(Integer value) {
            addCriterion("priority >", value, "priority");
            return (Criteria) this;
        }

        public Criteria andPriorityGreaterThanOrEqualTo(Integer value) {
            addCriterion("priority >=", value, "priority");
            return (Criteria) this;
        }

        public Criteria andPriorityLessThan(Integer value) {
            addCriterion("priority <", value, "priority");
            return (Criteria) this;
        }

        public Criteria andPriorityLessThanOrEqualTo(Integer value) {
            addCriterion("priority <=", value, "priority");
            return (Criteria) this;
        }

        public Criteria andPriorityIn(List<Integer> values) {
            addCriterion("priority in", values, "priority");
            return (Criteria) this;
        }

        public Criteria andPriorityNotIn(List<Integer> values) {
            addCriterion("priority not in", values, "priority");
            return (Criteria) this;
        }

        public Criteria andPriorityBetween(Integer value1, Integer value2) {
            addCriterion("priority between", value1, value2, "priority");
            return (Criteria) this;
        }

        public Criteria andPriorityNotBetween(Integer value1, Integer value2) {
            addCriterion("priority not between", value1, value2, "priority");
            return (Criteria) this;
        }

        public Criteria andConfidenceIsNull() {
            addCriterion("confidence is null");
            return (Criteria) this;
        }

        public Criteria andConfidenceIsNotNull() {
            addCriterion("confidence is not null");
            return (Criteria) this;
        }

        public Criteria andConfidenceEqualTo(Float value) {
            addCriterion("confidence =", value, "confidence");
            return (Criteria) this;
        }

        public Criteria andConfidenceNotEqualTo(Float value) {
            addCriterion("confidence <>", value, "confidence");
            return (Criteria) this;
        }

        public Criteria andConfidenceGreaterThan(Float value) {
            addCriterion("confidence >", value, "confidence");
            return (Criteria) this;
        }

        public Criteria andConfidenceGreaterThanOrEqualTo(Float value) {
            addCriterion("confidence >=", value, "confidence");
            return (Criteria) this;
        }

        public Criteria andConfidenceLessThan(Float value) {
            addCriterion("confidence <", value, "confidence");
            return (Criteria) this;
        }

        public Criteria andConfidenceLessThanOrEqualTo(Float value) {
            addCriterion("confidence <=", value, "confidence");
            return (Criteria) this;
        }

        public Criteria andConfidenceIn(List<Float> values) {
            addCriterion("confidence in", values, "confidence");
            return (Criteria) this;
        }

        public Criteria andConfidenceNotIn(List<Float> values) {
            addCriterion("confidence not in", values, "confidence");
            return (Criteria) this;
        }

        public Criteria andConfidenceBetween(Float value1, Float value2) {
            addCriterion("confidence between", value1, value2, "confidence");
            return (Criteria) this;
        }

        public Criteria andConfidenceNotBetween(Float value1, Float value2) {
            addCriterion("confidence not between", value1, value2, "confidence");
            return (Criteria) this;
        }

        public Criteria andPublishTimeIsNull() {
            addCriterion("publish_time is null");
            return (Criteria) this;
        }

        public Criteria andPublishTimeIsNotNull() {
            addCriterion("publish_time is not null");
            return (Criteria) this;
        }

        public Criteria andPublishTimeEqualTo(Date value) {
            addCriterion("publish_time =", value, "publishTime");
            return (Criteria) this;
        }

        public Criteria andPublishTimeNotEqualTo(Date value) {
            addCriterion("publish_time <>", value, "publishTime");
            return (Criteria) this;
        }

        public Criteria andPublishTimeGreaterThan(Date value) {
            addCriterion("publish_time >", value, "publishTime");
            return (Criteria) this;
        }

        public Criteria andPublishTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("publish_time >=", value, "publishTime");
            return (Criteria) this;
        }

        public Criteria andPublishTimeLessThan(Date value) {
            addCriterion("publish_time <", value, "publishTime");
            return (Criteria) this;
        }

        public Criteria andPublishTimeLessThanOrEqualTo(Date value) {
            addCriterion("publish_time <=", value, "publishTime");
            return (Criteria) this;
        }

        public Criteria andPublishTimeIn(List<Date> values) {
            addCriterion("publish_time in", values, "publishTime");
            return (Criteria) this;
        }

        public Criteria andPublishTimeNotIn(List<Date> values) {
            addCriterion("publish_time not in", values, "publishTime");
            return (Criteria) this;
        }

        public Criteria andPublishTimeBetween(Date value1, Date value2) {
            addCriterion("publish_time between", value1, value2, "publishTime");
            return (Criteria) this;
        }

        public Criteria andPublishTimeNotBetween(Date value1, Date value2) {
            addCriterion("publish_time not between", value1, value2, "publishTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andResourceContentIsNull() {
            addCriterion("resource_content is null");
            return (Criteria) this;
        }

        public Criteria andResourceContentIsNotNull() {
            addCriterion("resource_content is not null");
            return (Criteria) this;
        }

        public Criteria andResourceContentEqualTo(String value) {
            addCriterion("resource_content =", value, "resourceContent");
            return (Criteria) this;
        }

        public Criteria andResourceContentNotEqualTo(String value) {
            addCriterion("resource_content <>", value, "resourceContent");
            return (Criteria) this;
        }

        public Criteria andResourceContentGreaterThan(String value) {
            addCriterion("resource_content >", value, "resourceContent");
            return (Criteria) this;
        }

        public Criteria andResourceContentGreaterThanOrEqualTo(String value) {
            addCriterion("resource_content >=", value, "resourceContent");
            return (Criteria) this;
        }

        public Criteria andResourceContentLessThan(String value) {
            addCriterion("resource_content <", value, "resourceContent");
            return (Criteria) this;
        }

        public Criteria andResourceContentLessThanOrEqualTo(String value) {
            addCriterion("resource_content <=", value, "resourceContent");
            return (Criteria) this;
        }

        public Criteria andResourceContentLike(String value) {
            addCriterion("resource_content like", value, "resourceContent");
            return (Criteria) this;
        }

        public Criteria andResourceContentNotLike(String value) {
            addCriterion("resource_content not like", value, "resourceContent");
            return (Criteria) this;
        }

        public Criteria andResourceContentIn(List<String> values) {
            addCriterion("resource_content in", values, "resourceContent");
            return (Criteria) this;
        }

        public Criteria andResourceContentNotIn(List<String> values) {
            addCriterion("resource_content not in", values, "resourceContent");
            return (Criteria) this;
        }

        public Criteria andResourceContentBetween(String value1, String value2) {
            addCriterion("resource_content between", value1, value2, "resourceContent");
            return (Criteria) this;
        }

        public Criteria andResourceContentNotBetween(String value1, String value2) {
            addCriterion("resource_content not between", value1, value2, "resourceContent");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}