package com.sankuai.dzhealth.ai.service.infrastructure.phoneCallTask;



import com.google.common.collect.Lists;
import com.sankuai.dzhealth.ai.service.infrastructure.dal.entity.ChatPhoneCallTaskEntity;
import com.sankuai.dzhealth.ai.service.infrastructure.dal.example.ChatPhoneCallTaskEntityExample;
import com.sankuai.dzhealth.ai.service.infrastructure.dal.mapper.ChatPhoneCallTaskEntityMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;


@Repository
@Slf4j
public class ChatPhoneCallTaskRepository {
    @Resource
    private ChatPhoneCallTaskEntityMapper phoneCallTaskEntityMapper;

    //保存任务
    public void saveTask(ChatPhoneCallTaskEntity entity) {
        if (entity == null) {
            return;
        }
        phoneCallTaskEntityMapper.insertSelective(entity);
    }

    //根据任务id获取任务
    public ChatPhoneCallTaskEntity getTaskById(Long taskId) {
        return phoneCallTaskEntityMapper.selectByPrimaryKey(taskId);
    }


    //根据联络id获取任务
    public ChatPhoneCallTaskEntity getTaskByContactId(String contactId) {
        ChatPhoneCallTaskEntityExample example = new ChatPhoneCallTaskEntityExample();
        example.createCriteria().andContactIdEqualTo(contactId);
        List<ChatPhoneCallTaskEntity> entityList=phoneCallTaskEntityMapper.selectByExample(example);
        if(CollectionUtils.isEmpty(entityList))
        {
            return null;
        }
        return entityList.get(0);
    }


    //根据任务id更新联络id和任务状态
    public Boolean updateStatusByTaskId(Long taskId, Integer status) {
        ChatPhoneCallTaskEntity entity =getTaskById(taskId);
        if (entity == null) {
            return false;
        }
        entity.setStatus(status);
        return phoneCallTaskEntityMapper.updateByPrimaryKeySelective(entity) > 0;
    }

    //根据联络id更新任务状态
    public Boolean updateStatusByContactId(String contactId, Integer status) {
        ChatPhoneCallTaskEntity entity = getTaskByContactId(contactId);
        if (entity == null) {
            return false;
        }
        entity.setStatus(status);
        return phoneCallTaskEntityMapper.updateByPrimaryKeySelective(entity) > 0;
    }

    public List<ChatPhoneCallTaskEntity> queryTaskBySessionId(Long sessionId, List<Integer> statusList) {
        if (CollectionUtils.isEmpty(statusList) || sessionId == null) {
            return Lists.newArrayList();
        }
        ChatPhoneCallTaskEntityExample example = new ChatPhoneCallTaskEntityExample();
        example.createCriteria().andSessionIdEqualTo(sessionId).andStatusIn(statusList);
        return phoneCallTaskEntityMapper.selectByExample(example);
    }


    //更新填充的字段
    public Boolean updateTask(ChatPhoneCallTaskEntity updateEntity) {
        if (updateEntity == null) {
            return false;
        }
        if (updateEntity.getId() == null) {
            return false;
        }
        ChatPhoneCallTaskEntity entity = getTaskById(updateEntity.getId());
        if (entity == null) {
            return false;
        }
        // 更新字段
        if (updateEntity.getContactId() != null) {
            entity.setContactId(updateEntity.getContactId());
        }
        if (updateEntity.getMessageId() != null) {
            entity.setMessageId(updateEntity.getMessageId());
        }
        if (updateEntity.getSessionId() != null) {
            entity.setSessionId(updateEntity.getSessionId());
        }
        if (updateEntity.getStatus() != null) {
            entity.setStatus(updateEntity.getStatus());
        }
        if (updateEntity.getCallCount() != null) {
            entity.setCallCount(updateEntity.getCallCount());
        }
        if (updateEntity.getMtShopId() != null) {
            entity.setMtShopId(updateEntity.getMtShopId());
        }
        if (updateEntity.getStartTime() != null) {
            entity.setStartTime(updateEntity.getStartTime());
        }
        if (updateEntity.getEndTime() != null) {
            entity.setEndTime(updateEntity.getEndTime());
        }
        if (updateEntity.getTalkingTimeLen() != null) {
            entity.setTalkingTimeLen(updateEntity.getTalkingTimeLen());
        }
        if (updateEntity.getReleaseReason() != null) {
            entity.setReleaseReason(updateEntity.getReleaseReason());
        }
        if (updateEntity.getQuestion() != null) {
            entity.setQuestion(updateEntity.getQuestion());
        }
        if (updateEntity.getMediatxt() != null) {
            entity.setMediatxt(updateEntity.getMediatxt());
        }
        if (updateEntity.getDialogRawData() != null) {
            entity.setDialogRawData(updateEntity.getDialogRawData());
        }
        if (updateEntity.getDialogRefinedData()!=null) {
            entity.setDialogRefinedData(updateEntity.getDialogRefinedData());
        }
        return phoneCallTaskEntityMapper.updateByPrimaryKeySelective(entity) > 0;
    }

}
