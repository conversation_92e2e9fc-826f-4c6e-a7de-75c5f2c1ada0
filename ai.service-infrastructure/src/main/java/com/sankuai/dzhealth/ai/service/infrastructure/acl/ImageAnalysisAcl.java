package com.sankuai.dzhealth.ai.service.infrastructure.acl;

import com.sankuai.beautycontent.gpt.api.informationextraction.MultiModalAnalysisService;
import com.sankuai.beautycontent.gpt.dto.informationextraction.MultiModalAnalysisResponse;
import com.sankuai.beautycontent.gpt.dto.informationextraction.MultiModalAnalysisResult;
import com.sankuai.beautycontent.gpt.request.informationextraction.MultiModalAnalysisRequest;
import com.meituan.mdp.boot.starter.thrift.annotation.MdpThriftClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

/**
 * ACL 封装图片解析服务调用
 */
@Slf4j
@Component
public class ImageAnalysisAcl {

    @MdpThriftClient(remoteAppKey = "com.sankuai.beautycontent.function", timeout = 60000)
    private MultiModalAnalysisService analysisService;

    /**
     * 分析单张图片
     * @param imageUrl 图片 URL
     * @param context  图片上下文（可选，用于提示）
     * @return fullAnalysis ；若失败返回 null
     */
    public String getFullAnalysisOfImage(String imageUrl, String context) {
        if (analysisService == null || StringUtils.isBlank(imageUrl)) {
            return null;
        }
        try {
            MultiModalAnalysisRequest req = new MultiModalAnalysisRequest();
            req.setBizType("medical.pic.summary");
            req.setContext(context);
            req.setImageUrl(imageUrl);
            MultiModalAnalysisResponse resp = analysisService.analyzeMultiModalContent(req);
            if (resp.getCode() == 200 && resp.getData() != null) {
                MultiModalAnalysisResult data = resp.getData();
                return "\n\n" +data.getFullAnalysis();
            }
        } catch (Exception e) {
            log.warn("image analysis failed, url={}", imageUrl, e);
        }
        return null;
    }

    /**
     * 仅根据图片 URL 分析，等价于 analyze(imageUrl, null)
     */
    public String getFullAnalysisOfImage(String imageUrl) {
        return getFullAnalysisOfImage(imageUrl, null);
    }

    /**
     * 返回完整 MultiModalAnalysisResult，供上层工具做不同颗粒度封装
     */
    public MultiModalAnalysisResult analyzeResult(String imageUrl) {
        if (analysisService == null || StringUtils.isBlank(imageUrl)) {
            return null;
        }
        try {
            MultiModalAnalysisRequest req = new MultiModalAnalysisRequest();
            req.setBizType("medical.pic.summary");
            req.setImageUrl(imageUrl);
            MultiModalAnalysisResponse resp = analysisService.analyzeMultiModalContent(req);
            if (resp.getCode() == 200) {
                return resp.getData();
            }
        } catch (Exception e) {
            log.warn("image analysis result failed url={}", imageUrl, e);
        }
        return null;
    }
} 