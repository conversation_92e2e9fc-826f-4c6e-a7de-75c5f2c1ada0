package com.sankuai.dzhealth.ai.service.infrastructure.dal.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 *
 *   表名: refined_knowledge
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
public class RefinedKnowledgeDO {
    /**
     *   字段: id
     */
    private Long id;

    /**
     *   字段: corpus_id
     *   说明: 语料ID
     */
    private Long corpusId;

    /**
     *   字段: resource_id
     *   说明: 资源库ID
     */
    private Long resourceId;

    /**
     *   字段: resource_channel
     *   说明: 资源库渠道
     */
    private String resourceChannel;

    /**
     *   字段: resource_uri
     *   说明: 资源库uri
     */
    private String resourceUri;

    /**
     *   字段: knowledge_type
     *   说明: 知识类型,1-流程图,2-表格
     */
    private Integer knowledgeType;

    /**
     *   字段: is_success
     *   说明: 处理是否成功：1-成功，0-失败
     */
    private Boolean isSuccess;

    /**
     *   字段: is_content_quantified
     *   说明: 内容是否量化
     */
    private Boolean isContentQuantified;

    /**
     *   字段: priority
     *   说明: 优先级
     */
    private Integer priority;

    /**
     *   字段: confidence
     *   说明: 置信度
     */
    private Float confidence;

    /**
     *   字段: publish_time
     *   说明: 信息发布时间
     */
    private Date publishTime;

    /**
     *   字段: create_time
     *   说明: 创建时间
     */
    private Date createTime;

    /**
     *   字段: update_time
     *   说明: 更新时间
     */
    private Date updateTime;

    /**
     *   字段: resource_content
     *   说明: 来源内容
     */
    private String resourceContent;
}