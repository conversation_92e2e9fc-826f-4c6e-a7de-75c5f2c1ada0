package com.sankuai.dzhealth.ai.service.infrastructure.dal.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.Date;

/**
 *
 *   表名: answer_context
 */
@SuperBuilder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AiAnswerContextEntity {
    /**
     *   字段: id
     *   说明: 主键
     */
    private Long id;

    /**
     *   字段: userId
     *   说明: 用户ID
     */
    private String userid;

    /**
     *   字段: lat
     *   说明: 用户选择的地址纬度
     */
    private Double lat;

    /**
     *   字段: lng
     *   说明: 用户选择的地址经度
     */
    private Double lng;

    /**
     *   字段: clientType
     *   说明: 客户端类型
     */
    private String clienttype;

    /**
     *   字段: uuid
     *   说明: UUID美团侧唯一设备ID
     */
    private String uuid;

    /**
     *   字段: appVersion
     *   说明: 应用版本号
     */
    private String appversion;

    /**
     *   字段: cityId
     *   说明: 用户选择的地址对应的二级城市 ID
     */
    private Integer cityid;

    /**
     *   字段: sessionId
     *   说明: 会话的唯一标识
     */
    private Long sessionid;

    /**
     *   字段: msgId
     *   说明: 消息的唯一标识
     */
    private Long msgid;

    /**
     *   字段: requestId
     *   说明: 前端请求的唯一标识
     */
    private String requestid;

    /**
     *   字段: extension
     *   说明: 扩展信息
     */
    private String extension;

    /**
     *   字段: bizScene
     *   说明: 业务场景标识
     */
    private String bizscene;

    /**
     *   字段: requestTime
     *   说明: 请求的时间戳
     */
    private String requesttime;

    /**
     *   字段: type
     *   说明: 请求消息的类型
     */
    private Integer type;

    /**
     *   字段: role
     *   说明: 用户角色
     */
    private String role;

    /**
     *   字段: msgContext
     *   说明: 返回的消息上下文
     */
    private String msgcontext;

    /**
     *   字段: shopId
     *   说明: 商户的唯一标识
     */
    private Long shopid;

    /**
     *   字段: mtShopId
     *   说明: 美团商户的唯一标识
     */
    private Long mtshopid;

    /**
     *   字段: stream
     *   说明: 是否为流式数据
     */
    private Boolean stream;

    /**
     *   字段: platform
     *   说明: 平台标识
     */
    private Integer platform;

    /**
     *   字段: conversationIdStr
     *   说明: 会话的唯一标识字符串
     */
    private String conversationidstr;

    /**
     *   字段: messageIdStr
     *   说明: 消息的唯一标识字符串
     */
    private String messageidstr;

    /**
     *   字段: traceId
     *   说明: TraceId
     */
    private String traceid;

    /**
     *   字段: spans
     *   说明: Spans信息
     */
    private String spans;

    /**
     *   字段: businessType
     *   说明: 业务关联的类型
     */
    private String businesstype;

    /**
     *   字段: businessId
     *   说明: 关联的业务 ID
     */
    private String businessid;

    /**
     *   字段: departName2IdMap
     *   说明: 部门名称到 ID 的映射关系
     */
    private String departname2idmap;

    /**
     *   字段: stdDepartName2IdMap
     *   说明: 标准部门名称到 ID 的映射关系
     */
    private String stddepartname2idmap;

    /**
     *   字段: status
     *   说明: 当前状态信息
     */
    private String status;

    /**
     *   字段: searchInfo
     *   说明: 搜索策略相关的信息
     */
    private String searchinfo;

    /**
     *   字段: ragInfo
     *   说明: Rag策略相关的信息
     */
    private String raginfo;

    /**
     *   字段: created_at
     *   说明: 创建时间
     */
    private Date createdAt;

    /**
     *   字段: updated_at
     *   说明: 更新时间
     */
    private Date updatedAt;
}