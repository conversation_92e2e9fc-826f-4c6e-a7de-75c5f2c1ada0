package com.sankuai.dzhealth.ai.service.agent.infrastructure.repository.appointment;


import com.sankuai.dzhealth.ai.service.agent.infrastructure.cache.PhoneEncryptionUtils;
import com.sankuai.dzhealth.ai.service.agent.infrastructure.enums.AppointmentStatusEnum;
import com.sankuai.dzhealth.ai.service.agent.infrastructure.enums.ProductNameEnum;
import com.sankuai.dzhealth.ai.service.agent.infrastructure.dal.entity.AppointmentInfoDO;
import com.sankuai.dzhealth.ai.service.agent.infrastructure.dal.example.AppointmentInfoDOExample;
import com.sankuai.dzhealth.ai.service.agent.infrastructure.dal.mapper.AppointmentInfoDOMapper;
import com.sankuai.dzhealth.ai.service.agent.infrastructure.model.AppointmentInfoBO;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 预约信息仓储
 *
 * @author: jiyizhou
 * @time: 2025/7/10 21:00
 * @version: 0.0.1
 */
@Slf4j
@Repository
public class AppointmentInfoRepository {

    @Autowired
    private AppointmentInfoDOMapper appointmentInfoDOMapper;

    @Autowired
    private PhoneEncryptionUtils phoneEncryptionService;

    /**
     * 保存预约信息
     *
     * @param bo 预约信息业务对象
     * @return 保存后的记录ID
     */
    public Long save(AppointmentInfoBO bo) {
        if (bo == null) {
            log.info("保存预约信息失败：业务对象为空");
            return null;
        }

        try {
            // 转换为数据库实体
            AppointmentInfoDO entity = convertToEntity(bo);
            if (entity == null) {
                log.info("保存预约信息失败：转换实体为空");
                return null;
            }

            // 插入数据库
            appointmentInfoDOMapper.insertSelective(entity);

            log.info("保存预约信息成功, id={}, userId={}, sessionId={}",
                    entity.getId(), entity.getUserId(), entity.getSessionId());

            return entity.getId();
        } catch (Exception e) {
            log.error("保存预约信息异常, bo={}", bo, e);
            throw new RuntimeException("保存预约信息失败", e);
        }
    }

    public boolean findAppointmentBySessionId(String sessionId) {
        if (StringUtils.isEmpty(sessionId)) {
            return false;
        }

        AppointmentInfoDOExample example = new AppointmentInfoDOExample();
        example.createCriteria().andSessionIdEqualTo(sessionId);

        List<AppointmentInfoDO> entityList = appointmentInfoDOMapper.selectByExampleWithBLOBs(example);
        return CollectionUtils.isNotEmpty(entityList);
    }


    /**
     * 根据msgId和userId查询预约信息
     *
     * @param msgId 消息编号
     * @param userId 用户编号
     * @return 预约信息业务对象
     */
    public AppointmentInfoBO findByMsgIdAndUserId(String msgId, Long userId) {
        if (StringUtils.isBlank(msgId) || userId == null) {
            log.info("查询预约信息失败：参数为空, msgId={}, userId={}", msgId, userId);
            return null;
        }

        try {
            // 构建查询条件
            AppointmentInfoDOExample example = new AppointmentInfoDOExample();
            example.createCriteria()
                    .andMsgIdEqualTo(msgId)
                    .andUserIdEqualTo(userId);

            // 查询数据库
            List<AppointmentInfoDO> entityList = appointmentInfoDOMapper.selectByExampleWithBLOBs(example);

            if (CollectionUtils.isEmpty(entityList)) {
                log.info("未找到预约信息, msgId={}, userId={}", msgId, userId);
                return null;
            }

            // 取第一条记录（理论上msgId+userId应该唯一）
            AppointmentInfoDO entity = entityList.get(0);

            // 转换为业务对象
            AppointmentInfoBO bo = convertToBO(entity);

            log.info("查询预约信息成功, id={}, msgId={}, userId={}",
                    entity.getId(), msgId, userId);

            return bo;
        } catch (Exception e) {
            log.error("查询预约信息异常, msgId={}, userId={}", msgId, userId, e);
            return null;
        }
    }

    /**
     * 根据reservedMsgId和userId查询预约信息
     *
     * @param reservedMsgId 帮约消息编号
     * @param userId 用户编号
     * @return 预约信息业务对象
     */
    public AppointmentInfoBO findByReservedMsgIdAndUserId(String reservedMsgId, Long userId) {
        if (StringUtils.isBlank(reservedMsgId) || userId == null) {
            log.info("查询预约信息失败：参数为空, reservedMsgId={}, userId={}", reservedMsgId, userId);
            return null;
        }

        try {
            // 构建查询条件
            AppointmentInfoDOExample example = new AppointmentInfoDOExample();
            example.createCriteria()
                    .andReservedMsgIdEqualTo(reservedMsgId)
                    .andUserIdEqualTo(userId);

            // 查询数据库
            List<AppointmentInfoDO> entityList = appointmentInfoDOMapper.selectByExampleWithBLOBs(example);

            if (CollectionUtils.isEmpty(entityList)) {
                log.info("未找到预约信息, reservedMsgId={}, userId={}", reservedMsgId, userId);
                return null;
            }

            // 取第一条记录（理论上reservedMsgId+userId应该唯一）
            AppointmentInfoDO entity = entityList.get(0);

            // 转换为业务对象
            AppointmentInfoBO bo = convertToBO(entity);

            log.info("查询预约信息成功, id={}, reservedMsgId={}, userId={}",
                    entity.getId(), reservedMsgId, userId);

            return bo;
        } catch (Exception e) {
            log.error("查询预约信息异常, reservedMsgId={}, userId={}", reservedMsgId, userId, e);
            return null;
        }
    }

    /**
     * 根据msgId和userId查询预约信息
     *
     * @param taskId 外呼电话任务id
     * @return 预约信息业务对象
     */
    public AppointmentInfoBO findByTaskId(Long taskId) {
        if (taskId == null) {
            log.info("查询预约信息失败：参数为空, taskId={}",taskId);
            return null;
        }

        try {
            // 构建查询条件
            AppointmentInfoDOExample example = new AppointmentInfoDOExample();
            example.createCriteria()
                    .andTaskIdEqualTo(taskId);

            // 查询数据库
            List<AppointmentInfoDO> entityList = appointmentInfoDOMapper.selectByExampleWithBLOBs(example);

            if (CollectionUtils.isEmpty(entityList)) {
                log.info("查询预约信息失败：参数为空, taskId={}",taskId);
                return null;
            }
            AppointmentInfoDO entity = entityList.get(0);
            // 转换为业务对象
            AppointmentInfoBO bo = convertToBO(entity);
            return bo;
        } catch (Exception e) {
            log.error("查询预约信息异常, taskId={}", taskId, e);
            return null;
        }
    }



    /**
     * 根据leadId查询预约信息
     *
     * @param leadId 预约单Id
     * @return 预约信息业务对象
     */
    public AppointmentInfoBO findByLeadId(Long leadId) {
        if (leadId == null) {
            log.info("查询预约信息失败：参数为空, leadId={}",leadId);
            return null;
        }
        try {
            // 构建查询条件
            AppointmentInfoDOExample example = new AppointmentInfoDOExample();
            example.createCriteria()
                    .andLeadIdEqualTo(leadId);

            // 查询数据库
            List<AppointmentInfoDO> entityList = appointmentInfoDOMapper.selectByExampleWithBLOBs(example);

            if (CollectionUtils.isEmpty(entityList)) {
                log.info("查询预约信息失败：参数为空, leadId={}",leadId);
                return null;
            }
            AppointmentInfoDO entity = entityList.get(0);
            // 转换为业务对象
            AppointmentInfoBO bo = convertToBO(entity);
            return bo;
        } catch (Exception e) {
            log.error("查询预约信息异常, leadId={}", leadId, e);
            return null;
        }
    }




    /**
     * 更新预约信息（只更新非空字段）
     *
     * @param updateBO 要更新的预约信息业务对象
     * @return 是否更新成功
     */
    public Boolean updateAppointmentInfo(AppointmentInfoBO updateBO) {
        if (updateBO == null) {
            log.info("更新预约信息失败：业务对象为空");
            return false;
        }

        if (updateBO.getId() == null) {
            log.info("更新预约信息失败：主键ID为空");
            return false;
        }

        try {
            // 先检查记录是否存在
            AppointmentInfoDO existingEntity = appointmentInfoDOMapper.selectByPrimaryKey(updateBO.getId());
            if (existingEntity == null) {
                log.info("更新预约信息失败：记录不存在, id={}", updateBO.getId());
                return false;
            }

            // 直接转换BO为DO，利用MyBatis的updateByPrimaryKeySelective进行选择性更新
            AppointmentInfoDO updateEntity = convertToEntity(updateBO);

            // 执行选择性更新（XML中已处理null值判断）
            int updateCount = appointmentInfoDOMapper.updateByPrimaryKeySelective(updateEntity);

            boolean success = updateCount > 0;
            log.info("更新预约信息{}, id={}, updateCount={}",
                    success ? "成功" : "失败", updateBO.getId(), updateCount);

            return success;
        } catch (Exception e) {
            log.error("更新预约信息异常, id={}", updateBO.getId(), e);
            throw new RuntimeException("更新预约信息失败", e);
        }
    }

    /**
     * 业务对象转换为数据库实体（预处理）
     *
     * @param bo 业务对象
     * @return 数据库实体
     */
    public AppointmentInfoDO convertToEntity(AppointmentInfoBO bo) {
        if (bo == null) {
            return null;
        }

        AppointmentInfoDO entity = AppointmentInfoDO.builder()
                .id(bo.getId())
                .userId(bo.getUserId())
                .sessionId(bo.getSessionId())
                .msgId(bo.getMsgId())
                .reservedMsgId(bo.getReservedMsgId())
                .appointmentStartTime(bo.getAppointmentStartTime())
                .appointmentEndTime(bo.getAppointmentEndTime())
                .positionTxt(bo.getPositionTxt())
                .lng(bo.getLng())
                .lat(bo.getLat())
                .personDesc(bo.getPersonDesc())
                .filterItems(bo.getFilterItems())
                .successShopId(bo.getSuccessShopId())
                .taskId(bo.getTaskId())
                .cardInfo(bo.getCardInfo())
                .leadId(bo.getLeadId())
                .remark(bo.getRemark())
                .cityid(bo.getCityId())
                .searchword(bo.getSearchword())
                .distance(bo.getDistance())
                .price(bo.getPrice())
                .build();

        // 枚举转换为数字
        if (bo.getProductName() != null) {
            entity.setProductName(bo.getProductName().getCode());
        }

        if (bo.getStatus() != null) {
            entity.setStatus(bo.getStatus().getCode());
        }

        // List<Long>转换为字符串
        if (bo.getShopIdList() != null && !bo.getShopIdList().isEmpty()) {
            String shopIdListStr = bo.getShopIdList().stream()
                    .map(String::valueOf)
                    .collect(Collectors.joining("、"));
            entity.setShopIdList(shopIdListStr);
        }

        if (bo.getPhone() != null) {
            String encrypt = phoneEncryptionService.encrypt(bo.getPhone());
            if (encrypt == null) {
                log.error("加密手机号失败");
            }
            entity.setPhone(encrypt);
        }

        return entity;
    }

    /**
     * 数据库实体转换为业务对象（后处理）
     *
     * @param entity 数据库实体
     * @return 业务对象
     */
    public AppointmentInfoBO convertToBO(AppointmentInfoDO entity) {
        if (entity == null) {
            return null;
        }

        AppointmentInfoBO bo = AppointmentInfoBO.builder()
                .id(entity.getId())
                .userId(entity.getUserId())
                .sessionId(entity.getSessionId())
                .msgId(entity.getMsgId())
                .reservedMsgId(entity.getReservedMsgId())
                .appointmentStartTime(entity.getAppointmentStartTime())
                .appointmentEndTime(entity.getAppointmentEndTime())
                .positionTxt(entity.getPositionTxt())
                .lng(entity.getLng())
                .lat(entity.getLat())
                .personDesc(entity.getPersonDesc())
                .filterItems(entity.getFilterItems())
                .successShopId(entity.getSuccessShopId())
                .taskId(entity.getTaskId())
                .cardInfo(entity.getCardInfo())
                .leadId(entity.getLeadId())
                .remark(entity.getRemark())
                .createTime(entity.getCreateTime())
                .updateTime(entity.getUpdateTime())
                .cityId(entity.getCityid())
                .searchword(entity.getSearchword())
                .distance(entity.getDistance())
                .price(entity.getPrice())
                .build();

        // 数字转换为枚举
        bo.setProductName(ProductNameEnum.fromCode(entity.getProductName()));
        bo.setStatus(AppointmentStatusEnum.fromCode(entity.getStatus()));

        // 字符串转换为List<Long>
        if (StringUtils.isNotBlank(entity.getShopIdList())) {
            try {
                List<Long> shopIdList = Arrays.stream(entity.getShopIdList().split("、"))
                        .map(String::trim)
                        .filter(StringUtils::isNotBlank)
                        .map(Long::valueOf)
                        .collect(Collectors.toList());
                bo.setShopIdList(shopIdList);
            } catch (NumberFormatException e) {
                log.error("解析shopIdList失败: {}", entity.getShopIdList(), e);
                bo.setShopIdList(Collections.emptyList());
            }
        } else {
            bo.setShopIdList(Collections.emptyList());
        }

        if (entity.getPhone() != null) {
            String decrypt = phoneEncryptionService.decrypt(entity.getPhone());
            if (decrypt == null) {
                log.error("解密手机号失败");
            }
            bo.setPhone(decrypt);
        }

        return bo;
    }
}

