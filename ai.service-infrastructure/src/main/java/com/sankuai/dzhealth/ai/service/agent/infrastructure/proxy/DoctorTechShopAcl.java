package com.sankuai.dzhealth.ai.service.agent.infrastructure.proxy;

import com.meituan.mdp.boot.starter.thrift.annotation.MdpThriftClient;
import com.sankuai.medicalcosmetology.base.api.DoctorTechShopService;
import com.sankuai.medicalcosmetology.base.dto.DoctorTechShopRelationDTO;
import com.sankuai.medicalcosmetology.base.dto.RemoteResponse;
import com.sankuai.medicalcosmetology.base.request.DoctorTechShopRelationQueryRequest;
import com.sankuai.technician.info.online.enums.OnlineStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Component
@Slf4j
public class DoctorTechShopAcl {

    @MdpThriftClient(remoteAppKey = "com.sankuai.medicalcosmetology.doctor.function", timeout = 5000, testTimeout = 10000)
    private DoctorTechShopService doctorTechShopService;

    public List<DoctorTechShopRelationDTO> queryTechIdsByMergeDoctorId(List<Long> mergeDoctorIds) {
        return queryTechIdsByMergeDoctorIdAndShopId(mergeDoctorIds, null, null);
    }

    public List<DoctorTechShopRelationDTO> queryTechIdsByMergeDoctorId(List<Long> mergeDoctorIds, List<Integer> filterStatus) {
        return queryTechIdsByMergeDoctorIdAndShopId(mergeDoctorIds, null, filterStatus);
    }

    public List<DoctorTechShopRelationDTO> queryTechIdsByMergeDoctorIdAndShopId(List<Long> mergeDoctorIds, Long shopId, List<Integer> filterStatus) {
        if (CollectionUtils.isEmpty(mergeDoctorIds)) {
            return Collections.emptyList();
        }
        try {
            DoctorTechShopRelationQueryRequest request = new DoctorTechShopRelationQueryRequest();
            request.setMergeDoctorIds(mergeDoctorIds);
            if (CollectionUtils.isNotEmpty(filterStatus)) {
                request.setOnlineStatus(filterStatus);
            } else {
                request.setOnlineStatus(Collections.singletonList(OnlineStatusEnum.ONLINE.getCode()));
            }
            RemoteResponse<List<DoctorTechShopRelationDTO>> response = doctorTechShopService.batchQueryDoctorTechShopRelation(request);
            if (response == null || !Boolean.TRUE.equals(response.getSuccess()) || CollectionUtils.isEmpty(response.getData())) {
                log.info("queryTechIdsByMergeDoctorIdAndShopId empty, mergeDoctorIds={}, shopId={}", mergeDoctorIds, shopId);
                return Collections.emptyList();
            }
            return response.getData().stream().filter(item -> shopId == null || shopId <= 0 || Objects.equals(item.getShopId(), shopId)).collect(Collectors.toList());
        } catch (Exception e) {
            log.error("queryTechIdsByMergeDoctorIdAndShopId error, mergeDoctorIds={}, shopId={}", mergeDoctorIds, shopId, e);
            return Collections.emptyList();
        }
    }
}


