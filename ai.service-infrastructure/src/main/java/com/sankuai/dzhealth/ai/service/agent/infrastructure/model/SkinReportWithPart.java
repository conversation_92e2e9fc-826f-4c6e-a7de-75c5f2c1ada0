package com.sankuai.dzhealth.ai.service.agent.infrastructure.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 皮肤报告详细信息
 * 包含主要问题和子问题的详细数据
 *
 * @user: xia<PERSON><PERSON><PERSON>
 * @since: 2025/7/7 19:52
 * @version: 1.0
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SkinReportWithPart {
    /**
     * 问题名称
     */
    private String name;

    /**
     * 总体程度名称
     */
    private String totalDegreeName;


    /**
     * 子问题列表
     */
    private List<SubDegreeInfo> subDegreeList;

    /**
     * 子问题详细信息
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class SubDegreeInfo {

        /**
         * 子问题名称
         */
        private String name;

        /**
         * 部分程度名称
         */
        private String partDegreeName;

    }
}
