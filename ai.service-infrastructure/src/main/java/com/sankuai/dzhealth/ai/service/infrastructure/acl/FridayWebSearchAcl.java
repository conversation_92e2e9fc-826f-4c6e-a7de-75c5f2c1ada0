package com.sankuai.dzhealth.ai.service.infrastructure.acl;

import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.sankuai.dzhealth.ai.service.infrastructure.model.FridaySearchResult;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.WebClient;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR> youtianyu
 * @version : 0.1
 * @since : 2025/3/20
 */
@Slf4j
@Component
public class FridayWebSearchAcl {
    private static final String FRIDAY_SEARCH_URL = "https://aigc.sankuai.com/v1/friday/api/search";

    @Value("$KMS{friday.search.appId}")
    private String appId;

    private final WebClient webClient;

    public FridayWebSearchAcl(WebClient.Builder webClientBuilder) {
        this.webClient = webClientBuilder.build();
    }

    /**
     * 调用Friday搜索API进行网站搜索
     *
     * @param query 用户搜索词
     * @param sites 指定搜索的站点列表
     * @param topK  返回结果数量
     * @return 搜索结果列表
     */
    public List<FridaySearchResult> search(String query, List<String> sites, int topK) {
        Transaction transaction = Cat.newTransaction(getClass().getSimpleName(), "search");
        try {
            // 构造带有site限制的搜索查询
            String siteQuery = StringUtils.EMPTY;
            if (CollectionUtils.isNotEmpty(sites)) {
                siteQuery = " (" + sites.stream().map(site -> "site:" + site).collect(Collectors.joining(" OR ")) + ")";
            }
            String finalQuery = query + siteQuery;

            LocalDateTime now = LocalDateTime.now();
            LocalDateTime twoYearsAgo = now.minusYears(2);

            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            String endTime = now.format(formatter);
            String startTime = twoYearsAgo.format(formatter);

            String freshnessValue = startTime + ".." + endTime;

            // 构造请求体
            SearchRequest request =
                    SearchRequest.builder().query(finalQuery).api("bing-search-pro").topK(topK).isFast(true)
                            .bingSearchProParam(Collections.singletonMap("freshness", freshnessValue)).build();

            // 发送请求并获取响应
            List<FridaySearchResult> results = webClient.post()
                    .uri(FRIDAY_SEARCH_URL)
                    .header(HttpHeaders.AUTHORIZATION, "Bearer " + appId)
                    .header(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                    .bodyValue(request)
                    .retrieve()
                    .bodyToMono(FridaySearchResponse.class)
                    .flatMap(response -> {
                        if (response == null || response.getBingSearchProResults() == null) {
                            log.warn("Friday search response or BingSearchProResults is null for query: {}", finalQuery);
                            return Mono.just(Collections.<FridaySearchResult>emptyList());
                        }
                        BingSearchProResults bingResults = response.getBingSearchProResults();
                        if (bingResults.getWebPages() == null) {
                            log.warn("WebPages is null for query: {}", finalQuery);
                            return Mono.just(Collections.<FridaySearchResult>emptyList());
                        }
                        WebPages webPages = bingResults.getWebPages();
                        List<FridaySearchResult> value = webPages.getValue();
                        return Mono.just(value != null ? value : Collections.<FridaySearchResult>emptyList());
                    })
                    .switchIfEmpty(Mono.just(Collections.<FridaySearchResult>emptyList()))
                    .block();
            transaction.setSuccessStatus();
            log.info("query={},sites={},topK={},results={}", query, sites, topK, results);
            return results != null ? results : Collections.emptyList();
        } catch (Exception e) {
            log.error("query={},sites={},topK={},e={}", query, sites, topK, e, e);
            transaction.setStatus(e);
            return Collections.emptyList();
        } finally {
            transaction.complete();
        }
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class FridaySearchResponse {
        /**
         * 响应码
         */
        private String code;

        /**
         * 响应消息
         */
        private String msg;

        /**
         * 搜索结果列表
         */
        private BingSearchProResults bingSearchProResults;
        
        /**
         * results字段
         * 该字段在API响应中存在但在原始类中未定义
         */
        private Object results;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class BingSearchProResults {
        private WebPages webPages;
    }
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class WebPages {
        private String webSearchUrl;

        private Long totalEstimatedMatches;

        private List<FridaySearchResult> value;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SearchRequest {
        private String query;

        private String api;

        @JsonProperty("top_k")
        private Integer topK;

        @JsonProperty("is_fast")
        private Boolean isFast;

        @JsonProperty("bing_search_pro_param")
        private Map<String, String> bingSearchProParam;
    }

}

