package com.sankuai.dzhealth.ai.service.agent.infrastructure.cache;

import com.dianping.squirrel.client.StoreKey;
import com.dianping.squirrel.client.impl.redis.RedisStoreClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

/**
 * @author:chenwei
 * @time: 2025/7/9 14:56
 * @version: 0.0.1
 */

@Slf4j
@Component
public class RedisIdUtils {

    @Autowired
    @Qualifier("redisClient0")
    private RedisStoreClient redisStoreClient0;

    private static final String ID_CATEGORY = "chat_message_id";

    private static final int MAX_RETRY_TIME = 3;

    public Long nextId() {

        int retryTime = 0;
        while (++retryTime <= MAX_RETRY_TIME) {
            try {
                Long id = redisStoreClient0.incrBy(new StoreKey(ID_CATEGORY, "chatId"), 1);
                if (id != null && id > 0) {
                    return id;
                } else {
                    throw new RuntimeException("redis id 生成异常, 结果:" + id);
                }
            } catch (Exception e) {
                if (e.getMessage() != null && e.getMessage().contains("timeout")) {
                    continue;
                }
                throw e;
            }
        }
        throw new RuntimeException("redis id error:" + MAX_RETRY_TIME);

    }


}
