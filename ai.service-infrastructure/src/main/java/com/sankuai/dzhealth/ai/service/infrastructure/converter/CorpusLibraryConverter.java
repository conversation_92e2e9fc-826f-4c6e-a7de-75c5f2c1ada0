package com.sankuai.dzhealth.ai.service.infrastructure.converter;

import com.sankuai.dzhealth.ai.service.infrastructure.dal.entity.CorpusLibraryDOWithBLOBs;
import com.sankuai.dzhealth.ai.service.infrastructure.model.CorpusLibrary;

public class CorpusLibraryConverter {
    /**
     * 将DO对象转换为领域模型
     *
     * @param corpusLibraryDO DO对象
     * @return 领域模型
     */
    public static CorpusLibrary toDomain(CorpusLibraryDOWithBLOBs corpusLibraryDO) {
        if (corpusLibraryDO == null) {
            return null;
        }

        return CorpusLibrary.builder()
                .id(corpusLibraryDO.getId())
                .sortOrder(corpusLibraryDO.getSortOrder())
                .prevCorpusId(corpusLibraryDO.getPrevCorpusId())
                .resourceId(corpusLibraryDO.getResourceId())
                .resourceChannel(corpusLibraryDO.getResourceChannel())
                .resourceUri(corpusLibraryDO.getResourceUri())
                .corpusType(corpusLibraryDO.getCorpusType())
                .isContentQuantified(corpusLibraryDO.getIsContentQuantified())
                .priority(corpusLibraryDO.getPriority())
                .confidence(corpusLibraryDO.getConfidence())
                .publishTime(corpusLibraryDO.getPublishTime())
                .createdTime(corpusLibraryDO.getCreatedTime())
                .updatedTime(corpusLibraryDO.getUpdatedTime())
                .corpusSummary(corpusLibraryDO.getCorpusSummary())
                .corpusContent(corpusLibraryDO.getCorpusContent())
                .build();
    }

    public static CorpusLibraryDOWithBLOBs toDO(CorpusLibrary corpusLibrary) {
        if (corpusLibrary == null) {
            return null;
        }

        CorpusLibraryDOWithBLOBs corpusLibraryDO = new CorpusLibraryDOWithBLOBs();
        corpusLibraryDO.setId(corpusLibrary.getId());
        corpusLibraryDO.setSortOrder(corpusLibrary.getSortOrder());
        corpusLibraryDO.setPrevCorpusId(corpusLibrary.getPrevCorpusId());
        corpusLibraryDO.setResourceId(corpusLibrary.getResourceId());
        corpusLibraryDO.setResourceChannel(corpusLibrary.getResourceChannel());
        corpusLibraryDO.setResourceUri(corpusLibrary.getResourceUri());
        corpusLibraryDO.setCorpusType(corpusLibrary.getCorpusType());
        corpusLibraryDO.setIsContentQuantified(corpusLibrary.getIsContentQuantified());
        corpusLibraryDO.setPriority(corpusLibrary.getPriority());
        corpusLibraryDO.setConfidence(corpusLibrary.getConfidence());
        corpusLibraryDO.setPublishTime(corpusLibrary.getPublishTime());
        corpusLibraryDO.setCreatedTime(corpusLibrary.getCreatedTime());
        corpusLibraryDO.setUpdatedTime(corpusLibrary.getUpdatedTime());
        corpusLibraryDO.setCorpusSummary(corpusLibrary.getCorpusSummary());
        corpusLibraryDO.setCorpusContent(corpusLibrary.getCorpusContent());
        corpusLibraryDO.setMtShopId(corpusLibrary.getMtShopId());

        return corpusLibraryDO;
    }
}
