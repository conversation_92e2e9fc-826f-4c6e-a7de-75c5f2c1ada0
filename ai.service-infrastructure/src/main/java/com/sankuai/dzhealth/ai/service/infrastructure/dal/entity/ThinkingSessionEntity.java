package com.sankuai.dzhealth.ai.service.infrastructure.dal.entity;

import java.util.Date;
import lombok.*;

/**
 *
 *   表名: thinking_sessions
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ThinkingSessionEntity {
    /**
     *   字段: id
     *   说明: 会话ID
     */
    private Long id;

    /**
     *   字段: query
     *   说明: 搜索查询语句
     */
    private String query;

    /**
     *   字段: status
     *   说明: 会话状态（待执行/进行中/已完成/失败）
     */
    private String status;

    /**
     *   字段: search_results
     *   说明: 最终搜索结果
     */
    private String searchResults;

    /**
     *   字段: search_config
     *   说明: 搜索配置参数
     */
    private String searchConfig;

    /**
     *   字段: total_planned_steps
     *   说明: 计划步骤总数
     */
    private Long totalPlannedSteps;

    /**
     *   字段: actual_steps
     *   说明: 实际执行步骤数
     */
    private Long actualSteps;

    /**
     *   字段: start_time
     *   说明: 开始时间
     */
    private Date startTime;

    /**
     *   字段: end_time
     *   说明: 结束时间（未完成时为NULL）
     */
    private Date endTime;

    /**
     *   字段: add_time
     *   说明: 创建时间
     */
    private Date addTime;

    /**
     *   字段: update_time
     *   说明: 更新时间
     */
    private Date updateTime;
}