package com.sankuai.dzhealth.ai.service.infrastructure.repository.decision;

import com.sankuai.dzhealth.ai.service.enums.DecisionFlowElementStatusEnum;
import com.sankuai.dzhealth.ai.service.infrastructure.dal.entity.DecisionNodeEdgeDO;
import com.sankuai.dzhealth.ai.service.infrastructure.dal.example.DecisionNodeEdgeEntityExample;
import com.sankuai.dzhealth.ai.service.infrastructure.dal.mapper.DecisionNodeEdgeEntityMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

import java.util.Arrays;
import java.util.List;

/**
 * 决策路径仓储
 * <p>
 * 负责 decision_node_edge 表的 CRUD 及常用查询。
 */
@Repository
@RequiredArgsConstructor
public class DecisionEdgeRepository {

    private final DecisionNodeEdgeEntityMapper edgeMapper;

    private static final String STATUS_ONLINE_DELETE = DecisionFlowElementStatusEnum.ONLINE_DELETE.code();
    private static final String STATUS_GRAY_DELETE   = DecisionFlowElementStatusEnum.GRAY_DELETE.code();

    public DecisionNodeEdgeDO findById(Long id) {
        return edgeMapper.selectByPrimaryKey(id);
    }

    public List<DecisionNodeEdgeDO> listByBizScene(String bizScene) {
        DecisionNodeEdgeEntityExample example = new DecisionNodeEdgeEntityExample();
        example.createCriteria()
                .andBizSceneEqualTo(bizScene)
                .andStatusNotIn(Arrays.asList(STATUS_ONLINE_DELETE, STATUS_GRAY_DELETE));
        return edgeMapper.selectByExample(example);
    }

    public List<DecisionNodeEdgeDO> listByBizSceneAndStatus(String bizScene, String status) {
        DecisionNodeEdgeEntityExample example = new DecisionNodeEdgeEntityExample();
        example.createCriteria()
                .andBizSceneEqualTo(bizScene)
                .andStatusEqualTo(status);
        return edgeMapper.selectByExample(example);
    }

    public List<DecisionNodeEdgeDO> listByParentNode(String parentNodeId) {
        DecisionNodeEdgeEntityExample example = new DecisionNodeEdgeEntityExample();
        example.createCriteria()
                .andParentIdEqualTo(parentNodeId)
                .andStatusNotIn(Arrays.asList(STATUS_ONLINE_DELETE, STATUS_GRAY_DELETE));
        example.setOrderByClause("sort_order ASC");
        return edgeMapper.selectByExample(example);
    }

    public int insert(DecisionNodeEdgeDO entity) {
        // 设置 ext 字段默认值为空 JSON 对象，避免 MySQL JSON 字段报错
        if (entity.getExt() == null || entity.getExt().trim().isEmpty()) {
            entity.setExt("{}");
        }
        return edgeMapper.insertSelective(entity);
    }

    /**
     * 批量插入边
     */
    public int batchInsert(List<DecisionNodeEdgeDO> entities) {
        if (entities == null || entities.isEmpty()) {
            return 0;
        }

        // 设置默认值
        entities.forEach(entity -> {
            if (entity.getAddTime() == null) {
                entity.setAddTime(new java.util.Date());
            }
            if (entity.getUpdateTime() == null) {
                entity.setUpdateTime(new java.util.Date());
            }
            // 设置 ext 字段默认值为空 JSON 对象，避免 MySQL JSON 字段报错
            if (entity.getExt() == null || entity.getExt().trim().isEmpty()) {
                entity.setExt("{}");
            }
        });

        return edgeMapper.batchInsert(entities);
    }

    /**
     * 批量更新边内容
     */
    public int batchUpdateByEdgeId(List<DecisionNodeEdgeDO> entities) {
        if (entities == null || entities.isEmpty()) {
            return 0;
        }

        // 设置更新时间
        entities.forEach(entity -> {
            if (entity.getUpdateTime() == null) {
                entity.setUpdateTime(new java.util.Date());
            }
        });

        return edgeMapper.batchUpdateByEdgeId(entities);
    }

    /**
     * 批量更新边状态
     */
    public int batchUpdateStatusByEdgeIds(List<String> edgeIds, String status) {
        if (edgeIds == null || edgeIds.isEmpty()) {
            return 0;
        }
        DecisionNodeEdgeDO entity = new DecisionNodeEdgeDO();
        entity.setStatus(status);
        DecisionNodeEdgeEntityExample example = new DecisionNodeEdgeEntityExample();
        example.createCriteria().andEdgeIdIn(edgeIds);
        return edgeMapper.updateByExampleSelective(entity, example);
    }

    public int update(DecisionNodeEdgeDO entity) {
        return edgeMapper.updateByPrimaryKeySelective(entity);
    }

    public int delete(Long id) {
        DecisionNodeEdgeDO entity = new DecisionNodeEdgeDO();
        entity.setId(id);
        entity.setStatus(STATUS_ONLINE_DELETE);
        return edgeMapper.updateByPrimaryKeySelective(entity);
    }

    /**
     * 软删除指定业务场景下的所有边（设置为ONLINE_DELETE状态）
     */
    public int deleteByBizScene(String bizScene) {
        DecisionNodeEdgeDO entity = new DecisionNodeEdgeDO();
        entity.setStatus(DecisionFlowElementStatusEnum.ONLINE_DELETE.code());
        DecisionNodeEdgeEntityExample example = new DecisionNodeEdgeEntityExample();
        example.createCriteria().andBizSceneEqualTo(bizScene);
        return edgeMapper.updateByExampleSelective(entity, example);
    }

    /**
     * 灰度删除指定业务场景下的所有边（设置为GRAY_DELETE状态）
     */
    public int grayDeleteByBizScene(String bizScene) {
        DecisionNodeEdgeDO entity = new DecisionNodeEdgeDO();
        entity.setStatus(DecisionFlowElementStatusEnum.GRAY_DELETE.code());
        DecisionNodeEdgeEntityExample example = new DecisionNodeEdgeEntityExample();
        example.createCriteria().andBizSceneEqualTo(bizScene);
        return edgeMapper.updateByExampleSelective(entity, example);
    }

    /**
     * 物理删除指定业务场景下的所有边
     */
    public int hardDeleteByBizScene(String bizScene) {
        DecisionNodeEdgeEntityExample example = new DecisionNodeEdgeEntityExample();
        example.createCriteria().andBizSceneEqualTo(bizScene);
        return edgeMapper.deleteByExample(example);
    }

    public DecisionNodeEdgeDO findByEdgeId(String edgeId){
        DecisionNodeEdgeEntityExample example=new DecisionNodeEdgeEntityExample();
        example.createCriteria().andEdgeIdEqualTo(edgeId);
        List<DecisionNodeEdgeDO> list=edgeMapper.selectByExample(example);
        return list.isEmpty()?null:list.get(0);
    }

    /**
     * 根据边ID和状态查询边
     */
    public DecisionNodeEdgeDO findByEdgeIdAndStatus(String edgeId, String status) {
        DecisionNodeEdgeEntityExample example = new DecisionNodeEdgeEntityExample();
        example.createCriteria()
                .andEdgeIdEqualTo(edgeId)
                .andStatusEqualTo(status);
        List<DecisionNodeEdgeDO> list = edgeMapper.selectByExample(example);
        return list.isEmpty() ? null : list.get(0);
    }

    public int updateByEdgeId(DecisionNodeEdgeDO entity){
        DecisionNodeEdgeEntityExample example=new DecisionNodeEdgeEntityExample();
        example.createCriteria().andEdgeIdEqualTo(entity.getEdgeId());
        return edgeMapper.updateByExampleSelective(entity,example);
    }

    public int deleteByEdgeId(String edgeId){
        DecisionNodeEdgeDO entity = new DecisionNodeEdgeDO();
        entity.setStatus(STATUS_ONLINE_DELETE);
        DecisionNodeEdgeEntityExample example=new DecisionNodeEdgeEntityExample();
        example.createCriteria().andEdgeIdEqualTo(edgeId);
        return edgeMapper.updateByExampleSelective(entity, example);
    }

    public int deleteByParentNode(String parentNodeId){
        DecisionNodeEdgeDO entity = new DecisionNodeEdgeDO();
        entity.setStatus(STATUS_ONLINE_DELETE);
        DecisionNodeEdgeEntityExample example=new DecisionNodeEdgeEntityExample();
        example.createCriteria().andParentIdEqualTo(parentNodeId);
        return edgeMapper.updateByExampleSelective(entity, example);
    }

    /**
     * 查询与指定节点相关的所有边（作为父节点或子节点）
     */
    public List<DecisionNodeEdgeDO> listByNodeId(String nodeId) {
        DecisionNodeEdgeEntityExample example = new DecisionNodeEdgeEntityExample();
        example.createCriteria()
                .andParentIdEqualTo(nodeId)
                .andStatusNotEqualTo(STATUS_ONLINE_DELETE);

        DecisionNodeEdgeEntityExample example2 = new DecisionNodeEdgeEntityExample();
        example2.createCriteria()
                .andChildIdEqualTo(nodeId)
                .andStatusNotIn(Arrays.asList(STATUS_ONLINE_DELETE, STATUS_GRAY_DELETE));

        List<DecisionNodeEdgeDO> parentEdges = edgeMapper.selectByExample(example);
        List<DecisionNodeEdgeDO> childEdges = edgeMapper.selectByExample(example2);

        parentEdges.addAll(childEdges);
        return parentEdges;
    }
} 