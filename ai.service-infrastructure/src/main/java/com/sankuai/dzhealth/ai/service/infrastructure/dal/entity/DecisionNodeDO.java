package com.sankuai.dzhealth.ai.service.infrastructure.dal.entity;

import java.util.Date;
import lombok.*;

/**
 *
 *   表名: decision_node
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DecisionNodeDO {
    /**
     *   字段: id
     *   说明: PK
     */
    private Long id;

    /**
     *   字段: biz_scene
     *   说明: 业务场景
     */
    private String bizScene;

    /**
     *   字段: node_id
     *   说明: 节点业务ID；ROOT 为虚根
     */
    private String nodeId;

    /**
     *   字段: node_name
     *   说明: 节点名称
     */
    private String nodeName;

    /**
     *   字段: assessment_text
     *   说明: 判别文案
     */
    private String assessmentText;

    /**
     *   字段: assessment_img
     *   说明: 示例图 URL
     */
    private String assessmentImg;

    /**
     *   字段: guidance_text
     *   说明: 指导文案
     */
    private String guidanceText;

    /**
     *   字段: status
     *   说明: ONLINE/GRAY/ONLINE_DELETE/GRAY_DELETE/OFFLINE
     */
    private String status;

    /**
     *   字段: ext
     *   说明: 扩展字段
     */
    private String ext;

    /**
     *   字段: add_time
     */
    private Date addTime;

    /**
     *   字段: update_time
     */
    private Date updateTime;

    /**
     *   字段: need_supply
     *   说明: 是否需要推供给：1-是 0-否，决定下一层的需求中有没有推荐供给模块 (默认 1)
     */
    private Boolean needSupply;

    /**
     *   字段: need_doctor
     *   说明: 是否出医生：1-是 0-否 (默认 0)
     */
    private Boolean needDoctor;
}