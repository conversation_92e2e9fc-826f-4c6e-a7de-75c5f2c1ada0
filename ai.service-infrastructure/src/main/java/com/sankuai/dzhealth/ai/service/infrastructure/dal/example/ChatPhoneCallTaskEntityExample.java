package com.sankuai.dzhealth.ai.service.infrastructure.dal.example;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class ChatPhoneCallTaskEntityExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public ChatPhoneCallTaskEntityExample() {
        oredCriteria = new ArrayList<>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andContactIdIsNull() {
            addCriterion("contact_id is null");
            return (Criteria) this;
        }

        public Criteria andContactIdIsNotNull() {
            addCriterion("contact_id is not null");
            return (Criteria) this;
        }

        public Criteria andContactIdEqualTo(String value) {
            addCriterion("contact_id =", value, "contactId");
            return (Criteria) this;
        }

        public Criteria andContactIdNotEqualTo(String value) {
            addCriterion("contact_id <>", value, "contactId");
            return (Criteria) this;
        }

        public Criteria andContactIdGreaterThan(String value) {
            addCriterion("contact_id >", value, "contactId");
            return (Criteria) this;
        }

        public Criteria andContactIdGreaterThanOrEqualTo(String value) {
            addCriterion("contact_id >=", value, "contactId");
            return (Criteria) this;
        }

        public Criteria andContactIdLessThan(String value) {
            addCriterion("contact_id <", value, "contactId");
            return (Criteria) this;
        }

        public Criteria andContactIdLessThanOrEqualTo(String value) {
            addCriterion("contact_id <=", value, "contactId");
            return (Criteria) this;
        }

        public Criteria andContactIdLike(String value) {
            addCriterion("contact_id like", value, "contactId");
            return (Criteria) this;
        }

        public Criteria andContactIdNotLike(String value) {
            addCriterion("contact_id not like", value, "contactId");
            return (Criteria) this;
        }

        public Criteria andContactIdIn(List<String> values) {
            addCriterion("contact_id in", values, "contactId");
            return (Criteria) this;
        }

        public Criteria andContactIdNotIn(List<String> values) {
            addCriterion("contact_id not in", values, "contactId");
            return (Criteria) this;
        }

        public Criteria andContactIdBetween(String value1, String value2) {
            addCriterion("contact_id between", value1, value2, "contactId");
            return (Criteria) this;
        }

        public Criteria andContactIdNotBetween(String value1, String value2) {
            addCriterion("contact_id not between", value1, value2, "contactId");
            return (Criteria) this;
        }

        public Criteria andMessageIdIsNull() {
            addCriterion("message_id is null");
            return (Criteria) this;
        }

        public Criteria andMessageIdIsNotNull() {
            addCriterion("message_id is not null");
            return (Criteria) this;
        }

        public Criteria andMessageIdEqualTo(String value) {
            addCriterion("message_id =", value, "messageId");
            return (Criteria) this;
        }

        public Criteria andMessageIdNotEqualTo(String value) {
            addCriterion("message_id <>", value, "messageId");
            return (Criteria) this;
        }

        public Criteria andMessageIdGreaterThan(String value) {
            addCriterion("message_id >", value, "messageId");
            return (Criteria) this;
        }

        public Criteria andMessageIdGreaterThanOrEqualTo(String value) {
            addCriterion("message_id >=", value, "messageId");
            return (Criteria) this;
        }

        public Criteria andMessageIdLessThan(String value) {
            addCriterion("message_id <", value, "messageId");
            return (Criteria) this;
        }

        public Criteria andMessageIdLessThanOrEqualTo(String value) {
            addCriterion("message_id <=", value, "messageId");
            return (Criteria) this;
        }

        public Criteria andMessageIdLike(String value) {
            addCriterion("message_id like", value, "messageId");
            return (Criteria) this;
        }

        public Criteria andMessageIdNotLike(String value) {
            addCriterion("message_id not like", value, "messageId");
            return (Criteria) this;
        }

        public Criteria andMessageIdIn(List<String> values) {
            addCriterion("message_id in", values, "messageId");
            return (Criteria) this;
        }

        public Criteria andMessageIdNotIn(List<String> values) {
            addCriterion("message_id not in", values, "messageId");
            return (Criteria) this;
        }

        public Criteria andMessageIdBetween(String value1, String value2) {
            addCriterion("message_id between", value1, value2, "messageId");
            return (Criteria) this;
        }

        public Criteria andMessageIdNotBetween(String value1, String value2) {
            addCriterion("message_id not between", value1, value2, "messageId");
            return (Criteria) this;
        }

        public Criteria andSessionIdIsNull() {
            addCriterion("session_id is null");
            return (Criteria) this;
        }

        public Criteria andSessionIdIsNotNull() {
            addCriterion("session_id is not null");
            return (Criteria) this;
        }

        public Criteria andSessionIdEqualTo(Long value) {
            addCriterion("session_id =", value, "sessionId");
            return (Criteria) this;
        }

        public Criteria andSessionIdNotEqualTo(Long value) {
            addCriterion("session_id <>", value, "sessionId");
            return (Criteria) this;
        }

        public Criteria andSessionIdGreaterThan(Long value) {
            addCriterion("session_id >", value, "sessionId");
            return (Criteria) this;
        }

        public Criteria andSessionIdGreaterThanOrEqualTo(Long value) {
            addCriterion("session_id >=", value, "sessionId");
            return (Criteria) this;
        }

        public Criteria andSessionIdLessThan(Long value) {
            addCriterion("session_id <", value, "sessionId");
            return (Criteria) this;
        }

        public Criteria andSessionIdLessThanOrEqualTo(Long value) {
            addCriterion("session_id <=", value, "sessionId");
            return (Criteria) this;
        }

        public Criteria andSessionIdIn(List<Long> values) {
            addCriterion("session_id in", values, "sessionId");
            return (Criteria) this;
        }

        public Criteria andSessionIdNotIn(List<Long> values) {
            addCriterion("session_id not in", values, "sessionId");
            return (Criteria) this;
        }

        public Criteria andSessionIdBetween(Long value1, Long value2) {
            addCriterion("session_id between", value1, value2, "sessionId");
            return (Criteria) this;
        }

        public Criteria andSessionIdNotBetween(Long value1, Long value2) {
            addCriterion("session_id not between", value1, value2, "sessionId");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("status is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("status is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(Integer value) {
            addCriterion("status =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(Integer value) {
            addCriterion("status <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(Integer value) {
            addCriterion("status >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("status >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(Integer value) {
            addCriterion("status <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(Integer value) {
            addCriterion("status <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<Integer> values) {
            addCriterion("status in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<Integer> values) {
            addCriterion("status not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(Integer value1, Integer value2) {
            addCriterion("status between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("status not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andCallCountIsNull() {
            addCriterion("call_count is null");
            return (Criteria) this;
        }

        public Criteria andCallCountIsNotNull() {
            addCriterion("call_count is not null");
            return (Criteria) this;
        }

        public Criteria andCallCountEqualTo(Integer value) {
            addCriterion("call_count =", value, "callCount");
            return (Criteria) this;
        }

        public Criteria andCallCountNotEqualTo(Integer value) {
            addCriterion("call_count <>", value, "callCount");
            return (Criteria) this;
        }

        public Criteria andCallCountGreaterThan(Integer value) {
            addCriterion("call_count >", value, "callCount");
            return (Criteria) this;
        }

        public Criteria andCallCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("call_count >=", value, "callCount");
            return (Criteria) this;
        }

        public Criteria andCallCountLessThan(Integer value) {
            addCriterion("call_count <", value, "callCount");
            return (Criteria) this;
        }

        public Criteria andCallCountLessThanOrEqualTo(Integer value) {
            addCriterion("call_count <=", value, "callCount");
            return (Criteria) this;
        }

        public Criteria andCallCountIn(List<Integer> values) {
            addCriterion("call_count in", values, "callCount");
            return (Criteria) this;
        }

        public Criteria andCallCountNotIn(List<Integer> values) {
            addCriterion("call_count not in", values, "callCount");
            return (Criteria) this;
        }

        public Criteria andCallCountBetween(Integer value1, Integer value2) {
            addCriterion("call_count between", value1, value2, "callCount");
            return (Criteria) this;
        }

        public Criteria andCallCountNotBetween(Integer value1, Integer value2) {
            addCriterion("call_count not between", value1, value2, "callCount");
            return (Criteria) this;
        }

        public Criteria andMtShopIdIsNull() {
            addCriterion("mt_shop_id is null");
            return (Criteria) this;
        }

        public Criteria andMtShopIdIsNotNull() {
            addCriterion("mt_shop_id is not null");
            return (Criteria) this;
        }

        public Criteria andMtShopIdEqualTo(Long value) {
            addCriterion("mt_shop_id =", value, "mtShopId");
            return (Criteria) this;
        }

        public Criteria andMtShopIdNotEqualTo(Long value) {
            addCriterion("mt_shop_id <>", value, "mtShopId");
            return (Criteria) this;
        }

        public Criteria andMtShopIdGreaterThan(Long value) {
            addCriterion("mt_shop_id >", value, "mtShopId");
            return (Criteria) this;
        }

        public Criteria andMtShopIdGreaterThanOrEqualTo(Long value) {
            addCriterion("mt_shop_id >=", value, "mtShopId");
            return (Criteria) this;
        }

        public Criteria andMtShopIdLessThan(Long value) {
            addCriterion("mt_shop_id <", value, "mtShopId");
            return (Criteria) this;
        }

        public Criteria andMtShopIdLessThanOrEqualTo(Long value) {
            addCriterion("mt_shop_id <=", value, "mtShopId");
            return (Criteria) this;
        }

        public Criteria andMtShopIdIn(List<Long> values) {
            addCriterion("mt_shop_id in", values, "mtShopId");
            return (Criteria) this;
        }

        public Criteria andMtShopIdNotIn(List<Long> values) {
            addCriterion("mt_shop_id not in", values, "mtShopId");
            return (Criteria) this;
        }

        public Criteria andMtShopIdBetween(Long value1, Long value2) {
            addCriterion("mt_shop_id between", value1, value2, "mtShopId");
            return (Criteria) this;
        }

        public Criteria andMtShopIdNotBetween(Long value1, Long value2) {
            addCriterion("mt_shop_id not between", value1, value2, "mtShopId");
            return (Criteria) this;
        }

        public Criteria andStartTimeIsNull() {
            addCriterion("start_time is null");
            return (Criteria) this;
        }

        public Criteria andStartTimeIsNotNull() {
            addCriterion("start_time is not null");
            return (Criteria) this;
        }

        public Criteria andStartTimeEqualTo(Date value) {
            addCriterion("start_time =", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeNotEqualTo(Date value) {
            addCriterion("start_time <>", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeGreaterThan(Date value) {
            addCriterion("start_time >", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("start_time >=", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeLessThan(Date value) {
            addCriterion("start_time <", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeLessThanOrEqualTo(Date value) {
            addCriterion("start_time <=", value, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeIn(List<Date> values) {
            addCriterion("start_time in", values, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeNotIn(List<Date> values) {
            addCriterion("start_time not in", values, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeBetween(Date value1, Date value2) {
            addCriterion("start_time between", value1, value2, "startTime");
            return (Criteria) this;
        }

        public Criteria andStartTimeNotBetween(Date value1, Date value2) {
            addCriterion("start_time not between", value1, value2, "startTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeIsNull() {
            addCriterion("end_time is null");
            return (Criteria) this;
        }

        public Criteria andEndTimeIsNotNull() {
            addCriterion("end_time is not null");
            return (Criteria) this;
        }

        public Criteria andEndTimeEqualTo(Date value) {
            addCriterion("end_time =", value, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeNotEqualTo(Date value) {
            addCriterion("end_time <>", value, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeGreaterThan(Date value) {
            addCriterion("end_time >", value, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("end_time >=", value, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeLessThan(Date value) {
            addCriterion("end_time <", value, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeLessThanOrEqualTo(Date value) {
            addCriterion("end_time <=", value, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeIn(List<Date> values) {
            addCriterion("end_time in", values, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeNotIn(List<Date> values) {
            addCriterion("end_time not in", values, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeBetween(Date value1, Date value2) {
            addCriterion("end_time between", value1, value2, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeNotBetween(Date value1, Date value2) {
            addCriterion("end_time not between", value1, value2, "endTime");
            return (Criteria) this;
        }

        public Criteria andTalkingTimeLenIsNull() {
            addCriterion("talking_time_len is null");
            return (Criteria) this;
        }

        public Criteria andTalkingTimeLenIsNotNull() {
            addCriterion("talking_time_len is not null");
            return (Criteria) this;
        }

        public Criteria andTalkingTimeLenEqualTo(Integer value) {
            addCriterion("talking_time_len =", value, "talkingTimeLen");
            return (Criteria) this;
        }

        public Criteria andTalkingTimeLenNotEqualTo(Integer value) {
            addCriterion("talking_time_len <>", value, "talkingTimeLen");
            return (Criteria) this;
        }

        public Criteria andTalkingTimeLenGreaterThan(Integer value) {
            addCriterion("talking_time_len >", value, "talkingTimeLen");
            return (Criteria) this;
        }

        public Criteria andTalkingTimeLenGreaterThanOrEqualTo(Integer value) {
            addCriterion("talking_time_len >=", value, "talkingTimeLen");
            return (Criteria) this;
        }

        public Criteria andTalkingTimeLenLessThan(Integer value) {
            addCriterion("talking_time_len <", value, "talkingTimeLen");
            return (Criteria) this;
        }

        public Criteria andTalkingTimeLenLessThanOrEqualTo(Integer value) {
            addCriterion("talking_time_len <=", value, "talkingTimeLen");
            return (Criteria) this;
        }

        public Criteria andTalkingTimeLenIn(List<Integer> values) {
            addCriterion("talking_time_len in", values, "talkingTimeLen");
            return (Criteria) this;
        }

        public Criteria andTalkingTimeLenNotIn(List<Integer> values) {
            addCriterion("talking_time_len not in", values, "talkingTimeLen");
            return (Criteria) this;
        }

        public Criteria andTalkingTimeLenBetween(Integer value1, Integer value2) {
            addCriterion("talking_time_len between", value1, value2, "talkingTimeLen");
            return (Criteria) this;
        }

        public Criteria andTalkingTimeLenNotBetween(Integer value1, Integer value2) {
            addCriterion("talking_time_len not between", value1, value2, "talkingTimeLen");
            return (Criteria) this;
        }

        public Criteria andReleaseReasonIsNull() {
            addCriterion("release_reason is null");
            return (Criteria) this;
        }

        public Criteria andReleaseReasonIsNotNull() {
            addCriterion("release_reason is not null");
            return (Criteria) this;
        }

        public Criteria andReleaseReasonEqualTo(Integer value) {
            addCriterion("release_reason =", value, "releaseReason");
            return (Criteria) this;
        }

        public Criteria andReleaseReasonNotEqualTo(Integer value) {
            addCriterion("release_reason <>", value, "releaseReason");
            return (Criteria) this;
        }

        public Criteria andReleaseReasonGreaterThan(Integer value) {
            addCriterion("release_reason >", value, "releaseReason");
            return (Criteria) this;
        }

        public Criteria andReleaseReasonGreaterThanOrEqualTo(Integer value) {
            addCriterion("release_reason >=", value, "releaseReason");
            return (Criteria) this;
        }

        public Criteria andReleaseReasonLessThan(Integer value) {
            addCriterion("release_reason <", value, "releaseReason");
            return (Criteria) this;
        }

        public Criteria andReleaseReasonLessThanOrEqualTo(Integer value) {
            addCriterion("release_reason <=", value, "releaseReason");
            return (Criteria) this;
        }

        public Criteria andReleaseReasonIn(List<Integer> values) {
            addCriterion("release_reason in", values, "releaseReason");
            return (Criteria) this;
        }

        public Criteria andReleaseReasonNotIn(List<Integer> values) {
            addCriterion("release_reason not in", values, "releaseReason");
            return (Criteria) this;
        }

        public Criteria andReleaseReasonBetween(Integer value1, Integer value2) {
            addCriterion("release_reason between", value1, value2, "releaseReason");
            return (Criteria) this;
        }

        public Criteria andReleaseReasonNotBetween(Integer value1, Integer value2) {
            addCriterion("release_reason not between", value1, value2, "releaseReason");
            return (Criteria) this;
        }

        public Criteria andQuestionIsNull() {
            addCriterion("question is null");
            return (Criteria) this;
        }

        public Criteria andQuestionIsNotNull() {
            addCriterion("question is not null");
            return (Criteria) this;
        }

        public Criteria andQuestionEqualTo(String value) {
            addCriterion("question =", value, "question");
            return (Criteria) this;
        }

        public Criteria andQuestionNotEqualTo(String value) {
            addCriterion("question <>", value, "question");
            return (Criteria) this;
        }

        public Criteria andQuestionGreaterThan(String value) {
            addCriterion("question >", value, "question");
            return (Criteria) this;
        }

        public Criteria andQuestionGreaterThanOrEqualTo(String value) {
            addCriterion("question >=", value, "question");
            return (Criteria) this;
        }

        public Criteria andQuestionLessThan(String value) {
            addCriterion("question <", value, "question");
            return (Criteria) this;
        }

        public Criteria andQuestionLessThanOrEqualTo(String value) {
            addCriterion("question <=", value, "question");
            return (Criteria) this;
        }

        public Criteria andQuestionLike(String value) {
            addCriterion("question like", value, "question");
            return (Criteria) this;
        }

        public Criteria andQuestionNotLike(String value) {
            addCriterion("question not like", value, "question");
            return (Criteria) this;
        }

        public Criteria andQuestionIn(List<String> values) {
            addCriterion("question in", values, "question");
            return (Criteria) this;
        }

        public Criteria andQuestionNotIn(List<String> values) {
            addCriterion("question not in", values, "question");
            return (Criteria) this;
        }

        public Criteria andQuestionBetween(String value1, String value2) {
            addCriterion("question between", value1, value2, "question");
            return (Criteria) this;
        }

        public Criteria andQuestionNotBetween(String value1, String value2) {
            addCriterion("question not between", value1, value2, "question");
            return (Criteria) this;
        }

        public Criteria andMediatxtIsNull() {
            addCriterion("mediatxt is null");
            return (Criteria) this;
        }

        public Criteria andMediatxtIsNotNull() {
            addCriterion("mediatxt is not null");
            return (Criteria) this;
        }

        public Criteria andMediatxtEqualTo(String value) {
            addCriterion("mediatxt =", value, "mediatxt");
            return (Criteria) this;
        }

        public Criteria andMediatxtNotEqualTo(String value) {
            addCriterion("mediatxt <>", value, "mediatxt");
            return (Criteria) this;
        }

        public Criteria andMediatxtGreaterThan(String value) {
            addCriterion("mediatxt >", value, "mediatxt");
            return (Criteria) this;
        }

        public Criteria andMediatxtGreaterThanOrEqualTo(String value) {
            addCriterion("mediatxt >=", value, "mediatxt");
            return (Criteria) this;
        }

        public Criteria andMediatxtLessThan(String value) {
            addCriterion("mediatxt <", value, "mediatxt");
            return (Criteria) this;
        }

        public Criteria andMediatxtLessThanOrEqualTo(String value) {
            addCriterion("mediatxt <=", value, "mediatxt");
            return (Criteria) this;
        }

        public Criteria andMediatxtLike(String value) {
            addCriterion("mediatxt like", value, "mediatxt");
            return (Criteria) this;
        }

        public Criteria andMediatxtNotLike(String value) {
            addCriterion("mediatxt not like", value, "mediatxt");
            return (Criteria) this;
        }

        public Criteria andMediatxtIn(List<String> values) {
            addCriterion("mediatxt in", values, "mediatxt");
            return (Criteria) this;
        }

        public Criteria andMediatxtNotIn(List<String> values) {
            addCriterion("mediatxt not in", values, "mediatxt");
            return (Criteria) this;
        }

        public Criteria andMediatxtBetween(String value1, String value2) {
            addCriterion("mediatxt between", value1, value2, "mediatxt");
            return (Criteria) this;
        }

        public Criteria andMediatxtNotBetween(String value1, String value2) {
            addCriterion("mediatxt not between", value1, value2, "mediatxt");
            return (Criteria) this;
        }

        public Criteria andDialogRawDataIsNull() {
            addCriterion("dialog_raw_data is null");
            return (Criteria) this;
        }

        public Criteria andDialogRawDataIsNotNull() {
            addCriterion("dialog_raw_data is not null");
            return (Criteria) this;
        }

        public Criteria andDialogRawDataEqualTo(String value) {
            addCriterion("dialog_raw_data =", value, "dialogRawData");
            return (Criteria) this;
        }

        public Criteria andDialogRawDataNotEqualTo(String value) {
            addCriterion("dialog_raw_data <>", value, "dialogRawData");
            return (Criteria) this;
        }

        public Criteria andDialogRawDataGreaterThan(String value) {
            addCriterion("dialog_raw_data >", value, "dialogRawData");
            return (Criteria) this;
        }

        public Criteria andDialogRawDataGreaterThanOrEqualTo(String value) {
            addCriterion("dialog_raw_data >=", value, "dialogRawData");
            return (Criteria) this;
        }

        public Criteria andDialogRawDataLessThan(String value) {
            addCriterion("dialog_raw_data <", value, "dialogRawData");
            return (Criteria) this;
        }

        public Criteria andDialogRawDataLessThanOrEqualTo(String value) {
            addCriterion("dialog_raw_data <=", value, "dialogRawData");
            return (Criteria) this;
        }

        public Criteria andDialogRawDataLike(String value) {
            addCriterion("dialog_raw_data like", value, "dialogRawData");
            return (Criteria) this;
        }

        public Criteria andDialogRawDataNotLike(String value) {
            addCriterion("dialog_raw_data not like", value, "dialogRawData");
            return (Criteria) this;
        }

        public Criteria andDialogRawDataIn(List<String> values) {
            addCriterion("dialog_raw_data in", values, "dialogRawData");
            return (Criteria) this;
        }

        public Criteria andDialogRawDataNotIn(List<String> values) {
            addCriterion("dialog_raw_data not in", values, "dialogRawData");
            return (Criteria) this;
        }

        public Criteria andDialogRawDataBetween(String value1, String value2) {
            addCriterion("dialog_raw_data between", value1, value2, "dialogRawData");
            return (Criteria) this;
        }

        public Criteria andDialogRawDataNotBetween(String value1, String value2) {
            addCriterion("dialog_raw_data not between", value1, value2, "dialogRawData");
            return (Criteria) this;
        }

        public Criteria andDialogRefinedDataIsNull() {
            addCriterion("dialog_refined_data is null");
            return (Criteria) this;
        }

        public Criteria andDialogRefinedDataIsNotNull() {
            addCriterion("dialog_refined_data is not null");
            return (Criteria) this;
        }

        public Criteria andDialogRefinedDataEqualTo(String value) {
            addCriterion("dialog_refined_data =", value, "dialogRefinedData");
            return (Criteria) this;
        }

        public Criteria andDialogRefinedDataNotEqualTo(String value) {
            addCriterion("dialog_refined_data <>", value, "dialogRefinedData");
            return (Criteria) this;
        }

        public Criteria andDialogRefinedDataGreaterThan(String value) {
            addCriterion("dialog_refined_data >", value, "dialogRefinedData");
            return (Criteria) this;
        }

        public Criteria andDialogRefinedDataGreaterThanOrEqualTo(String value) {
            addCriterion("dialog_refined_data >=", value, "dialogRefinedData");
            return (Criteria) this;
        }

        public Criteria andDialogRefinedDataLessThan(String value) {
            addCriterion("dialog_refined_data <", value, "dialogRefinedData");
            return (Criteria) this;
        }

        public Criteria andDialogRefinedDataLessThanOrEqualTo(String value) {
            addCriterion("dialog_refined_data <=", value, "dialogRefinedData");
            return (Criteria) this;
        }

        public Criteria andDialogRefinedDataLike(String value) {
            addCriterion("dialog_refined_data like", value, "dialogRefinedData");
            return (Criteria) this;
        }

        public Criteria andDialogRefinedDataNotLike(String value) {
            addCriterion("dialog_refined_data not like", value, "dialogRefinedData");
            return (Criteria) this;
        }

        public Criteria andDialogRefinedDataIn(List<String> values) {
            addCriterion("dialog_refined_data in", values, "dialogRefinedData");
            return (Criteria) this;
        }

        public Criteria andDialogRefinedDataNotIn(List<String> values) {
            addCriterion("dialog_refined_data not in", values, "dialogRefinedData");
            return (Criteria) this;
        }

        public Criteria andDialogRefinedDataBetween(String value1, String value2) {
            addCriterion("dialog_refined_data between", value1, value2, "dialogRefinedData");
            return (Criteria) this;
        }

        public Criteria andDialogRefinedDataNotBetween(String value1, String value2) {
            addCriterion("dialog_refined_data not between", value1, value2, "dialogRefinedData");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}