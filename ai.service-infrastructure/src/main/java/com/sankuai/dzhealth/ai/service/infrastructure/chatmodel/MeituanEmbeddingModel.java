/*
 * Copyright 2023-2024 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.sankuai.dzhealth.ai.service.infrastructure.chatmodel;

import io.micrometer.observation.ObservationRegistry;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.ai.chat.metadata.DefaultUsage;
import org.springframework.ai.document.Document;
import org.springframework.ai.document.MetadataMode;
import org.springframework.ai.embedding.*;
import org.springframework.ai.embedding.observation.DefaultEmbeddingModelObservationConvention;
import org.springframework.ai.embedding.observation.EmbeddingModelObservationContext;
import org.springframework.ai.embedding.observation.EmbeddingModelObservationConvention;
import org.springframework.ai.embedding.observation.EmbeddingModelObservationDocumentation;
import org.springframework.ai.model.ModelOptionsUtils;
import org.springframework.ai.openai.OpenAiEmbeddingOptions;
import org.springframework.ai.openai.api.OpenAiApi;
import org.springframework.ai.openai.api.common.OpenAiApiConstants;
import org.springframework.ai.retry.RetryUtils;
import org.springframework.retry.support.RetryTemplate;
import org.springframework.util.Assert;

import javax.annotation.Nullable;
import java.util.List;

/**
 * Open AI Embedding Model implementation.
 *
 * <AUTHOR> Tzolov
 * <AUTHOR> Vitale
 */
public class MeituanEmbeddingModel extends AbstractEmbeddingModel {

    private static final Logger logger = LoggerFactory.getLogger(MeituanEmbeddingModel.class);

    private static final EmbeddingModelObservationConvention DEFAULT_OBSERVATION_CONVENTION = new DefaultEmbeddingModelObservationConvention();

    private final OpenAiEmbeddingOptions defaultOptions;

    private final RetryTemplate retryTemplate;

    private final OpenAiApi openAiApi;

    private final MetadataMode metadataMode;

    /**
     * Observation registry used for instrumentation.
     */
    private final ObservationRegistry observationRegistry;

    /**
     * Conventions to use for generating observations.
     */
    private EmbeddingModelObservationConvention observationConvention = DEFAULT_OBSERVATION_CONVENTION;

    /**
     * Constructor for the OpenAiEmbeddingModel class.
     * @param openAiApi The OpenAiApi instance to use for making API requests.
     */
    public MeituanEmbeddingModel(OpenAiApi openAiApi) {
        this(openAiApi, MetadataMode.EMBED);
    }

    /**
     * Initializes a new instance of the OpenAiEmbeddingModel class.
     * @param openAiApi The OpenAiApi instance to use for making API requests.
     * @param metadataMode The mode for generating metadata.
     */
    public MeituanEmbeddingModel(OpenAiApi openAiApi, MetadataMode metadataMode) {
        this(openAiApi, metadataMode,
                OpenAiEmbeddingOptions.builder().model(OpenAiApi.DEFAULT_EMBEDDING_MODEL).build());
    }

    /**
     * Initializes a new instance of the OpenAiEmbeddingModel class.
     * @param openAiApi The OpenAiApi instance to use for making API requests.
     * @param metadataMode The mode for generating metadata.
     * @param openAiEmbeddingOptions The options for OpenAi embedding.
     */
    public MeituanEmbeddingModel(OpenAiApi openAiApi, MetadataMode metadataMode,
                                 OpenAiEmbeddingOptions openAiEmbeddingOptions) {
        this(openAiApi, metadataMode, openAiEmbeddingOptions, RetryUtils.DEFAULT_RETRY_TEMPLATE);
    }

    /**
     * Initializes a new instance of the OpenAiEmbeddingModel class.
     * @param openAiApi - The OpenAiApi instance to use for making API requests.
     * @param metadataMode - The mode for generating metadata.
     * @param options - The options for OpenAI embedding.
     * @param retryTemplate - The RetryTemplate for retrying failed API requests.
     */
    public MeituanEmbeddingModel(OpenAiApi openAiApi, MetadataMode metadataMode, OpenAiEmbeddingOptions options,
                                 RetryTemplate retryTemplate) {
        this(openAiApi, metadataMode, options, retryTemplate, ObservationRegistry.NOOP);
    }

    /**
     * Initializes a new instance of the OpenAiEmbeddingModel class.
     * @param openAiApi - The OpenAiApi instance to use for making API requests.
     * @param metadataMode - The mode for generating metadata.
     * @param options - The options for OpenAI embedding.
     * @param retryTemplate - The RetryTemplate for retrying failed API requests.
     * @param observationRegistry - The ObservationRegistry used for instrumentation.
     */
    public MeituanEmbeddingModel(OpenAiApi openAiApi, MetadataMode metadataMode, OpenAiEmbeddingOptions options,
                                 RetryTemplate retryTemplate, ObservationRegistry observationRegistry) {
        Assert.notNull(openAiApi, "openAiApi must not be null");
        Assert.notNull(metadataMode, "metadataMode must not be null");
        Assert.notNull(options, "options must not be null");
        Assert.notNull(retryTemplate, "retryTemplate must not be null");
        Assert.notNull(observationRegistry, "observationRegistry must not be null");

        this.openAiApi = openAiApi;
        this.metadataMode = metadataMode;
        this.defaultOptions = options;
        this.retryTemplate = retryTemplate;
        this.observationRegistry = observationRegistry;
    }

    @Override
    public float[] embed(Document document) {
        Assert.notNull(document, "Document must not be null");
        return this.embed(document.getFormattedContent(this.metadataMode));
    }

    @Override
    public EmbeddingResponse call(EmbeddingRequest request) {
        OpenAiEmbeddingOptions requestOptions = mergeOptions(request.getOptions(), this.defaultOptions);
        OpenAiApi.EmbeddingRequest<List<String>> apiRequest = createRequest(request, requestOptions);

        var observationContext = EmbeddingModelObservationContext.builder()
                .embeddingRequest(request)
                .provider(OpenAiApiConstants.PROVIDER_NAME)
                .build();

        return EmbeddingModelObservationDocumentation.EMBEDDING_MODEL_OPERATION
                .observation(this.observationConvention, DEFAULT_OBSERVATION_CONVENTION, () -> observationContext,
                        this.observationRegistry)
                .observe(() -> {
                    OpenAiApi.EmbeddingList<OpenAiApi.Embedding> apiEmbeddingResponse = this.retryTemplate
                            .execute(ctx -> this.openAiApi.embeddings(apiRequest).getBody());

                    if (apiEmbeddingResponse == null) {
                        logger.warn("No embeddings returned for request: {}", request);
                        return new EmbeddingResponse(List.of());
                    }

                    var metadata = new EmbeddingResponseMetadata(apiEmbeddingResponse.model(),
                            getDefaultUsage(apiEmbeddingResponse.usage()));

                    List<Embedding> embeddings = apiEmbeddingResponse.data()
                            .stream()
                            .map(e -> new Embedding(e.embedding(), e.index()))
                            .toList();

                    EmbeddingResponse embeddingResponse = new EmbeddingResponse(embeddings, metadata);

                    observationContext.setResponse(embeddingResponse);

                    return embeddingResponse;
                });
    }

    private DefaultUsage getDefaultUsage(OpenAiApi.Usage usage) {
        if (usage == null) {
            return new DefaultUsage(0, 0);
        }
        return new DefaultUsage(usage.promptTokens(), usage.completionTokens(), usage.totalTokens(), usage);
    }

    private OpenAiApi.EmbeddingRequest<List<String>> createRequest(EmbeddingRequest request,
                                                                   OpenAiEmbeddingOptions requestOptions) {
        return new OpenAiApi.EmbeddingRequest<>(request.getInstructions(), requestOptions.getModel(),
                requestOptions.getEncodingFormat(), requestOptions.getDimensions(), requestOptions.getUser());
    }

    /**
     * Merge runtime and default {@link EmbeddingOptions} to compute the final options to
     * use in the request.
     */
    private OpenAiEmbeddingOptions mergeOptions(@Nullable EmbeddingOptions runtimeOptions,
                                                OpenAiEmbeddingOptions defaultOptions) {
        var runtimeOptionsForProvider = ModelOptionsUtils.copyToTarget(runtimeOptions, EmbeddingOptions.class,
                OpenAiEmbeddingOptions.class);

        if (runtimeOptionsForProvider == null) {
            return defaultOptions;
        }

        return OpenAiEmbeddingOptions.builder()
                // Handle portable embedding options
                .model(ModelOptionsUtils.mergeOption(runtimeOptionsForProvider.getModel(), defaultOptions.getModel()))
                .dimensions(ModelOptionsUtils.mergeOption(runtimeOptionsForProvider.getDimensions(),
                        defaultOptions.getDimensions()))
                // Handle OpenAI specific embedding options
                .encodingFormat(ModelOptionsUtils.mergeOption(runtimeOptionsForProvider.getEncodingFormat(),
                        defaultOptions.getEncodingFormat()))
                .user(ModelOptionsUtils.mergeOption(runtimeOptionsForProvider.getUser(), defaultOptions.getUser()))
                .build();
    }

    /**
     * Use the provided convention for reporting observation data
     * @param observationConvention The provided convention
     */
    public void setObservationConvention(EmbeddingModelObservationConvention observationConvention) {
        Assert.notNull(observationConvention, "observationConvention cannot be null");
        this.observationConvention = observationConvention;
    }

}
