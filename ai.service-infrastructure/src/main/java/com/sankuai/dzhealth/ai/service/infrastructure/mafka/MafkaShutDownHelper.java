package com.sankuai.dzhealth.ai.service.infrastructure.mafka;

import com.dianping.cat.Cat;
import com.google.common.collect.Lists;
import com.meituan.mafka.client.consumer.IConsumerProcessor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextClosedEvent;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;


@Service
@Slf4j
public class MafkaShutDownHelper implements ApplicationListener<ContextClosedEvent> {

    private static List<IConsumerProcessor> processors = Lists.newArrayList();

    public static void registerHook(IConsumerProcessor processor) {

        if (processor != null) {
            Cat.logEvent("MafkaShutDownHelper.registerHook", processor.getSendTopic());
            processors.add(processor);
        }
    }

    @Override
    public void onApplicationEvent(ContextClosedEvent event) {
        Cat.logEvent("INVALID_METHOD", "com.sankuai.dzhealth.medical.infrastructure.mafka.MafkaShutDownHelper.onApplicationEvent(org.springframework.context.event.ContextClosedEvent)");

        log.info("MafkaConsumerDestroy Count:{} ", processors.size());

        processors.forEach(consumer -> {

            if (consumer != null) {

                String topic = Optional.ofNullable(consumer.getSendTopic()).orElse("");

                try {
                    log.info("MafkaConsumerDestroy " + topic + "_start");
                    Cat.logEvent("MafkaShutDownHelper.destroyHook", topic);
                } catch (Exception e) {
                    log.info("MafkaConsumerDestroy fail" + topic + "_end");
                    Cat.logError(e);
                }finally {
                    try {
                        consumer.close();
                    } catch (Exception e) {
                        log.error("Failed to close consumer for topic: " + topic, e);
                        Cat.logError(e);
                    }
                }
            }
        });

    }
}