package com.sankuai.dzhealth.ai.service.infrastructure.cellar;
//
//import com.meituan.service.inf.kms.utils.KmsResultNullException;
//import com.taobao.tair3.client.Result;
//import com.taobao.tair3.client.TairClient;
//import com.taobao.tair3.client.config.impl.SimpleTairConfig;
//import com.taobao.tair3.client.config.impl.TairConfig;
//import com.taobao.tair3.client.error.TairException;
//import com.taobao.tair3.client.impl.MultiTairClient;
//import com.taobao.tair3.client.impl.ShareCellarClient;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.context.annotation.Bean;
//import org.springframework.stereotype.Component;
//
//@Slf4j
//@Component
//public class CellarHelper {
//
//
//    @Bean
//    public MultiTairClient tairClient() throws KmsResultNullException {
//        String localAppKey = "com.sankuai.dzhealth.ai.service";
//        String remoteAppKey = "com.sankuai.cellar.inf.qadaodian";
//
//
//        // todo 线上
//        remoteAppKey = "com.sankuai.cellar.gct.common";
//
//
//        TairConfig config = new SimpleTairConfig(localAppKey, remoteAppKey);
//// 如果需要指定客户端参数，可以传入ClientParameters对象
//// TairConfig config = new SimpleTairConfig(localAppKey, remoteAppKey, new ClientParameters(20, 100));
//        MultiTairClient tairClient = null;
//        try {
//            tairClient = ShareCellarClient.create(config);  // 相同的config，只会创建一个客户端实例，3.10.4及以上版本才可使用该方式进行初始化
//        } catch (TairException e) {
//            // 初始化失败
//            e.printStackTrace();
//            System.exit(-1);
//        }
//        return tairClient;
//    }
//
//
//    @Autowired
//    private MultiTairClient tairClient;
//
//
//    private static final short AREA = 1;
//
//    public String get(String key) {
//
//        TairClient.TairOption opt = new TairClient.TairOption(1000, (short) 0, 3600);
//
//        try {
//            Result<byte[]> result = tairClient.get(AREA, key.getBytes(), opt);
//            if (Result.ResultCode.OK.equals(result.getCode())) {
//                log.info("get is ok,value is : {}", new String(result.getResult()));
//                return new String(result.getResult());
//            } else {
//                log.info("get is bad");
//            }
//        } catch (Exception e) {
//            log.error("get is error", e);
//        }
//        return "";
//    }
//
//
//}