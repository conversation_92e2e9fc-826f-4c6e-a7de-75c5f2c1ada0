package com.sankuai.dzhealth.ai.service.agent.infrastructure.proxy.product;

import com.dianping.carnation.dto.TagTree;
import com.dianping.carnation.enums.BizGroupEnum;
import com.dianping.carnation.enums.TagSystemEnum;
import com.dianping.carnation.service.TagReferenceQueryService;
import com.meituan.mdp.boot.starter.pigeon.annotation.MdpPigeonClient;
import com.meituan.medicine.constant.response.IResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 标签服务RPC调用代理
 * 纯粹的基础设施层代理，只负责封装对carnation标签服务的RPC调用
 */
@Slf4j
@Service
public class TagServiceProxy {

    @MdpPigeonClient(url = "carnation.service.TagReferenceQueryService", timeout = 500L)
    private TagReferenceQueryService tagReferenceQueryService;

    @MdpPigeonClient(url = "carnation.service.TagService", timeout = 5000L)
    private com.dianping.carnation.service.TagService tagService;

    /**
     * 查询泛商品标签到医美项目标签的映射关系
     *
     * @param skuTagIds 泛商品标签ID列表
     * @return 映射关系，key为泛商品标签ID，value为对应的医美项目标签ID列表
     */
    public IResponse<Map<Long, List<Integer>>> queryTagMapping(List<Long> skuTagIds) {
        try {
            return tagReferenceQueryService.outerTagTagToInner(skuTagIds, TagSystemEnum.PRODUCT);
        } catch (Exception e) {
            log.error("queryTagMapping exception, skuTagIds={}", skuTagIds, e);
            return null;
        }
    }

    /**
     * 获取标签树结构（用于获取标签分级信息）
     *
     * @return 标签树，包含一级、二级、三级标签的层级关系
     */
    public TagTree getTagTree() {
        try {
            return tagService.getTagTree(BizGroupEnum.DEFAULT.getType());
        } catch (Exception e) {
            log.error("getTagTree exception", e);
            return null;
        }
    }
}

