package com.sankuai.dzhealth.ai.service.agent.infrastructure.proxy.doctorinfo;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.sankuai.dzhealth.ai.service.agent.request.DoctorInfoRequest;
import com.sankuai.medicalcosmetology.display.api.DoctorInfoQueryService;
import com.sankuai.medicalcosmetology.display.dto.AuthorizedBrandInfoDTO;
import com.sankuai.medicalcosmetology.display.dto.DoctorSimpleInfoDTO;
import com.sankuai.medicalcosmetology.display.dto.RemoteResponse;
import com.sankuai.medicalcosmetology.display.request.DoctorSimpleInfoBatchQueryRequest;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

/**
 * <AUTHOR> youtianyu
 * @version : 0.1
 * @since : 2025/7/16
 */
@RunWith(MockitoJUnitRunner.class)
@Slf4j
public class DoctorInfoAclTest {

    @InjectMocks
    private DoctorInfoAcl doctorInfoAcl;

    @Mock
    private DoctorInfoQueryService doctorInfoQueryService;

    @Test
    public void queryDoctorInfo_Success() {
        // 准备测试数据
        List<Long> doctorIds = Arrays.asList(1001L, 1002L);
        DoctorInfoRequest request = new DoctorInfoRequest();
        request.setMergeDoctorIds(doctorIds);

        // 构建模拟返回数据
        List<DoctorSimpleInfoDTO> doctorList = new ArrayList<>();

        // 第一个医生
        DoctorSimpleInfoDTO doctor1 = new DoctorSimpleInfoDTO();
        doctor1.setMergeDoctorId(1001L);
        doctor1.setClinicalTitle("主任医师");
        doctor1.setAcademicTitle("教授");
        doctor1.setWorkYears("15");
        doctor1.setSpeciality("皮肤美容");
        doctor1.setPositiveRate("99%");
        doctor1.setMergeReviewCnt(356L);
        doctor1.setIsSiNanRankDoctor(true);
        doctor1.setDoctorCaseNum(200L);

        // 添加品牌授权信息
        List<AuthorizedBrandInfoDTO> brands1 = new ArrayList<>();
        AuthorizedBrandInfoDTO brand1 = new AuthorizedBrandInfoDTO();
        brand1.setAuthorizedBrandId(101L);
        brand1.setAuthorizedBrandName("品牌A");
        brands1.add(brand1);
        doctor1.setAuthorizedBrands(brands1);

        doctorList.add(doctor1);

        // 第二个医生
        DoctorSimpleInfoDTO doctor2 = new DoctorSimpleInfoDTO();
        doctor2.setMergeDoctorId(1002L);
        doctor2.setClinicalTitle("副主任医师");
        doctor2.setAcademicTitle("副教授");
        doctor2.setWorkYears("10");
        doctor2.setSpeciality("微整形");
        doctor2.setPositiveRate("98%");
        doctor2.setMergeReviewCnt(200L);
        doctor2.setIsSiNanRankDoctor(false);
        doctor2.setDoctorCaseNum(150L);

        // 添加品牌授权信息
        List<AuthorizedBrandInfoDTO> brands2 = new ArrayList<>();
        AuthorizedBrandInfoDTO brand2 = new AuthorizedBrandInfoDTO();
        brand2.setAuthorizedBrandId(102L);
        brand2.setAuthorizedBrandName("品牌B");
        brands2.add(brand2);
        doctor2.setAuthorizedBrands(brands2);

        doctorList.add(doctor2);

        // 构建远程响应
        RemoteResponse<List<DoctorSimpleInfoDTO>> response = new RemoteResponse<>();
        response.setCode(200);
        response.setMsg("成功");
        response.setSuccess(true);
        response.setData(doctorList);

        // 设置Mock行为
        when(doctorInfoQueryService.queryDoctorSimpleInfo(any(DoctorSimpleInfoBatchQueryRequest.class)))
                .thenReturn(response);

        // 执行测试
        String result = doctorInfoAcl.queryDoctorInfo(request);

        System.out.println(result);

        // 验证结果
        assertNotNull(result);
        assertNotEquals("{}", result);

        JSONArray resultArray = JSON.parseArray(result);
        assertEquals(2, resultArray.size());

        // 验证第一个医生信息
        Map<String, Object> doctorMap1 = (Map<String, Object>) resultArray.get(0);
        assertEquals(1001, doctorMap1.get("融合医生id"));
        assertEquals("主任医师", doctorMap1.get("临床职称"));
        assertEquals("教授", doctorMap1.get("学术职称"));
        assertEquals("15", doctorMap1.get("从业年限"));
        assertEquals("皮肤美容", doctorMap1.get("擅长领域"));
        assertEquals("99%", doctorMap1.get("好评率"));
        assertEquals(356, doctorMap1.get("评价数"));
        assertEquals(true, doctorMap1.get("是否在司南榜"));
        assertEquals(200, doctorMap1.get("用户体验报告量"));

        // 验证品牌信息
        List<Map<String, Object>> brands = (List<Map<String, Object>>) doctorMap1.get("品牌授权信息");
        assertEquals(1, brands.size());
        assertEquals(101, brands.get(0).get("品牌id"));
        assertEquals("品牌A", brands.get(0).get("品牌名称"));

        // 验证调用次数
        verify(doctorInfoQueryService, times(1)).queryDoctorSimpleInfo(any(DoctorSimpleInfoBatchQueryRequest.class));
    }

    @Test
    public void queryDoctorInfo_EmptyResponse() {
        // 准备测试数据
        List<Long> doctorIds = List.of(1001L);
        DoctorInfoRequest request = new DoctorInfoRequest();
        request.setMergeDoctorIds(doctorIds);

        // 构建空响应
        RemoteResponse<List<DoctorSimpleInfoDTO>> response = new RemoteResponse<>();
        response.setCode(200);
        response.setMsg("成功");
        response.setSuccess(true);
        response.setData(new ArrayList<>());

        // 设置Mock行为
        when(doctorInfoQueryService.queryDoctorSimpleInfo(any(DoctorSimpleInfoBatchQueryRequest.class)))
                .thenReturn(response);

        // 执行测试
        String result = doctorInfoAcl.queryDoctorInfo(request);

        // 验证结果
        assertEquals("{}", result);

        // 验证调用次数
        verify(doctorInfoQueryService, times(1)).queryDoctorSimpleInfo(any(DoctorSimpleInfoBatchQueryRequest.class));
    }

    @Test
    public void queryDoctorInfo_NullResponse() {
        // 准备测试数据
        List<Long> doctorIds = List.of(1001L);
        DoctorInfoRequest request = new DoctorInfoRequest();
        request.setMergeDoctorIds(doctorIds);

        // 设置Mock行为返回null
        when(doctorInfoQueryService.queryDoctorSimpleInfo(any(DoctorSimpleInfoBatchQueryRequest.class)))
                .thenReturn(null);

        // 执行测试
        String result = doctorInfoAcl.queryDoctorInfo(request);

        // 验证结果
        assertEquals("{}", result);

        // 验证调用次数
        verify(doctorInfoQueryService, times(1)).queryDoctorSimpleInfo(any(DoctorSimpleInfoBatchQueryRequest.class));
    }

    @Test
    public void queryDoctorInfo_FailureResponse() {
        // 准备测试数据
        List<Long> doctorIds = List.of(1001L);
        DoctorInfoRequest request = new DoctorInfoRequest();
        request.setMergeDoctorIds(doctorIds);

        // 构建失败响应
        RemoteResponse<List<DoctorSimpleInfoDTO>> response = new RemoteResponse<>();
        response.setCode(500);
        response.setMsg("服务器错误");
        response.setSuccess(false);
        response.setData(null);

        // 设置Mock行为
        when(doctorInfoQueryService.queryDoctorSimpleInfo(any(DoctorSimpleInfoBatchQueryRequest.class)))
                .thenReturn(response);

        // 执行测试
        String result = doctorInfoAcl.queryDoctorInfo(request);

        // 验证结果
        assertEquals("{}", result);

        // 验证调用次数
        verify(doctorInfoQueryService, times(1)).queryDoctorSimpleInfo(any(DoctorSimpleInfoBatchQueryRequest.class));
    }

    @Test
    public void queryDoctorInfo_Exception() {
        // 准备测试数据
        List<Long> doctorIds = List.of(1001L);
        DoctorInfoRequest request = new DoctorInfoRequest();
        request.setMergeDoctorIds(doctorIds);

        // 设置Mock行为抛出异常
        when(doctorInfoQueryService.queryDoctorSimpleInfo(any(DoctorSimpleInfoBatchQueryRequest.class)))
                .thenThrow(new RuntimeException("测试异常"));

        // 验证是否抛出异常
        assertThrows(RuntimeException.class, () -> {
            doctorInfoAcl.queryDoctorInfo(request);
        });

        // 验证调用次数
        verify(doctorInfoQueryService, times(1)).queryDoctorSimpleInfo(any(DoctorSimpleInfoBatchQueryRequest.class));
    }
}