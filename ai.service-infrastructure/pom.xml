<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.sankuai.dzhealth</groupId>
        <artifactId>ai.service</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>ai.service-infrastructure</artifactId>
    <version>${revision}</version>
    <packaging>jar</packaging>
    <name>ai.service-infrastructure</name>

    <dependencies>
        <dependency>
            <groupId>com.sankuai.medicalcosmetology.doctor.function</groupId>
            <artifactId>doctor-datasync-api</artifactId>
            <version>1.0.5</version>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>account-api</artifactId>
            <version>4.0.21</version>
            <exclusions>
                <exclusion>
                    <groupId>dom4j</groupId>
                    <artifactId>dom4j</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.sankuai.sinai</groupId>
            <artifactId>sinai-api</artifactId>
            <version>1.0.87</version>
            <exclusions>
                <exclusion>
                    <groupId>javax.activation</groupId>
                    <artifactId>activation</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.dianping.poi</groupId>
            <artifactId>poi-relation-service-api</artifactId>
            <version>1.1.13</version>
            <exclusions>
                <exclusion>
                    <groupId>javax.activation</groupId>
                    <artifactId>activation</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.sankuai.dzhealth</groupId>
            <artifactId>ai.service-api</artifactId>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>dom4j</groupId>
                    <artifactId>dom4j</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.dianping.haima</groupId>
            <artifactId>haima-client</artifactId>
            <version>3.0.1</version>
            <exclusions>
                <exclusion>
                    <artifactId>commons-logging</artifactId>
                    <groupId>commons-logging</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>log4j</artifactId>
                    <groupId>log4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>dom4j</artifactId>
                    <groupId>dom4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.meituan.mdp.boot</groupId>
            <artifactId>mdp-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.mdp.boot</groupId>
            <artifactId>mdp-boot-starter-pigeon</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.mdp.boot</groupId>
            <artifactId>mdp-boot-starter-config</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.mdp.boot</groupId>
            <artifactId>mdp-boot-starter-cellar</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.mdp.boot</groupId>
            <artifactId>mdp-boot-starter-web-base</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.mdp.boot</groupId>
            <artifactId>mdp-boot-starter-web-toc</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.mdp.boot</groupId>
            <artifactId>mdp-boot-starter-zebra</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.mdp.boot</groupId>
            <artifactId>mdp-boot-starter-cerberus</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.mdp.boot</groupId>
            <artifactId>mdp-boot-starter-cache</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.mdp.boot</groupId>
            <artifactId>mdp-boot-starter-leaf</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.mdp.boot</groupId>
            <artifactId>mdp-boot-starter-squirrel</artifactId>
        </dependency>

        <!-- Spring AI -->

        <dependency>
            <groupId>com.sankuai.meituan</groupId>
            <artifactId>poros-java-api-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.meituan</groupId>
            <artifactId>poros-client</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.logging.log4j</groupId>
                    <artifactId>log4j-slf4j-impl</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.meituan.mdp.ai</groupId>
            <artifactId>mdp-ai-starter-openai</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.mdp.ai</groupId>
            <artifactId>mdp-ai-starter-deepseek</artifactId>
            <version>2.9.0.15</version>
        </dependency>
        <dependency>
            <groupId>com.meituan.mdp.ai</groupId>
            <artifactId>mdp-ai-starter-friday</artifactId>
            <version>2.9.0.15</version>
        </dependency>
        <dependency>
            <groupId>com.meituan.mdp.ai</groupId>
            <artifactId>mdp-ai-starter-elasticsearch-store</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>co.elastic.clients</groupId>
                    <artifactId>elasticsearch-java</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
        </dependency>

        <!-- KMS 加解密相关依赖 -->
        <dependency>
            <groupId>com.meituan.service.inf</groupId>
            <artifactId>kms-pangolin-sdk</artifactId>
            <version>0.14.0</version>
        </dependency>
        <dependency>
            <groupId>org.bouncycastle</groupId>
            <artifactId>bcprov-jdk15to18</artifactId>
            <version>1.79</version>
        </dependency>

        <dependency>
            <groupId>com.dianping.credit</groupId>
            <artifactId>credit-access-api</artifactId>
        </dependency>
        <!--测肤相关-->
        <dependency>
            <groupId>com.meituan.beauty</groupId>
            <artifactId>beauty-narcissus-api</artifactId>
            <version>1.1.28</version>
            <exclusions>
                <exclusion>
                    <groupId>javax.servlet</groupId>
                    <artifactId>javax.servlet-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>javax.validation</groupId>
                    <artifactId>validation-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>dom4j</groupId>
                    <artifactId>dom4j</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- 医生详情查询 -->
        <dependency>
            <groupId>com.sankuai.medicalcosmetology.doctor.function</groupId>
            <artifactId>doctor-display-api</artifactId>
            <version>1.0.16</version>
        </dependency>

        <!-- 向量搜索服务API -->
        <dependency>
            <groupId>com.sankuai.dzim</groupId>
            <artifactId>pilot-api</artifactId>
            <version>0.0.28</version>
        </dependency>

        <!-- 商户详情查询 -->
        <dependency>
            <groupId>com.sankuai.medicalcosmetology.mainpath</groupId>
            <artifactId>listing-api</artifactId>
            <version>1.0.1</version>
        </dependency>

        <!-- GPT 图片解析依赖 -->
        <dependency>
            <groupId>com.sankuai.beautycontent</groupId>
            <artifactId>gpt-api</artifactId>
            <version>1.0.9</version>
        </dependency>

        <dependency>
            <groupId>com.sankuai.beautycontent</groupId>
            <artifactId>experience-api</artifactId>
            <version>1.0.18</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.beautycontent</groupId>
            <artifactId>beauty-launch-api</artifactId>
            <version>0.1.41</version>
            <exclusions>
                <exclusion>
                    <groupId>com.dianping</groupId>
                    <artifactId>avatar-dao</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.meituan.mafka</groupId>
                    <artifactId>mafka-client_2.9</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.dianping.tpfun</groupId>
            <artifactId>sku-api</artifactId>
            <version>2.7.21</version>
            <exclusions>
                <exclusion>
                    <groupId>com.meituan.mafka</groupId>
                    <artifactId>mafka-client_2.9</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>javax.validation</groupId>
                    <artifactId>validation-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.dianping</groupId>
                    <artifactId>avatar-dao</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- 查询中心依赖 -->
        <dependency>
            <groupId>com.sankuai.general.product</groupId>
            <artifactId>client</artifactId>
            <version>1.0.54</version>
        </dependency>
        <dependency>
            <groupId>com.dianping.tpfun</groupId>
            <artifactId>sku-relation-api</artifactId>
            <version>1.0.7</version>
            <exclusions>
                <exclusion>
                    <groupId>javax.validation</groupId>
                    <artifactId>validation-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.meituan.medicine</groupId>
            <artifactId>medicine-constant-utils</artifactId>
            <version>1.0.9</version>
            <exclusions>
                <exclusion>
                    <groupId>com.meituan.mafka</groupId>
                    <artifactId>mafka-client_2.9</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>javax.validation</groupId>
                    <artifactId>validation-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.dianping</groupId>
                    <artifactId>avatar-dao</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.dianping.beauty</groupId>
            <artifactId>carnation-biz-api</artifactId>
            <version>2.0.73</version>
            <exclusions>
                <exclusion>
                    <artifactId>hibernate-validator</artifactId>
                    <groupId>org.hibernate.validator</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>lombok</artifactId>
                    <groupId>org.projectlombok</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>mobile-base-datatypes</artifactId>
                    <groupId>com.dianping.mobile</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>account-api</artifactId>
                    <groupId>com.dianping</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>beauty-zone-biz-api</artifactId>
                    <groupId>com.dianping.beauty</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>beauty-zone-remote-api</artifactId>
                    <groupId>com.dianping.beauty</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>lion-client</artifactId>
                    <groupId>com.dianping.lion</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>mns-invoker</artifactId>
                    <groupId>com.sankuai.octo</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>mapi-shell</artifactId>
                    <groupId>com.dianping.mobile</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>carnation-content-api</artifactId>
                    <groupId>com.meituan.beauty</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>dpsf-net</artifactId>
                    <groupId>com.dianping.dpsf</groupId>
                </exclusion>
                <exclusion>
                    <groupId>javax.validation</groupId>
                    <artifactId>validation-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.dianping</groupId>
                    <artifactId>avatar-dao</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.meituan.mafka</groupId>
                    <artifactId>mafka-client_2.9</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.meituan.beauty</groupId>
            <artifactId>beauty-arch-event</artifactId>
            <version>1.0.3</version>
            <exclusions>
                <exclusion>
                    <groupId>com.meituan.mafka</groupId>
                    <artifactId>mafka-client_2.9</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>javax.validation</groupId>
                    <artifactId>validation-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.dianping</groupId>
                    <artifactId>avatar-dao</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- 测试依赖 -->
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <version>4.13.2</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <version>4.8.0</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-junit-jupiter</artifactId>
            <version>4.8.0</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-test</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.mybatis.generator</groupId>
                <artifactId>mybatis-generator-maven-plugin</artifactId>
                <configuration>
                    <verbose>true</verbose>
                    <overwrite>true</overwrite>
                </configuration>
            </plugin>
            <plugin>
                <groupId>com.meituan.mdp.maven.plugins</groupId>
                <artifactId>mdp-mybatis-generator-maven-plugin</artifactId>
                <configuration>
                    <verbose>true</verbose>
                    <overwrite>true</overwrite>
                </configuration>
            </plugin>

        </plugins>
    </build>

</project>
