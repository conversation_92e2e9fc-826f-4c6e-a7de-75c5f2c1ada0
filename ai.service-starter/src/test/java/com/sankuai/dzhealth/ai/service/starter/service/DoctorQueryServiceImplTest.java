package com.sankuai.dzhealth.ai.service.starter.service;

import com.sankuai.dzhealth.ai.service.agent.infrastructure.proxy.doctorinfo.DoctorInfoAcl;
import com.sankuai.dzhealth.ai.service.agent.request.DoctorInfoRequest;
import com.sankuai.dzhealth.ai.service.api.RemoteResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class DoctorQueryServiceImplTest {

    @Mock
    private DoctorInfoAcl doctorInfoAcl;

    @InjectMocks
    private DoctorQueryServiceImpl doctorQueryService;

    private DoctorInfoRequest validRequest;

    @BeforeEach
    void setUp() {
        validRequest = new DoctorInfoRequest();
        validRequest.setMergeDoctorIds(Arrays.asList(1L, 2L, 3L));
    }

    @Test
    void queryDoctorsForAI_success_withValidData() {
        // Given
        String expectedJson = "{\"doctors\":[{\"id\":1,\"name\":\"张医生\"},{\"id\":2,\"name\":\"李医生\"}]}";
        when(doctorInfoAcl.queryDoctorInfo(any(DoctorInfoRequest.class))).thenReturn(expectedJson);

        // When
        RemoteResponse<String> response = doctorQueryService.queryDoctorsForAI(validRequest);

        // Then
        assertTrue(response.getSuccess());
        assertEquals(expectedJson, response.getData());
        verify(doctorInfoAcl, times(1)).queryDoctorInfo(validRequest);
    }

    @Test
    void queryDoctorsForAI_requestIsNull() {
        // When
        RemoteResponse<String> response = doctorQueryService.queryDoctorsForAI(null);

        // Then
        assertFalse(response.getSuccess());
        assertEquals("请求参数不能为空", response.getMsg());
        verify(doctorInfoAcl, never()).queryDoctorInfo(any());
    }

    @Test
    void queryDoctorsForAI_mergeDoctorIdsIsNull() {
        // Given
        DoctorInfoRequest request = new DoctorInfoRequest();
        request.setMergeDoctorIds(null);

        // When
        RemoteResponse<String> response = doctorQueryService.queryDoctorsForAI(request);

        // Then
        assertFalse(response.getSuccess());
        assertEquals("医生ID列表不能为空", response.getMsg());
        verify(doctorInfoAcl, never()).queryDoctorInfo(any());
    }

    @Test
    void queryDoctorsForAI_mergeDoctorIdsIsEmpty() {
        // Given
        DoctorInfoRequest request = new DoctorInfoRequest();
        request.setMergeDoctorIds(Collections.emptyList());

        // When
        RemoteResponse<String> response = doctorQueryService.queryDoctorsForAI(request);

        // Then
        assertFalse(response.getSuccess());
        assertEquals("医生ID列表不能为空", response.getMsg());
        verify(doctorInfoAcl, never()).queryDoctorInfo(any());
    }

    @Test
    void queryDoctorsForAI_mergeDoctorIdsSizeExceedsLimit() {
        // Given
        DoctorInfoRequest request = new DoctorInfoRequest();
        List<Long> largeDoctorIds = Arrays.asList(1L, 2L, 3L, 4L, 5L, 6L, 7L, 8L, 9L, 10L,
                11L, 12L, 13L, 14L, 15L, 16L, 17L, 18L, 19L, 20L, 21L);
        request.setMergeDoctorIds(largeDoctorIds);

        // When
        RemoteResponse<String> response = doctorQueryService.queryDoctorsForAI(request);

        // Then
        assertFalse(response.getSuccess());
        assertEquals("医生ID列表不能超过20个", response.getMsg());
        verify(doctorInfoAcl, never()).queryDoctorInfo(any());
    }

    @Test
    void queryDoctorsForAI_boundaryCase_exactly20DoctorIds() {
        // Given
        DoctorInfoRequest request = new DoctorInfoRequest();
        List<Long> exactlyTwentyIds = Arrays.asList(1L, 2L, 3L, 4L, 5L, 6L, 7L, 8L, 9L, 10L,
                11L, 12L, 13L, 14L, 15L, 16L, 17L, 18L, 19L, 20L);
        request.setMergeDoctorIds(exactlyTwentyIds);

        String expectedJson = "{\"doctors\":[]}";
        when(doctorInfoAcl.queryDoctorInfo(any(DoctorInfoRequest.class))).thenReturn(expectedJson);

        // When
        RemoteResponse<String> response = doctorQueryService.queryDoctorsForAI(request);

        // Then
        assertTrue(response.getSuccess());
        assertEquals(expectedJson, response.getData());
        verify(doctorInfoAcl, times(1)).queryDoctorInfo(request);
    }

    @Test
    void queryDoctorsForAI_aclReturnsEmptyString() {
        // Given
        when(doctorInfoAcl.queryDoctorInfo(any(DoctorInfoRequest.class))).thenReturn("");

        // When
        RemoteResponse<String> response = doctorQueryService.queryDoctorsForAI(validRequest);

        // Then
        assertTrue(response.getSuccess());
        assertEquals("{}", response.getData());
        verify(doctorInfoAcl, times(1)).queryDoctorInfo(validRequest);
    }

    @Test
    void queryDoctorsForAI_aclReturnsNull() {
        // Given
        when(doctorInfoAcl.queryDoctorInfo(any(DoctorInfoRequest.class))).thenReturn(null);

        // When
        RemoteResponse<String> response = doctorQueryService.queryDoctorsForAI(validRequest);

        // Then
        assertTrue(response.getSuccess());
        assertEquals("{}", response.getData());
        verify(doctorInfoAcl, times(1)).queryDoctorInfo(validRequest);
    }

    @Test
    void queryDoctorsForAI_aclReturnsEmptyJson() {
        // Given
        when(doctorInfoAcl.queryDoctorInfo(any(DoctorInfoRequest.class))).thenReturn("{}");

        // When
        RemoteResponse<String> response = doctorQueryService.queryDoctorsForAI(validRequest);

        // Then
        assertTrue(response.getSuccess());
        assertEquals("{}", response.getData());
        verify(doctorInfoAcl, times(1)).queryDoctorInfo(validRequest);
    }

    @Test
    void queryDoctorsForAI_aclReturnsWhitespaceString() {
        // Given
        when(doctorInfoAcl.queryDoctorInfo(any(DoctorInfoRequest.class))).thenReturn("   ");

        // When
        RemoteResponse<String> response = doctorQueryService.queryDoctorsForAI(validRequest);

        // Then
        assertTrue(response.getSuccess());
        assertEquals("{}", response.getData());
        verify(doctorInfoAcl, times(1)).queryDoctorInfo(validRequest);
    }

    @Test
    void queryDoctorsForAI_aclThrowsException() {
        // Given
        RuntimeException exception = new RuntimeException("数据库连接失败");
        when(doctorInfoAcl.queryDoctorInfo(any(DoctorInfoRequest.class))).thenThrow(exception);

        // When
        RemoteResponse<String> response = doctorQueryService.queryDoctorsForAI(validRequest);

        // Then
        assertFalse(response.getSuccess());
        assertEquals("查询医生信息失败: 数据库连接失败", response.getMsg());
        verify(doctorInfoAcl, times(1)).queryDoctorInfo(validRequest);
    }

    @Test
    void queryDoctorsForAI_singleDoctorId() {
        // Given
        DoctorInfoRequest request = new DoctorInfoRequest();
        request.setMergeDoctorIds(Collections.singletonList(1001L));

        String expectedJson = "{\"doctors\":[{\"id\":1001,\"name\":\"王医生\"}]}";
        when(doctorInfoAcl.queryDoctorInfo(any(DoctorInfoRequest.class))).thenReturn(expectedJson);

        // When
        RemoteResponse<String> response = doctorQueryService.queryDoctorsForAI(request);

        // Then
        assertTrue(response.getSuccess());
        assertEquals(expectedJson, response.getData());
        verify(doctorInfoAcl, times(1)).queryDoctorInfo(request);
    }

    @Test
    void queryDoctorsForAI_complexJsonResponse() {
        // Given
        String complexJson = "{\"doctors\":[{\"id\":1,\"name\":\"张医生\",\"department\":\"内科\",\"title\":\"主任医师\"}," +
                "{\"id\":2,\"name\":\"李医生\",\"department\":\"外科\",\"title\":\"副主任医师\"}]}";
        when(doctorInfoAcl.queryDoctorInfo(any(DoctorInfoRequest.class))).thenReturn(complexJson);

        // When
        RemoteResponse<String> response = doctorQueryService.queryDoctorsForAI(validRequest);

        // Then
        assertTrue(response.getSuccess());
        assertEquals(complexJson, response.getData());
        verify(doctorInfoAcl, times(1)).queryDoctorInfo(validRequest);
    }
}

