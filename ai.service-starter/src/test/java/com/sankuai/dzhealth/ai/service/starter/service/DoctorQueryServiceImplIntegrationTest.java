package com.sankuai.dzhealth.ai.service.starter.service;

import com.sankuai.dzhealth.ai.service.agent.api.DoctorQueryService;
import com.sankuai.dzhealth.ai.service.agent.request.DoctorInfoRequest;
import com.sankuai.dzhealth.ai.service.api.RemoteResponse;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;

import java.util.Arrays;

import static org.junit.Assert.assertNotNull;

@SpringBootTest
@TestPropertySource(properties = {
    "spring.profiles.active=test"
})
class DoctorQueryServiceImplIntegrationTest {

    @Autowired
    private DoctorQueryService doctorQueryService;

    @Test
    void testQueryDoctorsForAI_Integration() {
        // Given
        DoctorInfoRequest request = new DoctorInfoRequest();
        request.setMergeDoctorIds(Arrays.asList(124L, 112L));

        // When
        RemoteResponse<String> result = doctorQueryService.queryDoctorsForAI(request);

        // Then
        assertNotNull(result);
        // 根据实际情况验证结果
        System.out.println(result);
    }
}

