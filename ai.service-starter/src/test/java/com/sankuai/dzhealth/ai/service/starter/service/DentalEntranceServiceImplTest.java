package com.sankuai.dzhealth.ai.service.starter.service;

import com.sankuai.dzhealth.ai.service.agent.dto.DentalEntranceResponse;
import com.sankuai.dzhealth.ai.service.agent.request.DentalEntranceRequest;
import com.sankuai.dzhealth.ai.service.api.RemoteResponse;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

import static org.junit.Assert.*;

@RunWith(SpringRunner.class)
@SpringBootTest
public class DentalEntranceServiceImplTest {

    @Resource
    private DentalEntranceServiceImpl dentalEntranceService;

    /**
     * 测试请求参数为null的情况
     */
    @Test
    public void testGetDentalEntrance_RequestNull() {
        // arrange
        DentalEntranceRequest request = null;

        // act
        RemoteResponse<DentalEntranceResponse> response = dentalEntranceService.getDentalEntrance(request);

        // assert
        assertFalse(response.getSuccess());
        assertEquals("参数错误", response.getMsg());
        assertNull(response.getData());
    }

    /**
     * 测试请求参数中platform为null的情况
     */
    @Test
    public void testGetDentalEntrance_PlatformNull() {
        // arrange
        DentalEntranceRequest request = new DentalEntranceRequest();
        request.setUserId(123L);
        request.setPlatform(null);

        // act
        RemoteResponse<DentalEntranceResponse> response = dentalEntranceService.getDentalEntrance(request);

        // assert
        assertFalse(response.getSuccess());
        assertEquals("参数错误", response.getMsg());
        assertNull(response.getData());
    }

    /**
     * 测试请求参数中userId为null的情况
     */
    @Test
    public void testGetDentalEntrance_UserIdNull() {
        // arrange
        DentalEntranceRequest request = new DentalEntranceRequest();
        request.setUserId(null);
        request.setPlatform(1);

        // act
        RemoteResponse<DentalEntranceResponse> response = dentalEntranceService.getDentalEntrance(request);

        // assert
        assertFalse(response.getSuccess());
        assertEquals("参数错误", response.getMsg());
        assertNull(response.getData());
    }

    /**
     * 测试正常请求的情况
     */
    @Test
    public void testGetDentalEntrance_ValidRequest() {
        // arrange
        DentalEntranceRequest request = new DentalEntranceRequest();
        request.setUserId(123L);
        request.setPlatform(1);

        // act
        RemoteResponse<DentalEntranceResponse> response = dentalEntranceService.getDentalEntrance(request);

        System.out.println(response.getData());

        // assert
        assertTrue(response.getSuccess());
        assertNotNull(response.getData());

        DentalEntranceResponse data = response.getData();
        assertNotNull(data.getJumpLinkWithoutText());
        assertNotNull(data.getJumpLinkWithText());
        assertNotNull(data.getGuideQuestionText());

        // 验证基础URL
        assertEquals("imeituan://www.meituan.com/mrn?mrn_biz=gcbu&mrn_entry=aesthetic-medicine-ai&mrn_component=AestheticMedicineAI&categoryId=506&platform=1",
                    data.getJumpLinkWithoutText());

        // 验证带文本的链接包含平台参数
        assertTrue(data.getJumpLinkWithText().contains("platform=1"));
        assertTrue(data.getJumpLinkWithText().contains("categoryId=506"));

        System.out.println(response.getData());
    }

    /**
     * 测试不同平台参数的情况
     */
    @Test
    public void testGetDentalEntrance_DifferentPlatforms() {
        // 测试平台1
        DentalEntranceRequest request1 = new DentalEntranceRequest();
        request1.setUserId(123L);
        request1.setPlatform(1);

        RemoteResponse<DentalEntranceResponse> response1 = dentalEntranceService.getDentalEntrance(request1);
        assertTrue(response1.getSuccess());
        assertTrue(response1.getData().getJumpLinkWithText().contains("platform=1"));

        // 测试平台2
        DentalEntranceRequest request2 = new DentalEntranceRequest();
        request2.setUserId(456L);
        request2.setPlatform(2);

        RemoteResponse<DentalEntranceResponse> response2 = dentalEntranceService.getDentalEntrance(request2);
        assertTrue(response2.getSuccess());
        assertTrue(response2.getData().getJumpLinkWithText().contains("platform=2"));
    }

    /**
     * 测试响应数据结构完整性
     */
    @Test
    public void testGetDentalEntrance_ResponseStructure() {
        // arrange
        DentalEntranceRequest request = new DentalEntranceRequest();
        request.setUserId(123L);
        request.setPlatform(1);

        // act
        RemoteResponse<DentalEntranceResponse> response = dentalEntranceService.getDentalEntrance(request);

        // assert
        assertTrue(response.getSuccess());
        assertNotNull(response.getData());

        DentalEntranceResponse data = response.getData();

        // 验证jumpLinkWithoutText不为空且格式正确
        assertNotNull(data.getJumpLinkWithoutText());
        assertTrue(data.getJumpLinkWithoutText().startsWith("imeituan://"));

        // 验证jumpLinkWithText不为空且包含必要参数
        assertNotNull(data.getJumpLinkWithText());
        assertTrue(data.getJumpLinkWithText().contains("categoryId=506"));
        assertTrue(data.getJumpLinkWithText().contains("platform="));
        assertTrue(data.getJumpLinkWithText().contains("question="));

        // 验证guideQuestionText不为null
        assertNotNull(data.getGuideQuestionText());
    }

    /**
     * 测试URL编码是否正确
     */
    @Test
    public void testGetDentalEntrance_UrlEncoding() {
        // arrange
        DentalEntranceRequest request = new DentalEntranceRequest();
        request.setUserId(123L);
        request.setPlatform(1);

        // act
        RemoteResponse<DentalEntranceResponse> response = dentalEntranceService.getDentalEntrance(request);

        // assert
        assertTrue(response.getSuccess());
        assertNotNull(response.getData());

        String jumpLinkWithText = response.getData().getJumpLinkWithText();

        // 验证URL格式正确，包含question参数
        assertTrue(jumpLinkWithText.contains("question="));
    }
}

