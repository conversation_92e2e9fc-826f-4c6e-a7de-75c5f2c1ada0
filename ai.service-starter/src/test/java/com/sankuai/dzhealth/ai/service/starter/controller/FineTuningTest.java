package com.sankuai.dzhealth.ai.service.starter.controller;

import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.dianping.lion.client.util.CollectionUtils;
import com.sankuai.dzhealth.ai.service.agent.domain.model.fine_tuning.GraphData;
import com.sankuai.dzhealth.ai.service.agent.domain.model.fine_tuning.GraphNode;
import com.sankuai.dzhealth.ai.service.starter.util.GraphDataLoader;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;

import java.util.*;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

@Slf4j
class FineTuningTest {

    GraphDataLoader graphDataLoader = new GraphDataLoader();

    @Test
    void evaluation() {
        try {
            // 从classpath根目录读取graph-data.json文件
            GraphData graphData = graphDataLoader.loadFromClasspath("graph-data.json");

            log.info("成功读取并反序列化graph-data.json文件，节点数量: {}",
                    graphData.getNodes() != null ? graphData.getNodes().size() : 0);
            var nodeMap = new HashMap<String, GraphNode>();
            graphData.getNodes().forEach(node -> {
                if (!"decision".equals(node.getNodeType())) {
                    return;
                }
                nodeMap.put(node.getId(), node);
            });
            nodeMap.forEach(
                    (k, v) -> {
                        if (!"decision".equals(v.getNodeType())) {
                            return;
                        }
                        List<GraphNode> childrenNode = new ArrayList<>();
                        v.getChildren().forEach(e -> {
                            GraphNode child = nodeMap.get(e);
                            if (child != null) {
                                childrenNode.add(child);
                            }
                        });
                        v.setChildrenNode(childrenNode);
                        v.getParents().forEach(parentID -> {
                            GraphNode parent = nodeMap.get(parentID);
                            v.setParentsNode(parent);
                        });
                    }
            );

            // 修正根节点判断逻辑：基于parents列表是否为空来判断
            var rootList = new ArrayList<GraphNode>();
            nodeMap.forEach((k, node) -> {
                if (!"decision".equals(node.getNodeType())) {
                    return;
                }
                if (node.getParentsNode() == null) {
                    rootList.add(node);
                }
            });

            // DFS遍历根节点列表
            System.out.println("开始DFS遍历，根节点数量: " + rootList.size());
            for (GraphNode root : rootList) {
//                System.out.println("根节点: " + root.toString());
                dfsTraversal(root, new HashSet<>(), 0);
            }


        } catch (Exception e) {
            log.error("读取graph-data.json文件失败", e);
        }
    }



    @Test
    void test1() {
        Transaction t1 = Cat.newTransaction("method1", "test1");

        Transaction t2 = Cat.newTransaction("method2", "test2");
        String currentMessageId = Cat.getCurrentMessageId();
        t2.complete();
        currentMessageId = Cat.getCurrentMessageId();
        System.out.println(currentMessageId);
        test2();
        t1.complete();
    }

    void test2() {
        Transaction t2 = Cat.newTransaction("method2", "test2");
        String currentMessageId = Cat.getCurrentMessageId();
        String name = t2.getName();
        Object data = t2.getData();
        System.out.println(currentMessageId);
        t2.complete();
    }

    /**
     * DFS递归遍历图节点
     *
     * @param node    当前节点
     * @param visited 已访问节点集合，防止环路
     * @param depth   当前深度，用于缩进显示
     */
    private void dfsTraversal(GraphNode node, Set<String> visited, int depth) {
        if (node == null ) {
            return;
        }
        if (visited.contains(node.getId())) {
            System.out.println("dup" + node.getId());
            return;
        }

        // 标记当前节点为已访问
        visited.add(node.getId());


        // 打印当前节点信息（带缩进显示层级）
        String indent = "  ".repeat(depth);
        System.out.println(indent + node.getId());
        // 递归遍历子节点
        if (CollectionUtils.isNotEmpty(node.getChildrenNode())) {
            for (GraphNode child : node.getChildrenNode()) {
                dfsTraversal(child, visited, depth + 1);
            }
        }

//        // 如果没有构建childrenNode关系，则通过children ID列表遍历
//        if (CollectionUtils.isEmpty(node.getChildrenNode()) && CollectionUtils.isNotEmpty(node.getChildren())) {
//            System.out.println(indent + "  子节点ID列表: " + node.getChildren());
//        }
    }
}

