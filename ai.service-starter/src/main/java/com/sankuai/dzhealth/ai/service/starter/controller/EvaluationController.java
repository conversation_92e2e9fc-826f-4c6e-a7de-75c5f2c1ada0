package com.sankuai.dzhealth.ai.service.starter.controller;

import com.dianping.lion.client.util.CollectionUtils;
import com.dianping.rhino.Rhino;
import com.dianping.rhino.threadpool.DefaultThreadPoolProperties;
import com.dianping.rhino.threadpool.ThreadPool;
import com.dianping.zebra.group.router.ZebraForceMasterHelper;
import com.fasterxml.jackson.core.type.TypeReference;
import com.meituan.beauty.fundamental.light.plat.Platform;
import com.meituan.mtrace.Tracer;
import com.sankuai.dzhealth.ai.service.agent.domain.entity.ChatSessionMessageEntity;
import com.sankuai.dzhealth.ai.service.agent.domain.repository.chatSession.ChatSessionMessageRepository;
import com.sankuai.dzhealth.ai.service.agent.domain.service.evaluation.EvaluationDataService;
import com.sankuai.dzhealth.ai.service.agent.domain.service.evaluation.EvaluationDialogueService;
import com.sankuai.dzhealth.ai.service.agent.domain.service.evaluation.EvaluationMetricsService;
import com.sankuai.dzhealth.ai.service.agent.domain.utils.ExcelUtils;
import com.sankuai.dzhealth.ai.service.agent.domain.utils.HaimaAIUtils;
import com.sankuai.dzhealth.ai.service.agent.domain.utils.JsonExtractorUtil;
import com.sankuai.dzhealth.ai.service.agent.dto.SimulatedDialogueChatDTO;
import com.sankuai.dzhealth.ai.service.agent.dto.Topic;
import com.sankuai.dzhealth.ai.service.agent.infrastructure.dal.entity.*;
import com.sankuai.dzhealth.ai.service.agent.infrastructure.dal.repository.evaluation.EvaluationRecordsRepository;
import com.sankuai.dzhealth.ai.service.agent.infrastructure.dal.repository.evaluation.EvaluationSessionMessageRecordsRepository;
import com.sankuai.dzhealth.ai.service.agent.infrastructure.dal.repository.evaluation.EvaluationSessionRecordsRepository;
import com.sankuai.dzhealth.ai.service.agent.infrastructure.model.EvaluationResultsResponse;
import com.sankuai.dzhealth.ai.service.agent.request.EvaluationResultsRequest;
import com.sankuai.dzhealth.ai.service.agent.request.MessageAIRequest;
import com.sankuai.dzhealth.ai.service.api.RemoteResponse;
import com.sankuai.dzhealth.ai.service.domain.evaluation.MultiDialogueEvaluation;
import com.sankuai.dzhealth.ai.service.domain.evaluation.MultiEvaluationService;
import com.sankuai.dzhealth.ai.service.domain.evaluation.entity.EvaluationRequest;
import com.sankuai.dzhealth.ai.service.domain.evaluation.entity.MessageCopyRequest;
import com.sankuai.dzhealth.ai.service.domain.utils.JsonUtils;
import com.sankuai.dzhealth.ai.service.dto.evaluation.*;
import com.sankuai.dzhealth.ai.service.dto.haima.EvaluationCaseInfo;
import com.sankuai.dzhealth.ai.service.dto.haima.EvaluationMetric;
import com.sankuai.dzhealth.ai.service.dto.stream.StreamEventDTO;
import com.sankuai.dzhealth.ai.service.enums.StreamEventCardTypeEnum;
import com.sankuai.dzhealth.ai.service.infrastructure.utils.UidUtils;
import com.sankuai.dzhealth.ai.service.request.common.BasicParam;
import joptsimple.internal.Strings;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.sankuai.dzhealth.ai.service.agent.domain.enums.TaskTypeEnum.*;


@Slf4j
@RestController
@RequestMapping("/evaluation")
@RequiredArgsConstructor
public class EvaluationController {

    @Autowired
    private ChatMessageController controller;

    @Autowired
    private ChatSessionMessageRepository chatSessionMessageRepository;

    @Autowired
    private EvaluationDataService evaluationDataService;

    @Autowired
    private EvaluationMetricsService evaluationMetricsService;

    @Autowired
    @Qualifier("simulatedDialogueChatClient")
    private ChatClient simulatedDialogueChatClient;

    @Autowired
    private HaimaAIUtils haimaAIUtils;

    @Autowired
    private UidUtils uidUtils;

    @Autowired
    private ExcelUtils excelUtils;

    @Autowired
    private MultiDialogueEvaluation multiDialogueEvaluation;


    @Autowired
    private EvaluationRecordsRepository evaluationRecordsRepository;

    @Autowired
    private EvaluationSessionRecordsRepository evaluationSessionRecordsRepository;

    @Autowired
    private EvaluationSessionMessageRecordsRepository evaluationSessionMessageRecordsRepository;

    @Autowired
    private MultiEvaluationService multiEvaluationService;

    @Autowired
    private EvaluationDialogueService evaluationDialogueService;

    public static final ThreadPool Evaluation_POOL = Rhino.newThreadPool("Evaluation_POOL",
            DefaultThreadPoolProperties.Setter().withCoreSize(100).withMaxSize(200).withMaxQueueSize(1000));

    // 模拟对轮对话-评测数据构建
    @GetMapping("/simulate_dialogue")
    public RemoteResponse<String> evaluation(@RequestParam(value = "evaluation_name") String evaluationName,
                                             @RequestParam(value = "evaluation_id", required = false) String evaluationID,
                                             @RequestParam(value = "caseIdsStr", required = false) String caseIdsStr,
                                             @RequestParam(value = "limit", required = false) String limitStr,
                                             @RequestParam(value = "offset", required = false) String offsetStr,
                                             @RequestParam(value = "step", required = false) Integer step,
                                             @RequestParam(value = "user_id", required = false) String userID) {
        List<EvaluationCaseInfo> evaluationData = evaluationDataService.getEvaluationCaseInfo();
        if (CollectionUtils.isEmpty(evaluationData)) {
            return RemoteResponse.buildSuccess("is empty");
        }
        String traceID = Tracer.getServerTracer().getTraceId();
        if (!Strings.isNullOrEmpty(caseIdsStr)) {
            List<Integer> ids = JsonUtils.parseArray(caseIdsStr, Integer.class);
            evaluationData = evaluationData.stream().filter(e -> {
                return ids.contains(e.getNum());
            }).toList();
        }

        if (!Strings.isNullOrEmpty(limitStr)) {
            int limit = Integer.parseInt(limitStr);
            if (!Strings.isNullOrEmpty(offsetStr)) {
                int offset = Integer.parseInt(offsetStr);
                List<EvaluationCaseInfo> data = new ArrayList<>();
                for (int i = 0; i < limit; i++) {
                    data.add(evaluationData.get(offset + i));
                }
                evaluationData = data;
            }
        }

        if (evaluationID == null) {
            evaluationID = String.valueOf(uidUtils.nextSnowflakeId(UidUtils.CHAT_EVALUATION_TAG));
            EvaluationRecordsDO recordsDO = EvaluationRecordsDO.
                    builder().
                    evaluationId(evaluationID).
                    evaluationName(evaluationName).
                    status(0).
                    createTime(new Date()).
                    endTime(new Date()).
                    updateTime(new Date()).
                    build();
            evaluationRecordsRepository.insert(recordsDO);
        }

        ConcurrentHashMap<Integer, String> errorMap = new ConcurrentHashMap<>();
        ConcurrentHashMap<Integer, String> case2Session = new ConcurrentHashMap<>();
        int stepN = 10;
        if (step != null) {
            stepN = step;
        }
        String result = "";
        for (int i = 0; i < evaluationData.size(); i = i + stepN) {
            int max = Math.min(evaluationData.size(), i + stepN);
            List<CompletableFuture<Void>> futures = new ArrayList<>();
            for (int j = i; j < max; j++) {
                EvaluationCaseInfo data = evaluationData.get(j);
                String finalEvaluationID = evaluationID;
                CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                    long start = System.currentTimeMillis();
                    try {
                        long userIDL = 5552418191L;
                        if (!Strings.isNullOrEmpty(userID)) {
                            userIDL = Long.parseLong(userID);
                        }
                        log.info("[测评]开始case_id={}", caseIdsStr);
                        String sessionID = evaluateOneCase(finalEvaluationID, data, userIDL);
                        long duration = System.currentTimeMillis() - start;
                        log.info("[测评]结束case_id={} 耗时={}秒", caseIdsStr, duration / 1000.0);
                        case2Session.put(data.getNum(), sessionID);
                    } catch (Exception e) {
                        errorMap.put(data.getNum(), e.getMessage());
                        long duration = System.currentTimeMillis() - start;
                        log.error("[测评]失败case_id={} 耗时={}秒 error={}", caseIdsStr, duration / 1000.0, e);
                    }
                }, Evaluation_POOL.getExecutor());
                future.completeOnTimeout(null, 300, TimeUnit.SECONDS);
                futures.add(future);
            }
            try {
                CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
            } catch (Exception e) {
                result += e.getMessage();
            }
        }

        return RemoteResponse.buildSuccess(result);
    }

    // 对话回放
    @PostMapping("/dialogue_replay")
    public RemoteResponse<String> dialogueReplay(@RequestBody MessageCopyRequest request) {
        String s = evaluationDialogueService.dialogueReplay(request);
        return RemoteResponse.buildSuccess(s);
    }

    @GetMapping("/delete_dialogue")
    public RemoteResponse<Boolean> evaluation(@RequestParam(value = "evaluation_id", required = false) String evaluationID,
                                              @RequestParam(value = "session_ids", required = false) List<String> sessionIDs) {
        List<EvaluationCaseInfo> evaluationData = evaluationDataService.getEvaluationCaseInfo();
        if (CollectionUtils.isEmpty(evaluationData)) {
            return RemoteResponse.buildSuccess(true);
        }
        if (CollectionUtils.isNotEmpty(sessionIDs)) {
            int count = evaluationSessionRecordsRepository.delete(sessionIDs);
            if (count > 0) {
                return RemoteResponse.buildSuccess(true);
            }
        }
        return RemoteResponse.buildSuccess(false);
    }

    // 执行大模型离线评测
    @PostMapping("/re_evaluate")
    public RemoteResponse<String> reEvaluateByEvaluationId(
            @RequestBody EvaluationRequest request) {
        try {
            // 调用服务层方法进行重新评估
            multiEvaluationService.reEvaluateByEvaluationId(request);

            log.info("重新评估任务已启动，evaluationId: {}, sessionIds: {}, version: {}",
                    request.getEvaluationId(), request.getSessionIds(), request.getVersion());

            return RemoteResponse.buildSuccess("重新评估任务已启动");
        } catch (Exception e) {
            log.error("重新评估失败，evaluationId: {}, sessionIds: {}, version: {}",
                    request.getEvaluationId(), request.getSessionIds(), request.getVersion(), e);
            return RemoteResponse.buildFail("重新评估失败: " + e.getMessage());
        }
    }

    // 对话记录下载
    @GetMapping("/download_dialogue_record")
    public ResponseEntity<byte[]> downloadDialogueRecordCSV(@RequestParam(value = "evaluation_id") String evaluationId) {
        return evaluationDialogueService.downloadDialogueRecordCSV(evaluationId);
    }

    // 获取大模型评测结果
    @GetMapping("/download_evaluation_result")
    public ResponseEntity<byte[]> evaluationResult(@RequestParam(value = "evaluation_id") String evaluationID, @RequestParam(value = "version") Integer version) {
        return evaluationDialogueService.evaluationResult(evaluationID, version);
    }

    @GetMapping("/evaluation_list")
    public RemoteResponse<List<EvaluationListDTO>> getEvaluationList(
            @RequestParam(value = "page_num", defaultValue = "1") Integer pageNum,
            @RequestParam(value = "page_size", defaultValue = "10") Integer pageSize) {
        try {
            // 计算分页参数
            int offset = (pageNum - 1) * pageSize;
            // 查询测评记录
            List<EvaluationRecordsDO> records = evaluationRecordsRepository.findAllOrderByCreateTimeDesc(pageSize, offset);
            // 转换为DTO
            List<EvaluationListDTO> result = records.stream()
                    .map(record -> EvaluationListDTO.
                            builder().
                            evaluationId(record.getEvaluationId()).
                            evaluationName(record.getEvaluationName()).
                            status(EvaluationRecordsDO.StatusEnum.getByCode(record.getStatus()).msg).
                            createTime(DateFormatUtils.format(record.getCreateTime(), "yyyy-MM-dd HH:mm:ss")).
                            endTime(DateFormatUtils.format(record.getEndTime(), "yyyy-MM-dd HH:mm:ss")).
                            build()).collect(Collectors.toList());
            return RemoteResponse.buildSuccess(result);
        } catch (Exception e) {
            log.error("获取测评列表失败", e);
            return RemoteResponse.buildFail("获取测评列表失败: " + e.getMessage());
        }
    }


    /**
     * 根据测评ID查询对应的会话session列表，按创建时间倒序排列
     *
     * @param evaluationId 测评ID
     * @return 会话session列表
     */
    @GetMapping("/session_list")
    public RemoteResponse<List<SessionListDTO>> getSessionList(
            @RequestParam("evaluation_id") String evaluationId) {

        Map<String, EvaluationCaseInfo> evaluationData = evaluationDataService.getEvaluationCaseInfoMap();
        if (CollectionUtils.isEmpty(evaluationData)) {
            return RemoteResponse.buildFail("获取会话列表失败");
        }

        try {
            // 根据测评ID查询对应的会话记录，按创建时间倒序排列
            List<EvaluationSessionRecordsDO> sessionRecords = evaluationSessionRecordsRepository
                    .findByEvaluationIdOrderByCreateTimeDesc(evaluationId, 1000, 0);
            List<SessionListDTO> result = sessionRecords.stream()
                    .map(record -> {
                        EvaluationCaseInfo caseData = evaluationData.get(record.getCaseId());
                        return SessionListDTO.
                                builder().
                                sessionId(record.getSessionId()).
                                caseId(record.getCaseId()).
                                sessionName(caseData != null ? caseData.getInitialQuestion() : "").
                                status(EvaluationSessionRecordsDO.StatusEnum.getByCode(record.getStatus()).msg).
                                createTime(DateFormatUtils.format(record.getCreateTime(), "yyyy-MM-dd HH:mm:ss")).
                                build();
                    }).collect(Collectors.toList());

            return RemoteResponse.buildSuccess(result);
        } catch (Exception e) {
            log.error("获取会话列表失败，evaluationId: {}", evaluationId, e);
            return RemoteResponse.buildFail("获取会话列表失败: " + e.getMessage());
        }
    }


    @GetMapping("/message_metric")
    public RemoteResponse<MessageMetricDTO> getMessageMetricList(@RequestParam(value = "message_id") String messageID) {
        try {
            // 人工标注结果
            EvaluationSessionMessageRecordsDO recordsDO = evaluationSessionMessageRecordsRepository.findByMessageId(messageID);
            if (recordsDO == null) {
//                return RemoteResponse.buildFail("获取消息列表失败");
            }

            // 评测指标信息
            List<EvaluationMetric> evaluationMetricInfoList = evaluationMetricsService.getEvaluationMetricInfo();
            HashMap<String, List<EvaluationMetric>> modelSceneMetricInfoList = new HashMap<>();
            evaluationMetricInfoList.forEach(e -> {
                modelSceneMetricInfoList.computeIfAbsent(e.getScene(), k -> new ArrayList<>()).add(e);
            });
            Map<String, EvaluationMetric> evaluationMetricInfo = evaluationMetricInfoList.stream().collect(Collectors.toMap(EvaluationMetric::getKey, e -> e, (existing, replacement) -> existing));

            List<MessageEvaluationResultEntity> messageEvaluationResults = multiDialogueEvaluation.getMessageEvaluationResults(messageID);
            Map<String, List<MessageEvaluationResultEntity>> groupedByModelScene;
            if (CollectionUtils.isEmpty(messageEvaluationResults)) {
                groupedByModelScene = new HashMap<>();
            } else {
                groupedByModelScene = messageEvaluationResults.stream()
                        .collect(Collectors.groupingBy(MessageEvaluationResultEntity::getModelScene));
            }
            // 按照 modelScene 分组
            Map<String, Map<String, MessageEvaluationResultEntity>> modelSceneMap = new HashMap<>();
            groupedByModelScene.forEach((k, v) -> {
                modelSceneMap.put(k, v.stream().collect(Collectors.toMap(MessageEvaluationResultEntity::getItem, e -> e)));
            });


            List<MessageMetricDTO.Header> headers = new ArrayList<>();
            Map<String, MessageMetricDTO.Metric> metrics = new HashMap<>();


            // 大模型测评指标
            String[] modelSceneOrder = new String[]{MEDICAL_AGENT_TASK.getType(), MEDICAL_BAIKE_TASK.getType(), "medicalDecision", MEDICAL_APPOINTMENT_TASK.getType(), RELATED_QUESTION_TASK.getType()};
            String[] metricOrder = new String[]{"intent_correctness", "classification_rationality", "classification_consistency",
                    "recommendation_ability", "clarification", "guess_question", "scientific_explanation", "content_risk",
                    "appointment_completion", "dialogue_flow", "task_completion"};

            String intent = "";
            for (String s : modelSceneOrder) {
                if (!modelSceneMap.containsKey(s)) {
                    continue;
                }
                if (s.equals(MEDICAL_BAIKE_TASK.getType())) {
                    intent = "百科";
                } else if (s.equals(MEDICAL_APPOINTMENT_TASK.getType())) {
                    intent = "预约";
                } else if (s.equals("medicalDecision")) {
                    intent = "导购";
                }

                List<EvaluationMetric> evaluationMetrics = modelSceneMetricInfoList.get(s);
                Map<String, MessageEvaluationResultEntity> stringMessageEvaluationResultEntityMap = modelSceneMap.get(s);
                if (CollectionUtils.isNotEmpty(evaluationMetrics)) {
                    evaluationMetrics.forEach(e -> {
                        if (CollectionUtils.isNotEmpty(stringMessageEvaluationResultEntityMap)) {
                            MessageEvaluationResultEntity result = stringMessageEvaluationResultEntityMap.get(e.getKey());
                            headers.add(new MessageMetricDTO.Header(e.getKey(), e.getName()));
                            metrics.put(e.getKey(), new MessageMetricDTO.Metric(result.getScore().toBigInteger().toString(), result.getReason()));
                            return;
                        }
                        // 大模型人工测评指标缺失
                        headers.add(new MessageMetricDTO.Header(e.getKey(), e.getName()));
                        metrics.put(e.getKey(), new MessageMetricDTO.Metric("-1", "人工评测指标缺失"));
                    });
                }

            }
            headers.add(new MessageMetricDTO.Header("intent", "意图识别结果"));
            metrics.put("intent", new MessageMetricDTO.Metric("-1", intent));
            // 人工测评指标
            HashMap<String, EvaluationSessionMessageRecordsDO.EvaluationResult> evaluationResult;
            if (recordsDO != null && !Strings.isNullOrEmpty(recordsDO.getEvaluationResult())) {
                HashMap<String, EvaluationSessionMessageRecordsDO.EvaluationResult> result = JsonUtils.parseObject(recordsDO.getEvaluationResult(), new TypeReference<>() {
                });
                evaluationResult = Objects.requireNonNullElseGet(result, HashMap::new);
            } else {
                evaluationResult = new HashMap<>();
            }

            List<MessageMetricDTO.Header> manualHeaders = new ArrayList<>();
            Map<String, MessageMetricDTO.Metric> manualMetrics = new HashMap<>();

            Arrays.stream(metricOrder).forEach(metric -> {
                var metricInfo = evaluationMetricInfo.get(metric);
                if (metricInfo != null) {
                    manualHeaders.add(new MessageMetricDTO.Header(metric, metricInfo.getName()));
                }
                EvaluationSessionMessageRecordsDO.EvaluationResult evaluationResult1 = evaluationResult.get(metric);
                if (evaluationResult1 != null) {
                    manualMetrics.put(metric, new MessageMetricDTO.Metric(evaluationResult1.getScore(), evaluationResult1.getReason()));
                }
            });

            return RemoteResponse.buildSuccess(MessageMetricDTO.builder().metrics(metrics).headers(headers).manualHeaders(manualHeaders).manualMetrics(manualMetrics).build());
        } catch (Exception e) {
            log.error("获取消息列表失败, sessionId: {}", e);
            return RemoteResponse.buildFail("获取消息列表失败: " + e.getMessage());
        }
    }


    @GetMapping("/session_metric")
    public RemoteResponse<SessionMetricDTO> getSessionMetricList(@RequestParam(value = "session_id") String sessionId) {
        try {
//            EvaluationSessionRecordsDO recordsDO = evaluationSessionRecordsRepository.findBySessionId(sessionId);
//            Map<String, EvaluationMetric> evaluationMetric = evaluationMetricsService.getEvaluationMetric();

            List<SessionEvaluationResultEntity> sessionEvaluationResults = multiDialogueEvaluation.getSessionEvaluationResults(sessionId);
            // 按照 modelScene 分组
            List<SessionEvaluationResultEntity> sessionEvaluationResult = sessionEvaluationResults.stream().
                    filter(e -> "medicalAgent".equals(e.getModelScene())).toList();

            List<SessionMetricDTO.Header> headers = new ArrayList<>();
            Map<String, SessionMetricDTO.Metric> metrics = new HashMap<>();
            sessionEvaluationResult.stream().forEach(e -> {
                headers.add(new SessionMetricDTO.Header(e.getItem(), e.getDescription()));
                metrics.put(e.getItem(), new SessionMetricDTO.Metric(e.getScore().toBigInteger().toString(), e.getReason()));
            });
            return RemoteResponse.buildSuccess(SessionMetricDTO.builder().metrics(metrics).headers(headers).build());
        } catch (Exception e) {
            log.error("获取消息列表失败, sessionId: {}", e);
            return RemoteResponse.buildFail("获取消息列表失败: " + e.getMessage());
        }
    }

    @PostMapping("/update_message_metric")
    public ResponseEntity<String> updateMessageMetric(@RequestBody MessageMetricDTO messageMetricDTO) {
        if (messageMetricDTO == null) {
            return ResponseEntity.status(500).body("Failed to update message metric");
        }
        Map<String, EvaluationSessionMessageRecordsDO.EvaluationResult> evaluationResults = new HashMap<>();
        messageMetricDTO.getManualMetrics().forEach((e, metric) -> {
            evaluationResults.put(e, new EvaluationSessionMessageRecordsDO.EvaluationResult(metric.getScore(), metric.getReason()));
        });
        String jsonString = JsonUtils.toJsonString(evaluationResults);
        EvaluationSessionMessageRecordsDO data = EvaluationSessionMessageRecordsDO.builder().
                messageId(messageMetricDTO.getMessageId()).
                sessionId(messageMetricDTO.getSessionId()).
                updateTime(new Date()).
                createTime(new Date()).
                evaluationResult(jsonString).build();
        int count = evaluationSessionMessageRecordsRepository.InsertOrUpdate(data);
        if (count > 1) {

        }
        return ResponseEntity.status(200).body("success");

    }

    /**
     * 根据测评ID和会话ID查询消息列表，按创建时间倒序排列
     *
     * @param sessionId 会话ID（可选）
     * @return 消息列表
     */
    @GetMapping("/message_list")
    public RemoteResponse<List<MessageListDTO>> getMessageList(
            @RequestParam(value = "session_id") String sessionId) {
        try {
            // 根据条件查询消息记录，按创建时间倒序排列
            List<ChatSessionMessageEntity> chatSessionMessageEntities = getChatSessionMessageEntities(sessionId);
            List<MessageListDTO> messageListDTOS = extractMessageV2(chatSessionMessageEntities);
            return RemoteResponse.buildSuccess(messageListDTOS);
        } catch (Exception e) {
            log.error("获取消息列表失败, sessionId: {}", sessionId, e);
            return RemoteResponse.buildFail("获取消息列表失败: " + e.getMessage());
        }
    }

    public String evaluateOneCase(String evaluationID, EvaluationCaseInfo data, long userID) {
        // 1. 开启一轮对话，session_id为空
        MessageAIRequest evaluateRequest = BuildEvaluateBaseRequest(data, userID);
        SimulatedDialogueChatDTO chatDTO = SimulatedDialogueChatDTO.builder().behaviour("query").response(data.getInitialQuestion()).build();
        for (int i = 0; i < 3; i++) {
            log.info("[测评]第{}轮对话开始", i + 1);
            long start = System.currentTimeMillis();
            if (i != 0) {
                // 1. 生成本轮问题
                chatDTO = genNextChat(evaluateRequest.getSessionId(), data, chatDTO.getTopics(), chatDTO.getDiscussed());
                if (chatDTO == null || "<end>".equals(chatDTO.getResponse()) || "end".equals(chatDTO.getBehaviour())) {
                    break;
                }
            }
            evaluateRequest.setUserQuery(chatDTO.getResponse());
            // 2. 发送对话消息
            CompletableFuture<Void> future = controller.getChatResponseInner(new SseEmitter(600000L), evaluateRequest);
            // 3. 阻塞当前线程，等待回答完成
            future.join();
            long duration = System.currentTimeMillis() - start;
            log.info("[测评]第{}轮对话结束,耗时{}秒", i + 1, duration / 1000.0);
        }

        EvaluationSessionRecordsDO sessionRecordsDO = EvaluationSessionRecordsDO.
                builder().
                evaluationId(evaluationID).
                sessionId(evaluateRequest.getSessionId()).
                caseId(String.valueOf(data.getNum())).
                createTime(new Date()).
                updateTime(new Date()).
                status(0).
                build();
        evaluationSessionRecordsRepository.insert(sessionRecordsDO);
        return evaluateRequest.getSessionId();
    }

    private MessageAIRequest BuildEvaluateBaseRequest(EvaluationCaseInfo data, long userID) {
        String bizType = "medical_consult";
        if ("dental".equals(data.getType())) {
            bizType = "mouth_consult";
        }
        return MessageAIRequest.
                builder().
                bizId("Evaluation").// 本次不填
                        bizType(bizType). // todo 医美口腔后续需要分开
                        role("user").
                basicParam(BasicParam.
                        builder().
                        userId(userID).
                        lng(data.getLocation()[0]).
                        lat(data.getLocation()[1]).
                        clientType("ios").
                        appVersion("12.39.420").
                        uuid("0000000000000C7B743812CAB4D02841E4CD5BF6A3807A175379214780849564").
                        cityId(10).// 上海
                                userCityId(10).
                        locationAuthorized(1).
                        ip("127.0.0.1").
                        build()).
                querySource("evaluation").
                role("client").
                platform(Platform.MT.getCode()).
                build();
    }

    private SimulatedDialogueChatDTO genNextChat(String sessionID, EvaluationCaseInfo data, List<Topic> topics, List<String> discussedTopics) {
        String evaluationPrompt = haimaAIUtils.getAIConfig("evaluation_agent").getSystemPrompt();
        if (evaluationPrompt.isBlank()) {
            return null;
        }
        List<ChatSessionMessageEntity> chatSessionMessageEntities = getChatSessionMessageEntities(sessionID);
        List<MessageListDTO> messageListDTOS = extractMessageV2(chatSessionMessageEntities);
        List<String> chatRecords = messageListDTOS.stream().map(MessageListDTO::getMessageContent).toList();

        evaluationPrompt = evaluationPrompt.
                replace("${chat_records}", String.join("\n", chatRecords)).
                replace("${user_info}", data.getUserInfo()).
                replace("${current_topics}", CollectionUtils.isEmpty(topics) ? "[]" : JsonUtils.toJsonString(topics)).
                replace("${discussed_topics}", CollectionUtils.isEmpty(discussedTopics) ? "[]" : JsonUtils.toJsonString(discussedTopics));

        String content = simulatedDialogueChatClient.prompt()
                .system(evaluationPrompt)
                .call().content();

        log.info("genNextChat prompt:{} {}", evaluationPrompt, content);
        return JsonExtractorUtil.extractValidJson(content, SimulatedDialogueChatDTO.class);
    }

    @NotNull
    private List<MessageListDTO> extractMessageV2(List<ChatSessionMessageEntity> bySessionIdAndStatus) {
        ZebraForceMasterHelper.forceMasterInLocalContext();
        // 历史会话查询
        List<MessageListDTO> chatRecords = bySessionIdAndStatus.stream().map(e -> {
            String role = e.getRole();
            String content = "";
            if (Objects.equals(role, "user")) {
                content = "<<我>>:" + e.getContent();
            } else {
                List<StreamEventDTO> streamEventDTOS = JsonUtils.parseArray(e.getContent(), StreamEventDTO.class);
                List<String> mainText = streamEventDTOS.
                        stream().
                        filter(dto -> dto.getData() != null && dto.getData().getEvent().equals("mainText")).
                        map(dto -> {
                            List<String> cardList = dto.getData().getCardsData().stream().map(
                                    cardData -> {
                                        StreamEventCardTypeEnum cardType = StreamEventCardTypeEnum.getByType(cardData.getType());
                                        if (cardType == StreamEventCardTypeEnum.RICH_TEXT_TITLE_CARD) {
                                            return null;
                                        }
                                        if (cardType == null) {
                                            return JsonUtils.toJsonString(cardData.getCardProps());
                                        }
                                        return cardType.getDesc() + "<%s>%s<%s/>".formatted(cardType, cardData.getKey(), cardType) + "详细信息:\n " + "```\n" + JsonUtils.toPrettyJsonString(cardData.getCardProps()) + "\n```";
                                    }
                            ).filter(Objects::nonNull).toList();
                            return dto.getData().getContent() + "\n --- \n<<以下是卡片详细信息>>\n" + Strings.join(cardList, "\n");
                        }).
                        toList();
                content = "<<医生>>:" + Strings.join(mainText, "\n");
            }
            return MessageListDTO.
                    builder().
                    sessionId(e.getSessionId()).
                    messageId(e.getMessageId()).
                    messageContent(content).
                    createTime(DateFormatUtils.format(e.getCreateTime(), "yyyy-MM-dd HH:mm:ss")).
                    build();
        }).toList();
        return chatRecords;
    }

    @NotNull
    private List<ChatSessionMessageEntity> getChatSessionMessageEntities(String sessionID) {
        List<ChatSessionMessageEntity> bySessionIdAndStatus = chatSessionMessageRepository.findBySessionIdAndStatus(sessionID, 100, 0);
        ZebraForceMasterHelper.clearLocalContext();
        bySessionIdAndStatus.sort((o1, o2) -> {
            // 相同创建时间user在前 assistant在后
            if (o1.getCreateTime().equals(o2.getCreateTime())) {
                int val1 = "user".equals(o1.getRole()) ? 0 : 1;
                int val2 = "user".equals(o2.getRole()) ? 0 : 1;
                return val1 - val2;
            }
            return o1.getCreateTime().compareTo(o2.getCreateTime());
        });
        return bySessionIdAndStatus;
    }

    @PostMapping("/get_evaluation_results")
    public RemoteResponse<EvaluationResultsResponse> getEvaluationResults(@RequestBody EvaluationResultsRequest request) {
        EvaluationResultsResponse results = multiDialogueEvaluation.getEvaluationResultsBySessionAndMessage(request.getSessionId(), request.getMessageId());
        return RemoteResponse.buildSuccess(results);
    }

}






