package com.sankuai.dzhealth.ai.service.starter.controller;

import com.sankuai.dzhealth.ai.service.api.RecommendQuestionService;
import com.sankuai.dzhealth.ai.service.api.RemoteResponse;
import com.sankuai.dzhealth.ai.service.dto.RecommendQuestionDTO;
import com.sankuai.dzhealth.ai.service.request.RecommendQuestionRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@RequestMapping("/recommend")
@RequiredArgsConstructor
public class RecommendController {

    @Autowired
    private RecommendQuestionService recommendQuestionService;

    @GetMapping("/question")
    public RemoteResponse<RecommendQuestionDTO> getQuestion(@RequestParam("product_name") String productName, @RequestParam("user_id") String userId, @RequestParam("category_id") String categoryId) {
        RemoteResponse<RecommendQuestionDTO> recommendQuestions = recommendQuestionService.getRecommendQuestions(RecommendQuestionRequest.builder().userId(userId).productName(productName).categoryId(categoryId).build());
        return recommendQuestions;
    }
}