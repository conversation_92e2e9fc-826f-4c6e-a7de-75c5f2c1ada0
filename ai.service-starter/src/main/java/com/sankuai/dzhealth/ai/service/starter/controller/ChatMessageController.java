package com.sankuai.dzhealth.ai.service.starter.controller;

import com.alibaba.fastjson.JSON;
import com.dianping.account.UserAccountService;
import com.dianping.account.dto.VirtualBindUserInfoDTO;
import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.dianping.rhino.Rhino;
import com.dianping.rhino.threadpool.DefaultThreadPoolProperties;
import com.dianping.rhino.threadpool.ThreadPool;
import com.google.common.collect.Maps;
import com.meituan.beauty.fundamental.light.plat.Platform;
import com.meituan.mdp.boot.starter.leaf.MdpLeafException;
import com.sankuai.dzhealth.ai.service.agent.application.service.ChatMessageProcessService;
import com.sankuai.dzhealth.ai.service.agent.domain.context.MessageContext;
import com.sankuai.dzhealth.ai.service.agent.domain.entity.ChatSessionEntity;
import com.sankuai.dzhealth.ai.service.agent.domain.entity.ChatSessionMessageEntity;
import com.sankuai.dzhealth.ai.service.agent.domain.model.MemoryModel;
import com.sankuai.dzhealth.ai.service.agent.domain.repository.chatSession.ChatSessionMessageRepository;
import com.sankuai.dzhealth.ai.service.agent.domain.repository.chatSession.ChatSessionRepository;
import com.sankuai.dzhealth.ai.service.agent.domain.service.EncyclopediaService;
import com.sankuai.dzhealth.ai.service.agent.domain.utils.SseUtils;
import com.sankuai.dzhealth.ai.service.agent.domain.utils.StreamEventBuildUtils;
import com.sankuai.dzhealth.ai.service.agent.enums.ContextExtraKey;
import com.sankuai.dzhealth.ai.service.agent.request.EncyclopediaRequest;
import com.sankuai.dzhealth.ai.service.agent.request.MessageAIRequest;
import com.sankuai.dzhealth.ai.service.application.ProductQaApplicationService;
import com.sankuai.dzhealth.ai.service.domain.utils.JsonUtils;
import com.sankuai.dzhealth.ai.service.domain.utils.context.RequestContext;
import com.sankuai.dzhealth.ai.service.domain.utils.context.RequestContextConstant;
import com.sankuai.dzhealth.ai.service.dto.ProductQaDTO;
import com.sankuai.dzhealth.ai.service.dto.QaItemDTO;
import com.sankuai.dzhealth.ai.service.dto.SseAwareException;
import com.sankuai.dzhealth.ai.service.dto.stream.StreamEventDTO;
import com.sankuai.dzhealth.ai.service.dto.stream.StreamEventDataDTO;
import com.sankuai.dzhealth.ai.service.enums.BizSceneEnum;
import com.sankuai.dzhealth.ai.service.enums.StreamEventErrorTypeEnum;
import com.sankuai.dzhealth.ai.service.enums.StreamEventTypeEnum;
import com.sankuai.dzhealth.ai.service.infrastructure.security.AuditRequest;
import com.sankuai.dzhealth.ai.service.infrastructure.security.AuditResult;
import com.sankuai.dzhealth.ai.service.infrastructure.security.PorscheAuditAcl;
import com.sankuai.dzhealth.ai.service.infrastructure.utils.UidUtils;
import com.sankuai.dzhealth.ai.service.request.ProductQaRequest;
import com.sankuai.wpt.user.thrift.token.UserIdMsg;
import com.sankuai.wpt.user.thrift.token.UserValidateTokenService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;
import org.springframework.web.util.WebUtils;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.Serializable;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicInteger;

import static com.sankuai.dzhealth.ai.service.infrastructure.utils.UidUtils.CHAT_MESSAGE_LEAF_KEY;
import static com.sankuai.dzhealth.ai.service.infrastructure.utils.UidUtils.CHAT_SESSION_LEAF_KEY;

/**
 * @author:chenwei
 * @time: 2025/7/6 10:44
 * @version: 0.0.1
 */

@Slf4j
@RestController
public class ChatMessageController {


    @Autowired
    private UserAccountService userAccountService;

    @Autowired
    private UserValidateTokenService.Iface mdpUserValidateTokenService;

    @Autowired
    private UidUtils uidUtils;

    @Autowired
    private ChatMessageProcessService chatMessageProcessService;

    @Autowired
    private ChatSessionRepository chatSessionRepository;

    @Autowired
    private EncyclopediaService encyclopediaService;


    @Autowired
    private ChatSessionMessageRepository chatSessionMessageRepository;

    @Autowired
    private PorscheAuditAcl porscheAuditAcl;


    @Autowired
    private ProductQaApplicationService productQaApplicationService;


    public static final ThreadPool SSE_CONNECTION_POOL = Rhino.newThreadPool("CHAT_MESSAGE_AGENT_POOL",
            DefaultThreadPoolProperties.Setter().withCoreSize(100).withMaxSize(200).withMaxQueueSize(1000));


    @PostMapping(value = "/agent/chat/message", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public SseEmitter getChatResponse(@RequestBody MessageAIRequest requestBody, HttpServletRequest httpServletRequest,
            HttpServletResponse httpServletResponse) throws TException {
        Transaction transaction = Cat.newTransaction("ChatMessageController", "getChatResponse");
        try {
            SseUtils.setSseHeader(httpServletResponse);
            MessageAIRequest request = requestBody;
//                JsonUtils.parseObject(requestBody, MessageAIRequest.class);
            authenticateUserAndSetUserId(request, httpServletRequest);
            // 创建SseEmitter实例，设置超时时间为10分钟
            SseEmitter sseEmitter = new SseEmitter(600000L);
            getChatResponseInner(sseEmitter, request);
            return sseEmitter;

        } catch (Exception e) {
            transaction.setStatus(e);
            throw e;
        } finally {
            transaction.complete();
        }
    }


    protected CompletableFuture<Void> getChatResponseInner(SseEmitter sseEmitter, MessageAIRequest request) {
        return  CompletableFuture.runAsync(() -> {
            try {
                doSendMessage(sseEmitter, request);
            } catch (Exception e) {
                log.error("doSendMessage error");
                throw new RuntimeException(e);
            }
        }, SSE_CONNECTION_POOL.getExecutor());
    }

    private void doSendMessage(SseEmitter sseEmitter, MessageAIRequest request) throws Exception {
        try {
            sseEmitter.send(StreamEventBuildUtils.buildOpenEvent());

            if (request.getBasicParam().getUserId() == null || request.getBasicParam().getUserId() <= 0) {
                throw new SseAwareException(StreamEventErrorTypeEnum.UN_LOGIN);
            }

            if (request.getPlatform() == null) {
                throw new SseAwareException(StreamEventErrorTypeEnum.PARAM_ERROR);
            }

            MessageContext context = new MessageContext();


            // 补充context内容
            prepareContext(context, request);

            RequestContext.init();
            RequestContext.setAttribute(RequestContextConstant.SSE_EMITTER, sseEmitter);

            // 保时洁接入
            audit(request.getUserQuery(), context, new AtomicInteger(0));

            chatMessageProcessService.sendChatMessage(context);


        } catch (SseAwareException e) {
            log.error("SseAwareException processing chat response, request={}, result={}", request, JSON.toJSONString(request), e);
            sseEmitter.send(StreamEventDTO.builder().type(StreamEventTypeEnum.ERROR.getType())
                    .data(StreamEventDataDTO.builder().event(e.getEvent()).content(e.getContent()).build()).build());

        } catch (MdpLeafException e) {
            log.error("Error processing leaf id generate, request={}, result={}", request, JSON.toJSONString(request), e);
            sseEmitter.send(StreamEventDTO.builder().type(StreamEventTypeEnum.ERROR.getType())
                    .data(StreamEventDataDTO.builder().event(StreamEventErrorTypeEnum.SERVER_ERROR.getType())
                            .content("消息/会话创建失败").build()).build());
        } catch (Exception e) {
            log.error("Error processing chat response, request={}, result={}", request, JSON.toJSONString(request), e);
            sseEmitter.send(StreamEventDTO.builder().type(StreamEventTypeEnum.ERROR.getType())
                    .data(StreamEventDataDTO.builder().event(StreamEventErrorTypeEnum.SERVER_ERROR.getType())
                            .content("系统繁忙，请稍后再试").build()).build());
        } finally {
            sseEmitter.send(StreamEventDTO.builder().type(StreamEventTypeEnum.CLOSE.getType()).build());
            sseEmitter.complete();
            RequestContext.cleanup();
        }
    }

    private boolean audit(String content, MessageContext messageContext, AtomicInteger auditIndex) {
        long bizId = NumberUtils.toLong(messageContext.getMsgId()) + auditIndex.getAndIncrement();
        AuditResult contentAuditResult = porscheAuditAcl.audit(AuditRequest.builder()
                .bizId(bizId)
                .dataSource(9)
                .transId(String.valueOf(bizId))
                .userId(messageContext.getUserId())
                .userIP("127.0.0.1")
                .userSource(messageContext.getPlatform())
                .assistantId(messageContext.getBizType())
                .textBody(Collections.singletonList(AuditRequest.TextBody.builder()
                        .name("AIGCText")
                        .value(content)
                        .desc("AIGCText")
                        .build()))
                .type(100220)
                .build());
        if (!Objects.equals(contentAuditResult.getAdvice(), AuditResult.Advice.PASSED.getCode())) {
            throw new SseAwareException(StreamEventErrorTypeEnum.AUDIT_ERROR);
        }
        return true;
    }


    private void prepareContext(MessageContext context, MessageAIRequest request) {

        Map<String, Serializable> extra = new HashMap<>();
        if (request.getBasicParam() != null) {
            context.setBasicParam(request.getBasicParam());
            context.setUserId(request.getBasicParam().getUserId());
        }

        context.setSessionId(request.getSessionId());
        if (StringUtils.isBlank(request.getSessionId())) {
            //sessionId为空 需要分配一个新的id 会话消息创建
            String sessionId = String.valueOf(uidUtils.getNextId(CHAT_SESSION_LEAF_KEY));

            chatSessionRepository.insertSession(sessionId,
                    request.getBasicParam().getUserId(), request.getBizType(), request.getPlatform(),
                    request.getUserQuery(), null);
            request.setSessionId(sessionId);// todo
            context.setSessionId(sessionId);
        } else {
            ChatSessionEntity bySessionId = chatSessionRepository.findBySessionId(request.getSessionId(), request.getBasicParam().getUserId(), request.getPlatform(), request.getBizType());
            if (bySessionId == null) {
                throw new SseAwareException(StreamEventErrorTypeEnum.NO_RIGHT);
            }
            //更新title
            if (StringUtils.isBlank(bySessionId.getTitle())) {
                bySessionId.setTitle(request.getUserQuery());
                chatSessionRepository.updateSingleSession(bySessionId, request.getSessionId());
            }

        }

        if (MapUtils.isNotEmpty(request.getExtraParams())) {
            extra.putAll(request.getExtraParams());
        }

        if (extra.containsKey(ContextExtraKey.PRODUCT_QA.getKey())) {
            String productQaId = (String) extra.get(ContextExtraKey.PRODUCT_QA.getKey());
            try {
                ProductQaRequest productQaRequest = ProductQaRequest.builder()
                        .idType(BizSceneEnum.MEDICAL_CONSULT.getBizScene().equals(request.getBizType()) ? 3 : 2)
                        .categoryId(BizSceneEnum.MEDICAL_CONSULT.getBizScene().equals(request.getBizType()) ? "850" : "506")
                        .platform(request.getPlatform())
                        .productId(productQaId).build();
                ProductQaDTO productQa = productQaApplicationService.getProductQa(productQaRequest);
                if (productQa != null && CollectionUtils.isNotEmpty(productQa.getQaItems())) {
                    QaItemDTO qaItemDTO = productQa.getQaItems().get(0);
                    extra.put(ContextExtraKey.PRODUCT_QA.getKey(), qaItemDTO.getAnswer());
                }
            } catch (Exception e) {
                log.error("productQaId={}", productQaId, e);
            }
        }



        // 业务id放extra中
        extra.put(ContextExtraKey.BIZ_ID.getKey(), request.getBizId());


        // 发送消息id
        String msgId = String.valueOf(uidUtils.getNextId(CHAT_MESSAGE_LEAF_KEY));
        context.setMsgId(msgId);
        context.setBizType(request.getBizType());
        context.setQuerySource(request.getQuerySource());
        context.setMsg(request.getUserQuery());
        context.setImgUrls(request.getImgUrls());
        extra.put(ContextExtraKey.IMG_URLS.getKey(), JsonUtils.toJsonString(request.getImgUrls()));
        context.setExtra(extra);
        context.setPlatform(request.getPlatform());

        // 记忆模块处理 只查当前会话的最近一条消息 把记忆先传进去
        List<ChatSessionMessageEntity> recentMessageEntityList = chatSessionMessageRepository.findBySessionIdAndStatus(context.getSessionId(), 1, 0);
        if (CollectionUtils.isNotEmpty(recentMessageEntityList)) {
            String memorySnapshot = recentMessageEntityList.get(0).getMemorySnapshot();
            if (StringUtils.isNotBlank(memorySnapshot)) {
                context.setMemoryModel(JsonUtils.parseObject(memorySnapshot, MemoryModel.class));
            }
        }


    }


    private void authenticateUserAndSetUserId(MessageAIRequest request, HttpServletRequest httpServletRequest) throws TException {
        String token = getToken(httpServletRequest);
        Long userId = null;
        if (request.getPlatform() == Platform.DP.getCode()) {
            VirtualBindUserInfoDTO virtualBindUserInfoDTO = userAccountService.loadUserByToken(token, StringUtils.EMPTY, Maps.newHashMap());
            if (virtualBindUserInfoDTO != null) {
                userId = virtualBindUserInfoDTO.getDpid();
            }
        } else {
            UserIdMsg userIdByToken = mdpUserValidateTokenService.getUserIdByToken(token);
            if (userIdByToken != null) {
                userId = userIdByToken.getUserId();
            }
        }
        if (userId != null && userId > 0) {
            request.getBasicParam().setUserId(userId);
        }
    }

    private String getToken(HttpServletRequest request) {
        String token = request.getHeader("token");
        if (StringUtils.isBlank(token)) {
            Cookie tokenCookie = WebUtils.getCookie(request, "token");
            if (tokenCookie != null) {
                token = tokenCookie.getValue();
            }
        }
        if (StringUtils.isBlank(token)) {
            token = request.getParameter("token");
        }
        return token;
    }


    @PostMapping(value = "/agent/chat/encyclopedia", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public SseEmitter getEncyclopedia(@RequestBody EncyclopediaRequest request, HttpServletRequest httpServletRequest,
                                      HttpServletResponse httpServletResponse) throws TException {
        log.info("getEncyclopedia_request={}", request);
        SseUtils.setSseHeader(httpServletResponse);

        String token = getToken(httpServletRequest);
        authenticationUserAndSetUserId(request, token);

        // 用户未登录或用户ID无效，抛出异常
        if (request.getBasicParam().getUserId() == null || request.getBasicParam().getUserId() <= 0) {
            throw new SseAwareException(StreamEventErrorTypeEnum.UN_LOGIN);
        }
        // 创建SseEmitter实例，设置超时时间为10分钟
        SseEmitter sseEmitter = new SseEmitter(10 * 60_000L);

        // 耗时操作异步，先与浏览器建立连接，才能流式输出大模型回复内容
        CompletableFuture.runAsync(() -> {
            try {
                try {
                    // 发送open事件
                    sseEmitter.send(StreamEventBuildUtils.buildOpenEvent());
                    // 调用大模型回答百科知识
                    encyclopediaService.queryEncyclopedia(sseEmitter, request.getSessionId(), request.getUserQuery());
                } catch (Exception e) {
                    log.error("Error processing chat response, request={}, result={}", request,
                            JSON.toJSONString(request), e);
                    sseEmitter.send(StreamEventDTO.builder().type(StreamEventTypeEnum.ERROR.getType())
                            .data(StreamEventDataDTO.builder().event(StreamEventErrorTypeEnum.SERVER_ERROR.getType())
                                    .content("系统繁忙，请稍后再试").build())
                            .build());
                } finally {
                    // 发送close事件
                    sseEmitter.send(StreamEventDTO.builder().type(StreamEventTypeEnum.CLOSE.getType()).build());
                    // 完成SseEmitter，释放资源
                    sseEmitter.complete();
                }
            } catch (Exception e) {
                log.error("getEncyclopedia error");
                throw new RuntimeException(e);
            }
        }, SSE_CONNECTION_POOL.getExecutor());

        return sseEmitter;
    }

    private void authenticationUserAndSetUserId(EncyclopediaRequest request, String token) throws TException {
        // 根据平台校验用户
        Long userId = null;
        if (request.getPlatform() == Platform.DP.getCode()) {
            VirtualBindUserInfoDTO virtualBindUserInfoDTO = userAccountService.loadUserByToken(token, StringUtils.EMPTY,
                    Maps.newHashMap());
            if (virtualBindUserInfoDTO != null) {
                userId = virtualBindUserInfoDTO.getDpid();
            }
        } else {
            UserIdMsg userIdByToken = mdpUserValidateTokenService.getUserIdByToken(token);
            if (userIdByToken != null) {
                userId = userIdByToken.getUserId();
            }
        }
        if (userId != null) {
            request.getBasicParam().setUserId(userId);
        }
    }


}
