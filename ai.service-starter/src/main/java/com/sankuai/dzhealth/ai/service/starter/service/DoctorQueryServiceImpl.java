package com.sankuai.dzhealth.ai.service.starter.service;

import com.meituan.mdp.boot.starter.thrift.annotation.MdpThriftServer;
import com.sankuai.dzhealth.ai.service.agent.api.DoctorQueryService;
import com.sankuai.dzhealth.ai.service.agent.infrastructure.proxy.doctorinfo.DoctorInfoAcl;
import com.sankuai.dzhealth.ai.service.agent.request.DoctorInfoRequest;
import com.sankuai.dzhealth.ai.service.api.RemoteResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;

/**
 * 医生信息AI友好的Thrift服务实现
 */
@MdpThriftServer
@Slf4j
public class DoctorQueryServiceImpl implements DoctorQueryService {

    @Resource
    private DoctorInfoAcl doctorInfoAcl;

    @Override
    public RemoteResponse<String> queryDoctorsForAI(DoctorInfoRequest doctorInfoRequest) {
        try {
            // 参数校验
            if (doctorInfoRequest == null) {
                log.warn("queryDoctorsForAI request is null");
                return RemoteResponse.buildIllegalArgument("请求参数不能为空");
            }

            if (doctorInfoRequest.getMergeDoctorIds() == null || doctorInfoRequest.getMergeDoctorIds().isEmpty()) {
                log.warn("queryDoctorsForAI mergeDoctorIds is empty");
                return RemoteResponse.buildIllegalArgument("医生ID列表不能为空");
            }

            if(doctorInfoRequest.getMergeDoctorIds().size() > 20) {
                log.warn("queryDoctorsForAI mergeDoctorIds size is too large");
                return RemoteResponse.buildIllegalArgument("医生ID列表不能超过20个");
            }

            log.info("queryDoctorsForAI start, doctorIds: {}", doctorInfoRequest.getMergeDoctorIds());

            // 调用ACL获取数据
            String doctorInfoJson = doctorInfoAcl.queryDoctorInfo(doctorInfoRequest);

            // 检查返回结果
            if (!StringUtils.hasText(doctorInfoJson) || "{}".equals(doctorInfoJson)) {
                log.warn("queryDoctorsForAI no data found, doctorIds: {}", doctorInfoRequest.getMergeDoctorIds());
                return RemoteResponse.buildSuccess("{}");
            }

            return RemoteResponse.buildSuccess(doctorInfoJson);
        } catch (Exception e) {
            log.error("queryDoctorsForAI error, request: {}", doctorInfoRequest, e);
            return RemoteResponse.buildFail("查询医生信息失败: " + e.getMessage());
        }
    }
}

