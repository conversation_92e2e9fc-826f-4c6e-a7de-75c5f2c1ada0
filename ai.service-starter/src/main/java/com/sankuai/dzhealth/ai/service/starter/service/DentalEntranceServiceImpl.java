package com.sankuai.dzhealth.ai.service.starter.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.dianping.haima.entity.haima.HaimaContent;
import com.meituan.mdp.boot.starter.thrift.annotation.MdpThriftServer;
import com.sankuai.dzhealth.ai.service.agent.api.DentalEntranceService;
import com.sankuai.dzhealth.ai.service.agent.dto.DentalEntranceResponse;
import com.sankuai.dzhealth.ai.service.agent.request.DentalEntranceRequest;
import com.sankuai.dzhealth.ai.service.api.RemoteResponse;
import com.sankuai.dzhealth.ai.service.infrastructure.acl.HaimaAcl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

import javax.annotation.Resource;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Optional;

@Slf4j
@MdpThriftServer
public class DentalEntranceServiceImpl implements DentalEntranceService {

    @Resource
    private HaimaAcl haimaAcl;

    private static final String HAIMA_SCENE_KEY = "medical_ai_resource_config";
    private static final String HAIMA_RESOURCE_KEY = "detal_channel_banner";

    @Override
    public RemoteResponse<DentalEntranceResponse> getDentalEntrance(DentalEntranceRequest request) {
        if (request == null || request.getPlatform() == null || request.getUserId() == null) {
            log.error("getDentalEntrance: invalid request: {}", JSON.toJSONString(request));
            return RemoteResponse.buildFail("参数错误");
        }
        log.info("getDentalEntrance: request={}", JSON.toJSONString(request));

        String question = getQuestion();
        String baseUrl = "imeituan://www.meituan.com/mrn?mrn_biz=gcbu&mrn_entry=aesthetic-ai-app&mrn_component=AestheticMedicineAI&categoryId=506&source=2&bizType=mouth_consult";

        String jumpLinkWithoutText = String.format("%s&platform=%d", baseUrl, request.getPlatform());
        String jumpLinkWithText = String.format("%s&platform=%d&question=%s", baseUrl, request.getPlatform(), URLEncoder.encode(question, StandardCharsets.UTF_8));

        DentalEntranceResponse dentalEntranceResponse = new DentalEntranceResponse(question, jumpLinkWithText, jumpLinkWithoutText);

        log.info("getDentalEntrance: response={}，request={}", JSON.toJSONString(dentalEntranceResponse),JSON.toJSONString(request));
        return RemoteResponse.buildSuccess(dentalEntranceResponse);
    }

    private String getQuestion() {
        try {
            // 调用 HaimaAcl 的 getContent 方法
            List<HaimaContent> contents = haimaAcl.getContent(HAIMA_SCENE_KEY, null);
            if (CollectionUtils.isEmpty(contents)) {
                log.info("getContent: no content found for sceneKey={}", HAIMA_SCENE_KEY);
                return "帮你找种牙方案";
            }
            Optional<List<String>> question = contents.stream()
                    .filter(content -> HAIMA_RESOURCE_KEY.equals(content.getContentString("resource_key")))
                    .map(content -> JSON.parseObject(content.getContentString("resource_config"), new TypeReference<List<String>>() {}))
                    .findFirst();

            return question.orElse(List.of("帮你找种牙方案")).get(0);
        } catch (Exception e) {
            log.error("getContent error: sceneKey={}", DentalEntranceServiceImpl.HAIMA_SCENE_KEY, e);
            return "帮你找种牙方案";
        }
    }
}
