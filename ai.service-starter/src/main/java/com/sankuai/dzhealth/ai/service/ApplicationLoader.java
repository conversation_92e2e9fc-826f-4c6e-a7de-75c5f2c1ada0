package com.sankuai.dzhealth.ai.service;

import com.meituan.mdp.ai.model.friday.autoconfigure.*;
import com.meituan.mdp.boot.starter.MdpContextUtils;
import org.springframework.ai.model.chat.client.autoconfigure.ChatClientAutoConfiguration;
import org.springframework.ai.model.deepseek.autoconfigure.DeepSeekChatAutoConfiguration;
import org.springframework.ai.model.openai.autoconfigure.*;
import org.springframework.ai.vectorstore.elasticsearch.autoconfigure.ElasticsearchVectorStoreAutoConfiguration;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.scheduling.annotation.EnableScheduling;

import java.util.ArrayList;
import java.util.List;

@EnableScheduling
@SpringBootApplication(exclude = {
        DataSourceAutoConfiguration.class,
        OpenAiChatAutoConfiguration.class,
        ChatClientAutoConfiguration.class,
        OpenAiAudioSpeechAutoConfiguration.class,
        OpenAiAudioTranscriptionAutoConfiguration.class,
        OpenAiEmbeddingAutoConfiguration.class,
        OpenAiImageAutoConfiguration.class,
        OpenAiModerationAutoConfiguration.class,
        DeepSeekChatAutoConfiguration.class,
        FridayChatAutoConfiguration.class,
        FridayAudioSpeechAutoConfiguration.class,
        FridayAudioTranscriptionAutoConfiguration.class,
        FridayModerationAutoConfiguration.class,
        FridayEmbeddingAutoConfiguration.class,
        FridayImageAutoConfiguration.class,
        ElasticsearchVectorStoreAutoConfiguration.class},
                       scanBasePackages = {
                               "com.dianping.haima",
                               "com.dianping.appkit",
                               "com.sankuai.dzhealth.ai.service"})
public class ApplicationLoader  {

    public static void main(String[] args) {
        SpringApplication application = new SpringApplication(ApplicationLoader.class);
        application.setAdditionalProfiles(MdpContextUtils.getHostEnvStr());
        application.run(args);
    }

    @Bean
    public FilterRegistrationBean<CORSFilter> getCORSFilter(CORSFilter corsFilter) {
        FilterRegistrationBean<CORSFilter> filterRegistrationBean = new FilterRegistrationBean<>();
        filterRegistrationBean.setFilter(corsFilter);
        List<String> urlPatterns = new ArrayList<>();
        urlPatterns.add("/*");
        filterRegistrationBean.setUrlPatterns(urlPatterns);
        filterRegistrationBean.setOrder(2);
        return filterRegistrationBean;
    }
}


