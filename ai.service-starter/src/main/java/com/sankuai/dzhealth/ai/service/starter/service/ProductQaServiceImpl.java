package com.sankuai.dzhealth.ai.service.starter.service;

import com.meituan.mdp.boot.starter.thrift.annotation.MdpThriftServer;
import com.sankuai.dzhealth.ai.service.api.ProductQaService;
import com.sankuai.dzhealth.ai.service.api.RemoteResponse;
import com.sankuai.dzhealth.ai.service.application.ProductQaApplicationService;
import com.sankuai.dzhealth.ai.service.dto.ProductQaDTO;
import com.sankuai.dzhealth.ai.service.request.ProductQaRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

// 已移除mock代码对应的DTO/集合类引用

/**
 * 商品详情页AI问答服务实现
 * 提供商品相关的AI生成问答内容
 *
 * @author: AI Service Team
 * @version: 1.0.0
 */
@Slf4j
@MdpThriftServer
@RequiredArgsConstructor
public class ProductQaServiceImpl implements ProductQaService {

    private final ProductQaApplicationService productQaApplicationService;

    @Override
    public RemoteResponse<ProductQaDTO> getProductQaList(ProductQaRequest request) {
        log.info("获取商品问答列表请求: {}", request);

        try {
            // 1. 参数校验
            validateRequest(request);

            // 2. 业务逻辑处理交由应用层
            ProductQaDTO response = productQaApplicationService.getProductQa(request);

            log.info("获取商品问答列表成功, productId: {}, questionCount: {}",
                    request.getProductId(),
                    response.getQaItems() != null ? response.getQaItems().size() : 0);

            return RemoteResponse.buildSuccess(response);

        } catch (IllegalArgumentException e) {
            log.error("参数错误: {}", e.getMessage(), e);
            return RemoteResponse.buildIllegalArgument(e.getMessage());
        } catch (Exception e) {
            log.error("获取商品问答列表失败, productId: {}, error: {}",
                    request != null ? request.getProductId() : "null", e.getMessage(), e);
            return RemoteResponse.buildFail("系统繁忙，请稍后再试");
        }
    }

    /**
     * 参数校验
     */
    private void validateRequest(ProductQaRequest request) {
        if (request == null) {
            throw new IllegalArgumentException("请求参数不能为空");
        }

        if (request.getIdType() == null) {
            throw new IllegalArgumentException("入参Id类型不能为空");
        }

        if (StringUtils.isBlank(request.getProductId())) {
            throw new IllegalArgumentException("商品ID不能为空");
        }

        if (request.getPlatform() != null && request.getPlatform() != 1 && request.getPlatform() != 2) {
            throw new IllegalArgumentException("平台标识只支持1(点评)或2(美团)");
        }
    }
}

