package com.sankuai.dzhealth.ai.service.starter.gateway;

import com.meituan.mdp.boot.starter.thrift.annotation.MdpThriftServer;
import com.sankuai.dzhealth.ai.service.agent.application.service.DecisionFlowApplicationService;
import com.sankuai.dzhealth.ai.service.api.DecisionFlowService;
import com.sankuai.dzhealth.ai.service.api.RemoteResponse;
import com.sankuai.dzhealth.ai.service.dto.decision.*;
import com.sankuai.dzhealth.ai.service.request.*;
import lombok.RequiredArgsConstructor;

import java.util.List;

/**
 * 决策流 Thrift 服务实现 - starter 层
 * 职责：接口协议适配、基础参数校验、请求转发、响应结果封装、异常处理
 */
@MdpThriftServer
@RequiredArgsConstructor
public class DecisionFlowServiceImpl implements DecisionFlowService {

    // 注入 application 层的服务，而不是直接注入 domain 层服务
    private final DecisionFlowApplicationService applicationService;

    @Override
    public RemoteResponse<List<DecisionNodeDTO>> decideNext(DecideNextRequest request) {
        // starter 层只负责基础参数校验和请求转发
        if (request == null || request.getNodeId() == null) {
            return RemoteResponse.buildFail("nodeId is required");
        }
        return applicationService.decideNext(request);
    }

    @Override
    public RemoteResponse<List<ResourceRecommendationDTO>> listTopResources(ListTopResourcesRequest request) {
        if (request == null || request.getNodeId() == null) {
            return RemoteResponse.buildFail("nodeId is required");
        }
        return applicationService.listTopResources(request);
    }

    @Override
    public RemoteResponse<ImportResultDTO> importFlow(DecisionFlowImportRequest request) {
        if (request == null) {
            return RemoteResponse.buildFail("request is required");
        }
        return applicationService.importFlow(request);
    }

    @Override
    public RemoteResponse<Boolean> deleteFlow(BizSceneRequest request) {
        if (request == null || request.getBizScene() == null) {
            return RemoteResponse.buildFail("bizScene is required");
        }
        return applicationService.deleteFlow(request);
    }

    @Override
    public RemoteResponse<List<DecisionFlowDTO>> search(DecisionTreeSearchRequest request) {
        if (request == null || request.getBizScene() == null) {
            return RemoteResponse.buildFail("bizScene is required");
        }
        return applicationService.search(request);
    }

    @Override
    public RemoteResponse<DecisionFlowSummaryDTO> queryFlowSummary(QueryFlowSummaryRequest request) {
        if (request == null || request.getBizScene() == null) {
            return RemoteResponse.buildFail("bizScene is required");
        }
        return applicationService.queryFlowSummary(request);
    }

    @Override
    public RemoteResponse<DecisionFlowDTO> queryDecisionTree(DecisionTreeQueryRequest request) {
        if (request == null) {
            return RemoteResponse.buildFail("request is required");
        }
        return applicationService.queryDecisionTree(request);
    }

    @Override
    public RemoteResponse<List<BizSceneDTO>> listBizScenes() {
        return applicationService.listBizScenes();
    }

    @Override
    public RemoteResponse<Boolean> rollbackGray(BizSceneRequest request) {
        if (request == null || request.getBizScene() == null) {
            return RemoteResponse.buildFail("bizScene is required");
        }
        return applicationService.rollbackGray(request);
    }

    @Override
    public RemoteResponse<Boolean> grayToOnline(BizSceneRequest request) {
        if (request == null || request.getBizScene() == null) {
            return RemoteResponse.buildFail("bizScene is required");
        }
        return applicationService.grayToOnline(request);
    }

} 