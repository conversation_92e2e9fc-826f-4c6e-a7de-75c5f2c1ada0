package com.sankuai.dzhealth.ai.service.starter.service;

import com.meituan.beauty.fundamental.light.plat.Platform;
import com.meituan.mdp.boot.starter.thrift.annotation.MdpThriftServer;
import com.sankuai.dzhealth.ai.service.agent.api.SessionManageService;
import com.sankuai.dzhealth.ai.service.agent.application.service.SessionManageApplicationService;
import com.sankuai.dzhealth.ai.service.agent.dto.ChatMessageResponse;
import com.sankuai.dzhealth.ai.service.agent.dto.ChatSessionResponse;
import com.sankuai.dzhealth.ai.service.agent.dto.NavigateDTOResponse;
import com.sankuai.dzhealth.ai.service.agent.enums.SessionSceneEnum;
import com.sankuai.dzhealth.ai.service.agent.request.SessionManageRequest;
import com.sankuai.dzhealth.ai.service.api.RemoteResponse;
import com.sankuai.dzhealth.ai.service.enums.BizSceneEnum;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @author:chenwei
 * @time: 2025/7/12 13:43
 * @version: 0.0.1
 */

@MdpThriftServer
public class SessionManageServiceImpl implements SessionManageService {

    @Autowired
    private SessionManageApplicationService sessionManageApplicationService;


    @Override
    public RemoteResponse<ChatMessageResponse> processMessages(SessionManageRequest request) {

        validAndSetBasicRequest(request);

        return sessionManageApplicationService.processMessages(request);
    }

    @Override
    public RemoteResponse<ChatSessionResponse> operateSessions(SessionManageRequest request) {
        validAndSetBasicRequest(request);

        // 会话删除操作校验
        if (request.getScene() == SessionSceneEnum.DELETE_SESSION.getCode() && StringUtils.isBlank(request.getSessionId())
                && Boolean.FALSE.equals(request.getDeleteTotalSession())) {
            throw new IllegalArgumentException("会话删除操作必须指定sessionId或删除全部会话");
        }

        return sessionManageApplicationService.operateSessions(request);

    }

    @Override
    public RemoteResponse<NavigateDTOResponse> queryNavigate(String bizType) {
        return sessionManageApplicationService.queryNavigate(bizType);
    }

    private void validAndSetBasicRequest(SessionManageRequest request) {
        if (request == null) {
            throw new IllegalArgumentException("不合法的request");
        }
        if (request.getUserId() == null || request.getUserId() < 0) {
            throw new IllegalArgumentException("未登录");
        }
        if (Platform.get(request.getPlatform()) == null) {
            throw new IllegalArgumentException("不合法的platform");
        }
        if (BizSceneEnum.getByBizType(request.getBizType()) == null) {
            throw new IllegalArgumentException("不合法的bizType");
        }

        if (request.getScene() == null || request.getScene() < 0) {
            throw new IllegalArgumentException("<UNK>scene");
        }

        if (request.getPageNo() == null) {
            request.setPageNo(1);
        }
        if (request.getPageSize() == null || request.getPageSize() < 0 || request.getPageSize() > 20) {
            request.setPageSize(20);
        }
    }


}
