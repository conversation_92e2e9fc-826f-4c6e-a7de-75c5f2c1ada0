package com.sankuai.dzhealth.ai.service.starter.controller;

import com.alibaba.fastjson.JSON;
import com.dianping.lion.client.util.CollectionUtils;
import com.sankuai.dzhealth.ai.service.agent.application.service.DecisionFlowApplicationService;
import com.sankuai.dzhealth.ai.service.agent.domain.model.fine_tuning.GraphData;
import com.sankuai.dzhealth.ai.service.agent.domain.model.fine_tuning.GraphNode;
import com.sankuai.dzhealth.ai.service.dto.decision.DecisionFlowDTO;
import com.sankuai.dzhealth.ai.service.request.DecisionTreeSearchRequest;
import com.sankuai.dzhealth.ai.service.starter.util.GraphDataLoader;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;


@Slf4j
@RestController
@RequestMapping("/fine-tune")
@RequiredArgsConstructor
public class FineTuningController {
    GraphDataLoader graphDataLoader = new GraphDataLoader();

    @Autowired
    private DecisionFlowApplicationService applicationService;

    @GetMapping("/run")
    public ResponseEntity<byte[]> test(@RequestParam(value = "evaluation_name") String evaluationName) {
        try {
            // 从classpath根目录读取graph-data.json文件
            GraphData graphData = graphDataLoader.loadFromClasspath("graph-data.json");

            log.info("成功读取并反序列化graph-data.json文件，节点数量: {}",
                    graphData.getNodes() != null ? graphData.getNodes().size() : 0);
            var nodeMap = new HashMap<String, GraphNode>();
            graphData.getNodes().forEach(node -> {
                if (!"decision".equals(node.getNodeType())) {
                    return;
                }
                nodeMap.put(node.getId(), node);
            });
            nodeMap.forEach(
                    (k, v) -> {
                        if (!"decision".equals(v.getNodeType())) {
                            return;
                        }
                        GraphNode graphNode = nodeMap.get(v.getId());
                        List<GraphNode> childrenNode = new ArrayList<>();
                        v.getChildren().forEach(e -> {
                            GraphNode child = nodeMap.get(e);
                            if (child != null) {
                                childrenNode.add(child);
                            }
                        });
                        graphNode.setChildrenNode(childrenNode);
                        v.getParents().forEach(parentID -> {
                            GraphNode parent = nodeMap.get(parentID);
                            graphNode.setParentsNode(parent);
                        });
                    }
            );

            // 修正根节点判断逻辑：基于parents列表是否为空来判断
            var rootList = new ArrayList<GraphNode>();
            graphData.getNodes().forEach(node -> {
                if (!"decision".equals(node.getNodeType())) {
                    return;
                }
                if (CollectionUtils.isEmpty(node.getParents())) {
                    rootList.add(node);
                }
            });

            // DFS遍历根节点列表
            System.out.println("开始DFS遍历，根节点数量: " + rootList.size());
            for (GraphNode root : rootList) {
                dfsTraversal(root, new HashSet<>(), 0);
            }


        } catch (Exception e) {
            log.error("读取graph-data.json文件失败", e);
        }
        return null;
    }

    /**
     * DFS递归遍历图节点
     *
     * @param node    当前节点
     * @param visited 已访问节点集合，防止环路
     * @param depth   当前深度，用于缩进显示
     */
    private void dfsTraversal(GraphNode node, Set<String> visited, int depth) {
        if (node == null || visited.contains(node.getId())) {
            return;
        }

        // 标记当前节点为已访问
        visited.add(node.getId());
        // todo 构造case
        DecisionTreeSearchRequest request = DecisionTreeSearchRequest.builder().bizScene("医美决策树").topK(3).build();
        List<DecisionFlowDTO> decisionFlowDTOs = applicationService.getDecisionByID(request, node.getId());

        // todo
        String json = JSON.toJSONString(decisionFlowDTOs);
        System.out.println(json);




        // 打印当前节点信息（带缩进显示层级）
        String indent = "  ".repeat(depth);
        // 递归遍历子节点
        if (CollectionUtils.isNotEmpty(node.getChildrenNode())) {
            for (GraphNode child : node.getChildrenNode()) {
                dfsTraversal(child, visited, depth + 1);
            }
        }

        // 如果没有构建childrenNode关系，则通过children ID列表遍历
        if (CollectionUtils.isEmpty(node.getChildrenNode()) && CollectionUtils.isNotEmpty(node.getChildren())) {
            System.out.println(indent + "  子节点ID列表: " + node.getChildren());
        }
    }
}

