package com.sankuai.dzhealth.ai.service.starter.util;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.sankuai.dzhealth.ai.service.agent.domain.model.fine_tuning.GraphData;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.io.InputStream;

/**
 * 图数据加载器
 */
@Slf4j
@Component
public class GraphDataLoader {

    private final ObjectMapper objectMapper;

    public GraphDataLoader() {
        this.objectMapper = new ObjectMapper();
    }

    /**
     * 从classpath加载graph-data.json文件并反序列化为GraphData对象
     *
     * @param filePath 文件路径，相对于classpath
     * @return GraphData对象
     * @throws IOException 读取或解析文件时发生异常
     */
    public GraphData loadFromClasspath(String filePath) throws IOException {
        log.info("开始加载图数据文件: {}", filePath);

        ClassPathResource resource = new ClassPathResource(filePath);
        try (InputStream inputStream = resource.getInputStream()) {
            GraphData graphData = objectMapper.readValue(inputStream, GraphData.class);
            log.info("成功加载图数据，节点数量: {}", graphData.getNodes() != null ? graphData.getNodes().size() : 0);
            return graphData;
        }
    }

}

