package com.sankuai.dzhealth.ai.service.starter.service;

import com.meituan.mdp.boot.starter.thrift.annotation.MdpThriftServer;
import com.sankuai.dzhealth.ai.service.agent.api.ListingFacadeService;
import com.sankuai.dzhealth.ai.service.agent.infrastructure.proxy.shop.ListingFacadeProxy;
import com.sankuai.dzhealth.ai.service.agent.request.FillInfoRequest;
import com.sankuai.dzhealth.ai.service.api.RemoteResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;

/**
 *
 */
@MdpThriftServer
@Slf4j
public class ListingFacadeServiceImpl implements ListingFacadeService {

    @Autowired
    private ListingFacadeProxy listingFacadeProxy;

    @Override
    public RemoteResponse<String> queryListingForAI(FillInfoRequest fillInfoRequest) {
        log.info("queryListingForAI start, request: {}", fillInfoRequest);
        
        try {
            // 参数校验
            if (fillInfoRequest == null) {
                log.warn("queryListingForAI fillInfoRequest is null");
                return RemoteResponse.buildFail("请求参数不能为空");
            }

            // 调用代理层获取数据
            String result = listingFacadeProxy.fillInfo(fillInfoRequest);

            // 检查结果
            if (!StringUtils.hasText(result) || "{}".equals(result)) {
                log.info("queryListingForAI no data found, request: {}", fillInfoRequest);
                return RemoteResponse.buildSuccess("{}");
            }

            log.info("queryListingForAI success, request: {}, result length: {}",
                fillInfoRequest, result.length());
            return RemoteResponse.buildSuccess(result);

        } catch (Exception e) {
            log.error("queryListingForAI error, request: {}", fillInfoRequest, e);
            return RemoteResponse.buildFail("查询商户商品信息失败: " + e.getMessage());
        }
    }
}
