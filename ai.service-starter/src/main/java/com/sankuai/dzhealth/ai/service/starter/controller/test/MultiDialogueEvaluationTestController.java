package com.sankuai.dzhealth.ai.service.starter.controller.test;

import com.sankuai.dzhealth.ai.service.agent.infrastructure.model.MultiEvaluationRequest;
import com.sankuai.dzhealth.ai.service.agent.infrastructure.model.MultiEvaluationResponse;
import com.sankuai.dzhealth.ai.service.domain.evaluation.MultiDialogueEvaluation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.document.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * MultiDialogueEvaluation测试控制器
 * 用于测试MultiDialogueEvaluation#executeMultiEvaluation方法
 */
@RestController
@RequestMapping("/test/multi-dialogue-evaluation")
@Slf4j
public class MultiDialogueEvaluationTestController {

    @Autowired
    private MultiDialogueEvaluation multiDialogueEvaluation;

    /**
     * 测试executeMultiEvaluation方法 - 使用默认参数
     * @return 查询结果
     */
    @GetMapping("/execute-with-default-params")
    public MultiEvaluationResponse testExecuteMultiEvaluationWithDefaultParams() {
        log.info("开始测试MultiDialogueEvaluation#executeMultiEvaluation方法，使用默认参数");

        try {
            // 构建默认的RAG信息
            List<Document> ragInfo = new ArrayList<>();
            Map<String, Object> metadata1 = new HashMap<>();
            metadata1.put("source", "医疗知识库");
            metadata1.put("title", "高血压诊疗指南");
            Document doc1 = new Document("高血压是一种常见的心血管疾病，正常血压范围为收缩压90-140mmHg，舒张压60-90mmHg。", metadata1);
            ragInfo.add(doc1);

            Map<String, Object> metadata2 = new HashMap<>();
            metadata2.put("source", "药物说明书");
            metadata2.put("title", "降压药物使用指南");
            Document doc2 = new Document("常用降压药物包括ACE抑制剂、ARB类药物、钙通道阻滞剂等，需要根据患者具体情况选择。", metadata2);
            ragInfo.add(doc2);

            // 构建默认的对话消息
            MultiEvaluationRequest.Message dialog = MultiEvaluationRequest.Message.builder()
                    .userQuery("我最近血压有点高，应该怎么办？")
                    .assistantAnswer("根据您的情况，建议您首先进行生活方式调整，包括低盐饮食、适量运动、控制体重。如果血压持续偏高，建议及时就医，医生会根据您的具体情况制定个性化的治疗方案，可能包括药物治疗。")
                    .ragInfo(ragInfo)
                    .build();

            // 构建默认的评测请求
            MultiEvaluationRequest request = MultiEvaluationRequest.builder()
                    .bizScene("dzhealth_ai_evaluation")
                    // .sessionId("test_session_" + System.currentTimeMillis())
                    .sessionId("test_session_1752459264116")
                    .messageId("test_message_" + System.currentTimeMillis())
                    .modelScene("chat")
                    .dialog(dialog)
                    .context("用户咨询高血压相关问题，这是本次会话的第一轮对话。用户表现出对血压问题的担忧。")
                    .source(1) // 模拟用户
                    .build();

            // 调用方法
            MultiEvaluationResponse result = multiDialogueEvaluation.executeSingleDialogueEvaluation(request);

            log.info("测试完成，返回结果: {}", result);
            return result;
        } catch (Exception e) {
            log.error("测试过程中发生异常", e);
            throw e;
        }
    }

    /**
     * 测试executeMultiEvaluation方法 - POST方式，支持自定义参数
     * @param request 请求参数
     * @return 查询结果
     */
    @PostMapping("/execute-with-custom-params")
    public MultiEvaluationResponse testExecuteMultiEvaluationWithCustomParams(@RequestBody MultiEvaluationRequest request) {
        log.info("开始测试MultiDialogueEvaluation#executeMultiEvaluation方法，请求参数: {}", request);

        try {
            // 调用方法
            MultiEvaluationResponse result = multiDialogueEvaluation.executeSingleDialogueEvaluation(request);

            log.info("测试完成，返回结果: {}", result);
            return result;
        } catch (Exception e) {
            log.error("测试过程中发生异常", e);
            throw e;
        }
    }
}

