package com.sankuai.dzhealth.ai.service.starter.controller;

import com.dianping.cat.Cat;
import com.sankuai.dzhealth.ai.service.api.RemoteResponse;
import com.sankuai.dzhealth.ai.service.domain.contenttransfer.service.MarkdownConvertService;
import com.sankuai.dzhealth.ai.service.request.ContentTransferRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.web.bind.annotation.*;

/**
 * Markdown内容转换控制器
 * 提供内容转换为Markdown格式的HTTP接口
 */
@Slf4j
@RestController
@RequestMapping("/content")
@RequiredArgsConstructor
public class MarkdownConvertController {

    private final MarkdownConvertService markdownConvertService;

    /**
     * 将内容转换为Markdown格式
     *
     * @param request 内容转换请求
     * @return 转换后的Markdown内容
     */
    @PostMapping("/markdown-generator")
    public RemoteResponse<String> convertToMarkdown(@RequestBody ContentTransferRequest request) {
        Cat.logEvent("convertToMarkdown", "start");
        log.info("MarkdownConvertController.convertToMarkdown request={}", request.getOriginTextContent());

        try {
            // 参数校验
            if (request == null || StringUtils.isBlank(request.getOriginTextContent())) {
                return RemoteResponse.buildIllegalArgument("原始内容不能为空");
            }

            // 调用服务进行转换
            String markdownContent = markdownConvertService.convertToMarkdown(request.getOriginTextContent());

            log.info("MarkdownConvertController.convertToMarkdown success, originContentLength={}, markdownContentLength={}",
                    request.getOriginTextContent().length(),
                    markdownContent != null ? markdownContent.length() : 0);

            return RemoteResponse.buildSuccess(markdownContent);

        } catch (IllegalArgumentException e) {
            log.warn("MarkdownConvertController.convertToMarkdown illegal argument: {}", e.getMessage());
            Cat.logEvent("convertToMarkdown", "illegal_argument");
            return RemoteResponse.buildIllegalArgument(e.getMessage());

        } catch (Exception e) {
            log.error("MarkdownConvertController.convertToMarkdown error", e);
            Cat.logEvent("convertToMarkdown", "error");
            return RemoteResponse.buildFail("内容转换失败：" + e.getMessage());
        }
    }

    /**
     * 简单的GET接口，用于测试
     *
     * @param content 要转换的内容（通过URL参数传递）
     * @return 转换后的Markdown内容
     */
    @GetMapping("/markdown")
    public RemoteResponse<String> convertToMarkdownByGet(@RequestParam("content") String content) {
        Cat.logEvent("convertToMarkdownByGet", "start");
        log.info("MarkdownConvertController.convertToMarkdownByGet content length={}",
                content != null ? content.length() : 0);

        ContentTransferRequest request = new ContentTransferRequest(content);
        return convertToMarkdown(request);
    }
}