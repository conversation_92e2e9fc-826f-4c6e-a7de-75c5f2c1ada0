package com.sankuai.dzhealth.ai.service.starter.controller.test;

import com.sankuai.dzhealth.ai.service.agent.infrastructure.proxy.skinreport.BeautyNarcissusSkinReportAcl;
import com.sankuai.dzhealth.ai.service.agent.infrastructure.model.SkinReportResponse;
import com.sankuai.dzhealth.ai.service.agent.infrastructure.model.SkinReportTotalDegreeNameQuery;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * BeautyNarcissusSkinReportAcl测试控制器
 * 用于测试BeautyNarcissusSkinReportAcl#queryTotalDegreeName方法
 */
@RestController
@RequestMapping("/test/beauty-narcissus-skin-report")
@Slf4j
public class BeautyNarcissusSkinReportTestController {

    @Autowired
    private BeautyNarcissusSkinReportAcl beautyNarcissusSkinReportAcl;

    /**
     * 测试queryTotalDegreeName方法 - POST方式，支持自定义参数
     * @param query 请求参数
     * @return 查询结果
     */
    @PostMapping("/query-total-degree-name")
    public List<String> testQueryTotalDegreeNameWithCustomParams(@RequestBody SkinReportTotalDegreeNameQuery query) {
        log.info("开始测试BeautyNarcissusSkinReportAcl#queryTotalDegreeName方法，请求参数: {}", query);
        
        try {
            // 调用方法
            List<String> result = beautyNarcissusSkinReportAcl.queryTotalDegreeName(query);
            
            log.info("测试完成，返回结果: {}", result);
            return result;
        } catch (Exception e) {
            log.error("测试过程中发生异常", e);
            throw e;
        }
    }

    /**
     * 测试queryTotalDegreeNameWithJson方法 - POST方式，支持自定义参数
     * @param query 请求参数
     * @return JSON格式的皮肤报告响应
     */
    @PostMapping("/query-total-degree-name-json")
    public SkinReportResponse testQueryTotalDegreeNameWithJsonCustomParams(@RequestBody SkinReportTotalDegreeNameQuery query) {
        log.info("开始测试BeautyNarcissusSkinReportAcl#queryTotalDegreeNameWithJson方法，请求参数: {}", query);

        try {
            // 调用方法
            SkinReportResponse result = beautyNarcissusSkinReportAcl.queryTotalDegreeNameWithJson(query);

            log.info("测试完成，返回结果: {}", result);
            return result;
        } catch (Exception e) {
            log.error("测试过程中发生异常", e);
            throw e;
        }
    }


}

