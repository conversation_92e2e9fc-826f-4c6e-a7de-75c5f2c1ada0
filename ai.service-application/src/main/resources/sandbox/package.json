{"name": "starter-15", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "rm -rf ../static out && next build && mv out ../static && mv ../static/index.html ../static/result.html", "start": "next start", "lint": "next lint", "format": "prettier --write ."}, "dependencies": {"next": "15.3.5", "react": "^19.0.0", "react-dom": "^19.0.0", "three": "^0.179.1"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@types/three": "^0.177.0", "eslint": "^8", "eslint-config-next": "15.0.3", "postcss": "^8", "prettier": "^3.3.3", "tailwindcss": "^3.4.1", "typescript": "^5"}}