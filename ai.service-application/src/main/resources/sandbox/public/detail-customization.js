document.addEventListener('DOMContentLoaded', function() {
    // 获取所有选项
    const optionItems = document.querySelectorAll('.option-item');
    const customInputItem = document.getElementById('customInputItem');
    const customInputContainer = document.querySelector('.custom-input-container');
    const customInput = document.querySelector('.custom-input');
    const voiceInputBtn = document.querySelector('.voice-input-btn');
    const addDemandBtn = document.querySelector('.add-demand-btn');

    // 为每个选项添加点击事件
    optionItems.forEach(item => {
        if (item !== customInputContainer) {
            item.addEventListener('click', function() {
                const formGroup = this.closest('.form-group');
                const formGroups = Array.from(document.querySelectorAll('.form-group'));
                const formGroupIndex = formGroups.indexOf(formGroup);

                // 第一组选项（多选）
                if (formGroupIndex === 0) {
                    // 如果点击的是"手动添加需求"
                    if (this === customInputItem) {
                        customInputContainer.style.display = 'flex';
                        customInput.focus();
                    } else {
                        this.classList.toggle('selected');
                    }
                } else {
                    // 其他组选项（单选）
                    const siblings = Array.from(this.parentNode.children);
                    siblings.forEach(sibling => {
                        sibling.classList.remove('selected');
                    });
                    this.classList.add('selected');
                }
            });
        }
    });

    // 更新自定义输入选项的文本
    function updateCustomInputItemText(text) {
        const contentElement = customInputItem.querySelector('.option-content');
        if (contentElement) {
            // 保存原始文本，如果没有保存过
            if (!customInputItem.dataset.originalText) {
                customInputItem.dataset.originalText = contentElement.textContent;
            }

            // 更新文本
            contentElement.textContent = text || customInputItem.dataset.originalText;

            // 如果有文本，选中该选项
            if (text) {
                customInputItem.classList.add('selected');
            } else {
                customInputItem.classList.remove('selected');
            }
        }
    }

    // 添加需求按钮点击事件
    if (addDemandBtn) {
        addDemandBtn.addEventListener('click', function() {
            const inputValue = customInput.value.trim();
            if (inputValue) {
                updateCustomInputItemText(inputValue);
                customInputContainer.style.display = 'none';
            }
        });
    }

    // 自定义输入框处理
    if (customInput) {
        customInput.addEventListener('focus', function() {
            customInputContainer.classList.add('selected');
        });

        customInput.addEventListener('blur', function() {
            // 延迟处理，以便点击添加按钮时能先触发按钮的点击事件
            setTimeout(() => {
                if (this.value.trim() === '' && !customInputContainer.contains(document.activeElement)) {
                    customInputContainer.style.display = 'none';
                    customInputContainer.classList.remove('selected');
                    updateCustomInputItemText(''); // 恢复原始文本
                }
            }, 200);
        });

        // 监听回车键
        customInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                const inputValue = this.value.trim();
                if (inputValue) {
                    updateCustomInputItemText(inputValue);
                    customInputContainer.style.display = 'none';
                }
            }
        });
    }

    // 语音输入功能
    if (voiceInputBtn) {
        let recognition;
        let isRecording = false;

        // 检查浏览器是否支持语音识别
        if ('webkitSpeechRecognition' in window) {
            recognition = new webkitSpeechRecognition();
            recognition.continuous = false;
            recognition.interimResults = true;
            recognition.lang = 'zh-CN';

            recognition.onstart = function() {
                isRecording = true;
                voiceInputBtn.classList.add('recording');
                // 可以添加提示用户正在录音的UI
                console.log('语音识别已启动');
            };

            recognition.onresult = function(event) {
                const transcript = event.results[0][0].transcript;
                customInput.value = transcript;
                console.log('识别结果:', transcript);

                // 语音识别完成后自动更新选项文本
                if (event.results[0].isFinal) {
                    updateCustomInputItemText(transcript);
                }
            };

            recognition.onerror = function(event) {
                console.error('语音识别错误:', event.error);
                stopRecording();
            };

            recognition.onend = function() {
                stopRecording();

                // 语音识别结束后，如果有内容则更新选项并隐藏输入框
                if (customInput.value.trim() !== '') {
                    updateCustomInputItemText(customInput.value.trim());
                    customInputContainer.style.display = 'none';
                }
            };

            voiceInputBtn.addEventListener('click', function() {
                if (!isRecording) {
                    // 确保输入框可见
                    customInputContainer.style.display = 'flex';
                    customInputContainer.classList.add('selected');

                    // 开始录音
                    try {
                        recognition.start();
                    } catch (e) {
                        console.error('语音识别启动失败:', e);
                    }
                } else {
                    // 停止录音
                    recognition.stop();
                }
            });

            function stopRecording() {
                isRecording = false;
                voiceInputBtn.classList.remove('recording');
                console.log('语音识别已停止');
            }
        } else {
            // 浏览器不支持语音识别
            voiceInputBtn.addEventListener('click', function() {
                alert('您的浏览器不支持语音识别功能');
            });
            console.warn('此浏览器不支持语音识别');
        }
    }

    // 返回按钮点击事件
    const backButton = document.querySelector('.back-button');
    if (backButton) {
        backButton.addEventListener('click', function() {
            window.location.href = 'home.html';
        });
    }

    // 提交按钮点击事件
    const submitButton = document.querySelector('.submit-button');
    if (submitButton) {
        submitButton.addEventListener('click', function() {
            // 收集所有选中的选项
            const selectedOptions = {
                preferences: [],
                customPreference: '',
                improvementLevel: '',
                improvementMethod: ''
            };

            // 收集第一组选项（多选）
            const preferenceItems = document.querySelectorAll('.form-group:nth-child(1) .option-item.selected');
            preferenceItems.forEach(item => {
                if (item !== customInputContainer) {
                    selectedOptions.preferences.push(item.querySelector('.option-content').textContent);
                }
            });

            // 收集自定义输入
            if (customInput && customInput.value.trim() !== '') {
                selectedOptions.customPreference = customInput.value.trim();
            } else if (customInputItem.classList.contains('selected')) {
                // 如果自定义输入选项被选中，获取其文本
                const customText = customInputItem.querySelector('.option-content').textContent;
                if (customText !== customInputItem.dataset.originalText) {
                    selectedOptions.customPreference = customText;
                }
            }

            // 收集第二组选项（单选）
            const levelItem = document.querySelector('.form-group:nth-child(2) .option-item.selected');
            if (levelItem) {
                selectedOptions.improvementLevel = levelItem.querySelector('.option-content').textContent;
            }

            // 收集第三组选项（单选）
            const methodItem = document.querySelector('.form-group:nth-child(3) .option-item.selected');
            if (methodItem) {
                selectedOptions.improvementMethod = methodItem.querySelector('.option-content').textContent;
            }

            // 这里可以处理提交逻辑，例如发送到服务器或跳转到下一页
            console.log('提交的选项:', selectedOptions);
            window.location.href = 'analysis.html';
        });
    }
});
