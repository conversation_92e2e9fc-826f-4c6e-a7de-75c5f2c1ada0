<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <title>Facelab</title>
    <link rel="stylesheet" href="recording.css">
</head>
<body>
<div class="app-container">
    <div class="video-capture-container">
        <div class="header">
            <div class="back-button">
                <img src="images/back_dark.png" alt="返回" class="back-button-image">
            </div>
        </div>

        <video id="preview" autoplay muted playsinline></video>
        <canvas id="overlay" class="overlay"></canvas>
        <img id="face-circle" class="face-circle" src="images/record_bg.png" alt="Face Guide">

        <div class="guide-text-container">
            <div id="guideText" class="guide-text">请打开声音，根据语音引导开始拍摄</div>
            <div id="progressBar" class="progress-bar"></div>

        </div>

        <div class="camera-button-container">
            <button id="captureBtn" class="camera-button">
            </button>
        </div>
    </div>
</div>
<div id="modal" class="modal-overlay" style="display: flex;">
    <div class="guide-container">
        <h1 class="guide-title">拍摄指南</h1>
        <div class="guide-content">
            <div class="guide-tips">
                <ul>
                    <li>请确保光线充足，面部清晰可见</li>
                    <li>拍摄过程中请保持面部在框内</li>
                    <li>请按照语音提示完成各个表情动作</li>
                    <li>整个过程大约需要30秒钟</li>
                    <li>完成后系统将自动分析并生成您的面部模型</li>
                </ul>
            </div>
        </div>
        <button class="start-button">我知道了</button>
    </div>
    <img src="images/start_record_close.png" alt="Close" class="modal-close-button">
</div>

<script>
    const modal = document.getElementById('modal');
    const closeModalButton = document.querySelector('.modal-close-button');
    const startButton = document.querySelector('.start-button');

    closeModalButton.addEventListener('click', () => {
        modal.style.display = 'none';
    });

    startButton.addEventListener('click', () => {
        modal.style.display = 'none';
    });

    window.addEventListener('click', (event) => {
        if (event.target == modal) {
            modal.style.display = 'none';
        }
    });
</script>
<script src="recording.js"></script>
</body>
</html>
