* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
    -webkit-tap-highlight-color: transparent;
}

html, body {
    background-color: #000;
    color: #fff;
    height: 100%;
    width: 100%;
    overflow: hidden;
    position: fixed;
    touch-action: none;
}

.app-container {
    width: 100%;
    height: 100%;
    position: relative;
    overflow: hidden;
    background-color: #000;
    display: flex;
    flex-direction: column;
    max-width: 100vw;
}

.video-capture-container {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 60px;
    width: 100%;
    padding: 0 16px;
    position: relative;
}

.back-button {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: #000;
    cursor: pointer;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    z-index: 5;
}

.back-button-image {
    width: 100%;
    height: 100%;
}

#preview {
    width: 100%;
    height: 100%;
    object-fit: cover;
    background-color: #000;
    position: absolute;
    top: 0;
    left: 0;
}

.overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 2;
}

.face-circle {
    position: absolute;
    top: 40%;
    left: 0;
    transform: translateY(-50%);
    width: 100%;
    padding: 0 32px;
    box-sizing: border-box;
    border: none;
    border-radius: 0;
}

.guide-text-container {
    position: absolute;
    bottom: 9em;
    left: 0;
    width: 100%;
    height: 5rem;
    z-index: 5;
    align-items: center;
}

.guide-text {
    text-align: center;
    font-size: 1.2rem;
    color: #fff;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);
    background-color: transparent;
}

.progress-bar {
    padding: 1rem 0;
    text-align: center;
    font-size: 1.2rem;
    color: #fff;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);
    background-color: transparent;
    z-index: 10;
}

.progress-bar span {
    color: #6D45E5;
}

.camera-button-container {
    position: absolute;
    bottom: 3em;
    left: 0;
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 5;
}

.camera-button {
    width: 70px;
    height: 70px;
    background-color: transparent;
    background-image: url('images/record_button.png');
    background-size: contain;
    background-repeat: no-repeat;
    border: none;
    border-radius: 50%;
    padding: 0;
    cursor: pointer;
    -webkit-appearance: none;
    appearance: none;
    outline: none;
}

.camera-button.recording {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(255, 255, 255, 0);
    }
    20% {
        box-shadow: 0 0 40px 20px rgba(139, 92, 246, 0.9);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(255, 255, 255, 0);
    }
}

.camera-icon {
    color: #000;
    display: flex;
    justify-content: center;
    align-items: center;
}

.bottom-bar {
    position: absolute;
    bottom: calc(8px + env(safe-area-inset-bottom, 0px));
    left: 50%;
    transform: translateX(-50%);
    width: 40%;
    height: 5px;
    background-color: #fff;
    z-index: 5;
}

/* Modal Styles */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    padding: 0 32px;
    box-sizing: border-box;
}

.guide-container {
    width: 100%;
    max-width: 500px;
    background-color: #fff;
    border-radius: 20px;
    padding: 16px 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
    display: flex;
    flex-direction: column;
    color: #000;
}

.guide-title {
    text-align: center;
    font-size: 24px;
    font-weight: 600;
    margin: 0;
}

.guide-content {
    margin: 1rem 0;
}

.guide-tips ul {
    padding-left: 20px;
    margin: 0;
    line-height: 1rem;
}

.guide-tips li {
    margin-bottom: 0.25rem;
    line-height: 1.5;
    font-size: 1rem;
    color: #555555;
}

.start-button {
    width: 100%;
    height: 40px;
    background-color: #111111;
    color: #fff;
    border: none;
    border-radius: 20px;
    font-size: 18px;
    font-weight: 500;
    cursor: pointer;
}

.modal-close-button {
    position: absolute;
    bottom: calc(50% - 200px);
    left: 50%;
    transform: translate(-50%, 1rem);
    cursor: pointer;
    width: 2rem;
    height: 2rem;
}