var Hh = Object.create;
var { getPrototypeOf: Mh, defineProperty: Fr, getOwnPropertyNames: yh } = Object;
var Ah = Object.prototype.hasOwnProperty;
var rf = (l, t, i) => {
    i = l != null ? Hh(Mh(l)) : {};
    let r = t || !l || !l.__esModule ? Fr(i, 'default', { value: l, enumerable: !0 }) : i;
    for (let n of yh(l)) if (!Ah.call(r, n)) Fr(r, n, { get: () => l[n], enumerable: !0 });
    return r;
};
var hl = (l, t) => () => (t || l((t = { exports: {} }).exports, t), t.exports);
var D = (l, t) => {
    for (var i in t)
        Fr(l, i, { get: t[i], enumerable: !0, configurable: !0, set: (r) => (t[i] = () => r) });
};
var Rr = hl((Lx, nf) => {
    function Uh(l) {
        var t = typeof l;
        return l != null && (t == 'object' || t == 'function');
    }
    nf.exports = Uh;
});
var bf = hl((Bx, of) => {
    var kh = typeof global == 'object' && global && global.Object === Object && global;
    of.exports = kh;
});
var Vr = hl((Fx, ff) => {
    var Sh = bf(),
        Nh = typeof self == 'object' && self && self.Object === Object && self,
        Ch = Sh || Nh || Function('return this')();
    ff.exports = Ch;
});
var ef = hl((Rx, gf) => {
    var Ph = Vr(),
        Ih = function () {
            return Ph.Date.now();
        };
    gf.exports = Ih;
});
var cf = hl((Vx, hf) => {
    var Th = /\s/;
    function Zh(l) {
        var t = l.length;
        while (t-- && Th.test(l.charAt(t)));
        return t;
    }
    hf.exports = Zh;
});
var wf = hl((Kx, mf) => {
    var dh = cf(),
        lc = /^\s+/;
    function tc(l) {
        return l ? l.slice(0, dh(l) + 1).replace(lc, '') : l;
    }
    mf.exports = tc;
});
var Kr = hl((Hx, pf) => {
    var ic = Vr(),
        rc = ic.Symbol;
    pf.exports = rc;
});
var af = hl((Mx, xf) => {
    var uf = Kr(),
        zf = Object.prototype,
        nc = zf.hasOwnProperty,
        oc = zf.toString,
        hi = uf ? uf.toStringTag : void 0;
    function bc(l) {
        var t = nc.call(l, hi),
            i = l[hi];
        try {
            l[hi] = void 0;
            var r = !0;
        } catch (o) {}
        var n = oc.call(l);
        if (r)
            if (t) l[hi] = i;
            else delete l[hi];
        return n;
    }
    xf.exports = bc;
});
var vf = hl((yx, _f) => {
    var fc = Object.prototype,
        gc = fc.toString;
    function ec(l) {
        return gc.call(l);
    }
    _f.exports = ec;
});
var qf = hl((Ax, $f) => {
    var Of = Kr(),
        hc = af(),
        cc = vf(),
        mc = '[object Null]',
        wc = '[object Undefined]',
        Df = Of ? Of.toStringTag : void 0;
    function pc(l) {
        if (l == null) return l === void 0 ? wc : mc;
        return Df && Df in Object(l) ? hc(l) : cc(l);
    }
    $f.exports = pc;
});
var Jf = hl((Ux, sf) => {
    function uc(l) {
        return l != null && typeof l == 'object';
    }
    sf.exports = uc;
});
var Wf = hl((kx, Xf) => {
    var zc = qf(),
        xc = Jf(),
        ac = '[object Symbol]';
    function _c(l) {
        return typeof l == 'symbol' || (xc(l) && zc(l) == ac);
    }
    Xf.exports = _c;
});
var Qf = hl((Sx, Ef) => {
    var vc = wf(),
        jf = Rr(),
        Oc = Wf(),
        Yf = NaN,
        Dc = /^[-+]0x[0-9a-f]+$/i,
        $c = /^0b[01]+$/i,
        qc = /^0o[0-7]+$/i,
        sc = parseInt;
    function Jc(l) {
        if (typeof l == 'number') return l;
        if (Oc(l)) return Yf;
        if (jf(l)) {
            var t = typeof l.valueOf == 'function' ? l.valueOf() : l;
            l = jf(t) ? t + '' : t;
        }
        if (typeof l != 'string') return l === 0 ? l : +l;
        l = vc(l);
        var i = $c.test(l);
        return i || qc.test(l) ? sc(l.slice(2), i ? 2 : 8) : Dc.test(l) ? Yf : +l;
    }
    Ef.exports = Jc;
});
var Mr = hl((Nx, Lf) => {
    var Xc = Rr(),
        Hr = ef(),
        Gf = Qf(),
        Wc = 'Expected a function',
        jc = Math.max,
        Yc = Math.min;
    function Ec(l, t, i) {
        var r,
            n,
            o,
            b,
            g,
            e,
            f = 0,
            h = !1,
            c = !1,
            m = !0;
        if (typeof l != 'function') throw new TypeError(Wc);
        if (((t = Gf(t) || 0), Xc(i)))
            ((h = !!i.leading),
                (c = 'maxWait' in i),
                (o = c ? jc(Gf(i.maxWait) || 0, t) : o),
                (m = 'trailing' in i ? !!i.trailing : m));
        function u(L) {
            var y = r,
                T = n;
            return ((r = n = void 0), (f = L), (b = l.apply(T, y)), b);
        }
        function J(L) {
            return ((f = L), (g = setTimeout(q, t)), h ? u(L) : b);
        }
        function I(L) {
            var y = L - e,
                T = L - f,
                Br = t - y;
            return c ? Yc(Br, o - T) : Br;
        }
        function R(L) {
            var y = L - e,
                T = L - f;
            return e === void 0 || y >= t || y < 0 || (c && T >= o);
        }
        function q() {
            var L = Hr();
            if (R(L)) return V(L);
            g = setTimeout(q, I(L));
        }
        function V(L) {
            if (((g = void 0), m && r)) return u(L);
            return ((r = n = void 0), b);
        }
        function fl() {
            if (g !== void 0) clearTimeout(g);
            ((f = 0), (r = e = n = g = void 0));
        }
        function K() {
            return g === void 0 ? b : V(Hr());
        }
        function G() {
            var L = Hr(),
                y = R(L);
            if (((r = arguments), (n = this), (e = L), y)) {
                if (g === void 0) return J(e);
                if (c) return (clearTimeout(g), (g = setTimeout(q, t)), u(e));
            }
            if (g === void 0) g = setTimeout(q, t);
            return b;
        }
        return ((G.cancel = fl), (G.flush = K), G);
    }
    Lf.exports = Ec;
});
var Bg = hl((Wm) => {
    var Lg = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/'.split('');
    Wm.encode = function (l) {
        if (0 <= l && l < Lg.length) return Lg[l];
        throw new TypeError('Must be between 0 and 63: ' + l);
    };
    Wm.decode = function (l) {
        var t = 65,
            i = 90,
            r = 97,
            n = 122,
            o = 48,
            b = 57,
            g = 43,
            e = 47,
            f = 26,
            h = 52;
        if (t <= l && l <= i) return l - t;
        if (r <= l && l <= n) return l - r + f;
        if (o <= l && l <= b) return l - o + h;
        if (l == g) return 62;
        if (l == e) return 63;
        return -1;
    };
});
var Hg = hl((Gm) => {
    var Fg = Bg(),
        rn = 5,
        Rg = 1 << rn,
        Vg = Rg - 1,
        Kg = Rg;
    function Em(l) {
        return l < 0 ? (-l << 1) + 1 : (l << 1) + 0;
    }
    function Qm(l) {
        var t = (l & 1) === 1,
            i = l >> 1;
        return t ? -i : i;
    }
    Gm.encode = function l(t) {
        var i = '',
            r,
            n = Em(t);
        do {
            if (((r = n & Vg), (n >>>= rn), n > 0)) r |= Kg;
            i += Fg.encode(r);
        } while (n > 0);
        return i;
    };
    Gm.decode = function l(t, i, r) {
        var n = t.length,
            o = 0,
            b = 0,
            g,
            e;
        do {
            if (i >= n) throw new Error('Expected more digits in base 64 VLQ value.');
            if (((e = Fg.decode(t.charCodeAt(i++))), e === -1))
                throw new Error('Invalid base64 digit: ' + t.charAt(i - 1));
            ((g = !!(e & Kg)), (e &= Vg), (o = o + (e << b)), (b += rn));
        } while (g);
        ((r.value = Qm(o)), (r.rest = i));
    };
});
var fr = hl((Im) => {
    function Fm(l, t, i) {
        if (t in l) return l[t];
        else if (arguments.length === 3) return i;
        else throw new Error('"' + t + '" is a required argument.');
    }
    Im.getArg = Fm;
    var Mg = /^(?:([\w+\-.]+):)?\/\/(?:(\w+:\w+)@)?([\w.-]*)(?::(\d+))?(.*)$/,
        Rm = /^data:.+\,.+$/;
    function ai(l) {
        var t = l.match(Mg);
        if (!t) return null;
        return { scheme: t[1], auth: t[2], host: t[3], port: t[4], path: t[5] };
    }
    Im.urlParse = ai;
    function Vt(l) {
        var t = '';
        if (l.scheme) t += l.scheme + ':';
        if (((t += '//'), l.auth)) t += l.auth + '@';
        if (l.host) t += l.host;
        if (l.port) t += ':' + l.port;
        if (l.path) t += l.path;
        return t;
    }
    Im.urlGenerate = Vt;
    var Vm = 32;
    function Km(l) {
        var t = [];
        return function (i) {
            for (var r = 0; r < t.length; r++)
                if (t[r].input === i) {
                    var n = t[0];
                    return ((t[0] = t[r]), (t[r] = n), t[0].result);
                }
            var o = l(i);
            if ((t.unshift({ input: i, result: o }), t.length > Vm)) t.pop();
            return o;
        };
    }
    var nn = Km(function l(t) {
        var i = t,
            r = ai(t);
        if (r) {
            if (!r.path) return t;
            i = r.path;
        }
        var n = Im.isAbsolute(i),
            o = [],
            b = 0,
            g = 0;
        while (!0)
            if (((b = g), (g = i.indexOf('/', b)), g === -1)) {
                o.push(i.slice(b));
                break;
            } else {
                o.push(i.slice(b, g));
                while (g < i.length && i[g] === '/') g++;
            }
        for (var e, f = 0, g = o.length - 1; g >= 0; g--)
            if (((e = o[g]), e === '.')) o.splice(g, 1);
            else if (e === '..') f++;
            else if (f > 0)
                if (e === '') (o.splice(g + 1, f), (f = 0));
                else (o.splice(g, 2), f--);
        if (((i = o.join('/')), i === '')) i = n ? '/' : '.';
        if (r) return ((r.path = i), Vt(r));
        return i;
    });
    Im.normalize = nn;
    function yg(l, t) {
        if (l === '') l = '.';
        if (t === '') t = '.';
        var i = ai(t),
            r = ai(l);
        if (r) l = r.path || '/';
        if (i && !i.scheme) {
            if (r) i.scheme = r.scheme;
            return Vt(i);
        }
        if (i || t.match(Rm)) return t;
        if (r && !r.host && !r.path) return ((r.host = t), Vt(r));
        var n = t.charAt(0) === '/' ? t : nn(l.replace(/\/+$/, '') + '/' + t);
        if (r) return ((r.path = n), Vt(r));
        return n;
    }
    Im.join = yg;
    Im.isAbsolute = function (l) {
        return l.charAt(0) === '/' || Mg.test(l);
    };
    function Hm(l, t) {
        if (l === '') l = '.';
        l = l.replace(/\/$/, '');
        var i = 0;
        while (t.indexOf(l + '/') !== 0) {
            var r = l.lastIndexOf('/');
            if (r < 0) return t;
            if (((l = l.slice(0, r)), l.match(/^([^\/]+:\/)?\/*$/))) return t;
            ++i;
        }
        return Array(i + 1).join('../') + t.substr(l.length + 1);
    }
    Im.relative = Hm;
    var Ag = (function () {
        var l = Object.create(null);
        return !('__proto__' in l);
    })();
    function Ug(l) {
        return l;
    }
    function Mm(l) {
        if (kg(l)) return '$' + l;
        return l;
    }
    Im.toSetString = Ag ? Ug : Mm;
    function ym(l) {
        if (kg(l)) return l.slice(1);
        return l;
    }
    Im.fromSetString = Ag ? Ug : ym;
    function kg(l) {
        if (!l) return !1;
        var t = l.length;
        if (t < 9) return !1;
        if (
            l.charCodeAt(t - 1) !== 95 ||
            l.charCodeAt(t - 2) !== 95 ||
            l.charCodeAt(t - 3) !== 111 ||
            l.charCodeAt(t - 4) !== 116 ||
            l.charCodeAt(t - 5) !== 111 ||
            l.charCodeAt(t - 6) !== 114 ||
            l.charCodeAt(t - 7) !== 112 ||
            l.charCodeAt(t - 8) !== 95 ||
            l.charCodeAt(t - 9) !== 95
        )
            return !1;
        for (var i = t - 10; i >= 0; i--) if (l.charCodeAt(i) !== 36) return !1;
        return !0;
    }
    function Am(l, t, i) {
        var r = Zl(l.source, t.source);
        if (r !== 0) return r;
        if (((r = l.originalLine - t.originalLine), r !== 0)) return r;
        if (((r = l.originalColumn - t.originalColumn), r !== 0 || i)) return r;
        if (((r = l.generatedColumn - t.generatedColumn), r !== 0)) return r;
        if (((r = l.generatedLine - t.generatedLine), r !== 0)) return r;
        return Zl(l.name, t.name);
    }
    Im.compareByOriginalPositions = Am;
    function Um(l, t, i) {
        var r = l.originalLine - t.originalLine;
        if (r !== 0) return r;
        if (((r = l.originalColumn - t.originalColumn), r !== 0 || i)) return r;
        if (((r = l.generatedColumn - t.generatedColumn), r !== 0)) return r;
        if (((r = l.generatedLine - t.generatedLine), r !== 0)) return r;
        return Zl(l.name, t.name);
    }
    Im.compareByOriginalPositionsNoSource = Um;
    function km(l, t, i) {
        var r = l.generatedLine - t.generatedLine;
        if (r !== 0) return r;
        if (((r = l.generatedColumn - t.generatedColumn), r !== 0 || i)) return r;
        if (((r = Zl(l.source, t.source)), r !== 0)) return r;
        if (((r = l.originalLine - t.originalLine), r !== 0)) return r;
        if (((r = l.originalColumn - t.originalColumn), r !== 0)) return r;
        return Zl(l.name, t.name);
    }
    Im.compareByGeneratedPositionsDeflated = km;
    function Sm(l, t, i) {
        var r = l.generatedColumn - t.generatedColumn;
        if (r !== 0 || i) return r;
        if (((r = Zl(l.source, t.source)), r !== 0)) return r;
        if (((r = l.originalLine - t.originalLine), r !== 0)) return r;
        if (((r = l.originalColumn - t.originalColumn), r !== 0)) return r;
        return Zl(l.name, t.name);
    }
    Im.compareByGeneratedPositionsDeflatedNoLine = Sm;
    function Zl(l, t) {
        if (l === t) return 0;
        if (l === null) return 1;
        if (t === null) return -1;
        if (l > t) return 1;
        return -1;
    }
    function Nm(l, t) {
        var i = l.generatedLine - t.generatedLine;
        if (i !== 0) return i;
        if (((i = l.generatedColumn - t.generatedColumn), i !== 0)) return i;
        if (((i = Zl(l.source, t.source)), i !== 0)) return i;
        if (((i = l.originalLine - t.originalLine), i !== 0)) return i;
        if (((i = l.originalColumn - t.originalColumn), i !== 0)) return i;
        return Zl(l.name, t.name);
    }
    Im.compareByGeneratedPositionsInflated = Nm;
    function Cm(l) {
        return JSON.parse(l.replace(/^\)]}'[^\n]*\n/, ''));
    }
    Im.parseSourceMapInput = Cm;
    function Pm(l, t, i) {
        if (((t = t || ''), l)) {
            if (l[l.length - 1] !== '/' && t[0] !== '/') l += '/';
            t = l + t;
        }
        if (i) {
            var r = ai(i);
            if (!r) throw new Error('sourceMapURL could not be parsed');
            if (r.path) {
                var n = r.path.lastIndexOf('/');
                if (n >= 0) r.path = r.path.substring(0, n + 1);
            }
            t = yg(Vt(r), t);
        }
        return nn(t);
    }
    Im.computeSourceURL = Pm;
});
var Sg = hl((ww) => {
    var on = fr(),
        bn = Object.prototype.hasOwnProperty,
        vt = typeof Map !== 'undefined';
    function dl() {
        ((this._array = []), (this._set = vt ? new Map() : Object.create(null)));
    }
    dl.fromArray = function l(t, i) {
        var r = new dl();
        for (var n = 0, o = t.length; n < o; n++) r.add(t[n], i);
        return r;
    };
    dl.prototype.size = function l() {
        return vt ? this._set.size : Object.getOwnPropertyNames(this._set).length;
    };
    dl.prototype.add = function l(t, i) {
        var r = vt ? t : on.toSetString(t),
            n = vt ? this.has(t) : bn.call(this._set, r),
            o = this._array.length;
        if (!n || i) this._array.push(t);
        if (!n)
            if (vt) this._set.set(t, o);
            else this._set[r] = o;
    };
    dl.prototype.has = function l(t) {
        if (vt) return this._set.has(t);
        else {
            var i = on.toSetString(t);
            return bn.call(this._set, i);
        }
    };
    dl.prototype.indexOf = function l(t) {
        if (vt) {
            var i = this._set.get(t);
            if (i >= 0) return i;
        } else {
            var r = on.toSetString(t);
            if (bn.call(this._set, r)) return this._set[r];
        }
        throw new Error('"' + t + '" is not in the set.');
    };
    dl.prototype.at = function l(t) {
        if (t >= 0 && t < this._array.length) return this._array[t];
        throw new Error('No element indexed by ' + t);
    };
    dl.prototype.toArray = function l() {
        return this._array.slice();
    };
    ww.ArraySet = dl;
});
var Cg = hl((zw) => {
    var Ng = fr();
    function uw(l, t) {
        var i = l.generatedLine,
            r = t.generatedLine,
            n = l.generatedColumn,
            o = t.generatedColumn;
        return r > i || (r == i && o >= n) || Ng.compareByGeneratedPositionsInflated(l, t) <= 0;
    }
    function gr() {
        ((this._array = []),
            (this._sorted = !0),
            (this._last = { generatedLine: -1, generatedColumn: 0 }));
    }
    gr.prototype.unsortedForEach = function l(t, i) {
        this._array.forEach(t, i);
    };
    gr.prototype.add = function l(t) {
        if (uw(this._last, t)) ((this._last = t), this._array.push(t));
        else ((this._sorted = !1), this._array.push(t));
    };
    gr.prototype.toArray = function l() {
        if (!this._sorted)
            (this._array.sort(Ng.compareByGeneratedPositionsInflated), (this._sorted = !0));
        return this._array;
    };
    zw.MappingList = gr;
});
var Wt = 'PENPAL_CHILD';
var Vh = rf(Mr(), 1);
var Qc = class extends Error {
        code;
        constructor(l, t) {
            super(t);
            ((this.name = 'PenpalError'), (this.code = l));
        }
    },
    _l = Qc,
    Gc = (l) => ({
        name: l.name,
        message: l.message,
        stack: l.stack,
        penpalCode: l instanceof _l ? l.code : void 0,
    }),
    Lc = ({ name: l, message: t, stack: i, penpalCode: r }) => {
        let n = r ? new _l(r, t) : new Error(t);
        return ((n.name = l), (n.stack = i), n);
    },
    Bc = Symbol('Reply'),
    Fc = class {
        value;
        transferables;
        #l = Bc;
        constructor(l, t) {
            ((this.value = l), (this.transferables = t?.transferables));
        }
    },
    Rc = Fc,
    ql = 'penpal',
    Ui = (l) => {
        return typeof l === 'object' && l !== null;
    },
    Kf = (l) => {
        return typeof l === 'function';
    },
    Vc = (l) => {
        return Ui(l) && l.namespace === ql;
    },
    jt = (l) => {
        return l.type === 'SYN';
    },
    ki = (l) => {
        return l.type === 'ACK1';
    },
    ci = (l) => {
        return l.type === 'ACK2';
    },
    Hf = (l) => {
        return l.type === 'CALL';
    },
    Mf = (l) => {
        return l.type === 'REPLY';
    },
    Kc = (l) => {
        return l.type === 'DESTROY';
    },
    yf = (l, t = []) => {
        let i = [];
        for (let r of Object.keys(l)) {
            let n = l[r];
            if (Kf(n)) i.push([...t, r]);
            else if (Ui(n)) i.push(...yf(n, [...t, r]));
        }
        return i;
    },
    Hc = (l, t) => {
        let i = l.reduce((r, n) => {
            return Ui(r) ? r[n] : void 0;
        }, t);
        return Kf(i) ? i : void 0;
    },
    ot = (l) => {
        return l.join('.');
    },
    Bf = (l, t, i) => ({
        namespace: ql,
        channel: l,
        type: 'REPLY',
        callId: t,
        isError: !0,
        ...(i instanceof Error ? { value: Gc(i), isSerializedErrorInstance: !0 } : { value: i }),
    }),
    Mc = (l, t, i, r) => {
        let n = !1,
            o = async (b) => {
                if (n) return;
                if (!Hf(b)) return;
                r?.(`Received ${ot(b.methodPath)}() call`, b);
                let { methodPath: g, args: e, id: f } = b,
                    h,
                    c;
                try {
                    let m = Hc(g, t);
                    if (!m) throw new _l('METHOD_NOT_FOUND', `Method \`${ot(g)}\` is not found.`);
                    let u = await m(...e);
                    if (u instanceof Rc) ((c = u.transferables), (u = await u.value));
                    h = { namespace: ql, channel: i, type: 'REPLY', callId: f, value: u };
                } catch (m) {
                    h = Bf(i, f, m);
                }
                if (n) return;
                try {
                    (r?.(`Sending ${ot(g)}() reply`, h), l.sendMessage(h, c));
                } catch (m) {
                    if (m.name === 'DataCloneError')
                        ((h = Bf(i, f, m)), r?.(`Sending ${ot(g)}() reply`, h), l.sendMessage(h));
                    throw m;
                }
            };
        return (
            l.addMessageHandler(o),
            () => {
                ((n = !0), l.removeMessageHandler(o));
            }
        );
    },
    yc = Mc,
    Af =
        crypto.randomUUID?.bind(crypto) ??
        (() =>
            new Array(4)
                .fill(0)
                .map(() => Math.floor(Math.random() * Number.MAX_SAFE_INTEGER).toString(16))
                .join('-')),
    Ac = Symbol('CallOptions'),
    Uc = class {
        transferables;
        timeout;
        #l = Ac;
        constructor(l) {
            ((this.transferables = l?.transferables), (this.timeout = l?.timeout));
        }
    },
    kc = Uc,
    Sc = new Set(['apply', 'call', 'bind']),
    Uf = (l, t, i = []) => {
        return new Proxy(i.length ? () => {} : Object.create(null), {
            get(r, n) {
                if (n === 'then') return;
                if (i.length && Sc.has(n)) return Reflect.get(r, n);
                return Uf(l, t, [...i, n]);
            },
            apply(r, n, o) {
                return l(i, o);
            },
        });
    },
    Ff = (l) => {
        return new _l(
            'CONNECTION_DESTROYED',
            `Method call ${ot(l)}() failed due to destroyed connection`,
        );
    },
    Nc = (l, t, i) => {
        let r = !1,
            n = new Map(),
            o = (e) => {
                if (!Mf(e)) return;
                let { callId: f, value: h, isError: c, isSerializedErrorInstance: m } = e,
                    u = n.get(f);
                if (!u) return;
                if ((n.delete(f), i?.(`Received ${ot(u.methodPath)}() call`, e), c))
                    u.reject(m ? Lc(h) : h);
                else u.resolve(h);
            };
        return (
            l.addMessageHandler(o),
            {
                remoteProxy: Uf((e, f) => {
                    if (r) throw Ff(e);
                    let h = Af(),
                        c = f[f.length - 1],
                        m = c instanceof kc,
                        { timeout: u, transferables: J } = m ? c : {},
                        I = m ? f.slice(0, -1) : f;
                    return new Promise((R, q) => {
                        let V =
                            u !== void 0
                                ? window.setTimeout(() => {
                                      (n.delete(h),
                                          q(
                                              new _l(
                                                  'METHOD_CALL_TIMEOUT',
                                                  `Method call ${ot(e)}() timed out after ${u}ms`,
                                              ),
                                          ));
                                  }, u)
                                : void 0;
                        n.set(h, { methodPath: e, resolve: R, reject: q, timeoutId: V });
                        try {
                            let fl = {
                                namespace: ql,
                                channel: t,
                                type: 'CALL',
                                id: h,
                                methodPath: e,
                                args: I,
                            };
                            (i?.(`Sending ${ot(e)}() call`, fl), l.sendMessage(fl, J));
                        } catch (fl) {
                            q(new _l('TRANSMISSION_FAILED', fl.message));
                        }
                    });
                }, i),
                destroy: () => {
                    ((r = !0), l.removeMessageHandler(o));
                    for (let { methodPath: e, reject: f, timeoutId: h } of n.values())
                        (clearTimeout(h), f(Ff(e)));
                    n.clear();
                },
            }
        );
    },
    Cc = Nc,
    Pc = () => {
        let l, t;
        return {
            promise: new Promise((r, n) => {
                ((l = r), (t = n));
            }),
            resolve: l,
            reject: t,
        };
    },
    Ic = Pc,
    Tc = class extends Error {
        constructor(l) {
            super(
                `You've hit a bug in Penpal. Please file an issue with the following information: ${l}`,
            );
        }
    },
    Yt = Tc,
    yr = 'deprecated-penpal',
    Zc = (l) => {
        return Ui(l) && 'penpal' in l;
    },
    dc = (l) => l.split('.'),
    Rf = (l) => l.join('.'),
    kf = (l) => {
        return new Yt(`Unexpected message to translate: ${JSON.stringify(l)}`);
    },
    lm = (l) => {
        if (l.penpal === 'syn')
            return { namespace: ql, channel: void 0, type: 'SYN', participantId: yr };
        if (l.penpal === 'ack') return { namespace: ql, channel: void 0, type: 'ACK2' };
        if (l.penpal === 'call')
            return {
                namespace: ql,
                channel: void 0,
                type: 'CALL',
                id: l.id,
                methodPath: dc(l.methodName),
                args: l.args,
            };
        if (l.penpal === 'reply')
            if (l.resolution === 'fulfilled')
                return {
                    namespace: ql,
                    channel: void 0,
                    type: 'REPLY',
                    callId: l.id,
                    value: l.returnValue,
                };
            else
                return {
                    namespace: ql,
                    channel: void 0,
                    type: 'REPLY',
                    callId: l.id,
                    isError: !0,
                    ...(l.returnValueIsError
                        ? { value: l.returnValue, isSerializedErrorInstance: !0 }
                        : { value: l.returnValue }),
                };
        throw kf(l);
    },
    tm = (l) => {
        if (ki(l)) return { penpal: 'synAck', methodNames: l.methodPaths.map(Rf) };
        if (Hf(l)) return { penpal: 'call', id: l.id, methodName: Rf(l.methodPath), args: l.args };
        if (Mf(l))
            if (l.isError)
                return {
                    penpal: 'reply',
                    id: l.callId,
                    resolution: 'rejected',
                    ...(l.isSerializedErrorInstance
                        ? { returnValue: l.value, returnValueIsError: !0 }
                        : { returnValue: l.value }),
                };
            else
                return {
                    penpal: 'reply',
                    id: l.callId,
                    resolution: 'fulfilled',
                    returnValue: l.value,
                };
        throw kf(l);
    },
    im = ({ messenger: l, methods: t, timeout: i, channel: r, log: n }) => {
        let o = Af(),
            b,
            g = [],
            e = !1,
            f = yf(t),
            { promise: h, resolve: c, reject: m } = Ic(),
            u =
                i !== void 0
                    ? setTimeout(() => {
                          m(new _l('CONNECTION_TIMEOUT', `Connection timed out after ${i}ms`));
                      }, i)
                    : void 0,
            J = () => {
                for (let G of g) G();
            },
            I = () => {
                if (e) return;
                g.push(yc(l, t, r, n));
                let { remoteProxy: G, destroy: L } = Cc(l, r, n);
                (g.push(L), clearTimeout(u), (e = !0), c({ remoteProxy: G, destroy: J }));
            },
            R = () => {
                let G = { namespace: ql, type: 'SYN', channel: r, participantId: o };
                n?.('Sending handshake SYN', G);
                try {
                    l.sendMessage(G);
                } catch (L) {
                    m(new _l('TRANSMISSION_FAILED', L.message));
                }
            },
            q = (G) => {
                if ((n?.('Received handshake SYN', G), G.participantId === b && b !== yr)) return;
                if (((b = G.participantId), R(), !(o > b || b === yr))) return;
                let y = { namespace: ql, channel: r, type: 'ACK1', methodPaths: f };
                n?.('Sending handshake ACK1', y);
                try {
                    l.sendMessage(y);
                } catch (T) {
                    m(new _l('TRANSMISSION_FAILED', T.message));
                    return;
                }
            },
            V = (G) => {
                n?.('Received handshake ACK1', G);
                let L = { namespace: ql, channel: r, type: 'ACK2' };
                n?.('Sending handshake ACK2', L);
                try {
                    l.sendMessage(L);
                } catch (y) {
                    m(new _l('TRANSMISSION_FAILED', y.message));
                    return;
                }
                I();
            },
            fl = (G) => {
                (n?.('Received handshake ACK2', G), I());
            },
            K = (G) => {
                if (jt(G)) q(G);
                if (ki(G)) V(G);
                if (ci(G)) fl(G);
            };
        return (l.addMessageHandler(K), g.push(() => l.removeMessageHandler(K)), R(), h);
    },
    rm = im,
    nm = (l) => {
        let t = !1,
            i;
        return (...r) => {
            if (!t) ((t = !0), (i = l(...r)));
            return i;
        };
    },
    om = nm,
    Vf = new WeakSet(),
    bm = ({ messenger: l, methods: t = {}, timeout: i, channel: r, log: n }) => {
        if (!l) throw new _l('INVALID_ARGUMENT', 'messenger must be defined');
        if (Vf.has(l))
            throw new _l(
                'INVALID_ARGUMENT',
                'A messenger can only be used for a single connection',
            );
        Vf.add(l);
        let o = [l.destroy],
            b = om((f) => {
                if (f) {
                    let h = { namespace: ql, channel: r, type: 'DESTROY' };
                    try {
                        l.sendMessage(h);
                    } catch (c) {}
                }
                for (let h of o) h();
                n?.('Connection destroyed');
            }),
            g = (f) => {
                return Vc(f) && f.channel === r;
            };
        return {
            promise: (async () => {
                try {
                    (l.initialize({ log: n, validateReceivedMessage: g }),
                        l.addMessageHandler((c) => {
                            if (Kc(c)) b(!1);
                        }));
                    let { remoteProxy: f, destroy: h } = await rm({
                        messenger: l,
                        methods: t,
                        timeout: i,
                        channel: r,
                        log: n,
                    });
                    return (o.push(h), f);
                } catch (f) {
                    throw (b(!0), f);
                }
            })(),
            destroy: () => {
                b(!0);
            },
        };
    },
    Sf = bm,
    fm = class {
        #l;
        #n;
        #i;
        #t;
        #b;
        #r = new Set();
        #o;
        #f = !1;
        constructor({ remoteWindow: l, allowedOrigins: t }) {
            if (!l) throw new _l('INVALID_ARGUMENT', 'remoteWindow must be defined');
            ((this.#l = l), (this.#n = t?.length ? t : [window.origin]));
        }
        initialize = ({ log: l, validateReceivedMessage: t }) => {
            ((this.#i = l), (this.#t = t), window.addEventListener('message', this.#c));
        };
        sendMessage = (l, t) => {
            if (jt(l)) {
                let i = this.#g(l);
                this.#l.postMessage(l, { targetOrigin: i, transfer: t });
                return;
            }
            if (ki(l) || this.#f) {
                let i = this.#f ? tm(l) : l,
                    r = this.#g(l);
                this.#l.postMessage(i, { targetOrigin: r, transfer: t });
                return;
            }
            if (ci(l)) {
                let { port1: i, port2: r } = new MessageChannel();
                ((this.#o = i), i.addEventListener('message', this.#e), i.start());
                let n = [r, ...(t || [])],
                    o = this.#g(l);
                this.#l.postMessage(l, { targetOrigin: o, transfer: n });
                return;
            }
            if (this.#o) {
                this.#o.postMessage(l, { transfer: t });
                return;
            }
            throw new Yt('Port is undefined');
        };
        addMessageHandler = (l) => {
            this.#r.add(l);
        };
        removeMessageHandler = (l) => {
            this.#r.delete(l);
        };
        destroy = () => {
            (window.removeEventListener('message', this.#c), this.#h(), this.#r.clear());
        };
        #m = (l) => {
            return this.#n.some((t) => (t instanceof RegExp ? t.test(l) : t === l || t === '*'));
        };
        #g = (l) => {
            if (jt(l)) return '*';
            if (!this.#b) throw new Yt('Concrete remote origin not set');
            return this.#b === 'null' && this.#n.includes('*') ? '*' : this.#b;
        };
        #h = () => {
            (this.#o?.removeEventListener('message', this.#e),
                this.#o?.close(),
                (this.#o = void 0));
        };
        #c = ({ source: l, origin: t, ports: i, data: r }) => {
            if (l !== this.#l) return;
            if (Zc(r))
                (this.#i?.('Please upgrade the child window to the latest version of Penpal.'),
                    (this.#f = !0),
                    (r = lm(r)));
            if (!this.#t?.(r)) return;
            if (!this.#m(t)) {
                this.#i?.(
                    `Received a message from origin \`${t}\` which did not match allowed origins \`[${this.#n.join(', ')}]\``,
                );
                return;
            }
            if (jt(r)) (this.#h(), (this.#b = t));
            if (ci(r) && !this.#f) {
                if (((this.#o = i[0]), !this.#o)) throw new Yt('No port received on ACK2');
                (this.#o.addEventListener('message', this.#e), this.#o.start());
            }
            for (let n of this.#r) n(r);
        };
        #e = ({ data: l }) => {
            if (!this.#t?.(l)) return;
            for (let t of this.#r) t(l);
        };
    },
    Nf = fm,
    Cx = class {
        #l;
        #n;
        #i = new Set();
        #t;
        constructor({ worker: l }) {
            if (!l) throw new _l('INVALID_ARGUMENT', 'worker must be defined');
            this.#l = l;
        }
        initialize = ({ validateReceivedMessage: l }) => {
            ((this.#n = l), this.#l.addEventListener('message', this.#r));
        };
        sendMessage = (l, t) => {
            if (jt(l) || ki(l)) {
                this.#l.postMessage(l, { transfer: t });
                return;
            }
            if (ci(l)) {
                let { port1: i, port2: r } = new MessageChannel();
                ((this.#t = i),
                    i.addEventListener('message', this.#r),
                    i.start(),
                    this.#l.postMessage(l, { transfer: [r, ...(t || [])] }));
                return;
            }
            if (this.#t) {
                this.#t.postMessage(l, { transfer: t });
                return;
            }
            throw new Yt('Port is undefined');
        };
        addMessageHandler = (l) => {
            this.#i.add(l);
        };
        removeMessageHandler = (l) => {
            this.#i.delete(l);
        };
        destroy = () => {
            (this.#l.removeEventListener('message', this.#r), this.#b(), this.#i.clear());
        };
        #b = () => {
            (this.#t?.removeEventListener('message', this.#r),
                this.#t?.close(),
                (this.#t = void 0));
        };
        #r = ({ ports: l, data: t }) => {
            if (!this.#n?.(t)) return;
            if (jt(t)) this.#b();
            if (ci(t)) {
                if (((this.#t = l[0]), !this.#t)) throw new Yt('No port received on ACK2');
                (this.#t.addEventListener('message', this.#r), this.#t.start());
            }
            for (let i of this.#i) i(t);
        };
    };
var Px = class {
    #l;
    #n;
    #i = new Set();
    constructor({ port: l }) {
        if (!l) throw new _l('INVALID_ARGUMENT', 'port must be defined');
        this.#l = l;
    }
    initialize = ({ validateReceivedMessage: l }) => {
        ((this.#n = l), this.#l.addEventListener('message', this.#t), this.#l.start());
    };
    sendMessage = (l, t) => {
        this.#l?.postMessage(l, { transfer: t });
    };
    addMessageHandler = (l) => {
        this.#i.add(l);
    };
    removeMessageHandler = (l) => {
        this.#i.delete(l);
    };
    destroy = () => {
        (this.#l.removeEventListener('message', this.#t), this.#l.close(), this.#i.clear());
    };
    #t = ({ data: l }) => {
        if (!this.#n?.(l)) return;
        for (let t of this.#i) t(l);
    };
};
var Cf = ['SCRIPT', 'STYLE', 'LINK', 'META', 'NOSCRIPT'],
    Pf = new Set([
        'a',
        'abbr',
        'area',
        'audio',
        'b',
        'bdi',
        'bdo',
        'br',
        'button',
        'canvas',
        'cite',
        'code',
        'data',
        'datalist',
        'del',
        'dfn',
        'em',
        'embed',
        'h1',
        'h2',
        'h3',
        'h4',
        'h5',
        'h6',
        'i',
        'iframe',
        'img',
        'input',
        'ins',
        'kbd',
        'label',
        'li',
        'map',
        'mark',
        'meter',
        'noscript',
        'object',
        'output',
        'p',
        'picture',
        'progress',
        'q',
        'ruby',
        's',
        'samp',
        'script',
        'select',
        'slot',
        'small',
        'span',
        'strong',
        'sub',
        'sup',
        'svg',
        'template',
        'textarea',
        'time',
        'u',
        'var',
        'video',
        'wbr',
    ]);
var Ar = '.next-prod';
var c5 = {
    SCALE: 0.7,
    PAN_POSITION: { x: 175, y: 100 },
    URL: 'http://localhost:3000/',
    FRAME_POSITION: { x: 0, y: 0 },
    FRAME_DIMENSION: { width: 1536, height: 960 },
    ASPECT_RATIO_LOCKED: !1,
    DEVICE: 'Custom:Custom',
    THEME: 'system',
    ORIENTATION: 'Portrait',
    MIN_DIMENSIONS: { width: '280px', height: '360px' },
    COMMANDS: { run: 'bun run dev', build: 'bun run build', install: 'bun install' },
    IMAGE_FOLDER: 'public',
    IMAGE_DIMENSION: { width: '100px', height: '100px' },
    FONT_FOLDER: 'fonts',
    FONT_CONFIG: 'app/fonts.ts',
    TAILWIND_CONFIG: 'tailwind.config.ts',
    CHAT_SETTINGS: {
        showSuggestions: !0,
        autoApplyCode: !0,
        expandCodeBlocks: !1,
        showMiniChat: !0,
    },
    EDITOR_SETTINGS: { shouldWarnDelete: !1, enableBunReplace: !0, buildFlags: '--no-lint' },
};
var Ur = ['node_modules', 'dist', 'build', '.git', '.next'],
    p5 = [...Ur, 'static', 'out', Ar],
    u5 = [...Ur, Ar],
    z5 = [...Ur, 'coverage'],
    gm = ['.jsx', '.tsx'],
    em = ['.js', '.ts', '.mjs', '.cjs'],
    x5 = [...gm, ...em];
var v5 = { ['en']: 'English', ['ja']: '日本語', ['zh']: '中文', ['ko']: '한국어' };
var df = rf(Mr(), 1);
function H(l) {
    return document.querySelector(`[${'data-odid'}="${l}"]`);
}
function kr(l, t = !1) {
    let i = `[${'data-odid'}="${l}"]`;
    if (!t) return i;
    return hm(i);
}
function hm(l) {
    return CSS.escape(l);
}
function zt(l) {
    return (
        l &&
        l instanceof Node &&
        l.nodeType === Node.ELEMENT_NODE &&
        !Cf.includes(l.tagName) &&
        !l.hasAttribute('data-onlook-ignore') &&
        l.style.display !== 'none'
    );
}
var cm = 'useandom-26T198340PX75pxJACKVERYMINDBUSHWOLF_GQZbfghjklqvwyzrict';
var If = (l = 21) => {
    let t = '',
        i = l | 0;
    while (i--) t += cm[(Math.random() * 64) | 0];
    return t;
};
function sl(l) {
    let t = l.getAttribute('data-odid');
    if (!t) ((t = `odid-${If()}`), l.setAttribute('data-odid', t));
    return t;
}
function Vl(l) {
    return l.getAttribute('data-oid');
}
function Kl(l) {
    return l.getAttribute('data-oiid');
}
function Tf(l, t) {
    if (!vl) return;
    vl.onDomProcessed({ layerMap: Object.fromEntries(l), rootNode: t }).catch((i) => {
        console.error('Failed to send DOM processed event:', i);
    });
}
function Sr(l) {
    window._onlookFrameId = l;
}
function Et() {
    let l = window._onlookFrameId;
    if (!l)
        return (
            console.warn('Frame id not found'),
            vl?.getFrameId().then((t) => {
                Sr(t);
            }),
            ''
        );
    return l;
}
function mm(l = document.body) {
    if (!Et()) return (console.warn('frameView id not found, skipping dom processing'), null);
    let i = zl(l);
    if (!i) return (console.warn('Error building layer tree, root element is null'), null);
    let r = l.getAttribute('data-odid');
    if (!r) return (console.warn('Root dom id not found'), null);
    let n = i.get(r);
    if (!n) return (console.warn('Root node not found'), null);
    return (Tf(i, n), { rootDomId: r, layerMap: Array.from(i.entries()) });
}
var Si = df.default(mm, 500),
    wm = [
        (l) => {
            let t = l.parentElement;
            return t && t.tagName.toLowerCase() === 'svg';
        },
        (l) => {
            return l.tagName.toLowerCase() === 'next-route-announcer';
        },
        (l) => {
            return l.tagName.toLowerCase() === 'nextjs-portal';
        },
    ];
function zl(l) {
    if (!zt(l)) return null;
    let t = new Map(),
        i = document.createTreeWalker(l, NodeFilter.SHOW_ELEMENT, {
            acceptNode: (o) => {
                let b = o;
                if (wm.some((g) => g(b))) return NodeFilter.FILTER_REJECT;
                return zt(b) ? NodeFilter.FILTER_ACCEPT : NodeFilter.FILTER_SKIP;
            },
        }),
        r = Zf(l);
    ((r.children = []), t.set(r.domId, r));
    let n = i.nextNode();
    while (n) {
        let o = Zf(n);
        o.children = [];
        let b = n.parentElement;
        if (b) {
            let g = b.getAttribute('data-odid');
            if (g) {
                o.parent = g;
                let e = t.get(g);
                if (e && e.children) e.children.push(o.domId);
            }
        }
        (t.set(o.domId, o), (n = i.nextNode()));
    }
    return t;
}
function Zf(l) {
    let t = sl(l),
        i = Vl(l),
        r = Kl(l),
        n = Array.from(l.childNodes)
            .map((e) => (e.nodeType === Node.TEXT_NODE ? e.textContent : ''))
            .join(' ')
            .trim()
            .slice(0, 500),
        o = window.getComputedStyle(l),
        b = l.getAttribute('data-ocname');
    return {
        domId: t,
        oid: i || null,
        instanceId: r || null,
        textContent: n || '',
        tagName: l.tagName.toLowerCase(),
        isVisible: o.visibility !== 'hidden',
        component: b || null,
        frameId: Et(),
        children: null,
        parent: null,
        dynamicType: null,
        coreElementType: null,
    };
}
function Nr(l) {
    throw new Error(`Expected \`never\`, found: ${JSON.stringify(l)}`);
}
var lg = (l) => JSON.parse(JSON.stringify(l));
function tg(l) {
    let t = rg(l),
        i = pm(l),
        r = um(l);
    return { defined: { width: 'auto', height: 'auto', ...i, ...r }, computed: t };
}
function ig(l) {
    let t = H(l);
    if (!t) return {};
    return rg(t);
}
function rg(l) {
    return lg(window.getComputedStyle(l));
}
function pm(l) {
    let t = {},
        i = ng(l.style.cssText);
    return (
        Object.entries(i).forEach(([r, n]) => {
            t[r] = n;
        }),
        t
    );
}
function um(l) {
    let t = {},
        i = document.styleSheets;
    for (let r = 0; r < i.length; r++) {
        let n,
            o = i[r];
        try {
            if (!o) {
                console.warn('Sheet is undefined');
                continue;
            }
            n = Array.from(o.cssRules) || o.rules;
        } catch (b) {
            console.warn("Can't read the css rules of: " + o?.href, b);
            continue;
        }
        for (let b = 0; b < n.length; b++)
            try {
                let g = n[b];
                if (g && l.matches(g.selectorText)) {
                    let e = ng(g.style.cssText);
                    Object.entries(e).forEach(([f, h]) => (t[f] = h));
                }
            } catch (g) {
                console.warn('Error', g);
            }
    }
    return t;
}
function ng(l) {
    let t = {};
    return (
        l.split(';').forEach((i) => {
            if (((i = i.trim()), !i)) return;
            let [r, ...n] = i.split(':');
            t[r?.trim() ?? ''] = n.join(':').trim();
        }),
        t
    );
}
var og = (l, t) => {
        let i = document.elementFromPoint(l, t);
        if (!i) return;
        let r = (o) => {
            if (o?.shadowRoot) {
                let b = o.shadowRoot.elementFromPoint(l, t);
                if (b == o) return o;
                else if (b?.shadowRoot) return r(b);
                else return b || o;
            } else return o;
        };
        return r(i) || i;
    },
    nl = (l, t) => {
        let i = l.parentElement,
            r = i
                ? {
                      domId: i.getAttribute('data-odid'),
                      frameId: Et(),
                      oid: i.getAttribute('data-oid'),
                      instanceId: i.getAttribute('data-oiid'),
                      rect: i.getBoundingClientRect(),
                  }
                : null,
            n = l.getBoundingClientRect(),
            o = t ? tg(l) : null;
        return {
            domId: l.getAttribute('data-odid'),
            oid: l.getAttribute('data-oid'),
            frameId: Et(),
            instanceId: l.getAttribute('data-oiid'),
            rect: n,
            tagName: l.tagName,
            parent: r,
            styles: o,
        };
    };
function Ni(l) {
    try {
        let t = l.getAttribute('data-onlook-drag-saved-style');
        if (t) {
            let i = JSON.parse(t);
            for (let r in i) l.style[r] = i[r];
        }
    } catch (t) {
        console.warn('Error restoring style', t);
    }
}
function bg(l) {
    let t = l.parentElement;
    if (!t) return;
    return {
        type: 'index',
        targetDomId: t.getAttribute('data-odid'),
        targetOid: Kl(t) || Vl(t) || null,
        index: Array.from(l.parentElement?.children || []).indexOf(l),
        originalIndex: Array.from(l.parentElement?.children || []).indexOf(l),
    };
}
var fg = (l) => {
    let t = Array.from(l.childNodes)
        .filter((i) => i.nodeType === Node.TEXT_NODE)
        .map((i) => i.textContent);
    if (t.length === 0) return;
    return t.join('');
};
var Ci = (l, t) => {
        let i = H(l) || document.body;
        return nl(i, t);
    },
    gg = (l, t, i) => {
        let r = zm(l, t) || document.body;
        return nl(r, i);
    },
    zm = (l, t) => {
        let i = document.elementFromPoint(l, t);
        if (!i) return;
        let r = (o) => {
            if (o?.shadowRoot) {
                let b = o.shadowRoot.elementFromPoint(l, t);
                if (b == o) return o;
                else if (b?.shadowRoot) return r(b);
                else return b || o;
            } else return o;
        };
        return r(i) || i;
    },
    eg = (l, t, i) => {
        let r = H(l);
        if (!r) {
            console.warn('Failed to updateElementInstanceId: Element not found');
            return;
        }
        (r.setAttribute('data-oiid', t), r.setAttribute('data-ocname', i));
    },
    hg = (l) => {
        let t = H(l);
        if (!t?.parentElement) return null;
        return nl(t.parentElement, !1);
    },
    cg = (l) => {
        let t = H(l);
        if (!t) return 0;
        return t.children.length;
    },
    mg = (l) => {
        let t = H(l);
        if (!t) return null;
        return nl(t.offsetParent, !1);
    };
function wg(l, t, i) {
    let r = H(l.domId);
    if (!r) return (console.warn('Failed to find parent element', l.domId), null);
    let n = xm(t),
        o = new Set(i.map((f) => f.domId)),
        b = Array.from(r.children)
            .map((f, h) => ({ element: f, index: h, domId: sl(f) }))
            .filter(({ domId: f }) => o.has(f));
    if (b.length === 0) return (console.warn('No valid children found to group'), null);
    let g = Math.min(...b.map((f) => f.index));
    return (
        r.insertBefore(n, r.children[g] ?? null),
        b.forEach(({ element: f }) => {
            let h = f.cloneNode(!0);
            (h.setAttribute('data-onlook-inserted', 'true'),
                n.appendChild(h),
                (f.style.display = 'none'),
                ug(f));
        }),
        { domEl: nl(n, !0), newMap: zl(n) }
    );
}
function pg(l, t) {
    let i = H(l.domId);
    if (!i) return (console.warn(`Parent element not found: ${l.domId}`), null);
    let r;
    if (t.domId) r = H(t.domId);
    else return (console.warn('Container domId is required for ungrouping'), null);
    if (!r) return (console.warn('Container element not found for ungrouping'), null);
    return (
        Array.from(r.children).forEach((b) => {
            i.appendChild(b);
        }),
        r.remove(),
        { domEl: nl(i, !0), newMap: zl(i) }
    );
}
function xm(l) {
    let t = document.createElement(l.tagName);
    return (
        Object.entries(l.attributes).forEach(([i, r]) => {
            t.setAttribute(i, r);
        }),
        t.setAttribute('data-onlook-inserted', 'true'),
        t.setAttribute('data-odid', l.domId),
        t.setAttribute('data-oid', l.oid),
        t
    );
}
function ug(l) {
    (l.removeAttribute('data-odid'),
        l.removeAttribute('data-oid'),
        l.removeAttribute('data-onlook-inserted'));
    let t = Array.from(l.children);
    if (t.length === 0) return;
    t.forEach((i) => {
        ug(i);
    });
}
function Pi(l) {
    let t = H(l);
    if (!t) return (console.warn('Element not found for domId:', l), null);
    return zg(t);
}
function zg(l) {
    let t = Array.from(l.attributes).reduce((r, n) => {
            return ((r[n.name] = n.value), r);
        }, {}),
        i = Kl(l) || Vl(l) || null;
    if (!i) return (console.warn('Element has no oid'), null);
    return {
        oid: i,
        domId: sl(l),
        tagName: l.tagName.toLowerCase(),
        children: Array.from(l.children)
            .map((r) => zg(r))
            .filter(Boolean),
        attributes: t,
        textContent: fg(l) || null,
        styles: {},
    };
}
function xg(l) {
    let t = H(l);
    if (!t) throw new Error('Element not found for domId: ' + l);
    let i = t.parentElement;
    if (!i) throw new Error('Inserted element has no parent');
    let r = Kl(i) || Vl(i);
    if (!r) return (console.warn('Parent element has no oid'), null);
    let n = sl(i),
        o = Array.from(i.children).indexOf(t);
    if (o === -1) return { type: 'append', targetDomId: n, targetOid: r };
    return { type: 'index', targetDomId: n, targetOid: r, index: o, originalIndex: o };
}
function ag(l) {
    let t = document.querySelector(`[${'data-odid'}="${l}"]`);
    if (!t)
        return (
            console.warn('No element found', { domId: l }),
            { dynamicType: null, coreType: null }
        );
    let i = t.getAttribute('data-onlook-dynamic-type') || null,
        r = t.getAttribute('data-onlook-core-element-type') || null;
    return { dynamicType: i, coreType: r };
}
function _g(l, t, i) {
    let r = document.querySelector(`[${'data-odid'}="${l}"]`);
    if (r) {
        if (t) r.setAttribute('data-onlook-dynamic-type', t);
        if (i) r.setAttribute('data-onlook-core-element-type', i);
    }
}
function vg() {
    let t = document.body.querySelector(`[${'data-oid'}]`);
    if (t) return nl(t, !0);
    return null;
}
var Yl = 0,
    w = 1,
    _ = 2,
    S = 3,
    F = 4,
    gl = 5,
    Qt = 6,
    tl = 7,
    wl = 8,
    $ = 9,
    v = 10,
    A = 11,
    W = 12,
    Y = 13,
    Pl = 14,
    cl = 15,
    P = 16,
    Z = 17,
    d = 18,
    el = 19,
    pl = 20,
    j = 21,
    a = 22,
    N = 23,
    ml = 24,
    U = 25;
function ol(l) {
    return l >= 48 && l <= 57;
}
function Dl(l) {
    return ol(l) || (l >= 65 && l <= 70) || (l >= 97 && l <= 102);
}
function Zi(l) {
    return l >= 65 && l <= 90;
}
function am(l) {
    return l >= 97 && l <= 122;
}
function _m(l) {
    return Zi(l) || am(l);
}
function vm(l) {
    return l >= 128;
}
function Ti(l) {
    return _m(l) || vm(l) || l === 95;
}
function mi(l) {
    return Ti(l) || ol(l) || l === 45;
}
function Om(l) {
    return (l >= 0 && l <= 8) || l === 11 || (l >= 14 && l <= 31) || l === 127;
}
function wi(l) {
    return l === 10 || l === 13 || l === 12;
}
function Hl(l) {
    return wi(l) || l === 32 || l === 9;
}
function Ol(l, t) {
    if (l !== 92) return !1;
    if (wi(t) || t === 0) return !1;
    return !0;
}
function Gt(l, t, i) {
    if (l === 45) return Ti(t) || t === 45 || Ol(t, i);
    if (Ti(l)) return !0;
    if (l === 92) return Ol(l, t);
    return !1;
}
function di(l, t, i) {
    if (l === 43 || l === 45) {
        if (ol(t)) return 2;
        return t === 46 && ol(i) ? 3 : 0;
    }
    if (l === 46) return ol(t) ? 2 : 0;
    if (ol(l)) return 1;
    return 0;
}
function lr(l) {
    if (l === 65279) return 1;
    if (l === 65534) return 1;
    return 0;
}
var Cr = new Array(128),
    Dm = 128,
    pi = 130,
    Pr = 131,
    tr = 132,
    Ir = 133;
for (let l = 0; l < Cr.length; l++)
    Cr[l] = (Hl(l) && pi) || (ol(l) && Pr) || (Ti(l) && tr) || (Om(l) && Ir) || l || Dm;
function ir(l) {
    return l < 128 ? Cr[l] : tr;
}
function Lt(l, t) {
    return t < l.length ? l.charCodeAt(t) : 0;
}
function rr(l, t, i) {
    if (i === 13 && Lt(l, t + 1) === 10) return 2;
    return 1;
}
function Il(l, t, i) {
    let r = l.charCodeAt(t);
    if (Zi(r)) r = r | 32;
    return r === i;
}
function Tl(l, t, i, r) {
    if (i - t !== r.length) return !1;
    if (t < 0 || i > l.length) return !1;
    for (let n = t; n < i; n++) {
        let o = r.charCodeAt(n - t),
            b = l.charCodeAt(n);
        if (Zi(b)) b = b | 32;
        if (b !== o) return !1;
    }
    return !0;
}
function Og(l, t) {
    for (; t >= 0; t--) if (!Hl(l.charCodeAt(t))) break;
    return t + 1;
}
function ui(l, t) {
    for (; t < l.length; t++) if (!Hl(l.charCodeAt(t))) break;
    return t;
}
function Tr(l, t) {
    for (; t < l.length; t++) if (!ol(l.charCodeAt(t))) break;
    return t;
}
function Ml(l, t) {
    if (((t += 2), Dl(Lt(l, t - 1)))) {
        for (let r = Math.min(l.length, t + 5); t < r; t++) if (!Dl(Lt(l, t))) break;
        let i = Lt(l, t);
        if (Hl(i)) t += rr(l, t, i);
    }
    return t;
}
function zi(l, t) {
    for (; t < l.length; t++) {
        let i = l.charCodeAt(t);
        if (mi(i)) continue;
        if (Ol(i, Lt(l, t + 1))) {
            t = Ml(l, t) - 1;
            continue;
        }
        break;
    }
    return t;
}
function xt(l, t) {
    let i = l.charCodeAt(t);
    if (i === 43 || i === 45) i = l.charCodeAt((t += 1));
    if (ol(i)) ((t = Tr(l, t + 1)), (i = l.charCodeAt(t)));
    if (i === 46 && ol(l.charCodeAt(t + 1))) ((t += 2), (t = Tr(l, t)));
    if (Il(l, t, 101)) {
        let r = 0;
        if (((i = l.charCodeAt(t + 1)), i === 45 || i === 43)) ((r = 1), (i = l.charCodeAt(t + 2)));
        if (ol(i)) t = Tr(l, t + 1 + r + 1);
    }
    return t;
}
function nr(l, t) {
    for (; t < l.length; t++) {
        let i = l.charCodeAt(t);
        if (i === 41) {
            t++;
            break;
        }
        if (Ol(i, Lt(l, t + 1))) t = Ml(l, t);
    }
    return t;
}
function xi(l) {
    if (l.length === 1 && !Dl(l.charCodeAt(0))) return l[0];
    let t = parseInt(l, 16);
    if (t === 0 || (t >= 55296 && t <= 57343) || t > 1114111) t = 65533;
    return String.fromCodePoint(t);
}
var Bt = [
    'EOF-token',
    'ident-token',
    'function-token',
    'at-keyword-token',
    'hash-token',
    'string-token',
    'bad-string-token',
    'url-token',
    'bad-url-token',
    'delim-token',
    'number-token',
    'percentage-token',
    'dimension-token',
    'whitespace-token',
    'CDO-token',
    'CDC-token',
    'colon-token',
    'semicolon-token',
    'comma-token',
    '[-token',
    ']-token',
    '(-token',
    ')-token',
    '{-token',
    '}-token',
    'comment-token',
];
function Ft(l = null, t) {
    if (l === null || l.length < t) return new Uint32Array(Math.max(t + 1024, 16384));
    return l;
}
var Dg = 10,
    $m = 12,
    $g = 13;
function qg(l) {
    let t = l.source,
        i = t.length,
        r = t.length > 0 ? lr(t.charCodeAt(0)) : 0,
        n = Ft(l.lines, i),
        o = Ft(l.columns, i),
        b = l.startLine,
        g = l.startColumn;
    for (let e = r; e < i; e++) {
        let f = t.charCodeAt(e);
        if (((n[e] = b), (o[e] = g++), f === Dg || f === $g || f === $m)) {
            if (f === $g && e + 1 < i && t.charCodeAt(e + 1) === Dg) (e++, (n[e] = b), (o[e] = g));
            (b++, (g = 1));
        }
    }
    ((n[i] = b), (o[i] = g), (l.lines = n), (l.columns = o), (l.computed = !0));
}
class or {
    constructor(l, t, i, r) {
        (this.setSource(l, t, i, r), (this.lines = null), (this.columns = null));
    }
    setSource(l = '', t = 0, i = 1, r = 1) {
        ((this.source = l),
            (this.startOffset = t),
            (this.startLine = i),
            (this.startColumn = r),
            (this.computed = !1));
    }
    getLocation(l, t) {
        if (!this.computed) qg(this);
        return {
            source: t,
            offset: this.startOffset + l,
            line: this.lines[l],
            column: this.columns[l],
        };
    }
    getLocationRange(l, t, i) {
        if (!this.computed) qg(this);
        return {
            source: i,
            start: { offset: this.startOffset + l, line: this.lines[l], column: this.columns[l] },
            end: { offset: this.startOffset + t, line: this.lines[t], column: this.columns[t] },
        };
    }
}
var yl = 16777215,
    Al = 24,
    at = new Uint8Array(32);
at[_] = a;
at[j] = a;
at[el] = pl;
at[N] = ml;
function sg(l) {
    return at[l] !== 0;
}
class br {
    constructor(l, t) {
        this.setSource(l, t);
    }
    reset() {
        ((this.eof = !1),
            (this.tokenIndex = -1),
            (this.tokenType = 0),
            (this.tokenStart = this.firstCharOffset),
            (this.tokenEnd = this.firstCharOffset));
    }
    setSource(l = '', t = () => {}) {
        l = String(l || '');
        let i = l.length,
            r = Ft(this.offsetAndType, l.length + 1),
            n = Ft(this.balance, l.length + 1),
            o = 0,
            b = -1,
            g = 0,
            e = l.length;
        ((this.offsetAndType = null),
            (this.balance = null),
            n.fill(0),
            t(l, (f, h, c) => {
                let m = o++;
                if (((r[m] = (f << Al) | c), b === -1)) b = h;
                if (((n[m] = e), f === g)) {
                    let u = n[e];
                    ((n[e] = m), (e = u), (g = at[r[u] >> Al]));
                } else if (sg(f)) ((e = m), (g = at[f]));
            }),
            (r[o] = (Yl << Al) | i),
            (n[o] = o));
        for (let f = 0; f < o; f++) {
            let h = n[f];
            if (h <= f) {
                let c = n[h];
                if (c !== f) n[f] = c;
            } else if (h > o) n[f] = o;
        }
        ((this.source = l),
            (this.firstCharOffset = b === -1 ? 0 : b),
            (this.tokenCount = o),
            (this.offsetAndType = r),
            (this.balance = n),
            this.reset(),
            this.next());
    }
    lookupType(l) {
        if (((l += this.tokenIndex), l < this.tokenCount)) return this.offsetAndType[l] >> Al;
        return Yl;
    }
    lookupTypeNonSC(l) {
        for (let t = this.tokenIndex; t < this.tokenCount; t++) {
            let i = this.offsetAndType[t] >> Al;
            if (i !== Y && i !== U) {
                if (l-- === 0) return i;
            }
        }
        return Yl;
    }
    lookupOffset(l) {
        if (((l += this.tokenIndex), l < this.tokenCount)) return this.offsetAndType[l - 1] & yl;
        return this.source.length;
    }
    lookupOffsetNonSC(l) {
        for (let t = this.tokenIndex; t < this.tokenCount; t++) {
            let i = this.offsetAndType[t] >> Al;
            if (i !== Y && i !== U) {
                if (l-- === 0) return t - this.tokenIndex;
            }
        }
        return Yl;
    }
    lookupValue(l, t) {
        if (((l += this.tokenIndex), l < this.tokenCount))
            return Tl(this.source, this.offsetAndType[l - 1] & yl, this.offsetAndType[l] & yl, t);
        return !1;
    }
    getTokenStart(l) {
        if (l === this.tokenIndex) return this.tokenStart;
        if (l > 0)
            return l < this.tokenCount
                ? this.offsetAndType[l - 1] & yl
                : this.offsetAndType[this.tokenCount] & yl;
        return this.firstCharOffset;
    }
    substrToCursor(l) {
        return this.source.substring(l, this.tokenStart);
    }
    isBalanceEdge(l) {
        return this.balance[this.tokenIndex] < l;
    }
    isDelim(l, t) {
        if (t)
            return this.lookupType(t) === $ && this.source.charCodeAt(this.lookupOffset(t)) === l;
        return this.tokenType === $ && this.source.charCodeAt(this.tokenStart) === l;
    }
    skip(l) {
        let t = this.tokenIndex + l;
        if (t < this.tokenCount)
            ((this.tokenIndex = t),
                (this.tokenStart = this.offsetAndType[t - 1] & yl),
                (t = this.offsetAndType[t]),
                (this.tokenType = t >> Al),
                (this.tokenEnd = t & yl));
        else ((this.tokenIndex = this.tokenCount), this.next());
    }
    next() {
        let l = this.tokenIndex + 1;
        if (l < this.tokenCount)
            ((this.tokenIndex = l),
                (this.tokenStart = this.tokenEnd),
                (l = this.offsetAndType[l]),
                (this.tokenType = l >> Al),
                (this.tokenEnd = l & yl));
        else
            ((this.eof = !0),
                (this.tokenIndex = this.tokenCount),
                (this.tokenType = Yl),
                (this.tokenStart = this.tokenEnd = this.source.length));
    }
    skipSC() {
        while (this.tokenType === Y || this.tokenType === U) this.next();
    }
    skipUntilBalanced(l, t) {
        let i = l,
            r = 0,
            n = 0;
        l: for (; i < this.tokenCount; i++) {
            if (((r = this.balance[i]), r < l)) break l;
            switch (
                ((n = i > 0 ? this.offsetAndType[i - 1] & yl : this.firstCharOffset),
                t(this.source.charCodeAt(n)))
            ) {
                case 1:
                    break l;
                case 2:
                    i++;
                    break l;
                default:
                    if (sg(this.offsetAndType[i] >> Al)) i = r;
            }
        }
        this.skip(i - this.tokenIndex);
    }
    forEachToken(l) {
        for (let t = 0, i = this.firstCharOffset; t < this.tokenCount; t++) {
            let r = i,
                n = this.offsetAndType[t],
                o = n & yl,
                b = n >> Al;
            ((i = o), l(b, r, o, t));
        }
    }
    dump() {
        let l = new Array(this.tokenCount);
        return (
            this.forEachToken((t, i, r, n) => {
                l[n] = {
                    idx: n,
                    type: Bt[t],
                    chunk: this.source.substring(i, r),
                    balance: this.balance[n],
                };
            }),
            l
        );
    }
}
function bt(l, t) {
    function i(c) {
        return c < g ? l.charCodeAt(c) : 0;
    }
    function r() {
        if (((f = xt(l, f)), Gt(i(f), i(f + 1), i(f + 2)))) {
            ((h = W), (f = zi(l, f)));
            return;
        }
        if (i(f) === 37) {
            ((h = A), f++);
            return;
        }
        h = v;
    }
    function n() {
        let c = f;
        if (((f = zi(l, f)), Tl(l, c, f, 'url') && i(f) === 40)) {
            if (((f = ui(l, f + 1)), i(f) === 34 || i(f) === 39)) {
                ((h = _), (f = c + 4));
                return;
            }
            b();
            return;
        }
        if (i(f) === 40) {
            ((h = _), f++);
            return;
        }
        h = w;
    }
    function o(c) {
        if (!c) c = i(f++);
        h = gl;
        for (; f < l.length; f++) {
            let m = l.charCodeAt(f);
            switch (ir(m)) {
                case c:
                    f++;
                    return;
                case pi:
                    if (wi(m)) {
                        ((f += rr(l, f, m)), (h = Qt));
                        return;
                    }
                    break;
                case 92:
                    if (f === l.length - 1) break;
                    let u = i(f + 1);
                    if (wi(u)) f += rr(l, f + 1, u);
                    else if (Ol(m, u)) f = Ml(l, f) - 1;
                    break;
            }
        }
    }
    function b() {
        ((h = tl), (f = ui(l, f)));
        for (; f < l.length; f++) {
            let c = l.charCodeAt(f);
            switch (ir(c)) {
                case 41:
                    f++;
                    return;
                case pi:
                    if (((f = ui(l, f)), i(f) === 41 || f >= l.length)) {
                        if (f < l.length) f++;
                        return;
                    }
                    ((f = nr(l, f)), (h = wl));
                    return;
                case 34:
                case 39:
                case 40:
                case Ir:
                    ((f = nr(l, f)), (h = wl));
                    return;
                case 92:
                    if (Ol(c, i(f + 1))) {
                        f = Ml(l, f) - 1;
                        break;
                    }
                    ((f = nr(l, f)), (h = wl));
                    return;
            }
        }
    }
    l = String(l || '');
    let g = l.length,
        e = lr(i(0)),
        f = e,
        h;
    while (f < g) {
        let c = l.charCodeAt(f);
        switch (ir(c)) {
            case pi:
                ((h = Y), (f = ui(l, f + 1)));
                break;
            case 34:
                o();
                break;
            case 35:
                if (mi(i(f + 1)) || Ol(i(f + 1), i(f + 2))) ((h = F), (f = zi(l, f + 1)));
                else ((h = $), f++);
                break;
            case 39:
                o();
                break;
            case 40:
                ((h = j), f++);
                break;
            case 41:
                ((h = a), f++);
                break;
            case 43:
                if (di(c, i(f + 1), i(f + 2))) r();
                else ((h = $), f++);
                break;
            case 44:
                ((h = d), f++);
                break;
            case 45:
                if (di(c, i(f + 1), i(f + 2))) r();
                else if (i(f + 1) === 45 && i(f + 2) === 62) ((h = cl), (f = f + 3));
                else if (Gt(c, i(f + 1), i(f + 2))) n();
                else ((h = $), f++);
                break;
            case 46:
                if (di(c, i(f + 1), i(f + 2))) r();
                else ((h = $), f++);
                break;
            case 47:
                if (i(f + 1) === 42)
                    ((h = U), (f = l.indexOf('*/', f + 2)), (f = f === -1 ? l.length : f + 2));
                else ((h = $), f++);
                break;
            case 58:
                ((h = P), f++);
                break;
            case 59:
                ((h = Z), f++);
                break;
            case 60:
                if (i(f + 1) === 33 && i(f + 2) === 45 && i(f + 3) === 45) ((h = Pl), (f = f + 4));
                else ((h = $), f++);
                break;
            case 64:
                if (Gt(i(f + 1), i(f + 2), i(f + 3))) ((h = S), (f = zi(l, f + 1)));
                else ((h = $), f++);
                break;
            case 91:
                ((h = el), f++);
                break;
            case 92:
                if (Ol(c, i(f + 1))) n();
                else ((h = $), f++);
                break;
            case 93:
                ((h = pl), f++);
                break;
            case 123:
                ((h = N), f++);
                break;
            case 125:
                ((h = ml), f++);
                break;
            case Pr:
                r();
                break;
            case tr:
                n();
                break;
            default:
                ((h = $), f++);
        }
        t(h, e, (e = f));
    }
}
var Rt = null;
class ll {
    static createItem(l) {
        return { prev: null, next: null, data: l };
    }
    constructor() {
        ((this.head = null), (this.tail = null), (this.cursor = null));
    }
    createItem(l) {
        return ll.createItem(l);
    }
    allocateCursor(l, t) {
        let i;
        if (Rt !== null)
            ((i = Rt), (Rt = Rt.cursor), (i.prev = l), (i.next = t), (i.cursor = this.cursor));
        else i = { prev: l, next: t, cursor: this.cursor };
        return ((this.cursor = i), i);
    }
    releaseCursor() {
        let { cursor: l } = this;
        ((this.cursor = l.cursor), (l.prev = null), (l.next = null), (l.cursor = Rt), (Rt = l));
    }
    updateCursors(l, t, i, r) {
        let { cursor: n } = this;
        while (n !== null) {
            if (n.prev === l) n.prev = t;
            if (n.next === i) n.next = r;
            n = n.cursor;
        }
    }
    *[Symbol.iterator]() {
        for (let l = this.head; l !== null; l = l.next) yield l.data;
    }
    get size() {
        let l = 0;
        for (let t = this.head; t !== null; t = t.next) l++;
        return l;
    }
    get isEmpty() {
        return this.head === null;
    }
    get first() {
        return this.head && this.head.data;
    }
    get last() {
        return this.tail && this.tail.data;
    }
    fromArray(l) {
        let t = null;
        this.head = null;
        for (let i of l) {
            let r = ll.createItem(i);
            if (t !== null) t.next = r;
            else this.head = r;
            ((r.prev = t), (t = r));
        }
        return ((this.tail = t), this);
    }
    toArray() {
        return [...this];
    }
    toJSON() {
        return [...this];
    }
    forEach(l, t = this) {
        let i = this.allocateCursor(null, this.head);
        while (i.next !== null) {
            let r = i.next;
            ((i.next = r.next), l.call(t, r.data, r, this));
        }
        this.releaseCursor();
    }
    forEachRight(l, t = this) {
        let i = this.allocateCursor(this.tail, null);
        while (i.prev !== null) {
            let r = i.prev;
            ((i.prev = r.prev), l.call(t, r.data, r, this));
        }
        this.releaseCursor();
    }
    reduce(l, t, i = this) {
        let r = this.allocateCursor(null, this.head),
            n = t,
            o;
        while (r.next !== null)
            ((o = r.next), (r.next = o.next), (n = l.call(i, n, o.data, o, this)));
        return (this.releaseCursor(), n);
    }
    reduceRight(l, t, i = this) {
        let r = this.allocateCursor(this.tail, null),
            n = t,
            o;
        while (r.prev !== null)
            ((o = r.prev), (r.prev = o.prev), (n = l.call(i, n, o.data, o, this)));
        return (this.releaseCursor(), n);
    }
    some(l, t = this) {
        for (let i = this.head; i !== null; i = i.next) if (l.call(t, i.data, i, this)) return !0;
        return !1;
    }
    map(l, t = this) {
        let i = new ll();
        for (let r = this.head; r !== null; r = r.next) i.appendData(l.call(t, r.data, r, this));
        return i;
    }
    filter(l, t = this) {
        let i = new ll();
        for (let r = this.head; r !== null; r = r.next)
            if (l.call(t, r.data, r, this)) i.appendData(r.data);
        return i;
    }
    nextUntil(l, t, i = this) {
        if (l === null) return;
        let r = this.allocateCursor(null, l);
        while (r.next !== null) {
            let n = r.next;
            if (((r.next = n.next), t.call(i, n.data, n, this))) break;
        }
        this.releaseCursor();
    }
    prevUntil(l, t, i = this) {
        if (l === null) return;
        let r = this.allocateCursor(l, null);
        while (r.prev !== null) {
            let n = r.prev;
            if (((r.prev = n.prev), t.call(i, n.data, n, this))) break;
        }
        this.releaseCursor();
    }
    clear() {
        ((this.head = null), (this.tail = null));
    }
    copy() {
        let l = new ll();
        for (let t of this) l.appendData(t);
        return l;
    }
    prepend(l) {
        if ((this.updateCursors(null, l, this.head, l), this.head !== null))
            ((this.head.prev = l), (l.next = this.head));
        else this.tail = l;
        return ((this.head = l), this);
    }
    prependData(l) {
        return this.prepend(ll.createItem(l));
    }
    append(l) {
        return this.insert(l);
    }
    appendData(l) {
        return this.insert(ll.createItem(l));
    }
    insert(l, t = null) {
        if (t !== null)
            if ((this.updateCursors(t.prev, l, t, l), t.prev === null)) {
                if (this.head !== t) throw new Error("before doesn't belong to list");
                ((this.head = l), (t.prev = l), (l.next = t), this.updateCursors(null, l));
            } else ((t.prev.next = l), (l.prev = t.prev), (t.prev = l), (l.next = t));
        else {
            if ((this.updateCursors(this.tail, l, null, l), this.tail !== null))
                ((this.tail.next = l), (l.prev = this.tail));
            else this.head = l;
            this.tail = l;
        }
        return this;
    }
    insertData(l, t) {
        return this.insert(ll.createItem(l), t);
    }
    remove(l) {
        if ((this.updateCursors(l, l.prev, l, l.next), l.prev !== null)) l.prev.next = l.next;
        else {
            if (this.head !== l) throw new Error("item doesn't belong to list");
            this.head = l.next;
        }
        if (l.next !== null) l.next.prev = l.prev;
        else {
            if (this.tail !== l) throw new Error("item doesn't belong to list");
            this.tail = l.prev;
        }
        return ((l.prev = null), (l.next = null), l);
    }
    push(l) {
        this.insert(ll.createItem(l));
    }
    pop() {
        return this.tail !== null ? this.remove(this.tail) : null;
    }
    unshift(l) {
        this.prepend(ll.createItem(l));
    }
    shift() {
        return this.head !== null ? this.remove(this.head) : null;
    }
    prependList(l) {
        return this.insertList(l, this.head);
    }
    appendList(l) {
        return this.insertList(l);
    }
    insertList(l, t) {
        if (l.head === null) return this;
        if (t !== void 0 && t !== null) {
            if ((this.updateCursors(t.prev, l.tail, t, l.head), t.prev !== null))
                ((t.prev.next = l.head), (l.head.prev = t.prev));
            else this.head = l.head;
            ((t.prev = l.tail), (l.tail.next = t));
        } else {
            if ((this.updateCursors(this.tail, l.tail, null, l.head), this.tail !== null))
                ((this.tail.next = l.head), (l.head.prev = this.tail));
            else this.head = l.head;
            this.tail = l.tail;
        }
        return ((l.head = null), (l.tail = null), this);
    }
    replace(l, t) {
        if ('head' in t) this.insertList(t, l);
        else this.insert(t, l);
        this.remove(l);
    }
}
function _t(l, t) {
    let i = Object.create(SyntaxError.prototype),
        r = new Error();
    return Object.assign(i, {
        name: l,
        message: t,
        get stack() {
            return (r.stack || '').replace(
                /^(.+\n){1,3}/,
                `${l}: ${t}
`,
            );
        },
    });
}
var Zr = 100,
    Jg = 60,
    Xg = '    ';
function Wg({ source: l, line: t, column: i, baseLine: r, baseColumn: n }, o) {
    function b(J, I) {
        return f.slice(J, I).map((R, q) => String(J + q + 1).padStart(m) + ' |' + R).join(`
`);
    }
    let g = `
`.repeat(Math.max(r - 1, 0)),
        e = ' '.repeat(Math.max(n - 1, 0)),
        f = (g + e + l).split(/\r\n?|\n|\f/),
        h = Math.max(1, t - o) - 1,
        c = Math.min(t + o, f.length + 1),
        m = Math.max(4, String(c).length) + 1,
        u = 0;
    if (((i += (Xg.length - 1) * (f[t - 1].substr(0, i - 1).match(/\t/g) || []).length), i > Zr))
        ((u = i - Jg + 3), (i = Jg - 2));
    for (let J = h; J <= c; J++)
        if (J >= 0 && J < f.length)
            ((f[J] = f[J].replace(/\t/g, Xg)),
                (f[J] =
                    (u > 0 && f[J].length > u ? '…' : '') +
                    f[J].substr(u, Zr - 2) +
                    (f[J].length > u + Zr - 1 ? '…' : '')));
    return [b(h, t), new Array(i + m + 2).join('-') + '^', b(t, c)]
        .filter(Boolean)
        .join(
            `
`,
        )
        .replace(/^(\s+\d+\s+\|\n)+/, '')
        .replace(/\n(\s+\d+\s+\|)+$/, '');
}
function dr(l, t, i, r, n, o = 1, b = 1) {
    return Object.assign(_t('SyntaxError', l), {
        source: t,
        offset: i,
        line: r,
        column: n,
        sourceFragment(e) {
            return Wg(
                { source: t, line: r, column: n, baseLine: o, baseColumn: b },
                isNaN(e) ? 0 : e,
            );
        },
        get formattedMessage() {
            return (
                `Parse error: ${l}
` + Wg({ source: t, line: r, column: n, baseLine: o, baseColumn: b }, 2)
            );
        },
    });
}
function jg(l) {
    let t = this.createList(),
        i = !1,
        r = { recognizer: l };
    while (!this.eof) {
        switch (this.tokenType) {
            case U:
                this.next();
                continue;
            case Y:
                ((i = !0), this.next());
                continue;
        }
        let n = l.getNode.call(this, r);
        if (n === void 0) break;
        if (i) {
            if (l.onWhiteSpace) l.onWhiteSpace.call(this, n, t, r);
            i = !1;
        }
        t.push(n);
    }
    if (i && l.onWhiteSpace) l.onWhiteSpace.call(this, null, t, r);
    return t;
}
var Yg = () => {},
    qm = 33,
    sm = 35,
    ln = 59,
    Eg = 123,
    Qg = 0;
function Jm(l) {
    return function () {
        return this[l]();
    };
}
function tn(l) {
    let t = Object.create(null);
    for (let i of Object.keys(l)) {
        let r = l[i],
            n = r.parse || r;
        if (n) t[i] = n;
    }
    return t;
}
function Xm(l) {
    let t = {
        context: Object.create(null),
        features: Object.assign(Object.create(null), l.features),
        scope: Object.assign(Object.create(null), l.scope),
        atrule: tn(l.atrule),
        pseudo: tn(l.pseudo),
        node: tn(l.node),
    };
    for (let [i, r] of Object.entries(l.parseContext))
        switch (typeof r) {
            case 'function':
                t.context[i] = r;
                break;
            case 'string':
                t.context[i] = Jm(r);
                break;
        }
    return { config: t, ...t, ...t.node };
}
function Gg(l) {
    let t = '',
        i = '<unknown>',
        r = !1,
        n = Yg,
        o = !1,
        b = new or(),
        g = Object.assign(new br(), Xm(l || {}), {
            parseAtrulePrelude: !0,
            parseRulePrelude: !0,
            parseValue: !0,
            parseCustomProperty: !1,
            readSequence: jg,
            consumeUntilBalanceEnd: () => 0,
            consumeUntilLeftCurlyBracket(f) {
                return f === Eg ? 1 : 0;
            },
            consumeUntilLeftCurlyBracketOrSemicolon(f) {
                return f === Eg || f === ln ? 1 : 0;
            },
            consumeUntilExclamationMarkOrSemicolon(f) {
                return f === qm || f === ln ? 1 : 0;
            },
            consumeUntilSemicolonIncluded(f) {
                return f === ln ? 2 : 0;
            },
            createList() {
                return new ll();
            },
            createSingleNodeList(f) {
                return new ll().appendData(f);
            },
            getFirstListNode(f) {
                return f && f.first;
            },
            getLastListNode(f) {
                return f && f.last;
            },
            parseWithFallback(f, h) {
                let c = this.tokenIndex;
                try {
                    return f.call(this);
                } catch (m) {
                    if (o) throw m;
                    this.skip(c - this.tokenIndex);
                    let u = h.call(this);
                    return ((o = !0), n(m, u), (o = !1), u);
                }
            },
            lookupNonWSType(f) {
                let h;
                do if (((h = this.lookupType(f++)), h !== Y && h !== U)) return h;
                while (h !== Qg);
                return Qg;
            },
            charCodeAt(f) {
                return f >= 0 && f < t.length ? t.charCodeAt(f) : 0;
            },
            substring(f, h) {
                return t.substring(f, h);
            },
            substrToCursor(f) {
                return this.source.substring(f, this.tokenStart);
            },
            cmpChar(f, h) {
                return Il(t, f, h);
            },
            cmpStr(f, h, c) {
                return Tl(t, f, h, c);
            },
            consume(f) {
                let h = this.tokenStart;
                return (this.eat(f), this.substrToCursor(h));
            },
            consumeFunctionName() {
                let f = t.substring(this.tokenStart, this.tokenEnd - 1);
                return (this.eat(_), f);
            },
            consumeNumber(f) {
                let h = t.substring(this.tokenStart, xt(t, this.tokenStart));
                return (this.eat(f), h);
            },
            eat(f) {
                if (this.tokenType !== f) {
                    let h = Bt[f]
                            .slice(0, -6)
                            .replace(/-/g, ' ')
                            .replace(/^./, (u) => u.toUpperCase()),
                        c = `${/[[\](){}]/.test(h) ? `"${h}"` : h} is expected`,
                        m = this.tokenStart;
                    switch (f) {
                        case w:
                            if (this.tokenType === _ || this.tokenType === tl)
                                ((m = this.tokenEnd - 1),
                                    (c = 'Identifier is expected but function found'));
                            else c = 'Identifier is expected';
                            break;
                        case F:
                            if (this.isDelim(sm)) (this.next(), m++, (c = 'Name is expected'));
                            break;
                        case A:
                            if (this.tokenType === v)
                                ((m = this.tokenEnd), (c = 'Percent sign is expected'));
                            break;
                    }
                    this.error(c, m);
                }
                this.next();
            },
            eatIdent(f) {
                if (this.tokenType !== w || this.lookupValue(0, f) === !1)
                    this.error(`Identifier "${f}" is expected`);
                this.next();
            },
            eatDelim(f) {
                if (!this.isDelim(f)) this.error(`Delim "${String.fromCharCode(f)}" is expected`);
                this.next();
            },
            getLocation(f, h) {
                if (r) return b.getLocationRange(f, h, i);
                return null;
            },
            getLocationFromList(f) {
                if (r) {
                    let h = this.getFirstListNode(f),
                        c = this.getLastListNode(f);
                    return b.getLocationRange(
                        h !== null ? h.loc.start.offset - b.startOffset : this.tokenStart,
                        c !== null ? c.loc.end.offset - b.startOffset : this.tokenStart,
                        i,
                    );
                }
                return null;
            },
            error(f, h) {
                let c =
                    typeof h !== 'undefined' && h < t.length
                        ? b.getLocation(h)
                        : this.eof
                          ? b.getLocation(Og(t, t.length - 1))
                          : b.getLocation(this.tokenStart);
                throw new dr(
                    f || 'Unexpected input',
                    t,
                    c.offset,
                    c.line,
                    c.column,
                    b.startLine,
                    b.startColumn,
                );
            },
        });
    return Object.assign(
        function (f, h) {
            ((t = f),
                (h = h || {}),
                g.setSource(t, bt),
                b.setSource(t, h.offset, h.line, h.column),
                (i = h.filename || '<unknown>'),
                (r = Boolean(h.positions)),
                (n = typeof h.onParseError === 'function' ? h.onParseError : Yg),
                (o = !1),
                (g.parseAtrulePrelude =
                    'parseAtrulePrelude' in h ? Boolean(h.parseAtrulePrelude) : !0),
                (g.parseRulePrelude = 'parseRulePrelude' in h ? Boolean(h.parseRulePrelude) : !0),
                (g.parseValue = 'parseValue' in h ? Boolean(h.parseValue) : !0),
                (g.parseCustomProperty =
                    'parseCustomProperty' in h ? Boolean(h.parseCustomProperty) : !1));
            let { context: c = 'default', onComment: m } = h;
            if (c in g.context === !1) throw new Error('Unknown context `' + c + '`');
            if (typeof m === 'function')
                g.forEachToken((J, I, R) => {
                    if (J === U) {
                        let q = g.getLocation(I, R),
                            V = Tl(t, R - 2, R, '*/') ? t.slice(I + 2, R - 2) : t.slice(I + 2, R);
                        m(V, q);
                    }
                });
            let u = g.context[c].call(g, h);
            if (!g.eof) g.error();
            return u;
        },
        { SyntaxError: dr, config: g.config },
    );
}
var _i = Hg(),
    bl = fr(),
    er = Sg().ArraySet,
    aw = Cg().MappingList;
function Wl(l) {
    if (!l) l = {};
    ((this._file = bl.getArg(l, 'file', null)),
        (this._sourceRoot = bl.getArg(l, 'sourceRoot', null)),
        (this._skipValidation = bl.getArg(l, 'skipValidation', !1)),
        (this._ignoreInvalidMapping = bl.getArg(l, 'ignoreInvalidMapping', !1)),
        (this._sources = new er()),
        (this._names = new er()),
        (this._mappings = new aw()),
        (this._sourcesContents = null));
}
Wl.prototype._version = 3;
Wl.fromSourceMap = function l(t, i) {
    var r = t.sourceRoot,
        n = new Wl(Object.assign(i || {}, { file: t.file, sourceRoot: r }));
    return (
        t.eachMapping(function (o) {
            var b = { generated: { line: o.generatedLine, column: o.generatedColumn } };
            if (o.source != null) {
                if (((b.source = o.source), r != null)) b.source = bl.relative(r, b.source);
                if (
                    ((b.original = { line: o.originalLine, column: o.originalColumn }),
                    o.name != null)
                )
                    b.name = o.name;
            }
            n.addMapping(b);
        }),
        t.sources.forEach(function (o) {
            var b = o;
            if (r !== null) b = bl.relative(r, o);
            if (!n._sources.has(b)) n._sources.add(b);
            var g = t.sourceContentFor(o);
            if (g != null) n.setSourceContent(o, g);
        }),
        n
    );
};
Wl.prototype.addMapping = function l(t) {
    var i = bl.getArg(t, 'generated'),
        r = bl.getArg(t, 'original', null),
        n = bl.getArg(t, 'source', null),
        o = bl.getArg(t, 'name', null);
    if (!this._skipValidation) {
        if (this._validateMapping(i, r, n, o) === !1) return;
    }
    if (n != null) {
        if (((n = String(n)), !this._sources.has(n))) this._sources.add(n);
    }
    if (o != null) {
        if (((o = String(o)), !this._names.has(o))) this._names.add(o);
    }
    this._mappings.add({
        generatedLine: i.line,
        generatedColumn: i.column,
        originalLine: r != null && r.line,
        originalColumn: r != null && r.column,
        source: n,
        name: o,
    });
};
Wl.prototype.setSourceContent = function l(t, i) {
    var r = t;
    if (this._sourceRoot != null) r = bl.relative(this._sourceRoot, r);
    if (i != null) {
        if (!this._sourcesContents) this._sourcesContents = Object.create(null);
        this._sourcesContents[bl.toSetString(r)] = i;
    } else if (this._sourcesContents) {
        if (
            (delete this._sourcesContents[bl.toSetString(r)],
            Object.keys(this._sourcesContents).length === 0)
        )
            this._sourcesContents = null;
    }
};
Wl.prototype.applySourceMap = function l(t, i, r) {
    var n = i;
    if (i == null) {
        if (t.file == null)
            throw new Error(
                `SourceMapGenerator.prototype.applySourceMap requires either an explicit source file, or the source map's "file" property. Both were omitted.`,
            );
        n = t.file;
    }
    var o = this._sourceRoot;
    if (o != null) n = bl.relative(o, n);
    var b = new er(),
        g = new er();
    (this._mappings.unsortedForEach(function (e) {
        if (e.source === n && e.originalLine != null) {
            var f = t.originalPositionFor({ line: e.originalLine, column: e.originalColumn });
            if (f.source != null) {
                if (((e.source = f.source), r != null)) e.source = bl.join(r, e.source);
                if (o != null) e.source = bl.relative(o, e.source);
                if (((e.originalLine = f.line), (e.originalColumn = f.column), f.name != null))
                    e.name = f.name;
            }
        }
        var h = e.source;
        if (h != null && !b.has(h)) b.add(h);
        var c = e.name;
        if (c != null && !g.has(c)) g.add(c);
    }, this),
        (this._sources = b),
        (this._names = g),
        t.sources.forEach(function (e) {
            var f = t.sourceContentFor(e);
            if (f != null) {
                if (r != null) e = bl.join(r, e);
                if (o != null) e = bl.relative(o, e);
                this.setSourceContent(e, f);
            }
        }, this));
};
Wl.prototype._validateMapping = function l(t, i, r, n) {
    if (i && typeof i.line !== 'number' && typeof i.column !== 'number') {
        var o =
            'original.line and original.column are not numbers -- you probably meant to omit the original mapping entirely and only map the generated position. If so, pass null for the original mapping instead of an object with empty or null values.';
        if (this._ignoreInvalidMapping) {
            if (typeof console !== 'undefined' && console.warn) console.warn(o);
            return !1;
        } else throw new Error(o);
    }
    if (t && 'line' in t && 'column' in t && t.line > 0 && t.column >= 0 && !i && !r && !n) return;
    else if (
        t &&
        'line' in t &&
        'column' in t &&
        i &&
        'line' in i &&
        'column' in i &&
        t.line > 0 &&
        t.column >= 0 &&
        i.line > 0 &&
        i.column >= 0 &&
        r
    )
        return;
    else {
        var o =
            'Invalid mapping: ' + JSON.stringify({ generated: t, source: r, original: i, name: n });
        if (this._ignoreInvalidMapping) {
            if (typeof console !== 'undefined' && console.warn) console.warn(o);
            return !1;
        } else throw new Error(o);
    }
};
Wl.prototype._serializeMappings = function l() {
    var t = 0,
        i = 1,
        r = 0,
        n = 0,
        o = 0,
        b = 0,
        g = '',
        e,
        f,
        h,
        c,
        m = this._mappings.toArray();
    for (var u = 0, J = m.length; u < J; u++) {
        if (((f = m[u]), (e = ''), f.generatedLine !== i)) {
            t = 0;
            while (f.generatedLine !== i) ((e += ';'), i++);
        } else if (u > 0) {
            if (!bl.compareByGeneratedPositionsInflated(f, m[u - 1])) continue;
            e += ',';
        }
        if (((e += _i.encode(f.generatedColumn - t)), (t = f.generatedColumn), f.source != null)) {
            if (
                ((c = this._sources.indexOf(f.source)),
                (e += _i.encode(c - b)),
                (b = c),
                (e += _i.encode(f.originalLine - 1 - n)),
                (n = f.originalLine - 1),
                (e += _i.encode(f.originalColumn - r)),
                (r = f.originalColumn),
                f.name != null)
            )
                ((h = this._names.indexOf(f.name)), (e += _i.encode(h - o)), (o = h));
        }
        g += e;
    }
    return g;
};
Wl.prototype._generateSourcesContent = function l(t, i) {
    return t.map(function (r) {
        if (!this._sourcesContents) return null;
        if (i != null) r = bl.relative(i, r);
        var n = bl.toSetString(r);
        return Object.prototype.hasOwnProperty.call(this._sourcesContents, n)
            ? this._sourcesContents[n]
            : null;
    }, this);
};
Wl.prototype.toJSON = function l() {
    var t = {
        version: this._version,
        sources: this._sources.toArray(),
        names: this._names.toArray(),
        mappings: this._serializeMappings(),
    };
    if (this._file != null) t.file = this._file;
    if (this._sourceRoot != null) t.sourceRoot = this._sourceRoot;
    if (this._sourcesContents)
        t.sourcesContent = this._generateSourcesContent(t.sources, t.sourceRoot);
    return t;
};
Wl.prototype.toString = function l() {
    return JSON.stringify(this.toJSON());
};
var fn = Wl;
var Pg = new Set(['Atrule', 'Selector', 'Declaration']);
function Ig(l) {
    let t = new fn(),
        i = { line: 1, column: 0 },
        r = { line: 0, column: 0 },
        n = { line: 1, column: 0 },
        o = { generated: n },
        b = 1,
        g = 0,
        e = !1,
        f = l.node;
    l.node = function (m) {
        if (m.loc && m.loc.start && Pg.has(m.type)) {
            let u = m.loc.start.line,
                J = m.loc.start.column - 1;
            if (r.line !== u || r.column !== J) {
                if (((r.line = u), (r.column = J), (i.line = b), (i.column = g), e)) {
                    if (((e = !1), i.line !== n.line || i.column !== n.column)) t.addMapping(o);
                }
                ((e = !0), t.addMapping({ source: m.loc.source, original: r, generated: i }));
            }
        }
        if ((f.call(this, m), e && Pg.has(m.type))) ((n.line = b), (n.column = g));
    };
    let h = l.emit;
    l.emit = function (m, u, J) {
        for (let I = 0; I < m.length; I++)
            if (m.charCodeAt(I) === 10) (b++, (g = 0));
            else g++;
        h(m, u, J);
    };
    let c = l.result;
    return (
        (l.result = function () {
            if (e) t.addMapping(o);
            return { css: c(), map: t };
        }),
        l
    );
}
var hr = {};
D(hr, { spec: () => Dw, safe: () => en });
var _w = 43,
    vw = 45,
    gn = (l, t) => {
        if (l === $) l = t;
        if (typeof l === 'string') {
            let i = l.charCodeAt(0);
            return i > 127 ? 32768 : i << 8;
        }
        return l;
    },
    Tg = [
        [w, w],
        [w, _],
        [w, tl],
        [w, wl],
        [w, '-'],
        [w, v],
        [w, A],
        [w, W],
        [w, cl],
        [w, j],
        [S, w],
        [S, _],
        [S, tl],
        [S, wl],
        [S, '-'],
        [S, v],
        [S, A],
        [S, W],
        [S, cl],
        [F, w],
        [F, _],
        [F, tl],
        [F, wl],
        [F, '-'],
        [F, v],
        [F, A],
        [F, W],
        [F, cl],
        [W, w],
        [W, _],
        [W, tl],
        [W, wl],
        [W, '-'],
        [W, v],
        [W, A],
        [W, W],
        [W, cl],
        ['#', w],
        ['#', _],
        ['#', tl],
        ['#', wl],
        ['#', '-'],
        ['#', v],
        ['#', A],
        ['#', W],
        ['#', cl],
        ['-', w],
        ['-', _],
        ['-', tl],
        ['-', wl],
        ['-', '-'],
        ['-', v],
        ['-', A],
        ['-', W],
        ['-', cl],
        [v, w],
        [v, _],
        [v, tl],
        [v, wl],
        [v, v],
        [v, A],
        [v, W],
        [v, '%'],
        [v, cl],
        ['@', w],
        ['@', _],
        ['@', tl],
        ['@', wl],
        ['@', '-'],
        ['@', cl],
        ['.', v],
        ['.', A],
        ['.', W],
        ['+', v],
        ['+', A],
        ['+', W],
        ['/', '*'],
    ],
    Ow = Tg.concat([
        [w, F],
        [W, F],
        [F, F],
        [S, j],
        [S, gl],
        [S, P],
        [A, A],
        [A, W],
        [A, _],
        [A, '-'],
        [a, w],
        [a, _],
        [a, A],
        [a, W],
        [a, F],
        [a, '-'],
    ]);
function Zg(l) {
    let t = new Set(l.map(([i, r]) => (gn(i) << 16) | gn(r)));
    return function (i, r, n) {
        let o = gn(r, n),
            b = n.charCodeAt(0);
        if (
            (b === vw && r !== w && r !== _ && r !== cl) || b === _w
                ? t.has((i << 16) | (b << 8))
                : t.has((i << 16) | o)
        )
            this.emit(' ', Y, !0);
        return o;
    };
}
var Dw = Zg(Tg),
    en = Zg(Ow);
var $w = 92;
function qw(l, t) {
    if (typeof t === 'function') {
        let i = null;
        l.children.forEach((r) => {
            if (i !== null) t.call(this, i);
            (this.node(r), (i = r));
        });
        return;
    }
    l.children.forEach(this.node, this);
}
function sw(l) {
    bt(l, (t, i, r) => {
        this.token(t, l.slice(i, r));
    });
}
function dg(l) {
    let t = new Map();
    for (let [i, r] of Object.entries(l.node))
        if (typeof (r.generate || r) === 'function') t.set(i, r.generate || r);
    return function (i, r) {
        let n = '',
            o = 0,
            b = {
                node(e) {
                    if (t.has(e.type)) t.get(e.type).call(g, e);
                    else throw new Error('Unknown node type: ' + e.type);
                },
                tokenBefore: en,
                token(e, f) {
                    if (
                        ((o = this.tokenBefore(o, e, f)),
                        this.emit(f, e, !1),
                        e === $ && f.charCodeAt(0) === $w)
                    )
                        this.emit(
                            `
`,
                            Y,
                            !0,
                        );
                },
                emit(e) {
                    n += e;
                },
                result() {
                    return n;
                },
            };
        if (r) {
            if (typeof r.decorator === 'function') b = r.decorator(b);
            if (r.sourceMap) b = Ig(b);
            if (r.mode in hr) b.tokenBefore = hr[r.mode];
        }
        let g = {
            node: (e) => b.node(e),
            children: qw,
            token: (e, f) => b.token(e, f),
            tokenize: sw,
        };
        return (b.node(i), b.result());
    };
}
function le(l) {
    return {
        fromPlainObject(t) {
            return (
                l(t, {
                    enter(i) {
                        if (i.children && i.children instanceof ll === !1)
                            i.children = new ll().fromArray(i.children);
                    },
                }),
                t
            );
        },
        toPlainObject(t) {
            return (
                l(t, {
                    leave(i) {
                        if (i.children && i.children instanceof ll)
                            i.children = i.children.toArray();
                    },
                }),
                t
            );
        },
    };
}
var { hasOwnProperty: hn } = Object.prototype,
    vi = function () {};
function te(l) {
    return typeof l === 'function' ? l : vi;
}
function ie(l, t) {
    return function (i, r, n) {
        if (i.type === t) l.call(this, i, r, n);
    };
}
function Jw(l, t) {
    let i = t.structure,
        r = [];
    for (let n in i) {
        if (hn.call(i, n) === !1) continue;
        let o = i[n],
            b = { name: n, type: !1, nullable: !1 };
        if (!Array.isArray(o)) o = [o];
        for (let g of o)
            if (g === null) b.nullable = !0;
            else if (typeof g === 'string') b.type = 'node';
            else if (Array.isArray(g)) b.type = 'list';
        if (b.type) r.push(b);
    }
    if (r.length) return { context: t.walkContext, fields: r };
    return null;
}
function Xw(l) {
    let t = {};
    for (let i in l.node)
        if (hn.call(l.node, i)) {
            let r = l.node[i];
            if (!r.structure)
                throw new Error('Missed `structure` field in `' + i + '` node type definition');
            t[i] = Jw(i, r);
        }
    return t;
}
function re(l, t) {
    let i = l.fields.slice(),
        r = l.context,
        n = typeof r === 'string';
    if (t) i.reverse();
    return function (o, b, g, e) {
        let f;
        if (n) ((f = b[r]), (b[r] = o));
        for (let h of i) {
            let c = o[h.name];
            if (!h.nullable || c) {
                if (h.type === 'list') {
                    if (t ? c.reduceRight(e, !1) : c.reduce(e, !1)) return !0;
                } else if (g(c)) return !0;
            }
        }
        if (n) b[r] = f;
    };
}
function ne({ StyleSheet: l, Atrule: t, Rule: i, Block: r, DeclarationList: n }) {
    return {
        Atrule: { StyleSheet: l, Atrule: t, Rule: i, Block: r },
        Rule: { StyleSheet: l, Atrule: t, Rule: i, Block: r },
        Declaration: { StyleSheet: l, Atrule: t, Rule: i, Block: r, DeclarationList: n },
    };
}
function oe(l) {
    let t = Xw(l),
        i = {},
        r = {},
        n = Symbol('break-walk'),
        o = Symbol('skip-node');
    for (let f in t)
        if (hn.call(t, f) && t[f] !== null) ((i[f] = re(t[f], !1)), (r[f] = re(t[f], !0)));
    let b = ne(i),
        g = ne(r),
        e = function (f, h) {
            function c(q, V, fl) {
                let K = m.call(R, q, V, fl);
                if (K === n) return !0;
                if (K === o) return !1;
                if (J.hasOwnProperty(q.type)) {
                    if (J[q.type](q, R, c, I)) return !0;
                }
                if (u.call(R, q, V, fl) === n) return !0;
                return !1;
            }
            let m = vi,
                u = vi,
                J = i,
                I = (q, V, fl, K) => q || c(V, fl, K),
                R = {
                    break: n,
                    skip: o,
                    root: f,
                    stylesheet: null,
                    atrule: null,
                    atrulePrelude: null,
                    rule: null,
                    selector: null,
                    block: null,
                    declaration: null,
                    function: null,
                };
            if (typeof h === 'function') m = h;
            else if (h) {
                if (((m = te(h.enter)), (u = te(h.leave)), h.reverse)) J = r;
                if (h.visit) {
                    if (b.hasOwnProperty(h.visit)) J = h.reverse ? g[h.visit] : b[h.visit];
                    else if (!t.hasOwnProperty(h.visit))
                        throw new Error(
                            'Bad value `' +
                                h.visit +
                                '` for `visit` option (should be: ' +
                                Object.keys(t).sort().join(', ') +
                                ')',
                        );
                    ((m = ie(m, h.visit)), (u = ie(u, h.visit)));
                }
            }
            if (m === vi && u === vi)
                throw new Error(
                    "Neither `enter` nor `leave` walker handler is set or both aren't a function",
                );
            c(f);
        };
    return (
        (e.break = n),
        (e.skip = o),
        (e.find = function (f, h) {
            let c = null;
            return (
                e(f, function (m, u, J) {
                    if (h.call(this, m, u, J)) return ((c = m), n);
                }),
                c
            );
        }),
        (e.findLast = function (f, h) {
            let c = null;
            return (
                e(f, {
                    reverse: !0,
                    enter(m, u, J) {
                        if (h.call(this, m, u, J)) return ((c = m), n);
                    },
                }),
                c
            );
        }),
        (e.findAll = function (f, h) {
            let c = [];
            return (
                e(f, function (m, u, J) {
                    if (h.call(this, m, u, J)) c.push(m);
                }),
                c
            );
        }),
        e
    );
}
function Ww(l) {
    return l;
}
function jw(l) {
    let { min: t, max: i, comma: r } = l;
    if (t === 0 && i === 0) return r ? '#?' : '*';
    if (t === 0 && i === 1) return '?';
    if (t === 1 && i === 0) return r ? '#' : '+';
    if (t === 1 && i === 1) return '';
    return (r ? '#' : '') + (t === i ? '{' + t + '}' : '{' + t + ',' + (i !== 0 ? i : '') + '}');
}
function Yw(l) {
    switch (l.type) {
        case 'Range':
            return (
                ' [' + (l.min === null ? '-∞' : l.min) + ',' + (l.max === null ? '∞' : l.max) + ']'
            );
        default:
            throw new Error('Unknown node type `' + l.type + '`');
    }
}
function Ew(l, t, i, r) {
    let n = l.combinator === ' ' || r ? l.combinator : ' ' + l.combinator + ' ',
        o = l.terms.map((b) => cr(b, t, i, r)).join(n);
    if (l.explicit || i) return (r || o[0] === ',' ? '[' : '[ ') + o + (r ? ']' : ' ]');
    return o;
}
function cr(l, t, i, r) {
    let n;
    switch (l.type) {
        case 'Group':
            n = Ew(l, t, i, r) + (l.disallowEmpty ? '!' : '');
            break;
        case 'Multiplier':
            return cr(l.term, t, i, r) + t(jw(l), l);
        case 'Boolean':
            n = '<boolean-expr[' + cr(l.term, t, i, r) + ']>';
            break;
        case 'Type':
            n = '<' + l.name + (l.opts ? t(Yw(l.opts), l.opts) : '') + '>';
            break;
        case 'Property':
            n = "<'" + l.name + "'>";
            break;
        case 'Keyword':
            n = l.name;
            break;
        case 'AtKeyword':
            n = '@' + l.name;
            break;
        case 'Function':
            n = l.name + '(';
            break;
        case 'String':
        case 'Token':
            n = l.value;
            break;
        case 'Comma':
            n = ',';
            break;
        default:
            throw new Error('Unknown node type `' + l.type + '`');
    }
    return t(n, l);
}
function Kt(l, t) {
    let i = Ww,
        r = !1,
        n = !1;
    if (typeof t === 'function') i = t;
    else if (t) {
        if (
            ((r = Boolean(t.forceBraces)),
            (n = Boolean(t.compact)),
            typeof t.decorate === 'function')
        )
            i = t.decorate;
    }
    return cr(l, i, r, n);
}
var be = { offset: 0, line: 1, column: 1 };
function Qw(l, t) {
    let { tokens: i, longestMatch: r } = l,
        n = r < i.length ? i[r].node || null : null,
        o = n !== t ? n : null,
        b = 0,
        g = 0,
        e = 0,
        f = '',
        h,
        c;
    for (let m = 0; m < i.length; m++) {
        let u = i[m].value;
        if (m === r) ((g = u.length), (b = f.length));
        if (o !== null && i[m].node === o)
            if (m <= r) e++;
            else e = 0;
        f += u;
    }
    if (r === i.length || e > 1) ((h = mr(o || t, 'end') || Oi(be, f)), (c = Oi(h)));
    else
        ((h = mr(o, 'start') || Oi(mr(t, 'start') || be, f.slice(0, b))),
            (c = mr(o, 'end') || Oi(h, f.substr(b, g))));
    return { css: f, mismatchOffset: b, mismatchLength: g, start: h, end: c };
}
function mr(l, t) {
    let i = l && l.loc && l.loc[t];
    if (i) return 'line' in i ? Oi(i) : i;
    return null;
}
function Oi({ offset: l, line: t, column: i }, r) {
    let n = { offset: l, line: t, column: i };
    if (r) {
        let o = r.split(/\n|\r\n?|\f/);
        ((n.offset += r.length),
            (n.line += o.length - 1),
            (n.column = o.length === 1 ? n.column + r.length : o.pop().length + 1));
    }
    return n;
}
var Ht = function (l, t) {
        let i = _t('SyntaxReferenceError', l + (t ? ' `' + t + '`' : ''));
        return ((i.reference = t), i);
    },
    fe = function (l, t, i, r) {
        let n = _t('SyntaxMatchError', l),
            { css: o, mismatchOffset: b, mismatchLength: g, start: e, end: f } = Qw(r, i);
        return (
            (n.rawMessage = l),
            (n.syntax = t ? Kt(t) : '<generic>'),
            (n.css = o),
            (n.mismatchOffset = b),
            (n.mismatchLength = g),
            (n.message =
                l +
                `
  syntax: ` +
                n.syntax +
                `
   value: ` +
                (o || '<empty string>') +
                `
  --------` +
                new Array(n.mismatchOffset + 1).join('-') +
                '^'),
            Object.assign(n, e),
            (n.loc = { source: (i && i.loc && i.loc.source) || '<unknown>', start: e, end: f }),
            n
        );
    };
var wr = new Map(),
    Mt = new Map();
var pr = Gw,
    cn = Lw;
function ur(l, t) {
    return (
        (t = t || 0),
        l.length - t >= 2 && l.charCodeAt(t) === 45 && l.charCodeAt(t + 1) === 45
    );
}
function ge(l, t) {
    if (((t = t || 0), l.length - t >= 3)) {
        if (l.charCodeAt(t) === 45 && l.charCodeAt(t + 1) !== 45) {
            let i = l.indexOf('-', t + 2);
            if (i !== -1) return l.substring(t, i + 1);
        }
    }
    return '';
}
function Gw(l) {
    if (wr.has(l)) return wr.get(l);
    let t = l.toLowerCase(),
        i = wr.get(t);
    if (i === void 0) {
        let r = ur(t, 0),
            n = !r ? ge(t, 0) : '';
        i = Object.freeze({
            basename: t.substr(n.length),
            name: t,
            prefix: n,
            vendor: n,
            custom: r,
        });
    }
    return (wr.set(l, i), i);
}
function Lw(l) {
    if (Mt.has(l)) return Mt.get(l);
    let t = l,
        i = l[0];
    if (i === '/') i = l[1] === '/' ? '//' : '/';
    else if (i !== '_' && i !== '*' && i !== '$' && i !== '#' && i !== '+' && i !== '&') i = '';
    let r = ur(t, i.length);
    if (!r) {
        if (((t = t.toLowerCase()), Mt.has(t))) {
            let g = Mt.get(t);
            return (Mt.set(l, g), g);
        }
    }
    let n = !r ? ge(t, i.length) : '',
        o = t.substr(0, i.length + n.length),
        b = Object.freeze({
            basename: t.substr(o.length),
            name: t.substr(i.length),
            hack: i,
            vendor: n,
            prefix: o,
            custom: r,
        });
    return (Mt.set(l, b), b);
}
var yt = ['initial', 'inherit', 'unset', 'revert', 'revert-layer'];
var $i = 43,
    Ul = 45,
    mn = 110,
    At = !0,
    Fw = !1;
function pn(l, t) {
    return l !== null && l.type === $ && l.value.charCodeAt(0) === t;
}
function Di(l, t, i) {
    while (l !== null && (l.type === Y || l.type === U)) l = i(++t);
    return t;
}
function ft(l, t, i, r) {
    if (!l) return 0;
    let n = l.value.charCodeAt(t);
    if (n === $i || n === Ul) {
        if (i) return 0;
        t++;
    }
    for (; t < l.value.length; t++) if (!ol(l.value.charCodeAt(t))) return 0;
    return r + 1;
}
function wn(l, t, i) {
    let r = !1,
        n = Di(l, t, i);
    if (((l = i(n)), l === null)) return t;
    if (l.type !== v)
        if (pn(l, $i) || pn(l, Ul)) {
            if (((r = !0), (n = Di(i(++n), n, i)), (l = i(n)), l === null || l.type !== v))
                return 0;
        } else return t;
    if (!r) {
        let o = l.value.charCodeAt(0);
        if (o !== $i && o !== Ul) return 0;
    }
    return ft(l, r ? 0 : 1, r, n);
}
function un(l, t) {
    let i = 0;
    if (!l) return 0;
    if (l.type === v) return ft(l, 0, Fw, i);
    else if (l.type === w && l.value.charCodeAt(0) === Ul) {
        if (!Il(l.value, 1, mn)) return 0;
        switch (l.value.length) {
            case 2:
                return wn(t(++i), i, t);
            case 3:
                if (l.value.charCodeAt(2) !== Ul) return 0;
                return ((i = Di(t(++i), i, t)), (l = t(i)), ft(l, 0, At, i));
            default:
                if (l.value.charCodeAt(2) !== Ul) return 0;
                return ft(l, 3, At, i);
        }
    } else if (l.type === w || (pn(l, $i) && t(i + 1).type === w)) {
        if (l.type !== w) l = t(++i);
        if (l === null || !Il(l.value, 0, mn)) return 0;
        switch (l.value.length) {
            case 1:
                return wn(t(++i), i, t);
            case 2:
                if (l.value.charCodeAt(1) !== Ul) return 0;
                return ((i = Di(t(++i), i, t)), (l = t(i)), ft(l, 0, At, i));
            default:
                if (l.value.charCodeAt(1) !== Ul) return 0;
                return ft(l, 2, At, i);
        }
    } else if (l.type === W) {
        let r = l.value.charCodeAt(0),
            n = r === $i || r === Ul ? 1 : 0,
            o = n;
        for (; o < l.value.length; o++) if (!ol(l.value.charCodeAt(o))) break;
        if (o === n) return 0;
        if (!Il(l.value, o, mn)) return 0;
        if (o + 1 === l.value.length) return wn(t(++i), i, t);
        else {
            if (l.value.charCodeAt(o + 1) !== Ul) return 0;
            if (o + 2 === l.value.length)
                return ((i = Di(t(++i), i, t)), (l = t(i)), ft(l, 0, At, i));
            else return ft(l, o + 2, At, i);
        }
    }
    return 0;
}
var Rw = 43,
    ee = 45,
    he = 63,
    Vw = 117;
function zn(l, t) {
    return l !== null && l.type === $ && l.value.charCodeAt(0) === t;
}
function Kw(l, t) {
    return l.value.charCodeAt(0) === t;
}
function qi(l, t, i) {
    let r = 0;
    for (let n = t; n < l.value.length; n++) {
        let o = l.value.charCodeAt(n);
        if (o === ee && i && r !== 0) return (qi(l, t + r + 1, !1), 6);
        if (!Dl(o)) return 0;
        if (++r > 6) return 0;
    }
    return r;
}
function zr(l, t, i) {
    if (!l) return 0;
    while (zn(i(t), he)) {
        if (++l > 6) return 0;
        t++;
    }
    return t;
}
function xn(l, t) {
    let i = 0;
    if (l === null || l.type !== w || !Il(l.value, 0, Vw)) return 0;
    if (((l = t(++i)), l === null)) return 0;
    if (zn(l, Rw)) {
        if (((l = t(++i)), l === null)) return 0;
        if (l.type === w) return zr(qi(l, 0, !0), ++i, t);
        if (zn(l, he)) return zr(1, ++i, t);
        return 0;
    }
    if (l.type === v) {
        let r = qi(l, 1, !0);
        if (r === 0) return 0;
        if (((l = t(++i)), l === null)) return i;
        if (l.type === W || l.type === v) {
            if (!Kw(l, ee) || !qi(l, 1, !1)) return 0;
            return i + 1;
        }
        return zr(r, i, t);
    }
    if (l.type === W) return zr(qi(l, 1, !0), ++i, t);
    return 0;
}
var Hw = ['calc(', '-moz-calc(', '-webkit-calc('],
    an = new Map([
        [_, a],
        [j, a],
        [el, pl],
        [N, ml],
    ]);
function El(l, t) {
    return t < l.length ? l.charCodeAt(t) : 0;
}
function ce(l, t) {
    return Tl(l, 0, l.length, t);
}
function me(l, t) {
    for (let i = 0; i < t.length; i++) if (ce(l, t[i])) return !0;
    return !1;
}
function we(l, t) {
    if (t !== l.length - 2) return !1;
    return El(l, t) === 92 && ol(El(l, t + 1));
}
function xr(l, t, i) {
    if (l && l.type === 'Range') {
        let r = Number(i !== void 0 && i !== t.length ? t.substr(0, i) : t);
        if (isNaN(r)) return !0;
        if (l.min !== null && r < l.min && typeof l.min !== 'string') return !0;
        if (l.max !== null && r > l.max && typeof l.max !== 'string') return !0;
    }
    return !1;
}
function Mw(l, t) {
    let i = 0,
        r = [],
        n = 0;
    l: do {
        switch (l.type) {
            case ml:
            case a:
            case pl:
                if (l.type !== i) break l;
                if (((i = r.pop()), r.length === 0)) {
                    n++;
                    break l;
                }
                break;
            case _:
            case j:
            case el:
            case N:
                (r.push(i), (i = an.get(l.type)));
                break;
        }
        n++;
    } while ((l = t(n)));
    return n;
}
function jl(l) {
    return function (t, i, r) {
        if (t === null) return 0;
        if (t.type === _ && me(t.value, Hw)) return Mw(t, i);
        return l(t, i, r);
    };
}
function C(l) {
    return function (t) {
        if (t === null || t.type !== l) return 0;
        return 1;
    };
}
function yw(l) {
    if (l === null || l.type !== w) return 0;
    let t = l.value.toLowerCase();
    if (me(t, yt)) return 0;
    if (ce(t, 'default')) return 0;
    return 1;
}
function pe(l) {
    if (l === null || l.type !== w) return 0;
    if (El(l.value, 0) !== 45 || El(l.value, 1) !== 45) return 0;
    return 1;
}
function Aw(l) {
    if (!pe(l)) return 0;
    if (l.value === '--') return 0;
    return 1;
}
function Uw(l) {
    if (l === null || l.type !== F) return 0;
    let t = l.value.length;
    if (t !== 4 && t !== 5 && t !== 7 && t !== 9) return 0;
    for (let i = 1; i < t; i++) if (!Dl(El(l.value, i))) return 0;
    return 1;
}
function kw(l) {
    if (l === null || l.type !== F) return 0;
    if (!Gt(El(l.value, 1), El(l.value, 2), El(l.value, 3))) return 0;
    return 1;
}
function Sw(l, t) {
    if (!l) return 0;
    let i = 0,
        r = [],
        n = 0;
    l: do {
        switch (l.type) {
            case Qt:
            case wl:
                break l;
            case ml:
            case a:
            case pl:
                if (l.type !== i) break l;
                i = r.pop();
                break;
            case Z:
                if (i === 0) break l;
                break;
            case $:
                if (i === 0 && l.value === '!') break l;
                break;
            case _:
            case j:
            case el:
            case N:
                (r.push(i), (i = an.get(l.type)));
                break;
        }
        n++;
    } while ((l = t(n)));
    return n;
}
function Nw(l, t) {
    if (!l) return 0;
    let i = 0,
        r = [],
        n = 0;
    l: do {
        switch (l.type) {
            case Qt:
            case wl:
                break l;
            case ml:
            case a:
            case pl:
                if (l.type !== i) break l;
                i = r.pop();
                break;
            case _:
            case j:
            case el:
            case N:
                (r.push(i), (i = an.get(l.type)));
                break;
        }
        n++;
    } while ((l = t(n)));
    return n;
}
function lt(l) {
    if (l) l = new Set(l);
    return function (t, i, r) {
        if (t === null || t.type !== W) return 0;
        let n = xt(t.value, 0);
        if (l !== null) {
            let o = t.value.indexOf('\\', n),
                b = o === -1 || !we(t.value, o) ? t.value.substr(n) : t.value.substring(n, o);
            if (l.has(b.toLowerCase()) === !1) return 0;
        }
        if (xr(r, t.value, n)) return 0;
        return 1;
    };
}
function Cw(l, t, i) {
    if (l === null || l.type !== A) return 0;
    if (xr(i, l.value, l.value.length - 1)) return 0;
    return 1;
}
function ue(l) {
    if (typeof l !== 'function')
        l = function () {
            return 0;
        };
    return function (t, i, r) {
        if (t !== null && t.type === v) {
            if (Number(t.value) === 0) return 1;
        }
        return l(t, i, r);
    };
}
function Pw(l, t, i) {
    if (l === null) return 0;
    let r = xt(l.value, 0);
    if (r !== l.value.length && !we(l.value, r)) return 0;
    if (xr(i, l.value, r)) return 0;
    return 1;
}
function Iw(l, t, i) {
    if (l === null || l.type !== v) return 0;
    let r = El(l.value, 0) === 43 || El(l.value, 0) === 45 ? 1 : 0;
    for (; r < l.value.length; r++) if (!ol(El(l.value, r))) return 0;
    if (xr(i, l.value, r)) return 0;
    return 1;
}
var Tw = {
        'ident-token': C(w),
        'function-token': C(_),
        'at-keyword-token': C(S),
        'hash-token': C(F),
        'string-token': C(gl),
        'bad-string-token': C(Qt),
        'url-token': C(tl),
        'bad-url-token': C(wl),
        'delim-token': C($),
        'number-token': C(v),
        'percentage-token': C(A),
        'dimension-token': C(W),
        'whitespace-token': C(Y),
        'CDO-token': C(Pl),
        'CDC-token': C(cl),
        'colon-token': C(P),
        'semicolon-token': C(Z),
        'comma-token': C(d),
        '[-token': C(el),
        ']-token': C(pl),
        '(-token': C(j),
        ')-token': C(a),
        '{-token': C(N),
        '}-token': C(ml),
    },
    Zw = {
        string: C(gl),
        ident: C(w),
        percentage: jl(Cw),
        zero: ue(),
        number: jl(Pw),
        integer: jl(Iw),
        'custom-ident': yw,
        'dashed-ident': pe,
        'custom-property-name': Aw,
        'hex-color': Uw,
        'id-selector': kw,
        'an-plus-b': un,
        urange: xn,
        'declaration-value': Sw,
        'any-value': Nw,
    };
function dw(l) {
    let {
        angle: t,
        decibel: i,
        frequency: r,
        flex: n,
        length: o,
        resolution: b,
        semitones: g,
        time: e,
    } = l || {};
    return {
        dimension: jl(lt(null)),
        angle: jl(lt(t)),
        decibel: jl(lt(i)),
        frequency: jl(lt(r)),
        flex: jl(lt(n)),
        length: jl(ue(lt(o))),
        resolution: jl(lt(b)),
        semitones: jl(lt(g)),
        time: jl(lt(e)),
    };
}
function ze(l) {
    return { ...Tw, ...Zw, ...dw(l) };
}
var ar = {};
D(ar, {
    time: () => ip,
    semitones: () => fp,
    resolution: () => np,
    length: () => lp,
    frequency: () => rp,
    flex: () => op,
    decibel: () => bp,
    angle: () => tp,
});
var lp = [
        'cm',
        'mm',
        'q',
        'in',
        'pt',
        'pc',
        'px',
        'em',
        'rem',
        'ex',
        'rex',
        'cap',
        'rcap',
        'ch',
        'rch',
        'ic',
        'ric',
        'lh',
        'rlh',
        'vw',
        'svw',
        'lvw',
        'dvw',
        'vh',
        'svh',
        'lvh',
        'dvh',
        'vi',
        'svi',
        'lvi',
        'dvi',
        'vb',
        'svb',
        'lvb',
        'dvb',
        'vmin',
        'svmin',
        'lvmin',
        'dvmin',
        'vmax',
        'svmax',
        'lvmax',
        'dvmax',
        'cqw',
        'cqh',
        'cqi',
        'cqb',
        'cqmin',
        'cqmax',
    ],
    tp = ['deg', 'grad', 'rad', 'turn'],
    ip = ['s', 'ms'],
    rp = ['hz', 'khz'],
    np = ['dpi', 'dpcm', 'dppx', 'x'],
    op = ['fr'],
    bp = ['db'],
    fp = ['st'];
function _n(l, t, i) {
    return Object.assign(_t('SyntaxError', l), {
        input: t,
        offset: i,
        rawMessage: l,
        message:
            l +
            `
  ` +
            t +
            `
--` +
            new Array((i || t.length) + 1).join('-') +
            '^',
    });
}
var gp = 9,
    ep = 10,
    hp = 12,
    cp = 13,
    mp = 32,
    xe = new Uint8Array(128).map((l, t) => (/[a-zA-Z0-9\-]/.test(String.fromCharCode(t)) ? 1 : 0));
class vn {
    constructor(l) {
        ((this.str = l), (this.pos = 0));
    }
    charCodeAt(l) {
        return l < this.str.length ? this.str.charCodeAt(l) : 0;
    }
    charCode() {
        return this.charCodeAt(this.pos);
    }
    isNameCharCode(l = this.charCode()) {
        return l < 128 && xe[l] === 1;
    }
    nextCharCode() {
        return this.charCodeAt(this.pos + 1);
    }
    nextNonWsCode(l) {
        return this.charCodeAt(this.findWsEnd(l));
    }
    skipWs() {
        this.pos = this.findWsEnd(this.pos);
    }
    findWsEnd(l) {
        for (; l < this.str.length; l++) {
            let t = this.str.charCodeAt(l);
            if (t !== cp && t !== ep && t !== hp && t !== mp && t !== gp) break;
        }
        return l;
    }
    substringToPos(l) {
        return this.str.substring(this.pos, (this.pos = l));
    }
    eat(l) {
        if (this.charCode() !== l) this.error('Expect `' + String.fromCharCode(l) + '`');
        this.pos++;
    }
    peek() {
        return this.pos < this.str.length ? this.str.charAt(this.pos++) : '';
    }
    error(l) {
        throw new _n(l, this.str, this.pos);
    }
    scanSpaces() {
        return this.substringToPos(this.findWsEnd(this.pos));
    }
    scanWord() {
        let l = this.pos;
        for (; l < this.str.length; l++) {
            let t = this.str.charCodeAt(l);
            if (t >= 128 || xe[t] === 0) break;
        }
        if (this.pos === l) this.error('Expect a keyword');
        return this.substringToPos(l);
    }
    scanNumber() {
        let l = this.pos;
        for (; l < this.str.length; l++) {
            let t = this.str.charCodeAt(l);
            if (t < 48 || t > 57) break;
        }
        if (this.pos === l) this.error('Expect a number');
        return this.substringToPos(l);
    }
    scanString() {
        let l = this.str.indexOf("'", this.pos + 1);
        if (l === -1) ((this.pos = this.str.length), this.error('Expect an apostrophe'));
        return this.substringToPos(l + 1);
    }
}
var wp = 9,
    pp = 10,
    up = 12,
    zp = 13,
    xp = 32,
    se = 33,
    qn = 35,
    ae = 38,
    _r = 39,
    Je = 40,
    ap = 41,
    Xe = 42,
    sn = 43,
    Jn = 44,
    _e = 45,
    Xn = 60,
    Dn = 62,
    $n = 63,
    _p = 64,
    si = 91,
    Ji = 93,
    vr = 123,
    ve = 124,
    Oe = 125,
    De = 8734,
    $e = { ' ': 1, '&&': 2, '||': 3, '|': 4 };
function qe(l) {
    let t = null,
        i = null;
    if ((l.eat(vr), l.skipWs(), (t = l.scanNumber(l)), l.skipWs(), l.charCode() === Jn)) {
        if ((l.pos++, l.skipWs(), l.charCode() !== Oe)) ((i = l.scanNumber(l)), l.skipWs());
    } else i = t;
    return (l.eat(Oe), { min: Number(t), max: i ? Number(i) : 0 });
}
function vp(l) {
    let t = null,
        i = !1;
    switch (l.charCode()) {
        case Xe:
            (l.pos++, (t = { min: 0, max: 0 }));
            break;
        case sn:
            (l.pos++, (t = { min: 1, max: 0 }));
            break;
        case $n:
            (l.pos++, (t = { min: 0, max: 1 }));
            break;
        case qn:
            if ((l.pos++, (i = !0), l.charCode() === vr)) t = qe(l);
            else if (l.charCode() === $n) (l.pos++, (t = { min: 0, max: 0 }));
            else t = { min: 1, max: 0 };
            break;
        case vr:
            t = qe(l);
            break;
        default:
            return null;
    }
    return { type: 'Multiplier', comma: i, min: t.min, max: t.max, term: null };
}
function gt(l, t) {
    let i = vp(l);
    if (i !== null) {
        if (((i.term = t), l.charCode() === qn && l.charCodeAt(l.pos - 1) === sn)) return gt(l, i);
        return i;
    }
    return t;
}
function On(l) {
    let t = l.peek();
    if (t === '') return null;
    return gt(l, { type: 'Token', value: t });
}
function Op(l) {
    let t;
    return (
        l.eat(Xn),
        l.eat(_r),
        (t = l.scanWord()),
        l.eat(_r),
        l.eat(Dn),
        gt(l, { type: 'Property', name: t })
    );
}
function Dp(l) {
    let t = null,
        i = null,
        r = 1;
    if ((l.eat(si), l.charCode() === _e)) (l.peek(), (r = -1));
    if (r == -1 && l.charCode() === De) l.peek();
    else if (((t = r * Number(l.scanNumber(l))), l.isNameCharCode())) t += l.scanWord();
    if ((l.skipWs(), l.eat(Jn), l.skipWs(), l.charCode() === De)) l.peek();
    else {
        if (((r = 1), l.charCode() === _e)) (l.peek(), (r = -1));
        if (((i = r * Number(l.scanNumber(l))), l.isNameCharCode())) i += l.scanWord();
    }
    return (l.eat(Ji), { type: 'Range', min: t, max: i });
}
function $p(l) {
    let t,
        i = null;
    if ((l.eat(Xn), (t = l.scanWord()), t === 'boolean-expr')) {
        l.eat(si);
        let r = Wn(l, Ji);
        return (
            l.eat(Ji),
            l.eat(Dn),
            gt(l, { type: 'Boolean', term: r.terms.length === 1 ? r.terms[0] : r })
        );
    }
    if (l.charCode() === Je && l.nextCharCode() === ap) ((l.pos += 2), (t += '()'));
    if (l.charCodeAt(l.findWsEnd(l.pos)) === si) (l.skipWs(), (i = Dp(l)));
    return (l.eat(Dn), gt(l, { type: 'Type', name: t, opts: i }));
}
function qp(l) {
    let t = l.scanWord();
    if (l.charCode() === Je) return (l.pos++, { type: 'Function', name: t });
    return gt(l, { type: 'Keyword', name: t });
}
function sp(l, t) {
    function i(n, o) {
        return { type: 'Group', terms: n, combinator: o, disallowEmpty: !1, explicit: !1 };
    }
    let r;
    t = Object.keys(t).sort((n, o) => $e[n] - $e[o]);
    while (t.length > 0) {
        r = t.shift();
        let n = 0,
            o = 0;
        for (; n < l.length; n++) {
            let b = l[n];
            if (b.type === 'Combinator')
                if (b.value === r) {
                    if (o === -1) o = n - 1;
                    (l.splice(n, 1), n--);
                } else {
                    if (o !== -1 && n - o > 1)
                        (l.splice(o, n - o, i(l.slice(o, n), r)), (n = o + 1));
                    o = -1;
                }
        }
        if (o !== -1 && t.length) l.splice(o, n - o, i(l.slice(o, n), r));
    }
    return r;
}
function Wn(l, t) {
    let i = Object.create(null),
        r = [],
        n,
        o = null,
        b = l.pos;
    while (l.charCode() !== t && (n = Xp(l, t)))
        if (n.type !== 'Spaces') {
            if (n.type === 'Combinator') {
                if (o === null || o.type === 'Combinator')
                    ((l.pos = b), l.error('Unexpected combinator'));
                i[n.value] = !0;
            } else if (o !== null && o.type !== 'Combinator')
                ((i[' '] = !0), r.push({ type: 'Combinator', value: ' ' }));
            (r.push(n), (o = n), (b = l.pos));
        }
    if (o !== null && o.type === 'Combinator') ((l.pos -= b), l.error('Unexpected combinator'));
    return {
        type: 'Group',
        terms: r,
        combinator: sp(r, i) || ' ',
        disallowEmpty: !1,
        explicit: !1,
    };
}
function Jp(l, t) {
    let i;
    if ((l.eat(si), (i = Wn(l, t)), l.eat(Ji), (i.explicit = !0), l.charCode() === se))
        (l.pos++, (i.disallowEmpty = !0));
    return i;
}
function Xp(l, t) {
    let i = l.charCode();
    switch (i) {
        case Ji:
            break;
        case si:
            return gt(l, Jp(l, t));
        case Xn:
            return l.nextCharCode() === _r ? Op(l) : $p(l);
        case ve:
            return {
                type: 'Combinator',
                value: l.substringToPos(l.pos + (l.nextCharCode() === ve ? 2 : 1)),
            };
        case ae:
            return (l.pos++, l.eat(ae), { type: 'Combinator', value: '&&' });
        case Jn:
            return (l.pos++, { type: 'Comma' });
        case _r:
            return gt(l, { type: 'String', value: l.scanString() });
        case xp:
        case wp:
        case pp:
        case zp:
        case up:
            return { type: 'Spaces', value: l.scanSpaces() };
        case _p:
            if (((i = l.nextCharCode()), l.isNameCharCode(i)))
                return (l.pos++, { type: 'AtKeyword', name: l.scanWord() });
            return On(l);
        case Xe:
        case sn:
        case $n:
        case qn:
        case se:
            break;
        case vr:
            if (((i = l.nextCharCode()), i < 48 || i > 57)) return On(l);
            break;
        default:
            if (l.isNameCharCode(i)) return qp(l);
            return On(l);
    }
}
function Xi(l) {
    let t = new vn(l),
        i = Wn(t);
    if (t.pos !== l.length) t.error('Unexpected input');
    if (i.terms.length === 1 && i.terms[0].type === 'Group') return i.terms[0];
    return i;
}
var Wi = function () {};
function We(l) {
    return typeof l === 'function' ? l : Wi;
}
function jn(l, t, i) {
    function r(b) {
        switch ((n.call(i, b), b.type)) {
            case 'Group':
                b.terms.forEach(r);
                break;
            case 'Multiplier':
            case 'Boolean':
                r(b.term);
                break;
            case 'Type':
            case 'Property':
            case 'Keyword':
            case 'AtKeyword':
            case 'Function':
            case 'String':
            case 'Token':
            case 'Comma':
                break;
            default:
                throw new Error('Unknown type: ' + b.type);
        }
        o.call(i, b);
    }
    let n = Wi,
        o = Wi;
    if (typeof t === 'function') n = t;
    else if (t) ((n = We(t.enter)), (o = We(t.leave)));
    if (n === Wi && o === Wi)
        throw new Error(
            "Neither `enter` nor `leave` walker handler is set or both aren't a function",
        );
    r(l, i);
}
var jp = {
    decorator(l) {
        let t = [],
            i = null;
        return {
            ...l,
            node(r) {
                let n = i;
                ((i = r), l.node.call(this, r), (i = n));
            },
            emit(r, n, o) {
                t.push({ type: n, value: r, node: o ? null : i });
            },
            result() {
                return t;
            },
        };
    },
};
function Yp(l) {
    let t = [];
    return (bt(l, (i, r, n) => t.push({ type: i, value: l.slice(r, n), node: null })), t);
}
function Yn(l, t) {
    if (typeof l === 'string') return Yp(l);
    return t.generate(l, jp);
}
var M = { type: 'Match' },
    k = { type: 'Mismatch' },
    Or = { type: 'DisallowEmpty' },
    Ep = 40,
    Qp = 41;
function xl(l, t, i) {
    if (t === M && i === k) return l;
    if (l === M && t === M && i === M) return l;
    if (l.type === 'If' && l.else === k && t === M) ((t = l.then), (l = l.match));
    return { type: 'If', match: l, then: t, else: i };
}
function Ye(l) {
    return l.length > 2 && l.charCodeAt(l.length - 2) === Ep && l.charCodeAt(l.length - 1) === Qp;
}
function je(l) {
    return (
        l.type === 'Keyword' ||
        l.type === 'AtKeyword' ||
        l.type === 'Function' ||
        (l.type === 'Type' && Ye(l.name))
    );
}
function et(l, t = ' ', i = !1) {
    return { type: 'Group', terms: l, combinator: t, disallowEmpty: !1, explicit: i };
}
function ji(l, t, i = new Set()) {
    if (!i.has(l))
        switch ((i.add(l), l.type)) {
            case 'If':
                ((l.match = ji(l.match, t, i)),
                    (l.then = ji(l.then, t, i)),
                    (l.else = ji(l.else, t, i)));
                break;
            case 'Type':
                return t[l.name] || l;
        }
    return l;
}
function En(l, t, i) {
    switch (l) {
        case ' ': {
            let r = M;
            for (let n = t.length - 1; n >= 0; n--) {
                let o = t[n];
                r = xl(o, r, k);
            }
            return r;
        }
        case '|': {
            let r = k,
                n = null;
            for (let o = t.length - 1; o >= 0; o--) {
                let b = t[o];
                if (je(b)) {
                    if (n === null && o > 0 && je(t[o - 1]))
                        ((n = Object.create(null)), (r = xl({ type: 'Enum', map: n }, M, r)));
                    if (n !== null) {
                        let g = (Ye(b.name) ? b.name.slice(0, -1) : b.name).toLowerCase();
                        if (g in n === !1) {
                            n[g] = b;
                            continue;
                        }
                    }
                }
                ((n = null), (r = xl(b, M, r)));
            }
            return r;
        }
        case '&&': {
            if (t.length > 5) return { type: 'MatchOnce', terms: t, all: !0 };
            let r = k;
            for (let n = t.length - 1; n >= 0; n--) {
                let o = t[n],
                    b;
                if (t.length > 1)
                    b = En(
                        l,
                        t.filter(function (g) {
                            return g !== o;
                        }),
                        !1,
                    );
                else b = M;
                r = xl(o, b, r);
            }
            return r;
        }
        case '||': {
            if (t.length > 5) return { type: 'MatchOnce', terms: t, all: !1 };
            let r = i ? M : k;
            for (let n = t.length - 1; n >= 0; n--) {
                let o = t[n],
                    b;
                if (t.length > 1)
                    b = En(
                        l,
                        t.filter(function (g) {
                            return g !== o;
                        }),
                        !0,
                    );
                else b = M;
                r = xl(o, b, r);
            }
            return r;
        }
    }
}
function Gp(l) {
    let t = M,
        i = Ut(l.term);
    if (l.max === 0) {
        if (((i = xl(i, Or, k)), (t = xl(i, null, k)), (t.then = xl(M, M, t)), l.comma))
            t.then.else = xl({ type: 'Comma', syntax: l }, t, k);
    } else
        for (let r = l.min || 1; r <= l.max; r++) {
            if (l.comma && t !== M) t = xl({ type: 'Comma', syntax: l }, t, k);
            t = xl(i, xl(M, M, t), k);
        }
    if (l.min === 0) t = xl(M, M, t);
    else
        for (let r = 0; r < l.min - 1; r++) {
            if (l.comma && t !== M) t = xl({ type: 'Comma', syntax: l }, t, k);
            t = xl(i, t, k);
        }
    return t;
}
function Ut(l) {
    if (typeof l === 'function') return { type: 'Generic', fn: l };
    switch (l.type) {
        case 'Group': {
            let t = En(l.combinator, l.terms.map(Ut), !1);
            if (l.disallowEmpty) t = xl(t, Or, k);
            return t;
        }
        case 'Multiplier':
            return Gp(l);
        case 'Boolean': {
            let t = Ut(l.term),
                i = Ut(
                    et(
                        [
                            et([
                                { type: 'Keyword', name: 'not' },
                                { type: 'Type', name: '!boolean-group' },
                            ]),
                            et([
                                { type: 'Type', name: '!boolean-group' },
                                et(
                                    [
                                        {
                                            type: 'Multiplier',
                                            comma: !1,
                                            min: 0,
                                            max: 0,
                                            term: et([
                                                { type: 'Keyword', name: 'and' },
                                                { type: 'Type', name: '!boolean-group' },
                                            ]),
                                        },
                                        {
                                            type: 'Multiplier',
                                            comma: !1,
                                            min: 0,
                                            max: 0,
                                            term: et([
                                                { type: 'Keyword', name: 'or' },
                                                { type: 'Type', name: '!boolean-group' },
                                            ]),
                                        },
                                    ],
                                    '|',
                                ),
                            ]),
                        ],
                        '|',
                    ),
                ),
                r = Ut(
                    et(
                        [
                            { type: 'Type', name: '!term' },
                            et([
                                { type: 'Token', value: '(' },
                                { type: 'Type', name: '!self' },
                                { type: 'Token', value: ')' },
                            ]),
                            { type: 'Type', name: 'general-enclosed' },
                        ],
                        '|',
                    ),
                );
            return (ji(r, { '!term': t, '!self': i }), ji(i, { '!boolean-group': r }), i);
        }
        case 'Type':
        case 'Property':
            return { type: l.type, name: l.name, syntax: l };
        case 'Keyword':
            return { type: l.type, name: l.name.toLowerCase(), syntax: l };
        case 'AtKeyword':
            return { type: l.type, name: '@' + l.name.toLowerCase(), syntax: l };
        case 'Function':
            return { type: l.type, name: l.name.toLowerCase() + '(', syntax: l };
        case 'String':
            if (l.value.length === 3) return { type: 'Token', value: l.value.charAt(1), syntax: l };
            return {
                type: l.type,
                value: l.value.substr(1, l.value.length - 2).replace(/\\'/g, "'"),
                syntax: l,
            };
        case 'Token':
            return { type: l.type, value: l.value, syntax: l };
        case 'Comma':
            return { type: l.type, syntax: l };
        default:
            throw new Error('Unknown node type:', l.type);
    }
}
function Yi(l, t) {
    if (typeof l === 'string') l = Xi(l);
    return { type: 'MatchGraph', match: Ut(l), syntax: t || null, source: l };
}
var { hasOwnProperty: Ee } = Object.prototype,
    Lp = 0,
    Bp = 1,
    Gn = 2,
    Fe = 3,
    Qe = 'Match',
    Fp = 'Mismatch',
    Rp =
        'Maximum iteration number exceeded (please fill an issue on https://github.com/csstree/csstree/issues)',
    Ge = 15000,
    Vp = 0;
function Kp(l) {
    let t = null,
        i = null,
        r = l;
    while (r !== null) ((i = r.prev), (r.prev = t), (t = r), (r = i));
    return t;
}
function Qn(l, t) {
    if (l.length !== t.length) return !1;
    for (let i = 0; i < l.length; i++) {
        let r = t.charCodeAt(i),
            n = l.charCodeAt(i);
        if (n >= 65 && n <= 90) n = n | 32;
        if (n !== r) return !1;
    }
    return !0;
}
function Hp(l) {
    if (l.type !== $) return !1;
    return l.value !== '?';
}
function Le(l) {
    if (l === null) return !0;
    return l.type === d || l.type === _ || l.type === j || l.type === el || l.type === N || Hp(l);
}
function Be(l) {
    if (l === null) return !0;
    return l.type === a || l.type === pl || l.type === ml || (l.type === $ && l.value === '/');
}
function Mp(l, t, i) {
    function r() {
        do (V++, (q = V < l.length ? l[V] : null));
        while (q !== null && (q.type === Y || q.type === U));
    }
    function n(G) {
        let L = V + G;
        return L < l.length ? l[L] : null;
    }
    function o(G, L) {
        return {
            nextState: G,
            matchStack: K,
            syntaxStack: c,
            thenStack: m,
            tokenIndex: V,
            prev: L,
        };
    }
    function b(G) {
        m = { nextState: G, matchStack: K, syntaxStack: c, prev: m };
    }
    function g(G) {
        u = o(G, u);
    }
    function e() {
        if (((K = { type: Bp, syntax: t.syntax, token: q, prev: K }), r(), (J = null), V > fl))
            fl = V;
    }
    function f() {
        ((c = { syntax: t.syntax, opts: t.syntax.opts || (c !== null && c.opts) || null, prev: c }),
            (K = { type: Gn, syntax: t.syntax, token: K.token, prev: K }));
    }
    function h() {
        if (K.type === Gn) K = K.prev;
        else K = { type: Fe, syntax: c.syntax, token: K.token, prev: K };
        c = c.prev;
    }
    let c = null,
        m = null,
        u = null,
        J = null,
        I = 0,
        R = null,
        q = null,
        V = -1,
        fl = 0,
        K = { type: Lp, syntax: null, token: null, prev: null };
    r();
    while (R === null && ++I < Ge)
        switch (t.type) {
            case 'Match':
                if (m === null) {
                    if (q !== null) {
                        if (V !== l.length - 1 || (q.value !== '\\0' && q.value !== '\\9')) {
                            t = k;
                            break;
                        }
                    }
                    R = Qe;
                    break;
                }
                if (((t = m.nextState), t === Or))
                    if (m.matchStack === K) {
                        t = k;
                        break;
                    } else t = M;
                while (m.syntaxStack !== c) h();
                m = m.prev;
                break;
            case 'Mismatch':
                if (J !== null && J !== !1) {
                    if (u === null || V > u.tokenIndex) ((u = J), (J = !1));
                } else if (u === null) {
                    R = Fp;
                    break;
                }
                ((t = u.nextState),
                    (m = u.thenStack),
                    (c = u.syntaxStack),
                    (K = u.matchStack),
                    (V = u.tokenIndex),
                    (q = V < l.length ? l[V] : null),
                    (u = u.prev));
                break;
            case 'MatchGraph':
                t = t.match;
                break;
            case 'If':
                if (t.else !== k) g(t.else);
                if (t.then !== M) b(t.then);
                t = t.match;
                break;
            case 'MatchOnce':
                t = { type: 'MatchOnceBuffer', syntax: t, index: 0, mask: 0 };
                break;
            case 'MatchOnceBuffer': {
                let y = t.syntax.terms;
                if (t.index === y.length) {
                    if (t.mask === 0 || t.syntax.all) {
                        t = k;
                        break;
                    }
                    t = M;
                    break;
                }
                if (t.mask === (1 << y.length) - 1) {
                    t = M;
                    break;
                }
                for (; t.index < y.length; t.index++) {
                    let T = 1 << t.index;
                    if ((t.mask & T) === 0) {
                        (g(t),
                            b({ type: 'AddMatchOnce', syntax: t.syntax, mask: t.mask | T }),
                            (t = y[t.index++]));
                        break;
                    }
                }
                break;
            }
            case 'AddMatchOnce':
                t = { type: 'MatchOnceBuffer', syntax: t.syntax, index: 0, mask: t.mask };
                break;
            case 'Enum':
                if (q !== null) {
                    let y = q.value.toLowerCase();
                    if (y.indexOf('\\') !== -1) y = y.replace(/\\[09].*$/, '');
                    if (Ee.call(t.map, y)) {
                        t = t.map[y];
                        break;
                    }
                }
                t = k;
                break;
            case 'Generic': {
                let y = c !== null ? c.opts : null,
                    T = V + Math.floor(t.fn(q, n, y));
                if (!isNaN(T) && T > V) {
                    while (V < T) e();
                    t = M;
                } else t = k;
                break;
            }
            case 'Type':
            case 'Property': {
                let y = t.type === 'Type' ? 'types' : 'properties',
                    T = Ee.call(i, y) ? i[y][t.name] : null;
                if (!T || !T.match)
                    throw new Error(
                        'Bad syntax reference: ' +
                            (t.type === 'Type' ? '<' + t.name + '>' : "<'" + t.name + "'>"),
                    );
                if (J !== !1 && q !== null && t.type === 'Type') {
                    if (
                        (t.name === 'custom-ident' && q.type === w) ||
                        (t.name === 'length' && q.value === '0')
                    ) {
                        if (J === null) J = o(t, u);
                        t = k;
                        break;
                    }
                }
                (f(), (t = T.matchRef || T.match));
                break;
            }
            case 'Keyword': {
                let y = t.name;
                if (q !== null) {
                    let T = q.value;
                    if (T.indexOf('\\') !== -1) T = T.replace(/\\[09].*$/, '');
                    if (Qn(T, y)) {
                        (e(), (t = M));
                        break;
                    }
                }
                t = k;
                break;
            }
            case 'AtKeyword':
            case 'Function':
                if (q !== null && Qn(q.value, t.name)) {
                    (e(), (t = M));
                    break;
                }
                t = k;
                break;
            case 'Token':
                if (q !== null && q.value === t.value) {
                    (e(), (t = M));
                    break;
                }
                t = k;
                break;
            case 'Comma':
                if (q !== null && q.type === d)
                    if (Le(K.token)) t = k;
                    else (e(), (t = Be(q) ? k : M));
                else t = Le(K.token) || Be(q) ? M : k;
                break;
            case 'String':
                let G = '',
                    L = V;
                for (; L < l.length && G.length < t.value.length; L++) G += l[L].value;
                if (Qn(G, t.value)) {
                    while (V < L) e();
                    t = M;
                } else t = k;
                break;
            default:
                throw new Error('Unknown node type: ' + t.type);
        }
    switch (((Vp += I), R)) {
        case null:
            (console.warn('[csstree-match] BREAK after ' + Ge + ' iterations'),
                (R = Rp),
                (K = null));
            break;
        case Qe:
            while (c !== null) h();
            break;
        default:
            K = null;
    }
    return { tokens: l, reason: R, iterations: I, match: K, longestMatch: fl };
}
function Ln(l, t, i) {
    let r = Mp(l, t, i || {});
    if (r.match === null) return r;
    let n = r.match,
        o = (r.match = { syntax: t.syntax || null, match: [] }),
        b = [o];
    n = Kp(n).prev;
    while (n !== null) {
        switch (n.type) {
            case Gn:
                (o.match.push((o = { syntax: n.syntax, match: [] })), b.push(o));
                break;
            case Fe:
                (b.pop(), (o = b[b.length - 1]));
                break;
            default:
                o.match.push({
                    syntax: n.syntax || null,
                    token: n.token.value,
                    node: n.token.node,
                });
        }
        n = n.prev;
    }
    return r;
}
var Fn = {};
D(Fn, { isType: () => yp, isProperty: () => Ap, isKeyword: () => Up, getTrace: () => Re });
function Re(l) {
    function t(n) {
        if (n === null) return !1;
        return n.type === 'Type' || n.type === 'Property' || n.type === 'Keyword';
    }
    function i(n) {
        if (Array.isArray(n.match)) {
            for (let o = 0; o < n.match.length; o++)
                if (i(n.match[o])) {
                    if (t(n.syntax)) r.unshift(n.syntax);
                    return !0;
                }
        } else if (n.node === l) return ((r = t(n.syntax) ? [n.syntax] : []), !0);
        return !1;
    }
    let r = null;
    if (this.matched !== null) i(this.matched);
    return r;
}
function yp(l, t) {
    return Bn(this, l, (i) => i.type === 'Type' && i.name === t);
}
function Ap(l, t) {
    return Bn(this, l, (i) => i.type === 'Property' && i.name === t);
}
function Up(l) {
    return Bn(this, l, (t) => t.type === 'Keyword');
}
function Bn(l, t, i) {
    let r = Re.call(l, t);
    if (r === null) return !1;
    return r.some(i);
}
function Ve(l) {
    if ('node' in l) return l.node;
    return Ve(l.match[0]);
}
function Ke(l) {
    if ('node' in l) return l.node;
    return Ke(l.match[l.match.length - 1]);
}
function Rn(l, t, i, r, n) {
    function o(g) {
        if (g.syntax !== null && g.syntax.type === r && g.syntax.name === n) {
            let e = Ve(g),
                f = Ke(g);
            l.syntax.walk(t, function (h, c, m) {
                if (h === e) {
                    let u = new ll();
                    do {
                        if ((u.appendData(c.data), c.data === f)) break;
                        c = c.next;
                    } while (c !== null);
                    b.push({ parent: m, nodes: u });
                }
            });
        }
        if (Array.isArray(g.match)) g.match.forEach(o);
    }
    let b = [];
    if (i.matched !== null) o(i.matched);
    return b;
}
var { hasOwnProperty: Ei } = Object.prototype;
function Vn(l) {
    return typeof l === 'number' && isFinite(l) && Math.floor(l) === l && l >= 0;
}
function He(l) {
    return Boolean(l) && Vn(l.offset) && Vn(l.line) && Vn(l.column);
}
function kp(l, t) {
    return function i(r, n) {
        if (!r || r.constructor !== Object) return n(r, 'Type of node should be an Object');
        for (let o in r) {
            let b = !0;
            if (Ei.call(r, o) === !1) continue;
            if (o === 'type') {
                if (r.type !== l) n(r, 'Wrong node type `' + r.type + '`, expected `' + l + '`');
            } else if (o === 'loc') {
                if (r.loc === null) continue;
                else if (r.loc && r.loc.constructor === Object)
                    if (typeof r.loc.source !== 'string') o += '.source';
                    else if (!He(r.loc.start)) o += '.start';
                    else if (!He(r.loc.end)) o += '.end';
                    else continue;
                b = !1;
            } else if (t.hasOwnProperty(o)) {
                b = !1;
                for (let g = 0; !b && g < t[o].length; g++) {
                    let e = t[o][g];
                    switch (e) {
                        case String:
                            b = typeof r[o] === 'string';
                            break;
                        case Boolean:
                            b = typeof r[o] === 'boolean';
                            break;
                        case null:
                            b = r[o] === null;
                            break;
                        default:
                            if (typeof e === 'string') b = r[o] && r[o].type === e;
                            else if (Array.isArray(e)) b = r[o] instanceof ll;
                    }
                }
            } else n(r, 'Unknown field `' + o + '` for ' + l + ' node type');
            if (!b) n(r, 'Bad value for `' + l + '.' + o + '`');
        }
        for (let o in t)
            if (Ei.call(t, o) && Ei.call(r, o) === !1)
                n(r, 'Field `' + l + '.' + o + '` is missed');
    };
}
function Me(l, t) {
    let i = [];
    for (let r = 0; r < l.length; r++) {
        let n = l[r];
        if (n === String || n === Boolean) i.push(n.name.toLowerCase());
        else if (n === null) i.push('null');
        else if (typeof n === 'string') i.push(n);
        else if (Array.isArray(n)) i.push('List<' + (Me(n, t) || 'any') + '>');
        else throw new Error('Wrong value `' + n + '` in `' + t + '` structure definition');
    }
    return i.join(' | ');
}
function Sp(l, t) {
    let i = t.structure,
        r = { type: String, loc: !0 },
        n = { type: '"' + l + '"' };
    for (let o in i) {
        if (Ei.call(i, o) === !1) continue;
        let b = (r[o] = Array.isArray(i[o]) ? i[o].slice() : [i[o]]);
        n[o] = Me(b, l + '.' + o);
    }
    return { docs: n, check: kp(l, r) };
}
function ye(l) {
    let t = {};
    if (l.node) {
        for (let i in l.node)
            if (Ei.call(l.node, i)) {
                let r = l.node[i];
                if (r.structure) t[i] = Sp(i, r);
                else
                    throw new Error('Missed `structure` field in `' + i + '` node type definition');
            }
    }
    return t;
}
function Kn(l, t, i) {
    let r = {};
    for (let n in l) if (l[n].syntax) r[n] = i ? l[n].syntax : Kt(l[n].syntax, { compact: t });
    return r;
}
function Np(l, t, i) {
    let r = {};
    for (let [n, o] of Object.entries(l))
        r[n] = {
            prelude: o.prelude && (i ? o.prelude.syntax : Kt(o.prelude.syntax, { compact: t })),
            descriptors: o.descriptors && Kn(o.descriptors, t, i),
        };
    return r;
}
function Cp(l) {
    for (let t = 0; t < l.length; t++) if (l[t].value.toLowerCase() === 'var(') return !0;
    return !1;
}
function Pp(l) {
    let t = l.terms[0];
    return l.explicit === !1 && l.terms.length === 1 && t.type === 'Multiplier' && t.comma === !0;
}
function Ql(l, t, i) {
    return { matched: l, iterations: i, error: t, ...Fn };
}
function kt(l, t, i, r) {
    let n = Yn(i, l.syntax),
        o;
    if (Cp(n)) return Ql(null, new Error('Matching for a tree with var() is not supported'));
    if (r) o = Ln(n, l.cssWideKeywordsSyntax, l);
    if (!r || !o.match) {
        if (((o = Ln(n, t.match, l)), !o.match))
            return Ql(null, new fe(o.reason, t.syntax, i, o), o.iterations);
    }
    return Ql(o.match, null, o.iterations);
}
class Qi {
    constructor(l, t, i) {
        if (
            ((this.cssWideKeywords = yt),
            (this.syntax = t),
            (this.generic = !1),
            (this.units = { ...ar }),
            (this.atrules = Object.create(null)),
            (this.properties = Object.create(null)),
            (this.types = Object.create(null)),
            (this.structure = i || ye(l)),
            l)
        ) {
            if (l.cssWideKeywords) this.cssWideKeywords = l.cssWideKeywords;
            if (l.units) {
                for (let r of Object.keys(ar))
                    if (Array.isArray(l.units[r])) this.units[r] = l.units[r];
            }
            if (l.types) for (let [r, n] of Object.entries(l.types)) this.addType_(r, n);
            if (l.generic) {
                this.generic = !0;
                for (let [r, n] of Object.entries(ze(this.units))) this.addType_(r, n);
            }
            if (l.atrules) for (let [r, n] of Object.entries(l.atrules)) this.addAtrule_(r, n);
            if (l.properties)
                for (let [r, n] of Object.entries(l.properties)) this.addProperty_(r, n);
        }
        this.cssWideKeywordsSyntax = Yi(this.cssWideKeywords.join(' |  '));
    }
    checkStructure(l) {
        function t(n, o) {
            r.push({ node: n, message: o });
        }
        let i = this.structure,
            r = [];
        return (
            this.syntax.walk(l, function (n) {
                if (i.hasOwnProperty(n.type)) i[n.type].check(n, t);
                else t(n, 'Unknown node type `' + n.type + '`');
            }),
            r.length ? r : !1
        );
    }
    createDescriptor(l, t, i, r = null) {
        let n = { type: t, name: i },
            o = {
                type: t,
                name: i,
                parent: r,
                serializable: typeof l === 'string' || (l && typeof l.type === 'string'),
                syntax: null,
                match: null,
                matchRef: null,
            };
        if (typeof l === 'function') o.match = Yi(l, n);
        else {
            if (typeof l === 'string')
                Object.defineProperty(o, 'syntax', {
                    get() {
                        return (Object.defineProperty(o, 'syntax', { value: Xi(l) }), o.syntax);
                    },
                });
            else o.syntax = l;
            if (
                (Object.defineProperty(o, 'match', {
                    get() {
                        return (
                            Object.defineProperty(o, 'match', { value: Yi(o.syntax, n) }),
                            o.match
                        );
                    },
                }),
                t === 'Property')
            )
                Object.defineProperty(o, 'matchRef', {
                    get() {
                        let b = o.syntax,
                            g = Pp(b) ? Yi({ ...b, terms: [b.terms[0].term] }, n) : null;
                        return (Object.defineProperty(o, 'matchRef', { value: g }), g);
                    },
                });
        }
        return o;
    }
    addAtrule_(l, t) {
        if (!t) return;
        this.atrules[l] = {
            type: 'Atrule',
            name: l,
            prelude: t.prelude ? this.createDescriptor(t.prelude, 'AtrulePrelude', l) : null,
            descriptors: t.descriptors
                ? Object.keys(t.descriptors).reduce((i, r) => {
                      return (
                          (i[r] = this.createDescriptor(
                              t.descriptors[r],
                              'AtruleDescriptor',
                              r,
                              l,
                          )),
                          i
                      );
                  }, Object.create(null))
                : null,
        };
    }
    addProperty_(l, t) {
        if (!t) return;
        this.properties[l] = this.createDescriptor(t, 'Property', l);
    }
    addType_(l, t) {
        if (!t) return;
        this.types[l] = this.createDescriptor(t, 'Type', l);
    }
    checkAtruleName(l) {
        if (!this.getAtrule(l)) return new Ht('Unknown at-rule', '@' + l);
    }
    checkAtrulePrelude(l, t) {
        let i = this.checkAtruleName(l);
        if (i) return i;
        let r = this.getAtrule(l);
        if (!r.prelude && t)
            return new SyntaxError('At-rule `@' + l + '` should not contain a prelude');
        if (r.prelude && !t) {
            if (!kt(this, r.prelude, '', !1).matched)
                return new SyntaxError('At-rule `@' + l + '` should contain a prelude');
        }
    }
    checkAtruleDescriptorName(l, t) {
        let i = this.checkAtruleName(l);
        if (i) return i;
        let r = this.getAtrule(l),
            n = pr(t);
        if (!r.descriptors) return new SyntaxError('At-rule `@' + l + '` has no known descriptors');
        if (!r.descriptors[n.name] && !r.descriptors[n.basename])
            return new Ht('Unknown at-rule descriptor', t);
    }
    checkPropertyName(l) {
        if (!this.getProperty(l)) return new Ht('Unknown property', l);
    }
    matchAtrulePrelude(l, t) {
        let i = this.checkAtrulePrelude(l, t);
        if (i) return Ql(null, i);
        let r = this.getAtrule(l);
        if (!r.prelude) return Ql(null, null);
        return kt(this, r.prelude, t || '', !1);
    }
    matchAtruleDescriptor(l, t, i) {
        let r = this.checkAtruleDescriptorName(l, t);
        if (r) return Ql(null, r);
        let n = this.getAtrule(l),
            o = pr(t);
        return kt(this, n.descriptors[o.name] || n.descriptors[o.basename], i, !1);
    }
    matchDeclaration(l) {
        if (l.type !== 'Declaration') return Ql(null, new Error('Not a Declaration node'));
        return this.matchProperty(l.property, l.value);
    }
    matchProperty(l, t) {
        if (cn(l).custom)
            return Ql(null, new Error("Lexer matching doesn't applicable for custom properties"));
        let i = this.checkPropertyName(l);
        if (i) return Ql(null, i);
        return kt(this, this.getProperty(l), t, !0);
    }
    matchType(l, t) {
        let i = this.getType(l);
        if (!i) return Ql(null, new Ht('Unknown type', l));
        return kt(this, i, t, !1);
    }
    match(l, t) {
        if (typeof l !== 'string' && (!l || !l.type)) return Ql(null, new Ht('Bad syntax'));
        if (typeof l === 'string' || !l.match) l = this.createDescriptor(l, 'Type', 'anonymous');
        return kt(this, l, t, !1);
    }
    findValueFragments(l, t, i, r) {
        return Rn(this, t, this.matchProperty(l, t), i, r);
    }
    findDeclarationValueFragments(l, t, i) {
        return Rn(this, l.value, this.matchDeclaration(l), t, i);
    }
    findAllFragments(l, t, i) {
        let r = [];
        return (
            this.syntax.walk(l, {
                visit: 'Declaration',
                enter: (n) => {
                    r.push.apply(r, this.findDeclarationValueFragments(n, t, i));
                },
            }),
            r
        );
    }
    getAtrule(l, t = !0) {
        let i = pr(l);
        return (
            (i.vendor && t
                ? this.atrules[i.name] || this.atrules[i.basename]
                : this.atrules[i.name]) || null
        );
    }
    getAtrulePrelude(l, t = !0) {
        let i = this.getAtrule(l, t);
        return (i && i.prelude) || null;
    }
    getAtruleDescriptor(l, t) {
        return this.atrules.hasOwnProperty(l) && this.atrules.declarators
            ? this.atrules[l].declarators[t] || null
            : null;
    }
    getProperty(l, t = !0) {
        let i = cn(l);
        return (
            (i.vendor && t
                ? this.properties[i.name] || this.properties[i.basename]
                : this.properties[i.name]) || null
        );
    }
    getType(l) {
        return hasOwnProperty.call(this.types, l) ? this.types[l] : null;
    }
    validate() {
        function l(g, e) {
            return e ? `<${g}>` : `<'${g}'>`;
        }
        function t(g, e, f, h) {
            if (f.has(e)) return f.get(e);
            if ((f.set(e, !1), h.syntax !== null))
                jn(
                    h.syntax,
                    function (c) {
                        if (c.type !== 'Type' && c.type !== 'Property') return;
                        let m = c.type === 'Type' ? g.types : g.properties,
                            u = c.type === 'Type' ? r : n;
                        if (!hasOwnProperty.call(m, c.name))
                            (i.push(
                                `${l(e, f === r)} used missed syntax definition ${l(c.name, c.type === 'Type')}`,
                            ),
                                f.set(e, !0));
                        else if (t(g, c.name, u, m[c.name]))
                            (i.push(
                                `${l(e, f === r)} used broken syntax definition ${l(c.name, c.type === 'Type')}`,
                            ),
                                f.set(e, !0));
                    },
                    this,
                );
        }
        let i = [],
            r = new Map(),
            n = new Map();
        for (let g in this.types) t(this, g, r, this.types[g]);
        for (let g in this.properties) t(this, g, n, this.properties[g]);
        let o = [...r.keys()].filter((g) => r.get(g)),
            b = [...n.keys()].filter((g) => n.get(g));
        if (o.length || b.length) return { errors: i, types: o, properties: b };
        return null;
    }
    dump(l, t) {
        return {
            generic: this.generic,
            cssWideKeywords: this.cssWideKeywords,
            units: this.units,
            types: Kn(this.types, !t, l),
            properties: Kn(this.properties, !t, l),
            atrules: Np(this.atrules, !t, l),
        };
    }
    toString() {
        return JSON.stringify(this.dump());
    }
}
function Hn(l, t) {
    if (typeof t === 'string' && /^\s*\|/.test(t))
        return typeof l === 'string' ? l + t : t.replace(/^\s*\|\s*/, '');
    return t || null;
}
function Ae(l, t) {
    let i = Object.create(null);
    for (let [r, n] of Object.entries(l))
        if (n) {
            i[r] = {};
            for (let o of Object.keys(n)) if (t.includes(o)) i[r][o] = n[o];
        }
    return i;
}
function Gi(l, t) {
    let i = { ...l };
    for (let [r, n] of Object.entries(t))
        switch (r) {
            case 'generic':
                i[r] = Boolean(n);
                break;
            case 'cssWideKeywords':
                i[r] = l[r] ? [...l[r], ...n] : n || [];
                break;
            case 'units':
                i[r] = { ...l[r] };
                for (let [o, b] of Object.entries(n)) i[r][o] = Array.isArray(b) ? b : [];
                break;
            case 'atrules':
                i[r] = { ...l[r] };
                for (let [o, b] of Object.entries(n)) {
                    let g = i[r][o] || {},
                        e = (i[r][o] = {
                            prelude: g.prelude || null,
                            descriptors: { ...g.descriptors },
                        });
                    if (!b) continue;
                    e.prelude = b.prelude ? Hn(e.prelude, b.prelude) : e.prelude || null;
                    for (let [f, h] of Object.entries(b.descriptors || {}))
                        e.descriptors[f] = h ? Hn(e.descriptors[f], h) : null;
                    if (!Object.keys(e.descriptors).length) e.descriptors = null;
                }
                break;
            case 'types':
            case 'properties':
                i[r] = { ...l[r] };
                for (let [o, b] of Object.entries(n)) i[r][o] = Hn(i[r][o], b);
                break;
            case 'scope':
            case 'features':
                i[r] = { ...l[r] };
                for (let [o, b] of Object.entries(n)) i[r][o] = { ...i[r][o], ...b };
                break;
            case 'parseContext':
                i[r] = { ...l[r], ...n };
                break;
            case 'atrule':
            case 'pseudo':
                i[r] = { ...l[r], ...Ae(n, ['parse']) };
                break;
            case 'node':
                i[r] = {
                    ...l[r],
                    ...Ae(n, ['name', 'structure', 'parse', 'generate', 'walkContext']),
                };
                break;
        }
    return i;
}
function Ue(l) {
    let t = Gg(l),
        i = oe(l),
        r = dg(l),
        { fromPlainObject: n, toPlainObject: o } = le(i),
        b = {
            lexer: null,
            createLexer: (g) => new Qi(g, b, b.lexer.structure),
            tokenize: bt,
            parse: t,
            generate: r,
            walk: i,
            find: i.find,
            findLast: i.findLast,
            findAll: i.findAll,
            fromPlainObject: n,
            toPlainObject: o,
            fork(g) {
                let e = Gi({}, l);
                return Ue(typeof g === 'function' ? g(e) : Gi(e, g));
            },
        };
    return (
        (b.lexer = new Qi(
            {
                generic: l.generic,
                cssWideKeywords: l.cssWideKeywords,
                units: l.units,
                types: l.types,
                atrules: l.atrules,
                properties: l.properties,
                node: l.node,
            },
            b,
        )),
        b
    );
}
var Mn = (l) => Ue(Gi({}, l));
var ke = {
    generic: !0,
    cssWideKeywords: ['initial', 'inherit', 'unset', 'revert', 'revert-layer'],
    units: {
        angle: ['deg', 'grad', 'rad', 'turn'],
        decibel: ['db'],
        flex: ['fr'],
        frequency: ['hz', 'khz'],
        length: [
            'cm',
            'mm',
            'q',
            'in',
            'pt',
            'pc',
            'px',
            'em',
            'rem',
            'ex',
            'rex',
            'cap',
            'rcap',
            'ch',
            'rch',
            'ic',
            'ric',
            'lh',
            'rlh',
            'vw',
            'svw',
            'lvw',
            'dvw',
            'vh',
            'svh',
            'lvh',
            'dvh',
            'vi',
            'svi',
            'lvi',
            'dvi',
            'vb',
            'svb',
            'lvb',
            'dvb',
            'vmin',
            'svmin',
            'lvmin',
            'dvmin',
            'vmax',
            'svmax',
            'lvmax',
            'dvmax',
            'cqw',
            'cqh',
            'cqi',
            'cqb',
            'cqmin',
            'cqmax',
        ],
        resolution: ['dpi', 'dpcm', 'dppx', 'x'],
        semitones: ['st'],
        time: ['s', 'ms'],
    },
    types: {
        'abs()': 'abs( <calc-sum> )',
        'absolute-size': 'xx-small|x-small|small|medium|large|x-large|xx-large|xxx-large',
        'acos()': 'acos( <calc-sum> )',
        'alpha-value': '<number>|<percentage>',
        'angle-percentage': '<angle>|<percentage>',
        'angular-color-hint': '<angle-percentage>',
        'angular-color-stop': '<color>&&<color-stop-angle>?',
        'angular-color-stop-list':
            '[<angular-color-stop> [, <angular-color-hint>]?]# , <angular-color-stop>',
        'animateable-feature': 'scroll-position|contents|<custom-ident>',
        'asin()': 'asin( <calc-sum> )',
        'atan()': 'atan( <calc-sum> )',
        'atan2()': 'atan2( <calc-sum> , <calc-sum> )',
        attachment: 'scroll|fixed|local',
        'attr()': 'attr( <attr-name> <type-or-unit>? [, <attr-fallback>]? )',
        'attr-matcher': "['~'|'|'|'^'|'$'|'*']? '='",
        'attr-modifier': 'i|s',
        'attribute-selector':
            "'[' <wq-name> ']'|'[' <wq-name> <attr-matcher> [<string-token>|<ident-token>] <attr-modifier>? ']'",
        'auto-repeat':
            'repeat( [auto-fill|auto-fit] , [<line-names>? <fixed-size>]+ <line-names>? )',
        'auto-track-list':
            '[<line-names>? [<fixed-size>|<fixed-repeat>]]* <line-names>? <auto-repeat> [<line-names>? [<fixed-size>|<fixed-repeat>]]* <line-names>?',
        axis: 'block|inline|x|y',
        'baseline-position': '[first|last]? baseline',
        'basic-shape': '<inset()>|<xywh()>|<rect()>|<circle()>|<ellipse()>|<polygon()>|<path()>',
        'bg-image': 'none|<image>',
        'bg-layer':
            '<bg-image>||<bg-position> [/ <bg-size>]?||<repeat-style>||<attachment>||<box>||<box>',
        'bg-position':
            '[[left|center|right|top|bottom|<length-percentage>]|[left|center|right|<length-percentage>] [top|center|bottom|<length-percentage>]|[center|[left|right] <length-percentage>?]&&[center|[top|bottom] <length-percentage>?]]',
        'bg-size': '[<length-percentage>|auto]{1,2}|cover|contain',
        'blur()': 'blur( <length> )',
        'blend-mode':
            'normal|multiply|screen|overlay|darken|lighten|color-dodge|color-burn|hard-light|soft-light|difference|exclusion|hue|saturation|color|luminosity',
        box: 'border-box|padding-box|content-box',
        'brightness()': 'brightness( <number-percentage> )',
        'calc()': 'calc( <calc-sum> )',
        'calc-sum': "<calc-product> [['+'|'-'] <calc-product>]*",
        'calc-product': "<calc-value> ['*' <calc-value>|'/' <number>]*",
        'calc-value': '<number>|<dimension>|<percentage>|<calc-constant>|( <calc-sum> )',
        'calc-constant': 'e|pi|infinity|-infinity|NaN',
        'cf-final-image': '<image>|<color>',
        'cf-mixing-image': '<percentage>?&&<image>',
        'circle()': 'circle( [<shape-radius>]? [at <position>]? )',
        'clamp()': 'clamp( <calc-sum>#{3} )',
        'class-selector': "'.' <ident-token>",
        'clip-source': '<url>',
        color: '<color-base>|currentColor|<system-color>|<device-cmyk()>|<light-dark()>|<-non-standard-color>',
        'color-stop': '<color-stop-length>|<color-stop-angle>',
        'color-stop-angle': '<angle-percentage>{1,2}',
        'color-stop-length': '<length-percentage>{1,2}',
        'color-stop-list': '[<linear-color-stop> [, <linear-color-hint>]?]# , <linear-color-stop>',
        'color-interpolation-method':
            'in [<rectangular-color-space>|<polar-color-space> <hue-interpolation-method>?|<custom-color-space>]',
        combinator: "'>'|'+'|'~'|['|' '|']",
        'common-lig-values': '[common-ligatures|no-common-ligatures]',
        'compat-auto':
            'searchfield|textarea|push-button|slider-horizontal|checkbox|radio|square-button|menulist|listbox|meter|progress-bar|button',
        'composite-style':
            'clear|copy|source-over|source-in|source-out|source-atop|destination-over|destination-in|destination-out|destination-atop|xor',
        'compositing-operator': 'add|subtract|intersect|exclude',
        'compound-selector': '[<type-selector>? <subclass-selector>*]!',
        'compound-selector-list': '<compound-selector>#',
        'complex-selector': '<complex-selector-unit> [<combinator>? <complex-selector-unit>]*',
        'complex-selector-list': '<complex-selector>#',
        'conic-gradient()':
            'conic-gradient( [from <angle>]? [at <position>]? , <angular-color-stop-list> )',
        'contextual-alt-values': '[contextual|no-contextual]',
        'content-distribution': 'space-between|space-around|space-evenly|stretch',
        'content-list':
            '[<string>|contents|<image>|<counter>|<quote>|<target>|<leader()>|<attr()>]+',
        'content-position': 'center|start|end|flex-start|flex-end',
        'content-replacement': '<image>',
        'contrast()': 'contrast( [<number-percentage>] )',
        'cos()': 'cos( <calc-sum> )',
        counter: '<counter()>|<counters()>',
        'counter()': 'counter( <counter-name> , <counter-style>? )',
        'counter-name': '<custom-ident>',
        'counter-style': '<counter-style-name>|symbols( )',
        'counter-style-name': '<custom-ident>',
        'counters()': 'counters( <counter-name> , <string> , <counter-style>? )',
        'cross-fade()': 'cross-fade( <cf-mixing-image> , <cf-final-image>? )',
        'cubic-bezier-timing-function':
            'ease|ease-in|ease-out|ease-in-out|cubic-bezier( <number [0,1]> , <number> , <number [0,1]> , <number> )',
        'deprecated-system-color':
            'ActiveBorder|ActiveCaption|AppWorkspace|Background|ButtonFace|ButtonHighlight|ButtonShadow|ButtonText|CaptionText|GrayText|Highlight|HighlightText|InactiveBorder|InactiveCaption|InactiveCaptionText|InfoBackground|InfoText|Menu|MenuText|Scrollbar|ThreeDDarkShadow|ThreeDFace|ThreeDHighlight|ThreeDLightShadow|ThreeDShadow|Window|WindowFrame|WindowText',
        'discretionary-lig-values': '[discretionary-ligatures|no-discretionary-ligatures]',
        'display-box': 'contents|none',
        'display-inside': 'flow|flow-root|table|flex|grid|ruby',
        'display-internal':
            'table-row-group|table-header-group|table-footer-group|table-row|table-cell|table-column-group|table-column|table-caption|ruby-base|ruby-text|ruby-base-container|ruby-text-container',
        'display-legacy': 'inline-block|inline-list-item|inline-table|inline-flex|inline-grid',
        'display-listitem': '<display-outside>?&&[flow|flow-root]?&&list-item',
        'display-outside': 'block|inline|run-in',
        'drop-shadow()': 'drop-shadow( <length>{2,3} <color>? )',
        'east-asian-variant-values': '[jis78|jis83|jis90|jis04|simplified|traditional]',
        'east-asian-width-values': '[full-width|proportional-width]',
        'element()':
            'element( <custom-ident> , [first|start|last|first-except]? )|element( <id-selector> )',
        'ellipse()': 'ellipse( [<shape-radius>{2}]? [at <position>]? )',
        'ending-shape': 'circle|ellipse',
        'env()': 'env( <custom-ident> , <declaration-value>? )',
        'exp()': 'exp( <calc-sum> )',
        'explicit-track-list': '[<line-names>? <track-size>]+ <line-names>?',
        'family-name': '<string>|<custom-ident>+',
        'feature-tag-value': '<string> [<integer>|on|off]?',
        'feature-type':
            '@stylistic|@historical-forms|@styleset|@character-variant|@swash|@ornaments|@annotation',
        'feature-value-block': "<feature-type> '{' <feature-value-declaration-list> '}'",
        'feature-value-block-list': '<feature-value-block>+',
        'feature-value-declaration': '<custom-ident> : <integer>+ ;',
        'feature-value-declaration-list': '<feature-value-declaration>',
        'feature-value-name': '<custom-ident>',
        'fill-rule': 'nonzero|evenodd',
        'filter-function':
            '<blur()>|<brightness()>|<contrast()>|<drop-shadow()>|<grayscale()>|<hue-rotate()>|<invert()>|<opacity()>|<saturate()>|<sepia()>',
        'filter-function-list': '[<filter-function>|<url>]+',
        'final-bg-layer':
            "<'background-color'>||<bg-image>||<bg-position> [/ <bg-size>]?||<repeat-style>||<attachment>||<box>||<box>",
        'fixed-breadth': '<length-percentage>',
        'fixed-repeat': 'repeat( [<integer [1,∞]>] , [<line-names>? <fixed-size>]+ <line-names>? )',
        'fixed-size':
            '<fixed-breadth>|minmax( <fixed-breadth> , <track-breadth> )|minmax( <inflexible-breadth> , <fixed-breadth> )',
        'font-stretch-absolute':
            'normal|ultra-condensed|extra-condensed|condensed|semi-condensed|semi-expanded|expanded|extra-expanded|ultra-expanded|<percentage>',
        'font-variant-css21': '[normal|small-caps]',
        'font-weight-absolute': 'normal|bold|<number [1,1000]>',
        'frequency-percentage': '<frequency>|<percentage>',
        'general-enclosed': '[<function-token> <any-value>? )]|[( <any-value>? )]',
        'generic-family':
            '<generic-script-specific>|<generic-complete>|<generic-incomplete>|<-non-standard-generic-family>',
        'generic-name': 'serif|sans-serif|cursive|fantasy|monospace',
        'geometry-box': '<shape-box>|fill-box|stroke-box|view-box',
        gradient:
            '<linear-gradient()>|<repeating-linear-gradient()>|<radial-gradient()>|<repeating-radial-gradient()>|<conic-gradient()>|<repeating-conic-gradient()>|<-legacy-gradient>',
        'grayscale()': 'grayscale( <number-percentage> )',
        'grid-line':
            'auto|<custom-ident>|[<integer>&&<custom-ident>?]|[span&&[<integer>||<custom-ident>]]',
        'historical-lig-values': '[historical-ligatures|no-historical-ligatures]',
        'hsl()':
            'hsl( <hue> <percentage> <percentage> [/ <alpha-value>]? )|hsl( <hue> , <percentage> , <percentage> , <alpha-value>? )',
        'hsla()':
            'hsla( <hue> <percentage> <percentage> [/ <alpha-value>]? )|hsla( <hue> , <percentage> , <percentage> , <alpha-value>? )',
        hue: '<number>|<angle>',
        'hue-rotate()': 'hue-rotate( <angle> )',
        'hue-interpolation-method': '[shorter|longer|increasing|decreasing] hue',
        'hwb()':
            'hwb( [<hue>|none] [<percentage>|none] [<percentage>|none] [/ [<alpha-value>|none]]? )',
        'hypot()': 'hypot( <calc-sum># )',
        image: '<url>|<image()>|<image-set()>|<element()>|<paint()>|<cross-fade()>|<gradient>',
        'image()': 'image( <image-tags>? [<image-src>? , <color>?]! )',
        'image-set()': 'image-set( <image-set-option># )',
        'image-set-option': '[<image>|<string>] [<resolution>||type( <string> )]',
        'image-src': '<url>|<string>',
        'image-tags': 'ltr|rtl',
        'inflexible-breadth': '<length-percentage>|min-content|max-content|auto',
        'inset()': "inset( <length-percentage>{1,4} [round <'border-radius'>]? )",
        'invert()': 'invert( <number-percentage> )',
        'keyframes-name': '<custom-ident>|<string>',
        'keyframe-block': '<keyframe-selector># { <declaration-list> }',
        'keyframe-block-list': '<keyframe-block>+',
        'keyframe-selector': 'from|to|<percentage>|<timeline-range-name> <percentage>',
        'lab()':
            'lab( [<percentage>|<number>|none] [<percentage>|<number>|none] [<percentage>|<number>|none] [/ [<alpha-value>|none]]? )',
        'layer()': 'layer( <layer-name> )',
        'layer-name': "<ident> ['.' <ident>]*",
        'lch()':
            'lch( [<percentage>|<number>|none] [<percentage>|<number>|none] [<hue>|none] [/ [<alpha-value>|none]]? )',
        'leader()': 'leader( <leader-type> )',
        'leader-type': 'dotted|solid|space|<string>',
        'length-percentage': '<length>|<percentage>',
        'light-dark()': 'light-dark( <color> , <color> )',
        'line-names': "'[' <custom-ident>* ']'",
        'line-name-list': '[<line-names>|<name-repeat>]+',
        'line-style': 'none|hidden|dotted|dashed|solid|double|groove|ridge|inset|outset',
        'line-width': '<length>|thin|medium|thick',
        'linear-color-hint': '<length-percentage>',
        'linear-color-stop': '<color> <color-stop-length>?',
        'linear-gradient()':
            'linear-gradient( [[<angle>|to <side-or-corner>]||<color-interpolation-method>]? , <color-stop-list> )',
        'log()': 'log( <calc-sum> , <calc-sum>? )',
        'mask-layer':
            '<mask-reference>||<position> [/ <bg-size>]?||<repeat-style>||<geometry-box>||[<geometry-box>|no-clip]||<compositing-operator>||<masking-mode>',
        'mask-position':
            '[<length-percentage>|left|center|right] [<length-percentage>|top|center|bottom]?',
        'mask-reference': 'none|<image>|<mask-source>',
        'mask-source': '<url>',
        'masking-mode': 'alpha|luminance|match-source',
        'matrix()': 'matrix( <number>#{6} )',
        'matrix3d()': 'matrix3d( <number>#{16} )',
        'max()': 'max( <calc-sum># )',
        'media-and': '<media-in-parens> [and <media-in-parens>]+',
        'media-condition': '<media-not>|<media-and>|<media-or>|<media-in-parens>',
        'media-condition-without-or': '<media-not>|<media-and>|<media-in-parens>',
        'media-feature': '( [<mf-plain>|<mf-boolean>|<mf-range>] )',
        'media-in-parens': '( <media-condition> )|<media-feature>|<general-enclosed>',
        'media-not': 'not <media-in-parens>',
        'media-or': '<media-in-parens> [or <media-in-parens>]+',
        'media-query':
            '<media-condition>|[not|only]? <media-type> [and <media-condition-without-or>]?',
        'media-query-list': '<media-query>#',
        'media-type': '<ident>',
        'mf-boolean': '<mf-name>',
        'mf-name': '<ident>',
        'mf-plain': '<mf-name> : <mf-value>',
        'mf-range':
            "<mf-name> ['<'|'>']? '='? <mf-value>|<mf-value> ['<'|'>']? '='? <mf-name>|<mf-value> '<' '='? <mf-name> '<' '='? <mf-value>|<mf-value> '>' '='? <mf-name> '>' '='? <mf-value>",
        'mf-value': '<number>|<dimension>|<ident>|<ratio>',
        'min()': 'min( <calc-sum># )',
        'minmax()':
            'minmax( [<length-percentage>|min-content|max-content|auto] , [<length-percentage>|<flex>|min-content|max-content|auto] )',
        'mod()': 'mod( <calc-sum> , <calc-sum> )',
        'name-repeat': 'repeat( [<integer [1,∞]>|auto-fill] , <line-names>+ )',
        'named-color':
            'transparent|aliceblue|antiquewhite|aqua|aquamarine|azure|beige|bisque|black|blanchedalmond|blue|blueviolet|brown|burlywood|cadetblue|chartreuse|chocolate|coral|cornflowerblue|cornsilk|crimson|cyan|darkblue|darkcyan|darkgoldenrod|darkgray|darkgreen|darkgrey|darkkhaki|darkmagenta|darkolivegreen|darkorange|darkorchid|darkred|darksalmon|darkseagreen|darkslateblue|darkslategray|darkslategrey|darkturquoise|darkviolet|deeppink|deepskyblue|dimgray|dimgrey|dodgerblue|firebrick|floralwhite|forestgreen|fuchsia|gainsboro|ghostwhite|gold|goldenrod|gray|green|greenyellow|grey|honeydew|hotpink|indianred|indigo|ivory|khaki|lavender|lavenderblush|lawngreen|lemonchiffon|lightblue|lightcoral|lightcyan|lightgoldenrodyellow|lightgray|lightgreen|lightgrey|lightpink|lightsalmon|lightseagreen|lightskyblue|lightslategray|lightslategrey|lightsteelblue|lightyellow|lime|limegreen|linen|magenta|maroon|mediumaquamarine|mediumblue|mediumorchid|mediumpurple|mediumseagreen|mediumslateblue|mediumspringgreen|mediumturquoise|mediumvioletred|midnightblue|mintcream|mistyrose|moccasin|navajowhite|navy|oldlace|olive|olivedrab|orange|orangered|orchid|palegoldenrod|palegreen|paleturquoise|palevioletred|papayawhip|peachpuff|peru|pink|plum|powderblue|purple|rebeccapurple|red|rosybrown|royalblue|saddlebrown|salmon|sandybrown|seagreen|seashell|sienna|silver|skyblue|slateblue|slategray|slategrey|snow|springgreen|steelblue|tan|teal|thistle|tomato|turquoise|violet|wheat|white|whitesmoke|yellow|yellowgreen',
        'namespace-prefix': '<ident>',
        'ns-prefix': "[<ident-token>|'*']? '|'",
        'number-percentage': '<number>|<percentage>',
        'numeric-figure-values': '[lining-nums|oldstyle-nums]',
        'numeric-fraction-values': '[diagonal-fractions|stacked-fractions]',
        'numeric-spacing-values': '[proportional-nums|tabular-nums]',
        nth: '<an-plus-b>|even|odd',
        'opacity()': 'opacity( [<number-percentage>] )',
        'overflow-position': 'unsafe|safe',
        'outline-radius': '<length>|<percentage>',
        'page-body': '<declaration>? [; <page-body>]?|<page-margin-box> <page-body>',
        'page-margin-box': "<page-margin-box-type> '{' <declaration-list> '}'",
        'page-margin-box-type':
            '@top-left-corner|@top-left|@top-center|@top-right|@top-right-corner|@bottom-left-corner|@bottom-left|@bottom-center|@bottom-right|@bottom-right-corner|@left-top|@left-middle|@left-bottom|@right-top|@right-middle|@right-bottom',
        'page-selector-list': '[<page-selector>#]?',
        'page-selector': '<pseudo-page>+|<ident> <pseudo-page>*',
        'page-size': 'A5|A4|A3|B5|B4|JIS-B5|JIS-B4|letter|legal|ledger',
        'path()': 'path( [<fill-rule> ,]? <string> )',
        'paint()': 'paint( <ident> , <declaration-value>? )',
        'perspective()': 'perspective( [<length [0,∞]>|none] )',
        'polygon()': 'polygon( <fill-rule>? , [<length-percentage> <length-percentage>]# )',
        'polar-color-space': 'hsl|hwb|lch|oklch',
        position:
            '[[left|center|right]||[top|center|bottom]|[left|center|right|<length-percentage>] [top|center|bottom|<length-percentage>]?|[[left|right] <length-percentage>]&&[[top|bottom] <length-percentage>]]',
        'pow()': 'pow( <calc-sum> , <calc-sum> )',
        'pseudo-class-selector': "':' <ident-token>|':' <function-token> <any-value> ')'",
        'pseudo-element-selector': "':' <pseudo-class-selector>|<legacy-pseudo-element-selector>",
        'pseudo-page': ': [left|right|first|blank]',
        quote: 'open-quote|close-quote|no-open-quote|no-close-quote',
        'radial-gradient()':
            'radial-gradient( [<ending-shape>||<size>]? [at <position>]? , <color-stop-list> )',
        ratio: '<number [0,∞]> [/ <number [0,∞]>]?',
        'ray()': 'ray( <angle>&&<ray-size>?&&contain?&&[at <position>]? )',
        'ray-size': 'closest-side|closest-corner|farthest-side|farthest-corner|sides',
        'rectangular-color-space':
            'srgb|srgb-linear|display-p3|a98-rgb|prophoto-rgb|rec2020|lab|oklab|xyz|xyz-d50|xyz-d65',
        'relative-selector': '<combinator>? <complex-selector>',
        'relative-selector-list': '<relative-selector>#',
        'relative-size': 'larger|smaller',
        'rem()': 'rem( <calc-sum> , <calc-sum> )',
        'repeat-style': 'repeat-x|repeat-y|[repeat|space|round|no-repeat]{1,2}',
        'repeating-conic-gradient()':
            'repeating-conic-gradient( [from <angle>]? [at <position>]? , <angular-color-stop-list> )',
        'repeating-linear-gradient()':
            'repeating-linear-gradient( [<angle>|to <side-or-corner>]? , <color-stop-list> )',
        'repeating-radial-gradient()':
            'repeating-radial-gradient( [<ending-shape>||<size>]? [at <position>]? , <color-stop-list> )',
        'reversed-counter-name': 'reversed( <counter-name> )',
        'rgb()':
            'rgb( <percentage>{3} [/ <alpha-value>]? )|rgb( <number>{3} [/ <alpha-value>]? )|rgb( <percentage>#{3} , <alpha-value>? )|rgb( <number>#{3} , <alpha-value>? )',
        'rgba()':
            'rgba( <percentage>{3} [/ <alpha-value>]? )|rgba( <number>{3} [/ <alpha-value>]? )|rgba( <percentage>#{3} , <alpha-value>? )|rgba( <number>#{3} , <alpha-value>? )',
        'rotate()': 'rotate( [<angle>|<zero>] )',
        'rotate3d()': 'rotate3d( <number> , <number> , <number> , [<angle>|<zero>] )',
        'rotateX()': 'rotateX( [<angle>|<zero>] )',
        'rotateY()': 'rotateY( [<angle>|<zero>] )',
        'rotateZ()': 'rotateZ( [<angle>|<zero>] )',
        'round()': 'round( <rounding-strategy>? , <calc-sum> , <calc-sum> )',
        'rounding-strategy': 'nearest|up|down|to-zero',
        'saturate()': 'saturate( <number-percentage> )',
        'scale()': 'scale( [<number>|<percentage>]#{1,2} )',
        'scale3d()': 'scale3d( [<number>|<percentage>]#{3} )',
        'scaleX()': 'scaleX( [<number>|<percentage>] )',
        'scaleY()': 'scaleY( [<number>|<percentage>] )',
        'scaleZ()': 'scaleZ( [<number>|<percentage>] )',
        'scroll()': 'scroll( [<axis>||<scroller>]? )',
        scroller: 'root|nearest|self',
        'self-position': 'center|start|end|self-start|self-end|flex-start|flex-end',
        'shape-radius': '<length-percentage>|closest-side|farthest-side',
        'sign()': 'sign( <calc-sum> )',
        'skew()': 'skew( [<angle>|<zero>] , [<angle>|<zero>]? )',
        'skewX()': 'skewX( [<angle>|<zero>] )',
        'skewY()': 'skewY( [<angle>|<zero>] )',
        'sepia()': 'sepia( <number-percentage> )',
        shadow: 'inset?&&<length>{2,4}&&<color>?',
        'shadow-t': '[<length>{2,3}&&<color>?]',
        shape: 'rect( <top> , <right> , <bottom> , <left> )|rect( <top> <right> <bottom> <left> )',
        'shape-box': '<box>|margin-box',
        'side-or-corner': '[left|right]||[top|bottom]',
        'sin()': 'sin( <calc-sum> )',
        'single-animation':
            "<'animation-duration'>||<easing-function>||<'animation-delay'>||<single-animation-iteration-count>||<single-animation-direction>||<single-animation-fill-mode>||<single-animation-play-state>||[none|<keyframes-name>]||<single-animation-timeline>",
        'single-animation-direction': 'normal|reverse|alternate|alternate-reverse',
        'single-animation-fill-mode': 'none|forwards|backwards|both',
        'single-animation-iteration-count': 'infinite|<number>',
        'single-animation-play-state': 'running|paused',
        'single-animation-timeline': 'auto|none|<dashed-ident>|<scroll()>|<view()>',
        'single-transition':
            '[none|<single-transition-property>]||<time>||<easing-function>||<time>||<transition-behavior-value>',
        'single-transition-property': 'all|<custom-ident>',
        size: 'closest-side|farthest-side|closest-corner|farthest-corner|<length>|<length-percentage>{2}',
        'sqrt()': 'sqrt( <calc-sum> )',
        'step-position': 'jump-start|jump-end|jump-none|jump-both|start|end',
        'step-timing-function': 'step-start|step-end|steps( <integer> [, <step-position>]? )',
        'subclass-selector':
            '<id-selector>|<class-selector>|<attribute-selector>|<pseudo-class-selector>',
        'supports-condition':
            'not <supports-in-parens>|<supports-in-parens> [and <supports-in-parens>]*|<supports-in-parens> [or <supports-in-parens>]*',
        'supports-in-parens': '( <supports-condition> )|<supports-feature>|<general-enclosed>',
        'supports-feature': '<supports-decl>|<supports-selector-fn>',
        'supports-decl': '( <declaration> )',
        'supports-selector-fn': 'selector( <complex-selector> )',
        symbol: '<string>|<image>|<custom-ident>',
        'system-color':
            'AccentColor|AccentColorText|ActiveText|ButtonBorder|ButtonFace|ButtonText|Canvas|CanvasText|Field|FieldText|GrayText|Highlight|HighlightText|LinkText|Mark|MarkText|SelectedItem|SelectedItemText|VisitedText',
        'tan()': 'tan( <calc-sum> )',
        target: '<target-counter()>|<target-counters()>|<target-text()>',
        'target-counter()':
            'target-counter( [<string>|<url>] , <custom-ident> , <counter-style>? )',
        'target-counters()':
            'target-counters( [<string>|<url>] , <custom-ident> , <string> , <counter-style>? )',
        'target-text()': 'target-text( [<string>|<url>] , [content|before|after|first-letter]? )',
        'time-percentage': '<time>|<percentage>',
        'timeline-range-name': 'cover|contain|entry|exit|entry-crossing|exit-crossing',
        'easing-function': 'linear|<cubic-bezier-timing-function>|<step-timing-function>',
        'track-breadth': '<length-percentage>|<flex>|min-content|max-content|auto',
        'track-list': '[<line-names>? [<track-size>|<track-repeat>]]+ <line-names>?',
        'track-repeat': 'repeat( [<integer [1,∞]>] , [<line-names>? <track-size>]+ <line-names>? )',
        'track-size':
            '<track-breadth>|minmax( <inflexible-breadth> , <track-breadth> )|fit-content( <length-percentage> )',
        'transform-function':
            '<matrix()>|<translate()>|<translateX()>|<translateY()>|<scale()>|<scaleX()>|<scaleY()>|<rotate()>|<skew()>|<skewX()>|<skewY()>|<matrix3d()>|<translate3d()>|<translateZ()>|<scale3d()>|<scaleZ()>|<rotate3d()>|<rotateX()>|<rotateY()>|<rotateZ()>|<perspective()>',
        'transform-list': '<transform-function>+',
        'transition-behavior-value': 'normal|allow-discrete',
        'translate()': 'translate( <length-percentage> , <length-percentage>? )',
        'translate3d()': 'translate3d( <length-percentage> , <length-percentage> , <length> )',
        'translateX()': 'translateX( <length-percentage> )',
        'translateY()': 'translateY( <length-percentage> )',
        'translateZ()': 'translateZ( <length> )',
        'type-or-unit':
            'string|color|url|integer|number|length|angle|time|frequency|cap|ch|em|ex|ic|lh|rlh|rem|vb|vi|vw|vh|vmin|vmax|mm|Q|cm|in|pt|pc|px|deg|grad|rad|turn|ms|s|Hz|kHz|%',
        'type-selector': "<wq-name>|<ns-prefix>? '*'",
        'var()': 'var( <custom-property-name> , <declaration-value>? )',
        'view()': "view( [<axis>||<'view-timeline-inset'>]? )",
        'viewport-length': 'auto|<length-percentage>',
        'visual-box': 'content-box|padding-box|border-box',
        'wq-name': '<ns-prefix>? <ident-token>',
        '-legacy-gradient':
            '<-webkit-gradient()>|<-legacy-linear-gradient>|<-legacy-repeating-linear-gradient>|<-legacy-radial-gradient>|<-legacy-repeating-radial-gradient>',
        '-legacy-linear-gradient':
            '-moz-linear-gradient( <-legacy-linear-gradient-arguments> )|-webkit-linear-gradient( <-legacy-linear-gradient-arguments> )|-o-linear-gradient( <-legacy-linear-gradient-arguments> )',
        '-legacy-repeating-linear-gradient':
            '-moz-repeating-linear-gradient( <-legacy-linear-gradient-arguments> )|-webkit-repeating-linear-gradient( <-legacy-linear-gradient-arguments> )|-o-repeating-linear-gradient( <-legacy-linear-gradient-arguments> )',
        '-legacy-linear-gradient-arguments': '[<angle>|<side-or-corner>]? , <color-stop-list>',
        '-legacy-radial-gradient':
            '-moz-radial-gradient( <-legacy-radial-gradient-arguments> )|-webkit-radial-gradient( <-legacy-radial-gradient-arguments> )|-o-radial-gradient( <-legacy-radial-gradient-arguments> )',
        '-legacy-repeating-radial-gradient':
            '-moz-repeating-radial-gradient( <-legacy-radial-gradient-arguments> )|-webkit-repeating-radial-gradient( <-legacy-radial-gradient-arguments> )|-o-repeating-radial-gradient( <-legacy-radial-gradient-arguments> )',
        '-legacy-radial-gradient-arguments':
            '[<position> ,]? [[[<-legacy-radial-gradient-shape>||<-legacy-radial-gradient-size>]|[<length>|<percentage>]{2}] ,]? <color-stop-list>',
        '-legacy-radial-gradient-size':
            'closest-side|closest-corner|farthest-side|farthest-corner|contain|cover',
        '-legacy-radial-gradient-shape': 'circle|ellipse',
        '-non-standard-font':
            '-apple-system-body|-apple-system-headline|-apple-system-subheadline|-apple-system-caption1|-apple-system-caption2|-apple-system-footnote|-apple-system-short-body|-apple-system-short-headline|-apple-system-short-subheadline|-apple-system-short-caption1|-apple-system-short-footnote|-apple-system-tall-body',
        '-non-standard-color':
            '-moz-ButtonDefault|-moz-ButtonHoverFace|-moz-ButtonHoverText|-moz-CellHighlight|-moz-CellHighlightText|-moz-Combobox|-moz-ComboboxText|-moz-Dialog|-moz-DialogText|-moz-dragtargetzone|-moz-EvenTreeRow|-moz-Field|-moz-FieldText|-moz-html-CellHighlight|-moz-html-CellHighlightText|-moz-mac-accentdarkestshadow|-moz-mac-accentdarkshadow|-moz-mac-accentface|-moz-mac-accentlightesthighlight|-moz-mac-accentlightshadow|-moz-mac-accentregularhighlight|-moz-mac-accentregularshadow|-moz-mac-chrome-active|-moz-mac-chrome-inactive|-moz-mac-focusring|-moz-mac-menuselect|-moz-mac-menushadow|-moz-mac-menutextselect|-moz-MenuHover|-moz-MenuHoverText|-moz-MenuBarText|-moz-MenuBarHoverText|-moz-nativehyperlinktext|-moz-OddTreeRow|-moz-win-communicationstext|-moz-win-mediatext|-moz-activehyperlinktext|-moz-default-background-color|-moz-default-color|-moz-hyperlinktext|-moz-visitedhyperlinktext|-webkit-activelink|-webkit-focus-ring-color|-webkit-link|-webkit-text',
        '-non-standard-image-rendering':
            'optimize-contrast|-moz-crisp-edges|-o-crisp-edges|-webkit-optimize-contrast',
        '-non-standard-overflow':
            'overlay|-moz-scrollbars-none|-moz-scrollbars-horizontal|-moz-scrollbars-vertical|-moz-hidden-unscrollable',
        '-non-standard-size':
            'intrinsic|min-intrinsic|-webkit-fill-available|-webkit-fit-content|-webkit-min-content|-webkit-max-content|-moz-available|-moz-fit-content|-moz-min-content|-moz-max-content',
        '-webkit-gradient()':
            '-webkit-gradient( <-webkit-gradient-type> , <-webkit-gradient-point> [, <-webkit-gradient-point>|, <-webkit-gradient-radius> , <-webkit-gradient-point>] [, <-webkit-gradient-radius>]? [, <-webkit-gradient-color-stop>]* )',
        '-webkit-gradient-color-stop':
            'from( <color> )|color-stop( [<number-zero-one>|<percentage>] , <color> )|to( <color> )',
        '-webkit-gradient-point':
            '[left|center|right|<length-percentage>] [top|center|bottom|<length-percentage>]',
        '-webkit-gradient-radius': '<length>|<percentage>',
        '-webkit-gradient-type': 'linear|radial',
        '-webkit-mask-box-repeat': 'repeat|stretch|round',
        '-ms-filter-function-list': '<-ms-filter-function>+',
        '-ms-filter-function': '<-ms-filter-function-progid>|<-ms-filter-function-legacy>',
        '-ms-filter-function-progid':
            "'progid:' [<ident-token> '.']* [<ident-token>|<function-token> <any-value>? )]",
        '-ms-filter-function-legacy': '<ident-token>|<function-token> <any-value>? )',
        'absolute-color-base': '<hex-color>|<absolute-color-function>|<named-color>|transparent',
        'absolute-color-function':
            '<rgb()>|<rgba()>|<hsl()>|<hsla()>|<hwb()>|<lab()>|<lch()>|<oklab()>|<oklch()>|<color()>',
        age: 'child|young|old',
        'anchor-name': '<dashed-ident>',
        'attr-name': '<wq-name>',
        'attr-fallback': '<any-value>',
        'bg-clip': '<box>|border|text',
        bottom: '<length>|auto',
        'container-name': '<custom-ident>',
        'container-condition':
            'not <query-in-parens>|<query-in-parens> [[and <query-in-parens>]*|[or <query-in-parens>]*]',
        'coord-box': 'content-box|padding-box|border-box|fill-box|stroke-box|view-box',
        'generic-voice': '[<age>? <gender> <integer>?]',
        gender: 'male|female|neutral',
        'generic-script-specific': 'generic( kai )|generic( fangsong )|generic( nastaliq )',
        'generic-complete': 'serif|sans-serif|system-ui|cursive|fantasy|math|monospace',
        'generic-incomplete': 'ui-serif|ui-sans-serif|ui-monospace|ui-rounded',
        '-non-standard-generic-family': '-apple-system|BlinkMacSystemFont',
        left: '<length>|auto',
        'color-base': '<hex-color>|<color-function>|<named-color>|<color-mix()>|transparent',
        'color-function':
            '<rgb()>|<rgba()>|<hsl()>|<hsla()>|<hwb()>|<lab()>|<lch()>|<oklab()>|<oklch()>|<color()>',
        'device-cmyk()': '<legacy-device-cmyk-syntax>|<modern-device-cmyk-syntax>',
        'legacy-device-cmyk-syntax': 'device-cmyk( <number>#{4} )',
        'modern-device-cmyk-syntax': 'device-cmyk( <cmyk-component>{4} [/ [<alpha-value>|none]]? )',
        'cmyk-component': '<number>|<percentage>|none',
        'color-mix()':
            'color-mix( <color-interpolation-method> , [<color>&&<percentage [0,100]>?]#{2} )',
        'color-space': '<rectangular-color-space>|<polar-color-space>|<custom-color-space>',
        'custom-color-space': '<dashed-ident>',
        paint: 'none|<color>|<url> [none|<color>]?|context-fill|context-stroke',
        'palette-identifier': '<dashed-ident>',
        right: '<length>|auto',
        'scope-start': '<forgiving-selector-list>',
        'scope-end': '<forgiving-selector-list>',
        'forgiving-selector-list': '<complex-real-selector-list>',
        'forgiving-relative-selector-list': '<relative-real-selector-list>',
        'selector-list': '<complex-selector-list>',
        'complex-real-selector-list': '<complex-real-selector>#',
        'simple-selector-list': '<simple-selector>#',
        'relative-real-selector-list': '<relative-real-selector>#',
        'complex-selector-unit': '[<compound-selector>? <pseudo-compound-selector>*]!',
        'complex-real-selector': '<compound-selector> [<combinator>? <compound-selector>]*',
        'relative-real-selector': '<combinator>? <complex-real-selector>',
        'pseudo-compound-selector': '<pseudo-element-selector> <pseudo-class-selector>*',
        'simple-selector': '<type-selector>|<subclass-selector>',
        'legacy-pseudo-element-selector': "':' [before|after|first-line|first-letter]",
        'single-animation-composition': 'replace|add|accumulate',
        'svg-length': '<percentage>|<length>|<number>',
        'svg-writing-mode': 'lr-tb|rl-tb|tb-rl|lr|rl|tb',
        top: '<length>|auto',
        x: '<number>',
        y: '<number>',
        declaration: "<ident-token> : <declaration-value>? ['!' important]?",
        'declaration-list': "[<declaration>? ';']* <declaration>?",
        url: 'url( <string> <url-modifier>* )|<url-token>',
        'url-modifier': '<ident>|<function-token> <any-value> )',
        'number-zero-one': '<number [0,1]>',
        'number-one-or-greater': '<number [1,∞]>',
        'color()': 'color( <colorspace-params> [/ [<alpha-value>|none]]? )',
        'colorspace-params': '[<predefined-rgb-params>|<xyz-params>]',
        'predefined-rgb-params': '<predefined-rgb> [<number>|<percentage>|none]{3}',
        'predefined-rgb': 'srgb|srgb-linear|display-p3|a98-rgb|prophoto-rgb|rec2020',
        'xyz-params': '<xyz-space> [<number>|<percentage>|none]{3}',
        'xyz-space': 'xyz|xyz-d50|xyz-d65',
        'oklab()':
            'oklab( [<percentage>|<number>|none] [<percentage>|<number>|none] [<percentage>|<number>|none] [/ [<alpha-value>|none]]? )',
        'oklch()':
            'oklch( [<percentage>|<number>|none] [<percentage>|<number>|none] [<hue>|none] [/ [<alpha-value>|none]]? )',
        'offset-path': '<ray()>|<url>|<basic-shape>',
        'rect()': "rect( [<length-percentage>|auto]{4} [round <'border-radius'>]? )",
        'xywh()':
            "xywh( <length-percentage>{2} <length-percentage [0,∞]>{2} [round <'border-radius'>]? )",
        'query-in-parens':
            '( <container-condition> )|( <size-feature> )|style( <style-query> )|<general-enclosed>',
        'size-feature': '<mf-plain>|<mf-boolean>|<mf-range>',
        'style-feature': '<declaration>',
        'style-query': '<style-condition>|<style-feature>',
        'style-condition':
            'not <style-in-parens>|<style-in-parens> [[and <style-in-parens>]*|[or <style-in-parens>]*]',
        'style-in-parens': '( <style-condition> )|( <style-feature> )|<general-enclosed>',
        '-non-standard-display':
            '-ms-inline-flexbox|-ms-grid|-ms-inline-grid|-webkit-flex|-webkit-inline-flex|-webkit-box|-webkit-inline-box|-moz-inline-stack|-moz-box|-moz-inline-box',
        'inset-area':
            '[[left|center|right|span-left|span-right|x-start|x-end|span-x-start|span-x-end|x-self-start|x-self-end|span-x-self-start|span-x-self-end|span-all]||[top|center|bottom|span-top|span-bottom|y-start|y-end|span-y-start|span-y-end|y-self-start|y-self-end|span-y-self-start|span-y-self-end|span-all]|[block-start|center|block-end|span-block-start|span-block-end|span-all]||[inline-start|center|inline-end|span-inline-start|span-inline-end|span-all]|[self-block-start|self-block-end|span-self-block-start|span-self-block-end|span-all]||[self-inline-start|self-inline-end|span-self-inline-start|span-self-inline-end|span-all]|[start|center|end|span-start|span-end|span-all]{1,2}|[self-start|center|self-end|span-self-start|span-self-end|span-all]{1,2}]',
        'position-area':
            '[[left|center|right|span-left|span-right|x-start|x-end|span-x-start|span-x-end|x-self-start|x-self-end|span-x-self-start|span-x-self-end|span-all]||[top|center|bottom|span-top|span-bottom|y-start|y-end|span-y-start|span-y-end|y-self-start|y-self-end|span-y-self-start|span-y-self-end|span-all]|[block-start|center|block-end|span-block-start|span-block-end|span-all]||[inline-start|center|inline-end|span-inline-start|span-inline-end|span-all]|[self-block-start|center|self-block-end|span-self-block-start|span-self-block-end|span-all]||[self-inline-start|center|self-inline-end|span-self-inline-start|span-self-inline-end|span-all]|[start|center|end|span-start|span-end|span-all]{1,2}|[self-start|center|self-end|span-self-start|span-self-end|span-all]{1,2}]',
        'anchor()': 'anchor( <anchor-element>?&&<anchor-side> , <length-percentage>? )',
        'anchor-side':
            'inside|outside|top|left|right|bottom|start|end|self-start|self-end|<percentage>|center',
        'anchor-size()': 'anchor-size( [<anchor-element>||<anchor-size>]? , <length-percentage>? )',
        'anchor-size': 'width|height|block|inline|self-block|self-inline',
        'anchor-element': '<dashed-ident>',
        'try-size': 'most-width|most-height|most-block-size|most-inline-size',
        'try-tactic': 'flip-block||flip-inline||flip-start',
        'font-variant-css2': 'normal|small-caps',
        'font-width-css3':
            'normal|ultra-condensed|extra-condensed|condensed|semi-condensed|semi-expanded|expanded|extra-expanded|ultra-expanded',
        'system-family-name': 'caption|icon|menu|message-box|small-caption|status-bar',
    },
    properties: {
        '--*': '<declaration-value>',
        '-ms-accelerator': 'false|true',
        '-ms-block-progression': 'tb|rl|bt|lr',
        '-ms-content-zoom-chaining': 'none|chained',
        '-ms-content-zooming': 'none|zoom',
        '-ms-content-zoom-limit': "<'-ms-content-zoom-limit-min'> <'-ms-content-zoom-limit-max'>",
        '-ms-content-zoom-limit-max': '<percentage>',
        '-ms-content-zoom-limit-min': '<percentage>',
        '-ms-content-zoom-snap': "<'-ms-content-zoom-snap-type'>||<'-ms-content-zoom-snap-points'>",
        '-ms-content-zoom-snap-points':
            'snapInterval( <percentage> , <percentage> )|snapList( <percentage># )',
        '-ms-content-zoom-snap-type': 'none|proximity|mandatory',
        '-ms-filter': '<string>',
        '-ms-flow-from': '[none|<custom-ident>]#',
        '-ms-flow-into': '[none|<custom-ident>]#',
        '-ms-grid-columns': 'none|<track-list>|<auto-track-list>',
        '-ms-grid-rows': 'none|<track-list>|<auto-track-list>',
        '-ms-high-contrast-adjust': 'auto|none',
        '-ms-hyphenate-limit-chars': 'auto|<integer>{1,3}',
        '-ms-hyphenate-limit-lines': 'no-limit|<integer>',
        '-ms-hyphenate-limit-zone': '<percentage>|<length>',
        '-ms-ime-align': 'auto|after',
        '-ms-overflow-style': 'auto|none|scrollbar|-ms-autohiding-scrollbar',
        '-ms-scrollbar-3dlight-color': '<color>',
        '-ms-scrollbar-arrow-color': '<color>',
        '-ms-scrollbar-base-color': '<color>',
        '-ms-scrollbar-darkshadow-color': '<color>',
        '-ms-scrollbar-face-color': '<color>',
        '-ms-scrollbar-highlight-color': '<color>',
        '-ms-scrollbar-shadow-color': '<color>',
        '-ms-scrollbar-track-color': '<color>',
        '-ms-scroll-chaining': 'chained|none',
        '-ms-scroll-limit':
            "<'-ms-scroll-limit-x-min'> <'-ms-scroll-limit-y-min'> <'-ms-scroll-limit-x-max'> <'-ms-scroll-limit-y-max'>",
        '-ms-scroll-limit-x-max': 'auto|<length>',
        '-ms-scroll-limit-x-min': '<length>',
        '-ms-scroll-limit-y-max': 'auto|<length>',
        '-ms-scroll-limit-y-min': '<length>',
        '-ms-scroll-rails': 'none|railed',
        '-ms-scroll-snap-points-x':
            'snapInterval( <length-percentage> , <length-percentage> )|snapList( <length-percentage># )',
        '-ms-scroll-snap-points-y':
            'snapInterval( <length-percentage> , <length-percentage> )|snapList( <length-percentage># )',
        '-ms-scroll-snap-type': 'none|proximity|mandatory',
        '-ms-scroll-snap-x': "<'-ms-scroll-snap-type'> <'-ms-scroll-snap-points-x'>",
        '-ms-scroll-snap-y': "<'-ms-scroll-snap-type'> <'-ms-scroll-snap-points-y'>",
        '-ms-scroll-translation': 'none|vertical-to-horizontal',
        '-ms-text-autospace':
            'none|ideograph-alpha|ideograph-numeric|ideograph-parenthesis|ideograph-space',
        '-ms-touch-select': 'grippers|none',
        '-ms-user-select': 'none|element|text',
        '-ms-wrap-flow': 'auto|both|start|end|maximum|clear',
        '-ms-wrap-margin': '<length>',
        '-ms-wrap-through': 'wrap|none',
        '-moz-appearance':
            'none|button|button-arrow-down|button-arrow-next|button-arrow-previous|button-arrow-up|button-bevel|button-focus|caret|checkbox|checkbox-container|checkbox-label|checkmenuitem|dualbutton|groupbox|listbox|listitem|menuarrow|menubar|menucheckbox|menuimage|menuitem|menuitemtext|menulist|menulist-button|menulist-text|menulist-textfield|menupopup|menuradio|menuseparator|meterbar|meterchunk|progressbar|progressbar-vertical|progresschunk|progresschunk-vertical|radio|radio-container|radio-label|radiomenuitem|range|range-thumb|resizer|resizerpanel|scale-horizontal|scalethumbend|scalethumb-horizontal|scalethumbstart|scalethumbtick|scalethumb-vertical|scale-vertical|scrollbarbutton-down|scrollbarbutton-left|scrollbarbutton-right|scrollbarbutton-up|scrollbarthumb-horizontal|scrollbarthumb-vertical|scrollbartrack-horizontal|scrollbartrack-vertical|searchfield|separator|sheet|spinner|spinner-downbutton|spinner-textfield|spinner-upbutton|splitter|statusbar|statusbarpanel|tab|tabpanel|tabpanels|tab-scroll-arrow-back|tab-scroll-arrow-forward|textfield|textfield-multiline|toolbar|toolbarbutton|toolbarbutton-dropdown|toolbargripper|toolbox|tooltip|treeheader|treeheadercell|treeheadersortarrow|treeitem|treeline|treetwisty|treetwistyopen|treeview|-moz-mac-unified-toolbar|-moz-win-borderless-glass|-moz-win-browsertabbar-toolbox|-moz-win-communicationstext|-moz-win-communications-toolbox|-moz-win-exclude-glass|-moz-win-glass|-moz-win-mediatext|-moz-win-media-toolbox|-moz-window-button-box|-moz-window-button-box-maximized|-moz-window-button-close|-moz-window-button-maximize|-moz-window-button-minimize|-moz-window-button-restore|-moz-window-frame-bottom|-moz-window-frame-left|-moz-window-frame-right|-moz-window-titlebar|-moz-window-titlebar-maximized',
        '-moz-binding': '<url>|none',
        '-moz-border-bottom-colors': '<color>+|none',
        '-moz-border-left-colors': '<color>+|none',
        '-moz-border-right-colors': '<color>+|none',
        '-moz-border-top-colors': '<color>+|none',
        '-moz-context-properties': 'none|[fill|fill-opacity|stroke|stroke-opacity]#',
        '-moz-float-edge': 'border-box|content-box|margin-box|padding-box',
        '-moz-force-broken-image-icon': '0|1',
        '-moz-image-region': '<shape>|auto',
        '-moz-orient': 'inline|block|horizontal|vertical',
        '-moz-outline-radius': '<outline-radius>{1,4} [/ <outline-radius>{1,4}]?',
        '-moz-outline-radius-bottomleft': '<outline-radius>',
        '-moz-outline-radius-bottomright': '<outline-radius>',
        '-moz-outline-radius-topleft': '<outline-radius>',
        '-moz-outline-radius-topright': '<outline-radius>',
        '-moz-stack-sizing': 'ignore|stretch-to-fit',
        '-moz-text-blink': 'none|blink',
        '-moz-user-focus':
            'ignore|normal|select-after|select-before|select-menu|select-same|select-all|none',
        '-moz-user-input': 'auto|none|enabled|disabled',
        '-moz-user-modify': 'read-only|read-write|write-only',
        '-moz-window-dragging': 'drag|no-drag',
        '-moz-window-shadow': 'default|menu|tooltip|sheet|none',
        '-webkit-appearance':
            'none|button|button-bevel|caps-lock-indicator|caret|checkbox|default-button|inner-spin-button|listbox|listitem|media-controls-background|media-controls-fullscreen-background|media-current-time-display|media-enter-fullscreen-button|media-exit-fullscreen-button|media-fullscreen-button|media-mute-button|media-overlay-play-button|media-play-button|media-seek-back-button|media-seek-forward-button|media-slider|media-sliderthumb|media-time-remaining-display|media-toggle-closed-captions-button|media-volume-slider|media-volume-slider-container|media-volume-sliderthumb|menulist|menulist-button|menulist-text|menulist-textfield|meter|progress-bar|progress-bar-value|push-button|radio|scrollbarbutton-down|scrollbarbutton-left|scrollbarbutton-right|scrollbarbutton-up|scrollbargripper-horizontal|scrollbargripper-vertical|scrollbarthumb-horizontal|scrollbarthumb-vertical|scrollbartrack-horizontal|scrollbartrack-vertical|searchfield|searchfield-cancel-button|searchfield-decoration|searchfield-results-button|searchfield-results-decoration|slider-horizontal|slider-vertical|sliderthumb-horizontal|sliderthumb-vertical|square-button|textarea|textfield|-apple-pay-button',
        '-webkit-border-before': "<'border-width'>||<'border-style'>||<color>",
        '-webkit-border-before-color': '<color>',
        '-webkit-border-before-style': "<'border-style'>",
        '-webkit-border-before-width': "<'border-width'>",
        '-webkit-box-reflect': '[above|below|right|left]? <length>? <image>?',
        '-webkit-line-clamp': 'none|<integer>',
        '-webkit-mask':
            '[<mask-reference>||<position> [/ <bg-size>]?||<repeat-style>||[<box>|border|padding|content|text]||[<box>|border|padding|content]]#',
        '-webkit-mask-attachment': '<attachment>#',
        '-webkit-mask-clip': '[<box>|border|padding|content|text]#',
        '-webkit-mask-composite': '<composite-style>#',
        '-webkit-mask-image': '<mask-reference>#',
        '-webkit-mask-origin': '[<box>|border|padding|content]#',
        '-webkit-mask-position': '<position>#',
        '-webkit-mask-position-x': '[<length-percentage>|left|center|right]#',
        '-webkit-mask-position-y': '[<length-percentage>|top|center|bottom]#',
        '-webkit-mask-repeat': '<repeat-style>#',
        '-webkit-mask-repeat-x': 'repeat|no-repeat|space|round',
        '-webkit-mask-repeat-y': 'repeat|no-repeat|space|round',
        '-webkit-mask-size': '<bg-size>#',
        '-webkit-overflow-scrolling': 'auto|touch',
        '-webkit-tap-highlight-color': '<color>',
        '-webkit-text-fill-color': '<color>',
        '-webkit-text-stroke': '<length>||<color>',
        '-webkit-text-stroke-color': '<color>',
        '-webkit-text-stroke-width': '<length>',
        '-webkit-touch-callout': 'default|none',
        '-webkit-user-modify': 'read-only|read-write|read-write-plaintext-only',
        'accent-color': 'auto|<color>',
        'align-content':
            'normal|<baseline-position>|<content-distribution>|<overflow-position>? <content-position>',
        'align-items': 'normal|stretch|<baseline-position>|[<overflow-position>? <self-position>]',
        'align-self':
            'auto|normal|stretch|<baseline-position>|<overflow-position>? <self-position>',
        'align-tracks':
            '[normal|<baseline-position>|<content-distribution>|<overflow-position>? <content-position>]#',
        all: 'initial|inherit|unset|revert|revert-layer',
        'anchor-name': 'none|<dashed-ident>#',
        'anchor-scope': 'none|all|<dashed-ident>#',
        animation: '<single-animation>#',
        'animation-composition': '<single-animation-composition>#',
        'animation-delay': '<time>#',
        'animation-direction': '<single-animation-direction>#',
        'animation-duration': '<time>#',
        'animation-fill-mode': '<single-animation-fill-mode>#',
        'animation-iteration-count': '<single-animation-iteration-count>#',
        'animation-name': '[none|<keyframes-name>]#',
        'animation-play-state': '<single-animation-play-state>#',
        'animation-range': "[<'animation-range-start'> <'animation-range-end'>?]#",
        'animation-range-end':
            '[normal|<length-percentage>|<timeline-range-name> <length-percentage>?]#',
        'animation-range-start':
            '[normal|<length-percentage>|<timeline-range-name> <length-percentage>?]#',
        'animation-timing-function': '<easing-function>#',
        'animation-timeline': '<single-animation-timeline>#',
        appearance: 'none|auto|textfield|menulist-button|<compat-auto>',
        'aspect-ratio': 'auto||<ratio>',
        azimuth:
            '<angle>|[[left-side|far-left|left|center-left|center|center-right|right|far-right|right-side]||behind]|leftwards|rightwards',
        'backdrop-filter': 'none|<filter-function-list>',
        'backface-visibility': 'visible|hidden',
        background: '[<bg-layer> ,]* <final-bg-layer>',
        'background-attachment': '<attachment>#',
        'background-blend-mode': '<blend-mode>#',
        'background-clip': '<bg-clip>#',
        'background-color': '<color>',
        'background-image': '<bg-image>#',
        'background-origin': '<box>#',
        'background-position': '<bg-position>#',
        'background-position-x': '[center|[[left|right|x-start|x-end]? <length-percentage>?]!]#',
        'background-position-y': '[center|[[top|bottom|y-start|y-end]? <length-percentage>?]!]#',
        'background-repeat': '<repeat-style>#',
        'background-size': '<bg-size>#',
        'block-size': "<'width'>",
        border: '<line-width>||<line-style>||<color>',
        'border-block': "<'border-top-width'>||<'border-top-style'>||<color>",
        'border-block-color': "<'border-top-color'>{1,2}",
        'border-block-style': "<'border-top-style'>",
        'border-block-width': "<'border-top-width'>",
        'border-block-end': "<'border-top-width'>||<'border-top-style'>||<color>",
        'border-block-end-color': "<'border-top-color'>",
        'border-block-end-style': "<'border-top-style'>",
        'border-block-end-width': "<'border-top-width'>",
        'border-block-start': "<'border-top-width'>||<'border-top-style'>||<color>",
        'border-block-start-color': "<'border-top-color'>",
        'border-block-start-style': "<'border-top-style'>",
        'border-block-start-width': "<'border-top-width'>",
        'border-bottom': '<line-width>||<line-style>||<color>',
        'border-bottom-color': "<'border-top-color'>",
        'border-bottom-left-radius': '<length-percentage>{1,2}',
        'border-bottom-right-radius': '<length-percentage>{1,2}',
        'border-bottom-style': '<line-style>',
        'border-bottom-width': '<line-width>',
        'border-collapse': 'collapse|separate',
        'border-color': '<color>{1,4}',
        'border-end-end-radius': '<length-percentage>{1,2}',
        'border-end-start-radius': '<length-percentage>{1,2}',
        'border-image':
            "<'border-image-source'>||<'border-image-slice'> [/ <'border-image-width'>|/ <'border-image-width'>? / <'border-image-outset'>]?||<'border-image-repeat'>",
        'border-image-outset': '[<length>|<number>]{1,4}',
        'border-image-repeat': '[stretch|repeat|round|space]{1,2}',
        'border-image-slice': '<number-percentage>{1,4}&&fill?',
        'border-image-source': 'none|<image>',
        'border-image-width': '[<length-percentage>|<number>|auto]{1,4}',
        'border-inline': "<'border-top-width'>||<'border-top-style'>||<color>",
        'border-inline-end': "<'border-top-width'>||<'border-top-style'>||<color>",
        'border-inline-color': "<'border-top-color'>{1,2}",
        'border-inline-style': "<'border-top-style'>",
        'border-inline-width': "<'border-top-width'>",
        'border-inline-end-color': "<'border-top-color'>",
        'border-inline-end-style': "<'border-top-style'>",
        'border-inline-end-width': "<'border-top-width'>",
        'border-inline-start': "<'border-top-width'>||<'border-top-style'>||<color>",
        'border-inline-start-color': "<'border-top-color'>",
        'border-inline-start-style': "<'border-top-style'>",
        'border-inline-start-width': "<'border-top-width'>",
        'border-left': '<line-width>||<line-style>||<color>',
        'border-left-color': '<color>',
        'border-left-style': '<line-style>',
        'border-left-width': '<line-width>',
        'border-radius': '<length-percentage>{1,4} [/ <length-percentage>{1,4}]?',
        'border-right': '<line-width>||<line-style>||<color>',
        'border-right-color': '<color>',
        'border-right-style': '<line-style>',
        'border-right-width': '<line-width>',
        'border-spacing': '<length> <length>?',
        'border-start-end-radius': '<length-percentage>{1,2}',
        'border-start-start-radius': '<length-percentage>{1,2}',
        'border-style': '<line-style>{1,4}',
        'border-top': '<line-width>||<line-style>||<color>',
        'border-top-color': '<color>',
        'border-top-left-radius': '<length-percentage>{1,2}',
        'border-top-right-radius': '<length-percentage>{1,2}',
        'border-top-style': '<line-style>',
        'border-top-width': '<line-width>',
        'border-width': '<line-width>{1,4}',
        bottom: '<length>|<percentage>|auto',
        'box-align': 'start|center|end|baseline|stretch',
        'box-decoration-break': 'slice|clone',
        'box-direction': 'normal|reverse|inherit',
        'box-flex': '<number>',
        'box-flex-group': '<integer>',
        'box-lines': 'single|multiple',
        'box-ordinal-group': '<integer>',
        'box-orient': 'horizontal|vertical|inline-axis|block-axis|inherit',
        'box-pack': 'start|center|end|justify',
        'box-shadow': 'none|<shadow>#',
        'box-sizing': 'content-box|border-box',
        'break-after':
            'auto|avoid|always|all|avoid-page|page|left|right|recto|verso|avoid-column|column|avoid-region|region',
        'break-before':
            'auto|avoid|always|all|avoid-page|page|left|right|recto|verso|avoid-column|column|avoid-region|region',
        'break-inside': 'auto|avoid|avoid-page|avoid-column|avoid-region',
        'caption-side': 'top|bottom|block-start|block-end|inline-start|inline-end',
        caret: "<'caret-color'>||<'caret-shape'>",
        'caret-color': 'auto|<color>',
        'caret-shape': 'auto|bar|block|underscore',
        clear: 'none|left|right|both|inline-start|inline-end',
        clip: '<shape>|auto',
        'clip-path': '<clip-source>|[<basic-shape>||<geometry-box>]|none',
        'clip-rule': 'nonzero|evenodd',
        color: '<color>',
        'color-interpolation-filters': 'auto|sRGB|linearRGB',
        'color-scheme': 'normal|[light|dark|<custom-ident>]+&&only?',
        'column-count': '<integer>|auto',
        'column-fill': 'auto|balance',
        'column-gap': 'normal|<length-percentage>',
        'column-rule': "<'column-rule-width'>||<'column-rule-style'>||<'column-rule-color'>",
        'column-rule-color': '<color>',
        'column-rule-style': "<'border-style'>",
        'column-rule-width': "<'border-width'>",
        'column-span': 'none|all',
        'column-width': '<length>|auto',
        columns: "<'column-width'>||<'column-count'>",
        contain: 'none|strict|content|[[size||inline-size]||layout||style||paint]',
        'contain-intrinsic-size': '[auto? [none|<length>]]{1,2}',
        'contain-intrinsic-block-size': 'auto? [none|<length>]',
        'contain-intrinsic-height': 'auto? [none|<length>]',
        'contain-intrinsic-inline-size': 'auto? [none|<length>]',
        'contain-intrinsic-width': 'auto? [none|<length>]',
        container: "<'container-name'> [/ <'container-type'>]?",
        'container-name': 'none|<custom-ident>+',
        'container-type': 'normal||[size|inline-size]',
        content: 'normal|none|[<content-replacement>|<content-list>] [/ [<string>|<counter>]+]?',
        'content-visibility': 'visible|auto|hidden',
        'counter-increment': '[<counter-name> <integer>?]+|none',
        'counter-reset': '[<counter-name> <integer>?|<reversed-counter-name> <integer>?]+|none',
        'counter-set': '[<counter-name> <integer>?]+|none',
        cursor: '[[<url> [<x> <y>]? ,]* [auto|default|none|context-menu|help|pointer|progress|wait|cell|crosshair|text|vertical-text|alias|copy|move|no-drop|not-allowed|e-resize|n-resize|ne-resize|nw-resize|s-resize|se-resize|sw-resize|w-resize|ew-resize|ns-resize|nesw-resize|nwse-resize|col-resize|row-resize|all-scroll|zoom-in|zoom-out|grab|grabbing|hand|-webkit-grab|-webkit-grabbing|-webkit-zoom-in|-webkit-zoom-out|-moz-grab|-moz-grabbing|-moz-zoom-in|-moz-zoom-out]]',
        d: 'none|path( <string> )',
        cx: '<length>|<percentage>',
        cy: '<length>|<percentage>',
        direction: 'ltr|rtl',
        display:
            '[<display-outside>||<display-inside>]|<display-listitem>|<display-internal>|<display-box>|<display-legacy>|<-non-standard-display>',
        'dominant-baseline':
            'auto|use-script|no-change|reset-size|ideographic|alphabetic|hanging|mathematical|central|middle|text-after-edge|text-before-edge',
        'empty-cells': 'show|hide',
        'field-sizing': 'content|fixed',
        fill: '<paint>',
        'fill-opacity': '<number-zero-one>',
        'fill-rule': 'nonzero|evenodd',
        filter: 'none|<filter-function-list>|<-ms-filter-function-list>',
        flex: "none|[<'flex-grow'> <'flex-shrink'>?||<'flex-basis'>]",
        'flex-basis': "content|<'width'>",
        'flex-direction': 'row|row-reverse|column|column-reverse',
        'flex-flow': "<'flex-direction'>||<'flex-wrap'>",
        'flex-grow': '<number>',
        'flex-shrink': '<number>',
        'flex-wrap': 'nowrap|wrap|wrap-reverse',
        float: 'left|right|none|inline-start|inline-end',
        font: "[[<'font-style'>||<font-variant-css2>||<'font-weight'>||<font-width-css3>]? <'font-size'> [/ <'line-height'>]? <'font-family'>#]|<system-family-name>|<-non-standard-font>",
        'font-family': '[<family-name>|<generic-family>]#',
        'font-feature-settings': 'normal|<feature-tag-value>#',
        'font-kerning': 'auto|normal|none',
        'font-language-override': 'normal|<string>',
        'font-optical-sizing': 'auto|none',
        'font-palette': 'normal|light|dark|<palette-identifier>',
        'font-variation-settings': 'normal|[<string> <number>]#',
        'font-size': '<absolute-size>|<relative-size>|<length-percentage>',
        'font-size-adjust':
            'none|[ex-height|cap-height|ch-width|ic-width|ic-height]? [from-font|<number>]',
        'font-smooth': 'auto|never|always|<absolute-size>|<length>',
        'font-stretch': '<font-stretch-absolute>',
        'font-style': 'normal|italic|oblique <angle>?',
        'font-synthesis': 'none|[weight||style||small-caps||position]',
        'font-synthesis-position': 'auto|none',
        'font-synthesis-small-caps': 'auto|none',
        'font-synthesis-style': 'auto|none',
        'font-synthesis-weight': 'auto|none',
        'font-variant':
            'normal|none|[<common-lig-values>||<discretionary-lig-values>||<historical-lig-values>||<contextual-alt-values>||stylistic( <feature-value-name> )||historical-forms||styleset( <feature-value-name># )||character-variant( <feature-value-name># )||swash( <feature-value-name> )||ornaments( <feature-value-name> )||annotation( <feature-value-name> )||[small-caps|all-small-caps|petite-caps|all-petite-caps|unicase|titling-caps]||<numeric-figure-values>||<numeric-spacing-values>||<numeric-fraction-values>||ordinal||slashed-zero||<east-asian-variant-values>||<east-asian-width-values>||ruby]',
        'font-variant-alternates':
            'normal|[stylistic( <feature-value-name> )||historical-forms||styleset( <feature-value-name># )||character-variant( <feature-value-name># )||swash( <feature-value-name> )||ornaments( <feature-value-name> )||annotation( <feature-value-name> )]',
        'font-variant-caps':
            'normal|small-caps|all-small-caps|petite-caps|all-petite-caps|unicase|titling-caps',
        'font-variant-east-asian':
            'normal|[<east-asian-variant-values>||<east-asian-width-values>||ruby]',
        'font-variant-emoji': 'normal|text|emoji|unicode',
        'font-variant-ligatures':
            'normal|none|[<common-lig-values>||<discretionary-lig-values>||<historical-lig-values>||<contextual-alt-values>]',
        'font-variant-numeric':
            'normal|[<numeric-figure-values>||<numeric-spacing-values>||<numeric-fraction-values>||ordinal||slashed-zero]',
        'font-variant-position': 'normal|sub|super',
        'font-weight': '<font-weight-absolute>|bolder|lighter',
        'forced-color-adjust': 'auto|none|preserve-parent-color',
        gap: "<'row-gap'> <'column-gap'>?",
        grid: "<'grid-template'>|<'grid-template-rows'> / [auto-flow&&dense?] <'grid-auto-columns'>?|[auto-flow&&dense?] <'grid-auto-rows'>? / <'grid-template-columns'>",
        'grid-area': '<grid-line> [/ <grid-line>]{0,3}',
        'grid-auto-columns': '<track-size>+',
        'grid-auto-flow': '[row|column]||dense',
        'grid-auto-rows': '<track-size>+',
        'grid-column': '<grid-line> [/ <grid-line>]?',
        'grid-column-end': '<grid-line>',
        'grid-column-gap': '<length-percentage>',
        'grid-column-start': '<grid-line>',
        'grid-gap': "<'grid-row-gap'> <'grid-column-gap'>?",
        'grid-row': '<grid-line> [/ <grid-line>]?',
        'grid-row-end': '<grid-line>',
        'grid-row-gap': '<length-percentage>',
        'grid-row-start': '<grid-line>',
        'grid-template':
            "none|[<'grid-template-rows'> / <'grid-template-columns'>]|[<line-names>? <string> <track-size>? <line-names>?]+ [/ <explicit-track-list>]?",
        'grid-template-areas': 'none|<string>+',
        'grid-template-columns': 'none|<track-list>|<auto-track-list>|subgrid <line-name-list>?',
        'grid-template-rows': 'none|<track-list>|<auto-track-list>|subgrid <line-name-list>?',
        'hanging-punctuation': 'none|[first||[force-end|allow-end]||last]',
        height: 'auto|<length>|<percentage>|min-content|max-content|fit-content|fit-content( <length-percentage> )|stretch|<-non-standard-size>',
        'hyphenate-character': 'auto|<string>',
        'hyphenate-limit-chars': '[auto|<integer>]{1,3}',
        hyphens: 'none|manual|auto',
        'image-orientation': 'from-image|<angle>|[<angle>? flip]',
        'image-rendering':
            'auto|crisp-edges|pixelated|optimizeSpeed|optimizeQuality|<-non-standard-image-rendering>',
        'image-resolution': '[from-image||<resolution>]&&snap?',
        'ime-mode': 'auto|normal|active|inactive|disabled',
        'initial-letter': 'normal|[<number> <integer>?]',
        'initial-letter-align': '[auto|alphabetic|hanging|ideographic]',
        'inline-size': "<'width'>",
        'input-security': 'auto|none',
        inset: "<'top'>{1,4}",
        'inset-block': "<'top'>{1,2}",
        'inset-block-end': "<'top'>",
        'inset-block-start': "<'top'>",
        'inset-inline': "<'top'>{1,2}",
        'inset-inline-end': "<'top'>",
        'inset-inline-start': "<'top'>",
        'interpolate-size': 'numeric-only|allow-keywords',
        isolation: 'auto|isolate',
        'justify-content':
            'normal|<content-distribution>|<overflow-position>? [<content-position>|left|right]',
        'justify-items':
            'normal|stretch|<baseline-position>|<overflow-position>? [<self-position>|left|right]|legacy|legacy&&[left|right|center]',
        'justify-self':
            'auto|normal|stretch|<baseline-position>|<overflow-position>? [<self-position>|left|right]',
        'justify-tracks':
            '[normal|<content-distribution>|<overflow-position>? [<content-position>|left|right]]#',
        left: '<length>|<percentage>|auto',
        'letter-spacing': 'normal|<length-percentage>',
        'line-break': 'auto|loose|normal|strict|anywhere',
        'line-clamp': 'none|<integer>',
        'line-height': 'normal|<number>|<length>|<percentage>',
        'line-height-step': '<length>',
        'list-style': "<'list-style-type'>||<'list-style-position'>||<'list-style-image'>",
        'list-style-image': '<image>|none',
        'list-style-position': 'inside|outside',
        'list-style-type': '<counter-style>|<string>|none',
        margin: '[<length>|<percentage>|auto]{1,4}',
        'margin-block': "<'margin-left'>{1,2}",
        'margin-block-end': "<'margin-left'>",
        'margin-block-start': "<'margin-left'>",
        'margin-bottom': '<length>|<percentage>|auto',
        'margin-inline': "<'margin-left'>{1,2}",
        'margin-inline-end': "<'margin-left'>",
        'margin-inline-start': "<'margin-left'>",
        'margin-left': '<length>|<percentage>|auto',
        'margin-right': '<length>|<percentage>|auto',
        'margin-top': '<length>|<percentage>|auto',
        'margin-trim': 'none|in-flow|all',
        marker: 'none|<url>',
        'marker-end': 'none|<url>',
        'marker-mid': 'none|<url>',
        'marker-start': 'none|<url>',
        mask: '<mask-layer>#',
        'mask-border':
            "<'mask-border-source'>||<'mask-border-slice'> [/ <'mask-border-width'>? [/ <'mask-border-outset'>]?]?||<'mask-border-repeat'>||<'mask-border-mode'>",
        'mask-border-mode': 'luminance|alpha',
        'mask-border-outset': '[<length>|<number>]{1,4}',
        'mask-border-repeat': '[stretch|repeat|round|space]{1,2}',
        'mask-border-slice': '<number-percentage>{1,4} fill?',
        'mask-border-source': 'none|<image>',
        'mask-border-width': '[<length-percentage>|<number>|auto]{1,4}',
        'mask-clip': '[<geometry-box>|no-clip]#',
        'mask-composite': '<compositing-operator>#',
        'mask-image': '<mask-reference>#',
        'mask-mode': '<masking-mode>#',
        'mask-origin': '<geometry-box>#',
        'mask-position': '<position>#',
        'mask-repeat': '<repeat-style>#',
        'mask-size': '<bg-size>#',
        'mask-type': 'luminance|alpha',
        'masonry-auto-flow': '[pack|next]||[definite-first|ordered]',
        'math-depth': 'auto-add|add( <integer> )|<integer>',
        'math-shift': 'normal|compact',
        'math-style': 'normal|compact',
        'max-block-size': "<'max-width'>",
        'max-height':
            'none|<length-percentage>|min-content|max-content|fit-content|fit-content( <length-percentage> )|stretch|<-non-standard-size>',
        'max-inline-size': "<'max-width'>",
        'max-lines': 'none|<integer>',
        'max-width':
            'none|<length-percentage>|min-content|max-content|fit-content|fit-content( <length-percentage> )|stretch|<-non-standard-size>',
        'min-block-size': "<'min-width'>",
        'min-height':
            'auto|<length>|<percentage>|min-content|max-content|fit-content|fit-content( <length-percentage> )|stretch|<-non-standard-size>',
        'min-inline-size': "<'min-width'>",
        'min-width':
            'auto|<length>|<percentage>|min-content|max-content|fit-content|fit-content( <length-percentage> )|stretch|<-non-standard-size>',
        'mix-blend-mode': '<blend-mode>|plus-lighter',
        'object-fit': 'fill|contain|cover|none|scale-down',
        'object-position': '<position>',
        offset: "[<'offset-position'>? [<'offset-path'> [<'offset-distance'>||<'offset-rotate'>]?]?]! [/ <'offset-anchor'>]?",
        'offset-anchor': 'auto|<position>',
        'offset-distance': '<length-percentage>',
        'offset-path': 'none|<offset-path>||<coord-box>',
        'offset-position': 'normal|auto|<position>',
        'offset-rotate': '[auto|reverse]||<angle>',
        opacity: '<alpha-value>',
        order: '<integer>',
        orphans: '<integer>',
        outline: "[<'outline-width'>||<'outline-style'>||<'outline-color'>]",
        'outline-color': 'auto|<color>',
        'outline-offset': '<length>',
        'outline-style': "auto|<'border-style'>",
        'outline-width': '<line-width>',
        overflow: '[visible|hidden|clip|scroll|auto]{1,2}|<-non-standard-overflow>',
        'overflow-anchor': 'auto|none',
        'overflow-block': 'visible|hidden|clip|scroll|auto',
        'overflow-clip-box': 'padding-box|content-box',
        'overflow-clip-margin': '<visual-box>||<length [0,∞]>',
        'overflow-inline': 'visible|hidden|clip|scroll|auto',
        'overflow-wrap': 'normal|break-word|anywhere',
        'overflow-x': 'visible|hidden|clip|scroll|auto',
        'overflow-y': 'visible|hidden|clip|scroll|auto',
        overlay: 'none|auto',
        'overscroll-behavior': '[contain|none|auto]{1,2}',
        'overscroll-behavior-block': 'contain|none|auto',
        'overscroll-behavior-inline': 'contain|none|auto',
        'overscroll-behavior-x': 'contain|none|auto',
        'overscroll-behavior-y': 'contain|none|auto',
        padding: '[<length>|<percentage>]{1,4}',
        'padding-block': "<'padding-left'>{1,2}",
        'padding-block-end': "<'padding-left'>",
        'padding-block-start': "<'padding-left'>",
        'padding-bottom': '<length>|<percentage>',
        'padding-inline': "<'padding-left'>{1,2}",
        'padding-inline-end': "<'padding-left'>",
        'padding-inline-start': "<'padding-left'>",
        'padding-left': '<length>|<percentage>',
        'padding-right': '<length>|<percentage>',
        'padding-top': '<length>|<percentage>',
        page: 'auto|<custom-ident>',
        'page-break-after': 'auto|always|avoid|left|right|recto|verso',
        'page-break-before': 'auto|always|avoid|left|right|recto|verso',
        'page-break-inside': 'auto|avoid',
        'paint-order': 'normal|[fill||stroke||markers]',
        perspective: 'none|<length>',
        'perspective-origin': '<position>',
        'place-content': "<'align-content'> <'justify-content'>?",
        'place-items': "<'align-items'> <'justify-items'>?",
        'place-self': "<'align-self'> <'justify-self'>?",
        'pointer-events':
            'auto|none|visiblePainted|visibleFill|visibleStroke|visible|painted|fill|stroke|all|inherit',
        position: 'static|relative|absolute|sticky|fixed|-webkit-sticky',
        'position-anchor': 'auto|<anchor-name>',
        'position-area': 'none|<position-area>',
        'position-try': "<'position-try-order'>? <'position-try-fallbacks'>",
        'position-try-fallbacks': "none|[[<dashed-ident>||<try-tactic>]|<'position-area'>]#",
        'position-try-order': 'normal|<try-size>',
        'position-visibility': 'always|[anchors-valid||anchors-visible||no-overflow]',
        'print-color-adjust': 'economy|exact',
        quotes: 'none|auto|[<string> <string>]+',
        r: '<length>|<percentage>',
        resize: 'none|both|horizontal|vertical|block|inline',
        right: '<length>|<percentage>|auto',
        rotate: 'none|<angle>|[x|y|z|<number>{3}]&&<angle>',
        'row-gap': 'normal|<length-percentage>',
        'ruby-align': 'start|center|space-between|space-around',
        'ruby-merge': 'separate|collapse|auto',
        'ruby-position': '[alternate||[over|under]]|inter-character',
        rx: '<length>|<percentage>',
        ry: '<length>|<percentage>',
        scale: 'none|[<number>|<percentage>]{1,3}',
        'scrollbar-color': 'auto|<color>{2}',
        'scrollbar-gutter': 'auto|stable&&both-edges?',
        'scrollbar-width': 'auto|thin|none',
        'scroll-behavior': 'auto|smooth',
        'scroll-margin': '<length>{1,4}',
        'scroll-margin-block': '<length>{1,2}',
        'scroll-margin-block-start': '<length>',
        'scroll-margin-block-end': '<length>',
        'scroll-margin-bottom': '<length>',
        'scroll-margin-inline': '<length>{1,2}',
        'scroll-margin-inline-start': '<length>',
        'scroll-margin-inline-end': '<length>',
        'scroll-margin-left': '<length>',
        'scroll-margin-right': '<length>',
        'scroll-margin-top': '<length>',
        'scroll-padding': '[auto|<length-percentage>]{1,4}',
        'scroll-padding-block': '[auto|<length-percentage>]{1,2}',
        'scroll-padding-block-start': 'auto|<length-percentage>',
        'scroll-padding-block-end': 'auto|<length-percentage>',
        'scroll-padding-bottom': 'auto|<length-percentage>',
        'scroll-padding-inline': '[auto|<length-percentage>]{1,2}',
        'scroll-padding-inline-start': 'auto|<length-percentage>',
        'scroll-padding-inline-end': 'auto|<length-percentage>',
        'scroll-padding-left': 'auto|<length-percentage>',
        'scroll-padding-right': 'auto|<length-percentage>',
        'scroll-padding-top': 'auto|<length-percentage>',
        'scroll-snap-align': '[none|start|end|center]{1,2}',
        'scroll-snap-coordinate': 'none|<position>#',
        'scroll-snap-destination': '<position>',
        'scroll-snap-points-x': 'none|repeat( <length-percentage> )',
        'scroll-snap-points-y': 'none|repeat( <length-percentage> )',
        'scroll-snap-stop': 'normal|always',
        'scroll-snap-type': 'none|[x|y|block|inline|both] [mandatory|proximity]?',
        'scroll-snap-type-x': 'none|mandatory|proximity',
        'scroll-snap-type-y': 'none|mandatory|proximity',
        'scroll-timeline': "[<'scroll-timeline-name'>||<'scroll-timeline-axis'>]#",
        'scroll-timeline-axis': '[block|inline|x|y]#',
        'scroll-timeline-name': '[none|<dashed-ident>]#',
        'shape-image-threshold': '<alpha-value>',
        'shape-margin': '<length-percentage>',
        'shape-outside': 'none|[<shape-box>||<basic-shape>]|<image>',
        'shape-rendering': 'auto|optimizeSpeed|crispEdges|geometricPrecision',
        stroke: '<paint>',
        'stroke-dasharray': 'none|[<svg-length>+]#',
        'stroke-dashoffset': '<svg-length>',
        'stroke-linecap': 'butt|round|square',
        'stroke-linejoin': 'miter|round|bevel',
        'stroke-miterlimit': '<number-one-or-greater>',
        'stroke-opacity': "<'opacity'>",
        'stroke-width': '<svg-length>',
        'tab-size': '<integer>|<length>',
        'table-layout': 'auto|fixed',
        'text-align': 'start|end|left|right|center|justify|match-parent',
        'text-align-last': 'auto|start|end|left|right|center|justify',
        'text-anchor': 'start|middle|end',
        'text-combine-upright': 'none|all|[digits <integer>?]',
        'text-decoration':
            "<'text-decoration-line'>||<'text-decoration-style'>||<'text-decoration-color'>||<'text-decoration-thickness'>",
        'text-decoration-color': '<color>',
        'text-decoration-line':
            'none|[underline||overline||line-through||blink]|spelling-error|grammar-error',
        'text-decoration-skip':
            'none|[objects||[spaces|[leading-spaces||trailing-spaces]]||edges||box-decoration]',
        'text-decoration-skip-ink': 'auto|all|none',
        'text-decoration-style': 'solid|double|dotted|dashed|wavy',
        'text-decoration-thickness': 'auto|from-font|<length>|<percentage>',
        'text-emphasis': "<'text-emphasis-style'>||<'text-emphasis-color'>",
        'text-emphasis-color': '<color>',
        'text-emphasis-position': 'auto|[over|under]&&[right|left]?',
        'text-emphasis-style':
            'none|[[filled|open]||[dot|circle|double-circle|triangle|sesame]]|<string>',
        'text-indent': '<length-percentage>&&hanging?&&each-line?',
        'text-justify': 'auto|inter-character|inter-word|none',
        'text-orientation': 'mixed|upright|sideways',
        'text-overflow': '[clip|ellipsis|<string>]{1,2}',
        'text-rendering': 'auto|optimizeSpeed|optimizeLegibility|geometricPrecision',
        'text-shadow': 'none|<shadow-t>#',
        'text-size-adjust': 'none|auto|<percentage>',
        'text-spacing-trim': 'space-all|normal|space-first|trim-start|trim-both|trim-all|auto',
        'text-transform': 'none|capitalize|uppercase|lowercase|full-width|full-size-kana',
        'text-underline-offset': 'auto|<length>|<percentage>',
        'text-underline-position': 'auto|from-font|[under||[left|right]]',
        'text-wrap': "<'text-wrap-mode'>||<'text-wrap-style'>",
        'text-wrap-mode': 'auto|wrap|nowrap',
        'text-wrap-style': 'auto|balance|stable|pretty',
        'timeline-scope': 'none|<dashed-ident>#',
        top: '<length>|<percentage>|auto',
        'touch-action':
            'auto|none|[[pan-x|pan-left|pan-right]||[pan-y|pan-up|pan-down]||pinch-zoom]|manipulation',
        transform: 'none|<transform-list>',
        'transform-box': 'content-box|border-box|fill-box|stroke-box|view-box',
        'transform-origin':
            '[<length-percentage>|left|center|right|top|bottom]|[[<length-percentage>|left|center|right]&&[<length-percentage>|top|center|bottom]] <length>?',
        'transform-style': 'flat|preserve-3d',
        transition: '<single-transition>#',
        'transition-behavior': '<transition-behavior-value>#',
        'transition-delay': '<time>#',
        'transition-duration': '<time>#',
        'transition-property': 'none|<single-transition-property>#',
        'transition-timing-function': '<easing-function>#',
        translate: 'none|<length-percentage> [<length-percentage> <length>?]?',
        'unicode-bidi':
            'normal|embed|isolate|bidi-override|isolate-override|plaintext|-moz-isolate|-moz-isolate-override|-moz-plaintext|-webkit-isolate|-webkit-isolate-override|-webkit-plaintext',
        'user-select': 'auto|text|none|contain|all',
        'vector-effect': 'none|non-scaling-stroke|non-scaling-size|non-rotation|fixed-position',
        'vertical-align':
            'baseline|sub|super|text-top|text-bottom|middle|top|bottom|<percentage>|<length>',
        'view-timeline': "[<'view-timeline-name'> <'view-timeline-axis'>?]#",
        'view-timeline-axis': '[block|inline|x|y]#',
        'view-timeline-inset': '[[auto|<length-percentage>]{1,2}]#',
        'view-timeline-name': 'none|<dashed-ident>#',
        'view-transition-name': 'none|<custom-ident>',
        visibility: 'visible|hidden|collapse',
        'white-space':
            "normal|pre|nowrap|pre-wrap|pre-line|break-spaces|[<'white-space-collapse'>||<'text-wrap'>||<'white-space-trim'>]",
        'white-space-collapse':
            'collapse|discard|preserve|preserve-breaks|preserve-spaces|break-spaces',
        widows: '<integer>',
        width: 'auto|<length>|<percentage>|min-content|max-content|fit-content|fit-content( <length-percentage> )|stretch|<-non-standard-size>',
        'will-change': 'auto|<animateable-feature>#',
        'word-break': 'normal|break-all|keep-all|break-word|auto-phrase',
        'word-spacing': 'normal|<length>',
        'word-wrap': 'normal|break-word',
        'writing-mode':
            'horizontal-tb|vertical-rl|vertical-lr|sideways-rl|sideways-lr|<svg-writing-mode>',
        x: '<length>|<percentage>',
        y: '<length>|<percentage>',
        'z-index': 'auto|<integer>',
        zoom: 'normal|reset|<number>|<percentage>',
        '-moz-background-clip': 'padding|border',
        '-moz-border-radius-bottomleft': "<'border-bottom-left-radius'>",
        '-moz-border-radius-bottomright': "<'border-bottom-right-radius'>",
        '-moz-border-radius-topleft': "<'border-top-left-radius'>",
        '-moz-border-radius-topright': "<'border-bottom-right-radius'>",
        '-moz-control-character-visibility': 'visible|hidden',
        '-moz-osx-font-smoothing': 'auto|grayscale',
        '-moz-user-select': 'none|text|all|-moz-none',
        '-ms-flex-align': 'start|end|center|baseline|stretch',
        '-ms-flex-item-align': 'auto|start|end|center|baseline|stretch',
        '-ms-flex-line-pack': 'start|end|center|justify|distribute|stretch',
        '-ms-flex-negative': "<'flex-shrink'>",
        '-ms-flex-pack': 'start|end|center|justify|distribute',
        '-ms-flex-order': '<integer>',
        '-ms-flex-positive': "<'flex-grow'>",
        '-ms-flex-preferred-size': "<'flex-basis'>",
        '-ms-interpolation-mode': 'nearest-neighbor|bicubic',
        '-ms-grid-column-align': 'start|end|center|stretch',
        '-ms-grid-row-align': 'start|end|center|stretch',
        '-ms-hyphenate-limit-last': 'none|always|column|page|spread',
        '-webkit-background-clip': '[<box>|border|padding|content|text]#',
        '-webkit-column-break-after': 'always|auto|avoid',
        '-webkit-column-break-before': 'always|auto|avoid',
        '-webkit-column-break-inside': 'always|auto|avoid',
        '-webkit-font-smoothing': 'auto|none|antialiased|subpixel-antialiased',
        '-webkit-mask-box-image':
            '[<url>|<gradient>|none] [<length-percentage>{4} <-webkit-mask-box-repeat>{2}]?',
        '-webkit-print-color-adjust': 'economy|exact',
        '-webkit-text-security': 'none|circle|disc|square',
        '-webkit-user-drag': 'none|element|auto',
        '-webkit-user-select': 'auto|none|text|all',
        'alignment-baseline':
            'auto|baseline|before-edge|text-before-edge|middle|central|after-edge|text-after-edge|ideographic|alphabetic|hanging|mathematical',
        'baseline-shift': 'baseline|sub|super|<svg-length>',
        behavior: '<url>+',
        cue: "<'cue-before'> <'cue-after'>?",
        'cue-after': '<url> <decibel>?|none',
        'cue-before': '<url> <decibel>?|none',
        'glyph-orientation-horizontal': '<angle>',
        'glyph-orientation-vertical': '<angle>',
        kerning: 'auto|<svg-length>',
        pause: "<'pause-before'> <'pause-after'>?",
        'pause-after': '<time>|none|x-weak|weak|medium|strong|x-strong',
        'pause-before': '<time>|none|x-weak|weak|medium|strong|x-strong',
        rest: "<'rest-before'> <'rest-after'>?",
        'rest-after': '<time>|none|x-weak|weak|medium|strong|x-strong',
        'rest-before': '<time>|none|x-weak|weak|medium|strong|x-strong',
        src: '[<url> [format( <string># )]?|local( <family-name> )]#',
        speak: 'auto|never|always',
        'speak-as': 'normal|spell-out||digits||[literal-punctuation|no-punctuation]',
        'unicode-range': '<urange>#',
        'voice-balance': '<number>|left|center|right|leftwards|rightwards',
        'voice-duration': 'auto|<time>',
        'voice-family':
            '[[<family-name>|<generic-voice>] ,]* [<family-name>|<generic-voice>]|preserve',
        'voice-pitch':
            '<frequency>&&absolute|[[x-low|low|medium|high|x-high]||[<frequency>|<semitones>|<percentage>]]',
        'voice-range':
            '<frequency>&&absolute|[[x-low|low|medium|high|x-high]||[<frequency>|<semitones>|<percentage>]]',
        'voice-rate': '[normal|x-slow|slow|medium|fast|x-fast]||<percentage>',
        'voice-stress': 'normal|strong|moderate|none|reduced',
        'voice-volume': 'silent|[[x-soft|soft|medium|loud|x-loud]||<decibel>]',
        'white-space-trim': 'none|discard-before||discard-after||discard-inner',
    },
    atrules: {
        charset: { prelude: '<string>', descriptors: null },
        'counter-style': {
            prelude: '<counter-style-name>',
            descriptors: {
                'additive-symbols': '[<integer>&&<symbol>]#',
                fallback: '<counter-style-name>',
                negative: '<symbol> <symbol>?',
                pad: '<integer>&&<symbol>',
                prefix: '<symbol>',
                range: '[[<integer>|infinite]{2}]#|auto',
                'speak-as': 'auto|bullets|numbers|words|spell-out|<counter-style-name>',
                suffix: '<symbol>',
                symbols: '<symbol>+',
                system: 'cyclic|numeric|alphabetic|symbolic|additive|[fixed <integer>?]|[extends <counter-style-name>]',
            },
        },
        document: {
            prelude:
                '[<url>|url-prefix( <string> )|domain( <string> )|media-document( <string> )|regexp( <string> )]#',
            descriptors: null,
        },
        'font-palette-values': {
            prelude: '<dashed-ident>',
            descriptors: {
                'base-palette': 'light|dark|<integer [0,∞]>',
                'font-family': '<family-name>#',
                'override-colors': '[<integer [0,∞]> <absolute-color-base>]#',
            },
        },
        'font-face': {
            prelude: null,
            descriptors: {
                'ascent-override': 'normal|<percentage>',
                'descent-override': 'normal|<percentage>',
                'font-display': '[auto|block|swap|fallback|optional]',
                'font-family': '<family-name>',
                'font-feature-settings': 'normal|<feature-tag-value>#',
                'font-variation-settings': 'normal|[<string> <number>]#',
                'font-stretch': '<font-stretch-absolute>{1,2}',
                'font-style': 'normal|italic|oblique <angle>{0,2}',
                'font-weight': '<font-weight-absolute>{1,2}',
                'line-gap-override': 'normal|<percentage>',
                'size-adjust': '<percentage>',
                src: '[<url> [format( <string># )]?|local( <family-name> )]#',
                'unicode-range': '<urange>#',
            },
        },
        'font-feature-values': { prelude: '<family-name>#', descriptors: null },
        import: {
            prelude:
                '[<string>|<url>] [layer|layer( <layer-name> )]? [supports( [<supports-condition>|<declaration>] )]? <media-query-list>?',
            descriptors: null,
        },
        keyframes: { prelude: '<keyframes-name>', descriptors: null },
        layer: { prelude: '[<layer-name>#|<layer-name>?]', descriptors: null },
        media: { prelude: '<media-query-list>', descriptors: null },
        namespace: { prelude: '<namespace-prefix>? [<string>|<url>]', descriptors: null },
        page: {
            prelude: '<page-selector-list>',
            descriptors: {
                bleed: 'auto|<length>',
                marks: 'none|[crop||cross]',
                'page-orientation': 'upright|rotate-left|rotate-right',
                size: '<length>{1,2}|auto|[<page-size>||[portrait|landscape]]',
            },
        },
        'position-try': {
            prelude: '<dashed-ident>',
            descriptors: {
                top: "<'top'>",
                left: "<'left'>",
                bottom: "<'bottom'>",
                right: "<'right'>",
                'inset-block-start': "<'inset-block-start'>",
                'inset-block-end': "<'inset-block-end'>",
                'inset-inline-start': "<'inset-inline-start'>",
                'inset-inline-end': "<'inset-inline-end'>",
                'inset-block': "<'inset-block'>",
                'inset-inline': "<'inset-inline'>",
                inset: "<'inset'>",
                'margin-top': "<'margin-top'>",
                'margin-left': "<'margin-left'>",
                'margin-bottom': "<'margin-bottom'>",
                'margin-right': "<'margin-right'>",
                'margin-block-start': "<'margin-block-start'>",
                'margin-block-end': "<'margin-block-end'>",
                'margin-inline-start': "<'margin-inline-start'>",
                'margin-inline-end': "<'margin-inline-end'>",
                margin: "<'margin'>",
                'margin-block': "<'margin-block'>",
                'margin-inline': "<'margin-inline'>",
                width: "<'width'>",
                height: "<'height'>",
                'min-width': "<'min-width'>",
                'min-height': "<'min-height'>",
                'max-width': "<'max-width'>",
                'max-height': "<'max-height'>",
                'block-size': "<'block-size'>",
                'inline-size': "<'inline-size'>",
                'min-block-size': "<'min-block-size'>",
                'min-inline-size': "<'min-inline-size'>",
                'max-block-size': "<'max-block-size'>",
                'max-inline-size': "<'max-inline-size'>",
                'align-self': "<'align-self'>|anchor-center",
                'justify-self': "<'justify-self'>|anchor-center",
            },
        },
        property: {
            prelude: '<custom-property-name>',
            descriptors: {
                syntax: '<string>',
                inherits: 'true|false',
                'initial-value': '<declaration-value>?',
            },
        },
        scope: { prelude: '[( <scope-start> )]? [to ( <scope-end> )]?', descriptors: null },
        'starting-style': { prelude: null, descriptors: null },
        supports: { prelude: '<supports-condition>', descriptors: null },
        container: { prelude: '[<container-name>]? <container-condition>', descriptors: null },
        nest: { prelude: '<complex-selector-list>', descriptors: null },
    },
};
var Bi = {};
D(Bi, {
    WhiteSpace: () => Hb,
    Value: () => Vb,
    Url: () => Fb,
    UnicodeRange: () => Gb,
    TypeSelector: () => Yb,
    SupportsDeclaration: () => Xb,
    StyleSheet: () => sb,
    String: () => $b,
    SelectorList: () => vb,
    Selector: () => ab,
    Scope: () => zb,
    Rule: () => pb,
    Raw: () => mb,
    Ratio: () => hb,
    PseudoElementSelector: () => gb,
    PseudoClassSelector: () => bb,
    Percentage: () => nb,
    Parentheses: () => ib,
    Operator: () => lb,
    Number: () => To,
    Nth: () => Po,
    NestingSelector: () => No,
    MediaQueryList: () => ko,
    MediaQuery: () => Ao,
    LayerList: () => Mo,
    Layer: () => Ko,
    Identifier: () => Bo,
    IdSelector: () => Ro,
    Hash: () => Go,
    GeneralEnclosed: () => Eo,
    Function: () => jo,
    FeatureRange: () => Xo,
    FeatureFunction: () => qo,
    Feature: () => Do,
    Dimension: () => vo,
    DeclarationList: () => ao,
    Declaration: () => uo,
    Condition: () => wo,
    Comment: () => co,
    Combinator: () => eo,
    ClassSelector: () => fo,
    CDO: () => oo,
    CDC: () => ro,
    Brackets: () => to,
    Block: () => dn,
    AttributeSelector: () => Tn,
    AtrulePrelude: () => Cn,
    Atrule: () => Sn,
    AnPlusB: () => Un,
});
var Un = {};
D(Un, { structure: () => Zp, parse: () => An, name: () => Tp, generate: () => dp });
var kl = 43,
    Jl = 45,
    Dr = 110,
    Ot = !0,
    Ip = !1;
function $r(l, t) {
    let i = this.tokenStart + l,
        r = this.charCodeAt(i);
    if (r === kl || r === Jl) {
        if (t) this.error('Number sign is not allowed');
        i++;
    }
    for (; i < this.tokenEnd; i++)
        if (!ol(this.charCodeAt(i))) this.error('Integer is expected', i);
}
function St(l) {
    return $r.call(this, 0, l);
}
function ht(l, t) {
    if (!this.cmpChar(this.tokenStart + l, t)) {
        let i = '';
        switch (t) {
            case Dr:
                i = 'N is expected';
                break;
            case Jl:
                i = 'HyphenMinus is expected';
                break;
        }
        this.error(i, this.tokenStart + l);
    }
}
function yn() {
    let l = 0,
        t = 0,
        i = this.tokenType;
    while (i === Y || i === U) i = this.lookupType(++l);
    if (i !== v)
        if (this.isDelim(kl, l) || this.isDelim(Jl, l)) {
            t = this.isDelim(kl, l) ? kl : Jl;
            do i = this.lookupType(++l);
            while (i === Y || i === U);
            if (i !== v) (this.skip(l), St.call(this, Ot));
        } else return null;
    if (l > 0) this.skip(l);
    if (t === 0) {
        if (((i = this.charCodeAt(this.tokenStart)), i !== kl && i !== Jl))
            this.error('Number sign is expected');
    }
    return (St.call(this, t !== 0), t === Jl ? '-' + this.consume(v) : this.consume(v));
}
var Tp = 'AnPlusB',
    Zp = { a: [String, null], b: [String, null] };
function An() {
    let l = this.tokenStart,
        t = null,
        i = null;
    if (this.tokenType === v) (St.call(this, Ip), (i = this.consume(v)));
    else if (this.tokenType === w && this.cmpChar(this.tokenStart, Jl))
        switch (((t = '-1'), ht.call(this, 1, Dr), this.tokenEnd - this.tokenStart)) {
            case 2:
                (this.next(), (i = yn.call(this)));
                break;
            case 3:
                (ht.call(this, 2, Jl),
                    this.next(),
                    this.skipSC(),
                    St.call(this, Ot),
                    (i = '-' + this.consume(v)));
                break;
            default:
                (ht.call(this, 2, Jl),
                    $r.call(this, 3, Ot),
                    this.next(),
                    (i = this.substrToCursor(l + 2)));
        }
    else if (this.tokenType === w || (this.isDelim(kl) && this.lookupType(1) === w)) {
        let r = 0;
        if (((t = '1'), this.isDelim(kl))) ((r = 1), this.next());
        switch ((ht.call(this, 0, Dr), this.tokenEnd - this.tokenStart)) {
            case 1:
                (this.next(), (i = yn.call(this)));
                break;
            case 2:
                (ht.call(this, 1, Jl),
                    this.next(),
                    this.skipSC(),
                    St.call(this, Ot),
                    (i = '-' + this.consume(v)));
                break;
            default:
                (ht.call(this, 1, Jl),
                    $r.call(this, 2, Ot),
                    this.next(),
                    (i = this.substrToCursor(l + r + 1)));
        }
    } else if (this.tokenType === W) {
        let r = this.charCodeAt(this.tokenStart),
            n = r === kl || r === Jl,
            o = this.tokenStart + n;
        for (; o < this.tokenEnd; o++) if (!ol(this.charCodeAt(o))) break;
        if (o === this.tokenStart + n) this.error('Integer is expected', this.tokenStart + n);
        if (
            (ht.call(this, o - this.tokenStart, Dr),
            (t = this.substring(l, o)),
            o + 1 === this.tokenEnd)
        )
            (this.next(), (i = yn.call(this)));
        else if ((ht.call(this, o - this.tokenStart + 1, Jl), o + 2 === this.tokenEnd))
            (this.next(), this.skipSC(), St.call(this, Ot), (i = '-' + this.consume(v)));
        else
            ($r.call(this, o - this.tokenStart + 2, Ot),
                this.next(),
                (i = this.substrToCursor(o + 1)));
    } else this.error();
    if (t !== null && t.charCodeAt(0) === kl) t = t.substr(1);
    if (i !== null && i.charCodeAt(0) === kl) i = i.substr(1);
    return { type: 'AnPlusB', loc: this.getLocation(l, this.tokenStart), a: t, b: i };
}
function dp(l) {
    if (l.a) {
        let t =
            (l.a === '+1' && 'n') || (l.a === '1' && 'n') || (l.a === '-1' && '-n') || l.a + 'n';
        if (l.b) {
            let i = l.b[0] === '-' || l.b[0] === '+' ? l.b : '+' + l.b;
            this.tokenize(t + i);
        } else this.tokenize(t);
    } else this.tokenize(l.b);
}
var Sn = {};
D(Sn, {
    walkContext: () => iu,
    structure: () => ru,
    parse: () => kn,
    name: () => tu,
    generate: () => nu,
});
function Se() {
    return this.Raw(this.consumeUntilLeftCurlyBracketOrSemicolon, !0);
}
function lu() {
    for (let l = 1, t; (t = this.lookupType(l)); l++) {
        if (t === ml) return !0;
        if (t === N || t === S) return !1;
    }
    return !1;
}
var tu = 'Atrule',
    iu = 'atrule',
    ru = { name: String, prelude: ['AtrulePrelude', 'Raw', null], block: ['Block', null] };
function kn(l = !1) {
    let t = this.tokenStart,
        i,
        r,
        n = null,
        o = null;
    if (
        (this.eat(S),
        (i = this.substrToCursor(t + 1)),
        (r = i.toLowerCase()),
        this.skipSC(),
        this.eof === !1 && this.tokenType !== N && this.tokenType !== Z)
    ) {
        if (this.parseAtrulePrelude)
            n = this.parseWithFallback(this.AtrulePrelude.bind(this, i, l), Se);
        else n = Se.call(this, this.tokenIndex);
        this.skipSC();
    }
    switch (this.tokenType) {
        case Z:
            this.next();
            break;
        case N:
            if (hasOwnProperty.call(this.atrule, r) && typeof this.atrule[r].block === 'function')
                o = this.atrule[r].block.call(this, l);
            else o = this.Block(lu.call(this));
            break;
    }
    return {
        type: 'Atrule',
        loc: this.getLocation(t, this.tokenStart),
        name: i,
        prelude: n,
        block: o,
    };
}
function nu(l) {
    if ((this.token(S, '@' + l.name), l.prelude !== null)) this.node(l.prelude);
    if (l.block) this.node(l.block);
    else this.token(Z, ';');
}
var Cn = {};
D(Cn, {
    walkContext: () => bu,
    structure: () => fu,
    parse: () => Nn,
    name: () => ou,
    generate: () => gu,
});
var ou = 'AtrulePrelude',
    bu = 'atrulePrelude',
    fu = { children: [[]] };
function Nn(l) {
    let t = null;
    if (l !== null) l = l.toLowerCase();
    if (
        (this.skipSC(),
        hasOwnProperty.call(this.atrule, l) && typeof this.atrule[l].prelude === 'function')
    )
        t = this.atrule[l].prelude.call(this);
    else t = this.readSequence(this.scope.AtrulePrelude);
    if ((this.skipSC(), this.eof !== !0 && this.tokenType !== N && this.tokenType !== Z))
        this.error('Semicolon or block is expected');
    return { type: 'AtrulePrelude', loc: this.getLocationFromList(t), children: t };
}
function gu(l) {
    this.children(l);
}
var Tn = {};
D(Tn, { structure: () => uu, parse: () => In, name: () => pu, generate: () => zu });
var eu = 36,
    Ne = 42,
    qr = 61,
    hu = 94,
    Pn = 124,
    cu = 126;
function mu() {
    if (this.eof) this.error('Unexpected end of input');
    let l = this.tokenStart,
        t = !1;
    if (this.isDelim(Ne)) ((t = !0), this.next());
    else if (!this.isDelim(Pn)) this.eat(w);
    if (this.isDelim(Pn)) {
        if (this.charCodeAt(this.tokenStart + 1) !== qr) (this.next(), this.eat(w));
        else if (t) this.error('Identifier is expected', this.tokenEnd);
    } else if (t) this.error('Vertical line is expected');
    return {
        type: 'Identifier',
        loc: this.getLocation(l, this.tokenStart),
        name: this.substrToCursor(l),
    };
}
function wu() {
    let l = this.tokenStart,
        t = this.charCodeAt(l);
    if (t !== qr && t !== cu && t !== hu && t !== eu && t !== Ne && t !== Pn)
        this.error('Attribute selector (=, ~=, ^=, $=, *=, |=) is expected');
    if ((this.next(), t !== qr)) {
        if (!this.isDelim(qr)) this.error('Equal sign is expected');
        this.next();
    }
    return this.substrToCursor(l);
}
var pu = 'AttributeSelector',
    uu = {
        name: 'Identifier',
        matcher: [String, null],
        value: ['String', 'Identifier', null],
        flags: [String, null],
    };
function In() {
    let l = this.tokenStart,
        t,
        i = null,
        r = null,
        n = null;
    if ((this.eat(el), this.skipSC(), (t = mu.call(this)), this.skipSC(), this.tokenType !== pl)) {
        if (this.tokenType !== w)
            ((i = wu.call(this)),
                this.skipSC(),
                (r = this.tokenType === gl ? this.String() : this.Identifier()),
                this.skipSC());
        if (this.tokenType === w) ((n = this.consume(w)), this.skipSC());
    }
    return (
        this.eat(pl),
        {
            type: 'AttributeSelector',
            loc: this.getLocation(l, this.tokenStart),
            name: t,
            matcher: i,
            value: r,
            flags: n,
        }
    );
}
function zu(l) {
    if ((this.token($, '['), this.node(l.name), l.matcher !== null))
        (this.tokenize(l.matcher), this.node(l.value));
    if (l.flags !== null) this.token(w, l.flags);
    this.token($, ']');
}
var dn = {};
D(dn, {
    walkContext: () => vu,
    structure: () => Ou,
    parse: () => Zn,
    name: () => _u,
    generate: () => Du,
});
var xu = 38;
function Ie() {
    return this.Raw(null, !0);
}
function Ce() {
    return this.parseWithFallback(this.Rule, Ie);
}
function Pe() {
    return this.Raw(this.consumeUntilSemicolonIncluded, !0);
}
function au() {
    if (this.tokenType === Z) return Pe.call(this, this.tokenIndex);
    let l = this.parseWithFallback(this.Declaration, Pe);
    if (this.tokenType === Z) this.next();
    return l;
}
var _u = 'Block',
    vu = 'block',
    Ou = { children: [['Atrule', 'Rule', 'Declaration']] };
function Zn(l) {
    let t = l ? au : Ce,
        i = this.tokenStart,
        r = this.createList();
    this.eat(N);
    l: while (!this.eof)
        switch (this.tokenType) {
            case ml:
                break l;
            case Y:
            case U:
                this.next();
                break;
            case S:
                r.push(this.parseWithFallback(this.Atrule.bind(this, l), Ie));
                break;
            default:
                if (l && this.isDelim(xu)) r.push(Ce.call(this));
                else r.push(t.call(this));
        }
    if (!this.eof) this.eat(ml);
    return { type: 'Block', loc: this.getLocation(i, this.tokenStart), children: r };
}
function Du(l) {
    (this.token(N, '{'),
        this.children(l, (t) => {
            if (t.type === 'Declaration') this.token(Z, ';');
        }),
        this.token(ml, '}'));
}
var to = {};
D(to, { structure: () => qu, parse: () => lo, name: () => $u, generate: () => su });
var $u = 'Brackets',
    qu = { children: [[]] };
function lo(l, t) {
    let i = this.tokenStart,
        r = null;
    if ((this.eat(el), (r = l.call(this, t)), !this.eof)) this.eat(pl);
    return { type: 'Brackets', loc: this.getLocation(i, this.tokenStart), children: r };
}
function su(l) {
    (this.token($, '['), this.children(l), this.token($, ']'));
}
var ro = {};
D(ro, { structure: () => Xu, parse: () => io, name: () => Ju, generate: () => Wu });
var Ju = 'CDC',
    Xu = [];
function io() {
    let l = this.tokenStart;
    return (this.eat(cl), { type: 'CDC', loc: this.getLocation(l, this.tokenStart) });
}
function Wu() {
    this.token(cl, '-->');
}
var oo = {};
D(oo, { structure: () => Yu, parse: () => no, name: () => ju, generate: () => Eu });
var ju = 'CDO',
    Yu = [];
function no() {
    let l = this.tokenStart;
    return (this.eat(Pl), { type: 'CDO', loc: this.getLocation(l, this.tokenStart) });
}
function Eu() {
    this.token(Pl, '<!--');
}
var fo = {};
D(fo, { structure: () => Lu, parse: () => bo, name: () => Gu, generate: () => Bu });
var Qu = 46,
    Gu = 'ClassSelector',
    Lu = { name: String };
function bo() {
    return (
        this.eatDelim(Qu),
        {
            type: 'ClassSelector',
            loc: this.getLocation(this.tokenStart - 1, this.tokenEnd),
            name: this.consume(w),
        }
    );
}
function Bu(l) {
    (this.token($, '.'), this.token(w, l.name));
}
var eo = {};
D(eo, { structure: () => Hu, parse: () => go, name: () => Ku, generate: () => Mu });
var Fu = 43,
    Te = 47,
    Ru = 62,
    Vu = 126,
    Ku = 'Combinator',
    Hu = { name: String };
function go() {
    let l = this.tokenStart,
        t;
    switch (this.tokenType) {
        case Y:
            t = ' ';
            break;
        case $:
            switch (this.charCodeAt(this.tokenStart)) {
                case Ru:
                case Fu:
                case Vu:
                    this.next();
                    break;
                case Te:
                    (this.next(), this.eatIdent('deep'), this.eatDelim(Te));
                    break;
                default:
                    this.error('Combinator is expected');
            }
            t = this.substrToCursor(l);
            break;
    }
    return { type: 'Combinator', loc: this.getLocation(l, this.tokenStart), name: t };
}
function Mu(l) {
    this.tokenize(l.name);
}
var co = {};
D(co, { structure: () => ku, parse: () => ho, name: () => Uu, generate: () => Su });
var yu = 42,
    Au = 47,
    Uu = 'Comment',
    ku = { value: String };
function ho() {
    let l = this.tokenStart,
        t = this.tokenEnd;
    if (
        (this.eat(U),
        t - l + 2 >= 2 && this.charCodeAt(t - 2) === yu && this.charCodeAt(t - 1) === Au)
    )
        t -= 2;
    return {
        type: 'Comment',
        loc: this.getLocation(l, this.tokenStart),
        value: this.substring(l + 2, t),
    };
}
function Su(l) {
    this.token(U, '/*' + l.value + '*/');
}
var wo = {};
D(wo, { structure: () => Pu, parse: () => mo, name: () => Cu, generate: () => Tu });
var Nu = new Set([P, a, Yl]),
    Cu = 'Condition',
    Pu = {
        kind: String,
        children: [
            ['Identifier', 'Feature', 'FeatureFunction', 'FeatureRange', 'SupportsDeclaration'],
        ],
    };
function Ze(l) {
    if (this.lookupTypeNonSC(1) === w && Nu.has(this.lookupTypeNonSC(2))) return this.Feature(l);
    return this.FeatureRange(l);
}
var Iu = {
    media: Ze,
    container: Ze,
    supports() {
        return this.SupportsDeclaration();
    },
};
function mo(l = 'media') {
    let t = this.createList();
    l: while (!this.eof)
        switch (this.tokenType) {
            case U:
            case Y:
                this.next();
                continue;
            case w:
                t.push(this.Identifier());
                break;
            case j: {
                let i = this.parseWithFallback(
                    () => Iu[l].call(this, l),
                    () => null,
                );
                if (!i)
                    i = this.parseWithFallback(
                        () => {
                            this.eat(j);
                            let r = this.Condition(l);
                            return (this.eat(a), r);
                        },
                        () => {
                            return this.GeneralEnclosed(l);
                        },
                    );
                t.push(i);
                break;
            }
            case _: {
                let i = this.parseWithFallback(
                    () => this.FeatureFunction(l),
                    () => null,
                );
                if (!i) i = this.GeneralEnclosed(l);
                t.push(i);
                break;
            }
            default:
                break l;
        }
    if (t.isEmpty) this.error('Condition is expected');
    return { type: 'Condition', loc: this.getLocationFromList(t), kind: l, children: t };
}
function Tu(l) {
    l.children.forEach((t) => {
        if (t.type === 'Condition') (this.token(j, '('), this.node(t), this.token(a, ')'));
        else this.node(t);
    });
}
var uo = {};
D(uo, {
    walkContext: () => f1,
    structure: () => g1,
    parse: () => po,
    name: () => b1,
    generate: () => e1,
});
var l0 = 33,
    Zu = 35,
    du = 36,
    l1 = 38,
    t1 = 42,
    i1 = 43,
    de = 47;
function r1() {
    return this.Raw(this.consumeUntilExclamationMarkOrSemicolon, !0);
}
function n1() {
    return this.Raw(this.consumeUntilExclamationMarkOrSemicolon, !1);
}
function o1() {
    let l = this.tokenIndex,
        t = this.Value();
    if (
        t.type !== 'Raw' &&
        this.eof === !1 &&
        this.tokenType !== Z &&
        this.isDelim(l0) === !1 &&
        this.isBalanceEdge(l) === !1
    )
        this.error();
    return t;
}
var b1 = 'Declaration',
    f1 = 'declaration',
    g1 = { important: [Boolean, String], property: String, value: ['Value', 'Raw'] };
function po() {
    let l = this.tokenStart,
        t = this.tokenIndex,
        i = h1.call(this),
        r = ur(i),
        n = r ? this.parseCustomProperty : this.parseValue,
        o = r ? n1 : r1,
        b = !1,
        g;
    (this.skipSC(), this.eat(P));
    let e = this.tokenIndex;
    if (!r) this.skipSC();
    if (n) g = this.parseWithFallback(o1, o);
    else g = o.call(this, this.tokenIndex);
    if (r && g.type === 'Value' && g.children.isEmpty) {
        for (let f = e - this.tokenIndex; f <= 0; f++)
            if (this.lookupType(f) === Y) {
                g.children.appendData({ type: 'WhiteSpace', loc: null, value: ' ' });
                break;
            }
    }
    if (this.isDelim(l0)) ((b = c1.call(this)), this.skipSC());
    if (this.eof === !1 && this.tokenType !== Z && this.isBalanceEdge(t) === !1) this.error();
    return {
        type: 'Declaration',
        loc: this.getLocation(l, this.tokenStart),
        important: b,
        property: i,
        value: g,
    };
}
function e1(l) {
    if ((this.token(w, l.property), this.token(P, ':'), this.node(l.value), l.important))
        (this.token($, '!'), this.token(w, l.important === !0 ? 'important' : l.important));
}
function h1() {
    let l = this.tokenStart;
    if (this.tokenType === $)
        switch (this.charCodeAt(this.tokenStart)) {
            case t1:
            case du:
            case i1:
            case Zu:
            case l1:
                this.next();
                break;
            case de:
                if ((this.next(), this.isDelim(de))) this.next();
                break;
        }
    if (this.tokenType === F) this.eat(F);
    else this.eat(w);
    return this.substrToCursor(l);
}
function c1() {
    (this.eat($), this.skipSC());
    let l = this.consume(w);
    return l === 'important' ? !0 : l;
}
var ao = {};
D(ao, { structure: () => p1, parse: () => xo, name: () => w1, generate: () => u1 });
var m1 = 38;
function zo() {
    return this.Raw(this.consumeUntilSemicolonIncluded, !0);
}
var w1 = 'DeclarationList',
    p1 = { children: [['Declaration', 'Atrule', 'Rule']] };
function xo() {
    let l = this.createList();
    l: while (!this.eof)
        switch (this.tokenType) {
            case Y:
            case U:
            case Z:
                this.next();
                break;
            case S:
                l.push(this.parseWithFallback(this.Atrule.bind(this, !0), zo));
                break;
            default:
                if (this.isDelim(m1)) l.push(this.parseWithFallback(this.Rule, zo));
                else l.push(this.parseWithFallback(this.Declaration, zo));
        }
    return { type: 'DeclarationList', loc: this.getLocationFromList(l), children: l };
}
function u1(l) {
    this.children(l, (t) => {
        if (t.type === 'Declaration') this.token(Z, ';');
    });
}
var vo = {};
D(vo, { structure: () => x1, parse: () => _o, name: () => z1, generate: () => a1 });
var z1 = 'Dimension',
    x1 = { value: String, unit: String };
function _o() {
    let l = this.tokenStart,
        t = this.consumeNumber(W);
    return {
        type: 'Dimension',
        loc: this.getLocation(l, this.tokenStart),
        value: t,
        unit: this.substring(l + t.length, this.tokenStart),
    };
}
function a1(l) {
    this.token(W, l.value + l.unit);
}
var Do = {};
D(Do, { structure: () => O1, parse: () => Oo, name: () => v1, generate: () => D1 });
var _1 = 47,
    v1 = 'Feature',
    O1 = {
        kind: String,
        name: String,
        value: ['Identifier', 'Number', 'Dimension', 'Ratio', 'Function', null],
    };
function Oo(l) {
    let t = this.tokenStart,
        i,
        r = null;
    if ((this.eat(j), this.skipSC(), (i = this.consume(w)), this.skipSC(), this.tokenType !== a)) {
        switch ((this.eat(P), this.skipSC(), this.tokenType)) {
            case v:
                if (this.lookupNonWSType(1) === $) r = this.Ratio();
                else r = this.Number();
                break;
            case W:
                r = this.Dimension();
                break;
            case w:
                r = this.Identifier();
                break;
            case _:
                r = this.parseWithFallback(
                    () => {
                        let n = this.Function(this.readSequence, this.scope.Value);
                        if ((this.skipSC(), this.isDelim(_1))) this.error();
                        return n;
                    },
                    () => {
                        return this.Ratio();
                    },
                );
                break;
            default:
                this.error('Number, dimension, ratio or identifier is expected');
        }
        this.skipSC();
    }
    if (!this.eof) this.eat(a);
    return {
        type: 'Feature',
        loc: this.getLocation(t, this.tokenStart),
        kind: l,
        name: i,
        value: r,
    };
}
function D1(l) {
    if ((this.token(j, '('), this.token(w, l.name), l.value !== null))
        (this.token(P, ':'), this.node(l.value));
    this.token(a, ')');
}
var qo = {};
D(qo, { structure: () => q1, parse: () => $o, name: () => $1, generate: () => J1 });
var $1 = 'FeatureFunction',
    q1 = { kind: String, feature: String, value: ['Declaration', 'Selector'] };
function s1(l, t) {
    let r = (this.features[l] || {})[t];
    if (typeof r !== 'function') this.error(`Unknown feature ${t}()`);
    return r;
}
function $o(l = 'unknown') {
    let t = this.tokenStart,
        i = this.consumeFunctionName(),
        r = s1.call(this, l, i.toLowerCase());
    this.skipSC();
    let n = this.parseWithFallback(
        () => {
            let o = this.tokenIndex,
                b = r.call(this);
            if (this.eof === !1 && this.isBalanceEdge(o) === !1) this.error();
            return b;
        },
        () => this.Raw(null, !1),
    );
    if (!this.eof) this.eat(a);
    return {
        type: 'FeatureFunction',
        loc: this.getLocation(t, this.tokenStart),
        kind: l,
        feature: i,
        value: n,
    };
}
function J1(l) {
    (this.token(_, l.feature + '('), this.node(l.value), this.token(a, ')'));
}
var Xo = {};
D(Xo, { structure: () => Y1, parse: () => Jo, name: () => j1, generate: () => E1 });
var t0 = 47,
    X1 = 60,
    i0 = 61,
    W1 = 62,
    j1 = 'FeatureRange',
    Y1 = {
        kind: String,
        left: ['Identifier', 'Number', 'Dimension', 'Ratio', 'Function'],
        leftComparison: String,
        middle: ['Identifier', 'Number', 'Dimension', 'Ratio', 'Function'],
        rightComparison: [String, null],
        right: ['Identifier', 'Number', 'Dimension', 'Ratio', 'Function', null],
    };
function so() {
    switch ((this.skipSC(), this.tokenType)) {
        case v:
            if (this.isDelim(t0, this.lookupOffsetNonSC(1))) return this.Ratio();
            else return this.Number();
        case W:
            return this.Dimension();
        case w:
            return this.Identifier();
        case _:
            return this.parseWithFallback(
                () => {
                    let l = this.Function(this.readSequence, this.scope.Value);
                    if ((this.skipSC(), this.isDelim(t0))) this.error();
                    return l;
                },
                () => {
                    return this.Ratio();
                },
            );
        default:
            this.error('Number, dimension, ratio or identifier is expected');
    }
}
function r0(l) {
    if ((this.skipSC(), this.isDelim(X1) || this.isDelim(W1))) {
        let t = this.source[this.tokenStart];
        if ((this.next(), this.isDelim(i0))) return (this.next(), t + '=');
        return t;
    }
    if (this.isDelim(i0)) return '=';
    this.error(`Expected ${l ? '":", ' : ''}"<", ">", "=" or ")"`);
}
function Jo(l = 'unknown') {
    let t = this.tokenStart;
    (this.skipSC(), this.eat(j));
    let i = so.call(this),
        r = r0.call(this, i.type === 'Identifier'),
        n = so.call(this),
        o = null,
        b = null;
    if (this.lookupNonWSType(0) !== a) ((o = r0.call(this)), (b = so.call(this)));
    return (
        this.skipSC(),
        this.eat(a),
        {
            type: 'FeatureRange',
            loc: this.getLocation(t, this.tokenStart),
            kind: l,
            left: i,
            leftComparison: r,
            middle: n,
            rightComparison: o,
            right: b,
        }
    );
}
function E1(l) {
    if (
        (this.token(j, '('),
        this.node(l.left),
        this.tokenize(l.leftComparison),
        this.node(l.middle),
        l.right)
    )
        (this.tokenize(l.rightComparison), this.node(l.right));
    this.token(a, ')');
}
var jo = {};
D(jo, {
    walkContext: () => G1,
    structure: () => L1,
    parse: () => Wo,
    name: () => Q1,
    generate: () => B1,
});
var Q1 = 'Function',
    G1 = 'function',
    L1 = { name: String, children: [[]] };
function Wo(l, t) {
    let i = this.tokenStart,
        r = this.consumeFunctionName(),
        n = r.toLowerCase(),
        o;
    if (((o = t.hasOwnProperty(n) ? t[n].call(this, t) : l.call(this, t)), !this.eof)) this.eat(a);
    return { type: 'Function', loc: this.getLocation(i, this.tokenStart), name: r, children: o };
}
function B1(l) {
    (this.token(_, l.name + '('), this.children(l), this.token(a, ')'));
}
var Eo = {};
D(Eo, { structure: () => R1, parse: () => Yo, name: () => F1, generate: () => V1 });
var F1 = 'GeneralEnclosed',
    R1 = { kind: String, function: [String, null], children: [[]] };
function Yo(l) {
    let t = this.tokenStart,
        i = null;
    if (this.tokenType === _) i = this.consumeFunctionName();
    else this.eat(j);
    let r = this.parseWithFallback(
        () => {
            let n = this.tokenIndex,
                o = this.readSequence(this.scope.Value);
            if (this.eof === !1 && this.isBalanceEdge(n) === !1) this.error();
            return o;
        },
        () => this.createSingleNodeList(this.Raw(null, !1)),
    );
    if (!this.eof) this.eat(a);
    return {
        type: 'GeneralEnclosed',
        loc: this.getLocation(t, this.tokenStart),
        kind: l,
        function: i,
        children: r,
    };
}
function V1(l) {
    if (l.function) this.token(_, l.function + '(');
    else this.token(j, '(');
    (this.children(l), this.token(a, ')'));
}
var Go = {};
D(Go, { xxx: () => K1, structure: () => M1, parse: () => Qo, name: () => H1, generate: () => y1 });
var K1 = 'XXX',
    H1 = 'Hash',
    M1 = { value: String };
function Qo() {
    let l = this.tokenStart;
    return (
        this.eat(F),
        {
            type: 'Hash',
            loc: this.getLocation(l, this.tokenStart),
            value: this.substrToCursor(l + 1),
        }
    );
}
function y1(l) {
    this.token(F, '#' + l.value);
}
var Bo = {};
D(Bo, { structure: () => U1, parse: () => Lo, name: () => A1, generate: () => k1 });
var A1 = 'Identifier',
    U1 = { name: String };
function Lo() {
    return {
        type: 'Identifier',
        loc: this.getLocation(this.tokenStart, this.tokenEnd),
        name: this.consume(w),
    };
}
function k1(l) {
    this.token(w, l.name);
}
var Ro = {};
D(Ro, { structure: () => N1, parse: () => Fo, name: () => S1, generate: () => C1 });
var S1 = 'IdSelector',
    N1 = { name: String };
function Fo() {
    let l = this.tokenStart;
    return (
        this.eat(F),
        {
            type: 'IdSelector',
            loc: this.getLocation(l, this.tokenStart),
            name: this.substrToCursor(l + 1),
        }
    );
}
function C1(l) {
    this.token($, '#' + l.name);
}
var Ko = {};
D(Ko, { structure: () => T1, parse: () => Vo, name: () => I1, generate: () => Z1 });
var P1 = 46,
    I1 = 'Layer',
    T1 = { name: String };
function Vo() {
    let l = this.tokenStart,
        t = this.consume(w);
    while (this.isDelim(P1)) (this.eat($), (t += '.' + this.consume(w)));
    return { type: 'Layer', loc: this.getLocation(l, this.tokenStart), name: t };
}
function Z1(l) {
    this.tokenize(l.name);
}
var Mo = {};
D(Mo, { structure: () => l2, parse: () => Ho, name: () => d1, generate: () => t2 });
var d1 = 'LayerList',
    l2 = { children: [['Layer']] };
function Ho() {
    let l = this.createList();
    this.skipSC();
    while (!this.eof) {
        if ((l.push(this.Layer()), this.lookupTypeNonSC(0) !== d)) break;
        (this.skipSC(), this.next(), this.skipSC());
    }
    return { type: 'LayerList', loc: this.getLocationFromList(l), children: l };
}
function t2(l) {
    this.children(l, () => this.token(d, ','));
}
var Ao = {};
D(Ao, { structure: () => r2, parse: () => yo, name: () => i2, generate: () => n2 });
var i2 = 'MediaQuery',
    r2 = { modifier: [String, null], mediaType: [String, null], condition: ['Condition', null] };
function yo() {
    let l = this.tokenStart,
        t = null,
        i = null,
        r = null;
    if ((this.skipSC(), this.tokenType === w && this.lookupTypeNonSC(1) !== j)) {
        let n = this.consume(w),
            o = n.toLowerCase();
        if (o === 'not' || o === 'only') (this.skipSC(), (t = o), (i = this.consume(w)));
        else i = n;
        switch (this.lookupTypeNonSC(0)) {
            case w: {
                (this.skipSC(), this.eatIdent('and'), (r = this.Condition('media')));
                break;
            }
            case N:
            case Z:
            case d:
            case Yl:
                break;
            default:
                this.error('Identifier or parenthesis is expected');
        }
    } else
        switch (this.tokenType) {
            case w:
            case j:
            case _: {
                r = this.Condition('media');
                break;
            }
            case N:
            case Z:
            case Yl:
                break;
            default:
                this.error('Identifier or parenthesis is expected');
        }
    return {
        type: 'MediaQuery',
        loc: this.getLocation(l, this.tokenStart),
        modifier: t,
        mediaType: i,
        condition: r,
    };
}
function n2(l) {
    if (l.mediaType) {
        if (l.modifier) this.token(w, l.modifier);
        if ((this.token(w, l.mediaType), l.condition))
            (this.token(w, 'and'), this.node(l.condition));
    } else if (l.condition) this.node(l.condition);
}
var ko = {};
D(ko, { structure: () => b2, parse: () => Uo, name: () => o2, generate: () => f2 });
var o2 = 'MediaQueryList',
    b2 = { children: [['MediaQuery']] };
function Uo() {
    let l = this.createList();
    this.skipSC();
    while (!this.eof) {
        if ((l.push(this.MediaQuery()), this.tokenType !== d)) break;
        this.next();
    }
    return { type: 'MediaQueryList', loc: this.getLocationFromList(l), children: l };
}
function f2(l) {
    this.children(l, () => this.token(d, ','));
}
var No = {};
D(No, { structure: () => h2, parse: () => So, name: () => e2, generate: () => c2 });
var g2 = 38,
    e2 = 'NestingSelector',
    h2 = {};
function So() {
    let l = this.tokenStart;
    return (
        this.eatDelim(g2),
        { type: 'NestingSelector', loc: this.getLocation(l, this.tokenStart) }
    );
}
function c2() {
    this.token($, '&');
}
var Po = {};
D(Po, { structure: () => w2, parse: () => Co, name: () => m2, generate: () => p2 });
var m2 = 'Nth',
    w2 = { nth: ['AnPlusB', 'Identifier'], selector: ['SelectorList', null] };
function Co() {
    this.skipSC();
    let l = this.tokenStart,
        t = l,
        i = null,
        r;
    if (this.lookupValue(0, 'odd') || this.lookupValue(0, 'even')) r = this.Identifier();
    else r = this.AnPlusB();
    if (((t = this.tokenStart), this.skipSC(), this.lookupValue(0, 'of')))
        (this.next(), (i = this.SelectorList()), (t = this.tokenStart));
    return { type: 'Nth', loc: this.getLocation(l, t), nth: r, selector: i };
}
function p2(l) {
    if ((this.node(l.nth), l.selector !== null)) (this.token(w, 'of'), this.node(l.selector));
}
var To = {};
D(To, { structure: () => z2, parse: () => Io, name: () => u2, generate: () => x2 });
var u2 = 'Number',
    z2 = { value: String };
function Io() {
    return {
        type: 'Number',
        loc: this.getLocation(this.tokenStart, this.tokenEnd),
        value: this.consume(v),
    };
}
function x2(l) {
    this.token(v, l.value);
}
var lb = {};
D(lb, { structure: () => _2, parse: () => Zo, name: () => a2, generate: () => v2 });
var a2 = 'Operator',
    _2 = { value: String };
function Zo() {
    let l = this.tokenStart;
    return (
        this.next(),
        {
            type: 'Operator',
            loc: this.getLocation(l, this.tokenStart),
            value: this.substrToCursor(l),
        }
    );
}
function v2(l) {
    this.tokenize(l.value);
}
var ib = {};
D(ib, { structure: () => D2, parse: () => tb, name: () => O2, generate: () => $2 });
var O2 = 'Parentheses',
    D2 = { children: [[]] };
function tb(l, t) {
    let i = this.tokenStart,
        r = null;
    if ((this.eat(j), (r = l.call(this, t)), !this.eof)) this.eat(a);
    return { type: 'Parentheses', loc: this.getLocation(i, this.tokenStart), children: r };
}
function $2(l) {
    (this.token(j, '('), this.children(l), this.token(a, ')'));
}
var nb = {};
D(nb, { structure: () => s2, parse: () => rb, name: () => q2, generate: () => J2 });
var q2 = 'Percentage',
    s2 = { value: String };
function rb() {
    return {
        type: 'Percentage',
        loc: this.getLocation(this.tokenStart, this.tokenEnd),
        value: this.consumeNumber(A),
    };
}
function J2(l) {
    this.token(A, l.value + '%');
}
var bb = {};
D(bb, {
    walkContext: () => W2,
    structure: () => j2,
    parse: () => ob,
    name: () => X2,
    generate: () => Y2,
});
var X2 = 'PseudoClassSelector',
    W2 = 'function',
    j2 = { name: String, children: [['Raw'], null] };
function ob() {
    let l = this.tokenStart,
        t = null,
        i,
        r;
    if ((this.eat(P), this.tokenType === _)) {
        if (((i = this.consumeFunctionName()), (r = i.toLowerCase()), this.lookupNonWSType(0) == a))
            t = this.createList();
        else if (hasOwnProperty.call(this.pseudo, r))
            (this.skipSC(), (t = this.pseudo[r].call(this)), this.skipSC());
        else ((t = this.createList()), t.push(this.Raw(null, !1)));
        this.eat(a);
    } else i = this.consume(w);
    return {
        type: 'PseudoClassSelector',
        loc: this.getLocation(l, this.tokenStart),
        name: i,
        children: t,
    };
}
function Y2(l) {
    if ((this.token(P, ':'), l.children === null)) this.token(w, l.name);
    else (this.token(_, l.name + '('), this.children(l), this.token(a, ')'));
}
var gb = {};
D(gb, {
    walkContext: () => Q2,
    structure: () => G2,
    parse: () => fb,
    name: () => E2,
    generate: () => L2,
});
var E2 = 'PseudoElementSelector',
    Q2 = 'function',
    G2 = { name: String, children: [['Raw'], null] };
function fb() {
    let l = this.tokenStart,
        t = null,
        i,
        r;
    if ((this.eat(P), this.eat(P), this.tokenType === _)) {
        if (((i = this.consumeFunctionName()), (r = i.toLowerCase()), this.lookupNonWSType(0) == a))
            t = this.createList();
        else if (hasOwnProperty.call(this.pseudo, r))
            (this.skipSC(), (t = this.pseudo[r].call(this)), this.skipSC());
        else ((t = this.createList()), t.push(this.Raw(null, !1)));
        this.eat(a);
    } else i = this.consume(w);
    return {
        type: 'PseudoElementSelector',
        loc: this.getLocation(l, this.tokenStart),
        name: i,
        children: t,
    };
}
function L2(l) {
    if ((this.token(P, ':'), this.token(P, ':'), l.children === null)) this.token(w, l.name);
    else (this.token(_, l.name + '('), this.children(l), this.token(a, ')'));
}
var hb = {};
D(hb, { structure: () => F2, parse: () => eb, name: () => B2, generate: () => R2 });
var n0 = 47;
function o0() {
    switch ((this.skipSC(), this.tokenType)) {
        case v:
            return this.Number();
        case _:
            return this.Function(this.readSequence, this.scope.Value);
        default:
            this.error('Number of function is expected');
    }
}
var B2 = 'Ratio',
    F2 = { left: ['Number', 'Function'], right: ['Number', 'Function', null] };
function eb() {
    let l = this.tokenStart,
        t = o0.call(this),
        i = null;
    if ((this.skipSC(), this.isDelim(n0))) (this.eatDelim(n0), (i = o0.call(this)));
    return { type: 'Ratio', loc: this.getLocation(l, this.tokenStart), left: t, right: i };
}
function R2(l) {
    if ((this.node(l.left), this.token($, '/'), l.right)) this.node(l.right);
    else this.node(v, 1);
}
var mb = {};
D(mb, { structure: () => H2, parse: () => cb, name: () => K2, generate: () => M2 });
function V2() {
    if (this.tokenIndex > 0) {
        if (this.lookupType(-1) === Y)
            return this.tokenIndex > 1
                ? this.getTokenStart(this.tokenIndex - 1)
                : this.firstCharOffset;
    }
    return this.tokenStart;
}
var K2 = 'Raw',
    H2 = { value: String };
function cb(l, t) {
    let i = this.getTokenStart(this.tokenIndex),
        r;
    if (
        (this.skipUntilBalanced(this.tokenIndex, l || this.consumeUntilBalanceEnd),
        t && this.tokenStart > i)
    )
        r = V2.call(this);
    else r = this.tokenStart;
    return { type: 'Raw', loc: this.getLocation(i, r), value: this.substring(i, r) };
}
function M2(l) {
    this.tokenize(l.value);
}
var pb = {};
D(pb, {
    walkContext: () => U2,
    structure: () => k2,
    parse: () => wb,
    name: () => A2,
    generate: () => S2,
});
function b0() {
    return this.Raw(this.consumeUntilLeftCurlyBracket, !0);
}
function y2() {
    let l = this.SelectorList();
    if (l.type !== 'Raw' && this.eof === !1 && this.tokenType !== N) this.error();
    return l;
}
var A2 = 'Rule',
    U2 = 'rule',
    k2 = { prelude: ['SelectorList', 'Raw'], block: ['Block'] };
function wb() {
    let l = this.tokenIndex,
        t = this.tokenStart,
        i,
        r;
    if (this.parseRulePrelude) i = this.parseWithFallback(y2, b0);
    else i = b0.call(this, l);
    return (
        (r = this.Block(!0)),
        { type: 'Rule', loc: this.getLocation(t, this.tokenStart), prelude: i, block: r }
    );
}
function S2(l) {
    (this.node(l.prelude), this.node(l.block));
}
var zb = {};
D(zb, { structure: () => C2, parse: () => ub, name: () => N2, generate: () => P2 });
var N2 = 'Scope',
    C2 = { root: ['SelectorList', 'Raw', null], limit: ['SelectorList', 'Raw', null] };
function ub() {
    let l = null,
        t = null;
    this.skipSC();
    let i = this.tokenStart;
    if (this.tokenType === j)
        (this.next(),
            this.skipSC(),
            (l = this.parseWithFallback(this.SelectorList, () => this.Raw(!1, !0))),
            this.skipSC(),
            this.eat(a));
    if (this.lookupNonWSType(0) === w)
        (this.skipSC(),
            this.eatIdent('to'),
            this.skipSC(),
            this.eat(j),
            this.skipSC(),
            (t = this.parseWithFallback(this.SelectorList, () => this.Raw(!1, !0))),
            this.skipSC(),
            this.eat(a));
    return { type: 'Scope', loc: this.getLocation(i, this.tokenStart), root: l, limit: t };
}
function P2(l) {
    if (l.root) (this.token(j, '('), this.node(l.root), this.token(a, ')'));
    if (l.limit) (this.token(w, 'to'), this.token(j, '('), this.node(l.limit), this.token(a, ')'));
}
var ab = {};
D(ab, { structure: () => T2, parse: () => xb, name: () => I2, generate: () => Z2 });
var I2 = 'Selector',
    T2 = {
        children: [
            [
                'TypeSelector',
                'IdSelector',
                'ClassSelector',
                'AttributeSelector',
                'PseudoClassSelector',
                'PseudoElementSelector',
                'Combinator',
            ],
        ],
    };
function xb() {
    let l = this.readSequence(this.scope.Selector);
    if (this.getFirstListNode(l) === null) this.error('Selector is expected');
    return { type: 'Selector', loc: this.getLocationFromList(l), children: l };
}
function Z2(l) {
    this.children(l);
}
var vb = {};
D(vb, {
    walkContext: () => lz,
    structure: () => tz,
    parse: () => _b,
    name: () => d2,
    generate: () => iz,
});
var d2 = 'SelectorList',
    lz = 'selector',
    tz = { children: [['Selector', 'Raw']] };
function _b() {
    let l = this.createList();
    while (!this.eof) {
        if ((l.push(this.Selector()), this.tokenType === d)) {
            this.next();
            continue;
        }
        break;
    }
    return { type: 'SelectorList', loc: this.getLocationFromList(l), children: l };
}
function iz(l) {
    this.children(l, () => this.token(d, ','));
}
var $b = {};
D($b, { structure: () => nz, parse: () => Db, name: () => rz, generate: () => oz });
var Ob = 92,
    f0 = 34,
    g0 = 39;
function sr(l) {
    let t = l.length,
        i = l.charCodeAt(0),
        r = i === f0 || i === g0 ? 1 : 0,
        n = r === 1 && t > 1 && l.charCodeAt(t - 1) === i ? t - 2 : t - 1,
        o = '';
    for (let b = r; b <= n; b++) {
        let g = l.charCodeAt(b);
        if (g === Ob) {
            if (b === n) {
                if (b !== t - 1) o = l.substr(b + 1);
                break;
            }
            if (((g = l.charCodeAt(++b)), Ol(Ob, g))) {
                let e = b - 1,
                    f = Ml(l, e);
                ((b = f - 1), (o += xi(l.substring(e + 1, f))));
            } else if (g === 13 && l.charCodeAt(b + 1) === 10) b++;
        } else o += l[b];
    }
    return o;
}
function e0(l, t) {
    let i = t ? "'" : '"',
        r = t ? g0 : f0,
        n = '',
        o = !1;
    for (let b = 0; b < l.length; b++) {
        let g = l.charCodeAt(b);
        if (g === 0) {
            n += '�';
            continue;
        }
        if (g <= 31 || g === 127) {
            ((n += '\\' + g.toString(16)), (o = !0));
            continue;
        }
        if (g === r || g === Ob) ((n += '\\' + l.charAt(b)), (o = !1));
        else {
            if (o && (Dl(g) || Hl(g))) n += ' ';
            ((n += l.charAt(b)), (o = !1));
        }
    }
    return i + n + i;
}
var rz = 'String',
    nz = { value: String };
function Db() {
    return {
        type: 'String',
        loc: this.getLocation(this.tokenStart, this.tokenEnd),
        value: sr(this.consume(gl)),
    };
}
function oz(l) {
    this.token(gl, e0(l.value));
}
var sb = {};
D(sb, {
    walkContext: () => gz,
    structure: () => ez,
    parse: () => qb,
    name: () => fz,
    generate: () => hz,
});
var bz = 33;
function c0() {
    return this.Raw(null, !1);
}
var fz = 'StyleSheet',
    gz = 'stylesheet',
    ez = { children: [['Comment', 'CDO', 'CDC', 'Atrule', 'Rule', 'Raw']] };
function qb() {
    let l = this.tokenStart,
        t = this.createList(),
        i;
    l: while (!this.eof) {
        switch (this.tokenType) {
            case Y:
                this.next();
                continue;
            case U:
                if (this.charCodeAt(this.tokenStart + 2) !== bz) {
                    this.next();
                    continue;
                }
                i = this.Comment();
                break;
            case Pl:
                i = this.CDO();
                break;
            case cl:
                i = this.CDC();
                break;
            case S:
                i = this.parseWithFallback(this.Atrule, c0);
                break;
            default:
                i = this.parseWithFallback(this.Rule, c0);
        }
        t.push(i);
    }
    return { type: 'StyleSheet', loc: this.getLocation(l, this.tokenStart), children: t };
}
function hz(l) {
    this.children(l);
}
var Xb = {};
D(Xb, { structure: () => mz, parse: () => Jb, name: () => cz, generate: () => wz });
var cz = 'SupportsDeclaration',
    mz = { declaration: 'Declaration' };
function Jb() {
    let l = this.tokenStart;
    (this.eat(j), this.skipSC());
    let t = this.Declaration();
    if (!this.eof) this.eat(a);
    return {
        type: 'SupportsDeclaration',
        loc: this.getLocation(l, this.tokenStart),
        declaration: t,
    };
}
function wz(l) {
    (this.token(j, '('), this.node(l.declaration), this.token(a, ')'));
}
var Yb = {};
D(Yb, { structure: () => zz, parse: () => jb, name: () => uz, generate: () => xz });
var pz = 42,
    m0 = 124;
function Wb() {
    if (this.tokenType !== w && this.isDelim(pz) === !1)
        this.error('Identifier or asterisk is expected');
    this.next();
}
var uz = 'TypeSelector',
    zz = { name: String };
function jb() {
    let l = this.tokenStart;
    if (this.isDelim(m0)) (this.next(), Wb.call(this));
    else if ((Wb.call(this), this.isDelim(m0))) (this.next(), Wb.call(this));
    return {
        type: 'TypeSelector',
        loc: this.getLocation(l, this.tokenStart),
        name: this.substrToCursor(l),
    };
}
function xz(l) {
    this.tokenize(l.name);
}
var Gb = {};
D(Gb, { structure: () => Oz, parse: () => Qb, name: () => vz, generate: () => Dz });
var w0 = 43,
    p0 = 45,
    Eb = 63;
function Li(l, t) {
    let i = 0;
    for (let r = this.tokenStart + l; r < this.tokenEnd; r++) {
        let n = this.charCodeAt(r);
        if (n === p0 && t && i !== 0) return (Li.call(this, l + i + 1, !1), -1);
        if (!Dl(n))
            this.error(
                t && i !== 0
                    ? 'Hyphen minus' + (i < 6 ? ' or hex digit' : '') + ' is expected'
                    : i < 6
                      ? 'Hex digit is expected'
                      : 'Unexpected input',
                r,
            );
        if (++i > 6) this.error('Too many hex digits', r);
    }
    return (this.next(), i);
}
function Jr(l) {
    let t = 0;
    while (this.isDelim(Eb)) {
        if (++t > l) this.error('Too many question marks');
        this.next();
    }
}
function az(l) {
    if (this.charCodeAt(this.tokenStart) !== l)
        this.error((l === w0 ? 'Plus sign' : 'Hyphen minus') + ' is expected');
}
function _z() {
    let l = 0;
    switch (this.tokenType) {
        case v:
            if (((l = Li.call(this, 1, !0)), this.isDelim(Eb))) {
                Jr.call(this, 6 - l);
                break;
            }
            if (this.tokenType === W || this.tokenType === v) {
                (az.call(this, p0), Li.call(this, 1, !1));
                break;
            }
            break;
        case W:
            if (((l = Li.call(this, 1, !0)), l > 0)) Jr.call(this, 6 - l);
            break;
        default:
            if ((this.eatDelim(w0), this.tokenType === w)) {
                if (((l = Li.call(this, 0, !0)), l > 0)) Jr.call(this, 6 - l);
                break;
            }
            if (this.isDelim(Eb)) {
                (this.next(), Jr.call(this, 5));
                break;
            }
            this.error('Hex digit or question mark is expected');
    }
}
var vz = 'UnicodeRange',
    Oz = { value: String };
function Qb() {
    let l = this.tokenStart;
    return (
        this.eatIdent('u'),
        _z.call(this),
        {
            type: 'UnicodeRange',
            loc: this.getLocation(l, this.tokenStart),
            value: this.substrToCursor(l),
        }
    );
}
function Dz(l) {
    this.tokenize(l.value);
}
var Fb = {};
D(Fb, { structure: () => Wz, parse: () => Bb, name: () => Xz, generate: () => jz });
var $z = 32,
    Lb = 92,
    qz = 34,
    sz = 39,
    Jz = 40,
    u0 = 41;
function z0(l) {
    let t = l.length,
        i = 4,
        r = l.charCodeAt(t - 1) === u0 ? t - 2 : t - 1,
        n = '';
    while (i < r && Hl(l.charCodeAt(i))) i++;
    while (i < r && Hl(l.charCodeAt(r))) r--;
    for (let o = i; o <= r; o++) {
        let b = l.charCodeAt(o);
        if (b === Lb) {
            if (o === r) {
                if (o !== t - 1) n = l.substr(o + 1);
                break;
            }
            if (((b = l.charCodeAt(++o)), Ol(Lb, b))) {
                let g = o - 1,
                    e = Ml(l, g);
                ((o = e - 1), (n += xi(l.substring(g + 1, e))));
            } else if (b === 13 && l.charCodeAt(o + 1) === 10) o++;
        } else n += l[o];
    }
    return n;
}
function x0(l) {
    let t = '',
        i = !1;
    for (let r = 0; r < l.length; r++) {
        let n = l.charCodeAt(r);
        if (n === 0) {
            t += '�';
            continue;
        }
        if (n <= 31 || n === 127) {
            ((t += '\\' + n.toString(16)), (i = !0));
            continue;
        }
        if (n === $z || n === Lb || n === qz || n === sz || n === Jz || n === u0)
            ((t += '\\' + l.charAt(r)), (i = !1));
        else {
            if (i && Dl(n)) t += ' ';
            ((t += l.charAt(r)), (i = !1));
        }
    }
    return 'url(' + t + ')';
}
var Xz = 'Url',
    Wz = { value: String };
function Bb() {
    let l = this.tokenStart,
        t;
    switch (this.tokenType) {
        case tl:
            t = z0(this.consume(tl));
            break;
        case _:
            if (!this.cmpStr(this.tokenStart, this.tokenEnd, 'url('))
                this.error('Function name must be `url`');
            if ((this.eat(_), this.skipSC(), (t = sr(this.consume(gl))), this.skipSC(), !this.eof))
                this.eat(a);
            break;
        default:
            this.error('Url or Function is expected');
    }
    return { type: 'Url', loc: this.getLocation(l, this.tokenStart), value: t };
}
function jz(l) {
    this.token(tl, x0(l.value));
}
var Vb = {};
D(Vb, { structure: () => Ez, parse: () => Rb, name: () => Yz, generate: () => Qz });
var Yz = 'Value',
    Ez = { children: [[]] };
function Rb() {
    let l = this.tokenStart,
        t = this.readSequence(this.scope.Value);
    return { type: 'Value', loc: this.getLocation(l, this.tokenStart), children: t };
}
function Qz(l) {
    this.children(l);
}
var Hb = {};
D(Hb, { structure: () => Bz, parse: () => Kb, name: () => Lz, generate: () => Fz });
var Gz = Object.freeze({ type: 'WhiteSpace', loc: null, value: ' ' }),
    Lz = 'WhiteSpace',
    Bz = { value: String };
function Kb() {
    return (this.eat(Y), Gz);
}
function Fz(l) {
    this.token(Y, l.value);
}
var _0 = { generic: !0, cssWideKeywords: yt, ...ke, node: Bi };
var Ab = {};
D(Ab, { Value: () => s0, Selector: () => $0, AtrulePrelude: () => O0 });
var Rz = 35,
    Vz = 42,
    v0 = 43,
    Kz = 45,
    Hz = 47,
    Mz = 117;
function Fi(l) {
    switch (this.tokenType) {
        case F:
            return this.Hash();
        case d:
            return this.Operator();
        case j:
            return this.Parentheses(this.readSequence, l.recognizer);
        case el:
            return this.Brackets(this.readSequence, l.recognizer);
        case gl:
            return this.String();
        case W:
            return this.Dimension();
        case A:
            return this.Percentage();
        case v:
            return this.Number();
        case _:
            return this.cmpStr(this.tokenStart, this.tokenEnd, 'url(')
                ? this.Url()
                : this.Function(this.readSequence, l.recognizer);
        case tl:
            return this.Url();
        case w:
            if (this.cmpChar(this.tokenStart, Mz) && this.cmpChar(this.tokenStart + 1, v0))
                return this.UnicodeRange();
            else return this.Identifier();
        case $: {
            let t = this.charCodeAt(this.tokenStart);
            if (t === Hz || t === Vz || t === v0 || t === Kz) return this.Operator();
            if (t === Rz) this.error('Hex or identifier is expected', this.tokenStart + 1);
            break;
        }
    }
}
var O0 = { getNode: Fi };
var yz = 35,
    Az = 38,
    Uz = 42,
    kz = 43,
    Sz = 47,
    D0 = 46,
    Nz = 62,
    Cz = 124,
    Pz = 126;
function Iz(l, t) {
    if (t.last !== null && t.last.type !== 'Combinator' && l !== null && l.type !== 'Combinator')
        t.push({ type: 'Combinator', loc: null, name: ' ' });
}
function Tz() {
    switch (this.tokenType) {
        case el:
            return this.AttributeSelector();
        case F:
            return this.IdSelector();
        case P:
            if (this.lookupType(1) === P) return this.PseudoElementSelector();
            else return this.PseudoClassSelector();
        case w:
            return this.TypeSelector();
        case v:
        case A:
            return this.Percentage();
        case W:
            if (this.charCodeAt(this.tokenStart) === D0)
                this.error('Identifier is expected', this.tokenStart + 1);
            break;
        case $: {
            switch (this.charCodeAt(this.tokenStart)) {
                case kz:
                case Nz:
                case Pz:
                case Sz:
                    return this.Combinator();
                case D0:
                    return this.ClassSelector();
                case Uz:
                case Cz:
                    return this.TypeSelector();
                case yz:
                    return this.IdSelector();
                case Az:
                    return this.NestingSelector();
            }
            break;
        }
    }
}
var $0 = { onWhiteSpace: Iz, getNode: Tz };
function Mb() {
    return this.createSingleNodeList(this.Raw(null, !1));
}
function yb() {
    let l = this.createList();
    if ((this.skipSC(), l.push(this.Identifier()), this.skipSC(), this.tokenType === d)) {
        l.push(this.Operator());
        let t = this.tokenIndex,
            i = this.parseCustomProperty
                ? this.Value(null)
                : this.Raw(this.consumeUntilExclamationMarkOrSemicolon, !1);
        if (i.type === 'Value' && i.children.isEmpty) {
            for (let r = t - this.tokenIndex; r <= 0; r++)
                if (this.lookupType(r) === Y) {
                    i.children.appendData({ type: 'WhiteSpace', loc: null, value: ' ' });
                    break;
                }
        }
        l.push(i);
    }
    return l;
}
function q0(l) {
    return (
        l !== null &&
        l.type === 'Operator' &&
        (l.value[l.value.length - 1] === '-' || l.value[l.value.length - 1] === '+')
    );
}
var s0 = {
    getNode: Fi,
    onWhiteSpace(l, t) {
        if (q0(l)) l.value = ' ' + l.value;
        if (q0(t.last)) t.last.value += ' ';
    },
    expression: Mb,
    var: yb,
};
var Zz = new Set(['none', 'and', 'not', 'or']),
    J0 = {
        parse: {
            prelude() {
                let l = this.createList();
                if (this.tokenType === w) {
                    let t = this.substring(this.tokenStart, this.tokenEnd);
                    if (!Zz.has(t.toLowerCase())) l.push(this.Identifier());
                }
                return (l.push(this.Condition('container')), l);
            },
            block(l = !1) {
                return this.Block(l);
            },
        },
    };
var X0 = {
    parse: {
        prelude: null,
        block() {
            return this.Block(!0);
        },
    },
};
function Ub(l, t) {
    return this.parseWithFallback(
        () => {
            try {
                return l.call(this);
            } finally {
                if ((this.skipSC(), this.lookupNonWSType(0) !== a)) this.error();
            }
        },
        t || (() => this.Raw(null, !0)),
    );
}
var W0 = {
        layer() {
            this.skipSC();
            let l = this.createList(),
                t = Ub.call(this, this.Layer);
            if (t.type !== 'Raw' || t.value !== '') l.push(t);
            return l;
        },
        supports() {
            this.skipSC();
            let l = this.createList(),
                t = Ub.call(this, this.Declaration, () =>
                    Ub.call(this, () => this.Condition('supports')),
                );
            if (t.type !== 'Raw' || t.value !== '') l.push(t);
            return l;
        },
    },
    j0 = {
        parse: {
            prelude() {
                let l = this.createList();
                switch (this.tokenType) {
                    case gl:
                        l.push(this.String());
                        break;
                    case tl:
                    case _:
                        l.push(this.Url());
                        break;
                    default:
                        this.error('String or url() is expected');
                }
                if (
                    (this.skipSC(),
                    this.tokenType === w && this.cmpStr(this.tokenStart, this.tokenEnd, 'layer'))
                )
                    l.push(this.Identifier());
                else if (
                    this.tokenType === _ &&
                    this.cmpStr(this.tokenStart, this.tokenEnd, 'layer(')
                )
                    l.push(this.Function(null, W0));
                if (
                    (this.skipSC(),
                    this.tokenType === _ &&
                        this.cmpStr(this.tokenStart, this.tokenEnd, 'supports('))
                )
                    l.push(this.Function(null, W0));
                if (this.lookupNonWSType(0) === w || this.lookupNonWSType(0) === j)
                    l.push(this.MediaQueryList());
                return l;
            },
            block: null,
        },
    };
var Y0 = {
    parse: {
        prelude() {
            return this.createSingleNodeList(this.LayerList());
        },
        block() {
            return this.Block(!1);
        },
    },
};
var E0 = {
    parse: {
        prelude() {
            return this.createSingleNodeList(this.MediaQueryList());
        },
        block(l = !1) {
            return this.Block(l);
        },
    },
};
var Q0 = {
    parse: {
        prelude() {
            return this.createSingleNodeList(this.SelectorList());
        },
        block() {
            return this.Block(!0);
        },
    },
};
var G0 = {
    parse: {
        prelude() {
            return this.createSingleNodeList(this.SelectorList());
        },
        block() {
            return this.Block(!0);
        },
    },
};
var L0 = {
    parse: {
        prelude() {
            return this.createSingleNodeList(this.Scope());
        },
        block(l = !1) {
            return this.Block(l);
        },
    },
};
var B0 = {
    parse: {
        prelude: null,
        block(l = !1) {
            return this.Block(l);
        },
    },
};
var F0 = {
    parse: {
        prelude() {
            return this.createSingleNodeList(this.Condition('supports'));
        },
        block(l = !1) {
            return this.Block(l);
        },
    },
};
var R0 = {
    container: J0,
    'font-face': X0,
    import: j0,
    layer: Y0,
    media: E0,
    nest: Q0,
    page: G0,
    scope: L0,
    'starting-style': B0,
    supports: F0,
};
function V0() {
    let l = this.createList();
    this.skipSC();
    l: while (!this.eof) {
        switch (this.tokenType) {
            case w:
                l.push(this.Identifier());
                break;
            case gl:
                l.push(this.String());
                break;
            case d:
                l.push(this.Operator());
                break;
            case a:
                break l;
            default:
                this.error('Identifier, string or comma is expected');
        }
        this.skipSC();
    }
    return l;
}
var Dt = {
        parse() {
            return this.createSingleNodeList(this.SelectorList());
        },
    },
    kb = {
        parse() {
            return this.createSingleNodeList(this.Selector());
        },
    },
    dz = {
        parse() {
            return this.createSingleNodeList(this.Identifier());
        },
    },
    l4 = { parse: V0 },
    Xr = {
        parse() {
            return this.createSingleNodeList(this.Nth());
        },
    },
    K0 = {
        dir: dz,
        has: Dt,
        lang: l4,
        matches: Dt,
        is: Dt,
        '-moz-any': Dt,
        '-webkit-any': Dt,
        where: Dt,
        not: Dt,
        'nth-child': Xr,
        'nth-last-child': Xr,
        'nth-last-of-type': Xr,
        'nth-of-type': Xr,
        slotted: kb,
        host: kb,
        'host-context': kb,
    };
var Sb = {};
D(Sb, {
    WhiteSpace: () => Kb,
    Value: () => Rb,
    Url: () => Bb,
    UnicodeRange: () => Qb,
    TypeSelector: () => jb,
    SupportsDeclaration: () => Jb,
    StyleSheet: () => qb,
    String: () => Db,
    SelectorList: () => _b,
    Selector: () => xb,
    Scope: () => ub,
    Rule: () => wb,
    Raw: () => cb,
    Ratio: () => eb,
    PseudoElementSelector: () => fb,
    PseudoClassSelector: () => ob,
    Percentage: () => rb,
    Parentheses: () => tb,
    Operator: () => Zo,
    Number: () => Io,
    Nth: () => Co,
    NestingSelector: () => So,
    MediaQueryList: () => Uo,
    MediaQuery: () => yo,
    LayerList: () => Ho,
    Layer: () => Vo,
    Identifier: () => Lo,
    IdSelector: () => Fo,
    Hash: () => Qo,
    GeneralEnclosed: () => Yo,
    Function: () => Wo,
    FeatureRange: () => Jo,
    FeatureFunction: () => $o,
    Feature: () => Oo,
    Dimension: () => _o,
    DeclarationList: () => xo,
    Declaration: () => po,
    Condition: () => mo,
    Comment: () => ho,
    Combinator: () => go,
    ClassSelector: () => bo,
    CDO: () => no,
    CDC: () => io,
    Brackets: () => lo,
    Block: () => Zn,
    AttributeSelector: () => In,
    AtrulePrelude: () => Nn,
    Atrule: () => kn,
    AnPlusB: () => An,
});
var H0 = {
    parseContext: {
        default: 'StyleSheet',
        stylesheet: 'StyleSheet',
        atrule: 'Atrule',
        atrulePrelude(l) {
            return this.AtrulePrelude(l.atrule ? String(l.atrule) : null);
        },
        mediaQueryList: 'MediaQueryList',
        mediaQuery: 'MediaQuery',
        condition(l) {
            return this.Condition(l.kind);
        },
        rule: 'Rule',
        selectorList: 'SelectorList',
        selector: 'Selector',
        block() {
            return this.Block(!0);
        },
        declarationList: 'DeclarationList',
        declaration: 'Declaration',
        value: 'Value',
    },
    features: {
        supports: {
            selector() {
                return this.Selector();
            },
        },
        container: {
            style() {
                return this.Declaration();
            },
        },
    },
    scope: Ab,
    atrule: R0,
    pseudo: K0,
    node: Sb,
};
var M0 = { node: Bi };
var y0 = Mn({ ..._0, ...H0, ...M0 });
var {
    tokenize: O6,
    parse: Nb,
    generate: Cb,
    lexer: D6,
    createLexer: $6,
    walk: Wr,
    find: q6,
    findLast: s6,
    findAll: J6,
    toPlainObject: X6,
    fromPlainObject: W6,
    fork: j6,
} = y0;
class Nt {
    static instance;
    constructor() {}
    injectDefaultStyles() {
        try {
            let l = document.createElement('style');
            ((l.id = 'onlook-stylesheet'),
                (l.textContent = `
            [${'data-onlook-editing-text'}="true"] {
                opacity: 0;
            }
        `),
                document.head.appendChild(l));
        } catch (l) {
            console.warn('Error injecting default styles', l);
        }
    }
    static getInstance() {
        if (!Nt.instance) Nt.instance = new Nt();
        return Nt.instance;
    }
    get stylesheet() {
        let l = document.getElementById('onlook-stylesheet') || this.createStylesheet();
        return ((l.textContent = l.textContent || ''), Nb(l.textContent));
    }
    set stylesheet(l) {
        let t = document.getElementById('onlook-stylesheet') || this.createStylesheet();
        t.textContent = Cb(l);
    }
    createStylesheet() {
        let l = document.createElement('style');
        return ((l.id = 'onlook-stylesheet'), document.head.appendChild(l), l);
    }
    find(l, t) {
        let i = [];
        return (
            Wr(l, {
                visit: 'Rule',
                enter: (r) => {
                    if (r.type === 'Rule') {
                        let n = r;
                        if (n.prelude.type === 'SelectorList')
                            n.prelude.children.forEach((o) => {
                                if (Cb(o) === t) i.push(r);
                            });
                    }
                },
            }),
            i
        );
    }
    updateStyle(l, t) {
        let i = kr(l, !1),
            r = this.stylesheet;
        for (let [n, o] of Object.entries(t)) {
            let b = this.jsToCssProperty(n),
                g = this.find(r, i);
            if (!g.length) this.addRule(r, i, b, o.value);
            else
                g.forEach((e) => {
                    if (e.type === 'Rule') this.updateRule(e, b, o.value);
                });
        }
        this.stylesheet = r;
    }
    addRule(l, t, i, r) {
        let n = {
            type: 'Rule',
            prelude: {
                type: 'SelectorList',
                children: [{ type: 'Selector', children: [{ type: 'TypeSelector', name: t }] }],
            },
            block: {
                type: 'Block',
                children: [{ type: 'Declaration', property: i, value: { type: 'Raw', value: r } }],
            },
        };
        if (l.type === 'StyleSheet') l.children.push(n);
    }
    updateRule(l, t, i) {
        let r = !1;
        if (
            (Wr(l.block, {
                visit: 'Declaration',
                enter: (n) => {
                    if (n.property === t) {
                        if (((n.value = { type: 'Raw', value: i }), i === ''))
                            l.block.children = l.block.children.filter((o) => o.property !== t);
                        r = !0;
                    }
                },
            }),
            !r)
        )
            if (i === '') l.block.children = l.block.children.filter((n) => n.property !== t);
            else
                l.block.children.push({
                    type: 'Declaration',
                    property: t,
                    value: { type: 'Raw', value: i },
                    important: !1,
                });
    }
    getJsStyle(l) {
        let t = this.stylesheet,
            i = this.find(t, l),
            r = {};
        if (!i.length) return r;
        return (
            i.forEach((n) => {
                if (n.type === 'Rule')
                    Wr(n, {
                        visit: 'Declaration',
                        enter: (o) => {
                            r[this.cssToJsProperty(o.property)] = o.value.value;
                        },
                    });
            }),
            r
        );
    }
    jsToCssProperty(l) {
        if (!l) return '';
        return l.replace(/([A-Z])/g, '-$1').toLowerCase();
    }
    cssToJsProperty(l) {
        if (!l) return '';
        return l.replace(/-([a-z])/g, (t) => t[1]?.toUpperCase() ?? '');
    }
    removeStyles(l, t) {
        let i = kr(l, !1),
            r = this.stylesheet;
        (this.find(r, i).forEach((o) => {
            if (o.type === 'Rule') {
                let b = t.map((g) => this.jsToCssProperty(g));
                o.block.children = o.block.children.filter((g) => !b.includes(g.property));
            }
        }),
            (this.stylesheet = r));
    }
    clear() {
        this.stylesheet = Nb('');
    }
}
var tt = Nt.getInstance();
function A0(l, t) {
    return (tt.updateStyle(l, t.updated), Ci(l, !0));
}
function U0(l, t) {
    tt.updateStyle(l, { backgroundImage: { value: `url(${t})`, type: 'value' } });
}
function k0(l) {
    tt.updateStyle(l, { backgroundImage: { value: 'none', type: 'value' } });
}
function i4(l, t) {
    let i = Array.from(l.children);
    if (i.length === 0) return 0;
    let r = 0,
        n = 1 / 0;
    i.forEach((g, e) => {
        let f = g.getBoundingClientRect(),
            h = f.top + f.height / 2,
            c = Math.abs(t - h);
        if (c < n) ((n = c), (r = e));
    });
    let o = i[r]?.getBoundingClientRect();
    if (!o) return 0;
    let b = o.top + o.height / 2;
    return t > b ? r + 1 : r;
}
function S0(l, t) {
    let i = r4(l, t);
    if (!i) return null;
    let r = window.getComputedStyle(i).display;
    if (r === 'flex' || r === 'grid') {
        let o = i4(i, t);
        return {
            type: 'index',
            targetDomId: sl(i),
            targetOid: Kl(i) || Vl(i) || null,
            index: o,
            originalIndex: o,
        };
    }
    return { type: 'append', targetDomId: sl(i), targetOid: Kl(i) || Vl(i) || null };
}
function r4(l, t) {
    let i = og(l, t);
    if (!i) return null;
    let r = !0;
    while (i && r) if (((r = Pf.has(i.tagName.toLowerCase())), r)) i = i.parentElement;
    return i;
}
function N0(l, t) {
    let i = H(t.targetDomId);
    if (!i) {
        console.warn(`Target element not found: ${t.targetDomId}`);
        return;
    }
    let r = C0(l);
    switch (t.type) {
        case 'append':
            i.appendChild(r);
            break;
        case 'prepend':
            i.prepend(r);
            break;
        case 'index':
            if (t.index === void 0 || t.index < 0) {
                console.warn(`Invalid index: ${t.index}`);
                return;
            }
            if (t.index >= i.children.length) i.appendChild(r);
            else i.insertBefore(r, i.children.item(t.index));
            break;
        default:
            (console.warn(`Invalid position: ${t}`), Nr(t));
    }
    let n = nl(r, !0),
        o = zl(r);
    return { domEl: n, newMap: o };
}
function C0(l) {
    let t = document.createElement(l.tagName);
    t.setAttribute('data-onlook-inserted', 'true');
    for (let [i, r] of Object.entries(l.attributes)) t.setAttribute(i, r);
    if (l.textContent !== null && l.textContent !== void 0) t.textContent = l.textContent;
    for (let [i, r] of Object.entries(l.styles)) t.style.setProperty(tt.jsToCssProperty(i), r);
    for (let i of l.children) {
        let r = C0(i);
        t.appendChild(r);
    }
    return t;
}
function P0(l) {
    let t = H(l.targetDomId);
    if (!t) return (console.warn(`Target element not found: ${l.targetDomId}`), null);
    let i = null;
    switch (l.type) {
        case 'append':
            i = t.lastElementChild;
            break;
        case 'prepend':
            i = t.firstElementChild;
            break;
        case 'index':
            if (l.index !== -1) i = t.children.item(l.index);
            else return (console.warn(`Invalid index: ${l.index}`), null);
            break;
        default:
            (console.warn(`Invalid position: ${l}`), Nr(l));
    }
    if (i) {
        let r = nl(i, !0);
        i.style.display = 'none';
        let n = t.parentElement ? zl(t.parentElement) : null;
        return { domEl: r, newMap: n };
    } else return (console.warn('No element found to remove at the specified location'), null);
}
function I0(l, t) {
    let i = H(l);
    if (!i) return (console.warn('Element not found for domId:', l), null);
    let r = bg(i);
    if (!r) return (console.warn('Failed to get location for element:', i), null);
    let n = Pi(l);
    if (!n) return (console.warn('Failed to get action element for element:', i), null);
    return {
        type: 'remove-element',
        targets: [{ frameId: t, domId: n.domId, oid: n.oid }],
        location: r,
        element: n,
        editText: !1,
        pasteParams: null,
        codeBlock: null,
    };
}
function T0(l, t) {
    let i = H(l);
    if (!i) return (console.warn(`Move element not found: ${l}`), null);
    let r = n4(i, t);
    if (!r) return (console.warn(`Failed to move element: ${l}`), null);
    let n = nl(r, !0),
        o = r.parentElement ? zl(r.parentElement) : null;
    return { domEl: n, newMap: o };
}
function Z0(l) {
    let t = H(l);
    if (!t) return (console.warn(`Element not found: ${l}`), -1);
    return Array.from(t.parentElement?.children || [])
        .filter(zt)
        .indexOf(t);
}
function n4(l, t) {
    let i = l.parentElement;
    if (!i) {
        console.warn('Parent not found');
        return;
    }
    if ((i.removeChild(l), t >= i.children.length)) return (i.appendChild(l), l);
    let r = i.children[t];
    return (i.insertBefore(l, r ?? null), l);
}
function jr(l) {
    if (!l || !l.children || l.children.length < 2) return 'vertical';
    let t = Array.from(l.children),
        i = t[0],
        r = t[1],
        n = i?.getBoundingClientRect(),
        o = r?.getBoundingClientRect();
    if (n && o && Math.abs(n.left - o.left) < Math.abs(n.top - o.top)) return 'vertical';
    else return 'horizontal';
}
function d0(l, t, i, r) {
    if (l.length === 0) return 0;
    let n = l.map((o) => {
        let b = o.getBoundingClientRect();
        return { x: b.left + b.width / 2, y: b.top + b.height / 2 };
    });
    if (r === 'horizontal')
        for (let o = 0; o < n.length; o++) {
            let b = n[o];
            if (b && t < b.x) return o;
        }
    else
        for (let o = 0; o < n.length; o++) {
            let b = n[o];
            if (b && i < b.y) return o;
        }
    return l.length;
}
function lh(l, t, i, r) {
    let n = l.getBoundingClientRect(),
        o = window.getComputedStyle(l),
        b = o.gridTemplateColumns.split(' ').length,
        g = o.gridTemplateRows.split(' ').length,
        e = n.width / b,
        f = n.height / g,
        h = Math.floor((i - n.left) / e),
        m = Math.floor((r - n.top) / f) * b + h;
    return Math.min(Math.max(m, 0), t.length);
}
function th(l) {
    let t = document.createElement('div'),
        i = window.getComputedStyle(l),
        r = l.className;
    ((t.id = 'onlook-drag-stub'),
        (t.style.width = i.width),
        (t.style.height = i.height),
        (t.style.margin = i.margin),
        (t.style.padding = i.padding),
        (t.style.borderRadius = i.borderRadius),
        (t.style.backgroundColor = 'rgba(0, 0, 0, 0.2)'),
        (t.style.display = 'none'),
        (t.className = r),
        document.body.appendChild(t));
}
function ih(l, t, i) {
    let r = document.getElementById('onlook-drag-stub');
    if (!r) return;
    let n = l.parentElement;
    if (!n) return;
    let o = l.getAttribute('data-onlook-drag-direction');
    if (!o) o = jr(n);
    let b = window.getComputedStyle(n),
        g = b.display === 'grid';
    if (!g && b.display === 'flex' && (b.flexDirection === 'row' || b.flexDirection === ''))
        o = 'horizontal';
    let f = Array.from(n.children).filter((c) => c !== l && c !== r),
        h;
    if (g) h = lh(n, f, t, i);
    else h = d0(f, t, i, o);
    if ((r.remove(), h >= f.length)) n.appendChild(r);
    else n.insertBefore(r, f[h] ?? null);
    r.style.display = 'block';
}
function Pb() {
    let l = document.getElementById('onlook-drag-stub');
    if (!l) return;
    l.remove();
}
function rh(l, t) {
    let i = document.getElementById('onlook-drag-stub');
    if (!i) return -1;
    return Array.from(l.children)
        .filter((n) => n !== t)
        .indexOf(i);
}
function nh(l) {
    let t = H(l);
    if (!t) return (console.warn(`Start drag element not found: ${l}`), null);
    let i = t.parentElement;
    if (!i) return (console.warn('Start drag parent not found'), null);
    let n = Array.from(i.children).filter(zt).indexOf(t),
        o = window.getComputedStyle(t);
    if ((o4(t), o.position !== 'absolute')) th(t);
    let b = b4(t),
        g = t.getBoundingClientRect(),
        e =
            o.position === 'absolute'
                ? { x: b.left, y: b.top }
                : { x: b.left - g.left, y: b.top - g.top };
    return (
        t.setAttribute('data-onlook-drag-start-position', JSON.stringify({ ...b, offset: e })),
        n
    );
}
function oh(l, t, i, r) {
    let n = H(l);
    if (!n) {
        console.warn('Dragging element not found');
        return;
    }
    let o = n.parentElement;
    if (o) {
        let b = JSON.parse(n.getAttribute('data-onlook-drag-start-position') || '{}'),
            g = o.getBoundingClientRect(),
            e = t - g.left - (r.x - b.offset.x),
            f = i - g.top - (r.y - b.offset.y);
        ((n.style.left = `${e}px`), (n.style.top = `${f}px`));
    }
    n.style.transform = 'none';
}
function bh(l, t, i, r, n) {
    let o = H(l);
    if (!o) {
        console.warn('Dragging element not found');
        return;
    }
    if (!o.style.transition) o.style.transition = 'transform 0.05s cubic-bezier(0.2, 0, 0, 1)';
    let b = JSON.parse(o.getAttribute('data-onlook-drag-start-position') || '{}');
    if (o.style.position !== 'fixed') {
        let e = window.getComputedStyle(o);
        ((o.style.position = 'fixed'),
            (o.style.width = e.width),
            (o.style.height = e.height),
            (o.style.left = `${b.left}px`),
            (o.style.top = `${b.top}px`));
    }
    if (((o.style.transform = `translate(${t}px, ${i}px)`), o.parentElement)) ih(o, r, n);
}
function fh(l) {
    let t = H(l);
    if (!t) return (console.warn('End drag element not found'), null);
    let i = window.getComputedStyle(t);
    return (eh(t), sl(t), { left: i.left, top: i.top });
}
function gh(l) {
    let t = H(l);
    if (!t) return (console.warn('End drag element not found'), Tb(), null);
    let i = t.parentElement;
    if (!i) return (console.warn('End drag parent not found'), Ib(t), null);
    let r = rh(i, t);
    if ((Ib(t), Pb(), r === -1)) return null;
    let n = Array.from(i.children).indexOf(t);
    if (r === n) return null;
    return { newIndex: r, child: nl(t, !1), parent: nl(i, !1) };
}
function o4(l) {
    if (l.getAttribute('data-onlook-drag-saved-style')) return;
    let i = {
        position: l.style.position,
        transform: l.style.transform,
        width: l.style.width,
        height: l.style.height,
        left: l.style.left,
        top: l.style.top,
    };
    if (
        (l.setAttribute('data-onlook-drag-saved-style', JSON.stringify(i)),
        l.setAttribute('data-onlook-dragging', 'true'),
        (l.style.zIndex = '1000'),
        l.getAttribute('data-onlook-drag-direction') !== null)
    ) {
        let r = l.parentElement;
        if (r) {
            let n = jr(r);
            l.setAttribute('data-onlook-drag-direction', n);
        }
    }
}
function Ib(l) {
    (Ni(l), eh(l), sl(l));
}
function eh(l) {
    (l.removeAttribute('data-onlook-drag-saved-style'),
        l.removeAttribute('data-onlook-dragging'),
        l.removeAttribute('data-onlook-drag-direction'),
        l.removeAttribute('data-onlook-drag-start-position'));
}
function b4(l) {
    let t = l.getBoundingClientRect();
    return { left: t.left + window.scrollX, top: t.top + window.scrollY };
}
function Tb() {
    let l = document.querySelectorAll(`[${'data-onlook-dragging'}]`);
    for (let t of Array.from(l)) Ib(t);
    Pb();
}
function hh(l) {
    let t = H(l);
    if (!t) return (console.warn('Start editing text failed. No element for selector:', l), null);
    let i = Array.from(t.childNodes).filter((b) => b.nodeType !== Node.COMMENT_NODE),
        r = null,
        n = i.every(
            (b) =>
                b.nodeType === Node.TEXT_NODE ||
                (b.nodeType === Node.ELEMENT_NODE && b.tagName.toLowerCase() === 'br'),
        );
    if (i.length === 0) r = t;
    else if (i.length === 1 && i[0]?.nodeType === Node.TEXT_NODE) r = t;
    else if (n) r = t;
    if (!r)
        return (
            console.warn('Start editing text failed. No target element found for selector:', l),
            null
        );
    let o = ph(t);
    return (wh(r), { originalContent: o });
}
function ch(l, t) {
    let i = H(l);
    if (!i) return (console.warn('Edit text failed. No element for selector:', l), null);
    return (wh(i), e4(i, t), { domEl: nl(i, !0), newMap: zl(i) });
}
function mh(l) {
    let t = H(l);
    if (!t) return (console.warn('Stop editing text failed. No element for selector:', l), null);
    return (f4(t), { newContent: ph(t), domEl: nl(t, !0) });
}
function wh(l) {
    l.setAttribute('data-onlook-editing-text', 'true');
}
function f4(l) {
    (Ni(l), g4(l));
}
function g4(l) {
    l.removeAttribute('data-onlook-editing-text');
}
function e4(l, t) {
    let i = t.replace(/\n/g, '<br>');
    l.innerHTML = i;
}
function ph(l) {
    let t = l.innerHTML;
    ((t = t.replace(
        /<br\s*\/?>/gi,
        `
`,
    )),
        (t = t.replace(/<[^>]*>/g, '')));
    let i = document.createElement('textarea');
    return ((i.innerHTML = t), i.value);
}
function uh(l) {
    return !0;
}
function xh() {
    let l = document.body,
        t = { childList: !0, subtree: !0 };
    new MutationObserver((r) => {
        let n = new Map(),
            o = new Map();
        for (let b of r)
            if (b.type === 'childList') {
                let g = b.target;
                (b.addedNodes.forEach((e) => {
                    let f = e;
                    if (e.nodeType === Node.ELEMENT_NODE && f.hasAttribute('data-odid') && !zh(f)) {
                        if ((h4(f), g)) {
                            let h = zl(g);
                            if (h) n = new Map([...n, ...h]);
                        }
                    }
                }),
                    b.removedNodes.forEach((e) => {
                        let f = e;
                        if (
                            e.nodeType === Node.ELEMENT_NODE &&
                            f.hasAttribute('data-odid') &&
                            !zh(f)
                        ) {
                            if (g) {
                                let h = zl(g);
                                if (h) o = new Map([...o, ...h]);
                            }
                        }
                    }));
            }
        if (n.size > 0 || o.size > 0) {
            if (vl)
                vl.onWindowMutated({
                    added: Object.fromEntries(n),
                    removed: Object.fromEntries(o),
                }).catch((b) => {
                    console.error('Failed to send window mutation event:', b);
                });
        }
    }).observe(l, t);
}
function ah() {
    function l() {
        if (vl)
            vl.onWindowResized().catch((t) => {
                console.error('Failed to send window resize event:', t);
            });
    }
    window.addEventListener('resize', l);
}
function zh(l) {
    if (l.id === 'onlook-drag-stub') return !0;
    if (l.getAttribute('data-onlook-inserted')) return !0;
    return !1;
}
function h4(l) {
    let t = l.getAttribute('data-oid');
    if (!t) return;
    document.querySelectorAll(`[${'data-oid'}="${t}"][${'data-onlook-inserted'}]`).forEach((i) => {
        ([
            'data-odid',
            'data-onlook-drag-saved-style',
            'data-onlook-editing-text',
            'data-oiid',
        ].forEach((n) => {
            let o = i.getAttribute(n);
            if (o) l.setAttribute(n, o);
        }),
            i.remove());
    });
}
function _h() {
    (xh(), ah());
}
function Zb() {
    (_h(), c4(), tt.injectDefaultStyles());
}
var Ct = null;
function c4() {
    if (Ct !== null) (clearInterval(Ct), (Ct = null));
    let l = setInterval(() => {
        try {
            if (Si() !== null) (clearInterval(l), (Ct = null));
        } catch (t) {
            (clearInterval(l), (Ct = null), console.warn('Error in keepDomUpdated:', t));
        }
    }, 5000);
    Ct = l;
}
var m4 = setInterval(() => {
    if (
        ((window.onerror = function l(t, i, r) {
            console.log(`Unhandled error: ${t} ${i} ${r}`);
        }),
        window?.document?.body)
    ) {
        clearInterval(m4);
        try {
            Zb();
        } catch (l) {
            console.log('Error in documentBodyInit:', l);
        }
    }
}, 300);
async function Oh() {
    try {
        let { innerWidth: l, innerHeight: t } = window,
            i = document.createElement('canvas'),
            r = i.getContext('2d');
        if (!r) throw new Error('Failed to get canvas context');
        if (
            ((i.width = l),
            (i.height = t),
            navigator.mediaDevices && navigator.mediaDevices.getDisplayMedia)
        )
            try {
                let o = await navigator.mediaDevices.getDisplayMedia({
                        video: { width: l, height: t },
                    }),
                    b = document.createElement('video');
                ((b.srcObject = o),
                    (b.autoplay = !0),
                    (b.muted = !0),
                    await new Promise((e) => {
                        b.onloadedmetadata = () => {
                            (b.play(),
                                (b.oncanplay = () => {
                                    (r.drawImage(b, 0, 0, l, t),
                                        o.getTracks().forEach((f) => f.stop()),
                                        e());
                                }));
                        };
                    }));
                let g = await vh(i);
                return (
                    console.log(
                        `Screenshot captured - Size: ~${Math.round((g.length * 0.75) / 1024)} KB`,
                    ),
                    { mimeType: 'image/jpeg', data: g }
                );
            } catch (o) {
                console.log('getDisplayMedia failed, falling back to DOM rendering:', o);
            }
        await w4(r, l, t);
        let n = await vh(i);
        return (
            console.log(
                `DOM screenshot captured - Size: ~${Math.round((n.length * 0.75) / 1024)} KB`,
            ),
            { mimeType: 'image/jpeg', data: n }
        );
    } catch (l) {
        console.error('Failed to capture screenshot:', l);
        let t = document.createElement('canvas'),
            i = t.getContext('2d');
        if (i)
            return (
                (t.width = 400),
                (t.height = 300),
                (i.fillStyle = '#ffffff'),
                i.fillRect(0, 0, 400, 300),
                (i.fillStyle = '#ff0000'),
                (i.font = '14px Arial, sans-serif'),
                (i.textAlign = 'center'),
                i.fillText('Screenshot unavailable', 200, 150),
                { mimeType: 'image/jpeg', data: t.toDataURL('image/jpeg', 0.8) }
            );
        throw l;
    }
}
async function vh(l) {
    let r = [0.9, 0.8, 0.7, 0.6, 0.5, 0.4, 0.3],
        n = [1, 0.8, 0.6, 0.5, 0.4, 0.3];
    for (let g of n) {
        let e = l;
        if (g < 1) {
            e = document.createElement('canvas');
            let f = e.getContext('2d');
            if (!f) continue;
            ((e.width = l.width * g),
                (e.height = l.height * g),
                f.drawImage(l, 0, 0, e.width, e.height));
        }
        for (let f of r) {
            let h = e.toDataURL('image/jpeg', f);
            if (h.length <= 3932160) return h;
        }
    }
    let o = document.createElement('canvas'),
        b = o.getContext('2d');
    if (b)
        return (
            (o.width = l.width * 0.2),
            (o.height = l.height * 0.2),
            b.drawImage(l, 0, 0, o.width, o.height),
            o.toDataURL('image/jpeg', 0.2)
        );
    return l.toDataURL('image/jpeg', 0.1);
}
async function w4(l, t, i) {
    ((l.fillStyle = '#ffffff'), l.fillRect(0, 0, t, i));
    let r = document.querySelectorAll('*'),
        n = [];
    for (let o of r)
        if (o instanceof HTMLElement) {
            let b = o.getBoundingClientRect(),
                g = window.getComputedStyle(o);
            if (
                b.width > 0 &&
                b.height > 0 &&
                b.left < t &&
                b.top < i &&
                b.right > 0 &&
                b.bottom > 0 &&
                g.visibility !== 'hidden' &&
                g.display !== 'none' &&
                parseFloat(g.opacity) > 0
            )
                n.push({ element: o, rect: b, styles: g });
        }
    n.sort((o, b) => {
        let g = parseInt(o.styles.zIndex) || 0,
            e = parseInt(b.styles.zIndex) || 0;
        return g - e;
    });
    for (let { element: o, rect: b, styles: g } of n)
        try {
            await p4(l, o, b, g);
        } catch (e) {
            console.warn('Failed to render element:', o, e);
        }
}
async function p4(l, t, i, r) {
    let { left: n, top: o, width: b, height: g } = i;
    if (b < 1 || g < 1 || n > window.innerWidth || o > window.innerHeight) return;
    let e = r.backgroundColor;
    if (e && e !== 'rgba(0, 0, 0, 0)' && e !== 'transparent')
        ((l.fillStyle = e), l.fillRect(n, o, b, g));
    let f = parseFloat(r.borderWidth) || 0,
        h = r.borderColor;
    if (f > 0 && h && h !== 'transparent')
        ((l.strokeStyle = h), (l.lineWidth = f), l.strokeRect(n, o, b, g));
    if (t.textContent && t.children.length === 0) {
        let c = t.textContent.trim();
        if (c) {
            let m = parseFloat(r.fontSize) || 16,
                u = r.fontFamily || 'Arial, sans-serif',
                J = r.color || '#000000';
            ((l.fillStyle = J),
                (l.font = `${m}px ${u}`),
                (l.textAlign = 'left'),
                (l.textBaseline = 'top'));
            let I = c.split(' '),
                R = '',
                q = o + 2,
                V = m * 1.2;
            for (let fl of I) {
                let K = R + fl + ' ';
                if (l.measureText(K).width > b - 4 && R !== '') {
                    if ((l.fillText(R, n + 2, q), (R = fl + ' '), (q += V), q > o + g)) break;
                } else R = K;
            }
            if (R && q <= o + g) l.fillText(R, n + 2, q);
        }
    }
    if (t instanceof HTMLImageElement && t.complete && t.naturalWidth > 0)
        try {
            l.drawImage(t, n, o, b, g);
        } catch (c) {
            ((l.fillStyle = '#f0f0f0'),
                l.fillRect(n, o, b, g),
                (l.fillStyle = '#999999'),
                (l.font = '12px Arial, sans-serif'),
                (l.textAlign = 'center'),
                l.fillText('Image', n + b / 2, o + g / 2));
        }
}
var rl = {};
D(rl, {
    void: () => Z4,
    util: () => B,
    unknown: () => I4,
    union: () => ix,
    undefined: () => N4,
    tuple: () => ox,
    transformer: () => ux,
    symbol: () => S4,
    string: () => Yh,
    strictObject: () => tx,
    setErrorMap: () => x4,
    set: () => gx,
    record: () => bx,
    quotelessJson: () => u4,
    promise: () => px,
    preprocess: () => ax,
    pipeline: () => _x,
    ostring: () => vx,
    optional: () => zx,
    onumber: () => Ox,
    oboolean: () => Dx,
    objectUtil: () => db,
    object: () => lx,
    number: () => Eh,
    nullable: () => xx,
    null: () => C4,
    never: () => T4,
    nativeEnum: () => wx,
    nan: () => A4,
    map: () => fx,
    makeIssue: () => Ri,
    literal: () => cx,
    lazy: () => hx,
    late: () => M4,
    isValid: () => ct,
    isDirty: () => Er,
    isAsync: () => It,
    isAborted: () => Yr,
    intersection: () => nx,
    instanceof: () => y4,
    getParsedType: () => Sl,
    getErrorMap: () => Pt,
    function: () => ex,
    enum: () => mx,
    effect: () => ux,
    discriminatedUnion: () => rx,
    defaultErrorMap: () => it,
    datetimeRegex: () => Xh,
    date: () => k4,
    custom: () => jh,
    coerce: () => $x,
    boolean: () => Qh,
    bigint: () => U4,
    array: () => d4,
    any: () => P4,
    addIssueToContext: () => x,
    ZodVoid: () => Ki,
    ZodUnknown: () => mt,
    ZodUnion: () => ii,
    ZodUndefined: () => li,
    ZodType: () => Q,
    ZodTuple: () => Cl,
    ZodTransformer: () => Rl,
    ZodSymbol: () => Vi,
    ZodString: () => Gl,
    ZodSet: () => Jt,
    ZodSchema: () => Q,
    ZodRecord: () => Hi,
    ZodReadonly: () => ei,
    ZodPromise: () => Xt,
    ZodPipeline: () => Ai,
    ZodParsedType: () => z,
    ZodOptional: () => Bl,
    ZodObject: () => il,
    ZodNumber: () => wt,
    ZodNullable: () => nt,
    ZodNull: () => ti,
    ZodNever: () => Nl,
    ZodNativeEnum: () => bi,
    ZodNaN: () => yi,
    ZodMap: () => Mi,
    ZodLiteral: () => oi,
    ZodLazy: () => ni,
    ZodIssueCode: () => p,
    ZodIntersection: () => ri,
    ZodFunction: () => Zt,
    ZodFirstPartyTypeKind: () => X,
    ZodError: () => $l,
    ZodEnum: () => ut,
    ZodEffects: () => Rl,
    ZodDiscriminatedUnion: () => Qr,
    ZodDefault: () => fi,
    ZodDate: () => qt,
    ZodCatch: () => gi,
    ZodBranded: () => Gr,
    ZodBoolean: () => dt,
    ZodBigInt: () => pt,
    ZodArray: () => Ll,
    ZodAny: () => st,
    Schema: () => Q,
    ParseStatus: () => ul,
    OK: () => al,
    NEVER: () => qx,
    INVALID: () => s,
    EMPTY_PATH: () => a4,
    DIRTY: () => $t,
    BRAND: () => H4,
});
var B;
(function (l) {
    l.assertEqual = (n) => {};
    function t(n) {}
    l.assertIs = t;
    function i(n) {
        throw new Error();
    }
    ((l.assertNever = i),
        (l.arrayToEnum = (n) => {
            let o = {};
            for (let b of n) o[b] = b;
            return o;
        }),
        (l.getValidEnumValues = (n) => {
            let o = l.objectKeys(n).filter((g) => typeof n[n[g]] !== 'number'),
                b = {};
            for (let g of o) b[g] = n[g];
            return l.objectValues(b);
        }),
        (l.objectValues = (n) => {
            return l.objectKeys(n).map(function (o) {
                return n[o];
            });
        }),
        (l.objectKeys =
            typeof Object.keys === 'function'
                ? (n) => Object.keys(n)
                : (n) => {
                      let o = [];
                      for (let b in n) if (Object.prototype.hasOwnProperty.call(n, b)) o.push(b);
                      return o;
                  }),
        (l.find = (n, o) => {
            for (let b of n) if (o(b)) return b;
            return;
        }),
        (l.isInteger =
            typeof Number.isInteger === 'function'
                ? (n) => Number.isInteger(n)
                : (n) => typeof n === 'number' && Number.isFinite(n) && Math.floor(n) === n));
    function r(n, o = ' | ') {
        return n.map((b) => (typeof b === 'string' ? `'${b}'` : b)).join(o);
    }
    ((l.joinValues = r),
        (l.jsonStringifyReplacer = (n, o) => {
            if (typeof o === 'bigint') return o.toString();
            return o;
        }));
})(B || (B = {}));
var db;
(function (l) {
    l.mergeShapes = (t, i) => {
        return { ...t, ...i };
    };
})(db || (db = {}));
var z = B.arrayToEnum([
        'string',
        'nan',
        'number',
        'integer',
        'float',
        'boolean',
        'date',
        'bigint',
        'symbol',
        'function',
        'undefined',
        'null',
        'array',
        'object',
        'unknown',
        'promise',
        'void',
        'never',
        'map',
        'set',
    ]),
    Sl = (l) => {
        switch (typeof l) {
            case 'undefined':
                return z.undefined;
            case 'string':
                return z.string;
            case 'number':
                return Number.isNaN(l) ? z.nan : z.number;
            case 'boolean':
                return z.boolean;
            case 'function':
                return z.function;
            case 'bigint':
                return z.bigint;
            case 'symbol':
                return z.symbol;
            case 'object':
                if (Array.isArray(l)) return z.array;
                if (l === null) return z.null;
                if (
                    l.then &&
                    typeof l.then === 'function' &&
                    l.catch &&
                    typeof l.catch === 'function'
                )
                    return z.promise;
                if (typeof Map !== 'undefined' && l instanceof Map) return z.map;
                if (typeof Set !== 'undefined' && l instanceof Set) return z.set;
                if (typeof Date !== 'undefined' && l instanceof Date) return z.date;
                return z.object;
            default:
                return z.unknown;
        }
    };
var p = B.arrayToEnum([
        'invalid_type',
        'invalid_literal',
        'custom',
        'invalid_union',
        'invalid_union_discriminator',
        'invalid_enum_value',
        'unrecognized_keys',
        'invalid_arguments',
        'invalid_return_type',
        'invalid_date',
        'invalid_string',
        'too_small',
        'too_big',
        'invalid_intersection_types',
        'not_multiple_of',
        'not_finite',
    ]),
    u4 = (l) => {
        return JSON.stringify(l, null, 2).replace(/"([^"]+)":/g, '$1:');
    };
class $l extends Error {
    get errors() {
        return this.issues;
    }
    constructor(l) {
        super();
        ((this.issues = []),
            (this.addIssue = (i) => {
                this.issues = [...this.issues, i];
            }),
            (this.addIssues = (i = []) => {
                this.issues = [...this.issues, ...i];
            }));
        let t = new.target.prototype;
        if (Object.setPrototypeOf) Object.setPrototypeOf(this, t);
        else this.__proto__ = t;
        ((this.name = 'ZodError'), (this.issues = l));
    }
    format(l) {
        let t =
                l ||
                function (n) {
                    return n.message;
                },
            i = { _errors: [] },
            r = (n) => {
                for (let o of n.issues)
                    if (o.code === 'invalid_union') o.unionErrors.map(r);
                    else if (o.code === 'invalid_return_type') r(o.returnTypeError);
                    else if (o.code === 'invalid_arguments') r(o.argumentsError);
                    else if (o.path.length === 0) i._errors.push(t(o));
                    else {
                        let b = i,
                            g = 0;
                        while (g < o.path.length) {
                            let e = o.path[g];
                            if (g !== o.path.length - 1) b[e] = b[e] || { _errors: [] };
                            else ((b[e] = b[e] || { _errors: [] }), b[e]._errors.push(t(o)));
                            ((b = b[e]), g++);
                        }
                    }
            };
        return (r(this), i);
    }
    static assert(l) {
        if (!(l instanceof $l)) throw new Error(`Not a ZodError: ${l}`);
    }
    toString() {
        return this.message;
    }
    get message() {
        return JSON.stringify(this.issues, B.jsonStringifyReplacer, 2);
    }
    get isEmpty() {
        return this.issues.length === 0;
    }
    flatten(l = (t) => t.message) {
        let t = {},
            i = [];
        for (let r of this.issues)
            if (r.path.length > 0) {
                let n = r.path[0];
                ((t[n] = t[n] || []), t[n].push(l(r)));
            } else i.push(l(r));
        return { formErrors: i, fieldErrors: t };
    }
    get formErrors() {
        return this.flatten();
    }
}
$l.create = (l) => {
    return new $l(l);
};
var z4 = (l, t) => {
        let i;
        switch (l.code) {
            case p.invalid_type:
                if (l.received === z.undefined) i = 'Required';
                else i = `Expected ${l.expected}, received ${l.received}`;
                break;
            case p.invalid_literal:
                i = `Invalid literal value, expected ${JSON.stringify(l.expected, B.jsonStringifyReplacer)}`;
                break;
            case p.unrecognized_keys:
                i = `Unrecognized key(s) in object: ${B.joinValues(l.keys, ', ')}`;
                break;
            case p.invalid_union:
                i = 'Invalid input';
                break;
            case p.invalid_union_discriminator:
                i = `Invalid discriminator value. Expected ${B.joinValues(l.options)}`;
                break;
            case p.invalid_enum_value:
                i = `Invalid enum value. Expected ${B.joinValues(l.options)}, received '${l.received}'`;
                break;
            case p.invalid_arguments:
                i = 'Invalid function arguments';
                break;
            case p.invalid_return_type:
                i = 'Invalid function return type';
                break;
            case p.invalid_date:
                i = 'Invalid date';
                break;
            case p.invalid_string:
                if (typeof l.validation === 'object')
                    if ('includes' in l.validation) {
                        if (
                            ((i = `Invalid input: must include "${l.validation.includes}"`),
                            typeof l.validation.position === 'number')
                        )
                            i = `${i} at one or more positions greater than or equal to ${l.validation.position}`;
                    } else if ('startsWith' in l.validation)
                        i = `Invalid input: must start with "${l.validation.startsWith}"`;
                    else if ('endsWith' in l.validation)
                        i = `Invalid input: must end with "${l.validation.endsWith}"`;
                    else B.assertNever(l.validation);
                else if (l.validation !== 'regex') i = `Invalid ${l.validation}`;
                else i = 'Invalid';
                break;
            case p.too_small:
                if (l.type === 'array')
                    i = `Array must contain ${l.exact ? 'exactly' : l.inclusive ? 'at least' : 'more than'} ${l.minimum} element(s)`;
                else if (l.type === 'string')
                    i = `String must contain ${l.exact ? 'exactly' : l.inclusive ? 'at least' : 'over'} ${l.minimum} character(s)`;
                else if (l.type === 'number')
                    i = `Number must be ${l.exact ? 'exactly equal to ' : l.inclusive ? 'greater than or equal to ' : 'greater than '}${l.minimum}`;
                else if (l.type === 'bigint')
                    i = `Number must be ${l.exact ? 'exactly equal to ' : l.inclusive ? 'greater than or equal to ' : 'greater than '}${l.minimum}`;
                else if (l.type === 'date')
                    i = `Date must be ${l.exact ? 'exactly equal to ' : l.inclusive ? 'greater than or equal to ' : 'greater than '}${new Date(Number(l.minimum))}`;
                else i = 'Invalid input';
                break;
            case p.too_big:
                if (l.type === 'array')
                    i = `Array must contain ${l.exact ? 'exactly' : l.inclusive ? 'at most' : 'less than'} ${l.maximum} element(s)`;
                else if (l.type === 'string')
                    i = `String must contain ${l.exact ? 'exactly' : l.inclusive ? 'at most' : 'under'} ${l.maximum} character(s)`;
                else if (l.type === 'number')
                    i = `Number must be ${l.exact ? 'exactly' : l.inclusive ? 'less than or equal to' : 'less than'} ${l.maximum}`;
                else if (l.type === 'bigint')
                    i = `BigInt must be ${l.exact ? 'exactly' : l.inclusive ? 'less than or equal to' : 'less than'} ${l.maximum}`;
                else if (l.type === 'date')
                    i = `Date must be ${l.exact ? 'exactly' : l.inclusive ? 'smaller than or equal to' : 'smaller than'} ${new Date(Number(l.maximum))}`;
                else i = 'Invalid input';
                break;
            case p.custom:
                i = 'Invalid input';
                break;
            case p.invalid_intersection_types:
                i = 'Intersection results could not be merged';
                break;
            case p.not_multiple_of:
                i = `Number must be a multiple of ${l.multipleOf}`;
                break;
            case p.not_finite:
                i = 'Number must be finite';
                break;
            default:
                ((i = t.defaultError), B.assertNever(l));
        }
        return { message: i };
    },
    it = z4;
var Dh = it;
function x4(l) {
    Dh = l;
}
function Pt() {
    return Dh;
}
var Ri = (l) => {
        let { data: t, path: i, errorMaps: r, issueData: n } = l,
            o = [...i, ...(n.path || [])],
            b = { ...n, path: o };
        if (n.message !== void 0) return { ...n, path: o, message: n.message };
        let g = '',
            e = r
                .filter((f) => !!f)
                .slice()
                .reverse();
        for (let f of e) g = f(b, { data: t, defaultError: g }).message;
        return { ...n, path: o, message: g };
    },
    a4 = [];
function x(l, t) {
    let i = Pt(),
        r = Ri({
            issueData: t,
            data: l.data,
            path: l.path,
            errorMaps: [
                l.common.contextualErrorMap,
                l.schemaErrorMap,
                i,
                i === it ? void 0 : it,
            ].filter((n) => !!n),
        });
    l.common.issues.push(r);
}
class ul {
    constructor() {
        this.value = 'valid';
    }
    dirty() {
        if (this.value === 'valid') this.value = 'dirty';
    }
    abort() {
        if (this.value !== 'aborted') this.value = 'aborted';
    }
    static mergeArray(l, t) {
        let i = [];
        for (let r of t) {
            if (r.status === 'aborted') return s;
            if (r.status === 'dirty') l.dirty();
            i.push(r.value);
        }
        return { status: l.value, value: i };
    }
    static async mergeObjectAsync(l, t) {
        let i = [];
        for (let r of t) {
            let n = await r.key,
                o = await r.value;
            i.push({ key: n, value: o });
        }
        return ul.mergeObjectSync(l, i);
    }
    static mergeObjectSync(l, t) {
        let i = {};
        for (let r of t) {
            let { key: n, value: o } = r;
            if (n.status === 'aborted') return s;
            if (o.status === 'aborted') return s;
            if (n.status === 'dirty') l.dirty();
            if (o.status === 'dirty') l.dirty();
            if (n.value !== '__proto__' && (typeof o.value !== 'undefined' || r.alwaysSet))
                i[n.value] = o.value;
        }
        return { status: l.value, value: i };
    }
}
var s = Object.freeze({ status: 'aborted' }),
    $t = (l) => ({ status: 'dirty', value: l }),
    al = (l) => ({ status: 'valid', value: l }),
    Yr = (l) => l.status === 'aborted',
    Er = (l) => l.status === 'dirty',
    ct = (l) => l.status === 'valid',
    It = (l) => typeof Promise !== 'undefined' && l instanceof Promise;
var O;
(function (l) {
    ((l.errToObj = (t) => (typeof t === 'string' ? { message: t } : t || {})),
        (l.toString = (t) => (typeof t === 'string' ? t : t?.message)));
})(O || (O = {}));
class Fl {
    constructor(l, t, i, r) {
        ((this._cachedPath = []),
            (this.parent = l),
            (this.data = t),
            (this._path = i),
            (this._key = r));
    }
    get path() {
        if (!this._cachedPath.length)
            if (Array.isArray(this._key)) this._cachedPath.push(...this._path, ...this._key);
            else this._cachedPath.push(...this._path, this._key);
        return this._cachedPath;
    }
}
var $h = (l, t) => {
    if (ct(t)) return { success: !0, data: t.value };
    else {
        if (!l.common.issues.length) throw new Error('Validation failed but no issues detected.');
        return {
            success: !1,
            get error() {
                if (this._error) return this._error;
                let i = new $l(l.common.issues);
                return ((this._error = i), this._error);
            },
        };
    }
};
function E(l) {
    if (!l) return {};
    let { errorMap: t, invalid_type_error: i, required_error: r, description: n } = l;
    if (t && (i || r))
        throw new Error(
            `Can't use "invalid_type_error" or "required_error" in conjunction with custom error map.`,
        );
    if (t) return { errorMap: t, description: n };
    return {
        errorMap: (b, g) => {
            let { message: e } = l;
            if (b.code === 'invalid_enum_value') return { message: e ?? g.defaultError };
            if (typeof g.data === 'undefined') return { message: e ?? r ?? g.defaultError };
            if (b.code !== 'invalid_type') return { message: g.defaultError };
            return { message: e ?? i ?? g.defaultError };
        },
        description: n,
    };
}
class Q {
    get description() {
        return this._def.description;
    }
    _getType(l) {
        return Sl(l.data);
    }
    _getOrReturnCtx(l, t) {
        return (
            t || {
                common: l.parent.common,
                data: l.data,
                parsedType: Sl(l.data),
                schemaErrorMap: this._def.errorMap,
                path: l.path,
                parent: l.parent,
            }
        );
    }
    _processInputParams(l) {
        return {
            status: new ul(),
            ctx: {
                common: l.parent.common,
                data: l.data,
                parsedType: Sl(l.data),
                schemaErrorMap: this._def.errorMap,
                path: l.path,
                parent: l.parent,
            },
        };
    }
    _parseSync(l) {
        let t = this._parse(l);
        if (It(t)) throw new Error('Synchronous parse encountered promise.');
        return t;
    }
    _parseAsync(l) {
        let t = this._parse(l);
        return Promise.resolve(t);
    }
    parse(l, t) {
        let i = this.safeParse(l, t);
        if (i.success) return i.data;
        throw i.error;
    }
    safeParse(l, t) {
        let i = {
                common: { issues: [], async: t?.async ?? !1, contextualErrorMap: t?.errorMap },
                path: t?.path || [],
                schemaErrorMap: this._def.errorMap,
                parent: null,
                data: l,
                parsedType: Sl(l),
            },
            r = this._parseSync({ data: l, path: i.path, parent: i });
        return $h(i, r);
    }
    '~validate'(l) {
        let t = {
            common: { issues: [], async: !!this['~standard'].async },
            path: [],
            schemaErrorMap: this._def.errorMap,
            parent: null,
            data: l,
            parsedType: Sl(l),
        };
        if (!this['~standard'].async)
            try {
                let i = this._parseSync({ data: l, path: [], parent: t });
                return ct(i) ? { value: i.value } : { issues: t.common.issues };
            } catch (i) {
                if (i?.message?.toLowerCase()?.includes('encountered'))
                    this['~standard'].async = !0;
                t.common = { issues: [], async: !0 };
            }
        return this._parseAsync({ data: l, path: [], parent: t }).then((i) =>
            ct(i) ? { value: i.value } : { issues: t.common.issues },
        );
    }
    async parseAsync(l, t) {
        let i = await this.safeParseAsync(l, t);
        if (i.success) return i.data;
        throw i.error;
    }
    async safeParseAsync(l, t) {
        let i = {
                common: { issues: [], contextualErrorMap: t?.errorMap, async: !0 },
                path: t?.path || [],
                schemaErrorMap: this._def.errorMap,
                parent: null,
                data: l,
                parsedType: Sl(l),
            },
            r = this._parse({ data: l, path: i.path, parent: i }),
            n = await (It(r) ? r : Promise.resolve(r));
        return $h(i, n);
    }
    refine(l, t) {
        let i = (r) => {
            if (typeof t === 'string' || typeof t === 'undefined') return { message: t };
            else if (typeof t === 'function') return t(r);
            else return t;
        };
        return this._refinement((r, n) => {
            let o = l(r),
                b = () => n.addIssue({ code: p.custom, ...i(r) });
            if (typeof Promise !== 'undefined' && o instanceof Promise)
                return o.then((g) => {
                    if (!g) return (b(), !1);
                    else return !0;
                });
            if (!o) return (b(), !1);
            else return !0;
        });
    }
    refinement(l, t) {
        return this._refinement((i, r) => {
            if (!l(i)) return (r.addIssue(typeof t === 'function' ? t(i, r) : t), !1);
            else return !0;
        });
    }
    _refinement(l) {
        return new Rl({
            schema: this,
            typeName: X.ZodEffects,
            effect: { type: 'refinement', refinement: l },
        });
    }
    superRefine(l) {
        return this._refinement(l);
    }
    constructor(l) {
        ((this.spa = this.safeParseAsync),
            (this._def = l),
            (this.parse = this.parse.bind(this)),
            (this.safeParse = this.safeParse.bind(this)),
            (this.parseAsync = this.parseAsync.bind(this)),
            (this.safeParseAsync = this.safeParseAsync.bind(this)),
            (this.spa = this.spa.bind(this)),
            (this.refine = this.refine.bind(this)),
            (this.refinement = this.refinement.bind(this)),
            (this.superRefine = this.superRefine.bind(this)),
            (this.optional = this.optional.bind(this)),
            (this.nullable = this.nullable.bind(this)),
            (this.nullish = this.nullish.bind(this)),
            (this.array = this.array.bind(this)),
            (this.promise = this.promise.bind(this)),
            (this.or = this.or.bind(this)),
            (this.and = this.and.bind(this)),
            (this.transform = this.transform.bind(this)),
            (this.brand = this.brand.bind(this)),
            (this.default = this.default.bind(this)),
            (this.catch = this.catch.bind(this)),
            (this.describe = this.describe.bind(this)),
            (this.pipe = this.pipe.bind(this)),
            (this.readonly = this.readonly.bind(this)),
            (this.isNullable = this.isNullable.bind(this)),
            (this.isOptional = this.isOptional.bind(this)),
            (this['~standard'] = {
                version: 1,
                vendor: 'zod',
                validate: (t) => this['~validate'](t),
            }));
    }
    optional() {
        return Bl.create(this, this._def);
    }
    nullable() {
        return nt.create(this, this._def);
    }
    nullish() {
        return this.nullable().optional();
    }
    array() {
        return Ll.create(this);
    }
    promise() {
        return Xt.create(this, this._def);
    }
    or(l) {
        return ii.create([this, l], this._def);
    }
    and(l) {
        return ri.create(this, l, this._def);
    }
    transform(l) {
        return new Rl({
            ...E(this._def),
            schema: this,
            typeName: X.ZodEffects,
            effect: { type: 'transform', transform: l },
        });
    }
    default(l) {
        let t = typeof l === 'function' ? l : () => l;
        return new fi({
            ...E(this._def),
            innerType: this,
            defaultValue: t,
            typeName: X.ZodDefault,
        });
    }
    brand() {
        return new Gr({ typeName: X.ZodBranded, type: this, ...E(this._def) });
    }
    catch(l) {
        let t = typeof l === 'function' ? l : () => l;
        return new gi({ ...E(this._def), innerType: this, catchValue: t, typeName: X.ZodCatch });
    }
    describe(l) {
        return new this.constructor({ ...this._def, description: l });
    }
    pipe(l) {
        return Ai.create(this, l);
    }
    readonly() {
        return ei.create(this);
    }
    isOptional() {
        return this.safeParse(void 0).success;
    }
    isNullable() {
        return this.safeParse(null).success;
    }
}
var _4 = /^c[^\s-]{8,}$/i,
    v4 = /^[0-9a-z]+$/,
    O4 = /^[0-9A-HJKMNP-TV-Z]{26}$/i,
    D4 = /^[0-9a-fA-F]{8}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{12}$/i,
    $4 = /^[a-z0-9_-]{21}$/i,
    q4 = /^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]*$/,
    s4 =
        /^[-+]?P(?!$)(?:(?:[-+]?\d+Y)|(?:[-+]?\d+[.,]\d+Y$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:(?:[-+]?\d+W)|(?:[-+]?\d+[.,]\d+W$))?(?:(?:[-+]?\d+D)|(?:[-+]?\d+[.,]\d+D$))?(?:T(?=[\d+-])(?:(?:[-+]?\d+H)|(?:[-+]?\d+[.,]\d+H$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:[-+]?\d+(?:[.,]\d+)?S)?)??$/,
    J4 = /^(?!\.)(?!.*\.\.)([A-Z0-9_'+\-\.]*)[A-Z0-9_+-]@([A-Z0-9][A-Z0-9\-]*\.)+[A-Z]{2,}$/i,
    X4 = '^(\\p{Extended_Pictographic}|\\p{Emoji_Component})+$',
    lf,
    W4 =
        /^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])$/,
    j4 =
        /^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\/(3[0-2]|[12]?[0-9])$/,
    Y4 =
        /^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))$/,
    E4 =
        /^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))\/(12[0-8]|1[01][0-9]|[1-9]?[0-9])$/,
    Q4 = /^([0-9a-zA-Z+/]{4})*(([0-9a-zA-Z+/]{2}==)|([0-9a-zA-Z+/]{3}=))?$/,
    G4 = /^([0-9a-zA-Z-_]{4})*(([0-9a-zA-Z-_]{2}(==)?)|([0-9a-zA-Z-_]{3}(=)?))?$/,
    sh =
        '((\\d\\d[2468][048]|\\d\\d[13579][26]|\\d\\d0[48]|[02468][048]00|[13579][26]00)-02-29|\\d{4}-((0[13578]|1[02])-(0[1-9]|[12]\\d|3[01])|(0[469]|11)-(0[1-9]|[12]\\d|30)|(02)-(0[1-9]|1\\d|2[0-8])))',
    L4 = new RegExp(`^${sh}$`);
function Jh(l) {
    let t = '[0-5]\\d';
    if (l.precision) t = `${t}\\.\\d{${l.precision}}`;
    else if (l.precision == null) t = `${t}(\\.\\d+)?`;
    let i = l.precision ? '+' : '?';
    return `([01]\\d|2[0-3]):[0-5]\\d(:${t})${i}`;
}
function B4(l) {
    return new RegExp(`^${Jh(l)}$`);
}
function Xh(l) {
    let t = `${sh}T${Jh(l)}`,
        i = [];
    if ((i.push(l.local ? 'Z?' : 'Z'), l.offset)) i.push('([+-]\\d{2}:?\\d{2})');
    return ((t = `${t}(${i.join('|')})`), new RegExp(`^${t}$`));
}
function F4(l, t) {
    if ((t === 'v4' || !t) && W4.test(l)) return !0;
    if ((t === 'v6' || !t) && Y4.test(l)) return !0;
    return !1;
}
function R4(l, t) {
    if (!q4.test(l)) return !1;
    try {
        let [i] = l.split('.');
        if (!i) return !1;
        let r = i
                .replace(/-/g, '+')
                .replace(/_/g, '/')
                .padEnd(i.length + ((4 - (i.length % 4)) % 4), '='),
            n = JSON.parse(atob(r));
        if (typeof n !== 'object' || n === null) return !1;
        if ('typ' in n && n?.typ !== 'JWT') return !1;
        if (!n.alg) return !1;
        if (t && n.alg !== t) return !1;
        return !0;
    } catch {
        return !1;
    }
}
function V4(l, t) {
    if ((t === 'v4' || !t) && j4.test(l)) return !0;
    if ((t === 'v6' || !t) && E4.test(l)) return !0;
    return !1;
}
class Gl extends Q {
    _parse(l) {
        if (this._def.coerce) l.data = String(l.data);
        if (this._getType(l) !== z.string) {
            let n = this._getOrReturnCtx(l);
            return (x(n, { code: p.invalid_type, expected: z.string, received: n.parsedType }), s);
        }
        let i = new ul(),
            r = void 0;
        for (let n of this._def.checks)
            if (n.kind === 'min') {
                if (l.data.length < n.value)
                    ((r = this._getOrReturnCtx(l, r)),
                        x(r, {
                            code: p.too_small,
                            minimum: n.value,
                            type: 'string',
                            inclusive: !0,
                            exact: !1,
                            message: n.message,
                        }),
                        i.dirty());
            } else if (n.kind === 'max') {
                if (l.data.length > n.value)
                    ((r = this._getOrReturnCtx(l, r)),
                        x(r, {
                            code: p.too_big,
                            maximum: n.value,
                            type: 'string',
                            inclusive: !0,
                            exact: !1,
                            message: n.message,
                        }),
                        i.dirty());
            } else if (n.kind === 'length') {
                let o = l.data.length > n.value,
                    b = l.data.length < n.value;
                if (o || b) {
                    if (((r = this._getOrReturnCtx(l, r)), o))
                        x(r, {
                            code: p.too_big,
                            maximum: n.value,
                            type: 'string',
                            inclusive: !0,
                            exact: !0,
                            message: n.message,
                        });
                    else if (b)
                        x(r, {
                            code: p.too_small,
                            minimum: n.value,
                            type: 'string',
                            inclusive: !0,
                            exact: !0,
                            message: n.message,
                        });
                    i.dirty();
                }
            } else if (n.kind === 'email') {
                if (!J4.test(l.data))
                    ((r = this._getOrReturnCtx(l, r)),
                        x(r, { validation: 'email', code: p.invalid_string, message: n.message }),
                        i.dirty());
            } else if (n.kind === 'emoji') {
                if (!lf) lf = new RegExp(X4, 'u');
                if (!lf.test(l.data))
                    ((r = this._getOrReturnCtx(l, r)),
                        x(r, { validation: 'emoji', code: p.invalid_string, message: n.message }),
                        i.dirty());
            } else if (n.kind === 'uuid') {
                if (!D4.test(l.data))
                    ((r = this._getOrReturnCtx(l, r)),
                        x(r, { validation: 'uuid', code: p.invalid_string, message: n.message }),
                        i.dirty());
            } else if (n.kind === 'nanoid') {
                if (!$4.test(l.data))
                    ((r = this._getOrReturnCtx(l, r)),
                        x(r, { validation: 'nanoid', code: p.invalid_string, message: n.message }),
                        i.dirty());
            } else if (n.kind === 'cuid') {
                if (!_4.test(l.data))
                    ((r = this._getOrReturnCtx(l, r)),
                        x(r, { validation: 'cuid', code: p.invalid_string, message: n.message }),
                        i.dirty());
            } else if (n.kind === 'cuid2') {
                if (!v4.test(l.data))
                    ((r = this._getOrReturnCtx(l, r)),
                        x(r, { validation: 'cuid2', code: p.invalid_string, message: n.message }),
                        i.dirty());
            } else if (n.kind === 'ulid') {
                if (!O4.test(l.data))
                    ((r = this._getOrReturnCtx(l, r)),
                        x(r, { validation: 'ulid', code: p.invalid_string, message: n.message }),
                        i.dirty());
            } else if (n.kind === 'url')
                try {
                    new URL(l.data);
                } catch {
                    ((r = this._getOrReturnCtx(l, r)),
                        x(r, { validation: 'url', code: p.invalid_string, message: n.message }),
                        i.dirty());
                }
            else if (n.kind === 'regex') {
                if (((n.regex.lastIndex = 0), !n.regex.test(l.data)))
                    ((r = this._getOrReturnCtx(l, r)),
                        x(r, { validation: 'regex', code: p.invalid_string, message: n.message }),
                        i.dirty());
            } else if (n.kind === 'trim') l.data = l.data.trim();
            else if (n.kind === 'includes') {
                if (!l.data.includes(n.value, n.position))
                    ((r = this._getOrReturnCtx(l, r)),
                        x(r, {
                            code: p.invalid_string,
                            validation: { includes: n.value, position: n.position },
                            message: n.message,
                        }),
                        i.dirty());
            } else if (n.kind === 'toLowerCase') l.data = l.data.toLowerCase();
            else if (n.kind === 'toUpperCase') l.data = l.data.toUpperCase();
            else if (n.kind === 'startsWith') {
                if (!l.data.startsWith(n.value))
                    ((r = this._getOrReturnCtx(l, r)),
                        x(r, {
                            code: p.invalid_string,
                            validation: { startsWith: n.value },
                            message: n.message,
                        }),
                        i.dirty());
            } else if (n.kind === 'endsWith') {
                if (!l.data.endsWith(n.value))
                    ((r = this._getOrReturnCtx(l, r)),
                        x(r, {
                            code: p.invalid_string,
                            validation: { endsWith: n.value },
                            message: n.message,
                        }),
                        i.dirty());
            } else if (n.kind === 'datetime') {
                if (!Xh(n).test(l.data))
                    ((r = this._getOrReturnCtx(l, r)),
                        x(r, {
                            code: p.invalid_string,
                            validation: 'datetime',
                            message: n.message,
                        }),
                        i.dirty());
            } else if (n.kind === 'date') {
                if (!L4.test(l.data))
                    ((r = this._getOrReturnCtx(l, r)),
                        x(r, { code: p.invalid_string, validation: 'date', message: n.message }),
                        i.dirty());
            } else if (n.kind === 'time') {
                if (!B4(n).test(l.data))
                    ((r = this._getOrReturnCtx(l, r)),
                        x(r, { code: p.invalid_string, validation: 'time', message: n.message }),
                        i.dirty());
            } else if (n.kind === 'duration') {
                if (!s4.test(l.data))
                    ((r = this._getOrReturnCtx(l, r)),
                        x(r, {
                            validation: 'duration',
                            code: p.invalid_string,
                            message: n.message,
                        }),
                        i.dirty());
            } else if (n.kind === 'ip') {
                if (!F4(l.data, n.version))
                    ((r = this._getOrReturnCtx(l, r)),
                        x(r, { validation: 'ip', code: p.invalid_string, message: n.message }),
                        i.dirty());
            } else if (n.kind === 'jwt') {
                if (!R4(l.data, n.alg))
                    ((r = this._getOrReturnCtx(l, r)),
                        x(r, { validation: 'jwt', code: p.invalid_string, message: n.message }),
                        i.dirty());
            } else if (n.kind === 'cidr') {
                if (!V4(l.data, n.version))
                    ((r = this._getOrReturnCtx(l, r)),
                        x(r, { validation: 'cidr', code: p.invalid_string, message: n.message }),
                        i.dirty());
            } else if (n.kind === 'base64') {
                if (!Q4.test(l.data))
                    ((r = this._getOrReturnCtx(l, r)),
                        x(r, { validation: 'base64', code: p.invalid_string, message: n.message }),
                        i.dirty());
            } else if (n.kind === 'base64url') {
                if (!G4.test(l.data))
                    ((r = this._getOrReturnCtx(l, r)),
                        x(r, {
                            validation: 'base64url',
                            code: p.invalid_string,
                            message: n.message,
                        }),
                        i.dirty());
            } else B.assertNever(n);
        return { status: i.value, value: l.data };
    }
    _regex(l, t, i) {
        return this.refinement((r) => l.test(r), {
            validation: t,
            code: p.invalid_string,
            ...O.errToObj(i),
        });
    }
    _addCheck(l) {
        return new Gl({ ...this._def, checks: [...this._def.checks, l] });
    }
    email(l) {
        return this._addCheck({ kind: 'email', ...O.errToObj(l) });
    }
    url(l) {
        return this._addCheck({ kind: 'url', ...O.errToObj(l) });
    }
    emoji(l) {
        return this._addCheck({ kind: 'emoji', ...O.errToObj(l) });
    }
    uuid(l) {
        return this._addCheck({ kind: 'uuid', ...O.errToObj(l) });
    }
    nanoid(l) {
        return this._addCheck({ kind: 'nanoid', ...O.errToObj(l) });
    }
    cuid(l) {
        return this._addCheck({ kind: 'cuid', ...O.errToObj(l) });
    }
    cuid2(l) {
        return this._addCheck({ kind: 'cuid2', ...O.errToObj(l) });
    }
    ulid(l) {
        return this._addCheck({ kind: 'ulid', ...O.errToObj(l) });
    }
    base64(l) {
        return this._addCheck({ kind: 'base64', ...O.errToObj(l) });
    }
    base64url(l) {
        return this._addCheck({ kind: 'base64url', ...O.errToObj(l) });
    }
    jwt(l) {
        return this._addCheck({ kind: 'jwt', ...O.errToObj(l) });
    }
    ip(l) {
        return this._addCheck({ kind: 'ip', ...O.errToObj(l) });
    }
    cidr(l) {
        return this._addCheck({ kind: 'cidr', ...O.errToObj(l) });
    }
    datetime(l) {
        if (typeof l === 'string')
            return this._addCheck({
                kind: 'datetime',
                precision: null,
                offset: !1,
                local: !1,
                message: l,
            });
        return this._addCheck({
            kind: 'datetime',
            precision: typeof l?.precision === 'undefined' ? null : l?.precision,
            offset: l?.offset ?? !1,
            local: l?.local ?? !1,
            ...O.errToObj(l?.message),
        });
    }
    date(l) {
        return this._addCheck({ kind: 'date', message: l });
    }
    time(l) {
        if (typeof l === 'string')
            return this._addCheck({ kind: 'time', precision: null, message: l });
        return this._addCheck({
            kind: 'time',
            precision: typeof l?.precision === 'undefined' ? null : l?.precision,
            ...O.errToObj(l?.message),
        });
    }
    duration(l) {
        return this._addCheck({ kind: 'duration', ...O.errToObj(l) });
    }
    regex(l, t) {
        return this._addCheck({ kind: 'regex', regex: l, ...O.errToObj(t) });
    }
    includes(l, t) {
        return this._addCheck({
            kind: 'includes',
            value: l,
            position: t?.position,
            ...O.errToObj(t?.message),
        });
    }
    startsWith(l, t) {
        return this._addCheck({ kind: 'startsWith', value: l, ...O.errToObj(t) });
    }
    endsWith(l, t) {
        return this._addCheck({ kind: 'endsWith', value: l, ...O.errToObj(t) });
    }
    min(l, t) {
        return this._addCheck({ kind: 'min', value: l, ...O.errToObj(t) });
    }
    max(l, t) {
        return this._addCheck({ kind: 'max', value: l, ...O.errToObj(t) });
    }
    length(l, t) {
        return this._addCheck({ kind: 'length', value: l, ...O.errToObj(t) });
    }
    nonempty(l) {
        return this.min(1, O.errToObj(l));
    }
    trim() {
        return new Gl({ ...this._def, checks: [...this._def.checks, { kind: 'trim' }] });
    }
    toLowerCase() {
        return new Gl({ ...this._def, checks: [...this._def.checks, { kind: 'toLowerCase' }] });
    }
    toUpperCase() {
        return new Gl({ ...this._def, checks: [...this._def.checks, { kind: 'toUpperCase' }] });
    }
    get isDatetime() {
        return !!this._def.checks.find((l) => l.kind === 'datetime');
    }
    get isDate() {
        return !!this._def.checks.find((l) => l.kind === 'date');
    }
    get isTime() {
        return !!this._def.checks.find((l) => l.kind === 'time');
    }
    get isDuration() {
        return !!this._def.checks.find((l) => l.kind === 'duration');
    }
    get isEmail() {
        return !!this._def.checks.find((l) => l.kind === 'email');
    }
    get isURL() {
        return !!this._def.checks.find((l) => l.kind === 'url');
    }
    get isEmoji() {
        return !!this._def.checks.find((l) => l.kind === 'emoji');
    }
    get isUUID() {
        return !!this._def.checks.find((l) => l.kind === 'uuid');
    }
    get isNANOID() {
        return !!this._def.checks.find((l) => l.kind === 'nanoid');
    }
    get isCUID() {
        return !!this._def.checks.find((l) => l.kind === 'cuid');
    }
    get isCUID2() {
        return !!this._def.checks.find((l) => l.kind === 'cuid2');
    }
    get isULID() {
        return !!this._def.checks.find((l) => l.kind === 'ulid');
    }
    get isIP() {
        return !!this._def.checks.find((l) => l.kind === 'ip');
    }
    get isCIDR() {
        return !!this._def.checks.find((l) => l.kind === 'cidr');
    }
    get isBase64() {
        return !!this._def.checks.find((l) => l.kind === 'base64');
    }
    get isBase64url() {
        return !!this._def.checks.find((l) => l.kind === 'base64url');
    }
    get minLength() {
        let l = null;
        for (let t of this._def.checks)
            if (t.kind === 'min') {
                if (l === null || t.value > l) l = t.value;
            }
        return l;
    }
    get maxLength() {
        let l = null;
        for (let t of this._def.checks)
            if (t.kind === 'max') {
                if (l === null || t.value < l) l = t.value;
            }
        return l;
    }
}
Gl.create = (l) => {
    return new Gl({ checks: [], typeName: X.ZodString, coerce: l?.coerce ?? !1, ...E(l) });
};
function K4(l, t) {
    let i = (l.toString().split('.')[1] || '').length,
        r = (t.toString().split('.')[1] || '').length,
        n = i > r ? i : r,
        o = Number.parseInt(l.toFixed(n).replace('.', '')),
        b = Number.parseInt(t.toFixed(n).replace('.', ''));
    return (o % b) / 10 ** n;
}
class wt extends Q {
    constructor() {
        super(...arguments);
        ((this.min = this.gte), (this.max = this.lte), (this.step = this.multipleOf));
    }
    _parse(l) {
        if (this._def.coerce) l.data = Number(l.data);
        if (this._getType(l) !== z.number) {
            let n = this._getOrReturnCtx(l);
            return (x(n, { code: p.invalid_type, expected: z.number, received: n.parsedType }), s);
        }
        let i = void 0,
            r = new ul();
        for (let n of this._def.checks)
            if (n.kind === 'int') {
                if (!B.isInteger(l.data))
                    ((i = this._getOrReturnCtx(l, i)),
                        x(i, {
                            code: p.invalid_type,
                            expected: 'integer',
                            received: 'float',
                            message: n.message,
                        }),
                        r.dirty());
            } else if (n.kind === 'min') {
                if (n.inclusive ? l.data < n.value : l.data <= n.value)
                    ((i = this._getOrReturnCtx(l, i)),
                        x(i, {
                            code: p.too_small,
                            minimum: n.value,
                            type: 'number',
                            inclusive: n.inclusive,
                            exact: !1,
                            message: n.message,
                        }),
                        r.dirty());
            } else if (n.kind === 'max') {
                if (n.inclusive ? l.data > n.value : l.data >= n.value)
                    ((i = this._getOrReturnCtx(l, i)),
                        x(i, {
                            code: p.too_big,
                            maximum: n.value,
                            type: 'number',
                            inclusive: n.inclusive,
                            exact: !1,
                            message: n.message,
                        }),
                        r.dirty());
            } else if (n.kind === 'multipleOf') {
                if (K4(l.data, n.value) !== 0)
                    ((i = this._getOrReturnCtx(l, i)),
                        x(i, { code: p.not_multiple_of, multipleOf: n.value, message: n.message }),
                        r.dirty());
            } else if (n.kind === 'finite') {
                if (!Number.isFinite(l.data))
                    ((i = this._getOrReturnCtx(l, i)),
                        x(i, { code: p.not_finite, message: n.message }),
                        r.dirty());
            } else B.assertNever(n);
        return { status: r.value, value: l.data };
    }
    gte(l, t) {
        return this.setLimit('min', l, !0, O.toString(t));
    }
    gt(l, t) {
        return this.setLimit('min', l, !1, O.toString(t));
    }
    lte(l, t) {
        return this.setLimit('max', l, !0, O.toString(t));
    }
    lt(l, t) {
        return this.setLimit('max', l, !1, O.toString(t));
    }
    setLimit(l, t, i, r) {
        return new wt({
            ...this._def,
            checks: [
                ...this._def.checks,
                { kind: l, value: t, inclusive: i, message: O.toString(r) },
            ],
        });
    }
    _addCheck(l) {
        return new wt({ ...this._def, checks: [...this._def.checks, l] });
    }
    int(l) {
        return this._addCheck({ kind: 'int', message: O.toString(l) });
    }
    positive(l) {
        return this._addCheck({ kind: 'min', value: 0, inclusive: !1, message: O.toString(l) });
    }
    negative(l) {
        return this._addCheck({ kind: 'max', value: 0, inclusive: !1, message: O.toString(l) });
    }
    nonpositive(l) {
        return this._addCheck({ kind: 'max', value: 0, inclusive: !0, message: O.toString(l) });
    }
    nonnegative(l) {
        return this._addCheck({ kind: 'min', value: 0, inclusive: !0, message: O.toString(l) });
    }
    multipleOf(l, t) {
        return this._addCheck({ kind: 'multipleOf', value: l, message: O.toString(t) });
    }
    finite(l) {
        return this._addCheck({ kind: 'finite', message: O.toString(l) });
    }
    safe(l) {
        return this._addCheck({
            kind: 'min',
            inclusive: !0,
            value: Number.MIN_SAFE_INTEGER,
            message: O.toString(l),
        })._addCheck({
            kind: 'max',
            inclusive: !0,
            value: Number.MAX_SAFE_INTEGER,
            message: O.toString(l),
        });
    }
    get minValue() {
        let l = null;
        for (let t of this._def.checks)
            if (t.kind === 'min') {
                if (l === null || t.value > l) l = t.value;
            }
        return l;
    }
    get maxValue() {
        let l = null;
        for (let t of this._def.checks)
            if (t.kind === 'max') {
                if (l === null || t.value < l) l = t.value;
            }
        return l;
    }
    get isInt() {
        return !!this._def.checks.find(
            (l) => l.kind === 'int' || (l.kind === 'multipleOf' && B.isInteger(l.value)),
        );
    }
    get isFinite() {
        let l = null,
            t = null;
        for (let i of this._def.checks)
            if (i.kind === 'finite' || i.kind === 'int' || i.kind === 'multipleOf') return !0;
            else if (i.kind === 'min') {
                if (t === null || i.value > t) t = i.value;
            } else if (i.kind === 'max') {
                if (l === null || i.value < l) l = i.value;
            }
        return Number.isFinite(t) && Number.isFinite(l);
    }
}
wt.create = (l) => {
    return new wt({ checks: [], typeName: X.ZodNumber, coerce: l?.coerce || !1, ...E(l) });
};
class pt extends Q {
    constructor() {
        super(...arguments);
        ((this.min = this.gte), (this.max = this.lte));
    }
    _parse(l) {
        if (this._def.coerce)
            try {
                l.data = BigInt(l.data);
            } catch {
                return this._getInvalidInput(l);
            }
        if (this._getType(l) !== z.bigint) return this._getInvalidInput(l);
        let i = void 0,
            r = new ul();
        for (let n of this._def.checks)
            if (n.kind === 'min') {
                if (n.inclusive ? l.data < n.value : l.data <= n.value)
                    ((i = this._getOrReturnCtx(l, i)),
                        x(i, {
                            code: p.too_small,
                            type: 'bigint',
                            minimum: n.value,
                            inclusive: n.inclusive,
                            message: n.message,
                        }),
                        r.dirty());
            } else if (n.kind === 'max') {
                if (n.inclusive ? l.data > n.value : l.data >= n.value)
                    ((i = this._getOrReturnCtx(l, i)),
                        x(i, {
                            code: p.too_big,
                            type: 'bigint',
                            maximum: n.value,
                            inclusive: n.inclusive,
                            message: n.message,
                        }),
                        r.dirty());
            } else if (n.kind === 'multipleOf') {
                if (l.data % n.value !== BigInt(0))
                    ((i = this._getOrReturnCtx(l, i)),
                        x(i, { code: p.not_multiple_of, multipleOf: n.value, message: n.message }),
                        r.dirty());
            } else B.assertNever(n);
        return { status: r.value, value: l.data };
    }
    _getInvalidInput(l) {
        let t = this._getOrReturnCtx(l);
        return (x(t, { code: p.invalid_type, expected: z.bigint, received: t.parsedType }), s);
    }
    gte(l, t) {
        return this.setLimit('min', l, !0, O.toString(t));
    }
    gt(l, t) {
        return this.setLimit('min', l, !1, O.toString(t));
    }
    lte(l, t) {
        return this.setLimit('max', l, !0, O.toString(t));
    }
    lt(l, t) {
        return this.setLimit('max', l, !1, O.toString(t));
    }
    setLimit(l, t, i, r) {
        return new pt({
            ...this._def,
            checks: [
                ...this._def.checks,
                { kind: l, value: t, inclusive: i, message: O.toString(r) },
            ],
        });
    }
    _addCheck(l) {
        return new pt({ ...this._def, checks: [...this._def.checks, l] });
    }
    positive(l) {
        return this._addCheck({
            kind: 'min',
            value: BigInt(0),
            inclusive: !1,
            message: O.toString(l),
        });
    }
    negative(l) {
        return this._addCheck({
            kind: 'max',
            value: BigInt(0),
            inclusive: !1,
            message: O.toString(l),
        });
    }
    nonpositive(l) {
        return this._addCheck({
            kind: 'max',
            value: BigInt(0),
            inclusive: !0,
            message: O.toString(l),
        });
    }
    nonnegative(l) {
        return this._addCheck({
            kind: 'min',
            value: BigInt(0),
            inclusive: !0,
            message: O.toString(l),
        });
    }
    multipleOf(l, t) {
        return this._addCheck({ kind: 'multipleOf', value: l, message: O.toString(t) });
    }
    get minValue() {
        let l = null;
        for (let t of this._def.checks)
            if (t.kind === 'min') {
                if (l === null || t.value > l) l = t.value;
            }
        return l;
    }
    get maxValue() {
        let l = null;
        for (let t of this._def.checks)
            if (t.kind === 'max') {
                if (l === null || t.value < l) l = t.value;
            }
        return l;
    }
}
pt.create = (l) => {
    return new pt({ checks: [], typeName: X.ZodBigInt, coerce: l?.coerce ?? !1, ...E(l) });
};
class dt extends Q {
    _parse(l) {
        if (this._def.coerce) l.data = Boolean(l.data);
        if (this._getType(l) !== z.boolean) {
            let i = this._getOrReturnCtx(l);
            return (x(i, { code: p.invalid_type, expected: z.boolean, received: i.parsedType }), s);
        }
        return al(l.data);
    }
}
dt.create = (l) => {
    return new dt({ typeName: X.ZodBoolean, coerce: l?.coerce || !1, ...E(l) });
};
class qt extends Q {
    _parse(l) {
        if (this._def.coerce) l.data = new Date(l.data);
        if (this._getType(l) !== z.date) {
            let n = this._getOrReturnCtx(l);
            return (x(n, { code: p.invalid_type, expected: z.date, received: n.parsedType }), s);
        }
        if (Number.isNaN(l.data.getTime())) {
            let n = this._getOrReturnCtx(l);
            return (x(n, { code: p.invalid_date }), s);
        }
        let i = new ul(),
            r = void 0;
        for (let n of this._def.checks)
            if (n.kind === 'min') {
                if (l.data.getTime() < n.value)
                    ((r = this._getOrReturnCtx(l, r)),
                        x(r, {
                            code: p.too_small,
                            message: n.message,
                            inclusive: !0,
                            exact: !1,
                            minimum: n.value,
                            type: 'date',
                        }),
                        i.dirty());
            } else if (n.kind === 'max') {
                if (l.data.getTime() > n.value)
                    ((r = this._getOrReturnCtx(l, r)),
                        x(r, {
                            code: p.too_big,
                            message: n.message,
                            inclusive: !0,
                            exact: !1,
                            maximum: n.value,
                            type: 'date',
                        }),
                        i.dirty());
            } else B.assertNever(n);
        return { status: i.value, value: new Date(l.data.getTime()) };
    }
    _addCheck(l) {
        return new qt({ ...this._def, checks: [...this._def.checks, l] });
    }
    min(l, t) {
        return this._addCheck({ kind: 'min', value: l.getTime(), message: O.toString(t) });
    }
    max(l, t) {
        return this._addCheck({ kind: 'max', value: l.getTime(), message: O.toString(t) });
    }
    get minDate() {
        let l = null;
        for (let t of this._def.checks)
            if (t.kind === 'min') {
                if (l === null || t.value > l) l = t.value;
            }
        return l != null ? new Date(l) : null;
    }
    get maxDate() {
        let l = null;
        for (let t of this._def.checks)
            if (t.kind === 'max') {
                if (l === null || t.value < l) l = t.value;
            }
        return l != null ? new Date(l) : null;
    }
}
qt.create = (l) => {
    return new qt({ checks: [], coerce: l?.coerce || !1, typeName: X.ZodDate, ...E(l) });
};
class Vi extends Q {
    _parse(l) {
        if (this._getType(l) !== z.symbol) {
            let i = this._getOrReturnCtx(l);
            return (x(i, { code: p.invalid_type, expected: z.symbol, received: i.parsedType }), s);
        }
        return al(l.data);
    }
}
Vi.create = (l) => {
    return new Vi({ typeName: X.ZodSymbol, ...E(l) });
};
class li extends Q {
    _parse(l) {
        if (this._getType(l) !== z.undefined) {
            let i = this._getOrReturnCtx(l);
            return (
                x(i, { code: p.invalid_type, expected: z.undefined, received: i.parsedType }),
                s
            );
        }
        return al(l.data);
    }
}
li.create = (l) => {
    return new li({ typeName: X.ZodUndefined, ...E(l) });
};
class ti extends Q {
    _parse(l) {
        if (this._getType(l) !== z.null) {
            let i = this._getOrReturnCtx(l);
            return (x(i, { code: p.invalid_type, expected: z.null, received: i.parsedType }), s);
        }
        return al(l.data);
    }
}
ti.create = (l) => {
    return new ti({ typeName: X.ZodNull, ...E(l) });
};
class st extends Q {
    constructor() {
        super(...arguments);
        this._any = !0;
    }
    _parse(l) {
        return al(l.data);
    }
}
st.create = (l) => {
    return new st({ typeName: X.ZodAny, ...E(l) });
};
class mt extends Q {
    constructor() {
        super(...arguments);
        this._unknown = !0;
    }
    _parse(l) {
        return al(l.data);
    }
}
mt.create = (l) => {
    return new mt({ typeName: X.ZodUnknown, ...E(l) });
};
class Nl extends Q {
    _parse(l) {
        let t = this._getOrReturnCtx(l);
        return (x(t, { code: p.invalid_type, expected: z.never, received: t.parsedType }), s);
    }
}
Nl.create = (l) => {
    return new Nl({ typeName: X.ZodNever, ...E(l) });
};
class Ki extends Q {
    _parse(l) {
        if (this._getType(l) !== z.undefined) {
            let i = this._getOrReturnCtx(l);
            return (x(i, { code: p.invalid_type, expected: z.void, received: i.parsedType }), s);
        }
        return al(l.data);
    }
}
Ki.create = (l) => {
    return new Ki({ typeName: X.ZodVoid, ...E(l) });
};
class Ll extends Q {
    _parse(l) {
        let { ctx: t, status: i } = this._processInputParams(l),
            r = this._def;
        if (t.parsedType !== z.array)
            return (x(t, { code: p.invalid_type, expected: z.array, received: t.parsedType }), s);
        if (r.exactLength !== null) {
            let o = t.data.length > r.exactLength.value,
                b = t.data.length < r.exactLength.value;
            if (o || b)
                (x(t, {
                    code: o ? p.too_big : p.too_small,
                    minimum: b ? r.exactLength.value : void 0,
                    maximum: o ? r.exactLength.value : void 0,
                    type: 'array',
                    inclusive: !0,
                    exact: !0,
                    message: r.exactLength.message,
                }),
                    i.dirty());
        }
        if (r.minLength !== null) {
            if (t.data.length < r.minLength.value)
                (x(t, {
                    code: p.too_small,
                    minimum: r.minLength.value,
                    type: 'array',
                    inclusive: !0,
                    exact: !1,
                    message: r.minLength.message,
                }),
                    i.dirty());
        }
        if (r.maxLength !== null) {
            if (t.data.length > r.maxLength.value)
                (x(t, {
                    code: p.too_big,
                    maximum: r.maxLength.value,
                    type: 'array',
                    inclusive: !0,
                    exact: !1,
                    message: r.maxLength.message,
                }),
                    i.dirty());
        }
        if (t.common.async)
            return Promise.all(
                [...t.data].map((o, b) => {
                    return r.type._parseAsync(new Fl(t, o, t.path, b));
                }),
            ).then((o) => {
                return ul.mergeArray(i, o);
            });
        let n = [...t.data].map((o, b) => {
            return r.type._parseSync(new Fl(t, o, t.path, b));
        });
        return ul.mergeArray(i, n);
    }
    get element() {
        return this._def.type;
    }
    min(l, t) {
        return new Ll({ ...this._def, minLength: { value: l, message: O.toString(t) } });
    }
    max(l, t) {
        return new Ll({ ...this._def, maxLength: { value: l, message: O.toString(t) } });
    }
    length(l, t) {
        return new Ll({ ...this._def, exactLength: { value: l, message: O.toString(t) } });
    }
    nonempty(l) {
        return this.min(1, l);
    }
}
Ll.create = (l, t) => {
    return new Ll({
        type: l,
        minLength: null,
        maxLength: null,
        exactLength: null,
        typeName: X.ZodArray,
        ...E(t),
    });
};
function Tt(l) {
    if (l instanceof il) {
        let t = {};
        for (let i in l.shape) {
            let r = l.shape[i];
            t[i] = Bl.create(Tt(r));
        }
        return new il({ ...l._def, shape: () => t });
    } else if (l instanceof Ll) return new Ll({ ...l._def, type: Tt(l.element) });
    else if (l instanceof Bl) return Bl.create(Tt(l.unwrap()));
    else if (l instanceof nt) return nt.create(Tt(l.unwrap()));
    else if (l instanceof Cl) return Cl.create(l.items.map((t) => Tt(t)));
    else return l;
}
class il extends Q {
    constructor() {
        super(...arguments);
        ((this._cached = null), (this.nonstrict = this.passthrough), (this.augment = this.extend));
    }
    _getCached() {
        if (this._cached !== null) return this._cached;
        let l = this._def.shape(),
            t = B.objectKeys(l);
        return ((this._cached = { shape: l, keys: t }), this._cached);
    }
    _parse(l) {
        if (this._getType(l) !== z.object) {
            let e = this._getOrReturnCtx(l);
            return (x(e, { code: p.invalid_type, expected: z.object, received: e.parsedType }), s);
        }
        let { status: i, ctx: r } = this._processInputParams(l),
            { shape: n, keys: o } = this._getCached(),
            b = [];
        if (!(this._def.catchall instanceof Nl && this._def.unknownKeys === 'strip')) {
            for (let e in r.data) if (!o.includes(e)) b.push(e);
        }
        let g = [];
        for (let e of o) {
            let f = n[e],
                h = r.data[e];
            g.push({
                key: { status: 'valid', value: e },
                value: f._parse(new Fl(r, h, r.path, e)),
                alwaysSet: e in r.data,
            });
        }
        if (this._def.catchall instanceof Nl) {
            let e = this._def.unknownKeys;
            if (e === 'passthrough')
                for (let f of b)
                    g.push({
                        key: { status: 'valid', value: f },
                        value: { status: 'valid', value: r.data[f] },
                    });
            else if (e === 'strict') {
                if (b.length > 0) (x(r, { code: p.unrecognized_keys, keys: b }), i.dirty());
            } else if (e === 'strip');
            else throw new Error('Internal ZodObject error: invalid unknownKeys value.');
        } else {
            let e = this._def.catchall;
            for (let f of b) {
                let h = r.data[f];
                g.push({
                    key: { status: 'valid', value: f },
                    value: e._parse(new Fl(r, h, r.path, f)),
                    alwaysSet: f in r.data,
                });
            }
        }
        if (r.common.async)
            return Promise.resolve()
                .then(async () => {
                    let e = [];
                    for (let f of g) {
                        let h = await f.key,
                            c = await f.value;
                        e.push({ key: h, value: c, alwaysSet: f.alwaysSet });
                    }
                    return e;
                })
                .then((e) => {
                    return ul.mergeObjectSync(i, e);
                });
        else return ul.mergeObjectSync(i, g);
    }
    get shape() {
        return this._def.shape();
    }
    strict(l) {
        return (
            O.errToObj,
            new il({
                ...this._def,
                unknownKeys: 'strict',
                ...(l !== void 0
                    ? {
                          errorMap: (t, i) => {
                              let r = this._def.errorMap?.(t, i).message ?? i.defaultError;
                              if (t.code === 'unrecognized_keys')
                                  return { message: O.errToObj(l).message ?? r };
                              return { message: r };
                          },
                      }
                    : {}),
            })
        );
    }
    strip() {
        return new il({ ...this._def, unknownKeys: 'strip' });
    }
    passthrough() {
        return new il({ ...this._def, unknownKeys: 'passthrough' });
    }
    extend(l) {
        return new il({ ...this._def, shape: () => ({ ...this._def.shape(), ...l }) });
    }
    merge(l) {
        return new il({
            unknownKeys: l._def.unknownKeys,
            catchall: l._def.catchall,
            shape: () => ({ ...this._def.shape(), ...l._def.shape() }),
            typeName: X.ZodObject,
        });
    }
    setKey(l, t) {
        return this.augment({ [l]: t });
    }
    catchall(l) {
        return new il({ ...this._def, catchall: l });
    }
    pick(l) {
        let t = {};
        for (let i of B.objectKeys(l)) if (l[i] && this.shape[i]) t[i] = this.shape[i];
        return new il({ ...this._def, shape: () => t });
    }
    omit(l) {
        let t = {};
        for (let i of B.objectKeys(this.shape)) if (!l[i]) t[i] = this.shape[i];
        return new il({ ...this._def, shape: () => t });
    }
    deepPartial() {
        return Tt(this);
    }
    partial(l) {
        let t = {};
        for (let i of B.objectKeys(this.shape)) {
            let r = this.shape[i];
            if (l && !l[i]) t[i] = r;
            else t[i] = r.optional();
        }
        return new il({ ...this._def, shape: () => t });
    }
    required(l) {
        let t = {};
        for (let i of B.objectKeys(this.shape))
            if (l && !l[i]) t[i] = this.shape[i];
            else {
                let n = this.shape[i];
                while (n instanceof Bl) n = n._def.innerType;
                t[i] = n;
            }
        return new il({ ...this._def, shape: () => t });
    }
    keyof() {
        return Wh(B.objectKeys(this.shape));
    }
}
il.create = (l, t) => {
    return new il({
        shape: () => l,
        unknownKeys: 'strip',
        catchall: Nl.create(),
        typeName: X.ZodObject,
        ...E(t),
    });
};
il.strictCreate = (l, t) => {
    return new il({
        shape: () => l,
        unknownKeys: 'strict',
        catchall: Nl.create(),
        typeName: X.ZodObject,
        ...E(t),
    });
};
il.lazycreate = (l, t) => {
    return new il({
        shape: l,
        unknownKeys: 'strip',
        catchall: Nl.create(),
        typeName: X.ZodObject,
        ...E(t),
    });
};
class ii extends Q {
    _parse(l) {
        let { ctx: t } = this._processInputParams(l),
            i = this._def.options;
        function r(n) {
            for (let b of n) if (b.result.status === 'valid') return b.result;
            for (let b of n)
                if (b.result.status === 'dirty')
                    return (t.common.issues.push(...b.ctx.common.issues), b.result);
            let o = n.map((b) => new $l(b.ctx.common.issues));
            return (x(t, { code: p.invalid_union, unionErrors: o }), s);
        }
        if (t.common.async)
            return Promise.all(
                i.map(async (n) => {
                    let o = { ...t, common: { ...t.common, issues: [] }, parent: null };
                    return {
                        result: await n._parseAsync({ data: t.data, path: t.path, parent: o }),
                        ctx: o,
                    };
                }),
            ).then(r);
        else {
            let n = void 0,
                o = [];
            for (let g of i) {
                let e = { ...t, common: { ...t.common, issues: [] }, parent: null },
                    f = g._parseSync({ data: t.data, path: t.path, parent: e });
                if (f.status === 'valid') return f;
                else if (f.status === 'dirty' && !n) n = { result: f, ctx: e };
                if (e.common.issues.length) o.push(e.common.issues);
            }
            if (n) return (t.common.issues.push(...n.ctx.common.issues), n.result);
            let b = o.map((g) => new $l(g));
            return (x(t, { code: p.invalid_union, unionErrors: b }), s);
        }
    }
    get options() {
        return this._def.options;
    }
}
ii.create = (l, t) => {
    return new ii({ options: l, typeName: X.ZodUnion, ...E(t) });
};
var rt = (l) => {
    if (l instanceof ni) return rt(l.schema);
    else if (l instanceof Rl) return rt(l.innerType());
    else if (l instanceof oi) return [l.value];
    else if (l instanceof ut) return l.options;
    else if (l instanceof bi) return B.objectValues(l.enum);
    else if (l instanceof fi) return rt(l._def.innerType);
    else if (l instanceof li) return [void 0];
    else if (l instanceof ti) return [null];
    else if (l instanceof Bl) return [void 0, ...rt(l.unwrap())];
    else if (l instanceof nt) return [null, ...rt(l.unwrap())];
    else if (l instanceof Gr) return rt(l.unwrap());
    else if (l instanceof ei) return rt(l.unwrap());
    else if (l instanceof gi) return rt(l._def.innerType);
    else return [];
};
class Qr extends Q {
    _parse(l) {
        let { ctx: t } = this._processInputParams(l);
        if (t.parsedType !== z.object)
            return (x(t, { code: p.invalid_type, expected: z.object, received: t.parsedType }), s);
        let i = this.discriminator,
            r = t.data[i],
            n = this.optionsMap.get(r);
        if (!n)
            return (
                x(t, {
                    code: p.invalid_union_discriminator,
                    options: Array.from(this.optionsMap.keys()),
                    path: [i],
                }),
                s
            );
        if (t.common.async) return n._parseAsync({ data: t.data, path: t.path, parent: t });
        else return n._parseSync({ data: t.data, path: t.path, parent: t });
    }
    get discriminator() {
        return this._def.discriminator;
    }
    get options() {
        return this._def.options;
    }
    get optionsMap() {
        return this._def.optionsMap;
    }
    static create(l, t, i) {
        let r = new Map();
        for (let n of t) {
            let o = rt(n.shape[l]);
            if (!o.length)
                throw new Error(
                    `A discriminator value for key \`${l}\` could not be extracted from all schema options`,
                );
            for (let b of o) {
                if (r.has(b))
                    throw new Error(
                        `Discriminator property ${String(l)} has duplicate value ${String(b)}`,
                    );
                r.set(b, n);
            }
        }
        return new Qr({
            typeName: X.ZodDiscriminatedUnion,
            discriminator: l,
            options: t,
            optionsMap: r,
            ...E(i),
        });
    }
}
function tf(l, t) {
    let i = Sl(l),
        r = Sl(t);
    if (l === t) return { valid: !0, data: l };
    else if (i === z.object && r === z.object) {
        let n = B.objectKeys(t),
            o = B.objectKeys(l).filter((g) => n.indexOf(g) !== -1),
            b = { ...l, ...t };
        for (let g of o) {
            let e = tf(l[g], t[g]);
            if (!e.valid) return { valid: !1 };
            b[g] = e.data;
        }
        return { valid: !0, data: b };
    } else if (i === z.array && r === z.array) {
        if (l.length !== t.length) return { valid: !1 };
        let n = [];
        for (let o = 0; o < l.length; o++) {
            let b = l[o],
                g = t[o],
                e = tf(b, g);
            if (!e.valid) return { valid: !1 };
            n.push(e.data);
        }
        return { valid: !0, data: n };
    } else if (i === z.date && r === z.date && +l === +t) return { valid: !0, data: l };
    else return { valid: !1 };
}
class ri extends Q {
    _parse(l) {
        let { status: t, ctx: i } = this._processInputParams(l),
            r = (n, o) => {
                if (Yr(n) || Yr(o)) return s;
                let b = tf(n.value, o.value);
                if (!b.valid) return (x(i, { code: p.invalid_intersection_types }), s);
                if (Er(n) || Er(o)) t.dirty();
                return { status: t.value, value: b.data };
            };
        if (i.common.async)
            return Promise.all([
                this._def.left._parseAsync({ data: i.data, path: i.path, parent: i }),
                this._def.right._parseAsync({ data: i.data, path: i.path, parent: i }),
            ]).then(([n, o]) => r(n, o));
        else
            return r(
                this._def.left._parseSync({ data: i.data, path: i.path, parent: i }),
                this._def.right._parseSync({ data: i.data, path: i.path, parent: i }),
            );
    }
}
ri.create = (l, t, i) => {
    return new ri({ left: l, right: t, typeName: X.ZodIntersection, ...E(i) });
};
class Cl extends Q {
    _parse(l) {
        let { status: t, ctx: i } = this._processInputParams(l);
        if (i.parsedType !== z.array)
            return (x(i, { code: p.invalid_type, expected: z.array, received: i.parsedType }), s);
        if (i.data.length < this._def.items.length)
            return (
                x(i, {
                    code: p.too_small,
                    minimum: this._def.items.length,
                    inclusive: !0,
                    exact: !1,
                    type: 'array',
                }),
                s
            );
        if (!this._def.rest && i.data.length > this._def.items.length)
            (x(i, {
                code: p.too_big,
                maximum: this._def.items.length,
                inclusive: !0,
                exact: !1,
                type: 'array',
            }),
                t.dirty());
        let n = [...i.data]
            .map((o, b) => {
                let g = this._def.items[b] || this._def.rest;
                if (!g) return null;
                return g._parse(new Fl(i, o, i.path, b));
            })
            .filter((o) => !!o);
        if (i.common.async)
            return Promise.all(n).then((o) => {
                return ul.mergeArray(t, o);
            });
        else return ul.mergeArray(t, n);
    }
    get items() {
        return this._def.items;
    }
    rest(l) {
        return new Cl({ ...this._def, rest: l });
    }
}
Cl.create = (l, t) => {
    if (!Array.isArray(l)) throw new Error('You must pass an array of schemas to z.tuple([ ... ])');
    return new Cl({ items: l, typeName: X.ZodTuple, rest: null, ...E(t) });
};
class Hi extends Q {
    get keySchema() {
        return this._def.keyType;
    }
    get valueSchema() {
        return this._def.valueType;
    }
    _parse(l) {
        let { status: t, ctx: i } = this._processInputParams(l);
        if (i.parsedType !== z.object)
            return (x(i, { code: p.invalid_type, expected: z.object, received: i.parsedType }), s);
        let r = [],
            n = this._def.keyType,
            o = this._def.valueType;
        for (let b in i.data)
            r.push({
                key: n._parse(new Fl(i, b, i.path, b)),
                value: o._parse(new Fl(i, i.data[b], i.path, b)),
                alwaysSet: b in i.data,
            });
        if (i.common.async) return ul.mergeObjectAsync(t, r);
        else return ul.mergeObjectSync(t, r);
    }
    get element() {
        return this._def.valueType;
    }
    static create(l, t, i) {
        if (t instanceof Q)
            return new Hi({ keyType: l, valueType: t, typeName: X.ZodRecord, ...E(i) });
        return new Hi({ keyType: Gl.create(), valueType: l, typeName: X.ZodRecord, ...E(t) });
    }
}
class Mi extends Q {
    get keySchema() {
        return this._def.keyType;
    }
    get valueSchema() {
        return this._def.valueType;
    }
    _parse(l) {
        let { status: t, ctx: i } = this._processInputParams(l);
        if (i.parsedType !== z.map)
            return (x(i, { code: p.invalid_type, expected: z.map, received: i.parsedType }), s);
        let r = this._def.keyType,
            n = this._def.valueType,
            o = [...i.data.entries()].map(([b, g], e) => {
                return {
                    key: r._parse(new Fl(i, b, i.path, [e, 'key'])),
                    value: n._parse(new Fl(i, g, i.path, [e, 'value'])),
                };
            });
        if (i.common.async) {
            let b = new Map();
            return Promise.resolve().then(async () => {
                for (let g of o) {
                    let e = await g.key,
                        f = await g.value;
                    if (e.status === 'aborted' || f.status === 'aborted') return s;
                    if (e.status === 'dirty' || f.status === 'dirty') t.dirty();
                    b.set(e.value, f.value);
                }
                return { status: t.value, value: b };
            });
        } else {
            let b = new Map();
            for (let g of o) {
                let { key: e, value: f } = g;
                if (e.status === 'aborted' || f.status === 'aborted') return s;
                if (e.status === 'dirty' || f.status === 'dirty') t.dirty();
                b.set(e.value, f.value);
            }
            return { status: t.value, value: b };
        }
    }
}
Mi.create = (l, t, i) => {
    return new Mi({ valueType: t, keyType: l, typeName: X.ZodMap, ...E(i) });
};
class Jt extends Q {
    _parse(l) {
        let { status: t, ctx: i } = this._processInputParams(l);
        if (i.parsedType !== z.set)
            return (x(i, { code: p.invalid_type, expected: z.set, received: i.parsedType }), s);
        let r = this._def;
        if (r.minSize !== null) {
            if (i.data.size < r.minSize.value)
                (x(i, {
                    code: p.too_small,
                    minimum: r.minSize.value,
                    type: 'set',
                    inclusive: !0,
                    exact: !1,
                    message: r.minSize.message,
                }),
                    t.dirty());
        }
        if (r.maxSize !== null) {
            if (i.data.size > r.maxSize.value)
                (x(i, {
                    code: p.too_big,
                    maximum: r.maxSize.value,
                    type: 'set',
                    inclusive: !0,
                    exact: !1,
                    message: r.maxSize.message,
                }),
                    t.dirty());
        }
        let n = this._def.valueType;
        function o(g) {
            let e = new Set();
            for (let f of g) {
                if (f.status === 'aborted') return s;
                if (f.status === 'dirty') t.dirty();
                e.add(f.value);
            }
            return { status: t.value, value: e };
        }
        let b = [...i.data.values()].map((g, e) => n._parse(new Fl(i, g, i.path, e)));
        if (i.common.async) return Promise.all(b).then((g) => o(g));
        else return o(b);
    }
    min(l, t) {
        return new Jt({ ...this._def, minSize: { value: l, message: O.toString(t) } });
    }
    max(l, t) {
        return new Jt({ ...this._def, maxSize: { value: l, message: O.toString(t) } });
    }
    size(l, t) {
        return this.min(l, t).max(l, t);
    }
    nonempty(l) {
        return this.min(1, l);
    }
}
Jt.create = (l, t) => {
    return new Jt({ valueType: l, minSize: null, maxSize: null, typeName: X.ZodSet, ...E(t) });
};
class Zt extends Q {
    constructor() {
        super(...arguments);
        this.validate = this.implement;
    }
    _parse(l) {
        let { ctx: t } = this._processInputParams(l);
        if (t.parsedType !== z.function)
            return (
                x(t, { code: p.invalid_type, expected: z.function, received: t.parsedType }),
                s
            );
        function i(b, g) {
            return Ri({
                data: b,
                path: t.path,
                errorMaps: [t.common.contextualErrorMap, t.schemaErrorMap, Pt(), it].filter(
                    (e) => !!e,
                ),
                issueData: { code: p.invalid_arguments, argumentsError: g },
            });
        }
        function r(b, g) {
            return Ri({
                data: b,
                path: t.path,
                errorMaps: [t.common.contextualErrorMap, t.schemaErrorMap, Pt(), it].filter(
                    (e) => !!e,
                ),
                issueData: { code: p.invalid_return_type, returnTypeError: g },
            });
        }
        let n = { errorMap: t.common.contextualErrorMap },
            o = t.data;
        if (this._def.returns instanceof Xt) {
            let b = this;
            return al(async function (...g) {
                let e = new $l([]),
                    f = await b._def.args.parseAsync(g, n).catch((m) => {
                        throw (e.addIssue(i(g, m)), e);
                    }),
                    h = await Reflect.apply(o, this, f);
                return await b._def.returns._def.type.parseAsync(h, n).catch((m) => {
                    throw (e.addIssue(r(h, m)), e);
                });
            });
        } else {
            let b = this;
            return al(function (...g) {
                let e = b._def.args.safeParse(g, n);
                if (!e.success) throw new $l([i(g, e.error)]);
                let f = Reflect.apply(o, this, e.data),
                    h = b._def.returns.safeParse(f, n);
                if (!h.success) throw new $l([r(f, h.error)]);
                return h.data;
            });
        }
    }
    parameters() {
        return this._def.args;
    }
    returnType() {
        return this._def.returns;
    }
    args(...l) {
        return new Zt({ ...this._def, args: Cl.create(l).rest(mt.create()) });
    }
    returns(l) {
        return new Zt({ ...this._def, returns: l });
    }
    implement(l) {
        return this.parse(l);
    }
    strictImplement(l) {
        return this.parse(l);
    }
    static create(l, t, i) {
        return new Zt({
            args: l ? l : Cl.create([]).rest(mt.create()),
            returns: t || mt.create(),
            typeName: X.ZodFunction,
            ...E(i),
        });
    }
}
class ni extends Q {
    get schema() {
        return this._def.getter();
    }
    _parse(l) {
        let { ctx: t } = this._processInputParams(l);
        return this._def.getter()._parse({ data: t.data, path: t.path, parent: t });
    }
}
ni.create = (l, t) => {
    return new ni({ getter: l, typeName: X.ZodLazy, ...E(t) });
};
class oi extends Q {
    _parse(l) {
        if (l.data !== this._def.value) {
            let t = this._getOrReturnCtx(l);
            return (
                x(t, { received: t.data, code: p.invalid_literal, expected: this._def.value }),
                s
            );
        }
        return { status: 'valid', value: l.data };
    }
    get value() {
        return this._def.value;
    }
}
oi.create = (l, t) => {
    return new oi({ value: l, typeName: X.ZodLiteral, ...E(t) });
};
function Wh(l, t) {
    return new ut({ values: l, typeName: X.ZodEnum, ...E(t) });
}
class ut extends Q {
    _parse(l) {
        if (typeof l.data !== 'string') {
            let t = this._getOrReturnCtx(l),
                i = this._def.values;
            return (
                x(t, { expected: B.joinValues(i), received: t.parsedType, code: p.invalid_type }),
                s
            );
        }
        if (!this._cache) this._cache = new Set(this._def.values);
        if (!this._cache.has(l.data)) {
            let t = this._getOrReturnCtx(l),
                i = this._def.values;
            return (x(t, { received: t.data, code: p.invalid_enum_value, options: i }), s);
        }
        return al(l.data);
    }
    get options() {
        return this._def.values;
    }
    get enum() {
        let l = {};
        for (let t of this._def.values) l[t] = t;
        return l;
    }
    get Values() {
        let l = {};
        for (let t of this._def.values) l[t] = t;
        return l;
    }
    get Enum() {
        let l = {};
        for (let t of this._def.values) l[t] = t;
        return l;
    }
    extract(l, t = this._def) {
        return ut.create(l, { ...this._def, ...t });
    }
    exclude(l, t = this._def) {
        return ut.create(
            this.options.filter((i) => !l.includes(i)),
            { ...this._def, ...t },
        );
    }
}
ut.create = Wh;
class bi extends Q {
    _parse(l) {
        let t = B.getValidEnumValues(this._def.values),
            i = this._getOrReturnCtx(l);
        if (i.parsedType !== z.string && i.parsedType !== z.number) {
            let r = B.objectValues(t);
            return (
                x(i, { expected: B.joinValues(r), received: i.parsedType, code: p.invalid_type }),
                s
            );
        }
        if (!this._cache) this._cache = new Set(B.getValidEnumValues(this._def.values));
        if (!this._cache.has(l.data)) {
            let r = B.objectValues(t);
            return (x(i, { received: i.data, code: p.invalid_enum_value, options: r }), s);
        }
        return al(l.data);
    }
    get enum() {
        return this._def.values;
    }
}
bi.create = (l, t) => {
    return new bi({ values: l, typeName: X.ZodNativeEnum, ...E(t) });
};
class Xt extends Q {
    unwrap() {
        return this._def.type;
    }
    _parse(l) {
        let { ctx: t } = this._processInputParams(l);
        if (t.parsedType !== z.promise && t.common.async === !1)
            return (x(t, { code: p.invalid_type, expected: z.promise, received: t.parsedType }), s);
        let i = t.parsedType === z.promise ? t.data : Promise.resolve(t.data);
        return al(
            i.then((r) => {
                return this._def.type.parseAsync(r, {
                    path: t.path,
                    errorMap: t.common.contextualErrorMap,
                });
            }),
        );
    }
}
Xt.create = (l, t) => {
    return new Xt({ type: l, typeName: X.ZodPromise, ...E(t) });
};
class Rl extends Q {
    innerType() {
        return this._def.schema;
    }
    sourceType() {
        return this._def.schema._def.typeName === X.ZodEffects
            ? this._def.schema.sourceType()
            : this._def.schema;
    }
    _parse(l) {
        let { status: t, ctx: i } = this._processInputParams(l),
            r = this._def.effect || null,
            n = {
                addIssue: (o) => {
                    if ((x(i, o), o.fatal)) t.abort();
                    else t.dirty();
                },
                get path() {
                    return i.path;
                },
            };
        if (((n.addIssue = n.addIssue.bind(n)), r.type === 'preprocess')) {
            let o = r.transform(i.data, n);
            if (i.common.async)
                return Promise.resolve(o).then(async (b) => {
                    if (t.value === 'aborted') return s;
                    let g = await this._def.schema._parseAsync({
                        data: b,
                        path: i.path,
                        parent: i,
                    });
                    if (g.status === 'aborted') return s;
                    if (g.status === 'dirty') return $t(g.value);
                    if (t.value === 'dirty') return $t(g.value);
                    return g;
                });
            else {
                if (t.value === 'aborted') return s;
                let b = this._def.schema._parseSync({ data: o, path: i.path, parent: i });
                if (b.status === 'aborted') return s;
                if (b.status === 'dirty') return $t(b.value);
                if (t.value === 'dirty') return $t(b.value);
                return b;
            }
        }
        if (r.type === 'refinement') {
            let o = (b) => {
                let g = r.refinement(b, n);
                if (i.common.async) return Promise.resolve(g);
                if (g instanceof Promise)
                    throw new Error(
                        'Async refinement encountered during synchronous parse operation. Use .parseAsync instead.',
                    );
                return b;
            };
            if (i.common.async === !1) {
                let b = this._def.schema._parseSync({ data: i.data, path: i.path, parent: i });
                if (b.status === 'aborted') return s;
                if (b.status === 'dirty') t.dirty();
                return (o(b.value), { status: t.value, value: b.value });
            } else
                return this._def.schema
                    ._parseAsync({ data: i.data, path: i.path, parent: i })
                    .then((b) => {
                        if (b.status === 'aborted') return s;
                        if (b.status === 'dirty') t.dirty();
                        return o(b.value).then(() => {
                            return { status: t.value, value: b.value };
                        });
                    });
        }
        if (r.type === 'transform')
            if (i.common.async === !1) {
                let o = this._def.schema._parseSync({ data: i.data, path: i.path, parent: i });
                if (!ct(o)) return s;
                let b = r.transform(o.value, n);
                if (b instanceof Promise)
                    throw new Error(
                        'Asynchronous transform encountered during synchronous parse operation. Use .parseAsync instead.',
                    );
                return { status: t.value, value: b };
            } else
                return this._def.schema
                    ._parseAsync({ data: i.data, path: i.path, parent: i })
                    .then((o) => {
                        if (!ct(o)) return s;
                        return Promise.resolve(r.transform(o.value, n)).then((b) => ({
                            status: t.value,
                            value: b,
                        }));
                    });
        B.assertNever(r);
    }
}
Rl.create = (l, t, i) => {
    return new Rl({ schema: l, typeName: X.ZodEffects, effect: t, ...E(i) });
};
Rl.createWithPreprocess = (l, t, i) => {
    return new Rl({
        schema: t,
        effect: { type: 'preprocess', transform: l },
        typeName: X.ZodEffects,
        ...E(i),
    });
};
class Bl extends Q {
    _parse(l) {
        if (this._getType(l) === z.undefined) return al(void 0);
        return this._def.innerType._parse(l);
    }
    unwrap() {
        return this._def.innerType;
    }
}
Bl.create = (l, t) => {
    return new Bl({ innerType: l, typeName: X.ZodOptional, ...E(t) });
};
class nt extends Q {
    _parse(l) {
        if (this._getType(l) === z.null) return al(null);
        return this._def.innerType._parse(l);
    }
    unwrap() {
        return this._def.innerType;
    }
}
nt.create = (l, t) => {
    return new nt({ innerType: l, typeName: X.ZodNullable, ...E(t) });
};
class fi extends Q {
    _parse(l) {
        let { ctx: t } = this._processInputParams(l),
            i = t.data;
        if (t.parsedType === z.undefined) i = this._def.defaultValue();
        return this._def.innerType._parse({ data: i, path: t.path, parent: t });
    }
    removeDefault() {
        return this._def.innerType;
    }
}
fi.create = (l, t) => {
    return new fi({
        innerType: l,
        typeName: X.ZodDefault,
        defaultValue: typeof t.default === 'function' ? t.default : () => t.default,
        ...E(t),
    });
};
class gi extends Q {
    _parse(l) {
        let { ctx: t } = this._processInputParams(l),
            i = { ...t, common: { ...t.common, issues: [] } },
            r = this._def.innerType._parse({ data: i.data, path: i.path, parent: { ...i } });
        if (It(r))
            return r.then((n) => {
                return {
                    status: 'valid',
                    value:
                        n.status === 'valid'
                            ? n.value
                            : this._def.catchValue({
                                  get error() {
                                      return new $l(i.common.issues);
                                  },
                                  input: i.data,
                              }),
                };
            });
        else
            return {
                status: 'valid',
                value:
                    r.status === 'valid'
                        ? r.value
                        : this._def.catchValue({
                              get error() {
                                  return new $l(i.common.issues);
                              },
                              input: i.data,
                          }),
            };
    }
    removeCatch() {
        return this._def.innerType;
    }
}
gi.create = (l, t) => {
    return new gi({
        innerType: l,
        typeName: X.ZodCatch,
        catchValue: typeof t.catch === 'function' ? t.catch : () => t.catch,
        ...E(t),
    });
};
class yi extends Q {
    _parse(l) {
        if (this._getType(l) !== z.nan) {
            let i = this._getOrReturnCtx(l);
            return (x(i, { code: p.invalid_type, expected: z.nan, received: i.parsedType }), s);
        }
        return { status: 'valid', value: l.data };
    }
}
yi.create = (l) => {
    return new yi({ typeName: X.ZodNaN, ...E(l) });
};
var H4 = Symbol('zod_brand');
class Gr extends Q {
    _parse(l) {
        let { ctx: t } = this._processInputParams(l),
            i = t.data;
        return this._def.type._parse({ data: i, path: t.path, parent: t });
    }
    unwrap() {
        return this._def.type;
    }
}
class Ai extends Q {
    _parse(l) {
        let { status: t, ctx: i } = this._processInputParams(l);
        if (i.common.async)
            return (async () => {
                let n = await this._def.in._parseAsync({ data: i.data, path: i.path, parent: i });
                if (n.status === 'aborted') return s;
                if (n.status === 'dirty') return (t.dirty(), $t(n.value));
                else return this._def.out._parseAsync({ data: n.value, path: i.path, parent: i });
            })();
        else {
            let r = this._def.in._parseSync({ data: i.data, path: i.path, parent: i });
            if (r.status === 'aborted') return s;
            if (r.status === 'dirty') return (t.dirty(), { status: 'dirty', value: r.value });
            else return this._def.out._parseSync({ data: r.value, path: i.path, parent: i });
        }
    }
    static create(l, t) {
        return new Ai({ in: l, out: t, typeName: X.ZodPipeline });
    }
}
class ei extends Q {
    _parse(l) {
        let t = this._def.innerType._parse(l),
            i = (r) => {
                if (ct(r)) r.value = Object.freeze(r.value);
                return r;
            };
        return It(t) ? t.then((r) => i(r)) : i(t);
    }
    unwrap() {
        return this._def.innerType;
    }
}
ei.create = (l, t) => {
    return new ei({ innerType: l, typeName: X.ZodReadonly, ...E(t) });
};
function qh(l, t) {
    let i = typeof l === 'function' ? l(t) : typeof l === 'string' ? { message: l } : l;
    return typeof i === 'string' ? { message: i } : i;
}
function jh(l, t = {}, i) {
    if (l)
        return st.create().superRefine((r, n) => {
            let o = l(r);
            if (o instanceof Promise)
                return o.then((b) => {
                    if (!b) {
                        let g = qh(t, r),
                            e = g.fatal ?? i ?? !0;
                        n.addIssue({ code: 'custom', ...g, fatal: e });
                    }
                });
            if (!o) {
                let b = qh(t, r),
                    g = b.fatal ?? i ?? !0;
                n.addIssue({ code: 'custom', ...b, fatal: g });
            }
            return;
        });
    return st.create();
}
var M4 = { object: il.lazycreate },
    X;
(function (l) {
    ((l.ZodString = 'ZodString'),
        (l.ZodNumber = 'ZodNumber'),
        (l.ZodNaN = 'ZodNaN'),
        (l.ZodBigInt = 'ZodBigInt'),
        (l.ZodBoolean = 'ZodBoolean'),
        (l.ZodDate = 'ZodDate'),
        (l.ZodSymbol = 'ZodSymbol'),
        (l.ZodUndefined = 'ZodUndefined'),
        (l.ZodNull = 'ZodNull'),
        (l.ZodAny = 'ZodAny'),
        (l.ZodUnknown = 'ZodUnknown'),
        (l.ZodNever = 'ZodNever'),
        (l.ZodVoid = 'ZodVoid'),
        (l.ZodArray = 'ZodArray'),
        (l.ZodObject = 'ZodObject'),
        (l.ZodUnion = 'ZodUnion'),
        (l.ZodDiscriminatedUnion = 'ZodDiscriminatedUnion'),
        (l.ZodIntersection = 'ZodIntersection'),
        (l.ZodTuple = 'ZodTuple'),
        (l.ZodRecord = 'ZodRecord'),
        (l.ZodMap = 'ZodMap'),
        (l.ZodSet = 'ZodSet'),
        (l.ZodFunction = 'ZodFunction'),
        (l.ZodLazy = 'ZodLazy'),
        (l.ZodLiteral = 'ZodLiteral'),
        (l.ZodEnum = 'ZodEnum'),
        (l.ZodEffects = 'ZodEffects'),
        (l.ZodNativeEnum = 'ZodNativeEnum'),
        (l.ZodOptional = 'ZodOptional'),
        (l.ZodNullable = 'ZodNullable'),
        (l.ZodDefault = 'ZodDefault'),
        (l.ZodCatch = 'ZodCatch'),
        (l.ZodPromise = 'ZodPromise'),
        (l.ZodBranded = 'ZodBranded'),
        (l.ZodPipeline = 'ZodPipeline'),
        (l.ZodReadonly = 'ZodReadonly'));
})(X || (X = {}));
var y4 = (l, t = { message: `Input not instance of ${l.name}` }) => jh((i) => i instanceof l, t),
    Yh = Gl.create,
    Eh = wt.create,
    A4 = yi.create,
    U4 = pt.create,
    Qh = dt.create,
    k4 = qt.create,
    S4 = Vi.create,
    N4 = li.create,
    C4 = ti.create,
    P4 = st.create,
    I4 = mt.create,
    T4 = Nl.create,
    Z4 = Ki.create,
    d4 = Ll.create,
    lx = il.create,
    tx = il.strictCreate,
    ix = ii.create,
    rx = Qr.create,
    nx = ri.create,
    ox = Cl.create,
    bx = Hi.create,
    fx = Mi.create,
    gx = Jt.create,
    ex = Zt.create,
    hx = ni.create,
    cx = oi.create,
    mx = ut.create,
    wx = bi.create,
    px = Xt.create,
    ux = Rl.create,
    zx = Bl.create,
    xx = nt.create,
    ax = Rl.createWithPreprocess,
    _x = Ai.create,
    vx = () => Yh().optional(),
    Ox = () => Eh().optional(),
    Dx = () => Qh().optional(),
    $x = {
        string: (l) => Gl.create({ ...l, coerce: !0 }),
        number: (l) => wt.create({ ...l, coerce: !0 }),
        boolean: (l) => dt.create({ ...l, coerce: !0 }),
        bigint: (l) => pt.create({ ...l, coerce: !0 }),
        date: (l) => qt.create({ ...l, coerce: !0 }),
    };
var qx = s;
var Gh = rl.object({
        type: rl.enum(['prepend', 'append']),
        targetDomId: rl.string(),
        targetOid: rl.string().nullable(),
    }),
    sx = Gh.extend({ type: rl.literal('index'), index: rl.number(), originalIndex: rl.number() }),
    uv = rl.discriminatedUnion('type', [sx, Gh]);
var Gv = rl.object({
    title: rl
        .string()
        .describe(
            'The display title of the suggestion. This will be shown to the user. Keep it concise but descriptive.',
        ),
    prompt: rl
        .string()
        .describe(
            'The prompt for the suggestion. This will be used to generate the suggestion. Make this as detailed and specific as possible.',
        ),
});
var Fv = rl.object({
    filesDiscussed: rl
        .array(rl.string())
        .describe('List of file paths mentioned in the conversation'),
    projectContext: rl
        .string()
        .describe('Summary of what the user is building and their overall goals'),
    implementationDetails: rl
        .string()
        .describe('Summary of key code decisions, patterns, and important implementation details'),
    userPreferences: rl
        .string()
        .describe('Specific preferences the user has expressed about implementation, design, etc.'),
    currentStatus: rl.string().describe('Current state of the project and any pending work'),
});
var bO = {
        ['claude-sonnet-4-20250514']: 'us.anthropic.claude-sonnet-4-20250514-v1:0',
        ['claude-3-7-sonnet-20250219']: 'us.anthropic.claude-3-7-sonnet-20250219-v1:0',
        ['claude-3-5-haiku-20241022']: 'us.anthropic.claude-3-5-haiku-20241022-v1:0',
    },
    fO = {
        ['claude-sonnet-4-20250514']: 'claude-sonnet-4@20250514',
        ['claude-3-7-sonnet-20250219']: 'claude-3-7-sonnet@20250219',
        ['claude-3-5-haiku-20241022']: 'claude-3-5-haiku@20241022',
    };
function Lh() {
    try {
        return window?.localStorage.getItem('theme') || 'light';
    } catch (l) {
        return (console.warn('Failed to get theme', l), 'light');
    }
}
function Bh(l) {
    try {
        if (l === 'dark')
            (document.documentElement.classList.add('dark'),
                window?.localStorage.setItem('theme', 'dark'));
        else
            (document.documentElement.classList.remove('dark'),
                window?.localStorage.setItem('theme', 'light'));
        return !0;
    } catch (t) {
        return (console.warn('Failed to set theme', t), !1);
    }
}
function Jx(l) {
    return (...t) => {
        try {
            return l(...t);
        } catch (i) {
            return (console.error(`Error in ${l.name}:`, i), null);
        }
    };
}
var Xx = {
        processDom: Si,
        setFrameId: Sr,
        getComputedStyleByDomId: ig,
        updateElementInstance: eg,
        getFirstOnlookElement: vg,
        captureScreenshot: Oh,
        buildLayerTree: zl,
        getElementAtLoc: gg,
        getElementByDomId: Ci,
        getElementIndex: Z0,
        setElementType: _g,
        getElementType: ag,
        getParentElement: hg,
        getChildrenCount: cg,
        getOffsetParent: mg,
        getActionLocation: xg,
        getActionElement: Pi,
        getInsertLocation: S0,
        getRemoveAction: I0,
        getTheme: Lh,
        setTheme: Bh,
        startDrag: nh,
        drag: bh,
        dragAbsolute: oh,
        endDrag: gh,
        endDragAbsolute: fh,
        endAllDrag: Tb,
        startEditingText: hh,
        editText: ch,
        stopEditingText: mh,
        isChildTextEditable: uh,
        updateStyle: A0,
        insertElement: N0,
        removeElement: P0,
        moveElement: T0,
        groupElements: wg,
        ungroupElements: pg,
        insertImage: U0,
        removeImage: k0,
        handleBodyReady: Zb,
    },
    Fh = Object.fromEntries(Object.entries(Xx).map(([l, t]) => [l, Jx(t)]));
var vl = null,
    Lr = !1,
    Kh = async () => {
        if (Lr || vl) return vl;
        ((Lr = !0), console.log(`${Wt} - Creating penpal connection`));
        let l = new Nf({ remoteWindow: window.parent, allowedOrigins: ['*'] }),
            t = Sf({ messenger: l, methods: Fh });
        return (
            t.promise
                .then((i) => {
                    if (!i) {
                        (console.error(`${Wt} - Failed to setup penpal connection: child is null`),
                            Rh());
                        return;
                    }
                    ((vl = i), console.log(`${Wt} - Penpal connection set`));
                })
                .finally(() => {
                    Lr = !1;
                }),
            t.promise.catch((i) => {
                (console.error(`${Wt} - Failed to setup penpal connection:`, i), Rh());
            }),
            vl
        );
    },
    Rh = Vh.default(() => {
        if (Lr) return;
        (console.log(`${Wt} - Reconnecting to penpal parent`), (vl = null), Kh());
    }, 1000);
Kh();
export { vl as penpalParent };
