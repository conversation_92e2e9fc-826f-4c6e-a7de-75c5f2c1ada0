.mainContainer {
    position: relative;
    min-height: 100vh;
}

.backgroundImage {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    z-index: -2;
}

.backgroundImage2 {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    z-index: -1;
}

.topBar {
    position: sticky;
    top: 0;
    z-index: 50;
    padding: 0.75rem 1rem; /* py-3 px-4 */
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 60px;
    transition: background-color 0.3s ease, backdrop-filter 0.3s ease;
}

.topBar.scrolled {
    background-color: rgba(255, 255, 255, 0.5);
    backdrop-filter: blur(10px);
}

.sceneContainer {
    position: relative;
    margin-left: auto;
    margin-right: auto;
    width: 100%;
    height: 40vh;
}

.sceneWrapper {
    width: 100%;
    height: 100%;
    overflow: hidden;
    position: relative;
}

.analysisResults {
    position: relative;
    margin-bottom: 1rem; /* mb-4 */
    background: linear-gradient(180deg, rgba(255, 255, 255, 0.5) 0%, #FFFFFF 16%);
    box-sizing: border-box;
    border: 2px solid rgba(255, 255, 255, 0.6);
    backdrop-filter: blur(40px);
    border-top-left-radius: 2rem;
    border-top-right-radius: 2rem;
}

.treatmentRecommendations {
    margin-left: 1rem; /* mx-4 */
    margin-right: 1rem; /* mx-4 */
    border-radius: 0.5rem; /* rounded-lg */
    padding: 1rem; /* p-4 */
}

.backButton {
    color: #4B5563; /* text-gray-600 */
}

.backButtonImage {
    width: 1.5rem; /* w-6 */
    height: 1.5rem; /* h-6 */
}

.title {
    font-size: 1.125rem; /* text-lg */
    font-weight: 500; /* font-medium */
    color: #1F2937;
}

.historyLink {
    font-size: 0.875rem; /* text-sm */
    color: #1F2937;
}

.titleContainer {
    position: relative;
    display: inline-block;
}

.sectionTitle {
    font-size: 1.25rem; /* text-lg */
    line-height: 2rem;
    font-weight: bold; /* font-medium */
    margin-bottom: 0.5rem; /* mb-3 */
    position: relative;
    z-index: 10;
}

.titleBg1 {
    position: absolute;
    right: 0;
    bottom: 5px;
    height: 50%;
    width: auto;
}

.titleBg2 {
    position: absolute;
    right: 0;
    bottom: 5px;
    height: 50%;
    width: auto;
}

.effectTag {
    margin-left: 0.25rem;
}

.analysisContentBox {
    padding: 1rem; /* p-4 */
    border-radius: 0.5rem; /* rounded-lg */
    background: linear-gradient(132deg, #F2F0FA 0%, #FAFAFC 30%, #F2F5FA 98%);
    border-color: #EEECFF;
    border-width: 1px;
    border-style: solid;
    margin-bottom: 1rem;
}

.analysisContentBox > * + * {
    margin-top: 0.75rem; /* space-y-3 */
}

.subSectionTitle {
    font-weight: 500; /* font-medium */
    color: #111111; /* text-gray-800 */
    margin-bottom: 0.25rem; /* mb-1 */
}

.subSectionTitle2 {
    font-weight: 500; /* font-medium */
    color: #1F2937; /* text-gray-800 */
    margin-bottom: 0.5rem; /* mb-2 */
}

.descriptionText {
    font-size: 0.875rem; /* text-sm */
    color: #111111; /* text-gray-600 */
    line-height: 1.625; /* leading-relaxed */
}

.issuesContainer {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    gap: 0.5rem; /* gap-2 */
}

.issueItem {
    background-color: #ffffff;
    border-radius: 0.5rem; /* rounded-lg */
    padding: 0.5rem; /* p-2 */
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.issueName {
    font-weight: 500; /* font-medium */
    color: #1F2937; /* text-gray-800 */
    font-size: 0.875rem; /* text-sm */
    display: flex;
    align-items: center;
}

.issueDegree {
    font-size: 0.75rem; /* text-xs */
    margin-top: 0.25rem;
}

.issueDegreeText {
    color: #4B5563; /* text-gray-600 */
}

.jumpButton {
    background: none;
    border: none;
    cursor: pointer;
    padding: 0;
    margin-left: 0.5rem;
}

.jumpButtonImage {
    height: 0.875rem;
    width: auto;
}

.tabNavContainer {
    display: flex;
    margin-bottom: 1rem; /* mb-4 */
}

.toggleButton {
    width: 4.55rem;
    height: 2rem;
    background-color: transparent;
    padding: 0;
    border: none;
    cursor: pointer;
    border-radius: 1rem;
}

.finetuneButton {
    width: 4.55rem;
    height: 2rem;
    background-color: transparent;
    padding: 0;
    margin-right: 0.5rem;
    border: none;
    border-radius: 1rem;
}

.toggleButton img,
.finetuneButton img {
    width: 100%;
    height: 100%;
    object-fit: contain;
}

.controlsContainer {
    display: flex;
    justify-content: flex-end;
    padding: 0 1rem 1rem;
}

.contentLayer {
    position: relative;
    padding: 0.75rem;
}

.divider {
    border-top: 1px solid #E6ECF5;
    margin: 1rem 0;
}

.degreeLight {
    color: #00B300;
}

.degreeMedium {
    color: #FF7700;
}

.degreeSevere {
    color: #FF2D19;
}

.resultItemContainer {
    margin-bottom: 1rem;
}

.resultHeader {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.resultTitleContainer {
    display: flex;
    align-items: baseline;
}

.resultIndex {
    font-size: 1rem;
    color: #B6A8FF;
    margin-right: 0.5rem;
    font-weight: bold;
}

.resultTitle {
    font-size: 1rem;
    color: #111111;
    font-weight: bold;
}

.resultDegreeContainer {
    display: flex;
    align-items: baseline;
}

.resultDegree {
    font-size: 0.75rem;
}

.resultDegreeText {
    font-size: 0.75rem;
    color: #555555;
}

.degreeSeparator {
    font-size: 0.75rem;
    color: #555555;
    margin: 0 0.25rem;
}

.suggestionContainer {
    margin-bottom: 1rem;
}

.suggestionText {
    font-size: 0.75rem;
    color: #555555;
}

.solutionContainer {
    padding: 0.75rem;
    background: radial-gradient(27% 82% at 0% 0%, #F5F5FF 0%, rgba(248, 247, 255, 0) 100%, rgba(255, 255, 255, 0) 100%), radial-gradient(26% 88% at 98% 0%, #F5F7FC 0%, rgba(245, 247, 250, 0) 100%), linear-gradient(180deg, #F5F7FC 0%, rgba(245, 247, 250, 0) 10%);
    box-sizing: border-box;
    border: 1px solid #EEECFF;
    border-radius: 8px;
    margin-bottom: 1rem;
}

.solutionName {
    font-size: 1rem;
    color: #6D45E5;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.solutionDetails {
    display: flex;
    align-items: center;
    margin-bottom: 0.5rem;
}

.solutionPrice {
    font-size: 0.75rem;
    color: #FF2D19;
    font-weight: bold;
}

.solutionInfoSeparator {
    color: #888888;
    margin: 0 0.5rem;
    font-size: 0.5rem;
}

.solutionInfo {
    font-size: 0.75rem;
    color: #555555;
}

.shopCard {
    display: flex;
    flex-direction: column;
    cursor: pointer;
}

.shopCard:not(:last-child) {
    padding-bottom: 0.5rem;
}

.shopTopRow {
    display: flex;
    margin-bottom: 0.25rem;
}

.shopHeader {
    width: 5em;
    height: 5em;
    object-fit: cover;
    margin-right: 0.25rem;
    border-radius: 0.25rem;
}

.shopDetails {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.shopName {
    font-size: 1rem;
    color: #111111;
    font-weight: bold;
    margin-bottom: 0.25rem;
}

.shopRow2 {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.75rem;
    margin-bottom: 0.25rem;
}

.shopRow2Left, .shopRow2Right {
    display: flex;
    align-items: center;
}

.shopIcon {
    width: 0.75rem;
    height: 0.75rem;
    margin-right: 0.25rem;
}

.shopScore {
    color: #FF7700;
    margin-right: 0.25rem;
}

.shopCommentCount {
    color: #888888;
}

.shopArea {
    color: #888888;
    margin-right: 0.25rem;
}

.shopDistance {
    color: #888888;
}

.shopTags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.25rem;
    margin-bottom: 0.25rem;
    max-height: 1.5rem;
    overflow: hidden;
}

.shopTag {
    background-color: #F5F6F9;
    color: #555555;
    font-size: 0.75rem;
    padding: 0 0.25rem;
    border-radius: 0.125rem;
    height: 1rem;
    display: inline-flex;
    align-items: center;
}

.shopTagImage {
    height: 1rem;
    width: auto;
}

.shopDeals {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.dealItem {
    display: grid;
    grid-template-columns: 5rem 1fr auto;
    gap: 0.25rem;
    align-items: center;
    font-size: 0.75rem;
}

.dealPrice {
    color: #FF2D19;
    font-weight: bold;
    text-align: right;
}

.dealName {
    color: #111111;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.dealSales {
    color: #888888;
}

.finetunePanel {
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: white;
    z-index: 20;
    border-top-left-radius: 2rem;
    border-top-right-radius: 2rem;
}

.finetuneTitle {
    font-size: 1.25rem;
    color: black;
    text-align: center;
    padding-top: 1rem;
    padding-bottom: 1rem;
}

.finetuneContent {
    padding: 2rem;
    overflow-y: auto;
    max-height: calc(100vh - 200px); /* Adjust as needed */
}

.finetuneGrid {
    display: grid;
    grid-template-columns: auto 1fr;
    gap: 1.5rem 1rem;
    align-items: center;
}

.finetuneLabel {
    font-size: 1rem;
    color: #111111;
}

.finetuneControl {
    display: flex;
    align-items: center;
    justify-content: flex-start;
}

.sliderContainer {
    position: relative;
    width: 100%;
    display: flex;
    align-items: center;
    height: 1rem; /* Ensure container has height for thumb alignment */
}

.sliderTrack, .sliderProgress {
    position: absolute;
    height: 2px;
    top: 50%;
    transform: translateY(-50%);
    left: 0;
    border-radius: 1px;
}

.sliderTrack {
    background-color: #E1E7F0;
    width: 100%;
    z-index: 1;
}

.sliderProgress {
    background-color: #111;
    z-index: 2;
}
.slider:disabled {
    pointer-events: none;
}
.slider {
    -webkit-appearance: none;
    appearance: none;
    width: 100%;
    background: transparent;
    outline: none;
    margin: 0;
    position: relative;
    z-index: 4;
}

.slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 1rem;
    height: 1rem;
    background: white;
    border: 2px solid black;
    border-radius: 50%;
    cursor: pointer;
}

.slider::-moz-range-thumb {
    width: 1rem;
    height: 1rem;
    background: white;
    border: 2px solid black;
    border-radius: 50%;
    cursor: pointer;
}

.sliderNodes {
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    transform: translateY(-50%);
    display: flex;
    justify-content: space-between;
    pointer-events: none;
    z-index: 3;
}

.sliderNode {
    width: 8px;
    height: 8px;
    background-color: #E1E7F0;
    border: none;
    border-radius: 50%;
}

.activeNode {
    background-color: #111;
}

.switchContainer {
    width: 3rem;
    height: 1.5rem;
    background-color: #E5E7EB;
    border-radius: 9999px;
    position: relative;
    transition: background-color 0.2s ease;
    cursor: pointer;
}

.switchOn {
    background-color: #A78BFA; /* A shade of purple */
}

.switchDisabled {
    cursor: not-allowed;
    opacity: 0.7;
}

.switchCircle {
    position: absolute;
    top: 2px;
    left: 2px;
    width: 1.25rem;
    height: 1.25rem;
    background-color: white;
    border-radius: 50%;
    transition: transform 0.2s ease;
}

.switchOn .switchCircle {
    transform: translateX(1.5rem);
}

.toastMessage {
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    font-size: 1rem;
    padding: 0.75rem;
    border-radius: 0.5rem;
    z-index: 100;
    text-align: center;
    white-space: nowrap;
}
