'use client';

import React, {useEffect, useRef, useState} from 'react';
import ThreeScene, {ThreeSceneRef} from '@/components/ThreeScene';
import styles from './page.module.css';

interface Deal {
    dealName: string;
    sales: string;
    dealPrice: string;
    jumperUrl: string;
}

interface Tag {
    text?: string;
    imageUrl?: string;
}

interface Shop {
    shopName: string;
    reviewScore: string;
    area: string;
    distance: string;
    commentCount: string;
    header: string;
    jumperUrl: string;
    tags: Tag[];
    deals: Deal[];
}

interface Cases {
    avatars: string[];
    title: string;
    reason: string;
}

interface StyledText {
    text: string;
    style?: React.CSSProperties;
}

interface Solution {
    name: string;
    price: string;
    unit: string;
    duration: string;
    search: string;
    cases?: Cases;
    shop?: Shop[];
}

interface Result {
    name: string;
    degree: string;
    degreeDesc: string;
    suggestion: string;
    effect?: string | StyledText[];
    effect0?: string | StyledText[];
    effect1?: string | StyledText[];
    effect2?: string | StyledText[];
    effect3?: string | StyledText[];
    simuDegree: number;
    solutions: Solution[];
}

const SolutionItem = ({solution}: { solution: Solution }) => {
    return (
        <div>
            <div className={styles.solutionContainer}>
                <h4 className={styles.solutionName}>
                    {solution.name}
                </h4>
                <div className={styles.solutionDetails}>
                    <span className={styles.solutionPrice}>{solution.price}</span>
                    <span className={styles.solutionInfoSeparator}>|</span>
                    <span className={styles.solutionInfo}>{solution.unit}</span>
                    <span className={styles.solutionInfoSeparator}>|</span>
                    <span className={styles.solutionInfo}>{solution.duration}</span>
                </div>
                {solution.shop?.slice(0, 2).map((shop, shopIndex) => (
                    <div key={shopIndex} className={styles.shopCard} onClick={() => window.location.href = shop.jumperUrl}>
                        <div className={styles.shopTopRow}>
                            <img src={shop.header} alt={shop.shopName} className={styles.shopHeader}/>
                            <div className={styles.shopDetails}>
                                <div className={styles.shopName}>{shop.shopName}</div>
                                <div className={styles.shopRow2}>
                                    <div className={styles.shopRow2Left}>
                                        <img src="images/result_star.png" alt="星级" className={styles.shopIcon}/>
                                        <span className={styles.shopScore}>{shop.reviewScore}</span>
                                        <span className={styles.shopCommentCount}>{shop.commentCount}</span>
                                    </div>
                                    <div className={styles.shopRow2Right}>
                                        <img src="images/result_position.png" alt="地理位置" className={styles.shopIcon}/>
                                        <span className={styles.shopArea}>{shop.area}</span>
                                        <span className={styles.shopDistance}>{shop.distance}</span>
                                    </div>
                                </div>
                                <div className={styles.shopTags}>
                                    {shop.tags.slice(0, 2).map((tag, tagIndex) => (
                                        tag.imageUrl ? (
                                            <img key={tagIndex} src={tag.imageUrl} alt={tag.text} className={styles.shopTagImage}/>
                                        ) : (
                                            <span key={tagIndex} className={styles.shopTag}>{tag.text}</span>
                                        )
                                    ))}
                                </div>
                            </div>
                        </div>
                        <div className={styles.shopDeals}>
                            {shop.deals.map((deal, dealIndex) => (
                                <div key={dealIndex} className={styles.dealItem} onClick={(e) => {
                                    e.stopPropagation();
                                    window.location.href = deal.jumperUrl;
                                }}>
                                    <span className={styles.dealPrice}>{deal.dealPrice}</span>
                                    <span className={styles.dealName}>{deal.dealName}</span>
                                    <span className={styles.dealSales}>{deal.sales}</span>
                                </div>
                            ))}
                        </div>
                    </div>
                ))}
            </div>
        </div>
    );
};

const ResultItem = ({result, index, degreeClass, onRef, finetuneLevel}: {
    result: Result;
    index: number;
    degreeClass: { [key: string]: string; };
    onRef: (el: HTMLDivElement | null) => void;
    finetuneLevel?: number;
}) => {
    let effectText: string | StyledText[] | undefined;
    if (finetuneLevel !== undefined) {
        const effectKey = `effect${finetuneLevel}` as keyof Result;
        effectText = result[effectKey] as string | StyledText[] | undefined;
        if (effectText === undefined) {
            effectText = result.effect;
        }
    } else {
        effectText = result.effect;
    }

    const renderEffectText = (text: string | StyledText[] | undefined) => {
        if (!text) return null;
        if (typeof text === 'string') {
            return text;
        }

        return (
            <>
                {text.map((part, index) => (
                    <span key={index} style={part.style} className={styles.effectTag}>{part.text}</span>
                ))}
            </>
        );
    };

    return (
        <div ref={onRef} className={styles.resultItemContainer}>
            {/* First Row */}
            <div className={styles.resultHeader}>
                <div className={styles.resultTitleContainer}>
                    <span className={styles.resultIndex}>
                        {String(index + 1).padStart(2, '0')}
                    </span>
                    <span className={styles.resultTitle}>
                        {result.name}
                    </span>
                </div>
                <div className={styles.resultDegreeContainer}>
                    <span className={`${styles.resultDegree} ${styles[degreeClass[result.degree]]}`}>
                        ● {result.degree}
                    </span>
                    <span className={styles.degreeSeparator}> | </span>
                    <span className={styles.resultDegreeText}>
                        {result.degreeDesc}
                    </span>
                </div>
            </div>

            {/* Second Row */}
            <div className={styles.suggestionContainer}>
                <p className={styles.suggestionText}>
                    {result.suggestion}
                    <br/>
                    {renderEffectText(effectText)}
                </p>
            </div>

            {/* Third Part (Solutions) */}
            <div>
                {result.solutions.map((solution: Solution, solutionIndex: number) => (
                    <SolutionItem key={solutionIndex} solution={solution}/>
                ))}
            </div>
        </div>
    );
};

export default function Home() {
    const [selectedTab, setSelectedTab] = useState(0);
    const [isToggled, setIsToggled] = useState(true);
    const [results, setResults] = useState<Result[]>([]);
    const threeSceneRef = useRef<ThreeSceneRef>(null);
    const resultRefs = useRef<Array<HTMLDivElement | null>>([]);
    const [isFinetunePanelOpen, setIsFinetunePanelOpen] = useState(false);
    const [sliderValues, setSliderValues] = useState([3, 3, 3]);
    const analysisResultsRef = useRef<HTMLDivElement>(null);
    const [panelTop, setPanelTop] = useState<number | undefined>(undefined);
    const [isScrolled, setIsScrolled] = useState(false);
    const [showToast, setShowToast] = useState(false);
    const [toastMessage, setToastMessage] = useState('');
    const toastTimerRef = useRef<NodeJS.Timeout | null>(null);

    const handleToggleClick = () => {
        const newToggleState = !isToggled;
        setIsToggled(newToggleState);
        threeSceneRef.current?.toggleComparisonModel(!newToggleState);
    };

    const handleFinetuneClick = () => {
        setIsFinetunePanelOpen(prev => !prev);
    };

    const handleSliderChange = (index: number, value: string) => {
        const newValues = [...sliderValues];
        const intValue = parseInt(value, 10);
        newValues[index] = intValue;
        setSliderValues(newValues);
        if (index === 0) {
            // If the comparison toggle is off, turn it on when the user interacts with the slider.
            if (!isToggled) {
                setIsToggled(true);
                // We pass `false` to `toggleComparisonModel` to show the "after" (finetuned) model.
                threeSceneRef.current?.toggleComparisonModel(false);
            }
            threeSceneRef.current?.setFinetuneLevel(intValue);
        }
    };

    const showCustomToast = (message: string, timeout: number) => {
        if (toastTimerRef.current) {
            clearTimeout(toastTimerRef.current);
        }
        setToastMessage(message);
        setShowToast(true);
        toastTimerRef.current = setTimeout(() => {
            setShowToast(false);
        }, timeout);
    };

    const handleJumpToSolution = (index: number) => {
        const element = resultRefs.current[index];
        if (element) {
            const topPos = element.getBoundingClientRect().top + window.pageYOffset - 60;
            window.scrollTo({
                top: topPos,
                behavior: 'smooth'
            });
        }
    };

    useEffect(() => {
        fetch('results.json')
            .then(response => response.json())
            .then(data => setResults(data));

        const handleScroll = () => {
            setIsScrolled(window.scrollY > 10);
        };

        window.addEventListener('scroll', handleScroll);

        showCustomToast('体验版受算力影响暂时无法实时生成模型\n请先体验本组成员的真实模型吧～', 3000);

        return () => {
            window.removeEventListener('scroll', handleScroll);
        };
    }, []);

    useEffect(() => {
        if (isFinetunePanelOpen && analysisResultsRef.current) {
            setPanelTop(analysisResultsRef.current.offsetTop);
        }
    }, [isFinetunePanelOpen]);

    const degreeClass: { [key: string]: string } = {
        "轻度": "degreeLight",
        "中度": "degreeMedium",
        "重度": "degreeSevere",
    };

    const sliderItems = ['瘦咬肌', '填充太阳穴', '提亮肤色'];
    const switchItems = ['祛黑眼圈', '祛色斑'];

    return (
        <main className={styles.mainContainer} data-oid="q-tcxdb">
            <img src="images/result_bg2.png" alt="背景" className={styles.backgroundImage2}/>

            <img src="images/result_bg.png" alt="背景" className={styles.backgroundImage}/>
            <div className={`${styles.topBar} ${isScrolled ? styles.scrolled : ''}`} data-oid="xxt464q">
                <button className={styles.backButton} data-oid="-m0e10:" onClick={() => window.location.href = 'detail-customization.html'}>
                    <img src="images/back_light.png" alt="返回" className={styles.backButtonImage}/>
                </button>
                <h1 className={styles.title} data-oid="f4jnu-.">美学诊断</h1>
                <div className={styles.historyLink} data-oid="dzgfofe">历史报告</div>
            </div>
            <div className={styles.sceneContainer} data-oid="1y44pvp">
                <div className={styles.sceneWrapper} data-oid="27rz397">
                    <ThreeScene ref={threeSceneRef}/>
                </div>
                {showToast && (
                    <div className={styles.toastMessage}>
                        {toastMessage.split('\n').map((line, i) => (
                            <React.Fragment key={i}>
                                {line}
                                <br/>
                            </React.Fragment>
                        ))}
                    </div>
                )}
            </div>
            <div className={styles.controlsContainer}>
                <button className={styles.finetuneButton} onClick={handleFinetuneClick}>
                    <img src={isFinetunePanelOpen ? "images/result_finetune_active.png" : "images/result_finetune.png"} alt="效果微调"/>
                </button>
                <button className={styles.toggleButton} onClick={handleToggleClick} data-oid="toggle-button">
                    <img src={isToggled ? "images/result_diff_active.png" : "images/result_diff.png"} alt="效果对比"/>
                </button>
            </div>
            {/* Analysis Results */}
            <div ref={analysisResultsRef} className={styles.analysisResults} data-oid="g998.5q">

                {/* Content Layer */}
                <div className={styles.contentLayer}>
                    <div className={styles.titleContainer}>
                        <h2 className={styles.sectionTitle} data-oid="7-4ha.a">
                            面部特征分析
                        </h2>
                        <img src="images/result_title.png" alt="面部特征分析" className={styles.titleBg1}/>
                    </div>

                    <div className={styles.analysisContentBox} data-oid="zjjvn53">
                        <div data-oid="c:k4:aj">
                            <h3 className={styles.subSectionTitle} data-oid="h-6y.51">
                                这些无需改善
                            </h3>
                            <p className={styles.descriptionText} data-oid="4742fb6">
                                您的三庭五眼分布为黄金比例，五官很有辨识度，尤其是鼻子非常挺拔，给整个面部增添了立体感
                                <br/>
                                眼睛是气质大方的杏眼，眼距适中，十分耐看
                                <br/>
                                面部轮廓很柔和，是精致灵巧的江南小家碧玉脸，只需轻微调整流畅度，无需过度改善
                            </p>
                        </div>

                        <div className={styles.divider}/>

                        <div data-oid="fpcayym">
                            <h3 className={styles.subSectionTitle2} data-oid="pm5igv0">
                                这些可以关注
                            </h3>
                            <div className={styles.issuesContainer} data-oid="a5_9as7">
                                {results.map((item, index) => (
                                    <div key={index} className={styles.issueItem}>
                                        <div>
                                            <div style={{display: 'flex', alignItems: 'center'}}>
                                                <h4 className={styles.issueName}>{item.name}</h4>
                                                <button onClick={() => handleJumpToSolution(index)} className={styles.jumpButton}>
                                                    <img src="images/result_jump.png" alt="跳转至方案" className={styles.jumpButtonImage}/>
                                                </button>
                                            </div>
                                            <p className={styles.issueDegree}>
                                                <span className={styles[degreeClass[item.degree]]}>● {item.degree}</span>
                                                <span className={styles.issueDegreeText}> | {item.degreeDesc}</span>
                                            </p>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </div>
                    </div>
                    <div className={styles.titleContainer}>
                        <h2 className={styles.sectionTitle} data-oid="vwph0p:">
                            定制变美方案
                        </h2>
                        <img src="images/result_title.png" alt="定制变美方案" className={styles.titleBg2}/>
                    </div>

                    {/* Treatment Content */}
                    {results.map((result, resultIndex) => (
                        <ResultItem
                            key={resultIndex}
                            result={result}
                            index={resultIndex}
                            degreeClass={degreeClass}
                            onRef={el => {
                                resultRefs.current[resultIndex] = el;
                            }}
                            finetuneLevel={resultIndex <= 3 ? sliderValues[resultIndex] : undefined}
                        />
                    ))}
                </div>
            </div>

            {isFinetunePanelOpen && (
                <div className={styles.finetunePanel} style={{top: panelTop ? `${panelTop}px` : 'auto'}}>
                    <h2 className={styles.finetuneTitle}>效果微调</h2>
                    <div className={styles.finetuneContent}>
                        <div className={styles.finetuneGrid}>
                            {sliderItems.map((item, index) => {
                                const percentage = (sliderValues[index] / 3) * 100;
                                return (
                                    <React.Fragment key={item}>
                                        <div className={styles.finetuneLabel}>{item}</div>
                                        <div className={styles.finetuneControl} onClick={() => {
                                            if (index > 0) {
                                                showCustomToast('体验版暂时不支持调整哦～\n请先尝试调整“瘦下颌角”', 2000);
                                            }
                                        }}>
                                            <div className={styles.sliderContainer}>
                                                <div className={styles.sliderTrack}></div>
                                                <div className={styles.sliderProgress} style={{width: `${percentage}%`}}></div>
                                                <input
                                                    type="range"
                                                    min="0"
                                                    max="3"
                                                    step="1"
                                                    value={sliderValues[index]}
                                                    className={styles.slider}
                                                    onChange={(e) => handleSliderChange(index, e.target.value)}
                                                    disabled={index > 0}
                                                />
                                                <div className={styles.sliderNodes}>
                                                    {[...Array(4)].map((_, i) => <div key={i}
                                                                                      className={`${styles.sliderNode} ${sliderValues[index] >= i ? styles.activeNode : ''}`}></div>)}
                                                </div>
                                            </div>
                                        </div>
                                    </React.Fragment>
                                );
                            })}
                            {switchItems.map(item => (
                                <React.Fragment key={item}>
                                    <div className={styles.finetuneLabel}>{item}</div>
                                    <div className={styles.finetuneControl}>
                                        <div
                                            className={`${styles.switchContainer} ${styles.switchOn} ${styles.switchDisabled}`}
                                            onClick={() => showCustomToast('体验版暂时不支持调整哦～\n请先尝试调整“瘦下颌角”', 2000)}>
                                            <div className={styles.switchCircle}/>
                                        </div>
                                    </div>
                                </React.Fragment>
                            ))}
                        </div>
                    </div>
                </div>
            )}

        </main>
    );
}