import type {Metadata} from 'next';
import './globals.css';
import Script from 'next/script';

export const metadata: Metadata = {
    title: '美学诊断',
    description: 'Generated by create next app',
};

export default function RootLayout({ children }: Readonly<{ children: React.ReactNode }>) {
    return (
        <html lang="en" data-oid="b1jifow" suppressHydrationWarning>
            <body className="antialiased" data-oid="hqec2:a">
                {children}
                <Script
                    type="module"
                    strategy="afterInteractive"
                    src="onlook-preload-script.js"
                    data-oid="zxbuq9a"
                />
            </body>
        </html>
    );
}
