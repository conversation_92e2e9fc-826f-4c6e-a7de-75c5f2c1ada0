import React, {forwardRef, useEffect, useImperativeHandle, useRef, useState} from 'react';
import * as THREE from 'three';
import {GLTFLoader} from 'three/examples/jsm/loaders/GLTFLoader.js';
import {OrbitControls} from 'three/examples/jsm/controls/OrbitControls.js';
import {CSS2DObject, CSS2DRenderer} from 'three/examples/jsm/renderers/CSS2DRenderer.js';

export interface ThreeSceneRef {
    toggleComparisonModel: (showOriginal: boolean) => void;
    setFinetuneLevel: (level: number) => void;
}

const ThreeScene: React.ForwardRefRenderFunction<ThreeSceneRef, {}> = (props, ref) => {
    const mountRef = useRef<HTMLDivElement>(null);
    const modelsRef = useRef<{ [key: string]: THREE.Group }>({});
    const cameraRef = useRef<THREE.PerspectiveCamera>();
    const [isAutoRotating, setIsAutoRotating] = useState(true);

    const currentFinetuneLevel = useRef(3);
    const isShowingOriginal = useRef(false);

    const isAutoRotatingRef = useRef(isAutoRotating);
    isAutoRotatingRef.current = isAutoRotating;

    const updateModelVisibility = () => {
        Object.keys(modelsRef.current).forEach(key => {
            const model = modelsRef.current[key];
            if (isShowingOriginal.current) {
                model.visible = (key === 'original');
            } else {
                model.visible = (key === `finetune_${currentFinetuneLevel.current}`);
            }
        });
    };

    useImperativeHandle(ref, () => ({
        toggleComparisonModel: (showOriginal: boolean) => {
            isShowingOriginal.current = showOriginal;
            updateModelVisibility();
        },
        setFinetuneLevel: (level: number) => {
            currentFinetuneLevel.current = level;
            if (!isShowingOriginal.current) {
                updateModelVisibility();
            }
        }
    }));


    useEffect(() => {
        if (!mountRef.current) return;

        const currentMount = mountRef.current;

        // Scene setup
        const scene = new THREE.Scene();

        // Camera
        const camera = new THREE.PerspectiveCamera(50, currentMount.clientWidth / currentMount.clientHeight, 0.1, 1000);
        cameraRef.current = camera;

        // Renderer
        const renderer = new THREE.WebGLRenderer({antialias: true, alpha: true});
        renderer.setSize(currentMount.clientWidth, currentMount.clientHeight);
        renderer.setPixelRatio(window.devicePixelRatio);
        renderer.toneMapping = THREE.ACESFilmicToneMapping;
        renderer.toneMappingExposure = 1.0;
        renderer.outputColorSpace = THREE.SRGBColorSpace;
        currentMount.appendChild(renderer.domElement);

        // Label Renderer
        const labelRenderer = new CSS2DRenderer();
        labelRenderer.setSize(currentMount.clientWidth, currentMount.clientHeight);
        labelRenderer.domElement.style.position = 'absolute';
        labelRenderer.domElement.style.top = '0px';
        labelRenderer.domElement.style.pointerEvents = 'none';
        currentMount.appendChild(labelRenderer.domElement);

        // Lighting
        const ambientLight = new THREE.AmbientLight(0xffffff, 0.5);
        scene.add(ambientLight);
        const keyLight = new THREE.DirectionalLight(0xffffff, 0.8);
        keyLight.position.set(1.5, 3, 2);
        scene.add(keyLight);
        const fillLightLeft = new THREE.DirectionalLight(0xfff8ff, 1);
        fillLightLeft.position.set(-2, 2, 1.5);
        scene.add(fillLightLeft);
        const fillLightRight = new THREE.DirectionalLight(0xfffcf8, 0.8);
        fillLightRight.position.set(2, 1.5, 1.5);
        scene.add(fillLightRight);
        const frontLight = new THREE.DirectionalLight(0xffffff, 0.6);
        frontLight.position.set(0, 1, 3);
        scene.add(frontLight);
        const bounceLight = new THREE.DirectionalLight(0xfafafa, 0.3);
        bounceLight.position.set(0, -1.5, 1);
        scene.add(bounceLight);
        const hemisphereLight = new THREE.HemisphereLight(0xffffff, 0xfff8f8, 0.5);
        scene.add(hemisphereLight);

        // Controls
        const controls = new OrbitControls(camera, renderer.domElement);
        controls.enableDamping = true;
        controls.dampingFactor = 0.1;
        controls.minAzimuthAngle = -Math.PI / 9 - Math.PI / 9;
        controls.maxAzimuthAngle = Math.PI / 9 - Math.PI / 9;
        controls.minPolarAngle = Math.PI / 2 - Math.PI / 9;
        controls.maxPolarAngle = Math.PI / 2 + Math.PI / 9;

        const stopAutoRotation = () => {
            setIsAutoRotating(false);
            controls.removeEventListener('start', stopAutoRotation);
        };
        controls.addEventListener('start', stopAutoRotation);

        const setupModel = (modelScene: THREE.Group, name: string) => {
            const model = modelScene;
            scene.add(model);
            model.scale.set(1, 1, 1);
            model.position.set(0, 0, 0);
            model.rotation.set(0, -Math.PI / 9, 0)
            model.visible = false; // Initially hide all models
            modelsRef.current[name] = model;

            const faceMesh = model.getObjectByName('mesh');
            if (faceMesh) {
                const analysisPoints = [
                    {label: "肤色", position: new THREE.Vector3(0.04, 0.26, 0.50), pic: "images/positions/肤色.png", offset: "-2.4rem", height: "2rem"},
                    {label: "黑眼圈", position: new THREE.Vector3(0.15, -0.05, 0.51), pic: "images/positions/黑眼圈.png", offset: "-5.6rem", height: "2.25rem"},
                    {label: "太阳穴", position: new THREE.Vector3(0.31, 0.12, 0.33), pic: "images/positions/太阳穴.png", offset: "3.6rem", height: "2.75rem"},
                    {label: "色斑", position: new THREE.Vector3(0.3, -0.03, 0.41), pic: "images/positions/色斑.png", offset: "2.4rem", height: "2rem"},
                    {label: "咬肌", position: new THREE.Vector3(0.25, -0.38, 0.46), pic: "images/positions/咬肌.png", offset: "2.4rem", height: "2rem"},
                ];
                analysisPoints.forEach(point => {
                    const imageElement = document.createElement('img');
                    imageElement.src = point.pic;
                    imageElement.alt = point.label;
                    imageElement.style.pointerEvents = 'auto';
                    imageElement.style.height = point.height;
                    imageElement.style.width = 'auto';
                    imageElement.style.marginLeft = point.offset;
                    const label = new CSS2DObject(imageElement);
                    label.position.copy(point.position);
                    faceMesh.add(label);
                });
            }
            return model;
        };

        const loader = new GLTFLoader();
        let cameraZForAutoRotation: number;

        // 1. Load high-priority model first (myj_3.glb)
        loader.loadAsync('https://s3plus.meituan.net/merchant-dxw-video/myj_3.glb')
            .then(finetune3Gltf => {
                setupModel(finetune3Gltf.scene, 'finetune_3');

                // Set initial visibility for the first loaded model
                modelsRef.current['finetune_3'].visible = true;

                // Setup camera based on this first model
                const box = new THREE.Box3().setFromObject(modelsRef.current['finetune_3']);
                const size = box.getSize(new THREE.Vector3());
                const maxDim = Math.max(size.x, size.y, size.z);
                const fov = camera.fov * (Math.PI / 180);
                cameraZForAutoRotation = Math.abs(maxDim / 2 / Math.tan(fov / 2)) * 1.05;
                camera.position.set(0, size.y * 0.1, cameraZForAutoRotation);

                // 2. Lazy load other models.
                const lazyLoadUrls = {
                    'original': 'https://s3plus.meituan.net/merchant-dxw-video/myj.glb',
                    'finetune_0': 'https://s3plus.meituan.net/merchant-dxw-video/myj_0.glb',
                    'finetune_1': 'https://s3plus.meituan.net/merchant-dxw-video/myj_1.glb',
                    'finetune_2': 'https://s3plus.meituan.net/merchant-dxw-video/myj_2.glb'
                };

                Object.entries(lazyLoadUrls).forEach(([name, url]) => {
                    loader.loadAsync(url).then(gltf => {
                        setupModel(gltf.scene, name);
                        // After a lazy model loads, update visibility in case it's the active one
                        updateModelVisibility();
                    });
                });
            });

        // Animation loop
        const animate = () => {
            requestAnimationFrame(animate);

            if (isAutoRotatingRef.current && cameraZForAutoRotation) {
                const time = Date.now() * 0.0005;
                const angle = -Math.PI / 9 + (Math.PI / 9) * Math.sin(time);
                camera.position.x = cameraZForAutoRotation * Math.sin(angle);
                camera.position.z = cameraZForAutoRotation * Math.cos(angle);
            }

            controls.update();
            renderer.render(scene, camera);
            labelRenderer.render(scene, camera);
        };
        animate();

        // Coordinate picker logic
        const raycaster = new THREE.Raycaster();
        const mouse = new THREE.Vector2();

        const onDoubleClick = (event: MouseEvent) => {
            const bounds = currentMount.getBoundingClientRect();
            mouse.x = ((event.clientX - bounds.left) / bounds.width) * 2 - 1;
            mouse.y = -((event.clientY - bounds.top) / bounds.height) * 2 + 1;

            raycaster.setFromCamera(mouse, camera);

            const activeModelKey = isShowingOriginal.current ? 'original' : `finetune_${currentFinetuneLevel.current}`;
            const activeModel = modelsRef.current[activeModelKey];
            if (activeModel) {
                const faceMesh = activeModel.getObjectByName('mesh');
                if (faceMesh) {
                    const intersects = raycaster.intersectObject(faceMesh);
                    if (intersects.length > 0) {
                        const worldPoint = intersects[0].point;

                        // Convert world coordinates to local coordinates of the mesh
                        const localPoint = faceMesh.worldToLocal(worldPoint.clone());

                        console.log(`Clicked Local coordinates: ( ${localPoint.x.toFixed(2)}, ${localPoint.y.toFixed(2)}, ${localPoint.z.toFixed(2)} )`);
                    }
                }
            }
        };

        renderer.domElement.addEventListener('dblclick', onDoubleClick);

        // Handle resize
        const handleResize = () => {
            if (!currentMount) return;
            camera.aspect = currentMount.clientWidth / currentMount.clientHeight;
            camera.updateProjectionMatrix();
            renderer.setSize(currentMount.clientWidth, currentMount.clientHeight);
            labelRenderer.setSize(currentMount.clientWidth, currentMount.clientHeight);
        };
        window.addEventListener('resize', handleResize);

        // Cleanup
        return () => {
            window.removeEventListener('resize', handleResize);
            renderer.domElement.removeEventListener('dblclick', onDoubleClick);
            controls.removeEventListener('start', stopAutoRotation);
            currentMount.removeChild(renderer.domElement);
            currentMount.innerHTML = '';
        };
    }, []);

    return <div ref={mountRef} className="w-full h-full"/>;
};

export default forwardRef(ThreeScene);

