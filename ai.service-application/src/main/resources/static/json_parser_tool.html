<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>JSON提取工具</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Robot<PERSON>, "Helvetica Neue", <PERSON><PERSON>, sans-serif;
            margin: 0;
            padding: 1em;
            display: flex;
            flex-direction: column;
            height: 100vh;
            box-sizing: border-box;
        }
        .main-container {
            display: flex;
            flex-grow: 1;
            gap: 1em;
            height: calc(100% - 50px); /* Adjust based on button/header height */
        }
        .column {
            display: flex;
            flex-direction: column;
            flex: 1;
        }
        textarea {
            width: 100%;
            border: 1px solid #ccc;
            border-radius: 4px;
            padding: 8px;
            font-family: monospace;
            font-size: 13px;
            box-sizing: border-box;
        }
        #inputJson {
            flex-grow: 1;
        }
        #outputJson {
            flex-grow: 1; /* Fill available space */
        }
        button {
            padding: 10px 20px;
            font-size: 16px;
            cursor: pointer;
            margin-bottom: 1em;
            align-self: flex-start;
        }
        label {
            margin-bottom: 0.5em;
            font-weight: bold;
        }
    </style>
</head>
<body>

    <button id="parseBtn">解析</button>

    <div class="main-container">
        <div class="column">
            <label for="inputJson">输入</label>
            <textarea id="inputJson" rows="5" placeholder="在此处粘贴JSON..."></textarea>
        </div>
        <div class="column">
            <label for="outputJson">输出</label>
            <textarea id="outputJson" readonly placeholder="解析后的JSON将显示在此处..."></textarea>
        </div>
    </div>

    <script>
        function stripHtml(html) {
            if (!html) return "";
            const tempDiv = document.createElement("div");
            tempDiv.innerHTML = html;
            return tempDiv.textContent || tempDiv.innerText || "";
        }

        function parseAndTransform() {
            const inputJson = document.getElementById('inputJson').value;
            const outputJsonElement = document.getElementById('outputJson');

            if (!inputJson.trim()) {
                outputJsonElement.value = "输入为空。";
                return;
            }

            try {
                let parsedData = JSON.parse(inputJson);
                const dataArray = Array.isArray(parsedData) ? parsedData : [parsedData];

                const result = dataArray.map(item => {
                    const deals = (item.elements || []).map(deal => ({
                        dealName: deal.title ? deal.title.text : undefined,
                        sales: deal.sales,
                        dealPrice: deal.price ? stripHtml(deal.price.text) : undefined,
                        jumperUrl: deal.jumperUrl
                    }));

                    const tags = (item.descriptions || []).map(desc => {
                        if (desc.image && desc.image.url) {
                            return { imageUrl: desc.image.url };
                        }
                        if (desc.text) {
                            return { text: stripHtml(desc.text) };
                        }
                        return null;
                    }).filter(Boolean);

                    return {
                        shopName: item.title ? item.title.text : undefined,
                        reviewScore: item.reviewScore ? item.reviewScore.score : undefined,
                        area: item.area,
                        distance: item.distance,
                        commentCount: item.commentCount,
                        header: item.newHeaderImage ? item.newHeaderImage.url : undefined,
                        jumperUrl: item.jumperUrl,
                        tags: tags,
                        deals: deals
                    };
                });

                outputJsonElement.value = JSON.stringify(result.slice(2), null, 2);

            } catch (error) {
                outputJsonElement.value = "无效的JSON输入: " + error.message;
            }
        }

        document.getElementById('parseBtn').addEventListener('click', parseAndTransform);
    </script>

</body>
</html>

