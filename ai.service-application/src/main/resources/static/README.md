# AI聊天接口测试页面

这是一个用于测试AI聊天接口的前端页面，提供了两种接口的调用方式：

## 简单聊天接口

- 接口路径：`/api/chat/response`
- 请求方式：GET（使用SSE进行流式响应）
- 参数：`userInput` - 用户输入的文本

使用方法：
1. 在文本框中输入您的问题
2. 点击"发送"按钮
3. 等待响应在下方显示区域流式显示

## 高级聊天接口

- 接口路径：`/hospital/chat/question`
- 请求方式：POST（使用SSE进行流式响应）
- 请求体：JSON格式，包含完整的请求参数

使用方法：
1. 在JSON编辑框中编辑请求参数
2. 点击"发送"按钮
3. 等待响应在下方显示区域流式显示

## 请求参数说明

高级接口的请求参数示例：

\`\`\`json
{
  "requestId": "test-req-001",
  "sessionId": null,
  "content": "你好，请问你是谁？",
  "bizType": "HOSPITAL",
  "type": "TEXT",
  "role": "USER",
  "stream": true,
  "platform": "WEB",
  "basicParam": {
    "userId": "test-user-001",
    "clientType": "WEB",
    "appVersion": "1.0.0",
    "cityId": 1
  }
}
\`\`\`

参数说明：
- `requestId`：请求ID，用于标识请求
- `sessionId`：会话ID，首次对话为null，后续对话使用服务端返回的会话ID
- `content`：用户输入的内容
- `bizType`：业务类型
- `type`：消息类型
- `role`：角色，通常为"USER"
- `stream`：是否使用流式响应
- `platform`：平台
- `basicParam`：基本参数，包含用户信息

## 访问方式

启动应用后，访问以下URL：

- http://localhost:8080/ 或
- http://localhost:8080/chat

即可打开测试页面。