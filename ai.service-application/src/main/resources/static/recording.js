document.addEventListener('DOMContentLoaded', () => {
    // DOM元素
    const preview = document.getElementById('preview');
    const overlay = document.getElementById('overlay');
    const captureBtn = document.getElementById('captureBtn');
    const guideText = document.getElementById('guideText');
    const progressBar = document.getElementById('progressBar');
    const backButton = document.querySelector('.back-button');

    // 变量
    let stream = null;
    let mediaRecorder = null;
    let recordedChunks = [];
    let speechSynthesis = window.speechSynthesis;
    let currentGuideStep = 0;
    let isRecording = false;

    // 增强的引导步骤
    let guideSteps = [
        {
            text: "请将脸部对准圆圈",
            speech: "请将脸部对准圆圈，保持面部表情自然。"
        },
        {
            text: "请保持正面姿势，不要转动头部",
            speech: "很好，请保持正面姿势，面部放松，眼睛直视前方，不要转动头部。"
        },
        {
            text: "请缓慢地将头部向左转45度",
            speech: "现在，请缓慢地将头部向左转约45度，保持眼睛平视前方。"
        },
        {
            text: "请恢复正面姿势",
            speech: "请恢复正面姿势，面部放松，眼睛直视前方。"
        },
        {
            text: "请缓慢地将头部向右转45度",
            speech: "请缓慢地将头部向右转约45度，保持眼睛平视前方。"
        },
        {
            text: "请恢复正面姿势",
            speech: "请恢复正面姿势，面部放松，眼睛直视前方。"
        },
        {
            text: "请略微抬头，下巴抬起",
            speech: "请略微抬头，下巴抬起约30度，保持面部放松。"
        },
        {
            text: "请恢复正面姿势",
            speech: "请恢复正面姿势，面部放松，眼睛直视前方。"
        },
        {
            text: "请略微低头，下巴靠近胸部",
            speech: "请略微低头，下巴靠近胸部约30度，保持面部放松。"
        },
        {
            text: "请恢复正面姿势",
            speech: "请恢复正面姿势，面部放松，眼睛直视前方。"
        },
        {
            text: "拍摄完成，谢谢配合",
            speech: "非常好！拍摄已完成，感谢您的配合。"
        }
    ];

    // 阻止默认的触摸行为，防止页面缩放和滚动
    document.addEventListener('touchmove', function(e) {
        e.preventDefault();
    }, { passive: false });

    // 初始化摄像头
    async function initCamera() {
        try {
            // 尝试获取前置摄像头
            const constraints = {
                video: {
                    facingMode: "user",
                    width: { ideal: 1280 },
                    height: { ideal: 720 }
                },
                audio: true
            };

            // 在iOS上，我们需要先请求权限
            if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
                stream = await navigator.mediaDevices.getUserMedia(constraints);

                preview.srcObject = stream;
                // 设置镜像效果
                preview.style.transform = 'scaleX(-1)';
                overlay.style.transform = 'scaleX(-1)';

                guideText.textContent = "请打开声音，根据语音引导开始拍摄";

                // 初始化Canvas
                updateCanvasSize();

                // 等待视频元数据加载完成后更新canvas尺寸
                preview.onloadedmetadata = updateCanvasSize;

                // 添加面部检测提示
                drawFaceDetectionHint();
            } else {
                throw new Error("浏览器不支持getUserMedia API");
            }
        } catch (err) {
            console.error("摄像头访问失败:", err);
            guideText.textContent = "无法访问摄像头，请确保已授予权限";
        }
    }

    // 更新Canvas尺寸
    function updateCanvasSize() {
        const aspectRatio = preview.videoWidth / preview.videoHeight;

        if (window.innerWidth / window.innerHeight > aspectRatio) {
            // 如果屏幕比例更宽，则视频高度等于屏幕高度
            const newWidth = window.innerHeight * aspectRatio;
            overlay.width = newWidth;
            overlay.height = window.innerHeight;
        } else {
            // 如果屏幕比例更窄，则视频宽度等于屏幕宽度
            const newHeight = window.innerWidth / aspectRatio;
            overlay.width = window.innerWidth;
            overlay.height = newHeight;
        }

        // 重新绘制面部检测提示
        if (isRecording) {
            drawFaceDetectionHint();
        }
    }

    // 绘制面部检测提示
    function drawFaceDetectionHint() {
        // const ctx = overlay.getContext('2d');
        // ctx.clearRect(0, 0, overlay.width, overlay.height);
        //
        // // 根据当前步骤绘制不同的提示
        // if (currentGuideStep > 0 && currentGuideStep < guideSteps.length) {
        //     const step = currentGuideStep - 1;
        //
        //     // 绘制面部轮廓辅助线
        //     ctx.strokeStyle = 'rgba(255, 255, 255, 0.5)';
        //     ctx.lineWidth = 2;
        //
        //     const centerX = overlay.width / 2;
        //     const centerY = overlay.height / 2;
        //     const circleRadius = Math.min(overlay.width, overlay.height) * 0.3;
        //
        //     // 绘制水平和垂直辅助线
        //     ctx.beginPath();
        //     ctx.moveTo(centerX - circleRadius, centerY);
        //     ctx.lineTo(centerX + circleRadius, centerY);
        //     ctx.stroke();
        //
        //     ctx.beginPath();
        //     ctx.moveTo(centerX, centerY - circleRadius);
        //     ctx.lineTo(centerX, centerY + circleRadius);
        //     ctx.stroke();
        //
        //     // 根据步骤绘制不同的提示
        //     if (step === 2 || step === 4) { // 左右转头
        //         ctx.strokeStyle = 'rgba(106, 123, 255, 0.8)';
        //         ctx.beginPath();
        //         ctx.arc(centerX, centerY, circleRadius * 0.8, 0, Math.PI * 2);
        //         ctx.stroke();
        //
        //         // 绘制箭头
        //         const arrowLength = circleRadius * 0.5;
        //         const arrowAngle = step === 2 ? Math.PI : 0; // 左转或右转
        //
        //         ctx.beginPath();
        //         ctx.moveTo(centerX, centerY);
        //         ctx.lineTo(centerX + Math.cos(arrowAngle) * arrowLength, centerY + Math.sin(arrowAngle) * arrowLength);
        //         ctx.stroke();
        //     } else if (step === 6 || step === 8) { // 抬头或低头
        //         ctx.strokeStyle = 'rgba(106, 123, 255, 0.8)';
        //         ctx.beginPath();
        //         ctx.arc(centerX, centerY, circleRadius * 0.8, 0, Math.PI * 2);
        //         ctx.stroke();
        //
        //         // 绘制箭头
        //         const arrowLength = circleRadius * 0.5;
        //         const arrowAngle = step === 6 ? -Math.PI/2 : Math.PI/2; // 抬头或低头
        //
        //         ctx.beginPath();
        //         ctx.moveTo(centerX, centerY);
        //         ctx.lineTo(centerX + Math.cos(arrowAngle) * arrowLength, centerY + Math.sin(arrowAngle) * arrowLength);
        //         ctx.stroke();
        //     }
        // }
    }

    // 语音引导
    function speakGuide(text) {
        if (speechSynthesis) {
            // 停止当前正在播放的语音
            speechSynthesis.cancel();

            const utterance = new SpeechSynthesisUtterance(text);
            utterance.lang = 'zh-CN';
            utterance.rate = 0.9; // 稍微放慢语速
            utterance.pitch = 1.0;
            speechSynthesis.speak(utterance);
        }
    }

    // 开始引导流程
    function startGuideProcess() {
        currentGuideStep = 0;
        nextGuideStep();
    }

    // 进入下一个引导步骤
    function nextGuideStep() {
        if (currentGuideStep < guideSteps.length) {
            const step = guideSteps[currentGuideStep];
            guideText.textContent = step.text;
            speakGuide(step.speech);

            // 更新进度条
            progressBar.innerHTML = `已完成 <span>${currentGuideStep}</span> / ${guideSteps.length - 1}`;

            // 更新面部检测提示
            drawFaceDetectionHint();

            currentGuideStep++;

            // 根据步骤设置不同的等待时间
            let waitTime = 4000; // 默认4秒

            // 动作步骤需要更长时间
            if ([2, 4, 6, 8].includes(currentGuideStep - 1)) {
                waitTime = 6000; // 转头和抬低头给6秒
            } else if ([3, 5, 7, 9].includes(currentGuideStep - 1)) {
                waitTime = 3000; // 恢复正面姿势给3秒
            } else if (currentGuideStep - 1 === 10) {
                waitTime = 2000; // 最后一步给2秒
            }

            // 自动进入下一步
            if (currentGuideStep < guideSteps.length) {
                setTimeout(nextGuideStep, waitTime);
            } else {
                // 最后一步完成后
                setTimeout(() => {
                    if (isRecording) {
                        stopRecording();
                    }
                }, 3000);
            }
        }
    }

    // 开始录制
    function startRecording() {
        recordedChunks = [];
        isRecording = true;

        // 尝试使用不同的MIME类型
        let options;
        if (MediaRecorder.isTypeSupported('video/webm;codecs=vp9,opus')) {
            options = { mimeType: 'video/webm;codecs=vp9,opus' };
        } else if (MediaRecorder.isTypeSupported('video/webm;codecs=vp8,opus')) {
            options = { mimeType: 'video/webm;codecs=vp8,opus' };
        } else if (MediaRecorder.isTypeSupported('video/webm')) {
            options = { mimeType: 'video/webm' };
        } else if (MediaRecorder.isTypeSupported('video/mp4')) {
            options = { mimeType: 'video/mp4' };
        }

        try {
            mediaRecorder = new MediaRecorder(stream, options);
        } catch (e) {
            console.error('不支持指定的MIME类型:', e);
            try {
                mediaRecorder = new MediaRecorder(stream);
            } catch (e) {
                console.error('无法创建MediaRecorder:', e);
                guideText.textContent = "您的浏览器不支持录制功能";
                return;
            }
        }

        mediaRecorder.ondataavailable = (event) => {
            if (event.data && event.data.size > 0) {
                recordedChunks.push(event.data);
            }
        };

        mediaRecorder.onstop = () => {
            // const blob = new Blob(chunks, { type: 'video/webm' });
            // const url = URL.createObjectURL(blob);
            // const a = document.createElement('a');
            // a.href = url;
            // a.download = 'recording.webm';
            // a.click();
            // window.URL.revokeObjectURL(url);

            // 跳转到收集页
            window.location.href = 'detail-customization.html';
        };

        // 设置录制间隔，确保捕获足够的数据
        mediaRecorder.start(1000); // 每秒触发一次dataavailable事件
        captureBtn.classList.add('recording');
    }

    // 停止录制
    function stopRecording() {
        if (mediaRecorder && mediaRecorder.state !== "inactive") {
            mediaRecorder.stop();
            isRecording = false;
            captureBtn.classList.remove('recording');

            // 重置进度条
            progressBar.innerHTML = '';

            // 停止语音
            if (speechSynthesis) {
                speechSynthesis.cancel();
            }

            guideText.textContent = "视频已保存，点击按钮重新开始拍摄";
        }
    }

    // 处理拍摄按钮点击
    captureBtn.addEventListener('click', () => {
        if (!isRecording) {
            startRecording();
            startGuideProcess();
        } else {
            stopRecording();
        }
    });

    captureBtn.addEventListener('touchstart', function() {
        this.style.transform = 'scale(0.95)';
    });

    captureBtn.addEventListener('touchend', function() {
        this.style.transform = 'scale(1)';
    });

    backButton.addEventListener('click', () => {
        window.location.href = 'home.html';
    });

    // 页面加载时初始化摄像头
    initCamera();

    // 处理页面可见性变化
    document.addEventListener('visibilitychange', () => {
        if (document.visibilityState === 'hidden') {
            if (isRecording) {
                stopRecording();
            }
        }
    });

    // 处理窗口大小变化
    window.addEventListener('resize', updateCanvasSize);

    // 处理设备方向变化
    window.addEventListener('orientationchange', () => {
        // 等待方向变化完成后再更新尺寸
        setTimeout(updateCanvasSize, 300);
    });

    // 处理iOS上的全屏问题
    if (navigator.userAgent.match(/iPhone|iPad|iPod/i)) {
        window.addEventListener('touchend', () => {
            // 尝试进入全屏模式
            if (document.documentElement.requestFullscreen) {
                document.documentElement.requestFullscreen().catch(err => {
                    // 忽略错误，iOS通常不允许自动进入全屏
                });
            }
        }, { once: true });
    }
});
