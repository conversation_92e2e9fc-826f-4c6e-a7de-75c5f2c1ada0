body {
    background-color: #F0F0FC;
    margin: 0;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", Arial, sans-serif;
}

.app-container {
    position: relative;
    width: 100%;
    height: 100vh;
    display: flex;
    flex-direction: column;
}

.status-icons > div {
    margin-left: 5px;
    height: 16px;
}

.main-content {
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    padding: 2rem;
}

.main-content p {
    margin: 0;
    line-height: 3rem;
    width: 100%;
    text-align: left;
    justify-content: center;
    z-index: 1000;
    box-sizing: border-box;
}

.image-container {
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    padding: 2rem 0;
    width: 100%;
}

.line1 {
    font-weight: bold;
    color: #6D45E5;
    font-size: 2rem;
}

.line2 {
    font-weight: bold;
    font-size: 3rem;
    color: #000;
}

.line3 {
    font-weight: 300;
    font-size: 1.5rem;
    color: #000;
    letter-spacing: 0.25rem;
}

.logo {
    margin-top: 0;
    margin-bottom: 0;
    max-width: 100%;
}

.button-container {
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 15px;
    position: relative;
}

.start-tip {
    position: absolute;
    top: 0;
    right: 0;
    transform: translateY(-40%);
    z-index: 1;
    width: 40%;
}

.app-button {
    width: 100%;
    height: 48px;
    min-width: 208px;
    border-radius: 48px;
    font-size: 18px;
    font-weight: 500;
    cursor: pointer;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
}

#experience-now {
    color: white;
    background-color: #111111;
}

#see-example {
    color: #111111;
    background: rgba(0, 0, 0, 0.06);
}

.home-indicator {
    height: 5px;
    width: 40%;
    background-color: #000;
    border-radius: 3px;
    position: absolute;
    bottom: 8px;
    left: 50%;
    transform: translateX(-50%);
    opacity: 0.3;
}
