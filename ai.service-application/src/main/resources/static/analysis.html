<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI分析中</title>
    <link rel="stylesheet" href="analysis.css">
</head>
<body>
<div class="header">
    <div class="back-button">
        <img src="images/back_dark.png" alt="返回" class="back-button-image">
    </div>
</div>
<img src="images/loading.gif" alt="Loading..." class="loading-icon">
<div id="progress" class="progress-text">0%</div>
<div class="status-text">AI分析中...</div>
<div class="eta-text">预计还需10分钟，完成后会通知您</div>

<script>
    document.addEventListener('DOMContentLoaded', () => {
        const progressElement = document.getElementById('progress');
        let progress = 0;
        const intervalTime = 3000 / 10; // 2秒内完成，10%递进

        const interval = setInterval(() => {
            progress += 10;
            if (progress <= 100) {
                progressElement.textContent = `${progress}%`;
            } else {
                clearInterval(interval);
                window.location.href = 'result.html';
            }
        }, intervalTime);
    });

    const backButton = document.querySelector('.back-button');
    backButton.addEventListener('click', () => {
        window.location.href = 'home.html';
    });

</script>
</body>
</html>

