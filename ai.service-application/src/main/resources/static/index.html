<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI聊天接口测试</title>
    <style>
        body {
            font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            color: #333;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
        }
        .chat-section {
            flex: 1;
            min-width: 300px;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 20px;
        }
        h1 {
            color: #1890ff;
            text-align: center;
            margin-bottom: 30px;
        }
        h2 {
            color: #333;
            border-bottom: 1px solid #eee;
            padding-bottom: 10px;
            margin-top: 0;
        }
        .input-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="text"], textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            box-sizing: border-box;
            font-size: 14px;
        }
        textarea {
            min-height: 100px;
            resize: vertical;
        }
        button {
            background-color: #1890ff;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.3s;
        }
        button:hover {
            background-color: #40a9ff;
        }
        .response {
            margin-top: 20px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            padding: 15px;
            background-color: #fafafa;
            min-height: 200px;
            max-height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .json-editor {
            width: 100%;
            min-height: 200px;
            font-family: monospace;
            padding: 10px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            box-sizing: border-box;
        }
        .error {
            color: #f5222d;
            margin-top: 5px;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <h1>AI聊天接口测试</h1>
    
    <div class="container">
        <!-- 简单聊天接口 -->
        <div class="chat-section">
            <h2>简单聊天接口</h2>
            <div class="input-group">
                <label for="simple-input">用户输入:</label>
                <textarea id="simple-input" placeholder="请输入您的问题..."></textarea>
            </div>
            <button id="simple-submit">发送</button>
            <div class="response" id="simple-response">等待响应...</div>
        </div>
        
        <!-- 高级聊天接口 -->
        <div class="chat-section">
            <h2>高级聊天接口</h2>
            <div class="input-group">
                <label for="advanced-json">请求参数 (JSON):</label>
                <textarea id="advanced-json" class="json-editor">{
  "requestId": "test-req-001",
  "sessionId": null,
  "content": "你好，请问你是谁？",
  "bizType": "HOSPITAL",
  "type": "TEXT",
  "role": "USER",
  "stream": true,
  "platform": "WEB",
  "basicParam": {
    "userId": "test-user-001",
    "clientType": "WEB",
    "appVersion": "1.0.0",
    "cityId": 1
  }
}</textarea>
                <div id="json-error" class="error"></div>
            </div>
            <button id="advanced-submit">发送</button>
            <div class="response" id="advanced-response">等待响应...</div>
        </div>
    </div>

    <script>
        // 简单聊天接口
        document.getElementById('simple-submit').addEventListener('click', function() {
            const userInput = document.getElementById('simple-input').value.trim();
            const responseElement = document.getElementById('simple-response');
            
            if (!userInput) {
                responseElement.textContent = '请输入问题';
                return;
            }
            
            responseElement.textContent = '正在获取响应...';
            
            // 创建EventSource对象连接到SSE端点
            const url = `/api/chat/response?userInput=${encodeURIComponent(userInput)}`;
            const eventSource = new EventSource(url);
            
            let fullResponse = '';
            
            eventSource.onmessage = function(event) {
                fullResponse += event.data;
                responseElement.textContent = fullResponse;
                // 自动滚动到底部
                responseElement.scrollTop = responseElement.scrollHeight;
            };
            
            eventSource.onerror = function(error) {
                eventSource.close();
                if (fullResponse === '') {
                    responseElement.textContent = '获取响应时出错，请重试';
                } else {
                    responseElement.textContent = fullResponse + '\n\n[连接已关闭]';
                }
            };
        });
        
        // 高级聊天接口
        document.getElementById('advanced-submit').addEventListener('click', function() {
            const jsonInput = document.getElementById('advanced-json').value;
            const responseElement = document.getElementById('advanced-response');
            const errorElement = document.getElementById('json-error');
            
            // 验证JSON
            try {
                const requestData = JSON.parse(jsonInput);
                errorElement.textContent = '';
                
                responseElement.textContent = '正在获取响应...';
                
                // 创建POST请求
                fetch('/hospital/chat/question', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: jsonInput
                }).then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! Status: ${response.status}`);
                    }
                    
                    // 处理SSE响应
                    const reader = response.body.getReader();
                    const decoder = new TextDecoder();
                    let buffer = '';
                    
                    function processStream() {
                        return reader.read().then(({ done, value }) => {
                            if (done) {
                                return;
                            }
                            
                            buffer += decoder.decode(value, { stream: true });
                            
                            // 处理事件数据
                            const events = buffer.split('\n\n');
                            buffer = events.pop() || ''; // 保留最后一个不完整的事件
                            
                            for (const eventText of events) {
                                if (!eventText.trim()) continue;
                                
                                const lines = eventText.split('\n');
                                const eventData = {};
                                
                                for (const line of lines) {
                                    if (line.startsWith('data:')) {
                                        try {
                                            const data = JSON.parse(line.substring(5).trim());
                                            
                                            if (data.type === 'open') {
                                                responseElement.textContent = '连接已建立，等待响应...\n';
                                            } else if (data.type === 'message' && data.data && data.data.content) {
                                                responseElement.textContent += data.data.content;
                                            } else if (data.type === 'close') {
                                                responseElement.textContent += '\n\n[会话已结束]';
                                            } else if (data.type === 'server_error') {
                                                responseElement.textContent += '\n\n[错误] ' + (data.data?.content || '服务器错误');
                                            }
                                        } catch (e) {
                                            console.error('解析事件数据失败:', e, line);
                                        }
                                    }
                                }
                            }
                            
                            // 自动滚动到底部
                            responseElement.scrollTop = responseElement.scrollHeight;
                            
                            return processStream();
                        });
                    }
                    
                    return processStream();
                }).catch(error => {
                    responseElement.textContent = `请求失败: ${error.message}`;
                });
                
            } catch (e) {
                errorElement.textContent = 'JSON格式错误: ' + e.message;
            }
        });
    </script>
</body>
</html>