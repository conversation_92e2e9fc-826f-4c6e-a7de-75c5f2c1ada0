"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[699],{5416:(e,t,s)=>{s.d(t,{N:()=>p});var i=s(3264);let n={type:"change"},r={type:"start"},o={type:"end"},a=new i.RlV,l=new i.Zcv,h=Math.cos(70*i.cj9.DEG2RAD),c=new i.Pq0,u=2*Math.PI,d={NONE:-1,ROTATE:0,DOLLY:1,PAN:2,TOUCH_ROTATE:3,TOUCH_PAN:4,TOUCH_DOLLY_PAN:5,TOUCH_DOLLY_ROTATE:6};class p extends i.H2z{constructor(e,t=null){super(e,t),this.state=d.NONE,this.target=new i.Pq0,this.cursor=new i.Pq0,this.minDistance=0,this.maxDistance=1/0,this.minZoom=0,this.maxZoom=1/0,this.minTargetRadius=0,this.maxTargetRadius=1/0,this.minPolarAngle=0,this.maxPolarAngle=Math.PI,this.minAzimuthAngle=-1/0,this.maxAzimuthAngle=1/0,this.enableDamping=!1,this.dampingFactor=.05,this.enableZoom=!0,this.zoomSpeed=1,this.enableRotate=!0,this.rotateSpeed=1,this.keyRotateSpeed=1,this.enablePan=!0,this.panSpeed=1,this.screenSpacePanning=!0,this.keyPanSpeed=7,this.zoomToCursor=!1,this.autoRotate=!1,this.autoRotateSpeed=2,this.keys={LEFT:"ArrowLeft",UP:"ArrowUp",RIGHT:"ArrowRight",BOTTOM:"ArrowDown"},this.mouseButtons={LEFT:i.kBv.ROTATE,MIDDLE:i.kBv.DOLLY,RIGHT:i.kBv.PAN},this.touches={ONE:i.wtR.ROTATE,TWO:i.wtR.DOLLY_PAN},this.target0=this.target.clone(),this.position0=this.object.position.clone(),this.zoom0=this.object.zoom,this._domElementKeyEvents=null,this._lastPosition=new i.Pq0,this._lastQuaternion=new i.PTz,this._lastTargetPosition=new i.Pq0,this._quat=new i.PTz().setFromUnitVectors(e.up,new i.Pq0(0,1,0)),this._quatInverse=this._quat.clone().invert(),this._spherical=new i.YHV,this._sphericalDelta=new i.YHV,this._scale=1,this._panOffset=new i.Pq0,this._rotateStart=new i.I9Y,this._rotateEnd=new i.I9Y,this._rotateDelta=new i.I9Y,this._panStart=new i.I9Y,this._panEnd=new i.I9Y,this._panDelta=new i.I9Y,this._dollyStart=new i.I9Y,this._dollyEnd=new i.I9Y,this._dollyDelta=new i.I9Y,this._dollyDirection=new i.Pq0,this._mouse=new i.I9Y,this._performCursorZoom=!1,this._pointers=[],this._pointerPositions={},this._controlActive=!1,this._onPointerMove=f.bind(this),this._onPointerDown=m.bind(this),this._onPointerUp=_.bind(this),this._onContextMenu=v.bind(this),this._onMouseWheel=x.bind(this),this._onKeyDown=y.bind(this),this._onTouchStart=R.bind(this),this._onTouchMove=E.bind(this),this._onMouseDown=g.bind(this),this._onMouseMove=T.bind(this),this._interceptControlDown=b.bind(this),this._interceptControlUp=S.bind(this),null!==this.domElement&&this.connect(this.domElement),this.update()}connect(e){super.connect(e),this.domElement.addEventListener("pointerdown",this._onPointerDown),this.domElement.addEventListener("pointercancel",this._onPointerUp),this.domElement.addEventListener("contextmenu",this._onContextMenu),this.domElement.addEventListener("wheel",this._onMouseWheel,{passive:!1}),this.domElement.getRootNode().addEventListener("keydown",this._interceptControlDown,{passive:!0,capture:!0}),this.domElement.style.touchAction="none"}disconnect(){this.domElement.removeEventListener("pointerdown",this._onPointerDown),this.domElement.removeEventListener("pointermove",this._onPointerMove),this.domElement.removeEventListener("pointerup",this._onPointerUp),this.domElement.removeEventListener("pointercancel",this._onPointerUp),this.domElement.removeEventListener("wheel",this._onMouseWheel),this.domElement.removeEventListener("contextmenu",this._onContextMenu),this.stopListenToKeyEvents(),this.domElement.getRootNode().removeEventListener("keydown",this._interceptControlDown,{capture:!0}),this.domElement.style.touchAction="auto"}dispose(){this.disconnect()}getPolarAngle(){return this._spherical.phi}getAzimuthalAngle(){return this._spherical.theta}getDistance(){return this.object.position.distanceTo(this.target)}listenToKeyEvents(e){e.addEventListener("keydown",this._onKeyDown),this._domElementKeyEvents=e}stopListenToKeyEvents(){null!==this._domElementKeyEvents&&(this._domElementKeyEvents.removeEventListener("keydown",this._onKeyDown),this._domElementKeyEvents=null)}saveState(){this.target0.copy(this.target),this.position0.copy(this.object.position),this.zoom0=this.object.zoom}reset(){this.target.copy(this.target0),this.object.position.copy(this.position0),this.object.zoom=this.zoom0,this.object.updateProjectionMatrix(),this.dispatchEvent(n),this.update(),this.state=d.NONE}update(e=null){let t=this.object.position;c.copy(t).sub(this.target),c.applyQuaternion(this._quat),this._spherical.setFromVector3(c),this.autoRotate&&this.state===d.NONE&&this._rotateLeft(this._getAutoRotationAngle(e)),this.enableDamping?(this._spherical.theta+=this._sphericalDelta.theta*this.dampingFactor,this._spherical.phi+=this._sphericalDelta.phi*this.dampingFactor):(this._spherical.theta+=this._sphericalDelta.theta,this._spherical.phi+=this._sphericalDelta.phi);let s=this.minAzimuthAngle,r=this.maxAzimuthAngle;isFinite(s)&&isFinite(r)&&(s<-Math.PI?s+=u:s>Math.PI&&(s-=u),r<-Math.PI?r+=u:r>Math.PI&&(r-=u),s<=r?this._spherical.theta=Math.max(s,Math.min(r,this._spherical.theta)):this._spherical.theta=this._spherical.theta>(s+r)/2?Math.max(s,this._spherical.theta):Math.min(r,this._spherical.theta)),this._spherical.phi=Math.max(this.minPolarAngle,Math.min(this.maxPolarAngle,this._spherical.phi)),this._spherical.makeSafe(),!0===this.enableDamping?this.target.addScaledVector(this._panOffset,this.dampingFactor):this.target.add(this._panOffset),this.target.sub(this.cursor),this.target.clampLength(this.minTargetRadius,this.maxTargetRadius),this.target.add(this.cursor);let o=!1;if(this.zoomToCursor&&this._performCursorZoom||this.object.isOrthographicCamera)this._spherical.radius=this._clampDistance(this._spherical.radius);else{let e=this._spherical.radius;this._spherical.radius=this._clampDistance(this._spherical.radius*this._scale),o=e!=this._spherical.radius}if(c.setFromSpherical(this._spherical),c.applyQuaternion(this._quatInverse),t.copy(this.target).add(c),this.object.lookAt(this.target),!0===this.enableDamping?(this._sphericalDelta.theta*=1-this.dampingFactor,this._sphericalDelta.phi*=1-this.dampingFactor,this._panOffset.multiplyScalar(1-this.dampingFactor)):(this._sphericalDelta.set(0,0,0),this._panOffset.set(0,0,0)),this.zoomToCursor&&this._performCursorZoom){let e=null;if(this.object.isPerspectiveCamera){let t=c.length();e=this._clampDistance(t*this._scale);let s=t-e;this.object.position.addScaledVector(this._dollyDirection,s),this.object.updateMatrixWorld(),o=!!s}else if(this.object.isOrthographicCamera){let t=new i.Pq0(this._mouse.x,this._mouse.y,0);t.unproject(this.object);let s=this.object.zoom;this.object.zoom=Math.max(this.minZoom,Math.min(this.maxZoom,this.object.zoom/this._scale)),this.object.updateProjectionMatrix(),o=s!==this.object.zoom;let n=new i.Pq0(this._mouse.x,this._mouse.y,0);n.unproject(this.object),this.object.position.sub(n).add(t),this.object.updateMatrixWorld(),e=c.length()}else console.warn("WARNING: OrbitControls.js encountered an unknown camera type - zoom to cursor disabled."),this.zoomToCursor=!1;null!==e&&(this.screenSpacePanning?this.target.set(0,0,-1).transformDirection(this.object.matrix).multiplyScalar(e).add(this.object.position):(a.origin.copy(this.object.position),a.direction.set(0,0,-1).transformDirection(this.object.matrix),Math.abs(this.object.up.dot(a.direction))<h?this.object.lookAt(this.target):(l.setFromNormalAndCoplanarPoint(this.object.up,this.target),a.intersectPlane(l,this.target))))}else if(this.object.isOrthographicCamera){let e=this.object.zoom;this.object.zoom=Math.max(this.minZoom,Math.min(this.maxZoom,this.object.zoom/this._scale)),e!==this.object.zoom&&(this.object.updateProjectionMatrix(),o=!0)}return this._scale=1,this._performCursorZoom=!1,!!(o||this._lastPosition.distanceToSquared(this.object.position)>1e-6||8*(1-this._lastQuaternion.dot(this.object.quaternion))>1e-6||this._lastTargetPosition.distanceToSquared(this.target)>1e-6)&&(this.dispatchEvent(n),this._lastPosition.copy(this.object.position),this._lastQuaternion.copy(this.object.quaternion),this._lastTargetPosition.copy(this.target),!0)}_getAutoRotationAngle(e){return null!==e?u/60*this.autoRotateSpeed*e:u/60/60*this.autoRotateSpeed}_getZoomScale(e){let t=Math.abs(.01*e);return Math.pow(.95,this.zoomSpeed*t)}_rotateLeft(e){this._sphericalDelta.theta-=e}_rotateUp(e){this._sphericalDelta.phi-=e}_panLeft(e,t){c.setFromMatrixColumn(t,0),c.multiplyScalar(-e),this._panOffset.add(c)}_panUp(e,t){!0===this.screenSpacePanning?c.setFromMatrixColumn(t,1):(c.setFromMatrixColumn(t,0),c.crossVectors(this.object.up,c)),c.multiplyScalar(e),this._panOffset.add(c)}_pan(e,t){let s=this.domElement;if(this.object.isPerspectiveCamera){let i=this.object.position;c.copy(i).sub(this.target);let n=c.length();n*=Math.tan(this.object.fov/2*Math.PI/180),this._panLeft(2*e*n/s.clientHeight,this.object.matrix),this._panUp(2*t*n/s.clientHeight,this.object.matrix)}else this.object.isOrthographicCamera?(this._panLeft(e*(this.object.right-this.object.left)/this.object.zoom/s.clientWidth,this.object.matrix),this._panUp(t*(this.object.top-this.object.bottom)/this.object.zoom/s.clientHeight,this.object.matrix)):(console.warn("WARNING: OrbitControls.js encountered an unknown camera type - pan disabled."),this.enablePan=!1)}_dollyOut(e){this.object.isPerspectiveCamera||this.object.isOrthographicCamera?this._scale/=e:(console.warn("WARNING: OrbitControls.js encountered an unknown camera type - dolly/zoom disabled."),this.enableZoom=!1)}_dollyIn(e){this.object.isPerspectiveCamera||this.object.isOrthographicCamera?this._scale*=e:(console.warn("WARNING: OrbitControls.js encountered an unknown camera type - dolly/zoom disabled."),this.enableZoom=!1)}_updateZoomParameters(e,t){if(!this.zoomToCursor)return;this._performCursorZoom=!0;let s=this.domElement.getBoundingClientRect(),i=e-s.left,n=t-s.top,r=s.width,o=s.height;this._mouse.x=i/r*2-1,this._mouse.y=-(n/o*2)+1,this._dollyDirection.set(this._mouse.x,this._mouse.y,1).unproject(this.object).sub(this.object.position).normalize()}_clampDistance(e){return Math.max(this.minDistance,Math.min(this.maxDistance,e))}_handleMouseDownRotate(e){this._rotateStart.set(e.clientX,e.clientY)}_handleMouseDownDolly(e){this._updateZoomParameters(e.clientX,e.clientX),this._dollyStart.set(e.clientX,e.clientY)}_handleMouseDownPan(e){this._panStart.set(e.clientX,e.clientY)}_handleMouseMoveRotate(e){this._rotateEnd.set(e.clientX,e.clientY),this._rotateDelta.subVectors(this._rotateEnd,this._rotateStart).multiplyScalar(this.rotateSpeed);let t=this.domElement;this._rotateLeft(u*this._rotateDelta.x/t.clientHeight),this._rotateUp(u*this._rotateDelta.y/t.clientHeight),this._rotateStart.copy(this._rotateEnd),this.update()}_handleMouseMoveDolly(e){this._dollyEnd.set(e.clientX,e.clientY),this._dollyDelta.subVectors(this._dollyEnd,this._dollyStart),this._dollyDelta.y>0?this._dollyOut(this._getZoomScale(this._dollyDelta.y)):this._dollyDelta.y<0&&this._dollyIn(this._getZoomScale(this._dollyDelta.y)),this._dollyStart.copy(this._dollyEnd),this.update()}_handleMouseMovePan(e){this._panEnd.set(e.clientX,e.clientY),this._panDelta.subVectors(this._panEnd,this._panStart).multiplyScalar(this.panSpeed),this._pan(this._panDelta.x,this._panDelta.y),this._panStart.copy(this._panEnd),this.update()}_handleMouseWheel(e){this._updateZoomParameters(e.clientX,e.clientY),e.deltaY<0?this._dollyIn(this._getZoomScale(e.deltaY)):e.deltaY>0&&this._dollyOut(this._getZoomScale(e.deltaY)),this.update()}_handleKeyDown(e){let t=!1;switch(e.code){case this.keys.UP:e.ctrlKey||e.metaKey||e.shiftKey?this.enableRotate&&this._rotateUp(u*this.keyRotateSpeed/this.domElement.clientHeight):this.enablePan&&this._pan(0,this.keyPanSpeed),t=!0;break;case this.keys.BOTTOM:e.ctrlKey||e.metaKey||e.shiftKey?this.enableRotate&&this._rotateUp(-u*this.keyRotateSpeed/this.domElement.clientHeight):this.enablePan&&this._pan(0,-this.keyPanSpeed),t=!0;break;case this.keys.LEFT:e.ctrlKey||e.metaKey||e.shiftKey?this.enableRotate&&this._rotateLeft(u*this.keyRotateSpeed/this.domElement.clientHeight):this.enablePan&&this._pan(this.keyPanSpeed,0),t=!0;break;case this.keys.RIGHT:e.ctrlKey||e.metaKey||e.shiftKey?this.enableRotate&&this._rotateLeft(-u*this.keyRotateSpeed/this.domElement.clientHeight):this.enablePan&&this._pan(-this.keyPanSpeed,0),t=!0}t&&(e.preventDefault(),this.update())}_handleTouchStartRotate(e){if(1===this._pointers.length)this._rotateStart.set(e.pageX,e.pageY);else{let t=this._getSecondPointerPosition(e),s=.5*(e.pageX+t.x),i=.5*(e.pageY+t.y);this._rotateStart.set(s,i)}}_handleTouchStartPan(e){if(1===this._pointers.length)this._panStart.set(e.pageX,e.pageY);else{let t=this._getSecondPointerPosition(e),s=.5*(e.pageX+t.x),i=.5*(e.pageY+t.y);this._panStart.set(s,i)}}_handleTouchStartDolly(e){let t=this._getSecondPointerPosition(e),s=e.pageX-t.x,i=e.pageY-t.y,n=Math.sqrt(s*s+i*i);this._dollyStart.set(0,n)}_handleTouchStartDollyPan(e){this.enableZoom&&this._handleTouchStartDolly(e),this.enablePan&&this._handleTouchStartPan(e)}_handleTouchStartDollyRotate(e){this.enableZoom&&this._handleTouchStartDolly(e),this.enableRotate&&this._handleTouchStartRotate(e)}_handleTouchMoveRotate(e){if(1==this._pointers.length)this._rotateEnd.set(e.pageX,e.pageY);else{let t=this._getSecondPointerPosition(e),s=.5*(e.pageX+t.x),i=.5*(e.pageY+t.y);this._rotateEnd.set(s,i)}this._rotateDelta.subVectors(this._rotateEnd,this._rotateStart).multiplyScalar(this.rotateSpeed);let t=this.domElement;this._rotateLeft(u*this._rotateDelta.x/t.clientHeight),this._rotateUp(u*this._rotateDelta.y/t.clientHeight),this._rotateStart.copy(this._rotateEnd)}_handleTouchMovePan(e){if(1===this._pointers.length)this._panEnd.set(e.pageX,e.pageY);else{let t=this._getSecondPointerPosition(e),s=.5*(e.pageX+t.x),i=.5*(e.pageY+t.y);this._panEnd.set(s,i)}this._panDelta.subVectors(this._panEnd,this._panStart).multiplyScalar(this.panSpeed),this._pan(this._panDelta.x,this._panDelta.y),this._panStart.copy(this._panEnd)}_handleTouchMoveDolly(e){let t=this._getSecondPointerPosition(e),s=e.pageX-t.x,i=e.pageY-t.y,n=Math.sqrt(s*s+i*i);this._dollyEnd.set(0,n),this._dollyDelta.set(0,Math.pow(this._dollyEnd.y/this._dollyStart.y,this.zoomSpeed)),this._dollyOut(this._dollyDelta.y),this._dollyStart.copy(this._dollyEnd);let r=(e.pageX+t.x)*.5,o=(e.pageY+t.y)*.5;this._updateZoomParameters(r,o)}_handleTouchMoveDollyPan(e){this.enableZoom&&this._handleTouchMoveDolly(e),this.enablePan&&this._handleTouchMovePan(e)}_handleTouchMoveDollyRotate(e){this.enableZoom&&this._handleTouchMoveDolly(e),this.enableRotate&&this._handleTouchMoveRotate(e)}_addPointer(e){this._pointers.push(e.pointerId)}_removePointer(e){delete this._pointerPositions[e.pointerId];for(let t=0;t<this._pointers.length;t++)if(this._pointers[t]==e.pointerId)return void this._pointers.splice(t,1)}_isTrackingPointer(e){for(let t=0;t<this._pointers.length;t++)if(this._pointers[t]==e.pointerId)return!0;return!1}_trackPointer(e){let t=this._pointerPositions[e.pointerId];void 0===t&&(t=new i.I9Y,this._pointerPositions[e.pointerId]=t),t.set(e.pageX,e.pageY)}_getSecondPointerPosition(e){let t=e.pointerId===this._pointers[0]?this._pointers[1]:this._pointers[0];return this._pointerPositions[t]}_customWheelEvent(e){let t=e.deltaMode,s={clientX:e.clientX,clientY:e.clientY,deltaY:e.deltaY};switch(t){case 1:s.deltaY*=16;break;case 2:s.deltaY*=100}return e.ctrlKey&&!this._controlActive&&(s.deltaY*=10),s}}function m(e){!1!==this.enabled&&(0===this._pointers.length&&(this.domElement.setPointerCapture(e.pointerId),this.domElement.addEventListener("pointermove",this._onPointerMove),this.domElement.addEventListener("pointerup",this._onPointerUp)),this._isTrackingPointer(e)||(this._addPointer(e),"touch"===e.pointerType?this._onTouchStart(e):this._onMouseDown(e)))}function f(e){!1!==this.enabled&&("touch"===e.pointerType?this._onTouchMove(e):this._onMouseMove(e))}function _(e){switch(this._removePointer(e),this._pointers.length){case 0:this.domElement.releasePointerCapture(e.pointerId),this.domElement.removeEventListener("pointermove",this._onPointerMove),this.domElement.removeEventListener("pointerup",this._onPointerUp),this.dispatchEvent(o),this.state=d.NONE;break;case 1:let t=this._pointers[0],s=this._pointerPositions[t];this._onTouchStart({pointerId:t,pageX:s.x,pageY:s.y})}}function g(e){let t;switch(e.button){case 0:t=this.mouseButtons.LEFT;break;case 1:t=this.mouseButtons.MIDDLE;break;case 2:t=this.mouseButtons.RIGHT;break;default:t=-1}switch(t){case i.kBv.DOLLY:if(!1===this.enableZoom)return;this._handleMouseDownDolly(e),this.state=d.DOLLY;break;case i.kBv.ROTATE:if(e.ctrlKey||e.metaKey||e.shiftKey){if(!1===this.enablePan)return;this._handleMouseDownPan(e),this.state=d.PAN}else{if(!1===this.enableRotate)return;this._handleMouseDownRotate(e),this.state=d.ROTATE}break;case i.kBv.PAN:if(e.ctrlKey||e.metaKey||e.shiftKey){if(!1===this.enableRotate)return;this._handleMouseDownRotate(e),this.state=d.ROTATE}else{if(!1===this.enablePan)return;this._handleMouseDownPan(e),this.state=d.PAN}break;default:this.state=d.NONE}this.state!==d.NONE&&this.dispatchEvent(r)}function T(e){switch(this.state){case d.ROTATE:if(!1===this.enableRotate)return;this._handleMouseMoveRotate(e);break;case d.DOLLY:if(!1===this.enableZoom)return;this._handleMouseMoveDolly(e);break;case d.PAN:if(!1===this.enablePan)return;this._handleMouseMovePan(e)}}function x(e){!1!==this.enabled&&!1!==this.enableZoom&&this.state===d.NONE&&(e.preventDefault(),this.dispatchEvent(r),this._handleMouseWheel(this._customWheelEvent(e)),this.dispatchEvent(o))}function y(e){!1!==this.enabled&&this._handleKeyDown(e)}function R(e){switch(this._trackPointer(e),this._pointers.length){case 1:switch(this.touches.ONE){case i.wtR.ROTATE:if(!1===this.enableRotate)return;this._handleTouchStartRotate(e),this.state=d.TOUCH_ROTATE;break;case i.wtR.PAN:if(!1===this.enablePan)return;this._handleTouchStartPan(e),this.state=d.TOUCH_PAN;break;default:this.state=d.NONE}break;case 2:switch(this.touches.TWO){case i.wtR.DOLLY_PAN:if(!1===this.enableZoom&&!1===this.enablePan)return;this._handleTouchStartDollyPan(e),this.state=d.TOUCH_DOLLY_PAN;break;case i.wtR.DOLLY_ROTATE:if(!1===this.enableZoom&&!1===this.enableRotate)return;this._handleTouchStartDollyRotate(e),this.state=d.TOUCH_DOLLY_ROTATE;break;default:this.state=d.NONE}break;default:this.state=d.NONE}this.state!==d.NONE&&this.dispatchEvent(r)}function E(e){switch(this._trackPointer(e),this.state){case d.TOUCH_ROTATE:if(!1===this.enableRotate)return;this._handleTouchMoveRotate(e),this.update();break;case d.TOUCH_PAN:if(!1===this.enablePan)return;this._handleTouchMovePan(e),this.update();break;case d.TOUCH_DOLLY_PAN:if(!1===this.enableZoom&&!1===this.enablePan)return;this._handleTouchMoveDollyPan(e),this.update();break;case d.TOUCH_DOLLY_ROTATE:if(!1===this.enableZoom&&!1===this.enableRotate)return;this._handleTouchMoveDollyRotate(e),this.update();break;default:this.state=d.NONE}}function v(e){!1!==this.enabled&&e.preventDefault()}function b(e){"Control"===e.key&&(this._controlActive=!0,this.domElement.getRootNode().addEventListener("keyup",this._interceptControlUp,{passive:!0,capture:!0}))}function S(e){"Control"===e.key&&(this._controlActive=!1,this.domElement.getRootNode().removeEventListener("keyup",this._interceptControlUp,{passive:!0,capture:!0}))}},8512:(e,t,s)=>{s.d(t,{B:()=>c,v:()=>n});var i=s(3264);class n extends i.B69{constructor(e=document.createElement("div")){super(),this.isCSS2DObject=!0,this.element=e,this.element.style.position="absolute",this.element.style.userSelect="none",this.element.setAttribute("draggable",!1),this.center=new i.I9Y(.5,.5),this.addEventListener("removed",function(){this.traverse(function(e){e.element instanceof e.element.ownerDocument.defaultView.Element&&null!==e.element.parentNode&&e.element.remove()})})}copy(e,t){return super.copy(e,t),this.element=e.element.cloneNode(!0),this.center=e.center,this}}let r=new i.Pq0,o=new i.kn4,a=new i.kn4,l=new i.Pq0,h=new i.Pq0;class c{constructor(e={}){let t,s,i,n,c=this,u={objects:new WeakMap},d=void 0!==e.element?e.element:document.createElement("div");d.style.overflow="hidden",this.domElement=d,this.getSize=function(){return{width:t,height:s}},this.render=function(e,t){!0===e.matrixWorldAutoUpdate&&e.updateMatrixWorld(),null===t.parent&&!0===t.matrixWorldAutoUpdate&&t.updateMatrixWorld(),o.copy(t.matrixWorldInverse),a.multiplyMatrices(t.projectionMatrix,o),function e(t,s,o){if(!1===t.visible)return void function e(t){t.isCSS2DObject&&(t.element.style.display="none");for(let s=0,i=t.children.length;s<i;s++)e(t.children[s])}(t);if(t.isCSS2DObject){var p,m;r.setFromMatrixPosition(t.matrixWorld),r.applyMatrix4(a);let e=r.z>=-1&&r.z<=1&&!0===t.layers.test(o.layers),f=t.element;f.style.display=!0===e?"":"none",!0===e&&(t.onBeforeRender(c,s,o),f.style.transform="translate("+-100*t.center.x+"%,"+-100*t.center.y+"%)translate("+(r.x*i+i)+"px,"+(-r.y*n+n)+"px)",f.parentNode!==d&&d.appendChild(f),t.onAfterRender(c,s,o));let _={distanceToCameraSquared:(p=o,m=t,l.setFromMatrixPosition(p.matrixWorld),h.setFromMatrixPosition(m.matrixWorld),l.distanceToSquared(h))};u.objects.set(t,_)}for(let i=0,n=t.children.length;i<n;i++)e(t.children[i],s,o)}(e,e,t),function(e){let t=(function(e){let t=[];return e.traverseVisible(function(e){e.isCSS2DObject&&t.push(e)}),t})(e).sort(function(e,t){return e.renderOrder!==t.renderOrder?t.renderOrder-e.renderOrder:u.objects.get(e).distanceToCameraSquared-u.objects.get(t).distanceToCameraSquared}),s=t.length;for(let e=0,i=t.length;e<i;e++)t[e].element.style.zIndex=s-e}(e)},this.setSize=function(e,r){t=e,s=r,i=t/2,n=s/2,d.style.width=e+"px",d.style.height=r+"px"}}}},9311:(e,t,s)=>{s.d(t,{B:()=>r});var i=s(3264);function n(e,t){if(t===i.RJ4)return console.warn("THREE.BufferGeometryUtils.toTrianglesDrawMode(): Geometry already defined as triangles."),e;if(t!==i.rYR&&t!==i.O49)return console.error("THREE.BufferGeometryUtils.toTrianglesDrawMode(): Unknown draw mode:",t),e;{let s=e.getIndex();if(null===s){let t=[],i=e.getAttribute("position");if(void 0===i)return console.error("THREE.BufferGeometryUtils.toTrianglesDrawMode(): Undefined position attribute. Processing not possible."),e;for(let e=0;e<i.count;e++)t.push(e);e.setIndex(t),s=e.getIndex()}let n=s.count-2,r=[];if(t===i.rYR)for(let e=1;e<=n;e++)r.push(s.getX(0)),r.push(s.getX(e)),r.push(s.getX(e+1));else for(let e=0;e<n;e++)e%2==0?(r.push(s.getX(e)),r.push(s.getX(e+1)),r.push(s.getX(e+2))):(r.push(s.getX(e+2)),r.push(s.getX(e+1)),r.push(s.getX(e)));r.length/3!==n&&console.error("THREE.BufferGeometryUtils.toTrianglesDrawMode(): Unable to generate correct amount of triangles.");let o=e.clone();return o.setIndex(r),o.clearGroups(),o}}class r extends i.aHM{constructor(e){super(e),this.dracoLoader=null,this.ktx2Loader=null,this.meshoptDecoder=null,this.pluginCallbacks=[],this.register(function(e){return new u(e)}),this.register(function(e){return new d(e)}),this.register(function(e){return new R(e)}),this.register(function(e){return new E(e)}),this.register(function(e){return new v(e)}),this.register(function(e){return new m(e)}),this.register(function(e){return new f(e)}),this.register(function(e){return new _(e)}),this.register(function(e){return new g(e)}),this.register(function(e){return new c(e)}),this.register(function(e){return new T(e)}),this.register(function(e){return new p(e)}),this.register(function(e){return new y(e)}),this.register(function(e){return new x(e)}),this.register(function(e){return new l(e)}),this.register(function(e){return new b(e)}),this.register(function(e){return new S(e)})}load(e,t,s,n){let r,o=this;if(""!==this.resourcePath)r=this.resourcePath;else if(""!==this.path){let t=i.r6x.extractUrlBase(e);r=i.r6x.resolveURL(t,this.path)}else r=i.r6x.extractUrlBase(e);this.manager.itemStart(e);let a=function(t){n?n(t):console.error(t),o.manager.itemError(e),o.manager.itemEnd(e)},l=new i.Y9S(this.manager);l.setPath(this.path),l.setResponseType("arraybuffer"),l.setRequestHeader(this.requestHeader),l.setWithCredentials(this.withCredentials),l.load(e,function(s){try{o.parse(s,r,function(s){t(s),o.manager.itemEnd(e)},a)}catch(e){a(e)}},s,a)}setDRACOLoader(e){return this.dracoLoader=e,this}setKTX2Loader(e){return this.ktx2Loader=e,this}setMeshoptDecoder(e){return this.meshoptDecoder=e,this}register(e){return -1===this.pluginCallbacks.indexOf(e)&&this.pluginCallbacks.push(e),this}unregister(e){return -1!==this.pluginCallbacks.indexOf(e)&&this.pluginCallbacks.splice(this.pluginCallbacks.indexOf(e),1),this}parse(e,t,s,i){let n,r={},o={},l=new TextDecoder;if("string"==typeof e)n=JSON.parse(e);else if(e instanceof ArrayBuffer)if(l.decode(new Uint8Array(e,0,4))===w){try{r[a.KHR_BINARY_GLTF]=new A(e)}catch(e){i&&i(e);return}n=JSON.parse(r[a.KHR_BINARY_GLTF].content)}else n=JSON.parse(l.decode(e));else n=e;if(void 0===n.asset||n.asset.version[0]<2){i&&i(Error("THREE.GLTFLoader: Unsupported asset. glTF versions >=2.0 are supported."));return}let c=new Z(n,{path:t||this.resourcePath||"",crossOrigin:this.crossOrigin,requestHeader:this.requestHeader,manager:this.manager,ktx2Loader:this.ktx2Loader,meshoptDecoder:this.meshoptDecoder});c.fileLoader.setRequestHeader(this.requestHeader);for(let e=0;e<this.pluginCallbacks.length;e++){let t=this.pluginCallbacks[e](c);t.name||console.error("THREE.GLTFLoader: Invalid plugin found: missing name"),o[t.name]=t,r[t.name]=!0}if(n.extensionsUsed)for(let e=0;e<n.extensionsUsed.length;++e){let t=n.extensionsUsed[e],s=n.extensionsRequired||[];switch(t){case a.KHR_MATERIALS_UNLIT:r[t]=new h;break;case a.KHR_DRACO_MESH_COMPRESSION:r[t]=new M(n,this.dracoLoader);break;case a.KHR_TEXTURE_TRANSFORM:r[t]=new L;break;case a.KHR_MESH_QUANTIZATION:r[t]=new O;break;default:s.indexOf(t)>=0&&void 0===o[t]&&console.warn('THREE.GLTFLoader: Unknown extension "'+t+'".')}}c.setExtensions(r),c.setPlugins(o),c.parse(s,i)}parseAsync(e,t){let s=this;return new Promise(function(i,n){s.parse(e,t,i,n)})}}function o(){let e={};return{get:function(t){return e[t]},add:function(t,s){e[t]=s},remove:function(t){delete e[t]},removeAll:function(){e={}}}}let a={KHR_BINARY_GLTF:"KHR_binary_glTF",KHR_DRACO_MESH_COMPRESSION:"KHR_draco_mesh_compression",KHR_LIGHTS_PUNCTUAL:"KHR_lights_punctual",KHR_MATERIALS_CLEARCOAT:"KHR_materials_clearcoat",KHR_MATERIALS_DISPERSION:"KHR_materials_dispersion",KHR_MATERIALS_IOR:"KHR_materials_ior",KHR_MATERIALS_SHEEN:"KHR_materials_sheen",KHR_MATERIALS_SPECULAR:"KHR_materials_specular",KHR_MATERIALS_TRANSMISSION:"KHR_materials_transmission",KHR_MATERIALS_IRIDESCENCE:"KHR_materials_iridescence",KHR_MATERIALS_ANISOTROPY:"KHR_materials_anisotropy",KHR_MATERIALS_UNLIT:"KHR_materials_unlit",KHR_MATERIALS_VOLUME:"KHR_materials_volume",KHR_TEXTURE_BASISU:"KHR_texture_basisu",KHR_TEXTURE_TRANSFORM:"KHR_texture_transform",KHR_MESH_QUANTIZATION:"KHR_mesh_quantization",KHR_MATERIALS_EMISSIVE_STRENGTH:"KHR_materials_emissive_strength",EXT_MATERIALS_BUMP:"EXT_materials_bump",EXT_TEXTURE_WEBP:"EXT_texture_webp",EXT_TEXTURE_AVIF:"EXT_texture_avif",EXT_MESHOPT_COMPRESSION:"EXT_meshopt_compression",EXT_MESH_GPU_INSTANCING:"EXT_mesh_gpu_instancing"};class l{constructor(e){this.parser=e,this.name=a.KHR_LIGHTS_PUNCTUAL,this.cache={refs:{},uses:{}}}_markDefs(){let e=this.parser,t=this.parser.json.nodes||[];for(let s=0,i=t.length;s<i;s++){let i=t[s];i.extensions&&i.extensions[this.name]&&void 0!==i.extensions[this.name].light&&e._addNodeRef(this.cache,i.extensions[this.name].light)}}_loadLight(e){let t,s=this.parser,n="light:"+e,r=s.cache.get(n);if(r)return r;let o=s.json,a=((o.extensions&&o.extensions[this.name]||{}).lights||[])[e],l=new i.Q1f(0xffffff);void 0!==a.color&&l.setRGB(a.color[0],a.color[1],a.color[2],i.Zr2);let h=void 0!==a.range?a.range:0;switch(a.type){case"directional":(t=new i.ZyN(l)).target.position.set(0,0,-1),t.add(t.target);break;case"point":(t=new i.HiM(l)).distance=h;break;case"spot":(t=new i.nCl(l)).distance=h,a.spot=a.spot||{},a.spot.innerConeAngle=void 0!==a.spot.innerConeAngle?a.spot.innerConeAngle:0,a.spot.outerConeAngle=void 0!==a.spot.outerConeAngle?a.spot.outerConeAngle:Math.PI/4,t.angle=a.spot.outerConeAngle,t.penumbra=1-a.spot.innerConeAngle/a.spot.outerConeAngle,t.target.position.set(0,0,-1),t.add(t.target);break;default:throw Error("THREE.GLTFLoader: Unexpected light type: "+a.type)}return t.position.set(0,0,0),z(t,a),void 0!==a.intensity&&(t.intensity=a.intensity),t.name=s.createUniqueName(a.name||"light_"+e),r=Promise.resolve(t),s.cache.add(n,r),r}getDependency(e,t){if("light"===e)return this._loadLight(t)}createNodeAttachment(e){let t=this,s=this.parser,i=s.json.nodes[e],n=(i.extensions&&i.extensions[this.name]||{}).light;return void 0===n?null:this._loadLight(n).then(function(e){return s._getNodeRef(t.cache,n,e)})}}class h{constructor(){this.name=a.KHR_MATERIALS_UNLIT}getMaterialType(){return i.V9B}extendParams(e,t,s){let n=[];e.color=new i.Q1f(1,1,1),e.opacity=1;let r=t.pbrMetallicRoughness;if(r){if(Array.isArray(r.baseColorFactor)){let t=r.baseColorFactor;e.color.setRGB(t[0],t[1],t[2],i.Zr2),e.opacity=t[3]}void 0!==r.baseColorTexture&&n.push(s.assignTexture(e,"map",r.baseColorTexture,i.er$))}return Promise.all(n)}}class c{constructor(e){this.parser=e,this.name=a.KHR_MATERIALS_EMISSIVE_STRENGTH}extendMaterialParams(e,t){let s=this.parser.json.materials[e];if(!s.extensions||!s.extensions[this.name])return Promise.resolve();let i=s.extensions[this.name].emissiveStrength;return void 0!==i&&(t.emissiveIntensity=i),Promise.resolve()}}class u{constructor(e){this.parser=e,this.name=a.KHR_MATERIALS_CLEARCOAT}getMaterialType(e){let t=this.parser.json.materials[e];return t.extensions&&t.extensions[this.name]?i.uSd:null}extendMaterialParams(e,t){let s=this.parser,n=s.json.materials[e];if(!n.extensions||!n.extensions[this.name])return Promise.resolve();let r=[],o=n.extensions[this.name];if(void 0!==o.clearcoatFactor&&(t.clearcoat=o.clearcoatFactor),void 0!==o.clearcoatTexture&&r.push(s.assignTexture(t,"clearcoatMap",o.clearcoatTexture)),void 0!==o.clearcoatRoughnessFactor&&(t.clearcoatRoughness=o.clearcoatRoughnessFactor),void 0!==o.clearcoatRoughnessTexture&&r.push(s.assignTexture(t,"clearcoatRoughnessMap",o.clearcoatRoughnessTexture)),void 0!==o.clearcoatNormalTexture&&(r.push(s.assignTexture(t,"clearcoatNormalMap",o.clearcoatNormalTexture)),void 0!==o.clearcoatNormalTexture.scale)){let e=o.clearcoatNormalTexture.scale;t.clearcoatNormalScale=new i.I9Y(e,e)}return Promise.all(r)}}class d{constructor(e){this.parser=e,this.name=a.KHR_MATERIALS_DISPERSION}getMaterialType(e){let t=this.parser.json.materials[e];return t.extensions&&t.extensions[this.name]?i.uSd:null}extendMaterialParams(e,t){let s=this.parser.json.materials[e];if(!s.extensions||!s.extensions[this.name])return Promise.resolve();let i=s.extensions[this.name];return t.dispersion=void 0!==i.dispersion?i.dispersion:0,Promise.resolve()}}class p{constructor(e){this.parser=e,this.name=a.KHR_MATERIALS_IRIDESCENCE}getMaterialType(e){let t=this.parser.json.materials[e];return t.extensions&&t.extensions[this.name]?i.uSd:null}extendMaterialParams(e,t){let s=this.parser,i=s.json.materials[e];if(!i.extensions||!i.extensions[this.name])return Promise.resolve();let n=[],r=i.extensions[this.name];return void 0!==r.iridescenceFactor&&(t.iridescence=r.iridescenceFactor),void 0!==r.iridescenceTexture&&n.push(s.assignTexture(t,"iridescenceMap",r.iridescenceTexture)),void 0!==r.iridescenceIor&&(t.iridescenceIOR=r.iridescenceIor),void 0===t.iridescenceThicknessRange&&(t.iridescenceThicknessRange=[100,400]),void 0!==r.iridescenceThicknessMinimum&&(t.iridescenceThicknessRange[0]=r.iridescenceThicknessMinimum),void 0!==r.iridescenceThicknessMaximum&&(t.iridescenceThicknessRange[1]=r.iridescenceThicknessMaximum),void 0!==r.iridescenceThicknessTexture&&n.push(s.assignTexture(t,"iridescenceThicknessMap",r.iridescenceThicknessTexture)),Promise.all(n)}}class m{constructor(e){this.parser=e,this.name=a.KHR_MATERIALS_SHEEN}getMaterialType(e){let t=this.parser.json.materials[e];return t.extensions&&t.extensions[this.name]?i.uSd:null}extendMaterialParams(e,t){let s=this.parser,n=s.json.materials[e];if(!n.extensions||!n.extensions[this.name])return Promise.resolve();let r=[];t.sheenColor=new i.Q1f(0,0,0),t.sheenRoughness=0,t.sheen=1;let o=n.extensions[this.name];if(void 0!==o.sheenColorFactor){let e=o.sheenColorFactor;t.sheenColor.setRGB(e[0],e[1],e[2],i.Zr2)}return void 0!==o.sheenRoughnessFactor&&(t.sheenRoughness=o.sheenRoughnessFactor),void 0!==o.sheenColorTexture&&r.push(s.assignTexture(t,"sheenColorMap",o.sheenColorTexture,i.er$)),void 0!==o.sheenRoughnessTexture&&r.push(s.assignTexture(t,"sheenRoughnessMap",o.sheenRoughnessTexture)),Promise.all(r)}}class f{constructor(e){this.parser=e,this.name=a.KHR_MATERIALS_TRANSMISSION}getMaterialType(e){let t=this.parser.json.materials[e];return t.extensions&&t.extensions[this.name]?i.uSd:null}extendMaterialParams(e,t){let s=this.parser,i=s.json.materials[e];if(!i.extensions||!i.extensions[this.name])return Promise.resolve();let n=[],r=i.extensions[this.name];return void 0!==r.transmissionFactor&&(t.transmission=r.transmissionFactor),void 0!==r.transmissionTexture&&n.push(s.assignTexture(t,"transmissionMap",r.transmissionTexture)),Promise.all(n)}}class _{constructor(e){this.parser=e,this.name=a.KHR_MATERIALS_VOLUME}getMaterialType(e){let t=this.parser.json.materials[e];return t.extensions&&t.extensions[this.name]?i.uSd:null}extendMaterialParams(e,t){let s=this.parser,n=s.json.materials[e];if(!n.extensions||!n.extensions[this.name])return Promise.resolve();let r=[],o=n.extensions[this.name];t.thickness=void 0!==o.thicknessFactor?o.thicknessFactor:0,void 0!==o.thicknessTexture&&r.push(s.assignTexture(t,"thicknessMap",o.thicknessTexture)),t.attenuationDistance=o.attenuationDistance||1/0;let a=o.attenuationColor||[1,1,1];return t.attenuationColor=new i.Q1f().setRGB(a[0],a[1],a[2],i.Zr2),Promise.all(r)}}class g{constructor(e){this.parser=e,this.name=a.KHR_MATERIALS_IOR}getMaterialType(e){let t=this.parser.json.materials[e];return t.extensions&&t.extensions[this.name]?i.uSd:null}extendMaterialParams(e,t){let s=this.parser.json.materials[e];if(!s.extensions||!s.extensions[this.name])return Promise.resolve();let i=s.extensions[this.name];return t.ior=void 0!==i.ior?i.ior:1.5,Promise.resolve()}}class T{constructor(e){this.parser=e,this.name=a.KHR_MATERIALS_SPECULAR}getMaterialType(e){let t=this.parser.json.materials[e];return t.extensions&&t.extensions[this.name]?i.uSd:null}extendMaterialParams(e,t){let s=this.parser,n=s.json.materials[e];if(!n.extensions||!n.extensions[this.name])return Promise.resolve();let r=[],o=n.extensions[this.name];t.specularIntensity=void 0!==o.specularFactor?o.specularFactor:1,void 0!==o.specularTexture&&r.push(s.assignTexture(t,"specularIntensityMap",o.specularTexture));let a=o.specularColorFactor||[1,1,1];return t.specularColor=new i.Q1f().setRGB(a[0],a[1],a[2],i.Zr2),void 0!==o.specularColorTexture&&r.push(s.assignTexture(t,"specularColorMap",o.specularColorTexture,i.er$)),Promise.all(r)}}class x{constructor(e){this.parser=e,this.name=a.EXT_MATERIALS_BUMP}getMaterialType(e){let t=this.parser.json.materials[e];return t.extensions&&t.extensions[this.name]?i.uSd:null}extendMaterialParams(e,t){let s=this.parser,i=s.json.materials[e];if(!i.extensions||!i.extensions[this.name])return Promise.resolve();let n=[],r=i.extensions[this.name];return t.bumpScale=void 0!==r.bumpFactor?r.bumpFactor:1,void 0!==r.bumpTexture&&n.push(s.assignTexture(t,"bumpMap",r.bumpTexture)),Promise.all(n)}}class y{constructor(e){this.parser=e,this.name=a.KHR_MATERIALS_ANISOTROPY}getMaterialType(e){let t=this.parser.json.materials[e];return t.extensions&&t.extensions[this.name]?i.uSd:null}extendMaterialParams(e,t){let s=this.parser,i=s.json.materials[e];if(!i.extensions||!i.extensions[this.name])return Promise.resolve();let n=[],r=i.extensions[this.name];return void 0!==r.anisotropyStrength&&(t.anisotropy=r.anisotropyStrength),void 0!==r.anisotropyRotation&&(t.anisotropyRotation=r.anisotropyRotation),void 0!==r.anisotropyTexture&&n.push(s.assignTexture(t,"anisotropyMap",r.anisotropyTexture)),Promise.all(n)}}class R{constructor(e){this.parser=e,this.name=a.KHR_TEXTURE_BASISU}loadTexture(e){let t=this.parser,s=t.json,i=s.textures[e];if(!i.extensions||!i.extensions[this.name])return null;let n=i.extensions[this.name],r=t.options.ktx2Loader;if(!r)if(!(s.extensionsRequired&&s.extensionsRequired.indexOf(this.name)>=0))return null;else throw Error("THREE.GLTFLoader: setKTX2Loader must be called before loading KTX2 textures");return t.loadTextureImage(e,n.source,r)}}class E{constructor(e){this.parser=e,this.name=a.EXT_TEXTURE_WEBP}loadTexture(e){let t=this.name,s=this.parser,i=s.json,n=i.textures[e];if(!n.extensions||!n.extensions[t])return null;let r=n.extensions[t],o=i.images[r.source],a=s.textureLoader;if(o.uri){let e=s.options.manager.getHandler(o.uri);null!==e&&(a=e)}return s.loadTextureImage(e,r.source,a)}}class v{constructor(e){this.parser=e,this.name=a.EXT_TEXTURE_AVIF}loadTexture(e){let t=this.name,s=this.parser,i=s.json,n=i.textures[e];if(!n.extensions||!n.extensions[t])return null;let r=n.extensions[t],o=i.images[r.source],a=s.textureLoader;if(o.uri){let e=s.options.manager.getHandler(o.uri);null!==e&&(a=e)}return s.loadTextureImage(e,r.source,a)}}class b{constructor(e){this.name=a.EXT_MESHOPT_COMPRESSION,this.parser=e}loadBufferView(e){let t=this.parser.json,s=t.bufferViews[e];if(!s.extensions||!s.extensions[this.name])return null;{let e=s.extensions[this.name],i=this.parser.getDependency("buffer",e.buffer),n=this.parser.options.meshoptDecoder;if(!n||!n.supported)if(!(t.extensionsRequired&&t.extensionsRequired.indexOf(this.name)>=0))return null;else throw Error("THREE.GLTFLoader: setMeshoptDecoder must be called before loading compressed files");return i.then(function(t){let s=e.byteOffset||0,i=e.byteLength||0,r=e.count,o=e.byteStride,a=new Uint8Array(t,s,i);return n.decodeGltfBufferAsync?n.decodeGltfBufferAsync(r,o,a,e.mode,e.filter).then(function(e){return e.buffer}):n.ready.then(function(){let t=new ArrayBuffer(r*o);return n.decodeGltfBuffer(new Uint8Array(t),r,o,a,e.mode,e.filter),t})})}}}class S{constructor(e){this.name=a.EXT_MESH_GPU_INSTANCING,this.parser=e}createNodeMesh(e){let t=this.parser.json,s=t.nodes[e];if(!s.extensions||!s.extensions[this.name]||void 0===s.mesh)return null;for(let e of t.meshes[s.mesh].primitives)if(e.mode!==D.TRIANGLES&&e.mode!==D.TRIANGLE_STRIP&&e.mode!==D.TRIANGLE_FAN&&void 0!==e.mode)return null;let n=s.extensions[this.name].attributes,r=[],o={};for(let e in n)r.push(this.parser.getDependency("accessor",n[e]).then(t=>(o[e]=t,o[e])));return r.length<1?null:(r.push(this.parser.createNodeMesh(e)),Promise.all(r).then(e=>{let t=e.pop(),s=t.isGroup?t.children:[t],n=e[0].count,r=[];for(let e of s){let t=new i.kn4,s=new i.Pq0,a=new i.PTz,l=new i.Pq0(1,1,1),h=new i.ZLX(e.geometry,e.material,n);for(let e=0;e<n;e++)o.TRANSLATION&&s.fromBufferAttribute(o.TRANSLATION,e),o.ROTATION&&a.fromBufferAttribute(o.ROTATION,e),o.SCALE&&l.fromBufferAttribute(o.SCALE,e),h.setMatrixAt(e,t.compose(s,a,l));for(let t in o)if("_COLOR_0"===t){let e=o[t];h.instanceColor=new i.uWO(e.array,e.itemSize,e.normalized)}else"TRANSLATION"!==t&&"ROTATION"!==t&&"SCALE"!==t&&e.geometry.setAttribute(t,o[t]);i.B69.prototype.copy.call(h,e),this.parser.assignFinalMaterial(h),r.push(h)}return t.isGroup?(t.clear(),t.add(...r),t):r[0]}))}}let w="glTF",P={JSON:0x4e4f534a,BIN:5130562};class A{constructor(e){this.name=a.KHR_BINARY_GLTF,this.content=null,this.body=null;let t=new DataView(e,0,12),s=new TextDecoder;if(this.header={magic:s.decode(new Uint8Array(e.slice(0,4))),version:t.getUint32(4,!0),length:t.getUint32(8,!0)},this.header.magic!==w)throw Error("THREE.GLTFLoader: Unsupported glTF-Binary header.");if(this.header.version<2)throw Error("THREE.GLTFLoader: Legacy binary file detected.");let i=this.header.length-12,n=new DataView(e,12),r=0;for(;r<i;){let t=n.getUint32(r,!0);r+=4;let i=n.getUint32(r,!0);if(r+=4,i===P.JSON){let i=new Uint8Array(e,12+r,t);this.content=s.decode(i)}else if(i===P.BIN){let s=12+r;this.body=e.slice(s,s+t)}r+=t}if(null===this.content)throw Error("THREE.GLTFLoader: JSON content not found.")}}class M{constructor(e,t){if(!t)throw Error("THREE.GLTFLoader: No DRACOLoader instance provided.");this.name=a.KHR_DRACO_MESH_COMPRESSION,this.json=e,this.dracoLoader=t,this.dracoLoader.preload()}decodePrimitive(e,t){let s=this.json,n=this.dracoLoader,r=e.extensions[this.name].bufferView,o=e.extensions[this.name].attributes,a={},l={},h={};for(let e in o)a[F[e]||e.toLowerCase()]=o[e];for(let t in e.attributes){let i=F[t]||t.toLowerCase();if(void 0!==o[t]){let n=s.accessors[e.attributes[t]],r=k[n.componentType];h[i]=r.name,l[i]=!0===n.normalized}}return t.getDependency("bufferView",r).then(function(e){return new Promise(function(t,s){n.decodeDracoFile(e,function(e){for(let t in e.attributes){let s=e.attributes[t],i=l[t];void 0!==i&&(s.normalized=i)}t(e)},a,h,i.Zr2,s)})})}}class L{constructor(){this.name=a.KHR_TEXTURE_TRANSFORM}extendTexture(e,t){return(void 0===t.texCoord||t.texCoord===e.channel)&&void 0===t.offset&&void 0===t.rotation&&void 0===t.scale||(e=e.clone(),void 0!==t.texCoord&&(e.channel=t.texCoord),void 0!==t.offset&&e.offset.fromArray(t.offset),void 0!==t.rotation&&(e.rotation=t.rotation),void 0!==t.scale&&e.repeat.fromArray(t.scale),e.needsUpdate=!0),e}}class O{constructor(){this.name=a.KHR_MESH_QUANTIZATION}}class I extends i.lGw{constructor(e,t,s,i){super(e,t,s,i)}copySampleValue_(e){let t=this.resultBuffer,s=this.sampleValues,i=this.valueSize,n=e*i*3+i;for(let e=0;e!==i;e++)t[e]=s[n+e];return t}interpolate_(e,t,s,i){let n=this.resultBuffer,r=this.sampleValues,o=this.valueSize,a=2*o,l=3*o,h=i-t,c=(s-t)/h,u=c*c,d=u*c,p=e*l,m=p-l,f=-2*d+3*u,_=d-u,g=1-f,T=_-u+c;for(let e=0;e!==o;e++){let t=r[m+e+o],s=r[m+e+a]*h,i=r[p+e+o],l=r[p+e]*h;n[e]=g*t+T*s+f*i+_*l}return n}}let N=new i.PTz;class C extends I{interpolate_(e,t,s,i){let n=super.interpolate_(e,t,s,i);return N.fromArray(n).normalize().toArray(n),n}}let D={POINTS:0,LINES:1,LINE_LOOP:2,LINE_STRIP:3,TRIANGLES:4,TRIANGLE_STRIP:5,TRIANGLE_FAN:6},k={5120:Int8Array,5121:Uint8Array,5122:Int16Array,5123:Uint16Array,5125:Uint32Array,5126:Float32Array},H={9728:i.hxR,9729:i.k6q,9984:i.pHI,9985:i.kRr,9986:i.Cfg,9987:i.$_I},j={33071:i.ghU,33648:i.kTW,10497:i.GJx},U={SCALAR:1,VEC2:2,VEC3:3,VEC4:4,MAT2:4,MAT3:9,MAT4:16},F={POSITION:"position",NORMAL:"normal",TANGENT:"tangent",TEXCOORD_0:"uv",TEXCOORD_1:"uv1",TEXCOORD_2:"uv2",TEXCOORD_3:"uv3",COLOR_0:"color",WEIGHTS_0:"skinWeight",JOINTS_0:"skinIndex"},K={scale:"scale",translation:"position",rotation:"quaternion",weights:"morphTargetInfluences"},B={CUBICSPLINE:void 0,LINEAR:i.PJ3,STEP:i.ljd},G={OPAQUE:"OPAQUE",MASK:"MASK",BLEND:"BLEND"};function Y(e,t,s){for(let i in s.extensions)void 0===e[i]&&(t.userData.gltfExtensions=t.userData.gltfExtensions||{},t.userData.gltfExtensions[i]=s.extensions[i])}function z(e,t){void 0!==t.extras&&("object"==typeof t.extras?Object.assign(e.userData,t.extras):console.warn("THREE.GLTFLoader: Ignoring primitive type .extras, "+t.extras))}function X(e){let t="",s=Object.keys(e).sort();for(let i=0,n=s.length;i<n;i++)t+=s[i]+":"+e[s[i]]+";";return t}function V(e){switch(e){case Int8Array:return 1/127;case Uint8Array:return 1/255;case Int16Array:return 1/32767;case Uint16Array:return 1/65535;default:throw Error("THREE.GLTFLoader: Unsupported normalized accessor component type.")}}let q=new i.kn4;class Z{constructor(e={},t={}){this.json=e,this.extensions={},this.plugins={},this.options=t,this.cache=new o,this.associations=new Map,this.primitiveCache={},this.nodeCache={},this.meshCache={refs:{},uses:{}},this.cameraCache={refs:{},uses:{}},this.lightCache={refs:{},uses:{}},this.sourceCache={},this.textureCache={},this.nodeNamesUsed={};let s=!1,n=-1,r=!1,a=-1;if("undefined"!=typeof navigator){let e=navigator.userAgent;s=!0===/^((?!chrome|android).)*safari/i.test(e);let t=e.match(/Version\/(\d+)/);n=s&&t?parseInt(t[1],10):-1,a=(r=e.indexOf("Firefox")>-1)?e.match(/Firefox\/([0-9]+)\./)[1]:-1}"undefined"==typeof createImageBitmap||s&&n<17||r&&a<98?this.textureLoader=new i.Tap(this.options.manager):this.textureLoader=new i.Kzg(this.options.manager),this.textureLoader.setCrossOrigin(this.options.crossOrigin),this.textureLoader.setRequestHeader(this.options.requestHeader),this.fileLoader=new i.Y9S(this.options.manager),this.fileLoader.setResponseType("arraybuffer"),"use-credentials"===this.options.crossOrigin&&this.fileLoader.setWithCredentials(!0)}setExtensions(e){this.extensions=e}setPlugins(e){this.plugins=e}parse(e,t){let s=this,i=this.json,n=this.extensions;this.cache.removeAll(),this.nodeCache={},this._invokeAll(function(e){return e._markDefs&&e._markDefs()}),Promise.all(this._invokeAll(function(e){return e.beforeRoot&&e.beforeRoot()})).then(function(){return Promise.all([s.getDependencies("scene"),s.getDependencies("animation"),s.getDependencies("camera")])}).then(function(t){let r={scene:t[0][i.scene||0],scenes:t[0],animations:t[1],cameras:t[2],asset:i.asset,parser:s,userData:{}};return Y(n,r,i),z(r,i),Promise.all(s._invokeAll(function(e){return e.afterRoot&&e.afterRoot(r)})).then(function(){for(let e of r.scenes)e.updateMatrixWorld();e(r)})}).catch(t)}_markDefs(){let e=this.json.nodes||[],t=this.json.skins||[],s=this.json.meshes||[];for(let s=0,i=t.length;s<i;s++){let i=t[s].joints;for(let t=0,s=i.length;t<s;t++)e[i[t]].isBone=!0}for(let t=0,i=e.length;t<i;t++){let i=e[t];void 0!==i.mesh&&(this._addNodeRef(this.meshCache,i.mesh),void 0!==i.skin&&(s[i.mesh].isSkinnedMesh=!0)),void 0!==i.camera&&this._addNodeRef(this.cameraCache,i.camera)}}_addNodeRef(e,t){void 0!==t&&(void 0===e.refs[t]&&(e.refs[t]=e.uses[t]=0),e.refs[t]++)}_getNodeRef(e,t,s){if(e.refs[t]<=1)return s;let i=s.clone(),n=(e,t)=>{let s=this.associations.get(e);for(let[i,r]of(null!=s&&this.associations.set(t,s),e.children.entries()))n(r,t.children[i])};return n(s,i),i.name+="_instance_"+e.uses[t]++,i}_invokeOne(e){let t=Object.values(this.plugins);t.push(this);for(let s=0;s<t.length;s++){let i=e(t[s]);if(i)return i}return null}_invokeAll(e){let t=Object.values(this.plugins);t.unshift(this);let s=[];for(let i=0;i<t.length;i++){let n=e(t[i]);n&&s.push(n)}return s}getDependency(e,t){let s=e+":"+t,i=this.cache.get(s);if(!i){switch(e){case"scene":i=this.loadScene(t);break;case"node":i=this._invokeOne(function(e){return e.loadNode&&e.loadNode(t)});break;case"mesh":i=this._invokeOne(function(e){return e.loadMesh&&e.loadMesh(t)});break;case"accessor":i=this.loadAccessor(t);break;case"bufferView":i=this._invokeOne(function(e){return e.loadBufferView&&e.loadBufferView(t)});break;case"buffer":i=this.loadBuffer(t);break;case"material":i=this._invokeOne(function(e){return e.loadMaterial&&e.loadMaterial(t)});break;case"texture":i=this._invokeOne(function(e){return e.loadTexture&&e.loadTexture(t)});break;case"skin":i=this.loadSkin(t);break;case"animation":i=this._invokeOne(function(e){return e.loadAnimation&&e.loadAnimation(t)});break;case"camera":i=this.loadCamera(t);break;default:if(!(i=this._invokeOne(function(s){return s!=this&&s.getDependency&&s.getDependency(e,t)})))throw Error("Unknown type: "+e)}this.cache.add(s,i)}return i}getDependencies(e){let t=this.cache.get(e);if(!t){let s=this;t=Promise.all((this.json[e+("mesh"===e?"es":"s")]||[]).map(function(t,i){return s.getDependency(e,i)})),this.cache.add(e,t)}return t}loadBuffer(e){let t=this.json.buffers[e],s=this.fileLoader;if(t.type&&"arraybuffer"!==t.type)throw Error("THREE.GLTFLoader: "+t.type+" buffer type is not supported.");if(void 0===t.uri&&0===e)return Promise.resolve(this.extensions[a.KHR_BINARY_GLTF].body);let n=this.options;return new Promise(function(e,r){s.load(i.r6x.resolveURL(t.uri,n.path),e,void 0,function(){r(Error('THREE.GLTFLoader: Failed to load buffer "'+t.uri+'".'))})})}loadBufferView(e){let t=this.json.bufferViews[e];return this.getDependency("buffer",t.buffer).then(function(e){let s=t.byteLength||0,i=t.byteOffset||0;return e.slice(i,i+s)})}loadAccessor(e){let t=this,s=this.json,n=this.json.accessors[e];if(void 0===n.bufferView&&void 0===n.sparse){let e=U[n.type],t=k[n.componentType],s=!0===n.normalized,r=new t(n.count*e);return Promise.resolve(new i.THS(r,e,s))}let r=[];return void 0!==n.bufferView?r.push(this.getDependency("bufferView",n.bufferView)):r.push(null),void 0!==n.sparse&&(r.push(this.getDependency("bufferView",n.sparse.indices.bufferView)),r.push(this.getDependency("bufferView",n.sparse.values.bufferView))),Promise.all(r).then(function(e){let r,o,a=e[0],l=U[n.type],h=k[n.componentType],c=h.BYTES_PER_ELEMENT,u=c*l,d=n.byteOffset||0,p=void 0!==n.bufferView?s.bufferViews[n.bufferView].byteStride:void 0,m=!0===n.normalized;if(p&&p!==u){let e=Math.floor(d/p),s="InterleavedBuffer:"+n.bufferView+":"+n.componentType+":"+e+":"+n.count,u=t.cache.get(s);u||(r=new h(a,e*p,n.count*p/c),u=new i.eB$(r,p/c),t.cache.add(s,u)),o=new i.eHs(u,l,d%p/c,m)}else r=null===a?new h(n.count*l):new h(a,d,n.count*l),o=new i.THS(r,l,m);if(void 0!==n.sparse){let t=U.SCALAR,s=k[n.sparse.indices.componentType],r=n.sparse.indices.byteOffset||0,c=n.sparse.values.byteOffset||0,u=new s(e[1],r,n.sparse.count*t),d=new h(e[2],c,n.sparse.count*l);null!==a&&(o=new i.THS(o.array.slice(),o.itemSize,o.normalized)),o.normalized=!1;for(let e=0,t=u.length;e<t;e++){let t=u[e];if(o.setX(t,d[e*l]),l>=2&&o.setY(t,d[e*l+1]),l>=3&&o.setZ(t,d[e*l+2]),l>=4&&o.setW(t,d[e*l+3]),l>=5)throw Error("THREE.GLTFLoader: Unsupported itemSize in sparse BufferAttribute.")}o.normalized=m}return o})}loadTexture(e){let t=this.json,s=this.options,i=t.textures[e].source,n=t.images[i],r=this.textureLoader;if(n.uri){let e=s.manager.getHandler(n.uri);null!==e&&(r=e)}return this.loadTextureImage(e,i,r)}loadTextureImage(e,t,s){let n=this,r=this.json,o=r.textures[e],a=r.images[t],l=(a.uri||a.bufferView)+":"+o.sampler;if(this.textureCache[l])return this.textureCache[l];let h=this.loadImageSource(t,s).then(function(t){t.flipY=!1,t.name=o.name||a.name||"",""===t.name&&"string"==typeof a.uri&&!1===a.uri.startsWith("data:image/")&&(t.name=a.uri);let s=(r.samplers||{})[o.sampler]||{};return t.magFilter=H[s.magFilter]||i.k6q,t.minFilter=H[s.minFilter]||i.$_I,t.wrapS=j[s.wrapS]||i.GJx,t.wrapT=j[s.wrapT]||i.GJx,t.generateMipmaps=!t.isCompressedTexture&&t.minFilter!==i.hxR&&t.minFilter!==i.k6q,n.associations.set(t,{textures:e}),t}).catch(function(){return null});return this.textureCache[l]=h,h}loadImageSource(e,t){let s=this.json,n=this.options;if(void 0!==this.sourceCache[e])return this.sourceCache[e].then(e=>e.clone());let r=s.images[e],o=self.URL||self.webkitURL,a=r.uri||"",l=!1;if(void 0!==r.bufferView)a=this.getDependency("bufferView",r.bufferView).then(function(e){l=!0;let t=new Blob([e],{type:r.mimeType});return a=o.createObjectURL(t)});else if(void 0===r.uri)throw Error("THREE.GLTFLoader: Image "+e+" is missing URI and bufferView");let h=Promise.resolve(a).then(function(e){return new Promise(function(s,r){let o=s;!0===t.isImageBitmapLoader&&(o=function(e){let t=new i.gPd(e);t.needsUpdate=!0,s(t)}),t.load(i.r6x.resolveURL(e,n.path),o,void 0,r)})}).then(function(e){var t;return!0===l&&o.revokeObjectURL(a),z(e,r),e.userData.mimeType=r.mimeType||((t=r.uri).search(/\.jpe?g($|\?)/i)>0||0===t.search(/^data\:image\/jpeg/)?"image/jpeg":t.search(/\.webp($|\?)/i)>0||0===t.search(/^data\:image\/webp/)?"image/webp":t.search(/\.ktx2($|\?)/i)>0||0===t.search(/^data\:image\/ktx2/)?"image/ktx2":"image/png"),e}).catch(function(e){throw console.error("THREE.GLTFLoader: Couldn't load texture",a),e});return this.sourceCache[e]=h,h}assignTexture(e,t,s,i){let n=this;return this.getDependency("texture",s.index).then(function(r){if(!r)return null;if(void 0!==s.texCoord&&s.texCoord>0&&((r=r.clone()).channel=s.texCoord),n.extensions[a.KHR_TEXTURE_TRANSFORM]){let e=void 0!==s.extensions?s.extensions[a.KHR_TEXTURE_TRANSFORM]:void 0;if(e){let t=n.associations.get(r);r=n.extensions[a.KHR_TEXTURE_TRANSFORM].extendTexture(r,e),n.associations.set(r,t)}}return void 0!==i&&(r.colorSpace=i),e[t]=r,r})}assignFinalMaterial(e){let t=e.geometry,s=e.material,n=void 0===t.attributes.tangent,r=void 0!==t.attributes.color,o=void 0===t.attributes.normal;if(e.isPoints){let e="PointsMaterial:"+s.uuid,t=this.cache.get(e);t||(t=new i.BH$,i.imn.prototype.copy.call(t,s),t.color.copy(s.color),t.map=s.map,t.sizeAttenuation=!1,this.cache.add(e,t)),s=t}else if(e.isLine){let e="LineBasicMaterial:"+s.uuid,t=this.cache.get(e);t||(t=new i.mrM,i.imn.prototype.copy.call(t,s),t.color.copy(s.color),t.map=s.map,this.cache.add(e,t)),s=t}if(n||r||o){let e="ClonedMaterial:"+s.uuid+":";n&&(e+="derivative-tangents:"),r&&(e+="vertex-colors:"),o&&(e+="flat-shading:");let t=this.cache.get(e);t||(t=s.clone(),r&&(t.vertexColors=!0),o&&(t.flatShading=!0),n&&(t.normalScale&&(t.normalScale.y*=-1),t.clearcoatNormalScale&&(t.clearcoatNormalScale.y*=-1)),this.cache.add(e,t),this.associations.set(t,this.associations.get(s))),s=t}e.material=s}getMaterialType(){return i._4j}loadMaterial(e){let t,s=this,n=this.json,r=this.extensions,o=n.materials[e],l={},h=o.extensions||{},c=[];if(h[a.KHR_MATERIALS_UNLIT]){let e=r[a.KHR_MATERIALS_UNLIT];t=e.getMaterialType(),c.push(e.extendParams(l,o,s))}else{let n=o.pbrMetallicRoughness||{};if(l.color=new i.Q1f(1,1,1),l.opacity=1,Array.isArray(n.baseColorFactor)){let e=n.baseColorFactor;l.color.setRGB(e[0],e[1],e[2],i.Zr2),l.opacity=e[3]}void 0!==n.baseColorTexture&&c.push(s.assignTexture(l,"map",n.baseColorTexture,i.er$)),l.metalness=void 0!==n.metallicFactor?n.metallicFactor:1,l.roughness=void 0!==n.roughnessFactor?n.roughnessFactor:1,void 0!==n.metallicRoughnessTexture&&(c.push(s.assignTexture(l,"metalnessMap",n.metallicRoughnessTexture)),c.push(s.assignTexture(l,"roughnessMap",n.metallicRoughnessTexture))),t=this._invokeOne(function(t){return t.getMaterialType&&t.getMaterialType(e)}),c.push(Promise.all(this._invokeAll(function(t){return t.extendMaterialParams&&t.extendMaterialParams(e,l)})))}!0===o.doubleSided&&(l.side=i.$EB);let u=o.alphaMode||G.OPAQUE;if(u===G.BLEND?(l.transparent=!0,l.depthWrite=!1):(l.transparent=!1,u===G.MASK&&(l.alphaTest=void 0!==o.alphaCutoff?o.alphaCutoff:.5)),void 0!==o.normalTexture&&t!==i.V9B&&(c.push(s.assignTexture(l,"normalMap",o.normalTexture)),l.normalScale=new i.I9Y(1,1),void 0!==o.normalTexture.scale)){let e=o.normalTexture.scale;l.normalScale.set(e,e)}if(void 0!==o.occlusionTexture&&t!==i.V9B&&(c.push(s.assignTexture(l,"aoMap",o.occlusionTexture)),void 0!==o.occlusionTexture.strength&&(l.aoMapIntensity=o.occlusionTexture.strength)),void 0!==o.emissiveFactor&&t!==i.V9B){let e=o.emissiveFactor;l.emissive=new i.Q1f().setRGB(e[0],e[1],e[2],i.Zr2)}return void 0!==o.emissiveTexture&&t!==i.V9B&&c.push(s.assignTexture(l,"emissiveMap",o.emissiveTexture,i.er$)),Promise.all(c).then(function(){let i=new t(l);return o.name&&(i.name=o.name),z(i,o),s.associations.set(i,{materials:e}),o.extensions&&Y(r,i,o),i})}createUniqueName(e){let t=i.Nwf.sanitizeNodeName(e||"");return t in this.nodeNamesUsed?t+"_"+ ++this.nodeNamesUsed[t]:(this.nodeNamesUsed[t]=0,t)}loadGeometries(e){let t=this,s=this.extensions,n=this.primitiveCache,r=[];for(let o=0,l=e.length;o<l;o++){let l=e[o],h=function(e){let t,s=e.extensions&&e.extensions[a.KHR_DRACO_MESH_COMPRESSION];if(t=s?"draco:"+s.bufferView+":"+s.indices+":"+X(s.attributes):e.indices+":"+X(e.attributes)+":"+e.mode,void 0!==e.targets)for(let s=0,i=e.targets.length;s<i;s++)t+=":"+X(e.targets[s]);return t}(l),c=n[h];if(c)r.push(c.promise);else{let e;e=l.extensions&&l.extensions[a.KHR_DRACO_MESH_COMPRESSION]?function(e){return s[a.KHR_DRACO_MESH_COMPRESSION].decodePrimitive(e,t).then(function(s){return W(s,e,t)})}(l):W(new i.LoY,l,t),n[h]={primitive:l,promise:e},r.push(e)}}return Promise.all(r)}loadMesh(e){let t=this,s=this.json,r=this.extensions,o=s.meshes[e],a=o.primitives,l=[];for(let e=0,t=a.length;e<t;e++){var h;let t=void 0===a[e].material?(void 0===(h=this.cache).DefaultMaterial&&(h.DefaultMaterial=new i._4j({color:0xffffff,emissive:0,metalness:1,roughness:1,transparent:!1,depthTest:!0,side:i.hB5})),h.DefaultMaterial):this.getDependency("material",a[e].material);l.push(t)}return l.push(t.loadGeometries(a)),Promise.all(l).then(function(s){let l=s.slice(0,s.length-1),h=s[s.length-1],c=[];for(let s=0,u=h.length;s<u;s++){let u,d=h[s],p=a[s],m=l[s];if(p.mode===D.TRIANGLES||p.mode===D.TRIANGLE_STRIP||p.mode===D.TRIANGLE_FAN||void 0===p.mode)!0===(u=!0===o.isSkinnedMesh?new i.I46(d,m):new i.eaF(d,m)).isSkinnedMesh&&u.normalizeSkinWeights(),p.mode===D.TRIANGLE_STRIP?u.geometry=n(u.geometry,i.O49):p.mode===D.TRIANGLE_FAN&&(u.geometry=n(u.geometry,i.rYR));else if(p.mode===D.LINES)u=new i.DXC(d,m);else if(p.mode===D.LINE_STRIP)u=new i.N1A(d,m);else if(p.mode===D.LINE_LOOP)u=new i.FCc(d,m);else if(p.mode===D.POINTS)u=new i.ONl(d,m);else throw Error("THREE.GLTFLoader: Primitive mode unsupported: "+p.mode);Object.keys(u.geometry.morphAttributes).length>0&&function(e,t){if(e.updateMorphTargets(),void 0!==t.weights)for(let s=0,i=t.weights.length;s<i;s++)e.morphTargetInfluences[s]=t.weights[s];if(t.extras&&Array.isArray(t.extras.targetNames)){let s=t.extras.targetNames;if(e.morphTargetInfluences.length===s.length){e.morphTargetDictionary={};for(let t=0,i=s.length;t<i;t++)e.morphTargetDictionary[s[t]]=t}else console.warn("THREE.GLTFLoader: Invalid extras.targetNames length. Ignoring names.")}}(u,o),u.name=t.createUniqueName(o.name||"mesh_"+e),z(u,o),p.extensions&&Y(r,u,p),t.assignFinalMaterial(u),c.push(u)}for(let s=0,i=c.length;s<i;s++)t.associations.set(c[s],{meshes:e,primitives:s});if(1===c.length)return o.extensions&&Y(r,c[0],o),c[0];let u=new i.YJl;o.extensions&&Y(r,u,o),t.associations.set(u,{meshes:e});for(let e=0,t=c.length;e<t;e++)u.add(c[e]);return u})}loadCamera(e){let t,s=this.json.cameras[e],n=s[s.type];return n?("perspective"===s.type?t=new i.ubm(i.cj9.radToDeg(n.yfov),n.aspectRatio||1,n.znear||1,n.zfar||2e6):"orthographic"===s.type&&(t=new i.qUd(-n.xmag,n.xmag,n.ymag,-n.ymag,n.znear,n.zfar)),s.name&&(t.name=this.createUniqueName(s.name)),z(t,s),Promise.resolve(t)):void console.warn("THREE.GLTFLoader: Missing camera parameters.")}loadSkin(e){let t=this.json.skins[e],s=[];for(let e=0,i=t.joints.length;e<i;e++)s.push(this._loadNodeShallow(t.joints[e]));return void 0!==t.inverseBindMatrices?s.push(this.getDependency("accessor",t.inverseBindMatrices)):s.push(null),Promise.all(s).then(function(e){let s=e.pop(),n=[],r=[];for(let o=0,a=e.length;o<a;o++){let a=e[o];if(a){n.push(a);let e=new i.kn4;null!==s&&e.fromArray(s.array,16*o),r.push(e)}else console.warn('THREE.GLTFLoader: Joint "%s" could not be found.',t.joints[o])}return new i.EAD(n,r)})}loadAnimation(e){let t=this.json,s=this,n=t.animations[e],r=n.name?n.name:"animation_"+e,o=[],a=[],l=[],h=[],c=[];for(let e=0,t=n.channels.length;e<t;e++){let t=n.channels[e],s=n.samplers[t.sampler],i=t.target,r=i.node,u=void 0!==n.parameters?n.parameters[s.input]:s.input,d=void 0!==n.parameters?n.parameters[s.output]:s.output;void 0!==i.node&&(o.push(this.getDependency("node",r)),a.push(this.getDependency("accessor",u)),l.push(this.getDependency("accessor",d)),h.push(s),c.push(i))}return Promise.all([Promise.all(o),Promise.all(a),Promise.all(l),Promise.all(h),Promise.all(c)]).then(function(e){let t=e[0],n=e[1],o=e[2],a=e[3],l=e[4],h=[];for(let e=0,i=t.length;e<i;e++){let i=t[e],r=n[e],c=o[e],u=a[e],d=l[e];if(void 0===i)continue;i.updateMatrix&&i.updateMatrix();let p=s._createAnimationTracks(i,r,c,u,d);if(p)for(let e=0;e<p.length;e++)h.push(p[e])}return new i.tz3(r,void 0,h)})}createNodeMesh(e){let t=this.json,s=this,i=t.nodes[e];return void 0===i.mesh?null:s.getDependency("mesh",i.mesh).then(function(e){let t=s._getNodeRef(s.meshCache,i.mesh,e);return void 0!==i.weights&&t.traverse(function(e){if(e.isMesh)for(let t=0,s=i.weights.length;t<s;t++)e.morphTargetInfluences[t]=i.weights[t]}),t})}loadNode(e){let t=this.json.nodes[e],s=this._loadNodeShallow(e),i=[],n=t.children||[];for(let e=0,t=n.length;e<t;e++)i.push(this.getDependency("node",n[e]));let r=void 0===t.skin?Promise.resolve(null):this.getDependency("skin",t.skin);return Promise.all([s,Promise.all(i),r]).then(function(e){let t=e[0],s=e[1],i=e[2];null!==i&&t.traverse(function(e){e.isSkinnedMesh&&e.bind(i,q)});for(let e=0,i=s.length;e<i;e++)t.add(s[e]);return t})}_loadNodeShallow(e){let t=this.json,s=this.extensions,n=this;if(void 0!==this.nodeCache[e])return this.nodeCache[e];let r=t.nodes[e],o=r.name?n.createUniqueName(r.name):"",a=[],l=n._invokeOne(function(t){return t.createNodeMesh&&t.createNodeMesh(e)});return l&&a.push(l),void 0!==r.camera&&a.push(n.getDependency("camera",r.camera).then(function(e){return n._getNodeRef(n.cameraCache,r.camera,e)})),n._invokeAll(function(t){return t.createNodeAttachment&&t.createNodeAttachment(e)}).forEach(function(e){a.push(e)}),this.nodeCache[e]=Promise.all(a).then(function(t){let a;if((a=!0===r.isBone?new i.$Kf:t.length>1?new i.YJl:1===t.length?t[0]:new i.B69)!==t[0])for(let e=0,s=t.length;e<s;e++)a.add(t[e]);if(r.name&&(a.userData.name=r.name,a.name=o),z(a,r),r.extensions&&Y(s,a,r),void 0!==r.matrix){let e=new i.kn4;e.fromArray(r.matrix),a.applyMatrix4(e)}else void 0!==r.translation&&a.position.fromArray(r.translation),void 0!==r.rotation&&a.quaternion.fromArray(r.rotation),void 0!==r.scale&&a.scale.fromArray(r.scale);if(n.associations.has(a)){if(void 0!==r.mesh&&n.meshCache.refs[r.mesh]>1){let e=n.associations.get(a);n.associations.set(a,{...e})}}else n.associations.set(a,{});return n.associations.get(a).nodes=e,a}),this.nodeCache[e]}loadScene(e){let t=this.extensions,s=this.json.scenes[e],n=this,r=new i.YJl;s.name&&(r.name=n.createUniqueName(s.name)),z(r,s),s.extensions&&Y(t,r,s);let o=s.nodes||[],a=[];for(let e=0,t=o.length;e<t;e++)a.push(n.getDependency("node",o[e]));return Promise.all(a).then(function(e){for(let t=0,s=e.length;t<s;t++)r.add(e[t]);return n.associations=(e=>{let t=new Map;for(let[e,s]of n.associations)(e instanceof i.imn||e instanceof i.gPd)&&t.set(e,s);return e.traverse(e=>{let s=n.associations.get(e);null!=s&&t.set(e,s)}),t})(r),r})}_createAnimationTracks(e,t,s,n,r){let o,a=[],l=e.name?e.name:e.uuid,h=[];switch(K[r.path]===K.weights?e.traverse(function(e){e.morphTargetInfluences&&h.push(e.name?e.name:e.uuid)}):h.push(l),K[r.path]){case K.weights:o=i.Hit;break;case K.rotation:o=i.MBL;break;case K.translation:case K.scale:o=i.RiT;break;default:o=1===s.itemSize?i.Hit:i.RiT}let c=void 0!==n.interpolation?B[n.interpolation]:i.PJ3,u=this._getArrayFromAccessor(s);for(let e=0,s=h.length;e<s;e++){let s=new o(h[e]+"."+K[r.path],t.array,u,c);"CUBICSPLINE"===n.interpolation&&this._createCubicSplineTrackInterpolant(s),a.push(s)}return a}_getArrayFromAccessor(e){let t=e.array;if(e.normalized){let e=V(t.constructor),s=new Float32Array(t.length);for(let i=0,n=t.length;i<n;i++)s[i]=t[i]*e;t=s}return t}_createCubicSplineTrackInterpolant(e){e.createInterpolant=function(e){return new(this instanceof i.MBL?C:I)(this.times,this.values,this.getValueSize()/3,e)},e.createInterpolant.isInterpolantFactoryMethodGLTFCubicSpline=!0}}function W(e,t,s){let n=t.attributes,r=[];for(let t in n){let i=F[t]||t.toLowerCase();i in e.attributes||r.push(function(t,i){return s.getDependency("accessor",t).then(function(t){e.setAttribute(i,t)})}(n[t],i))}if(void 0!==t.indices&&!e.index){let i=s.getDependency("accessor",t.indices).then(function(t){e.setIndex(t)});r.push(i)}return i.ppV.workingColorSpace!==i.Zr2&&"COLOR_0"in n&&console.warn(`THREE.GLTFLoader: Converting vertex colors from "srgb-linear" to "${i.ppV.workingColorSpace}" not supported.`),z(e,t),!function(e,t,s){let n=t.attributes,r=new i.NRn;if(void 0===n.POSITION)return;{let e=s.json.accessors[n.POSITION],t=e.min,o=e.max;if(void 0===t||void 0===o)return console.warn("THREE.GLTFLoader: Missing min/max properties for accessor POSITION.");if(r.set(new i.Pq0(t[0],t[1],t[2]),new i.Pq0(o[0],o[1],o[2])),e.normalized){let t=V(k[e.componentType]);r.min.multiplyScalar(t),r.max.multiplyScalar(t)}}let o=t.targets;if(void 0!==o){let e=new i.Pq0,t=new i.Pq0;for(let i=0,n=o.length;i<n;i++){let n=o[i];if(void 0!==n.POSITION){let i=s.json.accessors[n.POSITION],r=i.min,o=i.max;if(void 0!==r&&void 0!==o){if(t.setX(Math.max(Math.abs(r[0]),Math.abs(o[0]))),t.setY(Math.max(Math.abs(r[1]),Math.abs(o[1]))),t.setZ(Math.max(Math.abs(r[2]),Math.abs(o[2]))),i.normalized){let e=V(k[i.componentType]);t.multiplyScalar(e)}e.max(t)}else console.warn("THREE.GLTFLoader: Missing min/max properties for accessor POSITION.")}}r.expandByVector(e)}e.boundingBox=r;let a=new i.iyt;r.getCenter(a.center),a.radius=r.min.distanceTo(r.max)/2,e.boundingSphere=a}(e,t,s),Promise.all(r).then(function(){return void 0!==t.targets?function(e,t,s){let i=!1,n=!1,r=!1;for(let e=0,s=t.length;e<s;e++){let s=t[e];if(void 0!==s.POSITION&&(i=!0),void 0!==s.NORMAL&&(n=!0),void 0!==s.COLOR_0&&(r=!0),i&&n&&r)break}if(!i&&!n&&!r)return Promise.resolve(e);let o=[],a=[],l=[];for(let h=0,c=t.length;h<c;h++){let c=t[h];if(i){let t=void 0!==c.POSITION?s.getDependency("accessor",c.POSITION):e.attributes.position;o.push(t)}if(n){let t=void 0!==c.NORMAL?s.getDependency("accessor",c.NORMAL):e.attributes.normal;a.push(t)}if(r){let t=void 0!==c.COLOR_0?s.getDependency("accessor",c.COLOR_0):e.attributes.color;l.push(t)}}return Promise.all([Promise.all(o),Promise.all(a),Promise.all(l)]).then(function(t){let s=t[0],o=t[1],a=t[2];return i&&(e.morphAttributes.position=s),n&&(e.morphAttributes.normal=o),r&&(e.morphAttributes.color=a),e.morphTargetsRelative=!0,e})}(e,t.targets,s):e})}}}]);