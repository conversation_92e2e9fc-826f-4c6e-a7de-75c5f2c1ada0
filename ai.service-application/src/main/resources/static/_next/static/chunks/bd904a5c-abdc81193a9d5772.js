"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[831],{3264:(t,e,i)=>{let s;i.d(e,{$EB:()=>y,$Kf:()=>r_,$Yl:()=>F,$_I:()=>tw,$ei:()=>A,$p8:()=>a2,A$4:()=>sP,AQS:()=>e0,B69:()=>sn,BH$:()=>r7,BKk:()=>s9,BXX:()=>tX,B_h:()=>t2,CSG:()=>ap,CVz:()=>tQ,CWW:()=>ec,Cfg:()=>tx,DXC:()=>r8,Dmk:()=>tP,EAD:()=>rC,EZo:()=>x,EdD:()=>M,FCc:()=>r9,FFZ:()=>eB,FV:()=>ts,FXf:()=>S,Fn:()=>en,GJx:()=>td,GWd:()=>tR,Gwm:()=>X,H23:()=>ea,H2z:()=>op,HIg:()=>tN,HO_:()=>eu,HXV:()=>tG,HiM:()=>aQ,Hit:()=>a_,I46:()=>rS,I9Y:()=>eX,IE4:()=>tJ,IUQ:()=>il,Iit:()=>s4,Jnc:()=>l,K52:()=>Y,KDk:()=>t0,KLL:()=>e_,KRh:()=>$,Kef:()=>eh,Kwu:()=>b,Kzg:()=>a4,LAk:()=>tn,LiQ:()=>E,LlO:()=>s8,LoY:()=>sU,MBL:()=>aA,MW4:()=>sI,Mjd:()=>te,N1A:()=>r3,N5j:()=>eg,NRn:()=>im,NTi:()=>g,Nex:()=>od,Nt7:()=>I,Nwf:()=>oa,Nz6:()=>tq,O49:()=>ey,O9p:()=>iQ,ONl:()=>nr,OUM:()=>tv,Om:()=>tp,OuU:()=>B,PJ3:()=>ed,PPD:()=>rq,PTz:()=>eY,Pq0:()=>eZ,Q1f:()=>s_,QP0:()=>u,Qev:()=>eN,Qrf:()=>t4,R3r:()=>rl,RJ4:()=>em,RQf:()=>tC,RiT:()=>aC,Riy:()=>tK,RlV:()=>iD,RrE:()=>L,RyA:()=>p,S$4:()=>er,THS:()=>sB,Tap:()=>aF,TdN:()=>eI,TiK:()=>eC,TkQ:()=>tW,U3G:()=>H,V3x:()=>tB,V9B:()=>sC,VCu:()=>na,VT0:()=>tF,Vb5:()=>h,VxR:()=>eS,W9U:()=>eo,WNZ:()=>o,Wdf:()=>eP,Wew:()=>tE,Wk7:()=>c,XG_:()=>el,XIg:()=>f,XrR:()=>Q,Y9S:()=>aR,YHV:()=>oc,YJl:()=>ro,Yuy:()=>tA,Z58:()=>ru,ZLX:()=>rV,ZQM:()=>tj,Zcv:()=>rD,Zr2:()=>ev,ZyN:()=>a1,_4j:()=>au,_QJ:()=>t9,_Ut:()=>s6,a55:()=>eF,a5J:()=>t8,aEY:()=>R,aHM:()=>aP,aJ8:()=>ta,aVO:()=>ad,amv:()=>ez,b4q:()=>rn,bC7:()=>ee,bCz:()=>w,bI3:()=>ex,bdM:()=>ae,bkx:()=>tT,brA:()=>J,bw0:()=>Z,c90:()=>tU,cHt:()=>tz,caT:()=>G,cj9:()=>eH,czI:()=>t3,dYF:()=>id,dcC:()=>tL,dth:()=>aD,dwI:()=>eQ,e0p:()=>j,eB$:()=>rc,eHc:()=>W,eHs:()=>rd,eaF:()=>s1,eoi:()=>ek,er$:()=>ew,f4X:()=>k,fBL:()=>t_,g7M:()=>tr,gJ2:()=>tO,gO9:()=>v,gPd:()=>ih,gWB:()=>eO,ghU:()=>tm,hB5:()=>d,hdd:()=>P,hgQ:()=>V,hsX:()=>m,hxR:()=>tf,hy7:()=>th,iNn:()=>s3,ie2:()=>O,imn:()=>sT,ix0:()=>tk,iyt:()=>iP,jR7:()=>tH,jej:()=>e4,jf0:()=>eM,jzd:()=>eE,k6Q:()=>tY,k6q:()=>tb,kBv:()=>n,kO0:()=>eT,kRr:()=>tM,kTW:()=>ty,kTp:()=>tZ,kn4:()=>iW,kyO:()=>tt,lGu:()=>U,lGw:()=>ag,lPF:()=>e2,ljd:()=>ep,lxW:()=>s5,lyL:()=>et,mcG:()=>e5,mrM:()=>rZ,nCl:()=>aX,nNL:()=>ti,nST:()=>_,nWS:()=>ic,nZQ:()=>a6,o6l:()=>ra,ojh:()=>T,ojs:()=>es,ov9:()=>D,pBf:()=>t$,pHI:()=>tg,paN:()=>tD,ppV:()=>e9,psI:()=>t6,qUd:()=>aK,qa3:()=>t1,qad:()=>C,qq$:()=>e1,r6x:()=>a3,rFo:()=>ip,rSH:()=>t5,rYR:()=>ef,sPf:()=>r,tBo:()=>oh,tJf:()=>tS,tz3:()=>ak,uB5:()=>t7,uSd:()=>ac,uV5:()=>tc,uWO:()=>rk,ubm:()=>rs,vim:()=>eA,vyJ:()=>eb,wfO:()=>tu,wn6:()=>N,wrO:()=>tI,wtR:()=>a,xFO:()=>tl,xSv:()=>q,y3Z:()=>ei,y_p:()=>K,zdS:()=>tV,zgK:()=>iK,znC:()=>z});let r="179",n={LEFT:0,MIDDLE:1,RIGHT:2,ROTATE:0,DOLLY:1,PAN:2},a={ROTATE:0,PAN:1,DOLLY_PAN:2,DOLLY_ROTATE:3},o=0,h=1,l=2,u=1,c=2,p=3,d=0,m=1,y=2,f=0,g=1,x=2,b=3,M=4,w=5,v=100,S=101,_=102,z=103,A=104,T=200,C=201,k=202,E=203,O=204,B=205,P=206,I=207,N=208,R=209,V=210,L=211,F=212,j=213,D=214,W=0,U=1,J=2,q=3,H=4,X=5,Y=6,Z=7,G=0,$=1,Q=2,K=0,tt=1,te=2,ti=3,ts=4,tr=5,tn=6,ta=7,to="attached",th=301,tl=302,tu=303,tc=304,tp=306,td=1e3,tm=1001,ty=1002,tf=1003,tg=1004,tx=1005,tb=1006,tM=1007,tw=1008,tv=1009,tS=1010,t_=1011,tz=1012,tA=1013,tT=1014,tC=1015,tk=1016,tE=1017,tO=1018,tB=1020,tP=35902,tI=1021,tN=1022,tR=1023,tV=1026,tL=1027,tF=1028,tj=1029,tD=1030,tW=1031,tU=1033,tJ=33776,tq=33777,tH=33778,tX=33779,tY=35840,tZ=35841,tG=35842,t$=35843,tQ=36196,tK=37492,t0=37496,t1=37808,t2=37809,t3=37810,t5=37811,t4=37812,t6=37813,t8=37814,t9=37815,t7=37816,et=37817,ee=37818,ei=37819,es=37820,er=37821,en=36492,ea=36494,eo=36495,eh=36283,el=36284,eu=36285,ec=36286,ep=2300,ed=2301,em=0,ey=1,ef=2,eg=3201,ex=0,eb=1,eM="",ew="srgb",ev="srgb-linear",eS="linear",e_="srgb",ez=512,eA=513,eT=514,eC=515,ek=516,eE=517,eO=518,eB=519,eP="300 es",eI=2e3;class eN{addEventListener(t,e){void 0===this._listeners&&(this._listeners={});let i=this._listeners;void 0===i[t]&&(i[t]=[]),-1===i[t].indexOf(e)&&i[t].push(e)}hasEventListener(t,e){let i=this._listeners;return void 0!==i&&void 0!==i[t]&&-1!==i[t].indexOf(e)}removeEventListener(t,e){let i=this._listeners;if(void 0===i)return;let s=i[t];if(void 0!==s){let t=s.indexOf(e);-1!==t&&s.splice(t,1)}}dispatchEvent(t){let e=this._listeners;if(void 0===e)return;let i=e[t.type];if(void 0!==i){t.target=this;let e=i.slice(0);for(let i=0,s=e.length;i<s;i++)e[i].call(this,t);t.target=null}}}let eR=["00","01","02","03","04","05","06","07","08","09","0a","0b","0c","0d","0e","0f","10","11","12","13","14","15","16","17","18","19","1a","1b","1c","1d","1e","1f","20","21","22","23","24","25","26","27","28","29","2a","2b","2c","2d","2e","2f","30","31","32","33","34","35","36","37","38","39","3a","3b","3c","3d","3e","3f","40","41","42","43","44","45","46","47","48","49","4a","4b","4c","4d","4e","4f","50","51","52","53","54","55","56","57","58","59","5a","5b","5c","5d","5e","5f","60","61","62","63","64","65","66","67","68","69","6a","6b","6c","6d","6e","6f","70","71","72","73","74","75","76","77","78","79","7a","7b","7c","7d","7e","7f","80","81","82","83","84","85","86","87","88","89","8a","8b","8c","8d","8e","8f","90","91","92","93","94","95","96","97","98","99","9a","9b","9c","9d","9e","9f","a0","a1","a2","a3","a4","a5","a6","a7","a8","a9","aa","ab","ac","ad","ae","af","b0","b1","b2","b3","b4","b5","b6","b7","b8","b9","ba","bb","bc","bd","be","bf","c0","c1","c2","c3","c4","c5","c6","c7","c8","c9","ca","cb","cc","cd","ce","cf","d0","d1","d2","d3","d4","d5","d6","d7","d8","d9","da","db","dc","dd","de","df","e0","e1","e2","e3","e4","e5","e6","e7","e8","e9","ea","eb","ec","ed","ee","ef","f0","f1","f2","f3","f4","f5","f6","f7","f8","f9","fa","fb","fc","fd","fe","ff"],eV=1234567,eL=Math.PI/180,eF=180/Math.PI;function ej(){let t=0xffffffff*Math.random()|0,e=0xffffffff*Math.random()|0,i=0xffffffff*Math.random()|0,s=0xffffffff*Math.random()|0;return(eR[255&t]+eR[t>>8&255]+eR[t>>16&255]+eR[t>>24&255]+"-"+eR[255&e]+eR[e>>8&255]+"-"+eR[e>>16&15|64]+eR[e>>24&255]+"-"+eR[63&i|128]+eR[i>>8&255]+"-"+eR[i>>16&255]+eR[i>>24&255]+eR[255&s]+eR[s>>8&255]+eR[s>>16&255]+eR[s>>24&255]).toLowerCase()}function eD(t,e,i){return Math.max(e,Math.min(i,t))}function eW(t,e){return(t%e+e)%e}function eU(t,e,i){return(1-i)*t+i*e}function eJ(t,e){switch(e.constructor){case Float32Array:return t;case Uint32Array:return t/0xffffffff;case Uint16Array:return t/65535;case Uint8Array:return t/255;case Int32Array:return Math.max(t/0x7fffffff,-1);case Int16Array:return Math.max(t/32767,-1);case Int8Array:return Math.max(t/127,-1);default:throw Error("Invalid component type.")}}function eq(t,e){switch(e.constructor){case Float32Array:return t;case Uint32Array:return Math.round(0xffffffff*t);case Uint16Array:return Math.round(65535*t);case Uint8Array:return Math.round(255*t);case Int32Array:return Math.round(0x7fffffff*t);case Int16Array:return Math.round(32767*t);case Int8Array:return Math.round(127*t);default:throw Error("Invalid component type.")}}let eH={DEG2RAD:eL,RAD2DEG:eF,generateUUID:ej,clamp:eD,euclideanModulo:eW,mapLinear:function(t,e,i,s,r){return s+(t-e)*(r-s)/(i-e)},inverseLerp:function(t,e,i){return t!==e?(i-t)/(e-t):0},lerp:eU,damp:function(t,e,i,s){return eU(t,e,1-Math.exp(-i*s))},pingpong:function(t,e=1){return e-Math.abs(eW(t,2*e)-e)},smoothstep:function(t,e,i){return t<=e?0:t>=i?1:(t=(t-e)/(i-e))*t*(3-2*t)},smootherstep:function(t,e,i){return t<=e?0:t>=i?1:(t=(t-e)/(i-e))*t*t*(t*(6*t-15)+10)},randInt:function(t,e){return t+Math.floor(Math.random()*(e-t+1))},randFloat:function(t,e){return t+Math.random()*(e-t)},randFloatSpread:function(t){return t*(.5-Math.random())},seededRandom:function(t){void 0!==t&&(eV=t);let e=eV+=0x6d2b79f5;return e=Math.imul(e^e>>>15,1|e),(((e^=e+Math.imul(e^e>>>7,61|e))^e>>>14)>>>0)/0x100000000},degToRad:function(t){return t*eL},radToDeg:function(t){return t*eF},isPowerOfTwo:function(t){return(t&t-1)==0&&0!==t},ceilPowerOfTwo:function(t){return Math.pow(2,Math.ceil(Math.log(t)/Math.LN2))},floorPowerOfTwo:function(t){return Math.pow(2,Math.floor(Math.log(t)/Math.LN2))},setQuaternionFromProperEuler:function(t,e,i,s,r){let n=Math.cos,a=Math.sin,o=n(i/2),h=a(i/2),l=n((e+s)/2),u=a((e+s)/2),c=n((e-s)/2),p=a((e-s)/2),d=n((s-e)/2),m=a((s-e)/2);switch(r){case"XYX":t.set(o*u,h*c,h*p,o*l);break;case"YZY":t.set(h*p,o*u,h*c,o*l);break;case"ZXZ":t.set(h*c,h*p,o*u,o*l);break;case"XZX":t.set(o*u,h*m,h*d,o*l);break;case"YXY":t.set(h*d,o*u,h*m,o*l);break;case"ZYZ":t.set(h*m,h*d,o*u,o*l);break;default:console.warn("THREE.MathUtils: .setQuaternionFromProperEuler() encountered an unknown order: "+r)}},normalize:eq,denormalize:eJ};class eX{constructor(t=0,e=0){eX.prototype.isVector2=!0,this.x=t,this.y=e}get width(){return this.x}set width(t){this.x=t}get height(){return this.y}set height(t){this.y=t}set(t,e){return this.x=t,this.y=e,this}setScalar(t){return this.x=t,this.y=t,this}setX(t){return this.x=t,this}setY(t){return this.y=t,this}setComponent(t,e){switch(t){case 0:this.x=e;break;case 1:this.y=e;break;default:throw Error("index is out of range: "+t)}return this}getComponent(t){switch(t){case 0:return this.x;case 1:return this.y;default:throw Error("index is out of range: "+t)}}clone(){return new this.constructor(this.x,this.y)}copy(t){return this.x=t.x,this.y=t.y,this}add(t){return this.x+=t.x,this.y+=t.y,this}addScalar(t){return this.x+=t,this.y+=t,this}addVectors(t,e){return this.x=t.x+e.x,this.y=t.y+e.y,this}addScaledVector(t,e){return this.x+=t.x*e,this.y+=t.y*e,this}sub(t){return this.x-=t.x,this.y-=t.y,this}subScalar(t){return this.x-=t,this.y-=t,this}subVectors(t,e){return this.x=t.x-e.x,this.y=t.y-e.y,this}multiply(t){return this.x*=t.x,this.y*=t.y,this}multiplyScalar(t){return this.x*=t,this.y*=t,this}divide(t){return this.x/=t.x,this.y/=t.y,this}divideScalar(t){return this.multiplyScalar(1/t)}applyMatrix3(t){let e=this.x,i=this.y,s=t.elements;return this.x=s[0]*e+s[3]*i+s[6],this.y=s[1]*e+s[4]*i+s[7],this}min(t){return this.x=Math.min(this.x,t.x),this.y=Math.min(this.y,t.y),this}max(t){return this.x=Math.max(this.x,t.x),this.y=Math.max(this.y,t.y),this}clamp(t,e){return this.x=eD(this.x,t.x,e.x),this.y=eD(this.y,t.y,e.y),this}clampScalar(t,e){return this.x=eD(this.x,t,e),this.y=eD(this.y,t,e),this}clampLength(t,e){let i=this.length();return this.divideScalar(i||1).multiplyScalar(eD(i,t,e))}floor(){return this.x=Math.floor(this.x),this.y=Math.floor(this.y),this}ceil(){return this.x=Math.ceil(this.x),this.y=Math.ceil(this.y),this}round(){return this.x=Math.round(this.x),this.y=Math.round(this.y),this}roundToZero(){return this.x=Math.trunc(this.x),this.y=Math.trunc(this.y),this}negate(){return this.x=-this.x,this.y=-this.y,this}dot(t){return this.x*t.x+this.y*t.y}cross(t){return this.x*t.y-this.y*t.x}lengthSq(){return this.x*this.x+this.y*this.y}length(){return Math.sqrt(this.x*this.x+this.y*this.y)}manhattanLength(){return Math.abs(this.x)+Math.abs(this.y)}normalize(){return this.divideScalar(this.length()||1)}angle(){return Math.atan2(-this.y,-this.x)+Math.PI}angleTo(t){let e=Math.sqrt(this.lengthSq()*t.lengthSq());return 0===e?Math.PI/2:Math.acos(eD(this.dot(t)/e,-1,1))}distanceTo(t){return Math.sqrt(this.distanceToSquared(t))}distanceToSquared(t){let e=this.x-t.x,i=this.y-t.y;return e*e+i*i}manhattanDistanceTo(t){return Math.abs(this.x-t.x)+Math.abs(this.y-t.y)}setLength(t){return this.normalize().multiplyScalar(t)}lerp(t,e){return this.x+=(t.x-this.x)*e,this.y+=(t.y-this.y)*e,this}lerpVectors(t,e,i){return this.x=t.x+(e.x-t.x)*i,this.y=t.y+(e.y-t.y)*i,this}equals(t){return t.x===this.x&&t.y===this.y}fromArray(t,e=0){return this.x=t[e],this.y=t[e+1],this}toArray(t=[],e=0){return t[e]=this.x,t[e+1]=this.y,t}fromBufferAttribute(t,e){return this.x=t.getX(e),this.y=t.getY(e),this}rotateAround(t,e){let i=Math.cos(e),s=Math.sin(e),r=this.x-t.x,n=this.y-t.y;return this.x=r*i-n*s+t.x,this.y=r*s+n*i+t.y,this}random(){return this.x=Math.random(),this.y=Math.random(),this}*[Symbol.iterator](){yield this.x,yield this.y}}class eY{constructor(t=0,e=0,i=0,s=1){this.isQuaternion=!0,this._x=t,this._y=e,this._z=i,this._w=s}static slerpFlat(t,e,i,s,r,n,a){let o=i[s+0],h=i[s+1],l=i[s+2],u=i[s+3],c=r[n+0],p=r[n+1],d=r[n+2],m=r[n+3];if(0===a){t[e+0]=o,t[e+1]=h,t[e+2]=l,t[e+3]=u;return}if(1===a){t[e+0]=c,t[e+1]=p,t[e+2]=d,t[e+3]=m;return}if(u!==m||o!==c||h!==p||l!==d){let t=1-a,e=o*c+h*p+l*d+u*m,i=e>=0?1:-1,s=1-e*e;if(s>Number.EPSILON){let r=Math.sqrt(s),n=Math.atan2(r,e*i);t=Math.sin(t*n)/r,a=Math.sin(a*n)/r}let r=a*i;if(o=o*t+c*r,h=h*t+p*r,l=l*t+d*r,u=u*t+m*r,t===1-a){let t=1/Math.sqrt(o*o+h*h+l*l+u*u);o*=t,h*=t,l*=t,u*=t}}t[e]=o,t[e+1]=h,t[e+2]=l,t[e+3]=u}static multiplyQuaternionsFlat(t,e,i,s,r,n){let a=i[s],o=i[s+1],h=i[s+2],l=i[s+3],u=r[n],c=r[n+1],p=r[n+2],d=r[n+3];return t[e]=a*d+l*u+o*p-h*c,t[e+1]=o*d+l*c+h*u-a*p,t[e+2]=h*d+l*p+a*c-o*u,t[e+3]=l*d-a*u-o*c-h*p,t}get x(){return this._x}set x(t){this._x=t,this._onChangeCallback()}get y(){return this._y}set y(t){this._y=t,this._onChangeCallback()}get z(){return this._z}set z(t){this._z=t,this._onChangeCallback()}get w(){return this._w}set w(t){this._w=t,this._onChangeCallback()}set(t,e,i,s){return this._x=t,this._y=e,this._z=i,this._w=s,this._onChangeCallback(),this}clone(){return new this.constructor(this._x,this._y,this._z,this._w)}copy(t){return this._x=t.x,this._y=t.y,this._z=t.z,this._w=t.w,this._onChangeCallback(),this}setFromEuler(t,e=!0){let i=t._x,s=t._y,r=t._z,n=t._order,a=Math.cos,o=Math.sin,h=a(i/2),l=a(s/2),u=a(r/2),c=o(i/2),p=o(s/2),d=o(r/2);switch(n){case"XYZ":this._x=c*l*u+h*p*d,this._y=h*p*u-c*l*d,this._z=h*l*d+c*p*u,this._w=h*l*u-c*p*d;break;case"YXZ":this._x=c*l*u+h*p*d,this._y=h*p*u-c*l*d,this._z=h*l*d-c*p*u,this._w=h*l*u+c*p*d;break;case"ZXY":this._x=c*l*u-h*p*d,this._y=h*p*u+c*l*d,this._z=h*l*d+c*p*u,this._w=h*l*u-c*p*d;break;case"ZYX":this._x=c*l*u-h*p*d,this._y=h*p*u+c*l*d,this._z=h*l*d-c*p*u,this._w=h*l*u+c*p*d;break;case"YZX":this._x=c*l*u+h*p*d,this._y=h*p*u+c*l*d,this._z=h*l*d-c*p*u,this._w=h*l*u-c*p*d;break;case"XZY":this._x=c*l*u-h*p*d,this._y=h*p*u-c*l*d,this._z=h*l*d+c*p*u,this._w=h*l*u+c*p*d;break;default:console.warn("THREE.Quaternion: .setFromEuler() encountered an unknown order: "+n)}return!0===e&&this._onChangeCallback(),this}setFromAxisAngle(t,e){let i=e/2,s=Math.sin(i);return this._x=t.x*s,this._y=t.y*s,this._z=t.z*s,this._w=Math.cos(i),this._onChangeCallback(),this}setFromRotationMatrix(t){let e=t.elements,i=e[0],s=e[4],r=e[8],n=e[1],a=e[5],o=e[9],h=e[2],l=e[6],u=e[10],c=i+a+u;if(c>0){let t=.5/Math.sqrt(c+1);this._w=.25/t,this._x=(l-o)*t,this._y=(r-h)*t,this._z=(n-s)*t}else if(i>a&&i>u){let t=2*Math.sqrt(1+i-a-u);this._w=(l-o)/t,this._x=.25*t,this._y=(s+n)/t,this._z=(r+h)/t}else if(a>u){let t=2*Math.sqrt(1+a-i-u);this._w=(r-h)/t,this._x=(s+n)/t,this._y=.25*t,this._z=(o+l)/t}else{let t=2*Math.sqrt(1+u-i-a);this._w=(n-s)/t,this._x=(r+h)/t,this._y=(o+l)/t,this._z=.25*t}return this._onChangeCallback(),this}setFromUnitVectors(t,e){let i=t.dot(e)+1;return i<1e-8?(i=0,Math.abs(t.x)>Math.abs(t.z)?(this._x=-t.y,this._y=t.x,this._z=0):(this._x=0,this._y=-t.z,this._z=t.y)):(this._x=t.y*e.z-t.z*e.y,this._y=t.z*e.x-t.x*e.z,this._z=t.x*e.y-t.y*e.x),this._w=i,this.normalize()}angleTo(t){return 2*Math.acos(Math.abs(eD(this.dot(t),-1,1)))}rotateTowards(t,e){let i=this.angleTo(t);if(0===i)return this;let s=Math.min(1,e/i);return this.slerp(t,s),this}identity(){return this.set(0,0,0,1)}invert(){return this.conjugate()}conjugate(){return this._x*=-1,this._y*=-1,this._z*=-1,this._onChangeCallback(),this}dot(t){return this._x*t._x+this._y*t._y+this._z*t._z+this._w*t._w}lengthSq(){return this._x*this._x+this._y*this._y+this._z*this._z+this._w*this._w}length(){return Math.sqrt(this._x*this._x+this._y*this._y+this._z*this._z+this._w*this._w)}normalize(){let t=this.length();return 0===t?(this._x=0,this._y=0,this._z=0,this._w=1):(t=1/t,this._x=this._x*t,this._y=this._y*t,this._z=this._z*t,this._w=this._w*t),this._onChangeCallback(),this}multiply(t){return this.multiplyQuaternions(this,t)}premultiply(t){return this.multiplyQuaternions(t,this)}multiplyQuaternions(t,e){let i=t._x,s=t._y,r=t._z,n=t._w,a=e._x,o=e._y,h=e._z,l=e._w;return this._x=i*l+n*a+s*h-r*o,this._y=s*l+n*o+r*a-i*h,this._z=r*l+n*h+i*o-s*a,this._w=n*l-i*a-s*o-r*h,this._onChangeCallback(),this}slerp(t,e){if(0===e)return this;if(1===e)return this.copy(t);let i=this._x,s=this._y,r=this._z,n=this._w,a=n*t._w+i*t._x+s*t._y+r*t._z;if(a<0?(this._w=-t._w,this._x=-t._x,this._y=-t._y,this._z=-t._z,a=-a):this.copy(t),a>=1)return this._w=n,this._x=i,this._y=s,this._z=r,this;let o=1-a*a;if(o<=Number.EPSILON){let t=1-e;return this._w=t*n+e*this._w,this._x=t*i+e*this._x,this._y=t*s+e*this._y,this._z=t*r+e*this._z,this.normalize(),this}let h=Math.sqrt(o),l=Math.atan2(h,a),u=Math.sin((1-e)*l)/h,c=Math.sin(e*l)/h;return this._w=n*u+this._w*c,this._x=i*u+this._x*c,this._y=s*u+this._y*c,this._z=r*u+this._z*c,this._onChangeCallback(),this}slerpQuaternions(t,e,i){return this.copy(t).slerp(e,i)}random(){let t=2*Math.PI*Math.random(),e=2*Math.PI*Math.random(),i=Math.random(),s=Math.sqrt(1-i),r=Math.sqrt(i);return this.set(s*Math.sin(t),s*Math.cos(t),r*Math.sin(e),r*Math.cos(e))}equals(t){return t._x===this._x&&t._y===this._y&&t._z===this._z&&t._w===this._w}fromArray(t,e=0){return this._x=t[e],this._y=t[e+1],this._z=t[e+2],this._w=t[e+3],this._onChangeCallback(),this}toArray(t=[],e=0){return t[e]=this._x,t[e+1]=this._y,t[e+2]=this._z,t[e+3]=this._w,t}fromBufferAttribute(t,e){return this._x=t.getX(e),this._y=t.getY(e),this._z=t.getZ(e),this._w=t.getW(e),this._onChangeCallback(),this}toJSON(){return this.toArray()}_onChange(t){return this._onChangeCallback=t,this}_onChangeCallback(){}*[Symbol.iterator](){yield this._x,yield this._y,yield this._z,yield this._w}}class eZ{constructor(t=0,e=0,i=0){eZ.prototype.isVector3=!0,this.x=t,this.y=e,this.z=i}set(t,e,i){return void 0===i&&(i=this.z),this.x=t,this.y=e,this.z=i,this}setScalar(t){return this.x=t,this.y=t,this.z=t,this}setX(t){return this.x=t,this}setY(t){return this.y=t,this}setZ(t){return this.z=t,this}setComponent(t,e){switch(t){case 0:this.x=e;break;case 1:this.y=e;break;case 2:this.z=e;break;default:throw Error("index is out of range: "+t)}return this}getComponent(t){switch(t){case 0:return this.x;case 1:return this.y;case 2:return this.z;default:throw Error("index is out of range: "+t)}}clone(){return new this.constructor(this.x,this.y,this.z)}copy(t){return this.x=t.x,this.y=t.y,this.z=t.z,this}add(t){return this.x+=t.x,this.y+=t.y,this.z+=t.z,this}addScalar(t){return this.x+=t,this.y+=t,this.z+=t,this}addVectors(t,e){return this.x=t.x+e.x,this.y=t.y+e.y,this.z=t.z+e.z,this}addScaledVector(t,e){return this.x+=t.x*e,this.y+=t.y*e,this.z+=t.z*e,this}sub(t){return this.x-=t.x,this.y-=t.y,this.z-=t.z,this}subScalar(t){return this.x-=t,this.y-=t,this.z-=t,this}subVectors(t,e){return this.x=t.x-e.x,this.y=t.y-e.y,this.z=t.z-e.z,this}multiply(t){return this.x*=t.x,this.y*=t.y,this.z*=t.z,this}multiplyScalar(t){return this.x*=t,this.y*=t,this.z*=t,this}multiplyVectors(t,e){return this.x=t.x*e.x,this.y=t.y*e.y,this.z=t.z*e.z,this}applyEuler(t){return this.applyQuaternion(e$.setFromEuler(t))}applyAxisAngle(t,e){return this.applyQuaternion(e$.setFromAxisAngle(t,e))}applyMatrix3(t){let e=this.x,i=this.y,s=this.z,r=t.elements;return this.x=r[0]*e+r[3]*i+r[6]*s,this.y=r[1]*e+r[4]*i+r[7]*s,this.z=r[2]*e+r[5]*i+r[8]*s,this}applyNormalMatrix(t){return this.applyMatrix3(t).normalize()}applyMatrix4(t){let e=this.x,i=this.y,s=this.z,r=t.elements,n=1/(r[3]*e+r[7]*i+r[11]*s+r[15]);return this.x=(r[0]*e+r[4]*i+r[8]*s+r[12])*n,this.y=(r[1]*e+r[5]*i+r[9]*s+r[13])*n,this.z=(r[2]*e+r[6]*i+r[10]*s+r[14])*n,this}applyQuaternion(t){let e=this.x,i=this.y,s=this.z,r=t.x,n=t.y,a=t.z,o=t.w,h=2*(n*s-a*i),l=2*(a*e-r*s),u=2*(r*i-n*e);return this.x=e+o*h+n*u-a*l,this.y=i+o*l+a*h-r*u,this.z=s+o*u+r*l-n*h,this}project(t){return this.applyMatrix4(t.matrixWorldInverse).applyMatrix4(t.projectionMatrix)}unproject(t){return this.applyMatrix4(t.projectionMatrixInverse).applyMatrix4(t.matrixWorld)}transformDirection(t){let e=this.x,i=this.y,s=this.z,r=t.elements;return this.x=r[0]*e+r[4]*i+r[8]*s,this.y=r[1]*e+r[5]*i+r[9]*s,this.z=r[2]*e+r[6]*i+r[10]*s,this.normalize()}divide(t){return this.x/=t.x,this.y/=t.y,this.z/=t.z,this}divideScalar(t){return this.multiplyScalar(1/t)}min(t){return this.x=Math.min(this.x,t.x),this.y=Math.min(this.y,t.y),this.z=Math.min(this.z,t.z),this}max(t){return this.x=Math.max(this.x,t.x),this.y=Math.max(this.y,t.y),this.z=Math.max(this.z,t.z),this}clamp(t,e){return this.x=eD(this.x,t.x,e.x),this.y=eD(this.y,t.y,e.y),this.z=eD(this.z,t.z,e.z),this}clampScalar(t,e){return this.x=eD(this.x,t,e),this.y=eD(this.y,t,e),this.z=eD(this.z,t,e),this}clampLength(t,e){let i=this.length();return this.divideScalar(i||1).multiplyScalar(eD(i,t,e))}floor(){return this.x=Math.floor(this.x),this.y=Math.floor(this.y),this.z=Math.floor(this.z),this}ceil(){return this.x=Math.ceil(this.x),this.y=Math.ceil(this.y),this.z=Math.ceil(this.z),this}round(){return this.x=Math.round(this.x),this.y=Math.round(this.y),this.z=Math.round(this.z),this}roundToZero(){return this.x=Math.trunc(this.x),this.y=Math.trunc(this.y),this.z=Math.trunc(this.z),this}negate(){return this.x=-this.x,this.y=-this.y,this.z=-this.z,this}dot(t){return this.x*t.x+this.y*t.y+this.z*t.z}lengthSq(){return this.x*this.x+this.y*this.y+this.z*this.z}length(){return Math.sqrt(this.x*this.x+this.y*this.y+this.z*this.z)}manhattanLength(){return Math.abs(this.x)+Math.abs(this.y)+Math.abs(this.z)}normalize(){return this.divideScalar(this.length()||1)}setLength(t){return this.normalize().multiplyScalar(t)}lerp(t,e){return this.x+=(t.x-this.x)*e,this.y+=(t.y-this.y)*e,this.z+=(t.z-this.z)*e,this}lerpVectors(t,e,i){return this.x=t.x+(e.x-t.x)*i,this.y=t.y+(e.y-t.y)*i,this.z=t.z+(e.z-t.z)*i,this}cross(t){return this.crossVectors(this,t)}crossVectors(t,e){let i=t.x,s=t.y,r=t.z,n=e.x,a=e.y,o=e.z;return this.x=s*o-r*a,this.y=r*n-i*o,this.z=i*a-s*n,this}projectOnVector(t){let e=t.lengthSq();if(0===e)return this.set(0,0,0);let i=t.dot(this)/e;return this.copy(t).multiplyScalar(i)}projectOnPlane(t){return eG.copy(this).projectOnVector(t),this.sub(eG)}reflect(t){return this.sub(eG.copy(t).multiplyScalar(2*this.dot(t)))}angleTo(t){let e=Math.sqrt(this.lengthSq()*t.lengthSq());return 0===e?Math.PI/2:Math.acos(eD(this.dot(t)/e,-1,1))}distanceTo(t){return Math.sqrt(this.distanceToSquared(t))}distanceToSquared(t){let e=this.x-t.x,i=this.y-t.y,s=this.z-t.z;return e*e+i*i+s*s}manhattanDistanceTo(t){return Math.abs(this.x-t.x)+Math.abs(this.y-t.y)+Math.abs(this.z-t.z)}setFromSpherical(t){return this.setFromSphericalCoords(t.radius,t.phi,t.theta)}setFromSphericalCoords(t,e,i){let s=Math.sin(e)*t;return this.x=s*Math.sin(i),this.y=Math.cos(e)*t,this.z=s*Math.cos(i),this}setFromCylindrical(t){return this.setFromCylindricalCoords(t.radius,t.theta,t.y)}setFromCylindricalCoords(t,e,i){return this.x=t*Math.sin(e),this.y=i,this.z=t*Math.cos(e),this}setFromMatrixPosition(t){let e=t.elements;return this.x=e[12],this.y=e[13],this.z=e[14],this}setFromMatrixScale(t){let e=this.setFromMatrixColumn(t,0).length(),i=this.setFromMatrixColumn(t,1).length(),s=this.setFromMatrixColumn(t,2).length();return this.x=e,this.y=i,this.z=s,this}setFromMatrixColumn(t,e){return this.fromArray(t.elements,4*e)}setFromMatrix3Column(t,e){return this.fromArray(t.elements,3*e)}setFromEuler(t){return this.x=t._x,this.y=t._y,this.z=t._z,this}setFromColor(t){return this.x=t.r,this.y=t.g,this.z=t.b,this}equals(t){return t.x===this.x&&t.y===this.y&&t.z===this.z}fromArray(t,e=0){return this.x=t[e],this.y=t[e+1],this.z=t[e+2],this}toArray(t=[],e=0){return t[e]=this.x,t[e+1]=this.y,t[e+2]=this.z,t}fromBufferAttribute(t,e){return this.x=t.getX(e),this.y=t.getY(e),this.z=t.getZ(e),this}random(){return this.x=Math.random(),this.y=Math.random(),this.z=Math.random(),this}randomDirection(){let t=Math.random()*Math.PI*2,e=2*Math.random()-1,i=Math.sqrt(1-e*e);return this.x=i*Math.cos(t),this.y=e,this.z=i*Math.sin(t),this}*[Symbol.iterator](){yield this.x,yield this.y,yield this.z}}let eG=new eZ,e$=new eY;class eQ{constructor(t,e,i,s,r,n,a,o,h){eQ.prototype.isMatrix3=!0,this.elements=[1,0,0,0,1,0,0,0,1],void 0!==t&&this.set(t,e,i,s,r,n,a,o,h)}set(t,e,i,s,r,n,a,o,h){let l=this.elements;return l[0]=t,l[1]=s,l[2]=a,l[3]=e,l[4]=r,l[5]=o,l[6]=i,l[7]=n,l[8]=h,this}identity(){return this.set(1,0,0,0,1,0,0,0,1),this}copy(t){let e=this.elements,i=t.elements;return e[0]=i[0],e[1]=i[1],e[2]=i[2],e[3]=i[3],e[4]=i[4],e[5]=i[5],e[6]=i[6],e[7]=i[7],e[8]=i[8],this}extractBasis(t,e,i){return t.setFromMatrix3Column(this,0),e.setFromMatrix3Column(this,1),i.setFromMatrix3Column(this,2),this}setFromMatrix4(t){let e=t.elements;return this.set(e[0],e[4],e[8],e[1],e[5],e[9],e[2],e[6],e[10]),this}multiply(t){return this.multiplyMatrices(this,t)}premultiply(t){return this.multiplyMatrices(t,this)}multiplyMatrices(t,e){let i=t.elements,s=e.elements,r=this.elements,n=i[0],a=i[3],o=i[6],h=i[1],l=i[4],u=i[7],c=i[2],p=i[5],d=i[8],m=s[0],y=s[3],f=s[6],g=s[1],x=s[4],b=s[7],M=s[2],w=s[5],v=s[8];return r[0]=n*m+a*g+o*M,r[3]=n*y+a*x+o*w,r[6]=n*f+a*b+o*v,r[1]=h*m+l*g+u*M,r[4]=h*y+l*x+u*w,r[7]=h*f+l*b+u*v,r[2]=c*m+p*g+d*M,r[5]=c*y+p*x+d*w,r[8]=c*f+p*b+d*v,this}multiplyScalar(t){let e=this.elements;return e[0]*=t,e[3]*=t,e[6]*=t,e[1]*=t,e[4]*=t,e[7]*=t,e[2]*=t,e[5]*=t,e[8]*=t,this}determinant(){let t=this.elements,e=t[0],i=t[1],s=t[2],r=t[3],n=t[4],a=t[5],o=t[6],h=t[7],l=t[8];return e*n*l-e*a*h-i*r*l+i*a*o+s*r*h-s*n*o}invert(){let t=this.elements,e=t[0],i=t[1],s=t[2],r=t[3],n=t[4],a=t[5],o=t[6],h=t[7],l=t[8],u=l*n-a*h,c=a*o-l*r,p=h*r-n*o,d=e*u+i*c+s*p;if(0===d)return this.set(0,0,0,0,0,0,0,0,0);let m=1/d;return t[0]=u*m,t[1]=(s*h-l*i)*m,t[2]=(a*i-s*n)*m,t[3]=c*m,t[4]=(l*e-s*o)*m,t[5]=(s*r-a*e)*m,t[6]=p*m,t[7]=(i*o-h*e)*m,t[8]=(n*e-i*r)*m,this}transpose(){let t,e=this.elements;return t=e[1],e[1]=e[3],e[3]=t,t=e[2],e[2]=e[6],e[6]=t,t=e[5],e[5]=e[7],e[7]=t,this}getNormalMatrix(t){return this.setFromMatrix4(t).invert().transpose()}transposeIntoArray(t){let e=this.elements;return t[0]=e[0],t[1]=e[3],t[2]=e[6],t[3]=e[1],t[4]=e[4],t[5]=e[7],t[6]=e[2],t[7]=e[5],t[8]=e[8],this}setUvTransform(t,e,i,s,r,n,a){let o=Math.cos(r),h=Math.sin(r);return this.set(i*o,i*h,-i*(o*n+h*a)+n+t,-s*h,s*o,-s*(-h*n+o*a)+a+e,0,0,1),this}scale(t,e){return this.premultiply(eK.makeScale(t,e)),this}rotate(t){return this.premultiply(eK.makeRotation(-t)),this}translate(t,e){return this.premultiply(eK.makeTranslation(t,e)),this}makeTranslation(t,e){return t.isVector2?this.set(1,0,t.x,0,1,t.y,0,0,1):this.set(1,0,t,0,1,e,0,0,1),this}makeRotation(t){let e=Math.cos(t),i=Math.sin(t);return this.set(e,-i,0,i,e,0,0,0,1),this}makeScale(t,e){return this.set(t,0,0,0,e,0,0,0,1),this}equals(t){let e=this.elements,i=t.elements;for(let t=0;t<9;t++)if(e[t]!==i[t])return!1;return!0}fromArray(t,e=0){for(let i=0;i<9;i++)this.elements[i]=t[i+e];return this}toArray(t=[],e=0){let i=this.elements;return t[e]=i[0],t[e+1]=i[1],t[e+2]=i[2],t[e+3]=i[3],t[e+4]=i[4],t[e+5]=i[5],t[e+6]=i[6],t[e+7]=i[7],t[e+8]=i[8],t}clone(){return new this.constructor().fromArray(this.elements)}}let eK=new eQ;function e0(t){for(let e=t.length-1;e>=0;--e)if(t[e]>=65535)return!0;return!1}function e1(t){return document.createElementNS("http://www.w3.org/1999/xhtml",t)}function e2(){let t=e1("canvas");return t.style.display="block",t}Int8Array,Uint8Array,Uint8ClampedArray,Int16Array,Uint16Array,Int32Array,Uint32Array,Float32Array,Float64Array;let e3={};function e5(t){t in e3||(e3[t]=!0,console.warn(t))}function e4(t,e,i){return new Promise(function(s,r){setTimeout(function n(){switch(t.clientWaitSync(e,t.SYNC_FLUSH_COMMANDS_BIT,0)){case t.WAIT_FAILED:r();break;case t.TIMEOUT_EXPIRED:setTimeout(n,i);break;default:s()}},i)})}let e6=new eQ().set(.4123908,.3575843,.1804808,.212639,.7151687,.0721923,.0193308,.1191948,.9505322),e8=new eQ().set(3.2409699,-1.5373832,-.4986108,-.9692436,1.8759675,.0415551,.0556301,-.203977,1.0569715),e9=function(){let t={enabled:!0,workingColorSpace:ev,spaces:{},convert:function(t,e,i){return!1!==this.enabled&&e!==i&&e&&i&&(this.spaces[e].transfer===e_&&(t.r=e7(t.r),t.g=e7(t.g),t.b=e7(t.b)),this.spaces[e].primaries!==this.spaces[i].primaries&&(t.applyMatrix3(this.spaces[e].toXYZ),t.applyMatrix3(this.spaces[i].fromXYZ)),this.spaces[i].transfer===e_&&(t.r=it(t.r),t.g=it(t.g),t.b=it(t.b))),t},workingToColorSpace:function(t,e){return this.convert(t,this.workingColorSpace,e)},colorSpaceToWorking:function(t,e){return this.convert(t,e,this.workingColorSpace)},getPrimaries:function(t){return this.spaces[t].primaries},getTransfer:function(t){return t===eM?eS:this.spaces[t].transfer},getLuminanceCoefficients:function(t,e=this.workingColorSpace){return t.fromArray(this.spaces[e].luminanceCoefficients)},define:function(t){Object.assign(this.spaces,t)},_getMatrix:function(t,e,i){return t.copy(this.spaces[e].toXYZ).multiply(this.spaces[i].fromXYZ)},_getDrawingBufferColorSpace:function(t){return this.spaces[t].outputColorSpaceConfig.drawingBufferColorSpace},_getUnpackColorSpace:function(t=this.workingColorSpace){return this.spaces[t].workingColorSpaceConfig.unpackColorSpace},fromWorkingColorSpace:function(e,i){return e5("THREE.ColorManagement: .fromWorkingColorSpace() has been renamed to .workingToColorSpace()."),t.workingToColorSpace(e,i)},toWorkingColorSpace:function(e,i){return e5("THREE.ColorManagement: .toWorkingColorSpace() has been renamed to .colorSpaceToWorking()."),t.colorSpaceToWorking(e,i)}},e=[.64,.33,.3,.6,.15,.06],i=[.2126,.7152,.0722],s=[.3127,.329];return t.define({[ev]:{primaries:e,whitePoint:s,transfer:eS,toXYZ:e6,fromXYZ:e8,luminanceCoefficients:i,workingColorSpaceConfig:{unpackColorSpace:ew},outputColorSpaceConfig:{drawingBufferColorSpace:ew}},[ew]:{primaries:e,whitePoint:s,transfer:e_,toXYZ:e6,fromXYZ:e8,luminanceCoefficients:i,outputColorSpaceConfig:{drawingBufferColorSpace:ew}}}),t}();function e7(t){return t<.04045?.0773993808*t:Math.pow(.9478672986*t+.0521327014,2.4)}function it(t){return t<.0031308?12.92*t:1.055*Math.pow(t,.41666)-.055}class ie{static getDataURL(t,e="image/png"){let i;if(/^data:/i.test(t.src)||"undefined"==typeof HTMLCanvasElement)return t.src;if(t instanceof HTMLCanvasElement)i=t;else{void 0===s&&(s=e1("canvas")),s.width=t.width,s.height=t.height;let e=s.getContext("2d");t instanceof ImageData?e.putImageData(t,0,0):e.drawImage(t,0,0,t.width,t.height),i=s}return i.toDataURL(e)}static sRGBToLinear(t){if("undefined"!=typeof HTMLImageElement&&t instanceof HTMLImageElement||"undefined"!=typeof HTMLCanvasElement&&t instanceof HTMLCanvasElement||"undefined"!=typeof ImageBitmap&&t instanceof ImageBitmap){let e=e1("canvas");e.width=t.width,e.height=t.height;let i=e.getContext("2d");i.drawImage(t,0,0,t.width,t.height);let s=i.getImageData(0,0,t.width,t.height),r=s.data;for(let t=0;t<r.length;t++)r[t]=255*e7(r[t]/255);return i.putImageData(s,0,0),e}if(!t.data)return console.warn("THREE.ImageUtils.sRGBToLinear(): Unsupported image type. No color space conversion applied."),t;{let e=t.data.slice(0);for(let t=0;t<e.length;t++)e instanceof Uint8Array||e instanceof Uint8ClampedArray?e[t]=Math.floor(255*e7(e[t]/255)):e[t]=e7(e[t]);return{data:e,width:t.width,height:t.height}}}}let ii=0;class is{constructor(t=null){this.isSource=!0,Object.defineProperty(this,"id",{value:ii++}),this.uuid=ej(),this.data=t,this.dataReady=!0,this.version=0}getSize(t){let e=this.data;return e instanceof HTMLVideoElement?t.set(e.videoWidth,e.videoHeight,0):e instanceof VideoFrame?t.set(e.displayHeight,e.displayWidth,0):null!==e?t.set(e.width,e.height,e.depth||0):t.set(0,0,0),t}set needsUpdate(t){!0===t&&this.version++}toJSON(t){let e=void 0===t||"string"==typeof t;if(!e&&void 0!==t.images[this.uuid])return t.images[this.uuid];let i={uuid:this.uuid,url:""},s=this.data;if(null!==s){let t;if(Array.isArray(s)){t=[];for(let e=0,i=s.length;e<i;e++)s[e].isDataTexture?t.push(ir(s[e].image)):t.push(ir(s[e]))}else t=ir(s);i.url=t}return e||(t.images[this.uuid]=i),i}}function ir(t){return"undefined"!=typeof HTMLImageElement&&t instanceof HTMLImageElement||"undefined"!=typeof HTMLCanvasElement&&t instanceof HTMLCanvasElement||"undefined"!=typeof ImageBitmap&&t instanceof ImageBitmap?ie.getDataURL(t):t.data?{data:Array.from(t.data),width:t.width,height:t.height,type:t.data.constructor.name}:(console.warn("THREE.Texture: Unable to serialize Texture."),{})}let ia=0,io=new eZ;class ih extends eN{constructor(t=ih.DEFAULT_IMAGE,e=ih.DEFAULT_MAPPING,i=tm,s=tm,r=tb,n=tw,a=tR,o=tv,h=ih.DEFAULT_ANISOTROPY,l=eM){super(),this.isTexture=!0,Object.defineProperty(this,"id",{value:ia++}),this.uuid=ej(),this.name="",this.source=new is(t),this.mipmaps=[],this.mapping=e,this.channel=0,this.wrapS=i,this.wrapT=s,this.magFilter=r,this.minFilter=n,this.anisotropy=h,this.format=a,this.internalFormat=null,this.type=o,this.offset=new eX(0,0),this.repeat=new eX(1,1),this.center=new eX(0,0),this.rotation=0,this.matrixAutoUpdate=!0,this.matrix=new eQ,this.generateMipmaps=!0,this.premultiplyAlpha=!1,this.flipY=!0,this.unpackAlignment=4,this.colorSpace=l,this.userData={},this.updateRanges=[],this.version=0,this.onUpdate=null,this.renderTarget=null,this.isRenderTargetTexture=!1,this.isArrayTexture=!!t&&!!t.depth&&t.depth>1,this.pmremVersion=0}get width(){return this.source.getSize(io).x}get height(){return this.source.getSize(io).y}get depth(){return this.source.getSize(io).z}get image(){return this.source.data}set image(t=null){this.source.data=t}updateMatrix(){this.matrix.setUvTransform(this.offset.x,this.offset.y,this.repeat.x,this.repeat.y,this.rotation,this.center.x,this.center.y)}addUpdateRange(t,e){this.updateRanges.push({start:t,count:e})}clearUpdateRanges(){this.updateRanges.length=0}clone(){return new this.constructor().copy(this)}copy(t){return this.name=t.name,this.source=t.source,this.mipmaps=t.mipmaps.slice(0),this.mapping=t.mapping,this.channel=t.channel,this.wrapS=t.wrapS,this.wrapT=t.wrapT,this.magFilter=t.magFilter,this.minFilter=t.minFilter,this.anisotropy=t.anisotropy,this.format=t.format,this.internalFormat=t.internalFormat,this.type=t.type,this.offset.copy(t.offset),this.repeat.copy(t.repeat),this.center.copy(t.center),this.rotation=t.rotation,this.matrixAutoUpdate=t.matrixAutoUpdate,this.matrix.copy(t.matrix),this.generateMipmaps=t.generateMipmaps,this.premultiplyAlpha=t.premultiplyAlpha,this.flipY=t.flipY,this.unpackAlignment=t.unpackAlignment,this.colorSpace=t.colorSpace,this.renderTarget=t.renderTarget,this.isRenderTargetTexture=t.isRenderTargetTexture,this.isArrayTexture=t.isArrayTexture,this.userData=JSON.parse(JSON.stringify(t.userData)),this.needsUpdate=!0,this}setValues(t){for(let e in t){let i=t[e];if(void 0===i){console.warn(`THREE.Texture.setValues(): parameter '${e}' has value of undefined.`);continue}let s=this[e];if(void 0===s){console.warn(`THREE.Texture.setValues(): property '${e}' does not exist.`);continue}s&&i&&s.isVector2&&i.isVector2||s&&i&&s.isVector3&&i.isVector3||s&&i&&s.isMatrix3&&i.isMatrix3?s.copy(i):this[e]=i}}toJSON(t){let e=void 0===t||"string"==typeof t;if(!e&&void 0!==t.textures[this.uuid])return t.textures[this.uuid];let i={metadata:{version:4.7,type:"Texture",generator:"Texture.toJSON"},uuid:this.uuid,name:this.name,image:this.source.toJSON(t).uuid,mapping:this.mapping,channel:this.channel,repeat:[this.repeat.x,this.repeat.y],offset:[this.offset.x,this.offset.y],center:[this.center.x,this.center.y],rotation:this.rotation,wrap:[this.wrapS,this.wrapT],format:this.format,internalFormat:this.internalFormat,type:this.type,colorSpace:this.colorSpace,minFilter:this.minFilter,magFilter:this.magFilter,anisotropy:this.anisotropy,flipY:this.flipY,generateMipmaps:this.generateMipmaps,premultiplyAlpha:this.premultiplyAlpha,unpackAlignment:this.unpackAlignment};return Object.keys(this.userData).length>0&&(i.userData=this.userData),e||(t.textures[this.uuid]=i),i}dispose(){this.dispatchEvent({type:"dispose"})}transformUv(t){if(300!==this.mapping)return t;if(t.applyMatrix3(this.matrix),t.x<0||t.x>1)switch(this.wrapS){case td:t.x=t.x-Math.floor(t.x);break;case tm:t.x=t.x<0?0:1;break;case ty:1===Math.abs(Math.floor(t.x)%2)?t.x=Math.ceil(t.x)-t.x:t.x=t.x-Math.floor(t.x)}if(t.y<0||t.y>1)switch(this.wrapT){case td:t.y=t.y-Math.floor(t.y);break;case tm:t.y=t.y<0?0:1;break;case ty:1===Math.abs(Math.floor(t.y)%2)?t.y=Math.ceil(t.y)-t.y:t.y=t.y-Math.floor(t.y)}return this.flipY&&(t.y=1-t.y),t}set needsUpdate(t){!0===t&&(this.version++,this.source.needsUpdate=!0)}set needsPMREMUpdate(t){!0===t&&this.pmremVersion++}}ih.DEFAULT_IMAGE=null,ih.DEFAULT_MAPPING=300,ih.DEFAULT_ANISOTROPY=1;class il{constructor(t=0,e=0,i=0,s=1){il.prototype.isVector4=!0,this.x=t,this.y=e,this.z=i,this.w=s}get width(){return this.z}set width(t){this.z=t}get height(){return this.w}set height(t){this.w=t}set(t,e,i,s){return this.x=t,this.y=e,this.z=i,this.w=s,this}setScalar(t){return this.x=t,this.y=t,this.z=t,this.w=t,this}setX(t){return this.x=t,this}setY(t){return this.y=t,this}setZ(t){return this.z=t,this}setW(t){return this.w=t,this}setComponent(t,e){switch(t){case 0:this.x=e;break;case 1:this.y=e;break;case 2:this.z=e;break;case 3:this.w=e;break;default:throw Error("index is out of range: "+t)}return this}getComponent(t){switch(t){case 0:return this.x;case 1:return this.y;case 2:return this.z;case 3:return this.w;default:throw Error("index is out of range: "+t)}}clone(){return new this.constructor(this.x,this.y,this.z,this.w)}copy(t){return this.x=t.x,this.y=t.y,this.z=t.z,this.w=void 0!==t.w?t.w:1,this}add(t){return this.x+=t.x,this.y+=t.y,this.z+=t.z,this.w+=t.w,this}addScalar(t){return this.x+=t,this.y+=t,this.z+=t,this.w+=t,this}addVectors(t,e){return this.x=t.x+e.x,this.y=t.y+e.y,this.z=t.z+e.z,this.w=t.w+e.w,this}addScaledVector(t,e){return this.x+=t.x*e,this.y+=t.y*e,this.z+=t.z*e,this.w+=t.w*e,this}sub(t){return this.x-=t.x,this.y-=t.y,this.z-=t.z,this.w-=t.w,this}subScalar(t){return this.x-=t,this.y-=t,this.z-=t,this.w-=t,this}subVectors(t,e){return this.x=t.x-e.x,this.y=t.y-e.y,this.z=t.z-e.z,this.w=t.w-e.w,this}multiply(t){return this.x*=t.x,this.y*=t.y,this.z*=t.z,this.w*=t.w,this}multiplyScalar(t){return this.x*=t,this.y*=t,this.z*=t,this.w*=t,this}applyMatrix4(t){let e=this.x,i=this.y,s=this.z,r=this.w,n=t.elements;return this.x=n[0]*e+n[4]*i+n[8]*s+n[12]*r,this.y=n[1]*e+n[5]*i+n[9]*s+n[13]*r,this.z=n[2]*e+n[6]*i+n[10]*s+n[14]*r,this.w=n[3]*e+n[7]*i+n[11]*s+n[15]*r,this}divide(t){return this.x/=t.x,this.y/=t.y,this.z/=t.z,this.w/=t.w,this}divideScalar(t){return this.multiplyScalar(1/t)}setAxisAngleFromQuaternion(t){this.w=2*Math.acos(t.w);let e=Math.sqrt(1-t.w*t.w);return e<1e-4?(this.x=1,this.y=0,this.z=0):(this.x=t.x/e,this.y=t.y/e,this.z=t.z/e),this}setAxisAngleFromRotationMatrix(t){let e,i,s,r,n=t.elements,a=n[0],o=n[4],h=n[8],l=n[1],u=n[5],c=n[9],p=n[2],d=n[6],m=n[10];if(.01>Math.abs(o-l)&&.01>Math.abs(h-p)&&.01>Math.abs(c-d)){if(.1>Math.abs(o+l)&&.1>Math.abs(h+p)&&.1>Math.abs(c+d)&&.1>Math.abs(a+u+m-3))return this.set(1,0,0,0),this;e=Math.PI;let t=(a+1)/2,n=(u+1)/2,y=(m+1)/2,f=(o+l)/4,g=(h+p)/4,x=(c+d)/4;return t>n&&t>y?t<.01?(i=0,s=.*********,r=.*********):(s=f/(i=Math.sqrt(t)),r=g/i):n>y?n<.01?(i=.*********,s=0,r=.*********):(i=f/(s=Math.sqrt(n)),r=x/s):y<.01?(i=.*********,s=.*********,r=0):(i=g/(r=Math.sqrt(y)),s=x/r),this.set(i,s,r,e),this}let y=Math.sqrt((d-c)*(d-c)+(h-p)*(h-p)+(l-o)*(l-o));return .001>Math.abs(y)&&(y=1),this.x=(d-c)/y,this.y=(h-p)/y,this.z=(l-o)/y,this.w=Math.acos((a+u+m-1)/2),this}setFromMatrixPosition(t){let e=t.elements;return this.x=e[12],this.y=e[13],this.z=e[14],this.w=e[15],this}min(t){return this.x=Math.min(this.x,t.x),this.y=Math.min(this.y,t.y),this.z=Math.min(this.z,t.z),this.w=Math.min(this.w,t.w),this}max(t){return this.x=Math.max(this.x,t.x),this.y=Math.max(this.y,t.y),this.z=Math.max(this.z,t.z),this.w=Math.max(this.w,t.w),this}clamp(t,e){return this.x=eD(this.x,t.x,e.x),this.y=eD(this.y,t.y,e.y),this.z=eD(this.z,t.z,e.z),this.w=eD(this.w,t.w,e.w),this}clampScalar(t,e){return this.x=eD(this.x,t,e),this.y=eD(this.y,t,e),this.z=eD(this.z,t,e),this.w=eD(this.w,t,e),this}clampLength(t,e){let i=this.length();return this.divideScalar(i||1).multiplyScalar(eD(i,t,e))}floor(){return this.x=Math.floor(this.x),this.y=Math.floor(this.y),this.z=Math.floor(this.z),this.w=Math.floor(this.w),this}ceil(){return this.x=Math.ceil(this.x),this.y=Math.ceil(this.y),this.z=Math.ceil(this.z),this.w=Math.ceil(this.w),this}round(){return this.x=Math.round(this.x),this.y=Math.round(this.y),this.z=Math.round(this.z),this.w=Math.round(this.w),this}roundToZero(){return this.x=Math.trunc(this.x),this.y=Math.trunc(this.y),this.z=Math.trunc(this.z),this.w=Math.trunc(this.w),this}negate(){return this.x=-this.x,this.y=-this.y,this.z=-this.z,this.w=-this.w,this}dot(t){return this.x*t.x+this.y*t.y+this.z*t.z+this.w*t.w}lengthSq(){return this.x*this.x+this.y*this.y+this.z*this.z+this.w*this.w}length(){return Math.sqrt(this.x*this.x+this.y*this.y+this.z*this.z+this.w*this.w)}manhattanLength(){return Math.abs(this.x)+Math.abs(this.y)+Math.abs(this.z)+Math.abs(this.w)}normalize(){return this.divideScalar(this.length()||1)}setLength(t){return this.normalize().multiplyScalar(t)}lerp(t,e){return this.x+=(t.x-this.x)*e,this.y+=(t.y-this.y)*e,this.z+=(t.z-this.z)*e,this.w+=(t.w-this.w)*e,this}lerpVectors(t,e,i){return this.x=t.x+(e.x-t.x)*i,this.y=t.y+(e.y-t.y)*i,this.z=t.z+(e.z-t.z)*i,this.w=t.w+(e.w-t.w)*i,this}equals(t){return t.x===this.x&&t.y===this.y&&t.z===this.z&&t.w===this.w}fromArray(t,e=0){return this.x=t[e],this.y=t[e+1],this.z=t[e+2],this.w=t[e+3],this}toArray(t=[],e=0){return t[e]=this.x,t[e+1]=this.y,t[e+2]=this.z,t[e+3]=this.w,t}fromBufferAttribute(t,e){return this.x=t.getX(e),this.y=t.getY(e),this.z=t.getZ(e),this.w=t.getW(e),this}random(){return this.x=Math.random(),this.y=Math.random(),this.z=Math.random(),this.w=Math.random(),this}*[Symbol.iterator](){yield this.x,yield this.y,yield this.z,yield this.w}}class iu extends eN{constructor(t=1,e=1,i={}){super(),i=Object.assign({generateMipmaps:!1,internalFormat:null,minFilter:tb,depthBuffer:!0,stencilBuffer:!1,resolveDepthBuffer:!0,resolveStencilBuffer:!0,depthTexture:null,samples:0,count:1,depth:1,multiview:!1},i),this.isRenderTarget=!0,this.width=t,this.height=e,this.depth=i.depth,this.scissor=new il(0,0,t,e),this.scissorTest=!1,this.viewport=new il(0,0,t,e);let s=new ih({width:t,height:e,depth:i.depth});this.textures=[];let r=i.count;for(let t=0;t<r;t++)this.textures[t]=s.clone(),this.textures[t].isRenderTargetTexture=!0,this.textures[t].renderTarget=this;this._setTextureOptions(i),this.depthBuffer=i.depthBuffer,this.stencilBuffer=i.stencilBuffer,this.resolveDepthBuffer=i.resolveDepthBuffer,this.resolveStencilBuffer=i.resolveStencilBuffer,this._depthTexture=null,this.depthTexture=i.depthTexture,this.samples=i.samples,this.multiview=i.multiview}_setTextureOptions(t={}){let e={minFilter:tb,generateMipmaps:!1,flipY:!1,internalFormat:null};void 0!==t.mapping&&(e.mapping=t.mapping),void 0!==t.wrapS&&(e.wrapS=t.wrapS),void 0!==t.wrapT&&(e.wrapT=t.wrapT),void 0!==t.wrapR&&(e.wrapR=t.wrapR),void 0!==t.magFilter&&(e.magFilter=t.magFilter),void 0!==t.minFilter&&(e.minFilter=t.minFilter),void 0!==t.format&&(e.format=t.format),void 0!==t.type&&(e.type=t.type),void 0!==t.anisotropy&&(e.anisotropy=t.anisotropy),void 0!==t.colorSpace&&(e.colorSpace=t.colorSpace),void 0!==t.flipY&&(e.flipY=t.flipY),void 0!==t.generateMipmaps&&(e.generateMipmaps=t.generateMipmaps),void 0!==t.internalFormat&&(e.internalFormat=t.internalFormat);for(let t=0;t<this.textures.length;t++)this.textures[t].setValues(e)}get texture(){return this.textures[0]}set texture(t){this.textures[0]=t}set depthTexture(t){null!==this._depthTexture&&(this._depthTexture.renderTarget=null),null!==t&&(t.renderTarget=this),this._depthTexture=t}get depthTexture(){return this._depthTexture}setSize(t,e,i=1){if(this.width!==t||this.height!==e||this.depth!==i){this.width=t,this.height=e,this.depth=i;for(let s=0,r=this.textures.length;s<r;s++)this.textures[s].image.width=t,this.textures[s].image.height=e,this.textures[s].image.depth=i,this.textures[s].isArrayTexture=this.textures[s].image.depth>1;this.dispose()}this.viewport.set(0,0,t,e),this.scissor.set(0,0,t,e)}clone(){return new this.constructor().copy(this)}copy(t){this.width=t.width,this.height=t.height,this.depth=t.depth,this.scissor.copy(t.scissor),this.scissorTest=t.scissorTest,this.viewport.copy(t.viewport),this.textures.length=0;for(let e=0,i=t.textures.length;e<i;e++){this.textures[e]=t.textures[e].clone(),this.textures[e].isRenderTargetTexture=!0,this.textures[e].renderTarget=this;let i=Object.assign({},t.textures[e].image);this.textures[e].source=new is(i)}return this.depthBuffer=t.depthBuffer,this.stencilBuffer=t.stencilBuffer,this.resolveDepthBuffer=t.resolveDepthBuffer,this.resolveStencilBuffer=t.resolveStencilBuffer,null!==t.depthTexture&&(this.depthTexture=t.depthTexture.clone()),this.samples=t.samples,this}dispose(){this.dispatchEvent({type:"dispose"})}}class ic extends iu{constructor(t=1,e=1,i={}){super(t,e,i),this.isWebGLRenderTarget=!0}}class ip extends ih{constructor(t=null,e=1,i=1,s=1){super(null),this.isDataArrayTexture=!0,this.image={data:t,width:e,height:i,depth:s},this.magFilter=tf,this.minFilter=tf,this.wrapR=tm,this.generateMipmaps=!1,this.flipY=!1,this.unpackAlignment=1,this.layerUpdates=new Set}addLayerUpdate(t){this.layerUpdates.add(t)}clearLayerUpdates(){this.layerUpdates.clear()}}class id extends ih{constructor(t=null,e=1,i=1,s=1){super(null),this.isData3DTexture=!0,this.image={data:t,width:e,height:i,depth:s},this.magFilter=tf,this.minFilter=tf,this.wrapR=tm,this.generateMipmaps=!1,this.flipY=!1,this.unpackAlignment=1}}class im{constructor(t=new eZ(Infinity,Infinity,Infinity),e=new eZ(-1/0,-1/0,-1/0)){this.isBox3=!0,this.min=t,this.max=e}set(t,e){return this.min.copy(t),this.max.copy(e),this}setFromArray(t){this.makeEmpty();for(let e=0,i=t.length;e<i;e+=3)this.expandByPoint(ig.fromArray(t,e));return this}setFromBufferAttribute(t){this.makeEmpty();for(let e=0,i=t.count;e<i;e++)this.expandByPoint(ig.fromBufferAttribute(t,e));return this}setFromPoints(t){this.makeEmpty();for(let e=0,i=t.length;e<i;e++)this.expandByPoint(t[e]);return this}setFromCenterAndSize(t,e){let i=ig.copy(e).multiplyScalar(.5);return this.min.copy(t).sub(i),this.max.copy(t).add(i),this}setFromObject(t,e=!1){return this.makeEmpty(),this.expandByObject(t,e)}clone(){return new this.constructor().copy(this)}copy(t){return this.min.copy(t.min),this.max.copy(t.max),this}makeEmpty(){return this.min.x=this.min.y=this.min.z=Infinity,this.max.x=this.max.y=this.max.z=-1/0,this}isEmpty(){return this.max.x<this.min.x||this.max.y<this.min.y||this.max.z<this.min.z}getCenter(t){return this.isEmpty()?t.set(0,0,0):t.addVectors(this.min,this.max).multiplyScalar(.5)}getSize(t){return this.isEmpty()?t.set(0,0,0):t.subVectors(this.max,this.min)}expandByPoint(t){return this.min.min(t),this.max.max(t),this}expandByVector(t){return this.min.sub(t),this.max.add(t),this}expandByScalar(t){return this.min.addScalar(-t),this.max.addScalar(t),this}expandByObject(t,e=!1){t.updateWorldMatrix(!1,!1);let i=t.geometry;if(void 0!==i){let s=i.getAttribute("position");if(!0===e&&void 0!==s&&!0!==t.isInstancedMesh)for(let e=0,i=s.count;e<i;e++)!0===t.isMesh?t.getVertexPosition(e,ig):ig.fromBufferAttribute(s,e),ig.applyMatrix4(t.matrixWorld),this.expandByPoint(ig);else void 0!==t.boundingBox?(null===t.boundingBox&&t.computeBoundingBox(),ix.copy(t.boundingBox)):(null===i.boundingBox&&i.computeBoundingBox(),ix.copy(i.boundingBox)),ix.applyMatrix4(t.matrixWorld),this.union(ix)}let s=t.children;for(let t=0,i=s.length;t<i;t++)this.expandByObject(s[t],e);return this}containsPoint(t){return t.x>=this.min.x&&t.x<=this.max.x&&t.y>=this.min.y&&t.y<=this.max.y&&t.z>=this.min.z&&t.z<=this.max.z}containsBox(t){return this.min.x<=t.min.x&&t.max.x<=this.max.x&&this.min.y<=t.min.y&&t.max.y<=this.max.y&&this.min.z<=t.min.z&&t.max.z<=this.max.z}getParameter(t,e){return e.set((t.x-this.min.x)/(this.max.x-this.min.x),(t.y-this.min.y)/(this.max.y-this.min.y),(t.z-this.min.z)/(this.max.z-this.min.z))}intersectsBox(t){return t.max.x>=this.min.x&&t.min.x<=this.max.x&&t.max.y>=this.min.y&&t.min.y<=this.max.y&&t.max.z>=this.min.z&&t.min.z<=this.max.z}intersectsSphere(t){return this.clampPoint(t.center,ig),ig.distanceToSquared(t.center)<=t.radius*t.radius}intersectsPlane(t){let e,i;return t.normal.x>0?(e=t.normal.x*this.min.x,i=t.normal.x*this.max.x):(e=t.normal.x*this.max.x,i=t.normal.x*this.min.x),t.normal.y>0?(e+=t.normal.y*this.min.y,i+=t.normal.y*this.max.y):(e+=t.normal.y*this.max.y,i+=t.normal.y*this.min.y),t.normal.z>0?(e+=t.normal.z*this.min.z,i+=t.normal.z*this.max.z):(e+=t.normal.z*this.max.z,i+=t.normal.z*this.min.z),e<=-t.constant&&i>=-t.constant}intersectsTriangle(t){if(this.isEmpty())return!1;this.getCenter(iz),iA.subVectors(this.max,iz),ib.subVectors(t.a,iz),iM.subVectors(t.b,iz),iw.subVectors(t.c,iz),iv.subVectors(iM,ib),iS.subVectors(iw,iM),i_.subVectors(ib,iw);let e=[0,-iv.z,iv.y,0,-iS.z,iS.y,0,-i_.z,i_.y,iv.z,0,-iv.x,iS.z,0,-iS.x,i_.z,0,-i_.x,-iv.y,iv.x,0,-iS.y,iS.x,0,-i_.y,i_.x,0];return!!ik(e,ib,iM,iw,iA)&&!!ik(e=[1,0,0,0,1,0,0,0,1],ib,iM,iw,iA)&&(iT.crossVectors(iv,iS),ik(e=[iT.x,iT.y,iT.z],ib,iM,iw,iA))}clampPoint(t,e){return e.copy(t).clamp(this.min,this.max)}distanceToPoint(t){return this.clampPoint(t,ig).distanceTo(t)}getBoundingSphere(t){return this.isEmpty()?t.makeEmpty():(this.getCenter(t.center),t.radius=.5*this.getSize(ig).length()),t}intersect(t){return this.min.max(t.min),this.max.min(t.max),this.isEmpty()&&this.makeEmpty(),this}union(t){return this.min.min(t.min),this.max.max(t.max),this}applyMatrix4(t){return this.isEmpty()||(iy[0].set(this.min.x,this.min.y,this.min.z).applyMatrix4(t),iy[1].set(this.min.x,this.min.y,this.max.z).applyMatrix4(t),iy[2].set(this.min.x,this.max.y,this.min.z).applyMatrix4(t),iy[3].set(this.min.x,this.max.y,this.max.z).applyMatrix4(t),iy[4].set(this.max.x,this.min.y,this.min.z).applyMatrix4(t),iy[5].set(this.max.x,this.min.y,this.max.z).applyMatrix4(t),iy[6].set(this.max.x,this.max.y,this.min.z).applyMatrix4(t),iy[7].set(this.max.x,this.max.y,this.max.z).applyMatrix4(t),this.setFromPoints(iy)),this}translate(t){return this.min.add(t),this.max.add(t),this}equals(t){return t.min.equals(this.min)&&t.max.equals(this.max)}toJSON(){return{min:this.min.toArray(),max:this.max.toArray()}}fromJSON(t){return this.min.fromArray(t.min),this.max.fromArray(t.max),this}}let iy=[new eZ,new eZ,new eZ,new eZ,new eZ,new eZ,new eZ,new eZ],ig=new eZ,ix=new im,ib=new eZ,iM=new eZ,iw=new eZ,iv=new eZ,iS=new eZ,i_=new eZ,iz=new eZ,iA=new eZ,iT=new eZ,iC=new eZ;function ik(t,e,i,s,r){for(let n=0,a=t.length-3;n<=a;n+=3){iC.fromArray(t,n);let a=r.x*Math.abs(iC.x)+r.y*Math.abs(iC.y)+r.z*Math.abs(iC.z),o=e.dot(iC),h=i.dot(iC),l=s.dot(iC);if(Math.max(-Math.max(o,h,l),Math.min(o,h,l))>a)return!1}return!0}let iE=new im,iO=new eZ,iB=new eZ;class iP{constructor(t=new eZ,e=-1){this.isSphere=!0,this.center=t,this.radius=e}set(t,e){return this.center.copy(t),this.radius=e,this}setFromPoints(t,e){let i=this.center;void 0!==e?i.copy(e):iE.setFromPoints(t).getCenter(i);let s=0;for(let e=0,r=t.length;e<r;e++)s=Math.max(s,i.distanceToSquared(t[e]));return this.radius=Math.sqrt(s),this}copy(t){return this.center.copy(t.center),this.radius=t.radius,this}isEmpty(){return this.radius<0}makeEmpty(){return this.center.set(0,0,0),this.radius=-1,this}containsPoint(t){return t.distanceToSquared(this.center)<=this.radius*this.radius}distanceToPoint(t){return t.distanceTo(this.center)-this.radius}intersectsSphere(t){let e=this.radius+t.radius;return t.center.distanceToSquared(this.center)<=e*e}intersectsBox(t){return t.intersectsSphere(this)}intersectsPlane(t){return Math.abs(t.distanceToPoint(this.center))<=this.radius}clampPoint(t,e){let i=this.center.distanceToSquared(t);return e.copy(t),i>this.radius*this.radius&&(e.sub(this.center).normalize(),e.multiplyScalar(this.radius).add(this.center)),e}getBoundingBox(t){return this.isEmpty()?t.makeEmpty():(t.set(this.center,this.center),t.expandByScalar(this.radius)),t}applyMatrix4(t){return this.center.applyMatrix4(t),this.radius=this.radius*t.getMaxScaleOnAxis(),this}translate(t){return this.center.add(t),this}expandByPoint(t){if(this.isEmpty())return this.center.copy(t),this.radius=0,this;iO.subVectors(t,this.center);let e=iO.lengthSq();if(e>this.radius*this.radius){let t=Math.sqrt(e),i=(t-this.radius)*.5;this.center.addScaledVector(iO,i/t),this.radius+=i}return this}union(t){return t.isEmpty()||(this.isEmpty()?this.copy(t):!0===this.center.equals(t.center)?this.radius=Math.max(this.radius,t.radius):(iB.subVectors(t.center,this.center).setLength(t.radius),this.expandByPoint(iO.copy(t.center).add(iB)),this.expandByPoint(iO.copy(t.center).sub(iB)))),this}equals(t){return t.center.equals(this.center)&&t.radius===this.radius}clone(){return new this.constructor().copy(this)}toJSON(){return{radius:this.radius,center:this.center.toArray()}}fromJSON(t){return this.radius=t.radius,this.center.fromArray(t.center),this}}let iI=new eZ,iN=new eZ,iR=new eZ,iV=new eZ,iL=new eZ,iF=new eZ,ij=new eZ;class iD{constructor(t=new eZ,e=new eZ(0,0,-1)){this.origin=t,this.direction=e}set(t,e){return this.origin.copy(t),this.direction.copy(e),this}copy(t){return this.origin.copy(t.origin),this.direction.copy(t.direction),this}at(t,e){return e.copy(this.origin).addScaledVector(this.direction,t)}lookAt(t){return this.direction.copy(t).sub(this.origin).normalize(),this}recast(t){return this.origin.copy(this.at(t,iI)),this}closestPointToPoint(t,e){e.subVectors(t,this.origin);let i=e.dot(this.direction);return i<0?e.copy(this.origin):e.copy(this.origin).addScaledVector(this.direction,i)}distanceToPoint(t){return Math.sqrt(this.distanceSqToPoint(t))}distanceSqToPoint(t){let e=iI.subVectors(t,this.origin).dot(this.direction);return e<0?this.origin.distanceToSquared(t):(iI.copy(this.origin).addScaledVector(this.direction,e),iI.distanceToSquared(t))}distanceSqToSegment(t,e,i,s){let r,n,a,o;iN.copy(t).add(e).multiplyScalar(.5),iR.copy(e).sub(t).normalize(),iV.copy(this.origin).sub(iN);let h=.5*t.distanceTo(e),l=-this.direction.dot(iR),u=iV.dot(this.direction),c=-iV.dot(iR),p=iV.lengthSq(),d=Math.abs(1-l*l);if(d>0)if(r=l*c-u,n=l*u-c,o=h*d,r>=0)if(n>=-o)if(n<=o){let t=1/d;r*=t,n*=t,a=r*(r+l*n+2*u)+n*(l*r+n+2*c)+p}else a=-(r=Math.max(0,-(l*(n=h)+u)))*r+n*(n+2*c)+p;else a=-(r=Math.max(0,-(l*(n=-h)+u)))*r+n*(n+2*c)+p;else n<=-o?(n=(r=Math.max(0,-(-l*h+u)))>0?-h:Math.min(Math.max(-h,-c),h),a=-r*r+n*(n+2*c)+p):n<=o?(r=0,a=(n=Math.min(Math.max(-h,-c),h))*(n+2*c)+p):(n=(r=Math.max(0,-(l*h+u)))>0?h:Math.min(Math.max(-h,-c),h),a=-r*r+n*(n+2*c)+p);else n=l>0?-h:h,a=-(r=Math.max(0,-(l*n+u)))*r+n*(n+2*c)+p;return i&&i.copy(this.origin).addScaledVector(this.direction,r),s&&s.copy(iN).addScaledVector(iR,n),a}intersectSphere(t,e){iI.subVectors(t.center,this.origin);let i=iI.dot(this.direction),s=iI.dot(iI)-i*i,r=t.radius*t.radius;if(s>r)return null;let n=Math.sqrt(r-s),a=i-n,o=i+n;return o<0?null:a<0?this.at(o,e):this.at(a,e)}intersectsSphere(t){return!(t.radius<0)&&this.distanceSqToPoint(t.center)<=t.radius*t.radius}distanceToPlane(t){let e=t.normal.dot(this.direction);if(0===e)return 0===t.distanceToPoint(this.origin)?0:null;let i=-(this.origin.dot(t.normal)+t.constant)/e;return i>=0?i:null}intersectPlane(t,e){let i=this.distanceToPlane(t);return null===i?null:this.at(i,e)}intersectsPlane(t){let e=t.distanceToPoint(this.origin);return!!(0===e||t.normal.dot(this.direction)*e<0)}intersectBox(t,e){let i,s,r,n,a,o,h=1/this.direction.x,l=1/this.direction.y,u=1/this.direction.z,c=this.origin;return(h>=0?(i=(t.min.x-c.x)*h,s=(t.max.x-c.x)*h):(i=(t.max.x-c.x)*h,s=(t.min.x-c.x)*h),l>=0?(r=(t.min.y-c.y)*l,n=(t.max.y-c.y)*l):(r=(t.max.y-c.y)*l,n=(t.min.y-c.y)*l),i>n||r>s||((r>i||isNaN(i))&&(i=r),(n<s||isNaN(s))&&(s=n),u>=0?(a=(t.min.z-c.z)*u,o=(t.max.z-c.z)*u):(a=(t.max.z-c.z)*u,o=(t.min.z-c.z)*u),i>o||a>s||((a>i||i!=i)&&(i=a),(o<s||s!=s)&&(s=o),s<0)))?null:this.at(i>=0?i:s,e)}intersectsBox(t){return null!==this.intersectBox(t,iI)}intersectTriangle(t,e,i,s,r){let n;iL.subVectors(e,t),iF.subVectors(i,t),ij.crossVectors(iL,iF);let a=this.direction.dot(ij);if(a>0){if(s)return null;n=1}else{if(!(a<0))return null;n=-1,a=-a}iV.subVectors(this.origin,t);let o=n*this.direction.dot(iF.crossVectors(iV,iF));if(o<0)return null;let h=n*this.direction.dot(iL.cross(iV));if(h<0||o+h>a)return null;let l=-n*iV.dot(ij);return l<0?null:this.at(l/a,r)}applyMatrix4(t){return this.origin.applyMatrix4(t),this.direction.transformDirection(t),this}equals(t){return t.origin.equals(this.origin)&&t.direction.equals(this.direction)}clone(){return new this.constructor().copy(this)}}class iW{constructor(t,e,i,s,r,n,a,o,h,l,u,c,p,d,m,y){iW.prototype.isMatrix4=!0,this.elements=[1,0,0,0,0,1,0,0,0,0,1,0,0,0,0,1],void 0!==t&&this.set(t,e,i,s,r,n,a,o,h,l,u,c,p,d,m,y)}set(t,e,i,s,r,n,a,o,h,l,u,c,p,d,m,y){let f=this.elements;return f[0]=t,f[4]=e,f[8]=i,f[12]=s,f[1]=r,f[5]=n,f[9]=a,f[13]=o,f[2]=h,f[6]=l,f[10]=u,f[14]=c,f[3]=p,f[7]=d,f[11]=m,f[15]=y,this}identity(){return this.set(1,0,0,0,0,1,0,0,0,0,1,0,0,0,0,1),this}clone(){return new iW().fromArray(this.elements)}copy(t){let e=this.elements,i=t.elements;return e[0]=i[0],e[1]=i[1],e[2]=i[2],e[3]=i[3],e[4]=i[4],e[5]=i[5],e[6]=i[6],e[7]=i[7],e[8]=i[8],e[9]=i[9],e[10]=i[10],e[11]=i[11],e[12]=i[12],e[13]=i[13],e[14]=i[14],e[15]=i[15],this}copyPosition(t){let e=this.elements,i=t.elements;return e[12]=i[12],e[13]=i[13],e[14]=i[14],this}setFromMatrix3(t){let e=t.elements;return this.set(e[0],e[3],e[6],0,e[1],e[4],e[7],0,e[2],e[5],e[8],0,0,0,0,1),this}extractBasis(t,e,i){return t.setFromMatrixColumn(this,0),e.setFromMatrixColumn(this,1),i.setFromMatrixColumn(this,2),this}makeBasis(t,e,i){return this.set(t.x,e.x,i.x,0,t.y,e.y,i.y,0,t.z,e.z,i.z,0,0,0,0,1),this}extractRotation(t){let e=this.elements,i=t.elements,s=1/iU.setFromMatrixColumn(t,0).length(),r=1/iU.setFromMatrixColumn(t,1).length(),n=1/iU.setFromMatrixColumn(t,2).length();return e[0]=i[0]*s,e[1]=i[1]*s,e[2]=i[2]*s,e[3]=0,e[4]=i[4]*r,e[5]=i[5]*r,e[6]=i[6]*r,e[7]=0,e[8]=i[8]*n,e[9]=i[9]*n,e[10]=i[10]*n,e[11]=0,e[12]=0,e[13]=0,e[14]=0,e[15]=1,this}makeRotationFromEuler(t){let e=this.elements,i=t.x,s=t.y,r=t.z,n=Math.cos(i),a=Math.sin(i),o=Math.cos(s),h=Math.sin(s),l=Math.cos(r),u=Math.sin(r);if("XYZ"===t.order){let t=n*l,i=n*u,s=a*l,r=a*u;e[0]=o*l,e[4]=-o*u,e[8]=h,e[1]=i+s*h,e[5]=t-r*h,e[9]=-a*o,e[2]=r-t*h,e[6]=s+i*h,e[10]=n*o}else if("YXZ"===t.order){let t=o*l,i=o*u,s=h*l,r=h*u;e[0]=t+r*a,e[4]=s*a-i,e[8]=n*h,e[1]=n*u,e[5]=n*l,e[9]=-a,e[2]=i*a-s,e[6]=r+t*a,e[10]=n*o}else if("ZXY"===t.order){let t=o*l,i=o*u,s=h*l,r=h*u;e[0]=t-r*a,e[4]=-n*u,e[8]=s+i*a,e[1]=i+s*a,e[5]=n*l,e[9]=r-t*a,e[2]=-n*h,e[6]=a,e[10]=n*o}else if("ZYX"===t.order){let t=n*l,i=n*u,s=a*l,r=a*u;e[0]=o*l,e[4]=s*h-i,e[8]=t*h+r,e[1]=o*u,e[5]=r*h+t,e[9]=i*h-s,e[2]=-h,e[6]=a*o,e[10]=n*o}else if("YZX"===t.order){let t=n*o,i=n*h,s=a*o,r=a*h;e[0]=o*l,e[4]=r-t*u,e[8]=s*u+i,e[1]=u,e[5]=n*l,e[9]=-a*l,e[2]=-h*l,e[6]=i*u+s,e[10]=t-r*u}else if("XZY"===t.order){let t=n*o,i=n*h,s=a*o,r=a*h;e[0]=o*l,e[4]=-u,e[8]=h*l,e[1]=t*u+r,e[5]=n*l,e[9]=i*u-s,e[2]=s*u-i,e[6]=a*l,e[10]=r*u+t}return e[3]=0,e[7]=0,e[11]=0,e[12]=0,e[13]=0,e[14]=0,e[15]=1,this}makeRotationFromQuaternion(t){return this.compose(iq,t,iH)}lookAt(t,e,i){let s=this.elements;return iZ.subVectors(t,e),0===iZ.lengthSq()&&(iZ.z=1),iZ.normalize(),iX.crossVectors(i,iZ),0===iX.lengthSq()&&(1===Math.abs(i.z)?iZ.x+=1e-4:iZ.z+=1e-4,iZ.normalize(),iX.crossVectors(i,iZ)),iX.normalize(),iY.crossVectors(iZ,iX),s[0]=iX.x,s[4]=iY.x,s[8]=iZ.x,s[1]=iX.y,s[5]=iY.y,s[9]=iZ.y,s[2]=iX.z,s[6]=iY.z,s[10]=iZ.z,this}multiply(t){return this.multiplyMatrices(this,t)}premultiply(t){return this.multiplyMatrices(t,this)}multiplyMatrices(t,e){let i=t.elements,s=e.elements,r=this.elements,n=i[0],a=i[4],o=i[8],h=i[12],l=i[1],u=i[5],c=i[9],p=i[13],d=i[2],m=i[6],y=i[10],f=i[14],g=i[3],x=i[7],b=i[11],M=i[15],w=s[0],v=s[4],S=s[8],_=s[12],z=s[1],A=s[5],T=s[9],C=s[13],k=s[2],E=s[6],O=s[10],B=s[14],P=s[3],I=s[7],N=s[11],R=s[15];return r[0]=n*w+a*z+o*k+h*P,r[4]=n*v+a*A+o*E+h*I,r[8]=n*S+a*T+o*O+h*N,r[12]=n*_+a*C+o*B+h*R,r[1]=l*w+u*z+c*k+p*P,r[5]=l*v+u*A+c*E+p*I,r[9]=l*S+u*T+c*O+p*N,r[13]=l*_+u*C+c*B+p*R,r[2]=d*w+m*z+y*k+f*P,r[6]=d*v+m*A+y*E+f*I,r[10]=d*S+m*T+y*O+f*N,r[14]=d*_+m*C+y*B+f*R,r[3]=g*w+x*z+b*k+M*P,r[7]=g*v+x*A+b*E+M*I,r[11]=g*S+x*T+b*O+M*N,r[15]=g*_+x*C+b*B+M*R,this}multiplyScalar(t){let e=this.elements;return e[0]*=t,e[4]*=t,e[8]*=t,e[12]*=t,e[1]*=t,e[5]*=t,e[9]*=t,e[13]*=t,e[2]*=t,e[6]*=t,e[10]*=t,e[14]*=t,e[3]*=t,e[7]*=t,e[11]*=t,e[15]*=t,this}determinant(){let t=this.elements,e=t[0],i=t[4],s=t[8],r=t[12],n=t[1],a=t[5],o=t[9],h=t[13],l=t[2],u=t[6],c=t[10],p=t[14],d=t[3],m=t[7];return d*(r*o*u-s*h*u-r*a*c+i*h*c+s*a*p-i*o*p)+m*(e*o*p-e*h*c+r*n*c-s*n*p+s*h*l-r*o*l)+t[11]*(e*h*u-e*a*p-r*n*u+i*n*p+r*a*l-i*h*l)+t[15]*(-s*a*l-e*o*u+e*a*c+s*n*u-i*n*c+i*o*l)}transpose(){let t,e=this.elements;return t=e[1],e[1]=e[4],e[4]=t,t=e[2],e[2]=e[8],e[8]=t,t=e[6],e[6]=e[9],e[9]=t,t=e[3],e[3]=e[12],e[12]=t,t=e[7],e[7]=e[13],e[13]=t,t=e[11],e[11]=e[14],e[14]=t,this}setPosition(t,e,i){let s=this.elements;return t.isVector3?(s[12]=t.x,s[13]=t.y,s[14]=t.z):(s[12]=t,s[13]=e,s[14]=i),this}invert(){let t=this.elements,e=t[0],i=t[1],s=t[2],r=t[3],n=t[4],a=t[5],o=t[6],h=t[7],l=t[8],u=t[9],c=t[10],p=t[11],d=t[12],m=t[13],y=t[14],f=t[15],g=u*y*h-m*c*h+m*o*p-a*y*p-u*o*f+a*c*f,x=d*c*h-l*y*h-d*o*p+n*y*p+l*o*f-n*c*f,b=l*m*h-d*u*h+d*a*p-n*m*p-l*a*f+n*u*f,M=d*u*o-l*m*o-d*a*c+n*m*c+l*a*y-n*u*y,w=e*g+i*x+s*b+r*M;if(0===w)return this.set(0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0);let v=1/w;return t[0]=g*v,t[1]=(m*c*r-u*y*r-m*s*p+i*y*p+u*s*f-i*c*f)*v,t[2]=(a*y*r-m*o*r+m*s*h-i*y*h-a*s*f+i*o*f)*v,t[3]=(u*o*r-a*c*r-u*s*h+i*c*h+a*s*p-i*o*p)*v,t[4]=x*v,t[5]=(l*y*r-d*c*r+d*s*p-e*y*p-l*s*f+e*c*f)*v,t[6]=(d*o*r-n*y*r-d*s*h+e*y*h+n*s*f-e*o*f)*v,t[7]=(n*c*r-l*o*r+l*s*h-e*c*h-n*s*p+e*o*p)*v,t[8]=b*v,t[9]=(d*u*r-l*m*r-d*i*p+e*m*p+l*i*f-e*u*f)*v,t[10]=(n*m*r-d*a*r+d*i*h-e*m*h-n*i*f+e*a*f)*v,t[11]=(l*a*r-n*u*r-l*i*h+e*u*h+n*i*p-e*a*p)*v,t[12]=M*v,t[13]=(l*m*s-d*u*s+d*i*c-e*m*c-l*i*y+e*u*y)*v,t[14]=(d*a*s-n*m*s-d*i*o+e*m*o+n*i*y-e*a*y)*v,t[15]=(n*u*s-l*a*s+l*i*o-e*u*o-n*i*c+e*a*c)*v,this}scale(t){let e=this.elements,i=t.x,s=t.y,r=t.z;return e[0]*=i,e[4]*=s,e[8]*=r,e[1]*=i,e[5]*=s,e[9]*=r,e[2]*=i,e[6]*=s,e[10]*=r,e[3]*=i,e[7]*=s,e[11]*=r,this}getMaxScaleOnAxis(){let t=this.elements,e=t[0]*t[0]+t[1]*t[1]+t[2]*t[2];return Math.sqrt(Math.max(e,t[4]*t[4]+t[5]*t[5]+t[6]*t[6],t[8]*t[8]+t[9]*t[9]+t[10]*t[10]))}makeTranslation(t,e,i){return t.isVector3?this.set(1,0,0,t.x,0,1,0,t.y,0,0,1,t.z,0,0,0,1):this.set(1,0,0,t,0,1,0,e,0,0,1,i,0,0,0,1),this}makeRotationX(t){let e=Math.cos(t),i=Math.sin(t);return this.set(1,0,0,0,0,e,-i,0,0,i,e,0,0,0,0,1),this}makeRotationY(t){let e=Math.cos(t),i=Math.sin(t);return this.set(e,0,i,0,0,1,0,0,-i,0,e,0,0,0,0,1),this}makeRotationZ(t){let e=Math.cos(t),i=Math.sin(t);return this.set(e,-i,0,0,i,e,0,0,0,0,1,0,0,0,0,1),this}makeRotationAxis(t,e){let i=Math.cos(e),s=Math.sin(e),r=1-i,n=t.x,a=t.y,o=t.z,h=r*n,l=r*a;return this.set(h*n+i,h*a-s*o,h*o+s*a,0,h*a+s*o,l*a+i,l*o-s*n,0,h*o-s*a,l*o+s*n,r*o*o+i,0,0,0,0,1),this}makeScale(t,e,i){return this.set(t,0,0,0,0,e,0,0,0,0,i,0,0,0,0,1),this}makeShear(t,e,i,s,r,n){return this.set(1,i,r,0,t,1,n,0,e,s,1,0,0,0,0,1),this}compose(t,e,i){let s=this.elements,r=e._x,n=e._y,a=e._z,o=e._w,h=r+r,l=n+n,u=a+a,c=r*h,p=r*l,d=r*u,m=n*l,y=n*u,f=a*u,g=o*h,x=o*l,b=o*u,M=i.x,w=i.y,v=i.z;return s[0]=(1-(m+f))*M,s[1]=(p+b)*M,s[2]=(d-x)*M,s[3]=0,s[4]=(p-b)*w,s[5]=(1-(c+f))*w,s[6]=(y+g)*w,s[7]=0,s[8]=(d+x)*v,s[9]=(y-g)*v,s[10]=(1-(c+m))*v,s[11]=0,s[12]=t.x,s[13]=t.y,s[14]=t.z,s[15]=1,this}decompose(t,e,i){let s=this.elements,r=iU.set(s[0],s[1],s[2]).length(),n=iU.set(s[4],s[5],s[6]).length(),a=iU.set(s[8],s[9],s[10]).length();0>this.determinant()&&(r=-r),t.x=s[12],t.y=s[13],t.z=s[14],iJ.copy(this);let o=1/r,h=1/n,l=1/a;return iJ.elements[0]*=o,iJ.elements[1]*=o,iJ.elements[2]*=o,iJ.elements[4]*=h,iJ.elements[5]*=h,iJ.elements[6]*=h,iJ.elements[8]*=l,iJ.elements[9]*=l,iJ.elements[10]*=l,e.setFromRotationMatrix(iJ),i.x=r,i.y=n,i.z=a,this}makePerspective(t,e,i,s,r,n,a=eI,o=!1){let h,l,u=this.elements;if(o)h=r/(n-r),l=n*r/(n-r);else if(a===eI)h=-(n+r)/(n-r),l=-2*n*r/(n-r);else if(2001===a)h=-n/(n-r),l=-n*r/(n-r);else throw Error("THREE.Matrix4.makePerspective(): Invalid coordinate system: "+a);return u[0]=2*r/(e-t),u[4]=0,u[8]=(e+t)/(e-t),u[12]=0,u[1]=0,u[5]=2*r/(i-s),u[9]=(i+s)/(i-s),u[13]=0,u[2]=0,u[6]=0,u[10]=h,u[14]=l,u[3]=0,u[7]=0,u[11]=-1,u[15]=0,this}makeOrthographic(t,e,i,s,r,n,a=eI,o=!1){let h,l,u=this.elements;if(o)h=1/(n-r),l=n/(n-r);else if(a===eI)h=-2/(n-r),l=-(n+r)/(n-r);else if(2001===a)h=-1/(n-r),l=-r/(n-r);else throw Error("THREE.Matrix4.makeOrthographic(): Invalid coordinate system: "+a);return u[0]=2/(e-t),u[4]=0,u[8]=0,u[12]=-(e+t)/(e-t),u[1]=0,u[5]=2/(i-s),u[9]=0,u[13]=-(i+s)/(i-s),u[2]=0,u[6]=0,u[10]=h,u[14]=l,u[3]=0,u[7]=0,u[11]=0,u[15]=1,this}equals(t){let e=this.elements,i=t.elements;for(let t=0;t<16;t++)if(e[t]!==i[t])return!1;return!0}fromArray(t,e=0){for(let i=0;i<16;i++)this.elements[i]=t[i+e];return this}toArray(t=[],e=0){let i=this.elements;return t[e]=i[0],t[e+1]=i[1],t[e+2]=i[2],t[e+3]=i[3],t[e+4]=i[4],t[e+5]=i[5],t[e+6]=i[6],t[e+7]=i[7],t[e+8]=i[8],t[e+9]=i[9],t[e+10]=i[10],t[e+11]=i[11],t[e+12]=i[12],t[e+13]=i[13],t[e+14]=i[14],t[e+15]=i[15],t}}let iU=new eZ,iJ=new iW,iq=new eZ(0,0,0),iH=new eZ(1,1,1),iX=new eZ,iY=new eZ,iZ=new eZ,iG=new iW,i$=new eY;class iQ{constructor(t=0,e=0,i=0,s=iQ.DEFAULT_ORDER){this.isEuler=!0,this._x=t,this._y=e,this._z=i,this._order=s}get x(){return this._x}set x(t){this._x=t,this._onChangeCallback()}get y(){return this._y}set y(t){this._y=t,this._onChangeCallback()}get z(){return this._z}set z(t){this._z=t,this._onChangeCallback()}get order(){return this._order}set order(t){this._order=t,this._onChangeCallback()}set(t,e,i,s=this._order){return this._x=t,this._y=e,this._z=i,this._order=s,this._onChangeCallback(),this}clone(){return new this.constructor(this._x,this._y,this._z,this._order)}copy(t){return this._x=t._x,this._y=t._y,this._z=t._z,this._order=t._order,this._onChangeCallback(),this}setFromRotationMatrix(t,e=this._order,i=!0){let s=t.elements,r=s[0],n=s[4],a=s[8],o=s[1],h=s[5],l=s[9],u=s[2],c=s[6],p=s[10];switch(e){case"XYZ":this._y=Math.asin(eD(a,-1,1)),.9999999>Math.abs(a)?(this._x=Math.atan2(-l,p),this._z=Math.atan2(-n,r)):(this._x=Math.atan2(c,h),this._z=0);break;case"YXZ":this._x=Math.asin(-eD(l,-1,1)),.9999999>Math.abs(l)?(this._y=Math.atan2(a,p),this._z=Math.atan2(o,h)):(this._y=Math.atan2(-u,r),this._z=0);break;case"ZXY":this._x=Math.asin(eD(c,-1,1)),.9999999>Math.abs(c)?(this._y=Math.atan2(-u,p),this._z=Math.atan2(-n,h)):(this._y=0,this._z=Math.atan2(o,r));break;case"ZYX":this._y=Math.asin(-eD(u,-1,1)),.9999999>Math.abs(u)?(this._x=Math.atan2(c,p),this._z=Math.atan2(o,r)):(this._x=0,this._z=Math.atan2(-n,h));break;case"YZX":this._z=Math.asin(eD(o,-1,1)),.9999999>Math.abs(o)?(this._x=Math.atan2(-l,h),this._y=Math.atan2(-u,r)):(this._x=0,this._y=Math.atan2(a,p));break;case"XZY":this._z=Math.asin(-eD(n,-1,1)),.9999999>Math.abs(n)?(this._x=Math.atan2(c,h),this._y=Math.atan2(a,r)):(this._x=Math.atan2(-l,p),this._y=0);break;default:console.warn("THREE.Euler: .setFromRotationMatrix() encountered an unknown order: "+e)}return this._order=e,!0===i&&this._onChangeCallback(),this}setFromQuaternion(t,e,i){return iG.makeRotationFromQuaternion(t),this.setFromRotationMatrix(iG,e,i)}setFromVector3(t,e=this._order){return this.set(t.x,t.y,t.z,e)}reorder(t){return i$.setFromEuler(this),this.setFromQuaternion(i$,t)}equals(t){return t._x===this._x&&t._y===this._y&&t._z===this._z&&t._order===this._order}fromArray(t){return this._x=t[0],this._y=t[1],this._z=t[2],void 0!==t[3]&&(this._order=t[3]),this._onChangeCallback(),this}toArray(t=[],e=0){return t[e]=this._x,t[e+1]=this._y,t[e+2]=this._z,t[e+3]=this._order,t}_onChange(t){return this._onChangeCallback=t,this}_onChangeCallback(){}*[Symbol.iterator](){yield this._x,yield this._y,yield this._z,yield this._order}}iQ.DEFAULT_ORDER="XYZ";class iK{constructor(){this.mask=1}set(t){this.mask=1<<t>>>0}enable(t){this.mask|=1<<t}enableAll(){this.mask=-1}toggle(t){this.mask^=1<<t}disable(t){this.mask&=~(1<<t)}disableAll(){this.mask=0}test(t){return(this.mask&t.mask)!=0}isEnabled(t){return(this.mask&1<<t)!=0}}let i0=0,i1=new eZ,i2=new eY,i3=new iW,i5=new eZ,i4=new eZ,i6=new eZ,i8=new eY,i9=new eZ(1,0,0),i7=new eZ(0,1,0),st=new eZ(0,0,1),se={type:"added"},si={type:"removed"},ss={type:"childadded",child:null},sr={type:"childremoved",child:null};class sn extends eN{constructor(){super(),this.isObject3D=!0,Object.defineProperty(this,"id",{value:i0++}),this.uuid=ej(),this.name="",this.type="Object3D",this.parent=null,this.children=[],this.up=sn.DEFAULT_UP.clone();let t=new eZ,e=new iQ,i=new eY,s=new eZ(1,1,1);e._onChange(function(){i.setFromEuler(e,!1)}),i._onChange(function(){e.setFromQuaternion(i,void 0,!1)}),Object.defineProperties(this,{position:{configurable:!0,enumerable:!0,value:t},rotation:{configurable:!0,enumerable:!0,value:e},quaternion:{configurable:!0,enumerable:!0,value:i},scale:{configurable:!0,enumerable:!0,value:s},modelViewMatrix:{value:new iW},normalMatrix:{value:new eQ}}),this.matrix=new iW,this.matrixWorld=new iW,this.matrixAutoUpdate=sn.DEFAULT_MATRIX_AUTO_UPDATE,this.matrixWorldAutoUpdate=sn.DEFAULT_MATRIX_WORLD_AUTO_UPDATE,this.matrixWorldNeedsUpdate=!1,this.layers=new iK,this.visible=!0,this.castShadow=!1,this.receiveShadow=!1,this.frustumCulled=!0,this.renderOrder=0,this.animations=[],this.customDepthMaterial=void 0,this.customDistanceMaterial=void 0,this.userData={}}onBeforeShadow(){}onAfterShadow(){}onBeforeRender(){}onAfterRender(){}applyMatrix4(t){this.matrixAutoUpdate&&this.updateMatrix(),this.matrix.premultiply(t),this.matrix.decompose(this.position,this.quaternion,this.scale)}applyQuaternion(t){return this.quaternion.premultiply(t),this}setRotationFromAxisAngle(t,e){this.quaternion.setFromAxisAngle(t,e)}setRotationFromEuler(t){this.quaternion.setFromEuler(t,!0)}setRotationFromMatrix(t){this.quaternion.setFromRotationMatrix(t)}setRotationFromQuaternion(t){this.quaternion.copy(t)}rotateOnAxis(t,e){return i2.setFromAxisAngle(t,e),this.quaternion.multiply(i2),this}rotateOnWorldAxis(t,e){return i2.setFromAxisAngle(t,e),this.quaternion.premultiply(i2),this}rotateX(t){return this.rotateOnAxis(i9,t)}rotateY(t){return this.rotateOnAxis(i7,t)}rotateZ(t){return this.rotateOnAxis(st,t)}translateOnAxis(t,e){return i1.copy(t).applyQuaternion(this.quaternion),this.position.add(i1.multiplyScalar(e)),this}translateX(t){return this.translateOnAxis(i9,t)}translateY(t){return this.translateOnAxis(i7,t)}translateZ(t){return this.translateOnAxis(st,t)}localToWorld(t){return this.updateWorldMatrix(!0,!1),t.applyMatrix4(this.matrixWorld)}worldToLocal(t){return this.updateWorldMatrix(!0,!1),t.applyMatrix4(i3.copy(this.matrixWorld).invert())}lookAt(t,e,i){t.isVector3?i5.copy(t):i5.set(t,e,i);let s=this.parent;this.updateWorldMatrix(!0,!1),i4.setFromMatrixPosition(this.matrixWorld),this.isCamera||this.isLight?i3.lookAt(i4,i5,this.up):i3.lookAt(i5,i4,this.up),this.quaternion.setFromRotationMatrix(i3),s&&(i3.extractRotation(s.matrixWorld),i2.setFromRotationMatrix(i3),this.quaternion.premultiply(i2.invert()))}add(t){if(arguments.length>1){for(let t=0;t<arguments.length;t++)this.add(arguments[t]);return this}return t===this?console.error("THREE.Object3D.add: object can't be added as a child of itself.",t):t&&t.isObject3D?(t.removeFromParent(),t.parent=this,this.children.push(t),t.dispatchEvent(se),ss.child=t,this.dispatchEvent(ss),ss.child=null):console.error("THREE.Object3D.add: object not an instance of THREE.Object3D.",t),this}remove(t){if(arguments.length>1){for(let t=0;t<arguments.length;t++)this.remove(arguments[t]);return this}let e=this.children.indexOf(t);return -1!==e&&(t.parent=null,this.children.splice(e,1),t.dispatchEvent(si),sr.child=t,this.dispatchEvent(sr),sr.child=null),this}removeFromParent(){let t=this.parent;return null!==t&&t.remove(this),this}clear(){return this.remove(...this.children)}attach(t){return this.updateWorldMatrix(!0,!1),i3.copy(this.matrixWorld).invert(),null!==t.parent&&(t.parent.updateWorldMatrix(!0,!1),i3.multiply(t.parent.matrixWorld)),t.applyMatrix4(i3),t.removeFromParent(),t.parent=this,this.children.push(t),t.updateWorldMatrix(!1,!0),t.dispatchEvent(se),ss.child=t,this.dispatchEvent(ss),ss.child=null,this}getObjectById(t){return this.getObjectByProperty("id",t)}getObjectByName(t){return this.getObjectByProperty("name",t)}getObjectByProperty(t,e){if(this[t]===e)return this;for(let i=0,s=this.children.length;i<s;i++){let s=this.children[i].getObjectByProperty(t,e);if(void 0!==s)return s}}getObjectsByProperty(t,e,i=[]){this[t]===e&&i.push(this);let s=this.children;for(let r=0,n=s.length;r<n;r++)s[r].getObjectsByProperty(t,e,i);return i}getWorldPosition(t){return this.updateWorldMatrix(!0,!1),t.setFromMatrixPosition(this.matrixWorld)}getWorldQuaternion(t){return this.updateWorldMatrix(!0,!1),this.matrixWorld.decompose(i4,t,i6),t}getWorldScale(t){return this.updateWorldMatrix(!0,!1),this.matrixWorld.decompose(i4,i8,t),t}getWorldDirection(t){this.updateWorldMatrix(!0,!1);let e=this.matrixWorld.elements;return t.set(e[8],e[9],e[10]).normalize()}raycast(){}traverse(t){t(this);let e=this.children;for(let i=0,s=e.length;i<s;i++)e[i].traverse(t)}traverseVisible(t){if(!1===this.visible)return;t(this);let e=this.children;for(let i=0,s=e.length;i<s;i++)e[i].traverseVisible(t)}traverseAncestors(t){let e=this.parent;null!==e&&(t(e),e.traverseAncestors(t))}updateMatrix(){this.matrix.compose(this.position,this.quaternion,this.scale),this.matrixWorldNeedsUpdate=!0}updateMatrixWorld(t){this.matrixAutoUpdate&&this.updateMatrix(),(this.matrixWorldNeedsUpdate||t)&&(!0===this.matrixWorldAutoUpdate&&(null===this.parent?this.matrixWorld.copy(this.matrix):this.matrixWorld.multiplyMatrices(this.parent.matrixWorld,this.matrix)),this.matrixWorldNeedsUpdate=!1,t=!0);let e=this.children;for(let i=0,s=e.length;i<s;i++)e[i].updateMatrixWorld(t)}updateWorldMatrix(t,e){let i=this.parent;if(!0===t&&null!==i&&i.updateWorldMatrix(!0,!1),this.matrixAutoUpdate&&this.updateMatrix(),!0===this.matrixWorldAutoUpdate&&(null===this.parent?this.matrixWorld.copy(this.matrix):this.matrixWorld.multiplyMatrices(this.parent.matrixWorld,this.matrix)),!0===e){let t=this.children;for(let e=0,i=t.length;e<i;e++)t[e].updateWorldMatrix(!1,!0)}}toJSON(t){let e=void 0===t||"string"==typeof t,i={};e&&(t={geometries:{},materials:{},textures:{},images:{},shapes:{},skeletons:{},animations:{},nodes:{}},i.metadata={version:4.7,type:"Object",generator:"Object3D.toJSON"});let s={};function r(e,i){return void 0===e[i.uuid]&&(e[i.uuid]=i.toJSON(t)),i.uuid}if(s.uuid=this.uuid,s.type=this.type,""!==this.name&&(s.name=this.name),!0===this.castShadow&&(s.castShadow=!0),!0===this.receiveShadow&&(s.receiveShadow=!0),!1===this.visible&&(s.visible=!1),!1===this.frustumCulled&&(s.frustumCulled=!1),0!==this.renderOrder&&(s.renderOrder=this.renderOrder),Object.keys(this.userData).length>0&&(s.userData=this.userData),s.layers=this.layers.mask,s.matrix=this.matrix.toArray(),s.up=this.up.toArray(),!1===this.matrixAutoUpdate&&(s.matrixAutoUpdate=!1),this.isInstancedMesh&&(s.type="InstancedMesh",s.count=this.count,s.instanceMatrix=this.instanceMatrix.toJSON(),null!==this.instanceColor&&(s.instanceColor=this.instanceColor.toJSON())),this.isBatchedMesh&&(s.type="BatchedMesh",s.perObjectFrustumCulled=this.perObjectFrustumCulled,s.sortObjects=this.sortObjects,s.drawRanges=this._drawRanges,s.reservedRanges=this._reservedRanges,s.geometryInfo=this._geometryInfo.map(t=>({...t,boundingBox:t.boundingBox?t.boundingBox.toJSON():void 0,boundingSphere:t.boundingSphere?t.boundingSphere.toJSON():void 0})),s.instanceInfo=this._instanceInfo.map(t=>({...t})),s.availableInstanceIds=this._availableInstanceIds.slice(),s.availableGeometryIds=this._availableGeometryIds.slice(),s.nextIndexStart=this._nextIndexStart,s.nextVertexStart=this._nextVertexStart,s.geometryCount=this._geometryCount,s.maxInstanceCount=this._maxInstanceCount,s.maxVertexCount=this._maxVertexCount,s.maxIndexCount=this._maxIndexCount,s.geometryInitialized=this._geometryInitialized,s.matricesTexture=this._matricesTexture.toJSON(t),s.indirectTexture=this._indirectTexture.toJSON(t),null!==this._colorsTexture&&(s.colorsTexture=this._colorsTexture.toJSON(t)),null!==this.boundingSphere&&(s.boundingSphere=this.boundingSphere.toJSON()),null!==this.boundingBox&&(s.boundingBox=this.boundingBox.toJSON())),this.isScene)this.background&&(this.background.isColor?s.background=this.background.toJSON():this.background.isTexture&&(s.background=this.background.toJSON(t).uuid)),this.environment&&this.environment.isTexture&&!0!==this.environment.isRenderTargetTexture&&(s.environment=this.environment.toJSON(t).uuid);else if(this.isMesh||this.isLine||this.isPoints){s.geometry=r(t.geometries,this.geometry);let e=this.geometry.parameters;if(void 0!==e&&void 0!==e.shapes){let i=e.shapes;if(Array.isArray(i))for(let e=0,s=i.length;e<s;e++){let s=i[e];r(t.shapes,s)}else r(t.shapes,i)}}if(this.isSkinnedMesh&&(s.bindMode=this.bindMode,s.bindMatrix=this.bindMatrix.toArray(),void 0!==this.skeleton&&(r(t.skeletons,this.skeleton),s.skeleton=this.skeleton.uuid)),void 0!==this.material)if(Array.isArray(this.material)){let e=[];for(let i=0,s=this.material.length;i<s;i++)e.push(r(t.materials,this.material[i]));s.material=e}else s.material=r(t.materials,this.material);if(this.children.length>0){s.children=[];for(let e=0;e<this.children.length;e++)s.children.push(this.children[e].toJSON(t).object)}if(this.animations.length>0){s.animations=[];for(let e=0;e<this.animations.length;e++){let i=this.animations[e];s.animations.push(r(t.animations,i))}}if(e){let e=n(t.geometries),s=n(t.materials),r=n(t.textures),a=n(t.images),o=n(t.shapes),h=n(t.skeletons),l=n(t.animations),u=n(t.nodes);e.length>0&&(i.geometries=e),s.length>0&&(i.materials=s),r.length>0&&(i.textures=r),a.length>0&&(i.images=a),o.length>0&&(i.shapes=o),h.length>0&&(i.skeletons=h),l.length>0&&(i.animations=l),u.length>0&&(i.nodes=u)}return i.object=s,i;function n(t){let e=[];for(let i in t){let s=t[i];delete s.metadata,e.push(s)}return e}}clone(t){return new this.constructor().copy(this,t)}copy(t,e=!0){if(this.name=t.name,this.up.copy(t.up),this.position.copy(t.position),this.rotation.order=t.rotation.order,this.quaternion.copy(t.quaternion),this.scale.copy(t.scale),this.matrix.copy(t.matrix),this.matrixWorld.copy(t.matrixWorld),this.matrixAutoUpdate=t.matrixAutoUpdate,this.matrixWorldAutoUpdate=t.matrixWorldAutoUpdate,this.matrixWorldNeedsUpdate=t.matrixWorldNeedsUpdate,this.layers.mask=t.layers.mask,this.visible=t.visible,this.castShadow=t.castShadow,this.receiveShadow=t.receiveShadow,this.frustumCulled=t.frustumCulled,this.renderOrder=t.renderOrder,this.animations=t.animations.slice(),this.userData=JSON.parse(JSON.stringify(t.userData)),!0===e)for(let e=0;e<t.children.length;e++){let i=t.children[e];this.add(i.clone())}return this}}sn.DEFAULT_UP=new eZ(0,1,0),sn.DEFAULT_MATRIX_AUTO_UPDATE=!0,sn.DEFAULT_MATRIX_WORLD_AUTO_UPDATE=!0;let sa=new eZ,so=new eZ,sh=new eZ,sl=new eZ,su=new eZ,sc=new eZ,sp=new eZ,sd=new eZ,sm=new eZ,sy=new eZ,sf=new il,sg=new il,sx=new il;class sb{constructor(t=new eZ,e=new eZ,i=new eZ){this.a=t,this.b=e,this.c=i}static getNormal(t,e,i,s){s.subVectors(i,e),sa.subVectors(t,e),s.cross(sa);let r=s.lengthSq();return r>0?s.multiplyScalar(1/Math.sqrt(r)):s.set(0,0,0)}static getBarycoord(t,e,i,s,r){sa.subVectors(s,e),so.subVectors(i,e),sh.subVectors(t,e);let n=sa.dot(sa),a=sa.dot(so),o=sa.dot(sh),h=so.dot(so),l=so.dot(sh),u=n*h-a*a;if(0===u)return r.set(0,0,0),null;let c=1/u,p=(h*o-a*l)*c,d=(n*l-a*o)*c;return r.set(1-p-d,d,p)}static containsPoint(t,e,i,s){return null!==this.getBarycoord(t,e,i,s,sl)&&sl.x>=0&&sl.y>=0&&sl.x+sl.y<=1}static getInterpolation(t,e,i,s,r,n,a,o){return null===this.getBarycoord(t,e,i,s,sl)?(o.x=0,o.y=0,"z"in o&&(o.z=0),"w"in o&&(o.w=0),null):(o.setScalar(0),o.addScaledVector(r,sl.x),o.addScaledVector(n,sl.y),o.addScaledVector(a,sl.z),o)}static getInterpolatedAttribute(t,e,i,s,r,n){return sf.setScalar(0),sg.setScalar(0),sx.setScalar(0),sf.fromBufferAttribute(t,e),sg.fromBufferAttribute(t,i),sx.fromBufferAttribute(t,s),n.setScalar(0),n.addScaledVector(sf,r.x),n.addScaledVector(sg,r.y),n.addScaledVector(sx,r.z),n}static isFrontFacing(t,e,i,s){return sa.subVectors(i,e),so.subVectors(t,e),0>sa.cross(so).dot(s)}set(t,e,i){return this.a.copy(t),this.b.copy(e),this.c.copy(i),this}setFromPointsAndIndices(t,e,i,s){return this.a.copy(t[e]),this.b.copy(t[i]),this.c.copy(t[s]),this}setFromAttributeAndIndices(t,e,i,s){return this.a.fromBufferAttribute(t,e),this.b.fromBufferAttribute(t,i),this.c.fromBufferAttribute(t,s),this}clone(){return new this.constructor().copy(this)}copy(t){return this.a.copy(t.a),this.b.copy(t.b),this.c.copy(t.c),this}getArea(){return sa.subVectors(this.c,this.b),so.subVectors(this.a,this.b),.5*sa.cross(so).length()}getMidpoint(t){return t.addVectors(this.a,this.b).add(this.c).multiplyScalar(1/3)}getNormal(t){return sb.getNormal(this.a,this.b,this.c,t)}getPlane(t){return t.setFromCoplanarPoints(this.a,this.b,this.c)}getBarycoord(t,e){return sb.getBarycoord(t,this.a,this.b,this.c,e)}getInterpolation(t,e,i,s,r){return sb.getInterpolation(t,this.a,this.b,this.c,e,i,s,r)}containsPoint(t){return sb.containsPoint(t,this.a,this.b,this.c)}isFrontFacing(t){return sb.isFrontFacing(this.a,this.b,this.c,t)}intersectsBox(t){return t.intersectsTriangle(this)}closestPointToPoint(t,e){let i,s,r=this.a,n=this.b,a=this.c;su.subVectors(n,r),sc.subVectors(a,r),sd.subVectors(t,r);let o=su.dot(sd),h=sc.dot(sd);if(o<=0&&h<=0)return e.copy(r);sm.subVectors(t,n);let l=su.dot(sm),u=sc.dot(sm);if(l>=0&&u<=l)return e.copy(n);let c=o*u-l*h;if(c<=0&&o>=0&&l<=0)return i=o/(o-l),e.copy(r).addScaledVector(su,i);sy.subVectors(t,a);let p=su.dot(sy),d=sc.dot(sy);if(d>=0&&p<=d)return e.copy(a);let m=p*h-o*d;if(m<=0&&h>=0&&d<=0)return s=h/(h-d),e.copy(r).addScaledVector(sc,s);let y=l*d-p*u;if(y<=0&&u-l>=0&&p-d>=0)return sp.subVectors(a,n),s=(u-l)/(u-l+(p-d)),e.copy(n).addScaledVector(sp,s);let f=1/(y+m+c);return i=m*f,s=c*f,e.copy(r).addScaledVector(su,i).addScaledVector(sc,s)}equals(t){return t.a.equals(this.a)&&t.b.equals(this.b)&&t.c.equals(this.c)}}let sM={aliceblue:0xf0f8ff,antiquewhite:0xfaebd7,aqua:65535,aquamarine:8388564,azure:0xf0ffff,beige:0xf5f5dc,bisque:0xffe4c4,black:0,blanchedalmond:0xffebcd,blue:255,blueviolet:9055202,brown:0xa52a2a,burlywood:0xdeb887,cadetblue:6266528,chartreuse:8388352,chocolate:0xd2691e,coral:0xff7f50,cornflowerblue:6591981,cornsilk:0xfff8dc,crimson:0xdc143c,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:0xb8860b,darkgray:0xa9a9a9,darkgreen:25600,darkgrey:0xa9a9a9,darkkhaki:0xbdb76b,darkmagenta:9109643,darkolivegreen:5597999,darkorange:0xff8c00,darkorchid:0x9932cc,darkred:9109504,darksalmon:0xe9967a,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:0xff1493,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:0xb22222,floralwhite:0xfffaf0,forestgreen:2263842,fuchsia:0xff00ff,gainsboro:0xdcdcdc,ghostwhite:0xf8f8ff,gold:0xffd700,goldenrod:0xdaa520,gray:8421504,green:32768,greenyellow:0xadff2f,grey:8421504,honeydew:0xf0fff0,hotpink:0xff69b4,indianred:0xcd5c5c,indigo:4915330,ivory:0xfffff0,khaki:0xf0e68c,lavender:0xe6e6fa,lavenderblush:0xfff0f5,lawngreen:8190976,lemonchiffon:0xfffacd,lightblue:0xadd8e6,lightcoral:0xf08080,lightcyan:0xe0ffff,lightgoldenrodyellow:0xfafad2,lightgray:0xd3d3d3,lightgreen:9498256,lightgrey:0xd3d3d3,lightpink:0xffb6c1,lightsalmon:0xffa07a,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:0xb0c4de,lightyellow:0xffffe0,lime:65280,limegreen:3329330,linen:0xfaf0e6,magenta:0xff00ff,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:0xba55d3,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:0xc71585,midnightblue:1644912,mintcream:0xf5fffa,mistyrose:0xffe4e1,moccasin:0xffe4b5,navajowhite:0xffdead,navy:128,oldlace:0xfdf5e6,olive:8421376,olivedrab:7048739,orange:0xffa500,orangered:0xff4500,orchid:0xda70d6,palegoldenrod:0xeee8aa,palegreen:0x98fb98,paleturquoise:0xafeeee,palevioletred:0xdb7093,papayawhip:0xffefd5,peachpuff:0xffdab9,peru:0xcd853f,pink:0xffc0cb,plum:0xdda0dd,powderblue:0xb0e0e6,purple:8388736,rebeccapurple:6697881,red:0xff0000,rosybrown:0xbc8f8f,royalblue:4286945,saddlebrown:9127187,salmon:0xfa8072,sandybrown:0xf4a460,seagreen:3050327,seashell:0xfff5ee,sienna:0xa0522d,silver:0xc0c0c0,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:0xfffafa,springgreen:65407,steelblue:4620980,tan:0xd2b48c,teal:32896,thistle:0xd8bfd8,tomato:0xff6347,turquoise:4251856,violet:0xee82ee,wheat:0xf5deb3,white:0xffffff,whitesmoke:0xf5f5f5,yellow:0xffff00,yellowgreen:0x9acd32},sw={h:0,s:0,l:0},sv={h:0,s:0,l:0};function sS(t,e,i){return(i<0&&(i+=1),i>1&&(i-=1),i<1/6)?t+(e-t)*6*i:i<.5?e:i<2/3?t+(e-t)*6*(2/3-i):t}class s_{constructor(t,e,i){return this.isColor=!0,this.r=1,this.g=1,this.b=1,this.set(t,e,i)}set(t,e,i){return void 0===e&&void 0===i?t&&t.isColor?this.copy(t):"number"==typeof t?this.setHex(t):"string"==typeof t&&this.setStyle(t):this.setRGB(t,e,i),this}setScalar(t){return this.r=t,this.g=t,this.b=t,this}setHex(t,e=ew){return t=Math.floor(t),this.r=(t>>16&255)/255,this.g=(t>>8&255)/255,this.b=(255&t)/255,e9.colorSpaceToWorking(this,e),this}setRGB(t,e,i,s=e9.workingColorSpace){return this.r=t,this.g=e,this.b=i,e9.colorSpaceToWorking(this,s),this}setHSL(t,e,i,s=e9.workingColorSpace){if(t=eW(t,1),e=eD(e,0,1),i=eD(i,0,1),0===e)this.r=this.g=this.b=i;else{let s=i<=.5?i*(1+e):i+e-i*e,r=2*i-s;this.r=sS(r,s,t+1/3),this.g=sS(r,s,t),this.b=sS(r,s,t-1/3)}return e9.colorSpaceToWorking(this,s),this}setStyle(t,e=ew){let i;function s(e){void 0!==e&&1>parseFloat(e)&&console.warn("THREE.Color: Alpha component of "+t+" will be ignored.")}if(i=/^(\w+)\(([^\)]*)\)/.exec(t)){let r,n=i[1],a=i[2];switch(n){case"rgb":case"rgba":if(r=/^\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*(?:,\s*(\d*\.?\d+)\s*)?$/.exec(a))return s(r[4]),this.setRGB(Math.min(255,parseInt(r[1],10))/255,Math.min(255,parseInt(r[2],10))/255,Math.min(255,parseInt(r[3],10))/255,e);if(r=/^\s*(\d+)\%\s*,\s*(\d+)\%\s*,\s*(\d+)\%\s*(?:,\s*(\d*\.?\d+)\s*)?$/.exec(a))return s(r[4]),this.setRGB(Math.min(100,parseInt(r[1],10))/100,Math.min(100,parseInt(r[2],10))/100,Math.min(100,parseInt(r[3],10))/100,e);break;case"hsl":case"hsla":if(r=/^\s*(\d*\.?\d+)\s*,\s*(\d*\.?\d+)\%\s*,\s*(\d*\.?\d+)\%\s*(?:,\s*(\d*\.?\d+)\s*)?$/.exec(a))return s(r[4]),this.setHSL(parseFloat(r[1])/360,parseFloat(r[2])/100,parseFloat(r[3])/100,e);break;default:console.warn("THREE.Color: Unknown color model "+t)}}else if(i=/^\#([A-Fa-f\d]+)$/.exec(t)){let s=i[1],r=s.length;if(3===r)return this.setRGB(parseInt(s.charAt(0),16)/15,parseInt(s.charAt(1),16)/15,parseInt(s.charAt(2),16)/15,e);if(6===r)return this.setHex(parseInt(s,16),e);console.warn("THREE.Color: Invalid hex color "+t)}else if(t&&t.length>0)return this.setColorName(t,e);return this}setColorName(t,e=ew){let i=sM[t.toLowerCase()];return void 0!==i?this.setHex(i,e):console.warn("THREE.Color: Unknown color "+t),this}clone(){return new this.constructor(this.r,this.g,this.b)}copy(t){return this.r=t.r,this.g=t.g,this.b=t.b,this}copySRGBToLinear(t){return this.r=e7(t.r),this.g=e7(t.g),this.b=e7(t.b),this}copyLinearToSRGB(t){return this.r=it(t.r),this.g=it(t.g),this.b=it(t.b),this}convertSRGBToLinear(){return this.copySRGBToLinear(this),this}convertLinearToSRGB(){return this.copyLinearToSRGB(this),this}getHex(t=ew){return e9.workingToColorSpace(sz.copy(this),t),65536*Math.round(eD(255*sz.r,0,255))+256*Math.round(eD(255*sz.g,0,255))+Math.round(eD(255*sz.b,0,255))}getHexString(t=ew){return("000000"+this.getHex(t).toString(16)).slice(-6)}getHSL(t,e=e9.workingColorSpace){let i,s;e9.workingToColorSpace(sz.copy(this),e);let r=sz.r,n=sz.g,a=sz.b,o=Math.max(r,n,a),h=Math.min(r,n,a),l=(h+o)/2;if(h===o)i=0,s=0;else{let t=o-h;switch(s=l<=.5?t/(o+h):t/(2-o-h),o){case r:i=(n-a)/t+6*(n<a);break;case n:i=(a-r)/t+2;break;case a:i=(r-n)/t+4}i/=6}return t.h=i,t.s=s,t.l=l,t}getRGB(t,e=e9.workingColorSpace){return e9.workingToColorSpace(sz.copy(this),e),t.r=sz.r,t.g=sz.g,t.b=sz.b,t}getStyle(t=ew){e9.workingToColorSpace(sz.copy(this),t);let e=sz.r,i=sz.g,s=sz.b;return t!==ew?`color(${t} ${e.toFixed(3)} ${i.toFixed(3)} ${s.toFixed(3)})`:`rgb(${Math.round(255*e)},${Math.round(255*i)},${Math.round(255*s)})`}offsetHSL(t,e,i){return this.getHSL(sw),this.setHSL(sw.h+t,sw.s+e,sw.l+i)}add(t){return this.r+=t.r,this.g+=t.g,this.b+=t.b,this}addColors(t,e){return this.r=t.r+e.r,this.g=t.g+e.g,this.b=t.b+e.b,this}addScalar(t){return this.r+=t,this.g+=t,this.b+=t,this}sub(t){return this.r=Math.max(0,this.r-t.r),this.g=Math.max(0,this.g-t.g),this.b=Math.max(0,this.b-t.b),this}multiply(t){return this.r*=t.r,this.g*=t.g,this.b*=t.b,this}multiplyScalar(t){return this.r*=t,this.g*=t,this.b*=t,this}lerp(t,e){return this.r+=(t.r-this.r)*e,this.g+=(t.g-this.g)*e,this.b+=(t.b-this.b)*e,this}lerpColors(t,e,i){return this.r=t.r+(e.r-t.r)*i,this.g=t.g+(e.g-t.g)*i,this.b=t.b+(e.b-t.b)*i,this}lerpHSL(t,e){this.getHSL(sw),t.getHSL(sv);let i=eU(sw.h,sv.h,e),s=eU(sw.s,sv.s,e),r=eU(sw.l,sv.l,e);return this.setHSL(i,s,r),this}setFromVector3(t){return this.r=t.x,this.g=t.y,this.b=t.z,this}applyMatrix3(t){let e=this.r,i=this.g,s=this.b,r=t.elements;return this.r=r[0]*e+r[3]*i+r[6]*s,this.g=r[1]*e+r[4]*i+r[7]*s,this.b=r[2]*e+r[5]*i+r[8]*s,this}equals(t){return t.r===this.r&&t.g===this.g&&t.b===this.b}fromArray(t,e=0){return this.r=t[e],this.g=t[e+1],this.b=t[e+2],this}toArray(t=[],e=0){return t[e]=this.r,t[e+1]=this.g,t[e+2]=this.b,t}fromBufferAttribute(t,e){return this.r=t.getX(e),this.g=t.getY(e),this.b=t.getZ(e),this}toJSON(){return this.getHex()}*[Symbol.iterator](){yield this.r,yield this.g,yield this.b}}let sz=new s_;s_.NAMES=sM;let sA=0;class sT extends eN{constructor(){super(),this.isMaterial=!0,Object.defineProperty(this,"id",{value:sA++}),this.uuid=ej(),this.name="",this.type="Material",this.blending=g,this.side=d,this.vertexColors=!1,this.opacity=1,this.transparent=!1,this.alphaHash=!1,this.blendSrc=O,this.blendDst=B,this.blendEquation=v,this.blendSrcAlpha=null,this.blendDstAlpha=null,this.blendEquationAlpha=null,this.blendColor=new s_(0,0,0),this.blendAlpha=0,this.depthFunc=q,this.depthTest=!0,this.depthWrite=!0,this.stencilWriteMask=255,this.stencilFunc=519,this.stencilRef=0,this.stencilFuncMask=255,this.stencilFail=7680,this.stencilZFail=7680,this.stencilZPass=7680,this.stencilWrite=!1,this.clippingPlanes=null,this.clipIntersection=!1,this.clipShadows=!1,this.shadowSide=null,this.colorWrite=!0,this.precision=null,this.polygonOffset=!1,this.polygonOffsetFactor=0,this.polygonOffsetUnits=0,this.dithering=!1,this.alphaToCoverage=!1,this.premultipliedAlpha=!1,this.forceSinglePass=!1,this.allowOverride=!0,this.visible=!0,this.toneMapped=!0,this.userData={},this.version=0,this._alphaTest=0}get alphaTest(){return this._alphaTest}set alphaTest(t){this._alphaTest>0!=t>0&&this.version++,this._alphaTest=t}onBeforeRender(){}onBeforeCompile(){}customProgramCacheKey(){return this.onBeforeCompile.toString()}setValues(t){if(void 0!==t)for(let e in t){let i=t[e];if(void 0===i){console.warn(`THREE.Material: parameter '${e}' has value of undefined.`);continue}let s=this[e];if(void 0===s){console.warn(`THREE.Material: '${e}' is not a property of THREE.${this.type}.`);continue}s&&s.isColor?s.set(i):s&&s.isVector3&&i&&i.isVector3?s.copy(i):this[e]=i}}toJSON(t){let e=void 0===t||"string"==typeof t;e&&(t={textures:{},images:{}});let i={metadata:{version:4.7,type:"Material",generator:"Material.toJSON"}};function s(t){let e=[];for(let i in t){let s=t[i];delete s.metadata,e.push(s)}return e}if(i.uuid=this.uuid,i.type=this.type,""!==this.name&&(i.name=this.name),this.color&&this.color.isColor&&(i.color=this.color.getHex()),void 0!==this.roughness&&(i.roughness=this.roughness),void 0!==this.metalness&&(i.metalness=this.metalness),void 0!==this.sheen&&(i.sheen=this.sheen),this.sheenColor&&this.sheenColor.isColor&&(i.sheenColor=this.sheenColor.getHex()),void 0!==this.sheenRoughness&&(i.sheenRoughness=this.sheenRoughness),this.emissive&&this.emissive.isColor&&(i.emissive=this.emissive.getHex()),void 0!==this.emissiveIntensity&&1!==this.emissiveIntensity&&(i.emissiveIntensity=this.emissiveIntensity),this.specular&&this.specular.isColor&&(i.specular=this.specular.getHex()),void 0!==this.specularIntensity&&(i.specularIntensity=this.specularIntensity),this.specularColor&&this.specularColor.isColor&&(i.specularColor=this.specularColor.getHex()),void 0!==this.shininess&&(i.shininess=this.shininess),void 0!==this.clearcoat&&(i.clearcoat=this.clearcoat),void 0!==this.clearcoatRoughness&&(i.clearcoatRoughness=this.clearcoatRoughness),this.clearcoatMap&&this.clearcoatMap.isTexture&&(i.clearcoatMap=this.clearcoatMap.toJSON(t).uuid),this.clearcoatRoughnessMap&&this.clearcoatRoughnessMap.isTexture&&(i.clearcoatRoughnessMap=this.clearcoatRoughnessMap.toJSON(t).uuid),this.clearcoatNormalMap&&this.clearcoatNormalMap.isTexture&&(i.clearcoatNormalMap=this.clearcoatNormalMap.toJSON(t).uuid,i.clearcoatNormalScale=this.clearcoatNormalScale.toArray()),void 0!==this.dispersion&&(i.dispersion=this.dispersion),void 0!==this.iridescence&&(i.iridescence=this.iridescence),void 0!==this.iridescenceIOR&&(i.iridescenceIOR=this.iridescenceIOR),void 0!==this.iridescenceThicknessRange&&(i.iridescenceThicknessRange=this.iridescenceThicknessRange),this.iridescenceMap&&this.iridescenceMap.isTexture&&(i.iridescenceMap=this.iridescenceMap.toJSON(t).uuid),this.iridescenceThicknessMap&&this.iridescenceThicknessMap.isTexture&&(i.iridescenceThicknessMap=this.iridescenceThicknessMap.toJSON(t).uuid),void 0!==this.anisotropy&&(i.anisotropy=this.anisotropy),void 0!==this.anisotropyRotation&&(i.anisotropyRotation=this.anisotropyRotation),this.anisotropyMap&&this.anisotropyMap.isTexture&&(i.anisotropyMap=this.anisotropyMap.toJSON(t).uuid),this.map&&this.map.isTexture&&(i.map=this.map.toJSON(t).uuid),this.matcap&&this.matcap.isTexture&&(i.matcap=this.matcap.toJSON(t).uuid),this.alphaMap&&this.alphaMap.isTexture&&(i.alphaMap=this.alphaMap.toJSON(t).uuid),this.lightMap&&this.lightMap.isTexture&&(i.lightMap=this.lightMap.toJSON(t).uuid,i.lightMapIntensity=this.lightMapIntensity),this.aoMap&&this.aoMap.isTexture&&(i.aoMap=this.aoMap.toJSON(t).uuid,i.aoMapIntensity=this.aoMapIntensity),this.bumpMap&&this.bumpMap.isTexture&&(i.bumpMap=this.bumpMap.toJSON(t).uuid,i.bumpScale=this.bumpScale),this.normalMap&&this.normalMap.isTexture&&(i.normalMap=this.normalMap.toJSON(t).uuid,i.normalMapType=this.normalMapType,i.normalScale=this.normalScale.toArray()),this.displacementMap&&this.displacementMap.isTexture&&(i.displacementMap=this.displacementMap.toJSON(t).uuid,i.displacementScale=this.displacementScale,i.displacementBias=this.displacementBias),this.roughnessMap&&this.roughnessMap.isTexture&&(i.roughnessMap=this.roughnessMap.toJSON(t).uuid),this.metalnessMap&&this.metalnessMap.isTexture&&(i.metalnessMap=this.metalnessMap.toJSON(t).uuid),this.emissiveMap&&this.emissiveMap.isTexture&&(i.emissiveMap=this.emissiveMap.toJSON(t).uuid),this.specularMap&&this.specularMap.isTexture&&(i.specularMap=this.specularMap.toJSON(t).uuid),this.specularIntensityMap&&this.specularIntensityMap.isTexture&&(i.specularIntensityMap=this.specularIntensityMap.toJSON(t).uuid),this.specularColorMap&&this.specularColorMap.isTexture&&(i.specularColorMap=this.specularColorMap.toJSON(t).uuid),this.envMap&&this.envMap.isTexture&&(i.envMap=this.envMap.toJSON(t).uuid,void 0!==this.combine&&(i.combine=this.combine)),void 0!==this.envMapRotation&&(i.envMapRotation=this.envMapRotation.toArray()),void 0!==this.envMapIntensity&&(i.envMapIntensity=this.envMapIntensity),void 0!==this.reflectivity&&(i.reflectivity=this.reflectivity),void 0!==this.refractionRatio&&(i.refractionRatio=this.refractionRatio),this.gradientMap&&this.gradientMap.isTexture&&(i.gradientMap=this.gradientMap.toJSON(t).uuid),void 0!==this.transmission&&(i.transmission=this.transmission),this.transmissionMap&&this.transmissionMap.isTexture&&(i.transmissionMap=this.transmissionMap.toJSON(t).uuid),void 0!==this.thickness&&(i.thickness=this.thickness),this.thicknessMap&&this.thicknessMap.isTexture&&(i.thicknessMap=this.thicknessMap.toJSON(t).uuid),void 0!==this.attenuationDistance&&this.attenuationDistance!==1/0&&(i.attenuationDistance=this.attenuationDistance),void 0!==this.attenuationColor&&(i.attenuationColor=this.attenuationColor.getHex()),void 0!==this.size&&(i.size=this.size),null!==this.shadowSide&&(i.shadowSide=this.shadowSide),void 0!==this.sizeAttenuation&&(i.sizeAttenuation=this.sizeAttenuation),this.blending!==g&&(i.blending=this.blending),this.side!==d&&(i.side=this.side),!0===this.vertexColors&&(i.vertexColors=!0),this.opacity<1&&(i.opacity=this.opacity),!0===this.transparent&&(i.transparent=!0),this.blendSrc!==O&&(i.blendSrc=this.blendSrc),this.blendDst!==B&&(i.blendDst=this.blendDst),this.blendEquation!==v&&(i.blendEquation=this.blendEquation),null!==this.blendSrcAlpha&&(i.blendSrcAlpha=this.blendSrcAlpha),null!==this.blendDstAlpha&&(i.blendDstAlpha=this.blendDstAlpha),null!==this.blendEquationAlpha&&(i.blendEquationAlpha=this.blendEquationAlpha),this.blendColor&&this.blendColor.isColor&&(i.blendColor=this.blendColor.getHex()),0!==this.blendAlpha&&(i.blendAlpha=this.blendAlpha),this.depthFunc!==q&&(i.depthFunc=this.depthFunc),!1===this.depthTest&&(i.depthTest=this.depthTest),!1===this.depthWrite&&(i.depthWrite=this.depthWrite),!1===this.colorWrite&&(i.colorWrite=this.colorWrite),255!==this.stencilWriteMask&&(i.stencilWriteMask=this.stencilWriteMask),519!==this.stencilFunc&&(i.stencilFunc=this.stencilFunc),0!==this.stencilRef&&(i.stencilRef=this.stencilRef),255!==this.stencilFuncMask&&(i.stencilFuncMask=this.stencilFuncMask),7680!==this.stencilFail&&(i.stencilFail=this.stencilFail),7680!==this.stencilZFail&&(i.stencilZFail=this.stencilZFail),7680!==this.stencilZPass&&(i.stencilZPass=this.stencilZPass),!0===this.stencilWrite&&(i.stencilWrite=this.stencilWrite),void 0!==this.rotation&&0!==this.rotation&&(i.rotation=this.rotation),!0===this.polygonOffset&&(i.polygonOffset=!0),0!==this.polygonOffsetFactor&&(i.polygonOffsetFactor=this.polygonOffsetFactor),0!==this.polygonOffsetUnits&&(i.polygonOffsetUnits=this.polygonOffsetUnits),void 0!==this.linewidth&&1!==this.linewidth&&(i.linewidth=this.linewidth),void 0!==this.dashSize&&(i.dashSize=this.dashSize),void 0!==this.gapSize&&(i.gapSize=this.gapSize),void 0!==this.scale&&(i.scale=this.scale),!0===this.dithering&&(i.dithering=!0),this.alphaTest>0&&(i.alphaTest=this.alphaTest),!0===this.alphaHash&&(i.alphaHash=!0),!0===this.alphaToCoverage&&(i.alphaToCoverage=!0),!0===this.premultipliedAlpha&&(i.premultipliedAlpha=!0),!0===this.forceSinglePass&&(i.forceSinglePass=!0),!0===this.wireframe&&(i.wireframe=!0),this.wireframeLinewidth>1&&(i.wireframeLinewidth=this.wireframeLinewidth),"round"!==this.wireframeLinecap&&(i.wireframeLinecap=this.wireframeLinecap),"round"!==this.wireframeLinejoin&&(i.wireframeLinejoin=this.wireframeLinejoin),!0===this.flatShading&&(i.flatShading=!0),!1===this.visible&&(i.visible=!1),!1===this.toneMapped&&(i.toneMapped=!1),!1===this.fog&&(i.fog=!1),Object.keys(this.userData).length>0&&(i.userData=this.userData),e){let e=s(t.textures),r=s(t.images);e.length>0&&(i.textures=e),r.length>0&&(i.images=r)}return i}clone(){return new this.constructor().copy(this)}copy(t){this.name=t.name,this.blending=t.blending,this.side=t.side,this.vertexColors=t.vertexColors,this.opacity=t.opacity,this.transparent=t.transparent,this.blendSrc=t.blendSrc,this.blendDst=t.blendDst,this.blendEquation=t.blendEquation,this.blendSrcAlpha=t.blendSrcAlpha,this.blendDstAlpha=t.blendDstAlpha,this.blendEquationAlpha=t.blendEquationAlpha,this.blendColor.copy(t.blendColor),this.blendAlpha=t.blendAlpha,this.depthFunc=t.depthFunc,this.depthTest=t.depthTest,this.depthWrite=t.depthWrite,this.stencilWriteMask=t.stencilWriteMask,this.stencilFunc=t.stencilFunc,this.stencilRef=t.stencilRef,this.stencilFuncMask=t.stencilFuncMask,this.stencilFail=t.stencilFail,this.stencilZFail=t.stencilZFail,this.stencilZPass=t.stencilZPass,this.stencilWrite=t.stencilWrite;let e=t.clippingPlanes,i=null;if(null!==e){let t=e.length;i=Array(t);for(let s=0;s!==t;++s)i[s]=e[s].clone()}return this.clippingPlanes=i,this.clipIntersection=t.clipIntersection,this.clipShadows=t.clipShadows,this.shadowSide=t.shadowSide,this.colorWrite=t.colorWrite,this.precision=t.precision,this.polygonOffset=t.polygonOffset,this.polygonOffsetFactor=t.polygonOffsetFactor,this.polygonOffsetUnits=t.polygonOffsetUnits,this.dithering=t.dithering,this.alphaTest=t.alphaTest,this.alphaHash=t.alphaHash,this.alphaToCoverage=t.alphaToCoverage,this.premultipliedAlpha=t.premultipliedAlpha,this.forceSinglePass=t.forceSinglePass,this.visible=t.visible,this.toneMapped=t.toneMapped,this.userData=JSON.parse(JSON.stringify(t.userData)),this}dispose(){this.dispatchEvent({type:"dispose"})}set needsUpdate(t){!0===t&&this.version++}}class sC extends sT{constructor(t){super(),this.isMeshBasicMaterial=!0,this.type="MeshBasicMaterial",this.color=new s_(0xffffff),this.map=null,this.lightMap=null,this.lightMapIntensity=1,this.aoMap=null,this.aoMapIntensity=1,this.specularMap=null,this.alphaMap=null,this.envMap=null,this.envMapRotation=new iQ,this.combine=G,this.reflectivity=1,this.refractionRatio=.98,this.wireframe=!1,this.wireframeLinewidth=1,this.wireframeLinecap="round",this.wireframeLinejoin="round",this.fog=!0,this.setValues(t)}copy(t){return super.copy(t),this.color.copy(t.color),this.map=t.map,this.lightMap=t.lightMap,this.lightMapIntensity=t.lightMapIntensity,this.aoMap=t.aoMap,this.aoMapIntensity=t.aoMapIntensity,this.specularMap=t.specularMap,this.alphaMap=t.alphaMap,this.envMap=t.envMap,this.envMapRotation.copy(t.envMapRotation),this.combine=t.combine,this.reflectivity=t.reflectivity,this.refractionRatio=t.refractionRatio,this.wireframe=t.wireframe,this.wireframeLinewidth=t.wireframeLinewidth,this.wireframeLinecap=t.wireframeLinecap,this.wireframeLinejoin=t.wireframeLinejoin,this.fog=t.fog,this}}let sk=new eZ,sE=new eX,sO=0;class sB{constructor(t,e,i=!1){if(Array.isArray(t))throw TypeError("THREE.BufferAttribute: array should be a Typed Array.");this.isBufferAttribute=!0,Object.defineProperty(this,"id",{value:sO++}),this.name="",this.array=t,this.itemSize=e,this.count=void 0!==t?t.length/e:0,this.normalized=i,this.usage=35044,this.updateRanges=[],this.gpuType=tC,this.version=0}onUploadCallback(){}set needsUpdate(t){!0===t&&this.version++}setUsage(t){return this.usage=t,this}addUpdateRange(t,e){this.updateRanges.push({start:t,count:e})}clearUpdateRanges(){this.updateRanges.length=0}copy(t){return this.name=t.name,this.array=new t.array.constructor(t.array),this.itemSize=t.itemSize,this.count=t.count,this.normalized=t.normalized,this.usage=t.usage,this.gpuType=t.gpuType,this}copyAt(t,e,i){t*=this.itemSize,i*=e.itemSize;for(let s=0,r=this.itemSize;s<r;s++)this.array[t+s]=e.array[i+s];return this}copyArray(t){return this.array.set(t),this}applyMatrix3(t){if(2===this.itemSize)for(let e=0,i=this.count;e<i;e++)sE.fromBufferAttribute(this,e),sE.applyMatrix3(t),this.setXY(e,sE.x,sE.y);else if(3===this.itemSize)for(let e=0,i=this.count;e<i;e++)sk.fromBufferAttribute(this,e),sk.applyMatrix3(t),this.setXYZ(e,sk.x,sk.y,sk.z);return this}applyMatrix4(t){for(let e=0,i=this.count;e<i;e++)sk.fromBufferAttribute(this,e),sk.applyMatrix4(t),this.setXYZ(e,sk.x,sk.y,sk.z);return this}applyNormalMatrix(t){for(let e=0,i=this.count;e<i;e++)sk.fromBufferAttribute(this,e),sk.applyNormalMatrix(t),this.setXYZ(e,sk.x,sk.y,sk.z);return this}transformDirection(t){for(let e=0,i=this.count;e<i;e++)sk.fromBufferAttribute(this,e),sk.transformDirection(t),this.setXYZ(e,sk.x,sk.y,sk.z);return this}set(t,e=0){return this.array.set(t,e),this}getComponent(t,e){let i=this.array[t*this.itemSize+e];return this.normalized&&(i=eJ(i,this.array)),i}setComponent(t,e,i){return this.normalized&&(i=eq(i,this.array)),this.array[t*this.itemSize+e]=i,this}getX(t){let e=this.array[t*this.itemSize];return this.normalized&&(e=eJ(e,this.array)),e}setX(t,e){return this.normalized&&(e=eq(e,this.array)),this.array[t*this.itemSize]=e,this}getY(t){let e=this.array[t*this.itemSize+1];return this.normalized&&(e=eJ(e,this.array)),e}setY(t,e){return this.normalized&&(e=eq(e,this.array)),this.array[t*this.itemSize+1]=e,this}getZ(t){let e=this.array[t*this.itemSize+2];return this.normalized&&(e=eJ(e,this.array)),e}setZ(t,e){return this.normalized&&(e=eq(e,this.array)),this.array[t*this.itemSize+2]=e,this}getW(t){let e=this.array[t*this.itemSize+3];return this.normalized&&(e=eJ(e,this.array)),e}setW(t,e){return this.normalized&&(e=eq(e,this.array)),this.array[t*this.itemSize+3]=e,this}setXY(t,e,i){return t*=this.itemSize,this.normalized&&(e=eq(e,this.array),i=eq(i,this.array)),this.array[t+0]=e,this.array[t+1]=i,this}setXYZ(t,e,i,s){return t*=this.itemSize,this.normalized&&(e=eq(e,this.array),i=eq(i,this.array),s=eq(s,this.array)),this.array[t+0]=e,this.array[t+1]=i,this.array[t+2]=s,this}setXYZW(t,e,i,s,r){return t*=this.itemSize,this.normalized&&(e=eq(e,this.array),i=eq(i,this.array),s=eq(s,this.array),r=eq(r,this.array)),this.array[t+0]=e,this.array[t+1]=i,this.array[t+2]=s,this.array[t+3]=r,this}onUpload(t){return this.onUploadCallback=t,this}clone(){return new this.constructor(this.array,this.itemSize).copy(this)}toJSON(){let t={itemSize:this.itemSize,type:this.array.constructor.name,array:Array.from(this.array),normalized:this.normalized};return""!==this.name&&(t.name=this.name),35044!==this.usage&&(t.usage=this.usage),t}}class sP extends sB{constructor(t,e,i){super(new Uint16Array(t),e,i)}}class sI extends sB{constructor(t,e,i){super(new Uint32Array(t),e,i)}}class sN extends sB{constructor(t,e,i){super(new Float32Array(t),e,i)}}let sR=0,sV=new iW,sL=new sn,sF=new eZ,sj=new im,sD=new im,sW=new eZ;class sU extends eN{constructor(){super(),this.isBufferGeometry=!0,Object.defineProperty(this,"id",{value:sR++}),this.uuid=ej(),this.name="",this.type="BufferGeometry",this.index=null,this.indirect=null,this.attributes={},this.morphAttributes={},this.morphTargetsRelative=!1,this.groups=[],this.boundingBox=null,this.boundingSphere=null,this.drawRange={start:0,count:1/0},this.userData={}}getIndex(){return this.index}setIndex(t){return Array.isArray(t)?this.index=new(e0(t)?sI:sP)(t,1):this.index=t,this}setIndirect(t){return this.indirect=t,this}getIndirect(){return this.indirect}getAttribute(t){return this.attributes[t]}setAttribute(t,e){return this.attributes[t]=e,this}deleteAttribute(t){return delete this.attributes[t],this}hasAttribute(t){return void 0!==this.attributes[t]}addGroup(t,e,i=0){this.groups.push({start:t,count:e,materialIndex:i})}clearGroups(){this.groups=[]}setDrawRange(t,e){this.drawRange.start=t,this.drawRange.count=e}applyMatrix4(t){let e=this.attributes.position;void 0!==e&&(e.applyMatrix4(t),e.needsUpdate=!0);let i=this.attributes.normal;if(void 0!==i){let e=new eQ().getNormalMatrix(t);i.applyNormalMatrix(e),i.needsUpdate=!0}let s=this.attributes.tangent;return void 0!==s&&(s.transformDirection(t),s.needsUpdate=!0),null!==this.boundingBox&&this.computeBoundingBox(),null!==this.boundingSphere&&this.computeBoundingSphere(),this}applyQuaternion(t){return sV.makeRotationFromQuaternion(t),this.applyMatrix4(sV),this}rotateX(t){return sV.makeRotationX(t),this.applyMatrix4(sV),this}rotateY(t){return sV.makeRotationY(t),this.applyMatrix4(sV),this}rotateZ(t){return sV.makeRotationZ(t),this.applyMatrix4(sV),this}translate(t,e,i){return sV.makeTranslation(t,e,i),this.applyMatrix4(sV),this}scale(t,e,i){return sV.makeScale(t,e,i),this.applyMatrix4(sV),this}lookAt(t){return sL.lookAt(t),sL.updateMatrix(),this.applyMatrix4(sL.matrix),this}center(){return this.computeBoundingBox(),this.boundingBox.getCenter(sF).negate(),this.translate(sF.x,sF.y,sF.z),this}setFromPoints(t){let e=this.getAttribute("position");if(void 0===e){let e=[];for(let i=0,s=t.length;i<s;i++){let s=t[i];e.push(s.x,s.y,s.z||0)}this.setAttribute("position",new sN(e,3))}else{let i=Math.min(t.length,e.count);for(let s=0;s<i;s++){let i=t[s];e.setXYZ(s,i.x,i.y,i.z||0)}t.length>e.count&&console.warn("THREE.BufferGeometry: Buffer size too small for points data. Use .dispose() and create a new geometry."),e.needsUpdate=!0}return this}computeBoundingBox(){null===this.boundingBox&&(this.boundingBox=new im);let t=this.attributes.position,e=this.morphAttributes.position;if(t&&t.isGLBufferAttribute){console.error("THREE.BufferGeometry.computeBoundingBox(): GLBufferAttribute requires a manual bounding box.",this),this.boundingBox.set(new eZ(-1/0,-1/0,-1/0),new eZ(Infinity,Infinity,Infinity));return}if(void 0!==t){if(this.boundingBox.setFromBufferAttribute(t),e)for(let t=0,i=e.length;t<i;t++){let i=e[t];sj.setFromBufferAttribute(i),this.morphTargetsRelative?(sW.addVectors(this.boundingBox.min,sj.min),this.boundingBox.expandByPoint(sW),sW.addVectors(this.boundingBox.max,sj.max),this.boundingBox.expandByPoint(sW)):(this.boundingBox.expandByPoint(sj.min),this.boundingBox.expandByPoint(sj.max))}}else this.boundingBox.makeEmpty();(isNaN(this.boundingBox.min.x)||isNaN(this.boundingBox.min.y)||isNaN(this.boundingBox.min.z))&&console.error('THREE.BufferGeometry.computeBoundingBox(): Computed min/max have NaN values. The "position" attribute is likely to have NaN values.',this)}computeBoundingSphere(){null===this.boundingSphere&&(this.boundingSphere=new iP);let t=this.attributes.position,e=this.morphAttributes.position;if(t&&t.isGLBufferAttribute){console.error("THREE.BufferGeometry.computeBoundingSphere(): GLBufferAttribute requires a manual bounding sphere.",this),this.boundingSphere.set(new eZ,1/0);return}if(t){let i=this.boundingSphere.center;if(sj.setFromBufferAttribute(t),e)for(let t=0,i=e.length;t<i;t++){let i=e[t];sD.setFromBufferAttribute(i),this.morphTargetsRelative?(sW.addVectors(sj.min,sD.min),sj.expandByPoint(sW),sW.addVectors(sj.max,sD.max),sj.expandByPoint(sW)):(sj.expandByPoint(sD.min),sj.expandByPoint(sD.max))}sj.getCenter(i);let s=0;for(let e=0,r=t.count;e<r;e++)sW.fromBufferAttribute(t,e),s=Math.max(s,i.distanceToSquared(sW));if(e)for(let r=0,n=e.length;r<n;r++){let n=e[r],a=this.morphTargetsRelative;for(let e=0,r=n.count;e<r;e++)sW.fromBufferAttribute(n,e),a&&(sF.fromBufferAttribute(t,e),sW.add(sF)),s=Math.max(s,i.distanceToSquared(sW))}this.boundingSphere.radius=Math.sqrt(s),isNaN(this.boundingSphere.radius)&&console.error('THREE.BufferGeometry.computeBoundingSphere(): Computed radius is NaN. The "position" attribute is likely to have NaN values.',this)}}computeTangents(){let t=this.index,e=this.attributes;if(null===t||void 0===e.position||void 0===e.normal||void 0===e.uv)return void console.error("THREE.BufferGeometry: .computeTangents() failed. Missing required attributes (index, position, normal or uv)");let i=e.position,s=e.normal,r=e.uv;!1===this.hasAttribute("tangent")&&this.setAttribute("tangent",new sB(new Float32Array(4*i.count),4));let n=this.getAttribute("tangent"),a=[],o=[];for(let t=0;t<i.count;t++)a[t]=new eZ,o[t]=new eZ;let h=new eZ,l=new eZ,u=new eZ,c=new eX,p=new eX,d=new eX,m=new eZ,y=new eZ,f=this.groups;0===f.length&&(f=[{start:0,count:t.count}]);for(let e=0,s=f.length;e<s;++e){let s=f[e],n=s.start,g=s.count;for(let e=n,s=n+g;e<s;e+=3)!function(t,e,s){h.fromBufferAttribute(i,t),l.fromBufferAttribute(i,e),u.fromBufferAttribute(i,s),c.fromBufferAttribute(r,t),p.fromBufferAttribute(r,e),d.fromBufferAttribute(r,s),l.sub(h),u.sub(h),p.sub(c),d.sub(c);let n=1/(p.x*d.y-d.x*p.y);isFinite(n)&&(m.copy(l).multiplyScalar(d.y).addScaledVector(u,-p.y).multiplyScalar(n),y.copy(u).multiplyScalar(p.x).addScaledVector(l,-d.x).multiplyScalar(n),a[t].add(m),a[e].add(m),a[s].add(m),o[t].add(y),o[e].add(y),o[s].add(y))}(t.getX(e+0),t.getX(e+1),t.getX(e+2))}let g=new eZ,x=new eZ,b=new eZ,M=new eZ;function w(t){b.fromBufferAttribute(s,t),M.copy(b);let e=a[t];g.copy(e),g.sub(b.multiplyScalar(b.dot(e))).normalize(),x.crossVectors(M,e);let i=x.dot(o[t]);n.setXYZW(t,g.x,g.y,g.z,i<0?-1:1)}for(let e=0,i=f.length;e<i;++e){let i=f[e],s=i.start,r=i.count;for(let e=s,i=s+r;e<i;e+=3)w(t.getX(e+0)),w(t.getX(e+1)),w(t.getX(e+2))}}computeVertexNormals(){let t=this.index,e=this.getAttribute("position");if(void 0!==e){let i=this.getAttribute("normal");if(void 0===i)i=new sB(new Float32Array(3*e.count),3),this.setAttribute("normal",i);else for(let t=0,e=i.count;t<e;t++)i.setXYZ(t,0,0,0);let s=new eZ,r=new eZ,n=new eZ,a=new eZ,o=new eZ,h=new eZ,l=new eZ,u=new eZ;if(t)for(let c=0,p=t.count;c<p;c+=3){let p=t.getX(c+0),d=t.getX(c+1),m=t.getX(c+2);s.fromBufferAttribute(e,p),r.fromBufferAttribute(e,d),n.fromBufferAttribute(e,m),l.subVectors(n,r),u.subVectors(s,r),l.cross(u),a.fromBufferAttribute(i,p),o.fromBufferAttribute(i,d),h.fromBufferAttribute(i,m),a.add(l),o.add(l),h.add(l),i.setXYZ(p,a.x,a.y,a.z),i.setXYZ(d,o.x,o.y,o.z),i.setXYZ(m,h.x,h.y,h.z)}else for(let t=0,a=e.count;t<a;t+=3)s.fromBufferAttribute(e,t+0),r.fromBufferAttribute(e,t+1),n.fromBufferAttribute(e,t+2),l.subVectors(n,r),u.subVectors(s,r),l.cross(u),i.setXYZ(t+0,l.x,l.y,l.z),i.setXYZ(t+1,l.x,l.y,l.z),i.setXYZ(t+2,l.x,l.y,l.z);this.normalizeNormals(),i.needsUpdate=!0}}normalizeNormals(){let t=this.attributes.normal;for(let e=0,i=t.count;e<i;e++)sW.fromBufferAttribute(t,e),sW.normalize(),t.setXYZ(e,sW.x,sW.y,sW.z)}toNonIndexed(){function t(t,e){let i=t.array,s=t.itemSize,r=t.normalized,n=new i.constructor(e.length*s),a=0,o=0;for(let r=0,h=e.length;r<h;r++){a=t.isInterleavedBufferAttribute?e[r]*t.data.stride+t.offset:e[r]*s;for(let t=0;t<s;t++)n[o++]=i[a++]}return new sB(n,s,r)}if(null===this.index)return console.warn("THREE.BufferGeometry.toNonIndexed(): BufferGeometry is already non-indexed."),this;let e=new sU,i=this.index.array,s=this.attributes;for(let r in s){let n=t(s[r],i);e.setAttribute(r,n)}let r=this.morphAttributes;for(let s in r){let n=[],a=r[s];for(let e=0,s=a.length;e<s;e++){let s=t(a[e],i);n.push(s)}e.morphAttributes[s]=n}e.morphTargetsRelative=this.morphTargetsRelative;let n=this.groups;for(let t=0,i=n.length;t<i;t++){let i=n[t];e.addGroup(i.start,i.count,i.materialIndex)}return e}toJSON(){let t={metadata:{version:4.7,type:"BufferGeometry",generator:"BufferGeometry.toJSON"}};if(t.uuid=this.uuid,t.type=this.type,""!==this.name&&(t.name=this.name),Object.keys(this.userData).length>0&&(t.userData=this.userData),void 0!==this.parameters){let e=this.parameters;for(let i in e)void 0!==e[i]&&(t[i]=e[i]);return t}t.data={attributes:{}};let e=this.index;null!==e&&(t.data.index={type:e.array.constructor.name,array:Array.prototype.slice.call(e.array)});let i=this.attributes;for(let e in i){let s=i[e];t.data.attributes[e]=s.toJSON(t.data)}let s={},r=!1;for(let e in this.morphAttributes){let i=this.morphAttributes[e],n=[];for(let e=0,s=i.length;e<s;e++){let s=i[e];n.push(s.toJSON(t.data))}n.length>0&&(s[e]=n,r=!0)}r&&(t.data.morphAttributes=s,t.data.morphTargetsRelative=this.morphTargetsRelative);let n=this.groups;n.length>0&&(t.data.groups=JSON.parse(JSON.stringify(n)));let a=this.boundingSphere;return null!==a&&(t.data.boundingSphere=a.toJSON()),t}clone(){return new this.constructor().copy(this)}copy(t){this.index=null,this.attributes={},this.morphAttributes={},this.groups=[],this.boundingBox=null,this.boundingSphere=null;let e={};this.name=t.name;let i=t.index;null!==i&&this.setIndex(i.clone());let s=t.attributes;for(let t in s){let i=s[t];this.setAttribute(t,i.clone(e))}let r=t.morphAttributes;for(let t in r){let i=[],s=r[t];for(let t=0,r=s.length;t<r;t++)i.push(s[t].clone(e));this.morphAttributes[t]=i}this.morphTargetsRelative=t.morphTargetsRelative;let n=t.groups;for(let t=0,e=n.length;t<e;t++){let e=n[t];this.addGroup(e.start,e.count,e.materialIndex)}let a=t.boundingBox;null!==a&&(this.boundingBox=a.clone());let o=t.boundingSphere;return null!==o&&(this.boundingSphere=o.clone()),this.drawRange.start=t.drawRange.start,this.drawRange.count=t.drawRange.count,this.userData=t.userData,this}dispose(){this.dispatchEvent({type:"dispose"})}}let sJ=new iW,sq=new iD,sH=new iP,sX=new eZ,sY=new eZ,sZ=new eZ,sG=new eZ,s$=new eZ,sQ=new eZ,sK=new eZ,s0=new eZ;class s1 extends sn{constructor(t=new sU,e=new sC){super(),this.isMesh=!0,this.type="Mesh",this.geometry=t,this.material=e,this.morphTargetDictionary=void 0,this.morphTargetInfluences=void 0,this.count=1,this.updateMorphTargets()}copy(t,e){return super.copy(t,e),void 0!==t.morphTargetInfluences&&(this.morphTargetInfluences=t.morphTargetInfluences.slice()),void 0!==t.morphTargetDictionary&&(this.morphTargetDictionary=Object.assign({},t.morphTargetDictionary)),this.material=Array.isArray(t.material)?t.material.slice():t.material,this.geometry=t.geometry,this}updateMorphTargets(){let t=this.geometry.morphAttributes,e=Object.keys(t);if(e.length>0){let i=t[e[0]];if(void 0!==i){this.morphTargetInfluences=[],this.morphTargetDictionary={};for(let t=0,e=i.length;t<e;t++){let e=i[t].name||String(t);this.morphTargetInfluences.push(0),this.morphTargetDictionary[e]=t}}}}getVertexPosition(t,e){let i=this.geometry,s=i.attributes.position,r=i.morphAttributes.position,n=i.morphTargetsRelative;e.fromBufferAttribute(s,t);let a=this.morphTargetInfluences;if(r&&a){sQ.set(0,0,0);for(let i=0,s=r.length;i<s;i++){let s=a[i],o=r[i];0!==s&&(s$.fromBufferAttribute(o,t),n?sQ.addScaledVector(s$,s):sQ.addScaledVector(s$.sub(e),s))}e.add(sQ)}return e}raycast(t,e){let i=this.geometry,s=this.material,r=this.matrixWorld;if(void 0!==s)null===i.boundingSphere&&i.computeBoundingSphere(),sH.copy(i.boundingSphere),sH.applyMatrix4(r),sq.copy(t.ray).recast(t.near),!1===sH.containsPoint(sq.origin)&&(null===sq.intersectSphere(sH,sX)||sq.origin.distanceToSquared(sX)>(t.far-t.near)**2)||(sJ.copy(r).invert(),sq.copy(t.ray).applyMatrix4(sJ),(null===i.boundingBox||!1!==sq.intersectsBox(i.boundingBox))&&this._computeIntersections(t,e,sq))}_computeIntersections(t,e,i){let s,r=this.geometry,n=this.material,a=r.index,o=r.attributes.position,h=r.attributes.uv,l=r.attributes.uv1,u=r.attributes.normal,c=r.groups,p=r.drawRange;if(null!==a)if(Array.isArray(n))for(let r=0,o=c.length;r<o;r++){let o=c[r],d=n[o.materialIndex],m=Math.max(o.start,p.start),y=Math.min(a.count,Math.min(o.start+o.count,p.start+p.count));for(let r=m;r<y;r+=3){let n=a.getX(r);(s=s2(this,d,t,i,h,l,u,n,a.getX(r+1),a.getX(r+2)))&&(s.faceIndex=Math.floor(r/3),s.face.materialIndex=o.materialIndex,e.push(s))}}else{let r=Math.max(0,p.start),o=Math.min(a.count,p.start+p.count);for(let c=r;c<o;c+=3){let r=a.getX(c);(s=s2(this,n,t,i,h,l,u,r,a.getX(c+1),a.getX(c+2)))&&(s.faceIndex=Math.floor(c/3),e.push(s))}}else if(void 0!==o)if(Array.isArray(n))for(let r=0,a=c.length;r<a;r++){let a=c[r],d=n[a.materialIndex],m=Math.max(a.start,p.start),y=Math.min(o.count,Math.min(a.start+a.count,p.start+p.count));for(let r=m;r<y;r+=3)(s=s2(this,d,t,i,h,l,u,r,r+1,r+2))&&(s.faceIndex=Math.floor(r/3),s.face.materialIndex=a.materialIndex,e.push(s))}else{let r=Math.max(0,p.start),a=Math.min(o.count,p.start+p.count);for(let o=r;o<a;o+=3)(s=s2(this,n,t,i,h,l,u,o,o+1,o+2))&&(s.faceIndex=Math.floor(o/3),e.push(s))}}}function s2(t,e,i,s,r,n,a,o,h,l){t.getVertexPosition(o,sY),t.getVertexPosition(h,sZ),t.getVertexPosition(l,sG);let u=function(t,e,i,s,r,n,a,o){let h;if(null===(e.side===m?s.intersectTriangle(a,n,r,!0,o):s.intersectTriangle(r,n,a,e.side===d,o)))return null;s0.copy(o),s0.applyMatrix4(t.matrixWorld);let l=i.ray.origin.distanceTo(s0);return l<i.near||l>i.far?null:{distance:l,point:s0.clone(),object:t}}(t,e,i,s,sY,sZ,sG,sK);if(u){let t=new eZ;sb.getBarycoord(sK,sY,sZ,sG,t),r&&(u.uv=sb.getInterpolatedAttribute(r,o,h,l,t,new eX)),n&&(u.uv1=sb.getInterpolatedAttribute(n,o,h,l,t,new eX)),a&&(u.normal=sb.getInterpolatedAttribute(a,o,h,l,t,new eZ),u.normal.dot(s.direction)>0&&u.normal.multiplyScalar(-1));let e={a:o,b:h,c:l,normal:new eZ,materialIndex:0};sb.getNormal(sY,sZ,sG,e.normal),u.face=e,u.barycoord=t}return u}class s3 extends sU{constructor(t=1,e=1,i=1,s=1,r=1,n=1){super(),this.type="BoxGeometry",this.parameters={width:t,height:e,depth:i,widthSegments:s,heightSegments:r,depthSegments:n};let a=this;s=Math.floor(s),r=Math.floor(r);let o=[],h=[],l=[],u=[],c=0,p=0;function d(t,e,i,s,r,n,d,m,y,f,g){let x=n/y,b=d/f,M=n/2,w=d/2,v=m/2,S=y+1,_=f+1,z=0,A=0,T=new eZ;for(let n=0;n<_;n++){let a=n*b-w;for(let o=0;o<S;o++){let c=o*x-M;T[t]=c*s,T[e]=a*r,T[i]=v,h.push(T.x,T.y,T.z),T[t]=0,T[e]=0,T[i]=m>0?1:-1,l.push(T.x,T.y,T.z),u.push(o/y),u.push(1-n/f),z+=1}}for(let t=0;t<f;t++)for(let e=0;e<y;e++){let i=c+e+S*t,s=c+e+S*(t+1),r=c+(e+1)+S*(t+1),n=c+(e+1)+S*t;o.push(i,s,n),o.push(s,r,n),A+=6}a.addGroup(p,A,g),p+=A,c+=z}d("z","y","x",-1,-1,i,e,t,n=Math.floor(n),r,0),d("z","y","x",1,-1,i,e,-t,n,r,1),d("x","z","y",1,1,t,i,e,s,n,2),d("x","z","y",1,-1,t,i,-e,s,n,3),d("x","y","z",1,-1,t,e,i,s,r,4),d("x","y","z",-1,-1,t,e,-i,s,r,5),this.setIndex(o),this.setAttribute("position",new sN(h,3)),this.setAttribute("normal",new sN(l,3)),this.setAttribute("uv",new sN(u,2))}copy(t){return super.copy(t),this.parameters=Object.assign({},t.parameters),this}static fromJSON(t){return new s3(t.width,t.height,t.depth,t.widthSegments,t.heightSegments,t.depthSegments)}}function s5(t){let e={};for(let i in t)for(let s in e[i]={},t[i]){let r=t[i][s];r&&(r.isColor||r.isMatrix3||r.isMatrix4||r.isVector2||r.isVector3||r.isVector4||r.isTexture||r.isQuaternion)?r.isRenderTargetTexture?(console.warn("UniformsUtils: Textures of render targets cannot be cloned via cloneUniforms() or mergeUniforms()."),e[i][s]=null):e[i][s]=r.clone():Array.isArray(r)?e[i][s]=r.slice():e[i][s]=r}return e}function s4(t){let e={};for(let i=0;i<t.length;i++){let s=s5(t[i]);for(let t in s)e[t]=s[t]}return e}function s6(t){let e=t.getRenderTarget();return null===e?t.outputColorSpace:!0===e.isXRRenderTarget?e.texture.colorSpace:e9.workingColorSpace}let s8={clone:s5,merge:s4};class s9 extends sT{constructor(t){super(),this.isShaderMaterial=!0,this.type="ShaderMaterial",this.defines={},this.uniforms={},this.uniformsGroups=[],this.vertexShader="void main() {\n	gl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n}",this.fragmentShader="void main() {\n	gl_FragColor = vec4( 1.0, 0.0, 0.0, 1.0 );\n}",this.linewidth=1,this.wireframe=!1,this.wireframeLinewidth=1,this.fog=!1,this.lights=!1,this.clipping=!1,this.forceSinglePass=!0,this.extensions={clipCullDistance:!1,multiDraw:!1},this.defaultAttributeValues={color:[1,1,1],uv:[0,0],uv1:[0,0]},this.index0AttributeName=void 0,this.uniformsNeedUpdate=!1,this.glslVersion=null,void 0!==t&&this.setValues(t)}copy(t){return super.copy(t),this.fragmentShader=t.fragmentShader,this.vertexShader=t.vertexShader,this.uniforms=s5(t.uniforms),this.uniformsGroups=function(t){let e=[];for(let i=0;i<t.length;i++)e.push(t[i].clone());return e}(t.uniformsGroups),this.defines=Object.assign({},t.defines),this.wireframe=t.wireframe,this.wireframeLinewidth=t.wireframeLinewidth,this.fog=t.fog,this.lights=t.lights,this.clipping=t.clipping,this.extensions=Object.assign({},t.extensions),this.glslVersion=t.glslVersion,this}toJSON(t){let e=super.toJSON(t);for(let i in e.glslVersion=this.glslVersion,e.uniforms={},this.uniforms){let s=this.uniforms[i].value;s&&s.isTexture?e.uniforms[i]={type:"t",value:s.toJSON(t).uuid}:s&&s.isColor?e.uniforms[i]={type:"c",value:s.getHex()}:s&&s.isVector2?e.uniforms[i]={type:"v2",value:s.toArray()}:s&&s.isVector3?e.uniforms[i]={type:"v3",value:s.toArray()}:s&&s.isVector4?e.uniforms[i]={type:"v4",value:s.toArray()}:s&&s.isMatrix3?e.uniforms[i]={type:"m3",value:s.toArray()}:s&&s.isMatrix4?e.uniforms[i]={type:"m4",value:s.toArray()}:e.uniforms[i]={value:s}}Object.keys(this.defines).length>0&&(e.defines=this.defines),e.vertexShader=this.vertexShader,e.fragmentShader=this.fragmentShader,e.lights=this.lights,e.clipping=this.clipping;let i={};for(let t in this.extensions)!0===this.extensions[t]&&(i[t]=!0);return Object.keys(i).length>0&&(e.extensions=i),e}}class s7 extends sn{constructor(){super(),this.isCamera=!0,this.type="Camera",this.matrixWorldInverse=new iW,this.projectionMatrix=new iW,this.projectionMatrixInverse=new iW,this.coordinateSystem=eI,this._reversedDepth=!1}get reversedDepth(){return this._reversedDepth}copy(t,e){return super.copy(t,e),this.matrixWorldInverse.copy(t.matrixWorldInverse),this.projectionMatrix.copy(t.projectionMatrix),this.projectionMatrixInverse.copy(t.projectionMatrixInverse),this.coordinateSystem=t.coordinateSystem,this}getWorldDirection(t){return super.getWorldDirection(t).negate()}updateMatrixWorld(t){super.updateMatrixWorld(t),this.matrixWorldInverse.copy(this.matrixWorld).invert()}updateWorldMatrix(t,e){super.updateWorldMatrix(t,e),this.matrixWorldInverse.copy(this.matrixWorld).invert()}clone(){return new this.constructor().copy(this)}}let rt=new eZ,re=new eX,ri=new eX;class rs extends s7{constructor(t=50,e=1,i=.1,s=2e3){super(),this.isPerspectiveCamera=!0,this.type="PerspectiveCamera",this.fov=t,this.zoom=1,this.near=i,this.far=s,this.focus=10,this.aspect=e,this.view=null,this.filmGauge=35,this.filmOffset=0,this.updateProjectionMatrix()}copy(t,e){return super.copy(t,e),this.fov=t.fov,this.zoom=t.zoom,this.near=t.near,this.far=t.far,this.focus=t.focus,this.aspect=t.aspect,this.view=null===t.view?null:Object.assign({},t.view),this.filmGauge=t.filmGauge,this.filmOffset=t.filmOffset,this}setFocalLength(t){let e=.5*this.getFilmHeight()/t;this.fov=2*eF*Math.atan(e),this.updateProjectionMatrix()}getFocalLength(){let t=Math.tan(.5*eL*this.fov);return .5*this.getFilmHeight()/t}getEffectiveFOV(){return 2*eF*Math.atan(Math.tan(.5*eL*this.fov)/this.zoom)}getFilmWidth(){return this.filmGauge*Math.min(this.aspect,1)}getFilmHeight(){return this.filmGauge/Math.max(this.aspect,1)}getViewBounds(t,e,i){rt.set(-1,-1,.5).applyMatrix4(this.projectionMatrixInverse),e.set(rt.x,rt.y).multiplyScalar(-t/rt.z),rt.set(1,1,.5).applyMatrix4(this.projectionMatrixInverse),i.set(rt.x,rt.y).multiplyScalar(-t/rt.z)}getViewSize(t,e){return this.getViewBounds(t,re,ri),e.subVectors(ri,re)}setViewOffset(t,e,i,s,r,n){this.aspect=t/e,null===this.view&&(this.view={enabled:!0,fullWidth:1,fullHeight:1,offsetX:0,offsetY:0,width:1,height:1}),this.view.enabled=!0,this.view.fullWidth=t,this.view.fullHeight=e,this.view.offsetX=i,this.view.offsetY=s,this.view.width=r,this.view.height=n,this.updateProjectionMatrix()}clearViewOffset(){null!==this.view&&(this.view.enabled=!1),this.updateProjectionMatrix()}updateProjectionMatrix(){let t=this.near,e=t*Math.tan(.5*eL*this.fov)/this.zoom,i=2*e,s=this.aspect*i,r=-.5*s,n=this.view;if(null!==this.view&&this.view.enabled){let t=n.fullWidth,a=n.fullHeight;r+=n.offsetX*s/t,e-=n.offsetY*i/a,s*=n.width/t,i*=n.height/a}let a=this.filmOffset;0!==a&&(r+=t*a/this.getFilmWidth()),this.projectionMatrix.makePerspective(r,r+s,e,e-i,t,this.far,this.coordinateSystem,this.reversedDepth),this.projectionMatrixInverse.copy(this.projectionMatrix).invert()}toJSON(t){let e=super.toJSON(t);return e.object.fov=this.fov,e.object.zoom=this.zoom,e.object.near=this.near,e.object.far=this.far,e.object.focus=this.focus,e.object.aspect=this.aspect,null!==this.view&&(e.object.view=Object.assign({},this.view)),e.object.filmGauge=this.filmGauge,e.object.filmOffset=this.filmOffset,e}}class rr extends sn{constructor(t,e,i){super(),this.type="CubeCamera",this.renderTarget=i,this.coordinateSystem=null,this.activeMipmapLevel=0;let s=new rs(-90,1,t,e);s.layers=this.layers,this.add(s);let r=new rs(-90,1,t,e);r.layers=this.layers,this.add(r);let n=new rs(-90,1,t,e);n.layers=this.layers,this.add(n);let a=new rs(-90,1,t,e);a.layers=this.layers,this.add(a);let o=new rs(-90,1,t,e);o.layers=this.layers,this.add(o);let h=new rs(-90,1,t,e);h.layers=this.layers,this.add(h)}updateCoordinateSystem(){let t=this.coordinateSystem,e=this.children.concat(),[i,s,r,n,a,o]=e;for(let t of e)this.remove(t);if(t===eI)i.up.set(0,1,0),i.lookAt(1,0,0),s.up.set(0,1,0),s.lookAt(-1,0,0),r.up.set(0,0,-1),r.lookAt(0,1,0),n.up.set(0,0,1),n.lookAt(0,-1,0),a.up.set(0,1,0),a.lookAt(0,0,1),o.up.set(0,1,0),o.lookAt(0,0,-1);else if(2001===t)i.up.set(0,-1,0),i.lookAt(-1,0,0),s.up.set(0,-1,0),s.lookAt(1,0,0),r.up.set(0,0,1),r.lookAt(0,1,0),n.up.set(0,0,-1),n.lookAt(0,-1,0),a.up.set(0,-1,0),a.lookAt(0,0,1),o.up.set(0,-1,0),o.lookAt(0,0,-1);else throw Error("THREE.CubeCamera.updateCoordinateSystem(): Invalid coordinate system: "+t);for(let t of e)this.add(t),t.updateMatrixWorld()}update(t,e){null===this.parent&&this.updateMatrixWorld();let{renderTarget:i,activeMipmapLevel:s}=this;this.coordinateSystem!==t.coordinateSystem&&(this.coordinateSystem=t.coordinateSystem,this.updateCoordinateSystem());let[r,n,a,o,h,l]=this.children,u=t.getRenderTarget(),c=t.getActiveCubeFace(),p=t.getActiveMipmapLevel(),d=t.xr.enabled;t.xr.enabled=!1;let m=i.texture.generateMipmaps;i.texture.generateMipmaps=!1,t.setRenderTarget(i,0,s),t.render(e,r),t.setRenderTarget(i,1,s),t.render(e,n),t.setRenderTarget(i,2,s),t.render(e,a),t.setRenderTarget(i,3,s),t.render(e,o),t.setRenderTarget(i,4,s),t.render(e,h),i.texture.generateMipmaps=m,t.setRenderTarget(i,5,s),t.render(e,l),t.setRenderTarget(u,c,p),t.xr.enabled=d,i.texture.needsPMREMUpdate=!0}}class rn extends ih{constructor(t=[],e=th,i,s,r,n,a,o,h,l){super(t,e,i,s,r,n,a,o,h,l),this.isCubeTexture=!0,this.flipY=!1}get images(){return this.image}set images(t){this.image=t}}class ra extends ic{constructor(t=1,e={}){super(t,t,e),this.isWebGLCubeRenderTarget=!0;let i={width:t,height:t,depth:1};this.texture=new rn([i,i,i,i,i,i]),this._setTextureOptions(e),this.texture.isRenderTargetTexture=!0}fromEquirectangularTexture(t,e){this.texture.type=e.type,this.texture.colorSpace=e.colorSpace,this.texture.generateMipmaps=e.generateMipmaps,this.texture.minFilter=e.minFilter,this.texture.magFilter=e.magFilter;let i={uniforms:{tEquirect:{value:null}},vertexShader:`

				varying vec3 vWorldDirection;

				vec3 transformDirection( in vec3 dir, in mat4 matrix ) {

					return normalize( ( matrix * vec4( dir, 0.0 ) ).xyz );

				}

				void main() {

					vWorldDirection = transformDirection( position, modelMatrix );

					#include <begin_vertex>
					#include <project_vertex>

				}
			`,fragmentShader:`

				uniform sampler2D tEquirect;

				varying vec3 vWorldDirection;

				#include <common>

				void main() {

					vec3 direction = normalize( vWorldDirection );

					vec2 sampleUV = equirectUv( direction );

					gl_FragColor = texture2D( tEquirect, sampleUV );

				}
			`},s=new s3(5,5,5),r=new s9({name:"CubemapFromEquirect",uniforms:s5(i.uniforms),vertexShader:i.vertexShader,fragmentShader:i.fragmentShader,side:m,blending:f});r.uniforms.tEquirect.value=e;let n=new s1(s,r),a=e.minFilter;return e.minFilter===tw&&(e.minFilter=tb),new rr(1,10,this).update(t,n),e.minFilter=a,n.geometry.dispose(),n.material.dispose(),this}clear(t,e=!0,i=!0,s=!0){let r=t.getRenderTarget();for(let r=0;r<6;r++)t.setRenderTarget(this,r),t.clear(e,i,s);t.setRenderTarget(r)}}class ro extends sn{constructor(){super(),this.isGroup=!0,this.type="Group"}}let rh={type:"move"};class rl{constructor(){this._targetRay=null,this._grip=null,this._hand=null}getHandSpace(){return null===this._hand&&(this._hand=new ro,this._hand.matrixAutoUpdate=!1,this._hand.visible=!1,this._hand.joints={},this._hand.inputState={pinching:!1}),this._hand}getTargetRaySpace(){return null===this._targetRay&&(this._targetRay=new ro,this._targetRay.matrixAutoUpdate=!1,this._targetRay.visible=!1,this._targetRay.hasLinearVelocity=!1,this._targetRay.linearVelocity=new eZ,this._targetRay.hasAngularVelocity=!1,this._targetRay.angularVelocity=new eZ),this._targetRay}getGripSpace(){return null===this._grip&&(this._grip=new ro,this._grip.matrixAutoUpdate=!1,this._grip.visible=!1,this._grip.hasLinearVelocity=!1,this._grip.linearVelocity=new eZ,this._grip.hasAngularVelocity=!1,this._grip.angularVelocity=new eZ),this._grip}dispatchEvent(t){return null!==this._targetRay&&this._targetRay.dispatchEvent(t),null!==this._grip&&this._grip.dispatchEvent(t),null!==this._hand&&this._hand.dispatchEvent(t),this}connect(t){if(t&&t.hand){let e=this._hand;if(e)for(let i of t.hand.values())this._getHandJoint(e,i)}return this.dispatchEvent({type:"connected",data:t}),this}disconnect(t){return this.dispatchEvent({type:"disconnected",data:t}),null!==this._targetRay&&(this._targetRay.visible=!1),null!==this._grip&&(this._grip.visible=!1),null!==this._hand&&(this._hand.visible=!1),this}update(t,e,i){let s=null,r=null,n=null,a=this._targetRay,o=this._grip,h=this._hand;if(t&&"visible-blurred"!==e.session.visibilityState){if(h&&t.hand){for(let s of(n=!0,t.hand.values())){let t=e.getJointPose(s,i),r=this._getHandJoint(h,s);null!==t&&(r.matrix.fromArray(t.transform.matrix),r.matrix.decompose(r.position,r.rotation,r.scale),r.matrixWorldNeedsUpdate=!0,r.jointRadius=t.radius),r.visible=null!==t}let s=h.joints["index-finger-tip"],r=h.joints["thumb-tip"],a=s.position.distanceTo(r.position);h.inputState.pinching&&a>.025?(h.inputState.pinching=!1,this.dispatchEvent({type:"pinchend",handedness:t.handedness,target:this})):!h.inputState.pinching&&a<=.015&&(h.inputState.pinching=!0,this.dispatchEvent({type:"pinchstart",handedness:t.handedness,target:this}))}else null!==o&&t.gripSpace&&null!==(r=e.getPose(t.gripSpace,i))&&(o.matrix.fromArray(r.transform.matrix),o.matrix.decompose(o.position,o.rotation,o.scale),o.matrixWorldNeedsUpdate=!0,r.linearVelocity?(o.hasLinearVelocity=!0,o.linearVelocity.copy(r.linearVelocity)):o.hasLinearVelocity=!1,r.angularVelocity?(o.hasAngularVelocity=!0,o.angularVelocity.copy(r.angularVelocity)):o.hasAngularVelocity=!1);null!==a&&(null===(s=e.getPose(t.targetRaySpace,i))&&null!==r&&(s=r),null!==s&&(a.matrix.fromArray(s.transform.matrix),a.matrix.decompose(a.position,a.rotation,a.scale),a.matrixWorldNeedsUpdate=!0,s.linearVelocity?(a.hasLinearVelocity=!0,a.linearVelocity.copy(s.linearVelocity)):a.hasLinearVelocity=!1,s.angularVelocity?(a.hasAngularVelocity=!0,a.angularVelocity.copy(s.angularVelocity)):a.hasAngularVelocity=!1,this.dispatchEvent(rh)))}return null!==a&&(a.visible=null!==s),null!==o&&(o.visible=null!==r),null!==h&&(h.visible=null!==n),this}_getHandJoint(t,e){if(void 0===t.joints[e.jointName]){let i=new ro;i.matrixAutoUpdate=!1,i.visible=!1,t.joints[e.jointName]=i,t.add(i)}return t.joints[e.jointName]}}class ru extends sn{constructor(){super(),this.isScene=!0,this.type="Scene",this.background=null,this.environment=null,this.fog=null,this.backgroundBlurriness=0,this.backgroundIntensity=1,this.backgroundRotation=new iQ,this.environmentIntensity=1,this.environmentRotation=new iQ,this.overrideMaterial=null,"undefined"!=typeof __THREE_DEVTOOLS__&&__THREE_DEVTOOLS__.dispatchEvent(new CustomEvent("observe",{detail:this}))}copy(t,e){return super.copy(t,e),null!==t.background&&(this.background=t.background.clone()),null!==t.environment&&(this.environment=t.environment.clone()),null!==t.fog&&(this.fog=t.fog.clone()),this.backgroundBlurriness=t.backgroundBlurriness,this.backgroundIntensity=t.backgroundIntensity,this.backgroundRotation.copy(t.backgroundRotation),this.environmentIntensity=t.environmentIntensity,this.environmentRotation.copy(t.environmentRotation),null!==t.overrideMaterial&&(this.overrideMaterial=t.overrideMaterial.clone()),this.matrixAutoUpdate=t.matrixAutoUpdate,this}toJSON(t){let e=super.toJSON(t);return null!==this.fog&&(e.object.fog=this.fog.toJSON()),this.backgroundBlurriness>0&&(e.object.backgroundBlurriness=this.backgroundBlurriness),1!==this.backgroundIntensity&&(e.object.backgroundIntensity=this.backgroundIntensity),e.object.backgroundRotation=this.backgroundRotation.toArray(),1!==this.environmentIntensity&&(e.object.environmentIntensity=this.environmentIntensity),e.object.environmentRotation=this.environmentRotation.toArray(),e}}class rc{constructor(t,e){this.isInterleavedBuffer=!0,this.array=t,this.stride=e,this.count=void 0!==t?t.length/e:0,this.usage=35044,this.updateRanges=[],this.version=0,this.uuid=ej()}onUploadCallback(){}set needsUpdate(t){!0===t&&this.version++}setUsage(t){return this.usage=t,this}addUpdateRange(t,e){this.updateRanges.push({start:t,count:e})}clearUpdateRanges(){this.updateRanges.length=0}copy(t){return this.array=new t.array.constructor(t.array),this.count=t.count,this.stride=t.stride,this.usage=t.usage,this}copyAt(t,e,i){t*=this.stride,i*=e.stride;for(let s=0,r=this.stride;s<r;s++)this.array[t+s]=e.array[i+s];return this}set(t,e=0){return this.array.set(t,e),this}clone(t){void 0===t.arrayBuffers&&(t.arrayBuffers={}),void 0===this.array.buffer._uuid&&(this.array.buffer._uuid=ej()),void 0===t.arrayBuffers[this.array.buffer._uuid]&&(t.arrayBuffers[this.array.buffer._uuid]=this.array.slice(0).buffer);let e=new this.array.constructor(t.arrayBuffers[this.array.buffer._uuid]),i=new this.constructor(e,this.stride);return i.setUsage(this.usage),i}onUpload(t){return this.onUploadCallback=t,this}toJSON(t){return void 0===t.arrayBuffers&&(t.arrayBuffers={}),void 0===this.array.buffer._uuid&&(this.array.buffer._uuid=ej()),void 0===t.arrayBuffers[this.array.buffer._uuid]&&(t.arrayBuffers[this.array.buffer._uuid]=Array.from(new Uint32Array(this.array.buffer))),{uuid:this.uuid,buffer:this.array.buffer._uuid,type:this.array.constructor.name,stride:this.stride}}}let rp=new eZ;class rd{constructor(t,e,i,s=!1){this.isInterleavedBufferAttribute=!0,this.name="",this.data=t,this.itemSize=e,this.offset=i,this.normalized=s}get count(){return this.data.count}get array(){return this.data.array}set needsUpdate(t){this.data.needsUpdate=t}applyMatrix4(t){for(let e=0,i=this.data.count;e<i;e++)rp.fromBufferAttribute(this,e),rp.applyMatrix4(t),this.setXYZ(e,rp.x,rp.y,rp.z);return this}applyNormalMatrix(t){for(let e=0,i=this.count;e<i;e++)rp.fromBufferAttribute(this,e),rp.applyNormalMatrix(t),this.setXYZ(e,rp.x,rp.y,rp.z);return this}transformDirection(t){for(let e=0,i=this.count;e<i;e++)rp.fromBufferAttribute(this,e),rp.transformDirection(t),this.setXYZ(e,rp.x,rp.y,rp.z);return this}getComponent(t,e){let i=this.array[t*this.data.stride+this.offset+e];return this.normalized&&(i=eJ(i,this.array)),i}setComponent(t,e,i){return this.normalized&&(i=eq(i,this.array)),this.data.array[t*this.data.stride+this.offset+e]=i,this}setX(t,e){return this.normalized&&(e=eq(e,this.array)),this.data.array[t*this.data.stride+this.offset]=e,this}setY(t,e){return this.normalized&&(e=eq(e,this.array)),this.data.array[t*this.data.stride+this.offset+1]=e,this}setZ(t,e){return this.normalized&&(e=eq(e,this.array)),this.data.array[t*this.data.stride+this.offset+2]=e,this}setW(t,e){return this.normalized&&(e=eq(e,this.array)),this.data.array[t*this.data.stride+this.offset+3]=e,this}getX(t){let e=this.data.array[t*this.data.stride+this.offset];return this.normalized&&(e=eJ(e,this.array)),e}getY(t){let e=this.data.array[t*this.data.stride+this.offset+1];return this.normalized&&(e=eJ(e,this.array)),e}getZ(t){let e=this.data.array[t*this.data.stride+this.offset+2];return this.normalized&&(e=eJ(e,this.array)),e}getW(t){let e=this.data.array[t*this.data.stride+this.offset+3];return this.normalized&&(e=eJ(e,this.array)),e}setXY(t,e,i){return t=t*this.data.stride+this.offset,this.normalized&&(e=eq(e,this.array),i=eq(i,this.array)),this.data.array[t+0]=e,this.data.array[t+1]=i,this}setXYZ(t,e,i,s){return t=t*this.data.stride+this.offset,this.normalized&&(e=eq(e,this.array),i=eq(i,this.array),s=eq(s,this.array)),this.data.array[t+0]=e,this.data.array[t+1]=i,this.data.array[t+2]=s,this}setXYZW(t,e,i,s,r){return t=t*this.data.stride+this.offset,this.normalized&&(e=eq(e,this.array),i=eq(i,this.array),s=eq(s,this.array),r=eq(r,this.array)),this.data.array[t+0]=e,this.data.array[t+1]=i,this.data.array[t+2]=s,this.data.array[t+3]=r,this}clone(t){if(void 0!==t)return void 0===t.interleavedBuffers&&(t.interleavedBuffers={}),void 0===t.interleavedBuffers[this.data.uuid]&&(t.interleavedBuffers[this.data.uuid]=this.data.clone(t)),new rd(t.interleavedBuffers[this.data.uuid],this.itemSize,this.offset,this.normalized);{console.log("THREE.InterleavedBufferAttribute.clone(): Cloning an interleaved buffer attribute will de-interleave buffer data.");let t=[];for(let e=0;e<this.count;e++){let i=e*this.data.stride+this.offset;for(let e=0;e<this.itemSize;e++)t.push(this.data.array[i+e])}return new sB(new this.array.constructor(t),this.itemSize,this.normalized)}}toJSON(t){if(void 0!==t)return void 0===t.interleavedBuffers&&(t.interleavedBuffers={}),void 0===t.interleavedBuffers[this.data.uuid]&&(t.interleavedBuffers[this.data.uuid]=this.data.toJSON(t)),{isInterleavedBufferAttribute:!0,itemSize:this.itemSize,data:this.data.uuid,offset:this.offset,normalized:this.normalized};{console.log("THREE.InterleavedBufferAttribute.toJSON(): Serializing an interleaved buffer attribute will de-interleave buffer data.");let t=[];for(let e=0;e<this.count;e++){let i=e*this.data.stride+this.offset;for(let e=0;e<this.itemSize;e++)t.push(this.data.array[i+e])}return{itemSize:this.itemSize,type:this.array.constructor.name,array:t,normalized:this.normalized}}}}let rm=new eZ,ry=new il,rf=new il,rg=new eZ,rx=new iW,rb=new eZ,rM=new iP,rw=new iW,rv=new iD;class rS extends s1{constructor(t,e){super(t,e),this.isSkinnedMesh=!0,this.type="SkinnedMesh",this.bindMode=to,this.bindMatrix=new iW,this.bindMatrixInverse=new iW,this.boundingBox=null,this.boundingSphere=null}computeBoundingBox(){let t=this.geometry;null===this.boundingBox&&(this.boundingBox=new im),this.boundingBox.makeEmpty();let e=t.getAttribute("position");for(let t=0;t<e.count;t++)this.getVertexPosition(t,rb),this.boundingBox.expandByPoint(rb)}computeBoundingSphere(){let t=this.geometry;null===this.boundingSphere&&(this.boundingSphere=new iP),this.boundingSphere.makeEmpty();let e=t.getAttribute("position");for(let t=0;t<e.count;t++)this.getVertexPosition(t,rb),this.boundingSphere.expandByPoint(rb)}copy(t,e){return super.copy(t,e),this.bindMode=t.bindMode,this.bindMatrix.copy(t.bindMatrix),this.bindMatrixInverse.copy(t.bindMatrixInverse),this.skeleton=t.skeleton,null!==t.boundingBox&&(this.boundingBox=t.boundingBox.clone()),null!==t.boundingSphere&&(this.boundingSphere=t.boundingSphere.clone()),this}raycast(t,e){let i=this.material,s=this.matrixWorld;if(void 0!==i)null===this.boundingSphere&&this.computeBoundingSphere(),rM.copy(this.boundingSphere),rM.applyMatrix4(s),!1!==t.ray.intersectsSphere(rM)&&(rw.copy(s).invert(),rv.copy(t.ray).applyMatrix4(rw),(null===this.boundingBox||!1!==rv.intersectsBox(this.boundingBox))&&this._computeIntersections(t,e,rv))}getVertexPosition(t,e){return super.getVertexPosition(t,e),this.applyBoneTransform(t,e),e}bind(t,e){this.skeleton=t,void 0===e&&(this.updateMatrixWorld(!0),this.skeleton.calculateInverses(),e=this.matrixWorld),this.bindMatrix.copy(e),this.bindMatrixInverse.copy(e).invert()}pose(){this.skeleton.pose()}normalizeSkinWeights(){let t=new il,e=this.geometry.attributes.skinWeight;for(let i=0,s=e.count;i<s;i++){t.fromBufferAttribute(e,i);let s=1/t.manhattanLength();s!==1/0?t.multiplyScalar(s):t.set(1,0,0,0),e.setXYZW(i,t.x,t.y,t.z,t.w)}}updateMatrixWorld(t){super.updateMatrixWorld(t),this.bindMode===to?this.bindMatrixInverse.copy(this.matrixWorld).invert():"detached"===this.bindMode?this.bindMatrixInverse.copy(this.bindMatrix).invert():console.warn("THREE.SkinnedMesh: Unrecognized bindMode: "+this.bindMode)}applyBoneTransform(t,e){let i=this.skeleton,s=this.geometry;ry.fromBufferAttribute(s.attributes.skinIndex,t),rf.fromBufferAttribute(s.attributes.skinWeight,t),rm.copy(e).applyMatrix4(this.bindMatrix),e.set(0,0,0);for(let t=0;t<4;t++){let s=rf.getComponent(t);if(0!==s){let r=ry.getComponent(t);rx.multiplyMatrices(i.bones[r].matrixWorld,i.boneInverses[r]),e.addScaledVector(rg.copy(rm).applyMatrix4(rx),s)}}return e.applyMatrix4(this.bindMatrixInverse)}}class r_ extends sn{constructor(){super(),this.isBone=!0,this.type="Bone"}}class rz extends ih{constructor(t=null,e=1,i=1,s,r,n,a,o,h=tf,l=tf,u,c){super(null,n,a,o,h,l,s,r,u,c),this.isDataTexture=!0,this.image={data:t,width:e,height:i},this.generateMipmaps=!1,this.flipY=!1,this.unpackAlignment=1}}let rA=new iW,rT=new iW;class rC{constructor(t=[],e=[]){this.uuid=ej(),this.bones=t.slice(0),this.boneInverses=e,this.boneMatrices=null,this.boneTexture=null,this.init()}init(){let t=this.bones,e=this.boneInverses;if(this.boneMatrices=new Float32Array(16*t.length),0===e.length)this.calculateInverses();else if(t.length!==e.length){console.warn("THREE.Skeleton: Number of inverse bone matrices does not match amount of bones."),this.boneInverses=[];for(let t=0,e=this.bones.length;t<e;t++)this.boneInverses.push(new iW)}}calculateInverses(){this.boneInverses.length=0;for(let t=0,e=this.bones.length;t<e;t++){let e=new iW;this.bones[t]&&e.copy(this.bones[t].matrixWorld).invert(),this.boneInverses.push(e)}}pose(){for(let t=0,e=this.bones.length;t<e;t++){let e=this.bones[t];e&&e.matrixWorld.copy(this.boneInverses[t]).invert()}for(let t=0,e=this.bones.length;t<e;t++){let e=this.bones[t];e&&(e.parent&&e.parent.isBone?(e.matrix.copy(e.parent.matrixWorld).invert(),e.matrix.multiply(e.matrixWorld)):e.matrix.copy(e.matrixWorld),e.matrix.decompose(e.position,e.quaternion,e.scale))}}update(){let t=this.bones,e=this.boneInverses,i=this.boneMatrices,s=this.boneTexture;for(let s=0,r=t.length;s<r;s++){let r=t[s]?t[s].matrixWorld:rT;rA.multiplyMatrices(r,e[s]),rA.toArray(i,16*s)}null!==s&&(s.needsUpdate=!0)}clone(){return new rC(this.bones,this.boneInverses)}computeBoneTexture(){let t=Math.sqrt(4*this.bones.length),e=new Float32Array((t=Math.max(t=4*Math.ceil(t/4),4))*t*4);e.set(this.boneMatrices);let i=new rz(e,t,t,tR,tC);return i.needsUpdate=!0,this.boneMatrices=e,this.boneTexture=i,this}getBoneByName(t){for(let e=0,i=this.bones.length;e<i;e++){let i=this.bones[e];if(i.name===t)return i}}dispose(){null!==this.boneTexture&&(this.boneTexture.dispose(),this.boneTexture=null)}fromJSON(t,e){this.uuid=t.uuid;for(let i=0,s=t.bones.length;i<s;i++){let s=t.bones[i],r=e[s];void 0===r&&(console.warn("THREE.Skeleton: No bone found with UUID:",s),r=new r_),this.bones.push(r),this.boneInverses.push(new iW().fromArray(t.boneInverses[i]))}return this.init(),this}toJSON(){let t={metadata:{version:4.7,type:"Skeleton",generator:"Skeleton.toJSON"},bones:[],boneInverses:[]};t.uuid=this.uuid;let e=this.bones,i=this.boneInverses;for(let s=0,r=e.length;s<r;s++){let r=e[s];t.bones.push(r.uuid);let n=i[s];t.boneInverses.push(n.toArray())}return t}}class rk extends sB{constructor(t,e,i,s=1){super(t,e,i),this.isInstancedBufferAttribute=!0,this.meshPerAttribute=s}copy(t){return super.copy(t),this.meshPerAttribute=t.meshPerAttribute,this}toJSON(){let t=super.toJSON();return t.meshPerAttribute=this.meshPerAttribute,t.isInstancedBufferAttribute=!0,t}}let rE=new iW,rO=new iW,rB=[],rP=new im,rI=new iW,rN=new s1,rR=new iP;class rV extends s1{constructor(t,e,i){super(t,e),this.isInstancedMesh=!0,this.instanceMatrix=new rk(new Float32Array(16*i),16),this.instanceColor=null,this.morphTexture=null,this.count=i,this.boundingBox=null,this.boundingSphere=null;for(let t=0;t<i;t++)this.setMatrixAt(t,rI)}computeBoundingBox(){let t=this.geometry,e=this.count;null===this.boundingBox&&(this.boundingBox=new im),null===t.boundingBox&&t.computeBoundingBox(),this.boundingBox.makeEmpty();for(let i=0;i<e;i++)this.getMatrixAt(i,rE),rP.copy(t.boundingBox).applyMatrix4(rE),this.boundingBox.union(rP)}computeBoundingSphere(){let t=this.geometry,e=this.count;null===this.boundingSphere&&(this.boundingSphere=new iP),null===t.boundingSphere&&t.computeBoundingSphere(),this.boundingSphere.makeEmpty();for(let i=0;i<e;i++)this.getMatrixAt(i,rE),rR.copy(t.boundingSphere).applyMatrix4(rE),this.boundingSphere.union(rR)}copy(t,e){return super.copy(t,e),this.instanceMatrix.copy(t.instanceMatrix),null!==t.morphTexture&&(this.morphTexture=t.morphTexture.clone()),null!==t.instanceColor&&(this.instanceColor=t.instanceColor.clone()),this.count=t.count,null!==t.boundingBox&&(this.boundingBox=t.boundingBox.clone()),null!==t.boundingSphere&&(this.boundingSphere=t.boundingSphere.clone()),this}getColorAt(t,e){e.fromArray(this.instanceColor.array,3*t)}getMatrixAt(t,e){e.fromArray(this.instanceMatrix.array,16*t)}getMorphAt(t,e){let i=e.morphTargetInfluences,s=this.morphTexture.source.data.data,r=t*(i.length+1)+1;for(let t=0;t<i.length;t++)i[t]=s[r+t]}raycast(t,e){let i=this.matrixWorld,s=this.count;if((rN.geometry=this.geometry,rN.material=this.material,void 0!==rN.material)&&(null===this.boundingSphere&&this.computeBoundingSphere(),rR.copy(this.boundingSphere),rR.applyMatrix4(i),!1!==t.ray.intersectsSphere(rR)))for(let r=0;r<s;r++){this.getMatrixAt(r,rE),rO.multiplyMatrices(i,rE),rN.matrixWorld=rO,rN.raycast(t,rB);for(let t=0,i=rB.length;t<i;t++){let i=rB[t];i.instanceId=r,i.object=this,e.push(i)}rB.length=0}}setColorAt(t,e){null===this.instanceColor&&(this.instanceColor=new rk(new Float32Array(3*this.instanceMatrix.count).fill(1),3)),e.toArray(this.instanceColor.array,3*t)}setMatrixAt(t,e){e.toArray(this.instanceMatrix.array,16*t)}setMorphAt(t,e){let i=e.morphTargetInfluences,s=i.length+1;null===this.morphTexture&&(this.morphTexture=new rz(new Float32Array(s*this.count),s,this.count,tF,tC));let r=this.morphTexture.source.data.data,n=0;for(let t=0;t<i.length;t++)n+=i[t];let a=this.geometry.morphTargetsRelative?1:1-n,o=s*t;r[o]=a,r.set(i,o+1)}updateMorphTargets(){}dispose(){this.dispatchEvent({type:"dispose"}),null!==this.morphTexture&&(this.morphTexture.dispose(),this.morphTexture=null)}}let rL=new eZ,rF=new eZ,rj=new eQ;class rD{constructor(t=new eZ(1,0,0),e=0){this.isPlane=!0,this.normal=t,this.constant=e}set(t,e){return this.normal.copy(t),this.constant=e,this}setComponents(t,e,i,s){return this.normal.set(t,e,i),this.constant=s,this}setFromNormalAndCoplanarPoint(t,e){return this.normal.copy(t),this.constant=-e.dot(this.normal),this}setFromCoplanarPoints(t,e,i){let s=rL.subVectors(i,e).cross(rF.subVectors(t,e)).normalize();return this.setFromNormalAndCoplanarPoint(s,t),this}copy(t){return this.normal.copy(t.normal),this.constant=t.constant,this}normalize(){let t=1/this.normal.length();return this.normal.multiplyScalar(t),this.constant*=t,this}negate(){return this.constant*=-1,this.normal.negate(),this}distanceToPoint(t){return this.normal.dot(t)+this.constant}distanceToSphere(t){return this.distanceToPoint(t.center)-t.radius}projectPoint(t,e){return e.copy(t).addScaledVector(this.normal,-this.distanceToPoint(t))}intersectLine(t,e){let i=t.delta(rL),s=this.normal.dot(i);if(0===s)return 0===this.distanceToPoint(t.start)?e.copy(t.start):null;let r=-(t.start.dot(this.normal)+this.constant)/s;return r<0||r>1?null:e.copy(t.start).addScaledVector(i,r)}intersectsLine(t){let e=this.distanceToPoint(t.start),i=this.distanceToPoint(t.end);return e<0&&i>0||i<0&&e>0}intersectsBox(t){return t.intersectsPlane(this)}intersectsSphere(t){return t.intersectsPlane(this)}coplanarPoint(t){return t.copy(this.normal).multiplyScalar(-this.constant)}applyMatrix4(t,e){let i=e||rj.getNormalMatrix(t),s=this.coplanarPoint(rL).applyMatrix4(t),r=this.normal.applyMatrix3(i).normalize();return this.constant=-s.dot(r),this}translate(t){return this.constant-=t.dot(this.normal),this}equals(t){return t.normal.equals(this.normal)&&t.constant===this.constant}clone(){return new this.constructor().copy(this)}}let rW=new iP,rU=new eX(.5,.5),rJ=new eZ;class rq{constructor(t=new rD,e=new rD,i=new rD,s=new rD,r=new rD,n=new rD){this.planes=[t,e,i,s,r,n]}set(t,e,i,s,r,n){let a=this.planes;return a[0].copy(t),a[1].copy(e),a[2].copy(i),a[3].copy(s),a[4].copy(r),a[5].copy(n),this}copy(t){let e=this.planes;for(let i=0;i<6;i++)e[i].copy(t.planes[i]);return this}setFromProjectionMatrix(t,e=eI,i=!1){let s=this.planes,r=t.elements,n=r[0],a=r[1],o=r[2],h=r[3],l=r[4],u=r[5],c=r[6],p=r[7],d=r[8],m=r[9],y=r[10],f=r[11],g=r[12],x=r[13],b=r[14],M=r[15];if(s[0].setComponents(h-n,p-l,f-d,M-g).normalize(),s[1].setComponents(h+n,p+l,f+d,M+g).normalize(),s[2].setComponents(h+a,p+u,f+m,M+x).normalize(),s[3].setComponents(h-a,p-u,f-m,M-x).normalize(),i)s[4].setComponents(o,c,y,b).normalize(),s[5].setComponents(h-o,p-c,f-y,M-b).normalize();else if(s[4].setComponents(h-o,p-c,f-y,M-b).normalize(),e===eI)s[5].setComponents(h+o,p+c,f+y,M+b).normalize();else if(2001===e)s[5].setComponents(o,c,y,b).normalize();else throw Error("THREE.Frustum.setFromProjectionMatrix(): Invalid coordinate system: "+e);return this}intersectsObject(t){if(void 0!==t.boundingSphere)null===t.boundingSphere&&t.computeBoundingSphere(),rW.copy(t.boundingSphere).applyMatrix4(t.matrixWorld);else{let e=t.geometry;null===e.boundingSphere&&e.computeBoundingSphere(),rW.copy(e.boundingSphere).applyMatrix4(t.matrixWorld)}return this.intersectsSphere(rW)}intersectsSprite(t){return rW.center.set(0,0,0),rW.radius=.*********1865476+rU.distanceTo(t.center),rW.applyMatrix4(t.matrixWorld),this.intersectsSphere(rW)}intersectsSphere(t){let e=this.planes,i=t.center,s=-t.radius;for(let t=0;t<6;t++)if(e[t].distanceToPoint(i)<s)return!1;return!0}intersectsBox(t){let e=this.planes;for(let i=0;i<6;i++){let s=e[i];if(rJ.x=s.normal.x>0?t.max.x:t.min.x,rJ.y=s.normal.y>0?t.max.y:t.min.y,rJ.z=s.normal.z>0?t.max.z:t.min.z,0>s.distanceToPoint(rJ))return!1}return!0}containsPoint(t){let e=this.planes;for(let i=0;i<6;i++)if(0>e[i].distanceToPoint(t))return!1;return!0}clone(){return new this.constructor().copy(this)}}let rH=new iW,rX=new rq;class rY{constructor(){this.coordinateSystem=eI}intersectsObject(t,e){if(!e.isArrayCamera||0===e.cameras.length)return!1;for(let i=0;i<e.cameras.length;i++){let s=e.cameras[i];if(rH.multiplyMatrices(s.projectionMatrix,s.matrixWorldInverse),rX.setFromProjectionMatrix(rH,s.coordinateSystem,s.reversedDepth),rX.intersectsObject(t))return!0}return!1}intersectsSprite(t,e){if(!e||!e.cameras||0===e.cameras.length)return!1;for(let i=0;i<e.cameras.length;i++){let s=e.cameras[i];if(rH.multiplyMatrices(s.projectionMatrix,s.matrixWorldInverse),rX.setFromProjectionMatrix(rH,s.coordinateSystem,s.reversedDepth),rX.intersectsSprite(t))return!0}return!1}intersectsSphere(t,e){if(!e||!e.cameras||0===e.cameras.length)return!1;for(let i=0;i<e.cameras.length;i++){let s=e.cameras[i];if(rH.multiplyMatrices(s.projectionMatrix,s.matrixWorldInverse),rX.setFromProjectionMatrix(rH,s.coordinateSystem,s.reversedDepth),rX.intersectsSphere(t))return!0}return!1}intersectsBox(t,e){if(!e||!e.cameras||0===e.cameras.length)return!1;for(let i=0;i<e.cameras.length;i++){let s=e.cameras[i];if(rH.multiplyMatrices(s.projectionMatrix,s.matrixWorldInverse),rX.setFromProjectionMatrix(rH,s.coordinateSystem,s.reversedDepth),rX.intersectsBox(t))return!0}return!1}containsPoint(t,e){if(!e||!e.cameras||0===e.cameras.length)return!1;for(let i=0;i<e.cameras.length;i++){let s=e.cameras[i];if(rH.multiplyMatrices(s.projectionMatrix,s.matrixWorldInverse),rX.setFromProjectionMatrix(rH,s.coordinateSystem,s.reversedDepth),rX.containsPoint(t))return!0}return!1}clone(){return new rY}}class rZ extends sT{constructor(t){super(),this.isLineBasicMaterial=!0,this.type="LineBasicMaterial",this.color=new s_(0xffffff),this.map=null,this.linewidth=1,this.linecap="round",this.linejoin="round",this.fog=!0,this.setValues(t)}copy(t){return super.copy(t),this.color.copy(t.color),this.map=t.map,this.linewidth=t.linewidth,this.linecap=t.linecap,this.linejoin=t.linejoin,this.fog=t.fog,this}}let rG=new eZ,r$=new eZ,rQ=new iW,rK=new iD,r0=new iP,r1=new eZ,r2=new eZ;class r3 extends sn{constructor(t=new sU,e=new rZ){super(),this.isLine=!0,this.type="Line",this.geometry=t,this.material=e,this.morphTargetDictionary=void 0,this.morphTargetInfluences=void 0,this.updateMorphTargets()}copy(t,e){return super.copy(t,e),this.material=Array.isArray(t.material)?t.material.slice():t.material,this.geometry=t.geometry,this}computeLineDistances(){let t=this.geometry;if(null===t.index){let e=t.attributes.position,i=[0];for(let t=1,s=e.count;t<s;t++)rG.fromBufferAttribute(e,t-1),r$.fromBufferAttribute(e,t),i[t]=i[t-1],i[t]+=rG.distanceTo(r$);t.setAttribute("lineDistance",new sN(i,1))}else console.warn("THREE.Line.computeLineDistances(): Computation only possible with non-indexed BufferGeometry.");return this}raycast(t,e){let i=this.geometry,s=this.matrixWorld,r=t.params.Line.threshold,n=i.drawRange;if(null===i.boundingSphere&&i.computeBoundingSphere(),r0.copy(i.boundingSphere),r0.applyMatrix4(s),r0.radius+=r,!1===t.ray.intersectsSphere(r0))return;rQ.copy(s).invert(),rK.copy(t.ray).applyMatrix4(rQ);let a=r/((this.scale.x+this.scale.y+this.scale.z)/3),o=a*a,h=this.isLineSegments?2:1,l=i.index,u=i.attributes.position;if(null!==l){let i=Math.max(0,n.start),s=Math.min(l.count,n.start+n.count);for(let r=i,n=s-1;r<n;r+=h){let i=r5(this,t,rK,o,l.getX(r),l.getX(r+1),r);i&&e.push(i)}if(this.isLineLoop){let r=r5(this,t,rK,o,l.getX(s-1),l.getX(i),s-1);r&&e.push(r)}}else{let i=Math.max(0,n.start),s=Math.min(u.count,n.start+n.count);for(let r=i,n=s-1;r<n;r+=h){let i=r5(this,t,rK,o,r,r+1,r);i&&e.push(i)}if(this.isLineLoop){let r=r5(this,t,rK,o,s-1,i,s-1);r&&e.push(r)}}}updateMorphTargets(){let t=this.geometry.morphAttributes,e=Object.keys(t);if(e.length>0){let i=t[e[0]];if(void 0!==i){this.morphTargetInfluences=[],this.morphTargetDictionary={};for(let t=0,e=i.length;t<e;t++){let e=i[t].name||String(t);this.morphTargetInfluences.push(0),this.morphTargetDictionary[e]=t}}}}}function r5(t,e,i,s,r,n,a){let o=t.geometry.attributes.position;if(rG.fromBufferAttribute(o,r),r$.fromBufferAttribute(o,n),i.distanceSqToSegment(rG,r$,r1,r2)>s)return;r1.applyMatrix4(t.matrixWorld);let h=e.ray.origin.distanceTo(r1);if(!(h<e.near)&&!(h>e.far))return{distance:h,point:r2.clone().applyMatrix4(t.matrixWorld),index:a,face:null,faceIndex:null,barycoord:null,object:t}}let r4=new eZ,r6=new eZ;class r8 extends r3{constructor(t,e){super(t,e),this.isLineSegments=!0,this.type="LineSegments"}computeLineDistances(){let t=this.geometry;if(null===t.index){let e=t.attributes.position,i=[];for(let t=0,s=e.count;t<s;t+=2)r4.fromBufferAttribute(e,t),r6.fromBufferAttribute(e,t+1),i[t]=0===t?0:i[t-1],i[t+1]=i[t]+r4.distanceTo(r6);t.setAttribute("lineDistance",new sN(i,1))}else console.warn("THREE.LineSegments.computeLineDistances(): Computation only possible with non-indexed BufferGeometry.");return this}}class r9 extends r3{constructor(t,e){super(t,e),this.isLineLoop=!0,this.type="LineLoop"}}class r7 extends sT{constructor(t){super(),this.isPointsMaterial=!0,this.type="PointsMaterial",this.color=new s_(0xffffff),this.map=null,this.alphaMap=null,this.size=1,this.sizeAttenuation=!0,this.fog=!0,this.setValues(t)}copy(t){return super.copy(t),this.color.copy(t.color),this.map=t.map,this.alphaMap=t.alphaMap,this.size=t.size,this.sizeAttenuation=t.sizeAttenuation,this.fog=t.fog,this}}let nt=new iW,ne=new iD,ni=new iP,ns=new eZ;class nr extends sn{constructor(t=new sU,e=new r7){super(),this.isPoints=!0,this.type="Points",this.geometry=t,this.material=e,this.morphTargetDictionary=void 0,this.morphTargetInfluences=void 0,this.updateMorphTargets()}copy(t,e){return super.copy(t,e),this.material=Array.isArray(t.material)?t.material.slice():t.material,this.geometry=t.geometry,this}raycast(t,e){let i=this.geometry,s=this.matrixWorld,r=t.params.Points.threshold,n=i.drawRange;if(null===i.boundingSphere&&i.computeBoundingSphere(),ni.copy(i.boundingSphere),ni.applyMatrix4(s),ni.radius+=r,!1===t.ray.intersectsSphere(ni))return;nt.copy(s).invert(),ne.copy(t.ray).applyMatrix4(nt);let a=r/((this.scale.x+this.scale.y+this.scale.z)/3),o=a*a,h=i.index,l=i.attributes.position;if(null!==h){let i=Math.max(0,n.start),r=Math.min(h.count,n.start+n.count);for(let n=i;n<r;n++){let i=h.getX(n);ns.fromBufferAttribute(l,i),nn(ns,i,o,s,t,e,this)}}else{let i=Math.max(0,n.start),r=Math.min(l.count,n.start+n.count);for(let n=i;n<r;n++)ns.fromBufferAttribute(l,n),nn(ns,n,o,s,t,e,this)}}updateMorphTargets(){let t=this.geometry.morphAttributes,e=Object.keys(t);if(e.length>0){let i=t[e[0]];if(void 0!==i){this.morphTargetInfluences=[],this.morphTargetDictionary={};for(let t=0,e=i.length;t<e;t++){let e=i[t].name||String(t);this.morphTargetInfluences.push(0),this.morphTargetDictionary[e]=t}}}}}function nn(t,e,i,s,r,n,a){let o=ne.distanceSqToPoint(t);if(o<i){let i=new eZ;ne.closestPointToPoint(t,i),i.applyMatrix4(s);let h=r.ray.origin.distanceTo(i);if(h<r.near||h>r.far)return;n.push({distance:h,distanceToRay:Math.sqrt(o),point:i,index:e,face:null,faceIndex:null,barycoord:null,object:a})}}class na extends ih{constructor(t,e,i=tT,s,r,n,a=tf,o=tf,h,l=tV,u=1){if(l!==tV&&l!==tL)throw Error("DepthTexture format must be either THREE.DepthFormat or THREE.DepthStencilFormat");super({width:t,height:e,depth:u},s,r,n,a,o,l,i,h),this.isDepthTexture=!0,this.flipY=!1,this.generateMipmaps=!1,this.compareFunction=null}copy(t){return super.copy(t),this.source=new is(Object.assign({},t.image)),this.compareFunction=t.compareFunction,this}toJSON(t){let e=super.toJSON(t);return null!==this.compareFunction&&(e.compareFunction=this.compareFunction),e}}class no extends sU{constructor(t=1,e=1,i=4,s=8,r=1){super(),this.type="CapsuleGeometry",this.parameters={radius:t,height:e,capSegments:i,radialSegments:s,heightSegments:r},e=Math.max(0,e),i=Math.max(1,Math.floor(i)),s=Math.max(3,Math.floor(s));let n=[],a=[],o=[],h=[],l=e/2,u=Math.PI/2*t,c=e,p=2*u+c,d=2*i+(r=Math.max(1,Math.floor(r))),m=s+1,y=new eZ,f=new eZ;for(let g=0;g<=d;g++){let x=0,b=0,M=0,w=0;if(g<=i){let e=g/i,s=e*Math.PI/2;b=-l-t*Math.cos(s),M=t*Math.sin(s),w=-t*Math.cos(s),x=e*u}else if(g<=i+r){let s=(g-i)/r;b=-l+s*e,M=t,w=0,x=u+s*c}else{let e=(g-i-r)/i,s=e*Math.PI/2;b=l+t*Math.sin(s),M=t*Math.cos(s),w=t*Math.sin(s),x=u+c+e*u}let v=Math.max(0,Math.min(1,x/p)),S=0;0===g?S=.5/s:g===d&&(S=-.5/s);for(let t=0;t<=s;t++){let e=t/s,i=e*Math.PI*2,r=Math.sin(i),n=Math.cos(i);f.x=-M*n,f.y=b,f.z=M*r,a.push(f.x,f.y,f.z),y.set(-M*n,w,M*r),y.normalize(),o.push(y.x,y.y,y.z),h.push(e+S,v)}if(g>0){let t=(g-1)*m;for(let e=0;e<s;e++){let i=t+e,s=t+e+1,r=g*m+e,a=g*m+e+1;n.push(i,s,r),n.push(s,a,r)}}}this.setIndex(n),this.setAttribute("position",new sN(a,3)),this.setAttribute("normal",new sN(o,3)),this.setAttribute("uv",new sN(h,2))}copy(t){return super.copy(t),this.parameters=Object.assign({},t.parameters),this}static fromJSON(t){return new no(t.radius,t.height,t.capSegments,t.radialSegments,t.heightSegments)}}class nh extends sU{constructor(t=1,e=32,i=0,s=2*Math.PI){super(),this.type="CircleGeometry",this.parameters={radius:t,segments:e,thetaStart:i,thetaLength:s},e=Math.max(3,e);let r=[],n=[],a=[],o=[],h=new eZ,l=new eX;n.push(0,0,0),a.push(0,0,1),o.push(.5,.5);for(let r=0,u=3;r<=e;r++,u+=3){let c=i+r/e*s;h.x=t*Math.cos(c),h.y=t*Math.sin(c),n.push(h.x,h.y,h.z),a.push(0,0,1),l.x=(n[u]/t+1)/2,l.y=(n[u+1]/t+1)/2,o.push(l.x,l.y)}for(let t=1;t<=e;t++)r.push(t,t+1,0);this.setIndex(r),this.setAttribute("position",new sN(n,3)),this.setAttribute("normal",new sN(a,3)),this.setAttribute("uv",new sN(o,2))}copy(t){return super.copy(t),this.parameters=Object.assign({},t.parameters),this}static fromJSON(t){return new nh(t.radius,t.segments,t.thetaStart,t.thetaLength)}}class nl extends sU{constructor(t=1,e=1,i=1,s=32,r=1,n=!1,a=0,o=2*Math.PI){super(),this.type="CylinderGeometry",this.parameters={radiusTop:t,radiusBottom:e,height:i,radialSegments:s,heightSegments:r,openEnded:n,thetaStart:a,thetaLength:o};let h=this;s=Math.floor(s),r=Math.floor(r);let l=[],u=[],c=[],p=[],d=0,m=[],y=i/2,f=0;function g(i){let r=d,n=new eX,m=new eZ,g=0,x=!0===i?t:e,b=!0===i?1:-1;for(let t=1;t<=s;t++)u.push(0,y*b,0),c.push(0,b,0),p.push(.5,.5),d++;let M=d;for(let t=0;t<=s;t++){let e=t/s*o+a,i=Math.cos(e),r=Math.sin(e);m.x=x*r,m.y=y*b,m.z=x*i,u.push(m.x,m.y,m.z),c.push(0,b,0),n.x=.5*i+.5,n.y=.5*r*b+.5,p.push(n.x,n.y),d++}for(let t=0;t<s;t++){let e=r+t,s=M+t;!0===i?l.push(s,s+1,e):l.push(s+1,s,e),g+=3}h.addGroup(f,g,!0===i?1:2),f+=g}(function(){let n=new eZ,g=new eZ,x=0,b=(e-t)/i;for(let h=0;h<=r;h++){let l=[],f=h/r,x=f*(e-t)+t;for(let t=0;t<=s;t++){let e=t/s,r=e*o+a,h=Math.sin(r),m=Math.cos(r);g.x=x*h,g.y=-f*i+y,g.z=x*m,u.push(g.x,g.y,g.z),n.set(h,b,m).normalize(),c.push(n.x,n.y,n.z),p.push(e,1-f),l.push(d++)}m.push(l)}for(let i=0;i<s;i++)for(let s=0;s<r;s++){let n=m[s][i],a=m[s+1][i],o=m[s+1][i+1],h=m[s][i+1];(t>0||0!==s)&&(l.push(n,a,h),x+=3),(e>0||s!==r-1)&&(l.push(a,o,h),x+=3)}h.addGroup(f,x,0),f+=x})(),!1===n&&(t>0&&g(!0),e>0&&g(!1)),this.setIndex(l),this.setAttribute("position",new sN(u,3)),this.setAttribute("normal",new sN(c,3)),this.setAttribute("uv",new sN(p,2))}copy(t){return super.copy(t),this.parameters=Object.assign({},t.parameters),this}static fromJSON(t){return new nl(t.radiusTop,t.radiusBottom,t.height,t.radialSegments,t.heightSegments,t.openEnded,t.thetaStart,t.thetaLength)}}class nu extends nl{constructor(t=1,e=1,i=32,s=1,r=!1,n=0,a=2*Math.PI){super(0,t,e,i,s,r,n,a),this.type="ConeGeometry",this.parameters={radius:t,height:e,radialSegments:i,heightSegments:s,openEnded:r,thetaStart:n,thetaLength:a}}static fromJSON(t){return new nu(t.radius,t.height,t.radialSegments,t.heightSegments,t.openEnded,t.thetaStart,t.thetaLength)}}class nc extends sU{constructor(t=[],e=[],i=1,s=0){super(),this.type="PolyhedronGeometry",this.parameters={vertices:t,indices:e,radius:i,detail:s};let r=[],n=[];function a(t){r.push(t.x,t.y,t.z)}function o(e,i){let s=3*e;i.x=t[s+0],i.y=t[s+1],i.z=t[s+2]}function h(t,e,i,s){s<0&&1===t.x&&(n[e]=t.x-1),0===i.x&&0===i.z&&(n[e]=s/2/Math.PI+.5)}function l(t){return Math.atan2(t.z,-t.x)}(function(t){let i=new eZ,s=new eZ,r=new eZ;for(let n=0;n<e.length;n+=3)o(e[n+0],i),o(e[n+1],s),o(e[n+2],r),function(t,e,i,s){let r=s+1,n=[];for(let s=0;s<=r;s++){n[s]=[];let a=t.clone().lerp(i,s/r),o=e.clone().lerp(i,s/r),h=r-s;for(let t=0;t<=h;t++)0===t&&s===r?n[s][t]=a:n[s][t]=a.clone().lerp(o,t/h)}for(let t=0;t<r;t++)for(let e=0;e<2*(r-t)-1;e++){let i=Math.floor(e/2);e%2==0?(a(n[t][i+1]),a(n[t+1][i]),a(n[t][i])):(a(n[t][i+1]),a(n[t+1][i+1]),a(n[t+1][i]))}}(i,s,r,t)})(s),function(t){let e=new eZ;for(let i=0;i<r.length;i+=3)e.x=r[i+0],e.y=r[i+1],e.z=r[i+2],e.normalize().multiplyScalar(t),r[i+0]=e.x,r[i+1]=e.y,r[i+2]=e.z}(i),function(){let t=new eZ;for(let i=0;i<r.length;i+=3){var e;t.x=r[i+0],t.y=r[i+1],t.z=r[i+2];let s=l(t)/2/Math.PI+.5,a=Math.atan2(-(e=t).y,Math.sqrt(e.x*e.x+e.z*e.z))/Math.PI+.5;n.push(s,1-a)}(function(){let t=new eZ,e=new eZ,i=new eZ,s=new eZ,a=new eX,o=new eX,u=new eX;for(let c=0,p=0;c<r.length;c+=9,p+=6){t.set(r[c+0],r[c+1],r[c+2]),e.set(r[c+3],r[c+4],r[c+5]),i.set(r[c+6],r[c+7],r[c+8]),a.set(n[p+0],n[p+1]),o.set(n[p+2],n[p+3]),u.set(n[p+4],n[p+5]),s.copy(t).add(e).add(i).divideScalar(3);let d=l(s);h(a,p+0,t,d),h(o,p+2,e,d),h(u,p+4,i,d)}})(),function(){for(let t=0;t<n.length;t+=6){let e=n[t+0],i=n[t+2],s=n[t+4],r=Math.max(e,i,s),a=Math.min(e,i,s);r>.9&&a<.1&&(e<.2&&(n[t+0]+=1),i<.2&&(n[t+2]+=1),s<.2&&(n[t+4]+=1))}}()}(),this.setAttribute("position",new sN(r,3)),this.setAttribute("normal",new sN(r.slice(),3)),this.setAttribute("uv",new sN(n,2)),0===s?this.computeVertexNormals():this.normalizeNormals()}copy(t){return super.copy(t),this.parameters=Object.assign({},t.parameters),this}static fromJSON(t){return new nc(t.vertices,t.indices,t.radius,t.details)}}class np extends nc{constructor(t=1,e=0){let i=(1+Math.sqrt(5))/2,s=1/i;super([-1,-1,-1,-1,-1,1,-1,1,-1,-1,1,1,1,-1,-1,1,-1,1,1,1,-1,1,1,1,0,-s,-i,0,-s,i,0,s,-i,0,s,i,-s,-i,0,-s,i,0,s,-i,0,s,i,0,-i,0,-s,i,0,-s,-i,0,s,i,0,s],[3,11,7,3,7,15,3,15,13,7,19,17,7,17,6,7,6,15,17,4,8,17,8,10,17,10,6,8,0,16,8,16,2,8,2,10,0,12,1,0,1,18,0,18,16,6,10,2,6,2,13,6,13,15,2,16,18,2,18,3,2,3,13,18,1,9,18,9,11,18,11,3,4,14,12,4,12,0,4,0,8,11,9,5,11,5,19,11,19,7,19,5,14,19,14,4,19,4,17,1,12,14,1,14,5,1,5,9],t,e),this.type="DodecahedronGeometry",this.parameters={radius:t,detail:e}}static fromJSON(t){return new np(t.radius,t.detail)}}let nd=new eZ,nm=new eZ,ny=new eZ,nf=new sb;class ng{constructor(){this.type="Curve",this.arcLengthDivisions=200,this.needsUpdate=!1,this.cacheArcLengths=null}getPoint(){console.warn("THREE.Curve: .getPoint() not implemented.")}getPointAt(t,e){let i=this.getUtoTmapping(t);return this.getPoint(i,e)}getPoints(t=5){let e=[];for(let i=0;i<=t;i++)e.push(this.getPoint(i/t));return e}getSpacedPoints(t=5){let e=[];for(let i=0;i<=t;i++)e.push(this.getPointAt(i/t));return e}getLength(){let t=this.getLengths();return t[t.length-1]}getLengths(t=this.arcLengthDivisions){if(this.cacheArcLengths&&this.cacheArcLengths.length===t+1&&!this.needsUpdate)return this.cacheArcLengths;this.needsUpdate=!1;let e=[],i,s=this.getPoint(0),r=0;e.push(0);for(let n=1;n<=t;n++)e.push(r+=(i=this.getPoint(n/t)).distanceTo(s)),s=i;return this.cacheArcLengths=e,e}updateArcLengths(){this.needsUpdate=!0,this.getLengths()}getUtoTmapping(t,e=null){let i,s=this.getLengths(),r=0,n=s.length;i=e||t*s[n-1];let a=0,o=n-1,h;for(;a<=o;)if((h=s[r=Math.floor(a+(o-a)/2)]-i)<0)a=r+1;else if(h>0)o=r-1;else{o=r;break}if(s[r=o]===i)return r/(n-1);let l=s[r],u=s[r+1];return(r+(i-l)/(u-l))/(n-1)}getTangent(t,e){let i=t-1e-4,s=t+1e-4;i<0&&(i=0),s>1&&(s=1);let r=this.getPoint(i),n=this.getPoint(s),a=e||(r.isVector2?new eX:new eZ);return a.copy(n).sub(r).normalize(),a}getTangentAt(t,e){let i=this.getUtoTmapping(t);return this.getTangent(i,e)}computeFrenetFrames(t,e=!1){let i=new eZ,s=[],r=[],n=[],a=new eZ,o=new iW;for(let e=0;e<=t;e++){let i=e/t;s[e]=this.getTangentAt(i,new eZ)}r[0]=new eZ,n[0]=new eZ;let h=Number.MAX_VALUE,l=Math.abs(s[0].x),u=Math.abs(s[0].y),c=Math.abs(s[0].z);l<=h&&(h=l,i.set(1,0,0)),u<=h&&(h=u,i.set(0,1,0)),c<=h&&i.set(0,0,1),a.crossVectors(s[0],i).normalize(),r[0].crossVectors(s[0],a),n[0].crossVectors(s[0],r[0]);for(let e=1;e<=t;e++){if(r[e]=r[e-1].clone(),n[e]=n[e-1].clone(),a.crossVectors(s[e-1],s[e]),a.length()>Number.EPSILON){a.normalize();let t=Math.acos(eD(s[e-1].dot(s[e]),-1,1));r[e].applyMatrix4(o.makeRotationAxis(a,t))}n[e].crossVectors(s[e],r[e])}if(!0===e){let e=Math.acos(eD(r[0].dot(r[t]),-1,1));e/=t,s[0].dot(a.crossVectors(r[0],r[t]))>0&&(e=-e);for(let i=1;i<=t;i++)r[i].applyMatrix4(o.makeRotationAxis(s[i],e*i)),n[i].crossVectors(s[i],r[i])}return{tangents:s,normals:r,binormals:n}}clone(){return new this.constructor().copy(this)}copy(t){return this.arcLengthDivisions=t.arcLengthDivisions,this}toJSON(){let t={metadata:{version:4.7,type:"Curve",generator:"Curve.toJSON"}};return t.arcLengthDivisions=this.arcLengthDivisions,t.type=this.type,t}fromJSON(t){return this.arcLengthDivisions=t.arcLengthDivisions,this}}class nx extends ng{constructor(t=0,e=0,i=1,s=1,r=0,n=2*Math.PI,a=!1,o=0){super(),this.isEllipseCurve=!0,this.type="EllipseCurve",this.aX=t,this.aY=e,this.xRadius=i,this.yRadius=s,this.aStartAngle=r,this.aEndAngle=n,this.aClockwise=a,this.aRotation=o}getPoint(t,e=new eX){let i=2*Math.PI,s=this.aEndAngle-this.aStartAngle,r=Math.abs(s)<Number.EPSILON;for(;s<0;)s+=i;for(;s>i;)s-=i;s<Number.EPSILON&&(s=r?0:i),!0!==this.aClockwise||r||(s===i?s=-i:s-=i);let n=this.aStartAngle+t*s,a=this.aX+this.xRadius*Math.cos(n),o=this.aY+this.yRadius*Math.sin(n);if(0!==this.aRotation){let t=Math.cos(this.aRotation),e=Math.sin(this.aRotation),i=a-this.aX,s=o-this.aY;a=i*t-s*e+this.aX,o=i*e+s*t+this.aY}return e.set(a,o)}copy(t){return super.copy(t),this.aX=t.aX,this.aY=t.aY,this.xRadius=t.xRadius,this.yRadius=t.yRadius,this.aStartAngle=t.aStartAngle,this.aEndAngle=t.aEndAngle,this.aClockwise=t.aClockwise,this.aRotation=t.aRotation,this}toJSON(){let t=super.toJSON();return t.aX=this.aX,t.aY=this.aY,t.xRadius=this.xRadius,t.yRadius=this.yRadius,t.aStartAngle=this.aStartAngle,t.aEndAngle=this.aEndAngle,t.aClockwise=this.aClockwise,t.aRotation=this.aRotation,t}fromJSON(t){return super.fromJSON(t),this.aX=t.aX,this.aY=t.aY,this.xRadius=t.xRadius,this.yRadius=t.yRadius,this.aStartAngle=t.aStartAngle,this.aEndAngle=t.aEndAngle,this.aClockwise=t.aClockwise,this.aRotation=t.aRotation,this}}class nb extends nx{constructor(t,e,i,s,r,n){super(t,e,i,i,s,r,n),this.isArcCurve=!0,this.type="ArcCurve"}}function nM(){let t=0,e=0,i=0,s=0;function r(r,n,a,o){t=r,e=a,i=-3*r+3*n-2*a-o,s=2*r-2*n+a+o}return{initCatmullRom:function(t,e,i,s,n){r(e,i,n*(i-t),n*(s-e))},initNonuniformCatmullRom:function(t,e,i,s,n,a,o){let h=(e-t)/n-(i-t)/(n+a)+(i-e)/a,l=(i-e)/a-(s-e)/(a+o)+(s-i)/o;r(e,i,h*=a,l*=a)},calc:function(r){let n=r*r;return t+e*r+i*n+n*r*s}}}let nw=new eZ,nv=new nM,nS=new nM,n_=new nM;class nz extends ng{constructor(t=[],e=!1,i="centripetal",s=.5){super(),this.isCatmullRomCurve3=!0,this.type="CatmullRomCurve3",this.points=t,this.closed=e,this.curveType=i,this.tension=s}getPoint(t,e=new eZ){let i,s,r=this.points,n=r.length,a=(n-!this.closed)*t,o=Math.floor(a),h=a-o;this.closed?o+=o>0?0:(Math.floor(Math.abs(o)/n)+1)*n:0===h&&o===n-1&&(o=n-2,h=1),this.closed||o>0?i=r[(o-1)%n]:(nw.subVectors(r[0],r[1]).add(r[0]),i=nw);let l=r[o%n],u=r[(o+1)%n];if(this.closed||o+2<n?s=r[(o+2)%n]:(nw.subVectors(r[n-1],r[n-2]).add(r[n-1]),s=nw),"centripetal"===this.curveType||"chordal"===this.curveType){let t="chordal"===this.curveType?.5:.25,e=Math.pow(i.distanceToSquared(l),t),r=Math.pow(l.distanceToSquared(u),t),n=Math.pow(u.distanceToSquared(s),t);r<1e-4&&(r=1),e<1e-4&&(e=r),n<1e-4&&(n=r),nv.initNonuniformCatmullRom(i.x,l.x,u.x,s.x,e,r,n),nS.initNonuniformCatmullRom(i.y,l.y,u.y,s.y,e,r,n),n_.initNonuniformCatmullRom(i.z,l.z,u.z,s.z,e,r,n)}else"catmullrom"===this.curveType&&(nv.initCatmullRom(i.x,l.x,u.x,s.x,this.tension),nS.initCatmullRom(i.y,l.y,u.y,s.y,this.tension),n_.initCatmullRom(i.z,l.z,u.z,s.z,this.tension));return e.set(nv.calc(h),nS.calc(h),n_.calc(h)),e}copy(t){super.copy(t),this.points=[];for(let e=0,i=t.points.length;e<i;e++){let i=t.points[e];this.points.push(i.clone())}return this.closed=t.closed,this.curveType=t.curveType,this.tension=t.tension,this}toJSON(){let t=super.toJSON();t.points=[];for(let e=0,i=this.points.length;e<i;e++){let i=this.points[e];t.points.push(i.toArray())}return t.closed=this.closed,t.curveType=this.curveType,t.tension=this.tension,t}fromJSON(t){super.fromJSON(t),this.points=[];for(let e=0,i=t.points.length;e<i;e++){let i=t.points[e];this.points.push(new eZ().fromArray(i))}return this.closed=t.closed,this.curveType=t.curveType,this.tension=t.tension,this}}function nA(t,e,i,s,r){let n=(s-e)*.5,a=(r-i)*.5,o=t*t;return t*o*(2*i-2*s+n+a)+(-3*i+3*s-2*n-a)*o+n*t+i}function nT(t,e,i,s){return function(t,e){let i=1-t;return i*i*e}(t,e)+2*(1-t)*t*i+t*t*s}function nC(t,e,i,s,r){return function(t,e){let i=1-t;return i*i*i*e}(t,e)+function(t,e){let i=1-t;return 3*i*i*t*e}(t,i)+3*(1-t)*t*t*s+t*t*t*r}class nk extends ng{constructor(t=new eX,e=new eX,i=new eX,s=new eX){super(),this.isCubicBezierCurve=!0,this.type="CubicBezierCurve",this.v0=t,this.v1=e,this.v2=i,this.v3=s}getPoint(t,e=new eX){let i=this.v0,s=this.v1,r=this.v2,n=this.v3;return e.set(nC(t,i.x,s.x,r.x,n.x),nC(t,i.y,s.y,r.y,n.y)),e}copy(t){return super.copy(t),this.v0.copy(t.v0),this.v1.copy(t.v1),this.v2.copy(t.v2),this.v3.copy(t.v3),this}toJSON(){let t=super.toJSON();return t.v0=this.v0.toArray(),t.v1=this.v1.toArray(),t.v2=this.v2.toArray(),t.v3=this.v3.toArray(),t}fromJSON(t){return super.fromJSON(t),this.v0.fromArray(t.v0),this.v1.fromArray(t.v1),this.v2.fromArray(t.v2),this.v3.fromArray(t.v3),this}}class nE extends ng{constructor(t=new eZ,e=new eZ,i=new eZ,s=new eZ){super(),this.isCubicBezierCurve3=!0,this.type="CubicBezierCurve3",this.v0=t,this.v1=e,this.v2=i,this.v3=s}getPoint(t,e=new eZ){let i=this.v0,s=this.v1,r=this.v2,n=this.v3;return e.set(nC(t,i.x,s.x,r.x,n.x),nC(t,i.y,s.y,r.y,n.y),nC(t,i.z,s.z,r.z,n.z)),e}copy(t){return super.copy(t),this.v0.copy(t.v0),this.v1.copy(t.v1),this.v2.copy(t.v2),this.v3.copy(t.v3),this}toJSON(){let t=super.toJSON();return t.v0=this.v0.toArray(),t.v1=this.v1.toArray(),t.v2=this.v2.toArray(),t.v3=this.v3.toArray(),t}fromJSON(t){return super.fromJSON(t),this.v0.fromArray(t.v0),this.v1.fromArray(t.v1),this.v2.fromArray(t.v2),this.v3.fromArray(t.v3),this}}class nO extends ng{constructor(t=new eX,e=new eX){super(),this.isLineCurve=!0,this.type="LineCurve",this.v1=t,this.v2=e}getPoint(t,e=new eX){return 1===t?e.copy(this.v2):(e.copy(this.v2).sub(this.v1),e.multiplyScalar(t).add(this.v1)),e}getPointAt(t,e){return this.getPoint(t,e)}getTangent(t,e=new eX){return e.subVectors(this.v2,this.v1).normalize()}getTangentAt(t,e){return this.getTangent(t,e)}copy(t){return super.copy(t),this.v1.copy(t.v1),this.v2.copy(t.v2),this}toJSON(){let t=super.toJSON();return t.v1=this.v1.toArray(),t.v2=this.v2.toArray(),t}fromJSON(t){return super.fromJSON(t),this.v1.fromArray(t.v1),this.v2.fromArray(t.v2),this}}class nB extends ng{constructor(t=new eZ,e=new eZ){super(),this.isLineCurve3=!0,this.type="LineCurve3",this.v1=t,this.v2=e}getPoint(t,e=new eZ){return 1===t?e.copy(this.v2):(e.copy(this.v2).sub(this.v1),e.multiplyScalar(t).add(this.v1)),e}getPointAt(t,e){return this.getPoint(t,e)}getTangent(t,e=new eZ){return e.subVectors(this.v2,this.v1).normalize()}getTangentAt(t,e){return this.getTangent(t,e)}copy(t){return super.copy(t),this.v1.copy(t.v1),this.v2.copy(t.v2),this}toJSON(){let t=super.toJSON();return t.v1=this.v1.toArray(),t.v2=this.v2.toArray(),t}fromJSON(t){return super.fromJSON(t),this.v1.fromArray(t.v1),this.v2.fromArray(t.v2),this}}class nP extends ng{constructor(t=new eX,e=new eX,i=new eX){super(),this.isQuadraticBezierCurve=!0,this.type="QuadraticBezierCurve",this.v0=t,this.v1=e,this.v2=i}getPoint(t,e=new eX){let i=this.v0,s=this.v1,r=this.v2;return e.set(nT(t,i.x,s.x,r.x),nT(t,i.y,s.y,r.y)),e}copy(t){return super.copy(t),this.v0.copy(t.v0),this.v1.copy(t.v1),this.v2.copy(t.v2),this}toJSON(){let t=super.toJSON();return t.v0=this.v0.toArray(),t.v1=this.v1.toArray(),t.v2=this.v2.toArray(),t}fromJSON(t){return super.fromJSON(t),this.v0.fromArray(t.v0),this.v1.fromArray(t.v1),this.v2.fromArray(t.v2),this}}class nI extends ng{constructor(t=new eZ,e=new eZ,i=new eZ){super(),this.isQuadraticBezierCurve3=!0,this.type="QuadraticBezierCurve3",this.v0=t,this.v1=e,this.v2=i}getPoint(t,e=new eZ){let i=this.v0,s=this.v1,r=this.v2;return e.set(nT(t,i.x,s.x,r.x),nT(t,i.y,s.y,r.y),nT(t,i.z,s.z,r.z)),e}copy(t){return super.copy(t),this.v0.copy(t.v0),this.v1.copy(t.v1),this.v2.copy(t.v2),this}toJSON(){let t=super.toJSON();return t.v0=this.v0.toArray(),t.v1=this.v1.toArray(),t.v2=this.v2.toArray(),t}fromJSON(t){return super.fromJSON(t),this.v0.fromArray(t.v0),this.v1.fromArray(t.v1),this.v2.fromArray(t.v2),this}}class nN extends ng{constructor(t=[]){super(),this.isSplineCurve=!0,this.type="SplineCurve",this.points=t}getPoint(t,e=new eX){let i=this.points,s=(i.length-1)*t,r=Math.floor(s),n=s-r,a=i[0===r?r:r-1],o=i[r],h=i[r>i.length-2?i.length-1:r+1],l=i[r>i.length-3?i.length-1:r+2];return e.set(nA(n,a.x,o.x,h.x,l.x),nA(n,a.y,o.y,h.y,l.y)),e}copy(t){super.copy(t),this.points=[];for(let e=0,i=t.points.length;e<i;e++){let i=t.points[e];this.points.push(i.clone())}return this}toJSON(){let t=super.toJSON();t.points=[];for(let e=0,i=this.points.length;e<i;e++){let i=this.points[e];t.points.push(i.toArray())}return t}fromJSON(t){super.fromJSON(t),this.points=[];for(let e=0,i=t.points.length;e<i;e++){let i=t.points[e];this.points.push(new eX().fromArray(i))}return this}}var nR=Object.freeze({__proto__:null,ArcCurve:nb,CatmullRomCurve3:nz,CubicBezierCurve:nk,CubicBezierCurve3:nE,EllipseCurve:nx,LineCurve:nO,LineCurve3:nB,QuadraticBezierCurve:nP,QuadraticBezierCurve3:nI,SplineCurve:nN});class nV extends ng{constructor(){super(),this.type="CurvePath",this.curves=[],this.autoClose=!1}add(t){this.curves.push(t)}closePath(){let t=this.curves[0].getPoint(0),e=this.curves[this.curves.length-1].getPoint(1);if(!t.equals(e)){let i=!0===t.isVector2?"LineCurve":"LineCurve3";this.curves.push(new nR[i](e,t))}return this}getPoint(t,e){let i=t*this.getLength(),s=this.getCurveLengths(),r=0;for(;r<s.length;){if(s[r]>=i){let t=s[r]-i,n=this.curves[r],a=n.getLength(),o=0===a?0:1-t/a;return n.getPointAt(o,e)}r++}return null}getLength(){let t=this.getCurveLengths();return t[t.length-1]}updateArcLengths(){this.needsUpdate=!0,this.cacheLengths=null,this.getCurveLengths()}getCurveLengths(){if(this.cacheLengths&&this.cacheLengths.length===this.curves.length)return this.cacheLengths;let t=[],e=0;for(let i=0,s=this.curves.length;i<s;i++)t.push(e+=this.curves[i].getLength());return this.cacheLengths=t,t}getSpacedPoints(t=40){let e=[];for(let i=0;i<=t;i++)e.push(this.getPoint(i/t));return this.autoClose&&e.push(e[0]),e}getPoints(t=12){let e,i=[];for(let s=0,r=this.curves;s<r.length;s++){let n=r[s],a=n.isEllipseCurve?2*t:n.isLineCurve||n.isLineCurve3?1:n.isSplineCurve?t*n.points.length:t,o=n.getPoints(a);for(let t=0;t<o.length;t++){let s=o[t];e&&e.equals(s)||(i.push(s),e=s)}}return this.autoClose&&i.length>1&&!i[i.length-1].equals(i[0])&&i.push(i[0]),i}copy(t){super.copy(t),this.curves=[];for(let e=0,i=t.curves.length;e<i;e++){let i=t.curves[e];this.curves.push(i.clone())}return this.autoClose=t.autoClose,this}toJSON(){let t=super.toJSON();t.autoClose=this.autoClose,t.curves=[];for(let e=0,i=this.curves.length;e<i;e++){let i=this.curves[e];t.curves.push(i.toJSON())}return t}fromJSON(t){super.fromJSON(t),this.autoClose=t.autoClose,this.curves=[];for(let e=0,i=t.curves.length;e<i;e++){let i=t.curves[e];this.curves.push(new nR[i.type]().fromJSON(i))}return this}}class nL extends nV{constructor(t){super(),this.type="Path",this.currentPoint=new eX,t&&this.setFromPoints(t)}setFromPoints(t){this.moveTo(t[0].x,t[0].y);for(let e=1,i=t.length;e<i;e++)this.lineTo(t[e].x,t[e].y);return this}moveTo(t,e){return this.currentPoint.set(t,e),this}lineTo(t,e){let i=new nO(this.currentPoint.clone(),new eX(t,e));return this.curves.push(i),this.currentPoint.set(t,e),this}quadraticCurveTo(t,e,i,s){let r=new nP(this.currentPoint.clone(),new eX(t,e),new eX(i,s));return this.curves.push(r),this.currentPoint.set(i,s),this}bezierCurveTo(t,e,i,s,r,n){let a=new nk(this.currentPoint.clone(),new eX(t,e),new eX(i,s),new eX(r,n));return this.curves.push(a),this.currentPoint.set(r,n),this}splineThru(t){let e=new nN([this.currentPoint.clone()].concat(t));return this.curves.push(e),this.currentPoint.copy(t[t.length-1]),this}arc(t,e,i,s,r,n){let a=this.currentPoint.x,o=this.currentPoint.y;return this.absarc(t+a,e+o,i,s,r,n),this}absarc(t,e,i,s,r,n){return this.absellipse(t,e,i,i,s,r,n),this}ellipse(t,e,i,s,r,n,a,o){let h=this.currentPoint.x,l=this.currentPoint.y;return this.absellipse(t+h,e+l,i,s,r,n,a,o),this}absellipse(t,e,i,s,r,n,a,o){let h=new nx(t,e,i,s,r,n,a,o);if(this.curves.length>0){let t=h.getPoint(0);t.equals(this.currentPoint)||this.lineTo(t.x,t.y)}this.curves.push(h);let l=h.getPoint(1);return this.currentPoint.copy(l),this}copy(t){return super.copy(t),this.currentPoint.copy(t.currentPoint),this}toJSON(){let t=super.toJSON();return t.currentPoint=this.currentPoint.toArray(),t}fromJSON(t){return super.fromJSON(t),this.currentPoint.fromArray(t.currentPoint),this}}class nF extends nL{constructor(t){super(t),this.uuid=ej(),this.type="Shape",this.holes=[]}getPointsHoles(t){let e=[];for(let i=0,s=this.holes.length;i<s;i++)e[i]=this.holes[i].getPoints(t);return e}extractPoints(t){return{shape:this.getPoints(t),holes:this.getPointsHoles(t)}}copy(t){super.copy(t),this.holes=[];for(let e=0,i=t.holes.length;e<i;e++){let i=t.holes[e];this.holes.push(i.clone())}return this}toJSON(){let t=super.toJSON();t.uuid=this.uuid,t.holes=[];for(let e=0,i=this.holes.length;e<i;e++){let i=this.holes[e];t.holes.push(i.toJSON())}return t}fromJSON(t){super.fromJSON(t),this.uuid=t.uuid,this.holes=[];for(let e=0,i=t.holes.length;e<i;e++){let i=t.holes[e];this.holes.push(new nL().fromJSON(i))}return this}}function nj(t,e,i,s,r){let n;if(r===function(t,e,i,s){let r=0;for(let n=e,a=i-s;n<i;n+=s)r+=(t[a]-t[n])*(t[n+1]+t[a+1]),a=n;return r}(t,e,i,s)>0)for(let r=e;r<i;r+=s)n=nK(r/s|0,t[r],t[r+1],n);else for(let r=i-s;r>=e;r-=s)n=nK(r/s|0,t[r],t[r+1],n);return n&&nX(n,n.next)&&(n0(n),n=n.next),n}function nD(t,e){if(!t)return t;e||(e=t);let i=t,s;do if(s=!1,!i.steiner&&(nX(i,i.next)||0===nH(i.prev,i,i.next))){if(n0(i),(i=e=i.prev)===i.next)break;s=!0}else i=i.next;while(s||i!==e);return e}function nW(t,e){let i=t.x-e.x;return 0===i&&0==(i=t.y-e.y)&&(i=(t.next.y-t.y)/(t.next.x-t.x)-(e.next.y-e.y)/(e.next.x-e.x)),i}function nU(t,e,i,s,r){return(t=((t=((t=((t=((t=(t-i)*r|0)|t<<8)&0xff00ff)|t<<4)&0xf0f0f0f)|t<<2)&0x33333333)|t<<1)&0x55555555)|(e=((e=((e=((e=((e=(e-s)*r|0)|e<<8)&0xff00ff)|e<<4)&0xf0f0f0f)|e<<2)&0x33333333)|e<<1)&0x55555555)<<1}function nJ(t,e,i,s,r,n,a,o){return(r-a)*(e-o)>=(t-a)*(n-o)&&(t-a)*(s-o)>=(i-a)*(e-o)&&(i-a)*(n-o)>=(r-a)*(s-o)}function nq(t,e,i,s,r,n,a,o){return(t!==a||e!==o)&&nJ(t,e,i,s,r,n,a,o)}function nH(t,e,i){return(e.y-t.y)*(i.x-e.x)-(e.x-t.x)*(i.y-e.y)}function nX(t,e){return t.x===e.x&&t.y===e.y}function nY(t,e,i,s){let r=nG(nH(t,e,i)),n=nG(nH(t,e,s)),a=nG(nH(i,s,t)),o=nG(nH(i,s,e));return!!(r!==n&&a!==o||0===r&&nZ(t,i,e)||0===n&&nZ(t,s,e)||0===a&&nZ(i,t,s)||0===o&&nZ(i,e,s))}function nZ(t,e,i){return e.x<=Math.max(t.x,i.x)&&e.x>=Math.min(t.x,i.x)&&e.y<=Math.max(t.y,i.y)&&e.y>=Math.min(t.y,i.y)}function nG(t){return t>0?1:t<0?-1:0}function n$(t,e){return 0>nH(t.prev,t,t.next)?nH(t,e,t.next)>=0&&nH(t,t.prev,e)>=0:0>nH(t,e,t.prev)||0>nH(t,t.next,e)}function nQ(t,e){let i=n1(t.i,t.x,t.y),s=n1(e.i,e.x,e.y),r=t.next,n=e.prev;return t.next=e,e.prev=t,i.next=r,r.prev=i,s.next=i,i.prev=s,n.next=s,s.prev=n,s}function nK(t,e,i,s){let r=n1(t,e,i);return s?(r.next=s.next,r.prev=s,s.next.prev=r,s.next=r):(r.prev=r,r.next=r),r}function n0(t){t.next.prev=t.prev,t.prev.next=t.next,t.prevZ&&(t.prevZ.nextZ=t.nextZ),t.nextZ&&(t.nextZ.prevZ=t.prevZ)}function n1(t,e,i){return{i:t,x:e,y:i,prev:null,next:null,z:0,prevZ:null,nextZ:null,steiner:!1}}class n2{static triangulate(t,e,i=2){return function(t,e,i=2){let s,r,n,a=e&&e.length,o=a?e[0]*i:t.length,h=nj(t,0,o,i,!0),l=[];if(!h||h.next===h.prev)return l;if(a&&(h=function(t,e,i,s){let r=[];for(let i=0,n=e.length;i<n;i++){let a=e[i]*s,o=i<n-1?e[i+1]*s:t.length,h=nj(t,a,o,s,!1);h===h.next&&(h.steiner=!0),r.push(function(t){let e=t,i=t;do(e.x<i.x||e.x===i.x&&e.y<i.y)&&(i=e),e=e.next;while(e!==t);return i}(h))}r.sort(nW);for(let t=0;t<r.length;t++)i=function(t,e){let i=function(t,e){let i,s=e,r=t.x,n=t.y,a=-1/0;if(nX(t,s))return s;do{if(nX(t,s.next))return s.next;if(n<=s.y&&n>=s.next.y&&s.next.y!==s.y){let t=s.x+(n-s.y)*(s.next.x-s.x)/(s.next.y-s.y);if(t<=r&&t>a&&(a=t,i=s.x<s.next.x?s:s.next,t===r))return i}s=s.next}while(s!==e);if(!i)return null;let o=i,h=i.x,l=i.y,u=1/0;s=i;do{if(r>=s.x&&s.x>=h&&r!==s.x&&nJ(n<l?r:a,n,h,l,n<l?a:r,n,s.x,s.y)){var c,p;let e=Math.abs(n-s.y)/(r-s.x);n$(s,t)&&(e<u||e===u&&(s.x>i.x||s.x===i.x&&(c=i,p=s,0>nH(c.prev,c,p.prev)&&0>nH(p.next,c,c.next))))&&(i=s,u=e)}s=s.next}while(s!==o);return i}(t,e);if(!i)return e;let s=nQ(i,t);return nD(s,s.next),nD(i,i.next)}(r[t],i);return i}(t,e,h,i)),t.length>80*i){s=1/0,r=1/0;let e=-1/0,a=-1/0;for(let n=i;n<o;n+=i){let i=t[n],o=t[n+1];i<s&&(s=i),o<r&&(r=o),i>e&&(e=i),o>a&&(a=o)}n=0!==(n=Math.max(e-s,a-r))?32767/n:0}return function t(e,i,s,r,n,a,o){if(!e)return;!o&&a&&function(t,e,i,s){let r=t;do 0===r.z&&(r.z=nU(r.x,r.y,e,i,s)),r.prevZ=r.prev,r.nextZ=r.next,r=r.next;while(r!==t);r.prevZ.nextZ=null,r.prevZ=null,function(t){let e,i=1;do{let s,r=t;t=null;let n=null;for(e=0;r;){e++;let a=r,o=0;for(let t=0;t<i&&(o++,a=a.nextZ);t++);let h=i;for(;o>0||h>0&&a;)0!==o&&(0===h||!a||r.z<=a.z)?(s=r,r=r.nextZ,o--):(s=a,a=a.nextZ,h--),n?n.nextZ=s:t=s,s.prevZ=n,n=s;r=a}n.nextZ=null,i*=2}while(e>1)}(r)}(e,r,n,a);let h=e;for(;e.prev!==e.next;){let l=e.prev,u=e.next;if(a?function(t,e,i,s){let r=t.prev,n=t.next;if(nH(r,t,n)>=0)return!1;let a=r.x,o=t.x,h=n.x,l=r.y,u=t.y,c=n.y,p=Math.min(a,o,h),d=Math.min(l,u,c),m=Math.max(a,o,h),y=Math.max(l,u,c),f=nU(p,d,e,i,s),g=nU(m,y,e,i,s),x=t.prevZ,b=t.nextZ;for(;x&&x.z>=f&&b&&b.z<=g;){if(x.x>=p&&x.x<=m&&x.y>=d&&x.y<=y&&x!==r&&x!==n&&nq(a,l,o,u,h,c,x.x,x.y)&&nH(x.prev,x,x.next)>=0||(x=x.prevZ,b.x>=p&&b.x<=m&&b.y>=d&&b.y<=y&&b!==r&&b!==n&&nq(a,l,o,u,h,c,b.x,b.y)&&nH(b.prev,b,b.next)>=0))return!1;b=b.nextZ}for(;x&&x.z>=f;){if(x.x>=p&&x.x<=m&&x.y>=d&&x.y<=y&&x!==r&&x!==n&&nq(a,l,o,u,h,c,x.x,x.y)&&nH(x.prev,x,x.next)>=0)return!1;x=x.prevZ}for(;b&&b.z<=g;){if(b.x>=p&&b.x<=m&&b.y>=d&&b.y<=y&&b!==r&&b!==n&&nq(a,l,o,u,h,c,b.x,b.y)&&nH(b.prev,b,b.next)>=0)return!1;b=b.nextZ}return!0}(e,r,n,a):function(t){let e=t.prev,i=t.next;if(nH(e,t,i)>=0)return!1;let s=e.x,r=t.x,n=i.x,a=e.y,o=t.y,h=i.y,l=Math.min(s,r,n),u=Math.min(a,o,h),c=Math.max(s,r,n),p=Math.max(a,o,h),d=i.next;for(;d!==e;){if(d.x>=l&&d.x<=c&&d.y>=u&&d.y<=p&&nq(s,a,r,o,n,h,d.x,d.y)&&nH(d.prev,d,d.next)>=0)return!1;d=d.next}return!0}(e)){i.push(l.i,e.i,u.i),n0(e),e=u.next,h=u.next;continue}if((e=u)===h){o?1===o?t(e=function(t,e){let i=t;do{let s=i.prev,r=i.next.next;!nX(s,r)&&nY(s,i,i.next,r)&&n$(s,r)&&n$(r,s)&&(e.push(s.i,i.i,r.i),n0(i),n0(i.next),i=t=r),i=i.next}while(i!==t);return nD(i)}(nD(e),i),i,s,r,n,a,2):2===o&&function(e,i,s,r,n,a){let o=e;do{let e=o.next.next;for(;e!==o.prev;){var h,l;if(o.i!==e.i&&(h=o,l=e,h.next.i!==l.i&&h.prev.i!==l.i&&!function(t,e){let i=t;do{if(i.i!==t.i&&i.next.i!==t.i&&i.i!==e.i&&i.next.i!==e.i&&nY(i,i.next,t,e))return!0;i=i.next}while(i!==t);return!1}(h,l)&&(n$(h,l)&&n$(l,h)&&function(t,e){let i=t,s=!1,r=(t.x+e.x)/2,n=(t.y+e.y)/2;do i.y>n!=i.next.y>n&&i.next.y!==i.y&&r<(i.next.x-i.x)*(n-i.y)/(i.next.y-i.y)+i.x&&(s=!s),i=i.next;while(i!==t);return s}(h,l)&&(nH(h.prev,h,l.prev)||nH(h,l.prev,l))||nX(h,l)&&nH(h.prev,h,h.next)>0&&nH(l.prev,l,l.next)>0))){let h=nQ(o,e);o=nD(o,o.next),h=nD(h,h.next),t(o,i,s,r,n,a,0),t(h,i,s,r,n,a,0);return}e=e.next}o=o.next}while(o!==e)}(e,i,s,r,n,a):t(nD(e),i,s,r,n,a,1);break}}}(h,l,i,s,r,n,0),l}(t,e,i)}}class n3{static area(t){let e=t.length,i=0;for(let s=e-1,r=0;r<e;s=r++)i+=t[s].x*t[r].y-t[r].x*t[s].y;return .5*i}static isClockWise(t){return 0>n3.area(t)}static triangulateShape(t,e){let i=[],s=[],r=[];n5(t),n4(i,t);let n=t.length;e.forEach(n5);for(let t=0;t<e.length;t++)s.push(n),n+=e[t].length,n4(i,e[t]);let a=n2.triangulate(i,s);for(let t=0;t<a.length;t+=3)r.push(a.slice(t,t+3));return r}}function n5(t){let e=t.length;e>2&&t[e-1].equals(t[0])&&t.pop()}function n4(t,e){for(let i=0;i<e.length;i++)t.push(e[i].x),t.push(e[i].y)}class n6 extends sU{constructor(t=new nF([new eX(.5,.5),new eX(-.5,.5),new eX(-.5,-.5),new eX(.5,-.5)]),e={}){super(),this.type="ExtrudeGeometry",this.parameters={shapes:t,options:e},t=Array.isArray(t)?t:[t];let i=this,s=[],r=[];for(let n=0,a=t.length;n<a;n++)!function(t){let n,a,o,h,l,u=[],c=void 0!==e.curveSegments?e.curveSegments:12,p=void 0!==e.steps?e.steps:1,d=void 0!==e.depth?e.depth:1,m=void 0===e.bevelEnabled||e.bevelEnabled,y=void 0!==e.bevelThickness?e.bevelThickness:.2,f=void 0!==e.bevelSize?e.bevelSize:y-.1,g=void 0!==e.bevelOffset?e.bevelOffset:0,x=void 0!==e.bevelSegments?e.bevelSegments:3,b=e.extrudePath,M=void 0!==e.UVGenerator?e.UVGenerator:n8,w,v=!1;b&&(w=b.getSpacedPoints(p),v=!0,m=!1,n=b.computeFrenetFrames(p,!1),a=new eZ,o=new eZ,h=new eZ),m||(x=0,y=0,f=0,g=0);let S=t.extractPoints(c),_=S.shape,z=S.holes;if(!n3.isClockWise(_)){_=_.reverse();for(let t=0,e=z.length;t<e;t++){let e=z[t];n3.isClockWise(e)&&(z[t]=e.reverse())}}function A(t){let e=1e-10*1e-10,i=t[0];for(let s=1;s<=t.length;s++){let r=s%t.length,n=t[r],a=n.x-i.x,o=n.y-i.y,h=a*a+o*o,l=Math.max(Math.abs(n.x),Math.abs(n.y),Math.abs(i.x),Math.abs(i.y));if(h<=e*l*l){t.splice(r,1),s--;continue}i=n}}A(_),z.forEach(A);let T=z.length,C=_;for(let t=0;t<T;t++){let e=z[t];_=_.concat(e)}function k(t,e,i){return e||console.error("THREE.ExtrudeGeometry: vec does not exist"),t.clone().addScaledVector(e,i)}let E=_.length;function O(t,e,i){let s,r,n,a=t.x-e.x,o=t.y-e.y,h=i.x-t.x,l=i.y-t.y,u=a*a+o*o;if(Math.abs(a*l-o*h)>Number.EPSILON){let c=Math.sqrt(u),p=Math.sqrt(h*h+l*l),d=e.x-o/c,m=e.y+a/c,y=((i.x-l/p-d)*l-(i.y+h/p-m)*h)/(a*l-o*h),f=(s=d+a*y-t.x)*s+(r=m+o*y-t.y)*r;if(f<=2)return new eX(s,r);n=Math.sqrt(f/2)}else{let t=!1;a>Number.EPSILON?h>Number.EPSILON&&(t=!0):a<-Number.EPSILON?h<-Number.EPSILON&&(t=!0):Math.sign(o)===Math.sign(l)&&(t=!0),t?(s=-o,r=a,n=Math.sqrt(u)):(s=a,r=o,n=Math.sqrt(u/2))}return new eX(s/n,r/n)}let B=[];for(let t=0,e=C.length,i=e-1,s=t+1;t<e;t++,i++,s++)i===e&&(i=0),s===e&&(s=0),B[t]=O(C[t],C[i],C[s]);let P=[],I,N=B.concat();for(let t=0;t<T;t++){let e=z[t];I=[];for(let t=0,i=e.length,s=i-1,r=t+1;t<i;t++,s++,r++)s===i&&(s=0),r===i&&(r=0),I[t]=O(e[t],e[s],e[r]);P.push(I),N=N.concat(I)}if(0===x)l=n3.triangulateShape(C,z);else{let t=[],e=[];for(let i=0;i<x;i++){let s=i/x,r=y*Math.cos(s*Math.PI/2),n=f*Math.sin(s*Math.PI/2)+g;for(let e=0,i=C.length;e<i;e++){let i=k(C[e],B[e],n);F(i.x,i.y,-r),0===s&&t.push(i)}for(let t=0;t<T;t++){let i=z[t];I=P[t];let a=[];for(let t=0,e=i.length;t<e;t++){let e=k(i[t],I[t],n);F(e.x,e.y,-r),0===s&&a.push(e)}0===s&&e.push(a)}}l=n3.triangulateShape(t,e)}let R=l.length,V=f+g;for(let t=0;t<E;t++){let e=m?k(_[t],N[t],V):_[t];v?(o.copy(n.normals[0]).multiplyScalar(e.x),a.copy(n.binormals[0]).multiplyScalar(e.y),h.copy(w[0]).add(o).add(a),F(h.x,h.y,h.z)):F(e.x,e.y,0)}for(let t=1;t<=p;t++)for(let e=0;e<E;e++){let i=m?k(_[e],N[e],V):_[e];v?(o.copy(n.normals[t]).multiplyScalar(i.x),a.copy(n.binormals[t]).multiplyScalar(i.y),h.copy(w[t]).add(o).add(a),F(h.x,h.y,h.z)):F(i.x,i.y,d/p*t)}for(let t=x-1;t>=0;t--){let e=t/x,i=y*Math.cos(e*Math.PI/2),s=f*Math.sin(e*Math.PI/2)+g;for(let t=0,e=C.length;t<e;t++){let e=k(C[t],B[t],s);F(e.x,e.y,d+i)}for(let t=0,e=z.length;t<e;t++){let e=z[t];I=P[t];for(let t=0,r=e.length;t<r;t++){let r=k(e[t],I[t],s);v?F(r.x,r.y+w[p-1].y,w[p-1].x+i):F(r.x,r.y,d+i)}}}function L(t,e){let r=t.length;for(;--r>=0;){let n=r,a=r-1;a<0&&(a=t.length-1);for(let t=0,r=p+2*x;t<r;t++){let r=E*t,o=E*(t+1);!function(t,e,r,n){D(t),D(e),D(n),D(e),D(r),D(n);let a=s.length/3,o=M.generateSideWallUV(i,s,a-6,a-3,a-2,a-1);W(o[0]),W(o[1]),W(o[3]),W(o[1]),W(o[2]),W(o[3])}(e+n+r,e+a+r,e+a+o,e+n+o)}}}function F(t,e,i){u.push(t),u.push(e),u.push(i)}function j(t,e,r){D(t),D(e),D(r);let n=s.length/3,a=M.generateTopUV(i,s,n-3,n-2,n-1);W(a[0]),W(a[1]),W(a[2])}function D(t){s.push(u[3*t+0]),s.push(u[3*t+1]),s.push(u[3*t+2])}function W(t){r.push(t.x),r.push(t.y)}(function(){let t=s.length/3;if(m){let t=0,e=0*E;for(let t=0;t<R;t++){let i=l[t];j(i[2]+e,i[1]+e,i[0]+e)}e=E*(p+2*x);for(let t=0;t<R;t++){let i=l[t];j(i[0]+e,i[1]+e,i[2]+e)}}else{for(let t=0;t<R;t++){let e=l[t];j(e[2],e[1],e[0])}for(let t=0;t<R;t++){let e=l[t];j(e[0]+E*p,e[1]+E*p,e[2]+E*p)}}i.addGroup(t,s.length/3-t,0)})(),function(){let t=s.length/3,e=0;L(C,0),e+=C.length;for(let t=0,i=z.length;t<i;t++){let i=z[t];L(i,e),e+=i.length}i.addGroup(t,s.length/3-t,1)}()}(t[n]);this.setAttribute("position",new sN(s,3)),this.setAttribute("uv",new sN(r,2)),this.computeVertexNormals()}copy(t){return super.copy(t),this.parameters=Object.assign({},t.parameters),this}toJSON(){let t=super.toJSON();return function(t,e,i){if(i.shapes=[],Array.isArray(t))for(let e=0,s=t.length;e<s;e++){let s=t[e];i.shapes.push(s.uuid)}else i.shapes.push(t.uuid);return i.options=Object.assign({},e),void 0!==e.extrudePath&&(i.options.extrudePath=e.extrudePath.toJSON()),i}(this.parameters.shapes,this.parameters.options,t)}static fromJSON(t,e){let i=[];for(let s=0,r=t.shapes.length;s<r;s++){let r=e[t.shapes[s]];i.push(r)}let s=t.options.extrudePath;return void 0!==s&&(t.options.extrudePath=new nR[s.type]().fromJSON(s)),new n6(i,t.options)}}let n8={generateTopUV:function(t,e,i,s,r){let n=e[3*i],a=e[3*i+1],o=e[3*s],h=e[3*s+1],l=e[3*r],u=e[3*r+1];return[new eX(n,a),new eX(o,h),new eX(l,u)]},generateSideWallUV:function(t,e,i,s,r,n){let a=e[3*i],o=e[3*i+1],h=e[3*i+2],l=e[3*s],u=e[3*s+1],c=e[3*s+2],p=e[3*r],d=e[3*r+1],m=e[3*r+2],y=e[3*n],f=e[3*n+1],g=e[3*n+2];return Math.abs(o-u)<Math.abs(a-l)?[new eX(a,1-h),new eX(l,1-c),new eX(p,1-m),new eX(y,1-g)]:[new eX(o,1-h),new eX(u,1-c),new eX(d,1-m),new eX(f,1-g)]}};class n9 extends nc{constructor(t=1,e=0){let i=(1+Math.sqrt(5))/2;super([-1,i,0,1,i,0,-1,-i,0,1,-i,0,0,-1,i,0,1,i,0,-1,-i,0,1,-i,i,0,-1,i,0,1,-i,0,-1,-i,0,1],[0,11,5,0,5,1,0,1,7,0,7,10,0,10,11,1,5,9,5,11,4,11,10,2,10,7,6,7,1,8,3,9,4,3,4,2,3,2,6,3,6,8,3,8,9,4,9,5,2,4,11,6,2,10,8,6,7,9,8,1],t,e),this.type="IcosahedronGeometry",this.parameters={radius:t,detail:e}}static fromJSON(t){return new n9(t.radius,t.detail)}}class n7 extends sU{constructor(t=[new eX(0,-.5),new eX(.5,0),new eX(0,.5)],e=12,i=0,s=2*Math.PI){super(),this.type="LatheGeometry",this.parameters={points:t,segments:e,phiStart:i,phiLength:s},e=Math.floor(e),s=eD(s,0,2*Math.PI);let r=[],n=[],a=[],o=[],h=[],l=1/e,u=new eZ,c=new eX,p=new eZ,d=new eZ,m=new eZ,y=0,f=0;for(let e=0;e<=t.length-1;e++)switch(e){case 0:y=t[e+1].x-t[e].x,p.x=+(f=t[e+1].y-t[e].y),p.y=-y,p.z=0*f,m.copy(p),p.normalize(),o.push(p.x,p.y,p.z);break;case t.length-1:o.push(m.x,m.y,m.z);break;default:y=t[e+1].x-t[e].x,p.x=+(f=t[e+1].y-t[e].y),p.y=-y,p.z=0*f,d.copy(p),p.x+=m.x,p.y+=m.y,p.z+=m.z,p.normalize(),o.push(p.x,p.y,p.z),m.copy(d)}for(let r=0;r<=e;r++){let p=i+r*l*s,d=Math.sin(p),m=Math.cos(p);for(let i=0;i<=t.length-1;i++){u.x=t[i].x*d,u.y=t[i].y,u.z=t[i].x*m,n.push(u.x,u.y,u.z),c.x=r/e,c.y=i/(t.length-1),a.push(c.x,c.y);let s=o[3*i+0]*d,l=o[3*i+1],p=o[3*i+0]*m;h.push(s,l,p)}}for(let i=0;i<e;i++)for(let e=0;e<t.length-1;e++){let s=e+i*t.length,n=s+t.length,a=s+t.length+1,o=s+1;r.push(s,n,o),r.push(a,o,n)}this.setIndex(r),this.setAttribute("position",new sN(n,3)),this.setAttribute("uv",new sN(a,2)),this.setAttribute("normal",new sN(h,3))}copy(t){return super.copy(t),this.parameters=Object.assign({},t.parameters),this}static fromJSON(t){return new n7(t.points,t.segments,t.phiStart,t.phiLength)}}class at extends nc{constructor(t=1,e=0){super([1,0,0,-1,0,0,0,1,0,0,-1,0,0,0,1,0,0,-1],[0,2,4,0,4,3,0,3,5,0,5,2,1,2,5,1,5,3,1,3,4,1,4,2],t,e),this.type="OctahedronGeometry",this.parameters={radius:t,detail:e}}static fromJSON(t){return new at(t.radius,t.detail)}}class ae extends sU{constructor(t=1,e=1,i=1,s=1){super(),this.type="PlaneGeometry",this.parameters={width:t,height:e,widthSegments:i,heightSegments:s};let r=t/2,n=e/2,a=Math.floor(i),o=Math.floor(s),h=a+1,l=o+1,u=t/a,c=e/o,p=[],d=[],m=[],y=[];for(let t=0;t<l;t++){let e=t*c-n;for(let i=0;i<h;i++){let s=i*u-r;d.push(s,-e,0),m.push(0,0,1),y.push(i/a),y.push(1-t/o)}}for(let t=0;t<o;t++)for(let e=0;e<a;e++){let i=e+h*t,s=e+h*(t+1),r=e+1+h*(t+1),n=e+1+h*t;p.push(i,s,n),p.push(s,r,n)}this.setIndex(p),this.setAttribute("position",new sN(d,3)),this.setAttribute("normal",new sN(m,3)),this.setAttribute("uv",new sN(y,2))}copy(t){return super.copy(t),this.parameters=Object.assign({},t.parameters),this}static fromJSON(t){return new ae(t.width,t.height,t.widthSegments,t.heightSegments)}}class ai extends sU{constructor(t=.5,e=1,i=32,s=1,r=0,n=2*Math.PI){super(),this.type="RingGeometry",this.parameters={innerRadius:t,outerRadius:e,thetaSegments:i,phiSegments:s,thetaStart:r,thetaLength:n},i=Math.max(3,i);let a=[],o=[],h=[],l=[],u=t,c=(e-t)/(s=Math.max(1,s)),p=new eZ,d=new eX;for(let t=0;t<=s;t++){for(let t=0;t<=i;t++){let s=r+t/i*n;p.x=u*Math.cos(s),p.y=u*Math.sin(s),o.push(p.x,p.y,p.z),h.push(0,0,1),d.x=(p.x/e+1)/2,d.y=(p.y/e+1)/2,l.push(d.x,d.y)}u+=c}for(let t=0;t<s;t++){let e=t*(i+1);for(let t=0;t<i;t++){let s=t+e,r=s+i+1,n=s+i+2,o=s+1;a.push(s,r,o),a.push(r,n,o)}}this.setIndex(a),this.setAttribute("position",new sN(o,3)),this.setAttribute("normal",new sN(h,3)),this.setAttribute("uv",new sN(l,2))}copy(t){return super.copy(t),this.parameters=Object.assign({},t.parameters),this}static fromJSON(t){return new ai(t.innerRadius,t.outerRadius,t.thetaSegments,t.phiSegments,t.thetaStart,t.thetaLength)}}class as extends sU{constructor(t=new nF([new eX(0,.5),new eX(-.5,-.5),new eX(.5,-.5)]),e=12){super(),this.type="ShapeGeometry",this.parameters={shapes:t,curveSegments:e};let i=[],s=[],r=[],n=[],a=0,o=0;if(!1===Array.isArray(t))h(t);else for(let e=0;e<t.length;e++)h(t[e]),this.addGroup(a,o,e),a+=o,o=0;function h(t){let a=s.length/3,h=t.extractPoints(e),l=h.shape,u=h.holes;!1===n3.isClockWise(l)&&(l=l.reverse());for(let t=0,e=u.length;t<e;t++){let e=u[t];!0===n3.isClockWise(e)&&(u[t]=e.reverse())}let c=n3.triangulateShape(l,u);for(let t=0,e=u.length;t<e;t++){let e=u[t];l=l.concat(e)}for(let t=0,e=l.length;t<e;t++){let e=l[t];s.push(e.x,e.y,0),r.push(0,0,1),n.push(e.x,e.y)}for(let t=0,e=c.length;t<e;t++){let e=c[t],s=e[0]+a,r=e[1]+a,n=e[2]+a;i.push(s,r,n),o+=3}}this.setIndex(i),this.setAttribute("position",new sN(s,3)),this.setAttribute("normal",new sN(r,3)),this.setAttribute("uv",new sN(n,2))}copy(t){return super.copy(t),this.parameters=Object.assign({},t.parameters),this}toJSON(){let t=super.toJSON();return function(t,e){if(e.shapes=[],Array.isArray(t))for(let i=0,s=t.length;i<s;i++){let s=t[i];e.shapes.push(s.uuid)}else e.shapes.push(t.uuid);return e}(this.parameters.shapes,t)}static fromJSON(t,e){let i=[];for(let s=0,r=t.shapes.length;s<r;s++){let r=e[t.shapes[s]];i.push(r)}return new as(i,t.curveSegments)}}class ar extends sU{constructor(t=1,e=32,i=16,s=0,r=2*Math.PI,n=0,a=Math.PI){super(),this.type="SphereGeometry",this.parameters={radius:t,widthSegments:e,heightSegments:i,phiStart:s,phiLength:r,thetaStart:n,thetaLength:a},e=Math.max(3,Math.floor(e)),i=Math.max(2,Math.floor(i));let o=Math.min(n+a,Math.PI),h=0,l=[],u=new eZ,c=new eZ,p=[],d=[],m=[],y=[];for(let p=0;p<=i;p++){let f=[],g=p/i,x=0;0===p&&0===n?x=.5/e:p===i&&o===Math.PI&&(x=-.5/e);for(let i=0;i<=e;i++){let o=i/e;u.x=-t*Math.cos(s+o*r)*Math.sin(n+g*a),u.y=t*Math.cos(n+g*a),u.z=t*Math.sin(s+o*r)*Math.sin(n+g*a),d.push(u.x,u.y,u.z),c.copy(u).normalize(),m.push(c.x,c.y,c.z),y.push(o+x,1-g),f.push(h++)}l.push(f)}for(let t=0;t<i;t++)for(let s=0;s<e;s++){let e=l[t][s+1],r=l[t][s],a=l[t+1][s],h=l[t+1][s+1];(0!==t||n>0)&&p.push(e,r,h),(t!==i-1||o<Math.PI)&&p.push(r,a,h)}this.setIndex(p),this.setAttribute("position",new sN(d,3)),this.setAttribute("normal",new sN(m,3)),this.setAttribute("uv",new sN(y,2))}copy(t){return super.copy(t),this.parameters=Object.assign({},t.parameters),this}static fromJSON(t){return new ar(t.radius,t.widthSegments,t.heightSegments,t.phiStart,t.phiLength,t.thetaStart,t.thetaLength)}}class an extends nc{constructor(t=1,e=0){super([1,1,1,-1,-1,1,-1,1,-1,1,-1,-1],[2,1,0,0,3,2,1,3,0,2,3,1],t,e),this.type="TetrahedronGeometry",this.parameters={radius:t,detail:e}}static fromJSON(t){return new an(t.radius,t.detail)}}class aa extends sU{constructor(t=1,e=.4,i=12,s=48,r=2*Math.PI){super(),this.type="TorusGeometry",this.parameters={radius:t,tube:e,radialSegments:i,tubularSegments:s,arc:r},i=Math.floor(i),s=Math.floor(s);let n=[],a=[],o=[],h=[],l=new eZ,u=new eZ,c=new eZ;for(let n=0;n<=i;n++)for(let p=0;p<=s;p++){let d=p/s*r,m=n/i*Math.PI*2;u.x=(t+e*Math.cos(m))*Math.cos(d),u.y=(t+e*Math.cos(m))*Math.sin(d),u.z=e*Math.sin(m),a.push(u.x,u.y,u.z),l.x=t*Math.cos(d),l.y=t*Math.sin(d),c.subVectors(u,l).normalize(),o.push(c.x,c.y,c.z),h.push(p/s),h.push(n/i)}for(let t=1;t<=i;t++)for(let e=1;e<=s;e++){let i=(s+1)*t+e-1,r=(s+1)*(t-1)+e-1,a=(s+1)*(t-1)+e,o=(s+1)*t+e;n.push(i,r,o),n.push(r,a,o)}this.setIndex(n),this.setAttribute("position",new sN(a,3)),this.setAttribute("normal",new sN(o,3)),this.setAttribute("uv",new sN(h,2))}copy(t){return super.copy(t),this.parameters=Object.assign({},t.parameters),this}static fromJSON(t){return new aa(t.radius,t.tube,t.radialSegments,t.tubularSegments,t.arc)}}class ao extends sU{constructor(t=1,e=.4,i=64,s=8,r=2,n=3){super(),this.type="TorusKnotGeometry",this.parameters={radius:t,tube:e,tubularSegments:i,radialSegments:s,p:r,q:n},i=Math.floor(i),s=Math.floor(s);let a=[],o=[],h=[],l=[],u=new eZ,c=new eZ,p=new eZ,d=new eZ,m=new eZ,y=new eZ,f=new eZ;for(let a=0;a<=i;++a){let x=a/i*r*Math.PI*2;g(x,r,n,t,p),g(x+.01,r,n,t,d),y.subVectors(d,p),f.addVectors(d,p),m.crossVectors(y,f),f.crossVectors(m,y),m.normalize(),f.normalize();for(let t=0;t<=s;++t){let r=t/s*Math.PI*2,n=-e*Math.cos(r),d=e*Math.sin(r);u.x=p.x+(n*f.x+d*m.x),u.y=p.y+(n*f.y+d*m.y),u.z=p.z+(n*f.z+d*m.z),o.push(u.x,u.y,u.z),c.subVectors(u,p).normalize(),h.push(c.x,c.y,c.z),l.push(a/i),l.push(t/s)}}for(let t=1;t<=i;t++)for(let e=1;e<=s;e++){let i=(s+1)*(t-1)+(e-1),r=(s+1)*t+(e-1),n=(s+1)*t+e,o=(s+1)*(t-1)+e;a.push(i,r,o),a.push(r,n,o)}function g(t,e,i,s,r){let n=Math.cos(t),a=Math.sin(t),o=i/e*t,h=Math.cos(o);r.x=s*(2+h)*.5*n,r.y=s*(2+h)*a*.5,r.z=s*Math.sin(o)*.5}this.setIndex(a),this.setAttribute("position",new sN(o,3)),this.setAttribute("normal",new sN(h,3)),this.setAttribute("uv",new sN(l,2))}copy(t){return super.copy(t),this.parameters=Object.assign({},t.parameters),this}static fromJSON(t){return new ao(t.radius,t.tube,t.tubularSegments,t.radialSegments,t.p,t.q)}}class ah extends sU{constructor(t=new nI(new eZ(-1,-1,0),new eZ(-1,1,0),new eZ(1,1,0)),e=64,i=1,s=8,r=!1){super(),this.type="TubeGeometry",this.parameters={path:t,tubularSegments:e,radius:i,radialSegments:s,closed:r};let n=t.computeFrenetFrames(e,r);this.tangents=n.tangents,this.normals=n.normals,this.binormals=n.binormals;let a=new eZ,o=new eZ,h=new eX,l=new eZ,u=[],c=[],p=[],d=[];function m(r){l=t.getPointAt(r/e,l);let h=n.normals[r],p=n.binormals[r];for(let t=0;t<=s;t++){let e=t/s*Math.PI*2,r=Math.sin(e),n=-Math.cos(e);o.x=n*h.x+r*p.x,o.y=n*h.y+r*p.y,o.z=n*h.z+r*p.z,o.normalize(),c.push(o.x,o.y,o.z),a.x=l.x+i*o.x,a.y=l.y+i*o.y,a.z=l.z+i*o.z,u.push(a.x,a.y,a.z)}}(function(){for(let t=0;t<e;t++)m(t);m(!1===r?e:0),function(){for(let t=0;t<=e;t++)for(let i=0;i<=s;i++)h.x=t/e,h.y=i/s,p.push(h.x,h.y)}(),function(){for(let t=1;t<=e;t++)for(let e=1;e<=s;e++){let i=(s+1)*(t-1)+(e-1),r=(s+1)*t+(e-1),n=(s+1)*t+e,a=(s+1)*(t-1)+e;d.push(i,r,a),d.push(r,n,a)}}()})(),this.setIndex(d),this.setAttribute("position",new sN(u,3)),this.setAttribute("normal",new sN(c,3)),this.setAttribute("uv",new sN(p,2))}copy(t){return super.copy(t),this.parameters=Object.assign({},t.parameters),this}toJSON(){let t=super.toJSON();return t.path=this.parameters.path.toJSON(),t}static fromJSON(t){return new ah(new nR[t.path.type]().fromJSON(t.path),t.tubularSegments,t.radius,t.radialSegments,t.closed)}}function al(t,e,i){let s=`${t.x},${t.y},${t.z}-${e.x},${e.y},${e.z}`,r=`${e.x},${e.y},${e.z}-${t.x},${t.y},${t.z}`;return!0!==i.has(s)&&!0!==i.has(r)&&(i.add(s),i.add(r),!0)}class au extends sT{constructor(t){super(),this.isMeshStandardMaterial=!0,this.type="MeshStandardMaterial",this.defines={STANDARD:""},this.color=new s_(0xffffff),this.roughness=1,this.metalness=0,this.map=null,this.lightMap=null,this.lightMapIntensity=1,this.aoMap=null,this.aoMapIntensity=1,this.emissive=new s_(0),this.emissiveIntensity=1,this.emissiveMap=null,this.bumpMap=null,this.bumpScale=1,this.normalMap=null,this.normalMapType=ex,this.normalScale=new eX(1,1),this.displacementMap=null,this.displacementScale=1,this.displacementBias=0,this.roughnessMap=null,this.metalnessMap=null,this.alphaMap=null,this.envMap=null,this.envMapRotation=new iQ,this.envMapIntensity=1,this.wireframe=!1,this.wireframeLinewidth=1,this.wireframeLinecap="round",this.wireframeLinejoin="round",this.flatShading=!1,this.fog=!0,this.setValues(t)}copy(t){return super.copy(t),this.defines={STANDARD:""},this.color.copy(t.color),this.roughness=t.roughness,this.metalness=t.metalness,this.map=t.map,this.lightMap=t.lightMap,this.lightMapIntensity=t.lightMapIntensity,this.aoMap=t.aoMap,this.aoMapIntensity=t.aoMapIntensity,this.emissive.copy(t.emissive),this.emissiveMap=t.emissiveMap,this.emissiveIntensity=t.emissiveIntensity,this.bumpMap=t.bumpMap,this.bumpScale=t.bumpScale,this.normalMap=t.normalMap,this.normalMapType=t.normalMapType,this.normalScale.copy(t.normalScale),this.displacementMap=t.displacementMap,this.displacementScale=t.displacementScale,this.displacementBias=t.displacementBias,this.roughnessMap=t.roughnessMap,this.metalnessMap=t.metalnessMap,this.alphaMap=t.alphaMap,this.envMap=t.envMap,this.envMapRotation.copy(t.envMapRotation),this.envMapIntensity=t.envMapIntensity,this.wireframe=t.wireframe,this.wireframeLinewidth=t.wireframeLinewidth,this.wireframeLinecap=t.wireframeLinecap,this.wireframeLinejoin=t.wireframeLinejoin,this.flatShading=t.flatShading,this.fog=t.fog,this}}class ac extends au{constructor(t){super(),this.isMeshPhysicalMaterial=!0,this.defines={STANDARD:"",PHYSICAL:""},this.type="MeshPhysicalMaterial",this.anisotropyRotation=0,this.anisotropyMap=null,this.clearcoatMap=null,this.clearcoatRoughness=0,this.clearcoatRoughnessMap=null,this.clearcoatNormalScale=new eX(1,1),this.clearcoatNormalMap=null,this.ior=1.5,Object.defineProperty(this,"reflectivity",{get:function(){return eD(2.5*(this.ior-1)/(this.ior+1),0,1)},set:function(t){this.ior=(1+.4*t)/(1-.4*t)}}),this.iridescenceMap=null,this.iridescenceIOR=1.3,this.iridescenceThicknessRange=[100,400],this.iridescenceThicknessMap=null,this.sheenColor=new s_(0),this.sheenColorMap=null,this.sheenRoughness=1,this.sheenRoughnessMap=null,this.transmissionMap=null,this.thickness=0,this.thicknessMap=null,this.attenuationDistance=1/0,this.attenuationColor=new s_(1,1,1),this.specularIntensity=1,this.specularIntensityMap=null,this.specularColor=new s_(1,1,1),this.specularColorMap=null,this._anisotropy=0,this._clearcoat=0,this._dispersion=0,this._iridescence=0,this._sheen=0,this._transmission=0,this.setValues(t)}get anisotropy(){return this._anisotropy}set anisotropy(t){this._anisotropy>0!=t>0&&this.version++,this._anisotropy=t}get clearcoat(){return this._clearcoat}set clearcoat(t){this._clearcoat>0!=t>0&&this.version++,this._clearcoat=t}get iridescence(){return this._iridescence}set iridescence(t){this._iridescence>0!=t>0&&this.version++,this._iridescence=t}get dispersion(){return this._dispersion}set dispersion(t){this._dispersion>0!=t>0&&this.version++,this._dispersion=t}get sheen(){return this._sheen}set sheen(t){this._sheen>0!=t>0&&this.version++,this._sheen=t}get transmission(){return this._transmission}set transmission(t){this._transmission>0!=t>0&&this.version++,this._transmission=t}copy(t){return super.copy(t),this.defines={STANDARD:"",PHYSICAL:""},this.anisotropy=t.anisotropy,this.anisotropyRotation=t.anisotropyRotation,this.anisotropyMap=t.anisotropyMap,this.clearcoat=t.clearcoat,this.clearcoatMap=t.clearcoatMap,this.clearcoatRoughness=t.clearcoatRoughness,this.clearcoatRoughnessMap=t.clearcoatRoughnessMap,this.clearcoatNormalMap=t.clearcoatNormalMap,this.clearcoatNormalScale.copy(t.clearcoatNormalScale),this.dispersion=t.dispersion,this.ior=t.ior,this.iridescence=t.iridescence,this.iridescenceMap=t.iridescenceMap,this.iridescenceIOR=t.iridescenceIOR,this.iridescenceThicknessRange=[...t.iridescenceThicknessRange],this.iridescenceThicknessMap=t.iridescenceThicknessMap,this.sheen=t.sheen,this.sheenColor.copy(t.sheenColor),this.sheenColorMap=t.sheenColorMap,this.sheenRoughness=t.sheenRoughness,this.sheenRoughnessMap=t.sheenRoughnessMap,this.transmission=t.transmission,this.transmissionMap=t.transmissionMap,this.thickness=t.thickness,this.thicknessMap=t.thicknessMap,this.attenuationDistance=t.attenuationDistance,this.attenuationColor.copy(t.attenuationColor),this.specularIntensity=t.specularIntensity,this.specularIntensityMap=t.specularIntensityMap,this.specularColor.copy(t.specularColor),this.specularColorMap=t.specularColorMap,this}}class ap extends sT{constructor(t){super(),this.isMeshDepthMaterial=!0,this.type="MeshDepthMaterial",this.depthPacking=3200,this.map=null,this.alphaMap=null,this.displacementMap=null,this.displacementScale=1,this.displacementBias=0,this.wireframe=!1,this.wireframeLinewidth=1,this.setValues(t)}copy(t){return super.copy(t),this.depthPacking=t.depthPacking,this.map=t.map,this.alphaMap=t.alphaMap,this.displacementMap=t.displacementMap,this.displacementScale=t.displacementScale,this.displacementBias=t.displacementBias,this.wireframe=t.wireframe,this.wireframeLinewidth=t.wireframeLinewidth,this}}class ad extends sT{constructor(t){super(),this.isMeshDistanceMaterial=!0,this.type="MeshDistanceMaterial",this.map=null,this.alphaMap=null,this.displacementMap=null,this.displacementScale=1,this.displacementBias=0,this.setValues(t)}copy(t){return super.copy(t),this.map=t.map,this.alphaMap=t.alphaMap,this.displacementMap=t.displacementMap,this.displacementScale=t.displacementScale,this.displacementBias=t.displacementBias,this}}function am(t,e){return t&&t.constructor!==e?"number"==typeof e.BYTES_PER_ELEMENT?new e(t):Array.prototype.slice.call(t):t}function ay(t,e,i){let s=t.length,r=new t.constructor(s);for(let n=0,a=0;a!==s;++n){let s=i[n]*e;for(let i=0;i!==e;++i)r[a++]=t[s+i]}return r}function af(t,e,i,s){let r=1,n=t[0];for(;void 0!==n&&void 0===n[s];)n=t[r++];if(void 0===n)return;let a=n[s];if(void 0!==a)if(Array.isArray(a))do void 0!==(a=n[s])&&(e.push(n.time),i.push(...a)),n=t[r++];while(void 0!==n);else if(void 0!==a.toArray)do void 0!==(a=n[s])&&(e.push(n.time),a.toArray(i,i.length)),n=t[r++];while(void 0!==n);else do void 0!==(a=n[s])&&(e.push(n.time),i.push(a)),n=t[r++];while(void 0!==n)}class ag{constructor(t,e,i,s){this.parameterPositions=t,this._cachedIndex=0,this.resultBuffer=void 0!==s?s:new e.constructor(i),this.sampleValues=e,this.valueSize=i,this.settings=null,this.DefaultSettings_={}}evaluate(t){let e=this.parameterPositions,i=this._cachedIndex,s=e[i],r=e[i-1];t:{e:{let n;i:{s:if(!(t<s)){for(let n=i+2;;){if(void 0===s){if(t<r)break s;return i=e.length,this._cachedIndex=i,this.copySampleValue_(i-1)}if(i===n)break;if(r=s,t<(s=e[++i]))break e}n=e.length;break i}if(!(t>=r)){let a=e[1];t<a&&(i=2,r=a);for(let n=i-2;;){if(void 0===r)return this._cachedIndex=0,this.copySampleValue_(0);if(i===n)break;if(s=r,t>=(r=e[--i-1]))break e}n=i,i=0;break i}break t}for(;i<n;){let s=i+n>>>1;t<e[s]?n=s:i=s+1}if(s=e[i],void 0===(r=e[i-1]))return this._cachedIndex=0,this.copySampleValue_(0);if(void 0===s)return i=e.length,this._cachedIndex=i,this.copySampleValue_(i-1)}this._cachedIndex=i,this.intervalChanged_(i,r,s)}return this.interpolate_(i,r,t,s)}getSettings_(){return this.settings||this.DefaultSettings_}copySampleValue_(t){let e=this.resultBuffer,i=this.sampleValues,s=this.valueSize,r=t*s;for(let t=0;t!==s;++t)e[t]=i[r+t];return e}interpolate_(){throw Error("call to abstract method")}intervalChanged_(){}}class ax extends ag{constructor(t,e,i,s){super(t,e,i,s),this._weightPrev=-0,this._offsetPrev=-0,this._weightNext=-0,this._offsetNext=-0,this.DefaultSettings_={endingStart:2400,endingEnd:2400}}intervalChanged_(t,e,i){let s=this.parameterPositions,r=t-2,n=t+1,a=s[r],o=s[n];if(void 0===a)switch(this.getSettings_().endingStart){case 2401:r=t,a=2*e-i;break;case 2402:r=s.length-2,a=e+s[r]-s[r+1];break;default:r=t,a=i}if(void 0===o)switch(this.getSettings_().endingEnd){case 2401:n=t,o=2*i-e;break;case 2402:n=1,o=i+s[1]-s[0];break;default:n=t-1,o=e}let h=(i-e)*.5,l=this.valueSize;this._weightPrev=h/(e-a),this._weightNext=h/(o-i),this._offsetPrev=r*l,this._offsetNext=n*l}interpolate_(t,e,i,s){let r=this.resultBuffer,n=this.sampleValues,a=this.valueSize,o=t*a,h=o-a,l=this._offsetPrev,u=this._offsetNext,c=this._weightPrev,p=this._weightNext,d=(i-e)/(s-e),m=d*d,y=m*d,f=-c*y+2*c*m-c*d,g=(1+c)*y+(-1.5-2*c)*m+(-.5+c)*d+1,x=(-1-p)*y+(1.5+p)*m+.5*d,b=p*y-p*m;for(let t=0;t!==a;++t)r[t]=f*n[l+t]+g*n[h+t]+x*n[o+t]+b*n[u+t];return r}}class ab extends ag{constructor(t,e,i,s){super(t,e,i,s)}interpolate_(t,e,i,s){let r=this.resultBuffer,n=this.sampleValues,a=this.valueSize,o=t*a,h=o-a,l=(i-e)/(s-e),u=1-l;for(let t=0;t!==a;++t)r[t]=n[h+t]*u+n[o+t]*l;return r}}class aM extends ag{constructor(t,e,i,s){super(t,e,i,s)}interpolate_(t){return this.copySampleValue_(t-1)}}class aw{constructor(t,e,i,s){if(void 0===t)throw Error("THREE.KeyframeTrack: track name is undefined");if(void 0===e||0===e.length)throw Error("THREE.KeyframeTrack: no keyframes in track named "+t);this.name=t,this.times=am(e,this.TimeBufferType),this.values=am(i,this.ValueBufferType),this.setInterpolation(s||this.DefaultInterpolation)}static toJSON(t){let e,i=t.constructor;if(i.toJSON!==this.toJSON)e=i.toJSON(t);else{e={name:t.name,times:am(t.times,Array),values:am(t.values,Array)};let i=t.getInterpolation();i!==t.DefaultInterpolation&&(e.interpolation=i)}return e.type=t.ValueTypeName,e}InterpolantFactoryMethodDiscrete(t){return new aM(this.times,this.values,this.getValueSize(),t)}InterpolantFactoryMethodLinear(t){return new ab(this.times,this.values,this.getValueSize(),t)}InterpolantFactoryMethodSmooth(t){return new ax(this.times,this.values,this.getValueSize(),t)}setInterpolation(t){let e;switch(t){case ep:e=this.InterpolantFactoryMethodDiscrete;break;case ed:e=this.InterpolantFactoryMethodLinear;break;case 2302:e=this.InterpolantFactoryMethodSmooth}if(void 0===e){let e="unsupported interpolation for "+this.ValueTypeName+" keyframe track named "+this.name;if(void 0===this.createInterpolant)if(t!==this.DefaultInterpolation)this.setInterpolation(this.DefaultInterpolation);else throw Error(e);return console.warn("THREE.KeyframeTrack:",e),this}return this.createInterpolant=e,this}getInterpolation(){switch(this.createInterpolant){case this.InterpolantFactoryMethodDiscrete:return ep;case this.InterpolantFactoryMethodLinear:return ed;case this.InterpolantFactoryMethodSmooth:return 2302}}getValueSize(){return this.values.length/this.times.length}shift(t){if(0!==t){let e=this.times;for(let i=0,s=e.length;i!==s;++i)e[i]+=t}return this}scale(t){if(1!==t){let e=this.times;for(let i=0,s=e.length;i!==s;++i)e[i]*=t}return this}trim(t,e){let i=this.times,s=i.length,r=0,n=s-1;for(;r!==s&&i[r]<t;)++r;for(;-1!==n&&i[n]>e;)--n;if(++n,0!==r||n!==s){r>=n&&(r=(n=Math.max(n,1))-1);let t=this.getValueSize();this.times=i.slice(r,n),this.values=this.values.slice(r*t,n*t)}return this}validate(){var t;let e=!0,i=this.getValueSize();i-Math.floor(i)!=0&&(console.error("THREE.KeyframeTrack: Invalid value size in track.",this),e=!1);let s=this.times,r=this.values,n=s.length;0===n&&(console.error("THREE.KeyframeTrack: Track is empty.",this),e=!1);let a=null;for(let t=0;t!==n;t++){let i=s[t];if("number"==typeof i&&isNaN(i)){console.error("THREE.KeyframeTrack: Time is not a valid number.",this,t,i),e=!1;break}if(null!==a&&a>i){console.error("THREE.KeyframeTrack: Out of order keys.",this,t,i,a),e=!1;break}a=i}if(void 0!==r&&ArrayBuffer.isView(t=r)&&!(t instanceof DataView))for(let t=0,i=r.length;t!==i;++t){let i=r[t];if(isNaN(i)){console.error("THREE.KeyframeTrack: Value is not a valid number.",this,t,i),e=!1;break}}return e}optimize(){let t=this.times.slice(),e=this.values.slice(),i=this.getValueSize(),s=2302===this.getInterpolation(),r=t.length-1,n=1;for(let a=1;a<r;++a){let r=!1,o=t[a];if(o!==t[a+1]&&(1!==a||o!==t[0]))if(s)r=!0;else{let t=a*i,s=t-i,n=t+i;for(let a=0;a!==i;++a){let i=e[t+a];if(i!==e[s+a]||i!==e[n+a]){r=!0;break}}}if(r){if(a!==n){t[n]=t[a];let s=a*i,r=n*i;for(let t=0;t!==i;++t)e[r+t]=e[s+t]}++n}}if(r>0){t[n]=t[r];for(let t=r*i,s=n*i,a=0;a!==i;++a)e[s+a]=e[t+a];++n}return n!==t.length?(this.times=t.slice(0,n),this.values=e.slice(0,n*i)):(this.times=t,this.values=e),this}clone(){let t=this.times.slice(),e=this.values.slice(),i=new this.constructor(this.name,t,e);return i.createInterpolant=this.createInterpolant,i}}aw.prototype.ValueTypeName="",aw.prototype.TimeBufferType=Float32Array,aw.prototype.ValueBufferType=Float32Array,aw.prototype.DefaultInterpolation=ed;class av extends aw{constructor(t,e,i){super(t,e,i)}}av.prototype.ValueTypeName="bool",av.prototype.ValueBufferType=Array,av.prototype.DefaultInterpolation=ep,av.prototype.InterpolantFactoryMethodLinear=void 0,av.prototype.InterpolantFactoryMethodSmooth=void 0;class aS extends aw{constructor(t,e,i,s){super(t,e,i,s)}}aS.prototype.ValueTypeName="color";class a_ extends aw{constructor(t,e,i,s){super(t,e,i,s)}}a_.prototype.ValueTypeName="number";class az extends ag{constructor(t,e,i,s){super(t,e,i,s)}interpolate_(t,e,i,s){let r=this.resultBuffer,n=this.sampleValues,a=this.valueSize,o=(i-e)/(s-e),h=t*a;for(let t=h+a;h!==t;h+=4)eY.slerpFlat(r,0,n,h-a,n,h,o);return r}}class aA extends aw{constructor(t,e,i,s){super(t,e,i,s)}InterpolantFactoryMethodLinear(t){return new az(this.times,this.values,this.getValueSize(),t)}}aA.prototype.ValueTypeName="quaternion",aA.prototype.InterpolantFactoryMethodSmooth=void 0;class aT extends aw{constructor(t,e,i){super(t,e,i)}}aT.prototype.ValueTypeName="string",aT.prototype.ValueBufferType=Array,aT.prototype.DefaultInterpolation=ep,aT.prototype.InterpolantFactoryMethodLinear=void 0,aT.prototype.InterpolantFactoryMethodSmooth=void 0;class aC extends aw{constructor(t,e,i,s){super(t,e,i,s)}}aC.prototype.ValueTypeName="vector";class ak{constructor(t="",e=-1,i=[],s=2500){this.name=t,this.tracks=i,this.duration=e,this.blendMode=s,this.uuid=ej(),this.duration<0&&this.resetDuration()}static parse(t){let e=[],i=t.tracks,s=1/(t.fps||1);for(let t=0,r=i.length;t!==r;++t)e.push((function(t){if(void 0===t.type)throw Error("THREE.KeyframeTrack: track type undefined, can not parse");let e=function(t){switch(t.toLowerCase()){case"scalar":case"double":case"float":case"number":case"integer":return a_;case"vector":case"vector2":case"vector3":case"vector4":return aC;case"color":return aS;case"quaternion":return aA;case"bool":case"boolean":return av;case"string":return aT}throw Error("THREE.KeyframeTrack: Unsupported typeName: "+t)}(t.type);if(void 0===t.times){let e=[],i=[];af(t.keys,e,i,"value"),t.times=e,t.values=i}return void 0!==e.parse?e.parse(t):new e(t.name,t.times,t.values,t.interpolation)})(i[t]).scale(s));let r=new this(t.name,t.duration,e,t.blendMode);return r.uuid=t.uuid,r}static toJSON(t){let e=[],i=t.tracks,s={name:t.name,duration:t.duration,tracks:e,uuid:t.uuid,blendMode:t.blendMode};for(let t=0,s=i.length;t!==s;++t)e.push(aw.toJSON(i[t]));return s}static CreateFromMorphTargetSequence(t,e,i,s){let r=e.length,n=[];for(let t=0;t<r;t++){let a=[],o=[];a.push((t+r-1)%r,t,(t+1)%r),o.push(0,1,0);let h=function(t){let e=t.length,i=Array(e);for(let t=0;t!==e;++t)i[t]=t;return i.sort(function(e,i){return t[e]-t[i]}),i}(a);a=ay(a,1,h),o=ay(o,1,h),s||0!==a[0]||(a.push(r),o.push(o[0])),n.push(new a_(".morphTargetInfluences["+e[t].name+"]",a,o).scale(1/i))}return new this(t,-1,n)}static findByName(t,e){let i=t;Array.isArray(t)||(i=t.geometry&&t.geometry.animations||t.animations);for(let t=0;t<i.length;t++)if(i[t].name===e)return i[t];return null}static CreateClipsFromMorphTargetSequences(t,e,i){let s={},r=/^([\w-]*?)([\d]+)$/;for(let e=0,i=t.length;e<i;e++){let i=t[e],n=i.name.match(r);if(n&&n.length>1){let t=n[1],e=s[t];e||(s[t]=e=[]),e.push(i)}}let n=[];for(let t in s)n.push(this.CreateFromMorphTargetSequence(t,s[t],e,i));return n}static parseAnimation(t,e){if(console.warn("THREE.AnimationClip: parseAnimation() is deprecated and will be removed with r185"),!t)return console.error("THREE.AnimationClip: No animation in JSONLoader data."),null;let i=function(t,e,i,s,r){if(0!==i.length){let n=[],a=[];af(i,n,a,s),0!==n.length&&r.push(new t(e,n,a))}},s=[],r=t.name||"default",n=t.fps||30,a=t.blendMode,o=t.length||-1,h=t.hierarchy||[];for(let t=0;t<h.length;t++){let r=h[t].keys;if(r&&0!==r.length)if(r[0].morphTargets){let t,e={};for(t=0;t<r.length;t++)if(r[t].morphTargets)for(let i=0;i<r[t].morphTargets.length;i++)e[r[t].morphTargets[i]]=-1;for(let i in e){let e=[],n=[];for(let s=0;s!==r[t].morphTargets.length;++s){let s=r[t];e.push(s.time),n.push(+(s.morphTarget===i))}s.push(new a_(".morphTargetInfluence["+i+"]",e,n))}o=e.length*n}else{let n=".bones["+e[t].name+"]";i(aC,n+".position",r,"pos",s),i(aA,n+".quaternion",r,"rot",s),i(aC,n+".scale",r,"scl",s)}}return 0===s.length?null:new this(r,o,s,a)}resetDuration(){let t=this.tracks,e=0;for(let i=0,s=t.length;i!==s;++i){let t=this.tracks[i];e=Math.max(e,t.times[t.times.length-1])}return this.duration=e,this}trim(){for(let t=0;t<this.tracks.length;t++)this.tracks[t].trim(0,this.duration);return this}validate(){let t=!0;for(let e=0;e<this.tracks.length;e++)t=t&&this.tracks[e].validate();return t}optimize(){for(let t=0;t<this.tracks.length;t++)this.tracks[t].optimize();return this}clone(){let t=[];for(let e=0;e<this.tracks.length;e++)t.push(this.tracks[e].clone());return new this.constructor(this.name,this.duration,t,this.blendMode)}toJSON(){return this.constructor.toJSON(this)}}let aE={enabled:!1,files:{},add:function(t,e){!1!==this.enabled&&(this.files[t]=e)},get:function(t){if(!1!==this.enabled)return this.files[t]},remove:function(t){delete this.files[t]},clear:function(){this.files={}}};class aO{constructor(t,e,i){let s,r=this,n=!1,a=0,o=0,h=[];this.onStart=void 0,this.onLoad=t,this.onProgress=e,this.onError=i,this.abortController=new AbortController,this.itemStart=function(t){o++,!1===n&&void 0!==r.onStart&&r.onStart(t,a,o),n=!0},this.itemEnd=function(t){a++,void 0!==r.onProgress&&r.onProgress(t,a,o),a===o&&(n=!1,void 0!==r.onLoad&&r.onLoad())},this.itemError=function(t){void 0!==r.onError&&r.onError(t)},this.resolveURL=function(t){return s?s(t):t},this.setURLModifier=function(t){return s=t,this},this.addHandler=function(t,e){return h.push(t,e),this},this.removeHandler=function(t){let e=h.indexOf(t);return -1!==e&&h.splice(e,2),this},this.getHandler=function(t){for(let e=0,i=h.length;e<i;e+=2){let i=h[e],s=h[e+1];if(i.global&&(i.lastIndex=0),i.test(t))return s}return null},this.abort=function(){return this.abortController.abort(),this.abortController=new AbortController,this}}}let aB=new aO;class aP{constructor(t){this.manager=void 0!==t?t:aB,this.crossOrigin="anonymous",this.withCredentials=!1,this.path="",this.resourcePath="",this.requestHeader={}}load(){}loadAsync(t,e){let i=this;return new Promise(function(s,r){i.load(t,s,e,r)})}parse(){}setCrossOrigin(t){return this.crossOrigin=t,this}setWithCredentials(t){return this.withCredentials=t,this}setPath(t){return this.path=t,this}setResourcePath(t){return this.resourcePath=t,this}setRequestHeader(t){return this.requestHeader=t,this}abort(){return this}}aP.DEFAULT_MATERIAL_NAME="__DEFAULT";let aI={};class aN extends Error{constructor(t,e){super(t),this.response=e}}class aR extends aP{constructor(t){super(t),this.mimeType="",this.responseType="",this._abortController=new AbortController}load(t,e,i,s){void 0===t&&(t=""),void 0!==this.path&&(t=this.path+t),t=this.manager.resolveURL(t);let r=aE.get(`file:${t}`);if(void 0!==r)return this.manager.itemStart(t),setTimeout(()=>{e&&e(r),this.manager.itemEnd(t)},0),r;if(void 0!==aI[t])return void aI[t].push({onLoad:e,onProgress:i,onError:s});aI[t]=[],aI[t].push({onLoad:e,onProgress:i,onError:s});let n=new Request(t,{headers:new Headers(this.requestHeader),credentials:this.withCredentials?"include":"same-origin",signal:"function"==typeof AbortSignal.any?AbortSignal.any([this._abortController.signal,this.manager.abortController.signal]):this._abortController.signal}),a=this.mimeType,o=this.responseType;fetch(n).then(e=>{if(200===e.status||0===e.status){if(0===e.status&&console.warn("THREE.FileLoader: HTTP Status 0 received."),"undefined"==typeof ReadableStream||void 0===e.body||void 0===e.body.getReader)return e;let i=aI[t],s=e.body.getReader(),r=e.headers.get("X-File-Size")||e.headers.get("Content-Length"),n=r?parseInt(r):0,a=0!==n,o=0;return new Response(new ReadableStream({start(t){!function e(){s.read().then(({done:s,value:r})=>{if(s)t.close();else{let s=new ProgressEvent("progress",{lengthComputable:a,loaded:o+=r.byteLength,total:n});for(let t=0,e=i.length;t<e;t++){let e=i[t];e.onProgress&&e.onProgress(s)}t.enqueue(r),e()}},e=>{t.error(e)})}()}}))}throw new aN(`fetch for "${e.url}" responded with ${e.status}: ${e.statusText}`,e)}).then(t=>{switch(o){case"arraybuffer":return t.arrayBuffer();case"blob":return t.blob();case"document":return t.text().then(t=>new DOMParser().parseFromString(t,a));case"json":return t.json();default:if(""===a)return t.text();{let e=/charset="?([^;"\s]*)"?/i.exec(a),i=new TextDecoder(e&&e[1]?e[1].toLowerCase():void 0);return t.arrayBuffer().then(t=>i.decode(t))}}}).then(e=>{aE.add(`file:${t}`,e);let i=aI[t];delete aI[t];for(let t=0,s=i.length;t<s;t++){let s=i[t];s.onLoad&&s.onLoad(e)}}).catch(e=>{let i=aI[t];if(void 0===i)throw this.manager.itemError(t),e;delete aI[t];for(let t=0,s=i.length;t<s;t++){let s=i[t];s.onError&&s.onError(e)}this.manager.itemError(t)}).finally(()=>{this.manager.itemEnd(t)}),this.manager.itemStart(t)}setResponseType(t){return this.responseType=t,this}setMimeType(t){return this.mimeType=t,this}abort(){return this._abortController.abort(),this._abortController=new AbortController,this}}let aV=new WeakMap;class aL extends aP{constructor(t){super(t)}load(t,e,i,s){void 0!==this.path&&(t=this.path+t),t=this.manager.resolveURL(t);let r=this,n=aE.get(`image:${t}`);if(void 0!==n){if(!0===n.complete)r.manager.itemStart(t),setTimeout(function(){e&&e(n),r.manager.itemEnd(t)},0);else{let t=aV.get(n);void 0===t&&(t=[],aV.set(n,t)),t.push({onLoad:e,onError:s})}return n}let a=e1("img");function o(){l(),e&&e(this);let i=aV.get(this)||[];for(let t=0;t<i.length;t++){let e=i[t];e.onLoad&&e.onLoad(this)}aV.delete(this),r.manager.itemEnd(t)}function h(e){l(),s&&s(e),aE.remove(`image:${t}`);let i=aV.get(this)||[];for(let t=0;t<i.length;t++){let s=i[t];s.onError&&s.onError(e)}aV.delete(this),r.manager.itemError(t),r.manager.itemEnd(t)}function l(){a.removeEventListener("load",o,!1),a.removeEventListener("error",h,!1)}return a.addEventListener("load",o,!1),a.addEventListener("error",h,!1),"data:"!==t.slice(0,5)&&void 0!==this.crossOrigin&&(a.crossOrigin=this.crossOrigin),aE.add(`image:${t}`,a),r.manager.itemStart(t),a.src=t,a}}class aF extends aP{constructor(t){super(t)}load(t,e,i,s){let r=new ih,n=new aL(this.manager);return n.setCrossOrigin(this.crossOrigin),n.setPath(this.path),n.load(t,function(t){r.image=t,r.needsUpdate=!0,void 0!==e&&e(r)},i,s),r}}class aj extends sn{constructor(t,e=1){super(),this.isLight=!0,this.type="Light",this.color=new s_(t),this.intensity=e}dispose(){}copy(t,e){return super.copy(t,e),this.color.copy(t.color),this.intensity=t.intensity,this}toJSON(t){let e=super.toJSON(t);return e.object.color=this.color.getHex(),e.object.intensity=this.intensity,void 0!==this.groundColor&&(e.object.groundColor=this.groundColor.getHex()),void 0!==this.distance&&(e.object.distance=this.distance),void 0!==this.angle&&(e.object.angle=this.angle),void 0!==this.decay&&(e.object.decay=this.decay),void 0!==this.penumbra&&(e.object.penumbra=this.penumbra),void 0!==this.shadow&&(e.object.shadow=this.shadow.toJSON()),void 0!==this.target&&(e.object.target=this.target.uuid),e}}class aD extends aj{constructor(t,e,i){super(t,i),this.isHemisphereLight=!0,this.type="HemisphereLight",this.position.copy(sn.DEFAULT_UP),this.updateMatrix(),this.groundColor=new s_(e)}copy(t,e){return super.copy(t,e),this.groundColor.copy(t.groundColor),this}}let aW=new iW,aU=new eZ,aJ=new eZ;class aq{constructor(t){this.camera=t,this.intensity=1,this.bias=0,this.normalBias=0,this.radius=1,this.blurSamples=8,this.mapSize=new eX(512,512),this.mapType=tv,this.map=null,this.mapPass=null,this.matrix=new iW,this.autoUpdate=!0,this.needsUpdate=!1,this._frustum=new rq,this._frameExtents=new eX(1,1),this._viewportCount=1,this._viewports=[new il(0,0,1,1)]}getViewportCount(){return this._viewportCount}getFrustum(){return this._frustum}updateMatrices(t){let e=this.camera,i=this.matrix;aU.setFromMatrixPosition(t.matrixWorld),e.position.copy(aU),aJ.setFromMatrixPosition(t.target.matrixWorld),e.lookAt(aJ),e.updateMatrixWorld(),aW.multiplyMatrices(e.projectionMatrix,e.matrixWorldInverse),this._frustum.setFromProjectionMatrix(aW,e.coordinateSystem,e.reversedDepth),e.reversedDepth?i.set(.5,0,0,.5,0,.5,0,.5,0,0,1,0,0,0,0,1):i.set(.5,0,0,.5,0,.5,0,.5,0,0,.5,.5,0,0,0,1),i.multiply(aW)}getViewport(t){return this._viewports[t]}getFrameExtents(){return this._frameExtents}dispose(){this.map&&this.map.dispose(),this.mapPass&&this.mapPass.dispose()}copy(t){return this.camera=t.camera.clone(),this.intensity=t.intensity,this.bias=t.bias,this.radius=t.radius,this.autoUpdate=t.autoUpdate,this.needsUpdate=t.needsUpdate,this.normalBias=t.normalBias,this.blurSamples=t.blurSamples,this.mapSize.copy(t.mapSize),this}clone(){return new this.constructor().copy(this)}toJSON(){let t={};return 1!==this.intensity&&(t.intensity=this.intensity),0!==this.bias&&(t.bias=this.bias),0!==this.normalBias&&(t.normalBias=this.normalBias),1!==this.radius&&(t.radius=this.radius),(512!==this.mapSize.x||512!==this.mapSize.y)&&(t.mapSize=this.mapSize.toArray()),t.camera=this.camera.toJSON(!1).object,delete t.camera.matrix,t}}class aH extends aq{constructor(){super(new rs(50,1,.5,500)),this.isSpotLightShadow=!0,this.focus=1,this.aspect=1}updateMatrices(t){let e=this.camera,i=2*eF*t.angle*this.focus,s=this.mapSize.width/this.mapSize.height*this.aspect,r=t.distance||e.far;(i!==e.fov||s!==e.aspect||r!==e.far)&&(e.fov=i,e.aspect=s,e.far=r,e.updateProjectionMatrix()),super.updateMatrices(t)}copy(t){return super.copy(t),this.focus=t.focus,this}}class aX extends aj{constructor(t,e,i=0,s=Math.PI/3,r=0,n=2){super(t,e),this.isSpotLight=!0,this.type="SpotLight",this.position.copy(sn.DEFAULT_UP),this.updateMatrix(),this.target=new sn,this.distance=i,this.angle=s,this.penumbra=r,this.decay=n,this.map=null,this.shadow=new aH}get power(){return this.intensity*Math.PI}set power(t){this.intensity=t/Math.PI}dispose(){this.shadow.dispose()}copy(t,e){return super.copy(t,e),this.distance=t.distance,this.angle=t.angle,this.penumbra=t.penumbra,this.decay=t.decay,this.target=t.target.clone(),this.shadow=t.shadow.clone(),this}}let aY=new iW,aZ=new eZ,aG=new eZ;class a$ extends aq{constructor(){super(new rs(90,1,.5,500)),this.isPointLightShadow=!0,this._frameExtents=new eX(4,2),this._viewportCount=6,this._viewports=[new il(2,1,1,1),new il(0,1,1,1),new il(3,1,1,1),new il(1,1,1,1),new il(3,0,1,1),new il(1,0,1,1)],this._cubeDirections=[new eZ(1,0,0),new eZ(-1,0,0),new eZ(0,0,1),new eZ(0,0,-1),new eZ(0,1,0),new eZ(0,-1,0)],this._cubeUps=[new eZ(0,1,0),new eZ(0,1,0),new eZ(0,1,0),new eZ(0,1,0),new eZ(0,0,1),new eZ(0,0,-1)]}updateMatrices(t,e=0){let i=this.camera,s=this.matrix,r=t.distance||i.far;r!==i.far&&(i.far=r,i.updateProjectionMatrix()),aZ.setFromMatrixPosition(t.matrixWorld),i.position.copy(aZ),aG.copy(i.position),aG.add(this._cubeDirections[e]),i.up.copy(this._cubeUps[e]),i.lookAt(aG),i.updateMatrixWorld(),s.makeTranslation(-aZ.x,-aZ.y,-aZ.z),aY.multiplyMatrices(i.projectionMatrix,i.matrixWorldInverse),this._frustum.setFromProjectionMatrix(aY,i.coordinateSystem,i.reversedDepth)}}class aQ extends aj{constructor(t,e,i=0,s=2){super(t,e),this.isPointLight=!0,this.type="PointLight",this.distance=i,this.decay=s,this.shadow=new a$}get power(){return 4*this.intensity*Math.PI}set power(t){this.intensity=t/(4*Math.PI)}dispose(){this.shadow.dispose()}copy(t,e){return super.copy(t,e),this.distance=t.distance,this.decay=t.decay,this.shadow=t.shadow.clone(),this}}class aK extends s7{constructor(t=-1,e=1,i=1,s=-1,r=.1,n=2e3){super(),this.isOrthographicCamera=!0,this.type="OrthographicCamera",this.zoom=1,this.view=null,this.left=t,this.right=e,this.top=i,this.bottom=s,this.near=r,this.far=n,this.updateProjectionMatrix()}copy(t,e){return super.copy(t,e),this.left=t.left,this.right=t.right,this.top=t.top,this.bottom=t.bottom,this.near=t.near,this.far=t.far,this.zoom=t.zoom,this.view=null===t.view?null:Object.assign({},t.view),this}setViewOffset(t,e,i,s,r,n){null===this.view&&(this.view={enabled:!0,fullWidth:1,fullHeight:1,offsetX:0,offsetY:0,width:1,height:1}),this.view.enabled=!0,this.view.fullWidth=t,this.view.fullHeight=e,this.view.offsetX=i,this.view.offsetY=s,this.view.width=r,this.view.height=n,this.updateProjectionMatrix()}clearViewOffset(){null!==this.view&&(this.view.enabled=!1),this.updateProjectionMatrix()}updateProjectionMatrix(){let t=(this.right-this.left)/(2*this.zoom),e=(this.top-this.bottom)/(2*this.zoom),i=(this.right+this.left)/2,s=(this.top+this.bottom)/2,r=i-t,n=i+t,a=s+e,o=s-e;if(null!==this.view&&this.view.enabled){let t=(this.right-this.left)/this.view.fullWidth/this.zoom,e=(this.top-this.bottom)/this.view.fullHeight/this.zoom;r+=t*this.view.offsetX,n=r+t*this.view.width,a-=e*this.view.offsetY,o=a-e*this.view.height}this.projectionMatrix.makeOrthographic(r,n,a,o,this.near,this.far,this.coordinateSystem,this.reversedDepth),this.projectionMatrixInverse.copy(this.projectionMatrix).invert()}toJSON(t){let e=super.toJSON(t);return e.object.zoom=this.zoom,e.object.left=this.left,e.object.right=this.right,e.object.top=this.top,e.object.bottom=this.bottom,e.object.near=this.near,e.object.far=this.far,null!==this.view&&(e.object.view=Object.assign({},this.view)),e}}class a0 extends aq{constructor(){super(new aK(-5,5,5,-5,.5,500)),this.isDirectionalLightShadow=!0}}class a1 extends aj{constructor(t,e){super(t,e),this.isDirectionalLight=!0,this.type="DirectionalLight",this.position.copy(sn.DEFAULT_UP),this.updateMatrix(),this.target=new sn,this.shadow=new a0}dispose(){this.shadow.dispose()}copy(t){return super.copy(t),this.target=t.target.clone(),this.shadow=t.shadow.clone(),this}}class a2 extends aj{constructor(t,e){super(t,e),this.isAmbientLight=!0,this.type="AmbientLight"}}class a3{static extractUrlBase(t){let e=t.lastIndexOf("/");return -1===e?"./":t.slice(0,e+1)}static resolveURL(t,e){return"string"!=typeof t||""===t?"":(/^https?:\/\//i.test(e)&&/^\//.test(t)&&(e=e.replace(/(^https?:\/\/[^\/]+).*/i,"$1")),/^(https?:)?\/\//i.test(t)||/^data:.*,.*$/i.test(t)||/^blob:.*$/i.test(t))?t:e+t}}let a5=new WeakMap;class a4 extends aP{constructor(t){super(t),this.isImageBitmapLoader=!0,"undefined"==typeof createImageBitmap&&console.warn("THREE.ImageBitmapLoader: createImageBitmap() not supported."),"undefined"==typeof fetch&&console.warn("THREE.ImageBitmapLoader: fetch() not supported."),this.options={premultiplyAlpha:"none"},this._abortController=new AbortController}setOptions(t){return this.options=t,this}load(t,e,i,s){void 0===t&&(t=""),void 0!==this.path&&(t=this.path+t),t=this.manager.resolveURL(t);let r=this,n=aE.get(`image-bitmap:${t}`);if(void 0!==n)return(r.manager.itemStart(t),n.then)?void n.then(i=>{if(!0!==a5.has(n))return e&&e(i),r.manager.itemEnd(t),i;s&&s(a5.get(n)),r.manager.itemError(t),r.manager.itemEnd(t)}):(setTimeout(function(){e&&e(n),r.manager.itemEnd(t)},0),n);let a={};a.credentials="anonymous"===this.crossOrigin?"same-origin":"include",a.headers=this.requestHeader,a.signal="function"==typeof AbortSignal.any?AbortSignal.any([this._abortController.signal,this.manager.abortController.signal]):this._abortController.signal;let o=fetch(t,a).then(function(t){return t.blob()}).then(function(t){return createImageBitmap(t,Object.assign(r.options,{colorSpaceConversion:"none"}))}).then(function(i){return aE.add(`image-bitmap:${t}`,i),e&&e(i),r.manager.itemEnd(t),i}).catch(function(e){s&&s(e),a5.set(o,e),aE.remove(`image-bitmap:${t}`),r.manager.itemError(t),r.manager.itemEnd(t)});aE.add(`image-bitmap:${t}`,o),r.manager.itemStart(t)}abort(){return this._abortController.abort(),this._abortController=new AbortController,this}}class a6 extends rs{constructor(t=[]){super(),this.isArrayCamera=!0,this.isMultiViewCamera=!1,this.cameras=t}}let a8="\\[\\]\\.:\\/",a9=RegExp("["+a8+"]","g"),a7="[^"+a8+"]",ot="[^"+a8.replace("\\.","")+"]",oe=/((?:WC+[\/:])*)/.source.replace("WC",a7),oi=/(WCOD+)?/.source.replace("WCOD",ot),os=RegExp("^"+oe+oi+/(?:\.(WC+)(?:\[(.+)\])?)?/.source.replace("WC",a7)+/\.(WC+)(?:\[(.+)\])?/.source.replace("WC",a7)+"$"),or=["material","materials","bones","map"];class on{constructor(t,e,i){let s=i||oa.parseTrackName(e);this._targetGroup=t,this._bindings=t.subscribe_(e,s)}getValue(t,e){this.bind();let i=this._targetGroup.nCachedObjects_,s=this._bindings[i];void 0!==s&&s.getValue(t,e)}setValue(t,e){let i=this._bindings;for(let s=this._targetGroup.nCachedObjects_,r=i.length;s!==r;++s)i[s].setValue(t,e)}bind(){let t=this._bindings;for(let e=this._targetGroup.nCachedObjects_,i=t.length;e!==i;++e)t[e].bind()}unbind(){let t=this._bindings;for(let e=this._targetGroup.nCachedObjects_,i=t.length;e!==i;++e)t[e].unbind()}}class oa{constructor(t,e,i){this.path=e,this.parsedPath=i||oa.parseTrackName(e),this.node=oa.findNode(t,this.parsedPath.nodeName),this.rootNode=t,this.getValue=this._getValue_unbound,this.setValue=this._setValue_unbound}static create(t,e,i){return t&&t.isAnimationObjectGroup?new oa.Composite(t,e,i):new oa(t,e,i)}static sanitizeNodeName(t){return t.replace(/\s/g,"_").replace(a9,"")}static parseTrackName(t){let e=os.exec(t);if(null===e)throw Error("PropertyBinding: Cannot parse trackName: "+t);let i={nodeName:e[2],objectName:e[3],objectIndex:e[4],propertyName:e[5],propertyIndex:e[6]},s=i.nodeName&&i.nodeName.lastIndexOf(".");if(void 0!==s&&-1!==s){let t=i.nodeName.substring(s+1);-1!==or.indexOf(t)&&(i.nodeName=i.nodeName.substring(0,s),i.objectName=t)}if(null===i.propertyName||0===i.propertyName.length)throw Error("PropertyBinding: can not parse propertyName from trackName: "+t);return i}static findNode(t,e){if(void 0===e||""===e||"."===e||-1===e||e===t.name||e===t.uuid)return t;if(t.skeleton){let i=t.skeleton.getBoneByName(e);if(void 0!==i)return i}if(t.children){let i=function(t){for(let s=0;s<t.length;s++){let r=t[s];if(r.name===e||r.uuid===e)return r;let n=i(r.children);if(n)return n}return null},s=i(t.children);if(s)return s}return null}_getValue_unavailable(){}_setValue_unavailable(){}_getValue_direct(t,e){t[e]=this.targetObject[this.propertyName]}_getValue_array(t,e){let i=this.resolvedProperty;for(let s=0,r=i.length;s!==r;++s)t[e++]=i[s]}_getValue_arrayElement(t,e){t[e]=this.resolvedProperty[this.propertyIndex]}_getValue_toArray(t,e){this.resolvedProperty.toArray(t,e)}_setValue_direct(t,e){this.targetObject[this.propertyName]=t[e]}_setValue_direct_setNeedsUpdate(t,e){this.targetObject[this.propertyName]=t[e],this.targetObject.needsUpdate=!0}_setValue_direct_setMatrixWorldNeedsUpdate(t,e){this.targetObject[this.propertyName]=t[e],this.targetObject.matrixWorldNeedsUpdate=!0}_setValue_array(t,e){let i=this.resolvedProperty;for(let s=0,r=i.length;s!==r;++s)i[s]=t[e++]}_setValue_array_setNeedsUpdate(t,e){let i=this.resolvedProperty;for(let s=0,r=i.length;s!==r;++s)i[s]=t[e++];this.targetObject.needsUpdate=!0}_setValue_array_setMatrixWorldNeedsUpdate(t,e){let i=this.resolvedProperty;for(let s=0,r=i.length;s!==r;++s)i[s]=t[e++];this.targetObject.matrixWorldNeedsUpdate=!0}_setValue_arrayElement(t,e){this.resolvedProperty[this.propertyIndex]=t[e]}_setValue_arrayElement_setNeedsUpdate(t,e){this.resolvedProperty[this.propertyIndex]=t[e],this.targetObject.needsUpdate=!0}_setValue_arrayElement_setMatrixWorldNeedsUpdate(t,e){this.resolvedProperty[this.propertyIndex]=t[e],this.targetObject.matrixWorldNeedsUpdate=!0}_setValue_fromArray(t,e){this.resolvedProperty.fromArray(t,e)}_setValue_fromArray_setNeedsUpdate(t,e){this.resolvedProperty.fromArray(t,e),this.targetObject.needsUpdate=!0}_setValue_fromArray_setMatrixWorldNeedsUpdate(t,e){this.resolvedProperty.fromArray(t,e),this.targetObject.matrixWorldNeedsUpdate=!0}_getValue_unbound(t,e){this.bind(),this.getValue(t,e)}_setValue_unbound(t,e){this.bind(),this.setValue(t,e)}bind(){let t=this.node,e=this.parsedPath,i=e.objectName,s=e.propertyName,r=e.propertyIndex;if(t||(t=oa.findNode(this.rootNode,e.nodeName),this.node=t),this.getValue=this._getValue_unavailable,this.setValue=this._setValue_unavailable,!t)return void console.warn("THREE.PropertyBinding: No target node found for track: "+this.path+".");if(i){let s=e.objectIndex;switch(i){case"materials":if(!t.material)return void console.error("THREE.PropertyBinding: Can not bind to material as node does not have a material.",this);if(!t.material.materials)return void console.error("THREE.PropertyBinding: Can not bind to material.materials as node.material does not have a materials array.",this);t=t.material.materials;break;case"bones":if(!t.skeleton)return void console.error("THREE.PropertyBinding: Can not bind to bones as node does not have a skeleton.",this);t=t.skeleton.bones;for(let e=0;e<t.length;e++)if(t[e].name===s){s=e;break}break;case"map":if("map"in t){t=t.map;break}if(!t.material)return void console.error("THREE.PropertyBinding: Can not bind to material as node does not have a material.",this);if(!t.material.map)return void console.error("THREE.PropertyBinding: Can not bind to material.map as node.material does not have a map.",this);t=t.material.map;break;default:if(void 0===t[i])return void console.error("THREE.PropertyBinding: Can not bind to objectName of node undefined.",this);t=t[i]}if(void 0!==s){if(void 0===t[s])return void console.error("THREE.PropertyBinding: Trying to bind to objectIndex of objectName, but is undefined.",this,t);t=t[s]}}let n=t[s];if(void 0===n)return void console.error("THREE.PropertyBinding: Trying to update property for track: "+e.nodeName+"."+s+" but it wasn't found.",t);let a=this.Versioning.None;this.targetObject=t,!0===t.isMaterial?a=this.Versioning.NeedsUpdate:!0===t.isObject3D&&(a=this.Versioning.MatrixWorldNeedsUpdate);let o=this.BindingType.Direct;if(void 0!==r){if("morphTargetInfluences"===s){if(!t.geometry)return void console.error("THREE.PropertyBinding: Can not bind to morphTargetInfluences because node does not have a geometry.",this);if(!t.geometry.morphAttributes)return void console.error("THREE.PropertyBinding: Can not bind to morphTargetInfluences because node does not have a geometry.morphAttributes.",this);void 0!==t.morphTargetDictionary[r]&&(r=t.morphTargetDictionary[r])}o=this.BindingType.ArrayElement,this.resolvedProperty=n,this.propertyIndex=r}else void 0!==n.fromArray&&void 0!==n.toArray?(o=this.BindingType.HasFromToArray,this.resolvedProperty=n):Array.isArray(n)?(o=this.BindingType.EntireArray,this.resolvedProperty=n):this.propertyName=s;this.getValue=this.GetterByBindingType[o],this.setValue=this.SetterByBindingTypeAndVersioning[o][a]}unbind(){this.node=null,this.getValue=this._getValue_unbound,this.setValue=this._setValue_unbound}}oa.Composite=on,oa.prototype.BindingType={Direct:0,EntireArray:1,ArrayElement:2,HasFromToArray:3},oa.prototype.Versioning={None:0,NeedsUpdate:1,MatrixWorldNeedsUpdate:2},oa.prototype.GetterByBindingType=[oa.prototype._getValue_direct,oa.prototype._getValue_array,oa.prototype._getValue_arrayElement,oa.prototype._getValue_toArray],oa.prototype.SetterByBindingTypeAndVersioning=[[oa.prototype._setValue_direct,oa.prototype._setValue_direct_setNeedsUpdate,oa.prototype._setValue_direct_setMatrixWorldNeedsUpdate],[oa.prototype._setValue_array,oa.prototype._setValue_array_setNeedsUpdate,oa.prototype._setValue_array_setMatrixWorldNeedsUpdate],[oa.prototype._setValue_arrayElement,oa.prototype._setValue_arrayElement_setNeedsUpdate,oa.prototype._setValue_arrayElement_setMatrixWorldNeedsUpdate],[oa.prototype._setValue_fromArray,oa.prototype._setValue_fromArray_setNeedsUpdate,oa.prototype._setValue_fromArray_setMatrixWorldNeedsUpdate]],new Float32Array(1);let oo=new iW;class oh{constructor(t,e,i=0,s=1/0){this.ray=new iD(t,e),this.near=i,this.far=s,this.camera=null,this.layers=new iK,this.params={Mesh:{},Line:{threshold:1},LOD:{},Points:{threshold:1},Sprite:{}}}set(t,e){this.ray.set(t,e)}setFromCamera(t,e){e.isPerspectiveCamera?(this.ray.origin.setFromMatrixPosition(e.matrixWorld),this.ray.direction.set(t.x,t.y,.5).unproject(e).sub(this.ray.origin).normalize(),this.camera=e):e.isOrthographicCamera?(this.ray.origin.set(t.x,t.y,(e.near+e.far)/(e.near-e.far)).unproject(e),this.ray.direction.set(0,0,-1).transformDirection(e.matrixWorld),this.camera=e):console.error("THREE.Raycaster: Unsupported camera type: "+e.type)}setFromXRController(t){return oo.identity().extractRotation(t.matrixWorld),this.ray.origin.setFromMatrixPosition(t.matrixWorld),this.ray.direction.set(0,0,-1).applyMatrix4(oo),this}intersectObject(t,e=!0,i=[]){return ou(t,this,i,e),i.sort(ol),i}intersectObjects(t,e=!0,i=[]){for(let s=0,r=t.length;s<r;s++)ou(t[s],this,i,e);return i.sort(ol),i}}function ol(t,e){return t.distance-e.distance}function ou(t,e,i,s){let r=!0;if(t.layers.test(e.layers)&&!1===t.raycast(e,i)&&(r=!1),!0===r&&!0===s){let s=t.children;for(let t=0,r=s.length;t<r;t++)ou(s[t],e,i,!0)}}class oc{constructor(t=1,e=0,i=0){this.radius=t,this.phi=e,this.theta=i}set(t,e,i){return this.radius=t,this.phi=e,this.theta=i,this}copy(t){return this.radius=t.radius,this.phi=t.phi,this.theta=t.theta,this}makeSafe(){return this.phi=eD(this.phi,1e-6,Math.PI-1e-6),this}setFromVector3(t){return this.setFromCartesianCoords(t.x,t.y,t.z)}setFromCartesianCoords(t,e,i){return this.radius=Math.sqrt(t*t+e*e+i*i),0===this.radius?(this.theta=0,this.phi=0):(this.theta=Math.atan2(t,i),this.phi=Math.acos(eD(e/this.radius,-1,1))),this}clone(){return new this.constructor().copy(this)}}class op extends eN{constructor(t,e=null){super(),this.object=t,this.domElement=e,this.enabled=!0,this.state=-1,this.keys={},this.mouseButtons={LEFT:null,MIDDLE:null,RIGHT:null},this.touches={ONE:null,TWO:null}}connect(t){if(void 0===t)return void console.warn("THREE.Controls: connect() now requires an element.");null!==this.domElement&&this.disconnect(),this.domElement=t}disconnect(){}dispose(){}update(){}}function od(t,e,i,s){let r=function(t){switch(t){case tv:case tS:return{byteLength:1,components:1};case tz:case t_:case tk:return{byteLength:2,components:1};case tE:case tO:return{byteLength:2,components:4};case tT:case tA:case tC:return{byteLength:4,components:1};case tP:return{byteLength:4,components:3}}throw Error(`Unknown texture type ${t}.`)}(s);switch(i){case tI:return t*e;case tF:case tj:return t*e/r.components*r.byteLength;case tD:case tW:return t*e*2/r.components*r.byteLength;case tN:return t*e*3/r.components*r.byteLength;case tR:case tU:return t*e*4/r.components*r.byteLength;case tJ:case tq:return Math.floor((t+3)/4)*Math.floor((e+3)/4)*8;case tH:case tX:return Math.floor((t+3)/4)*Math.floor((e+3)/4)*16;case tZ:case t$:return Math.max(t,16)*Math.max(e,8)/4;case tY:case tG:return Math.max(t,8)*Math.max(e,8)/2;case tQ:case tK:return Math.floor((t+3)/4)*Math.floor((e+3)/4)*8;case t0:case t1:return Math.floor((t+3)/4)*Math.floor((e+3)/4)*16;case t2:return Math.floor((t+4)/5)*Math.floor((e+3)/4)*16;case t3:return Math.floor((t+4)/5)*Math.floor((e+4)/5)*16;case t5:return Math.floor((t+5)/6)*Math.floor((e+4)/5)*16;case t4:return Math.floor((t+5)/6)*Math.floor((e+5)/6)*16;case t6:return Math.floor((t+7)/8)*Math.floor((e+4)/5)*16;case t8:return Math.floor((t+7)/8)*Math.floor((e+5)/6)*16;case t9:return Math.floor((t+7)/8)*Math.floor((e+7)/8)*16;case t7:return Math.floor((t+9)/10)*Math.floor((e+4)/5)*16;case et:return Math.floor((t+9)/10)*Math.floor((e+5)/6)*16;case ee:return Math.floor((t+9)/10)*Math.floor((e+7)/8)*16;case ei:return Math.floor((t+9)/10)*Math.floor((e+9)/10)*16;case es:return Math.floor((t+11)/12)*Math.floor((e+9)/10)*16;case er:return Math.floor((t+11)/12)*Math.floor((e+11)/12)*16;case en:case ea:case eo:return Math.ceil(t/4)*Math.ceil(e/4)*16;case eh:case el:return Math.ceil(t/4)*Math.ceil(e/4)*8;case eu:case ec:return Math.ceil(t/4)*Math.ceil(e/4)*16}throw Error(`Unable to determine texture byte length for ${i} format.`)}"undefined"!=typeof __THREE_DEVTOOLS__&&__THREE_DEVTOOLS__.dispatchEvent(new CustomEvent("register",{detail:{revision:r}})),"undefined"!=typeof window&&(window.__THREE__?console.warn("WARNING: Multiple instances of Three.js being imported."):window.__THREE__=r)}}]);