(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[974],{57:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>u});var a=t(5155),n=t(2115),i=t(3264),l=t(7431),o=t(9311),r=t(5416),c=t(8512);let d=(0,n.forwardRef)((e,s)=>{let t=(0,n.useRef)(null),d=(0,n.useRef)({}),p=(0,n.useRef)(),[_,g]=(0,n.useState)(!0),m=(0,n.useRef)(3),u=(0,n.useRef)(!1),h=(0,n.useRef)(_);h.current=_;let f=()=>{Object.keys(d.current).forEach(e=>{let s=d.current[e];u.current?s.visible="original"===e:s.visible=e==="finetune_".concat(m.current)})};return(0,n.useImperativeHandle)(s,()=>({toggleComparisonModel:e=>{u.current=e,f()},setFinetuneLevel:e=>{m.current=e,u.current||f()}})),(0,n.useEffect)(()=>{let e;if(!t.current)return;let s=t.current,a=new i.Z58,n=new i.ubm(50,s.clientWidth/s.clientHeight,.1,1e3);p.current=n;let _=new l.JeP({antialias:!0,alpha:!0});_.setSize(s.clientWidth,s.clientHeight),_.setPixelRatio(window.devicePixelRatio),_.toneMapping=i.FV,_.toneMappingExposure=1,_.outputColorSpace=i.er$,s.appendChild(_.domElement);let x=new c.B;x.setSize(s.clientWidth,s.clientHeight),x.domElement.style.position="absolute",x.domElement.style.top="0px",x.domElement.style.pointerEvents="none",s.appendChild(x.domElement);let j=new i.$p8(0xffffff,.5);a.add(j);let N=new i.ZyN(0xffffff,.8);N.position.set(1.5,3,2),a.add(N);let v=new i.ZyN(0xfff8ff,1);v.position.set(-2,2,1.5),a.add(v);let w=new i.ZyN(0xfffcf8,.8);w.position.set(2,1.5,1.5),a.add(w);let C=new i.ZyN(0xffffff,.6);C.position.set(0,1,3),a.add(C);let b=new i.ZyN(0xfafafa,.3);b.position.set(0,-1.5,1),a.add(b);let y=new i.dth(0xffffff,0xfff8f8,.5);a.add(y);let I=new r.N(n,_.domElement);I.enableDamping=!0,I.dampingFactor=.1,I.minAzimuthAngle=-Math.PI/9-Math.PI/9,I.maxAzimuthAngle=Math.PI/9-Math.PI/9,I.minPolarAngle=Math.PI/2-Math.PI/9,I.maxPolarAngle=Math.PI/2+Math.PI/9;let T=()=>{g(!1),I.removeEventListener("start",T)};I.addEventListener("start",T);let S=(e,s)=>{a.add(e),e.scale.set(1,1,1),e.position.set(0,0,0),e.rotation.set(0,-Math.PI/9,0),e.visible=!1,d.current[s]=e;let t=e.getObjectByName("mesh");return t&&[{label:"肤色",position:new i.Pq0(.04,.26,.5),pic:"images/positions/肤色.png",offset:"-2.4rem",height:"2rem"},{label:"黑眼圈",position:new i.Pq0(.15,-.05,.51),pic:"images/positions/黑眼圈.png",offset:"-5.6rem",height:"2.25rem"},{label:"太阳穴",position:new i.Pq0(.31,.12,.33),pic:"images/positions/太阳穴.png",offset:"3.6rem",height:"2.75rem"},{label:"色斑",position:new i.Pq0(.3,-.03,.41),pic:"images/positions/色斑.png",offset:"2.4rem",height:"2rem"},{label:"咬肌",position:new i.Pq0(.25,-.38,.46),pic:"images/positions/咬肌.png",offset:"2.4rem",height:"2rem"}].forEach(e=>{let s=document.createElement("img");s.src=e.pic,s.alt=e.label,s.style.pointerEvents="auto",s.style.height=e.height,s.style.width="auto",s.style.marginLeft=e.offset;let a=new c.v(s);a.position.copy(e.position),t.add(a)}),e},P=new o.B;P.loadAsync("https://s3plus.meituan.net/merchant-dxw-video/myj_3.glb").then(s=>{S(s.scene,"finetune_3"),d.current.finetune_3.visible=!0;let t=new i.NRn().setFromObject(d.current.finetune_3).getSize(new i.Pq0);e=1.05*Math.abs(Math.max(t.x,t.y,t.z)/2/Math.tan(n.fov*(Math.PI/180)/2)),n.position.set(0,.1*t.y,e),Object.entries({original:"https://s3plus.meituan.net/merchant-dxw-video/myj.glb",finetune_0:"https://s3plus.meituan.net/merchant-dxw-video/myj_0.glb",finetune_1:"https://s3plus.meituan.net/merchant-dxw-video/myj_1.glb",finetune_2:"https://s3plus.meituan.net/merchant-dxw-video/myj_2.glb"}).forEach(e=>{let[s,t]=e;P.loadAsync(t).then(e=>{S(e.scene,s),f()})})});let k=()=>{if(requestAnimationFrame(k),h.current&&e){let s=-Math.PI/9+Math.PI/9*Math.sin(5e-4*Date.now());n.position.x=e*Math.sin(s),n.position.z=e*Math.cos(s)}I.update(),_.render(a,n),x.render(a,n)};k();let B=new i.tBo,R=new i.I9Y,L=e=>{let t=s.getBoundingClientRect();R.x=(e.clientX-t.left)/t.width*2-1,R.y=-(2*((e.clientY-t.top)/t.height))+1,B.setFromCamera(R,n);let a=u.current?"original":"finetune_".concat(m.current),i=d.current[a];if(i){let e=i.getObjectByName("mesh");if(e){let s=B.intersectObject(e);if(s.length>0){let t=s[0].point,a=e.worldToLocal(t.clone());console.log("Clicked Local coordinates: ( ".concat(a.x.toFixed(2),", ").concat(a.y.toFixed(2),", ").concat(a.z.toFixed(2)," )"))}}}};_.domElement.addEventListener("dblclick",L);let D=()=>{s&&(n.aspect=s.clientWidth/s.clientHeight,n.updateProjectionMatrix(),_.setSize(s.clientWidth,s.clientHeight),x.setSize(s.clientWidth,s.clientHeight))};return window.addEventListener("resize",D),()=>{window.removeEventListener("resize",D),_.domElement.removeEventListener("dblclick",L),I.removeEventListener("start",T),s.removeChild(_.domElement),s.innerHTML=""}},[]),(0,a.jsx)("div",{ref:t,className:"w-full h-full"})});var p=t(8334),_=t.n(p);let g=e=>{var s;let{solution:t}=e;return(0,a.jsx)("div",{children:(0,a.jsxs)("div",{className:_().solutionContainer,children:[(0,a.jsx)("h4",{className:_().solutionName,children:t.name}),(0,a.jsxs)("div",{className:_().solutionDetails,children:[(0,a.jsx)("span",{className:_().solutionPrice,children:t.price}),(0,a.jsx)("span",{className:_().solutionInfoSeparator,children:"|"}),(0,a.jsx)("span",{className:_().solutionInfo,children:t.unit}),(0,a.jsx)("span",{className:_().solutionInfoSeparator,children:"|"}),(0,a.jsx)("span",{className:_().solutionInfo,children:t.duration})]}),null==(s=t.shop)?void 0:s.slice(0,2).map((e,s)=>(0,a.jsxs)("div",{className:_().shopCard,onClick:()=>window.location.href=e.jumperUrl,children:[(0,a.jsxs)("div",{className:_().shopTopRow,children:[(0,a.jsx)("img",{src:e.header,alt:e.shopName,className:_().shopHeader}),(0,a.jsxs)("div",{className:_().shopDetails,children:[(0,a.jsx)("div",{className:_().shopName,children:e.shopName}),(0,a.jsxs)("div",{className:_().shopRow2,children:[(0,a.jsxs)("div",{className:_().shopRow2Left,children:[(0,a.jsx)("img",{src:"images/result_star.png",alt:"星级",className:_().shopIcon}),(0,a.jsx)("span",{className:_().shopScore,children:e.reviewScore}),(0,a.jsx)("span",{className:_().shopCommentCount,children:e.commentCount})]}),(0,a.jsxs)("div",{className:_().shopRow2Right,children:[(0,a.jsx)("img",{src:"images/result_position.png",alt:"地理位置",className:_().shopIcon}),(0,a.jsx)("span",{className:_().shopArea,children:e.area}),(0,a.jsx)("span",{className:_().shopDistance,children:e.distance})]})]}),(0,a.jsx)("div",{className:_().shopTags,children:e.tags.slice(0,2).map((e,s)=>e.imageUrl?(0,a.jsx)("img",{src:e.imageUrl,alt:e.text,className:_().shopTagImage},s):(0,a.jsx)("span",{className:_().shopTag,children:e.text},s))})]})]}),(0,a.jsx)("div",{className:_().shopDeals,children:e.deals.map((e,s)=>(0,a.jsxs)("div",{className:_().dealItem,onClick:s=>{s.stopPropagation(),window.location.href=e.jumperUrl},children:[(0,a.jsx)("span",{className:_().dealPrice,children:e.dealPrice}),(0,a.jsx)("span",{className:_().dealName,children:e.dealName}),(0,a.jsx)("span",{className:_().dealSales,children:e.sales})]},s))})]},s))]})})},m=e=>{let s,{result:t,index:n,degreeClass:i,onRef:l,finetuneLevel:o}=e;return void 0!==o?void 0===(s=t["effect".concat(o)])&&(s=t.effect):s=t.effect,(0,a.jsxs)("div",{ref:l,className:_().resultItemContainer,children:[(0,a.jsxs)("div",{className:_().resultHeader,children:[(0,a.jsxs)("div",{className:_().resultTitleContainer,children:[(0,a.jsx)("span",{className:_().resultIndex,children:String(n+1).padStart(2,"0")}),(0,a.jsx)("span",{className:_().resultTitle,children:t.name})]}),(0,a.jsxs)("div",{className:_().resultDegreeContainer,children:[(0,a.jsxs)("span",{className:"".concat(_().resultDegree," ").concat(_()[i[t.degree]]),children:["● ",t.degree]}),(0,a.jsx)("span",{className:_().degreeSeparator,children:" | "}),(0,a.jsx)("span",{className:_().resultDegreeText,children:t.degreeDesc})]})]}),(0,a.jsx)("div",{className:_().suggestionContainer,children:(0,a.jsxs)("p",{className:_().suggestionText,children:[t.suggestion,(0,a.jsx)("br",{}),(e=>e?"string"==typeof e?e:(0,a.jsx)(a.Fragment,{children:e.map((e,s)=>(0,a.jsx)("span",{style:e.style,className:_().effectTag,children:e.text},s))}):null)(s)]})}),(0,a.jsx)("div",{children:t.solutions.map((e,s)=>(0,a.jsx)(g,{solution:e},s))})]})};function u(){let[e,s]=(0,n.useState)(0),[t,i]=(0,n.useState)(!0),[l,o]=(0,n.useState)([]),r=(0,n.useRef)(null),c=(0,n.useRef)([]),[p,g]=(0,n.useState)(!1),[u,h]=(0,n.useState)([3,3,3]),f=(0,n.useRef)(null),[x,j]=(0,n.useState)(void 0),[N,v]=(0,n.useState)(!1),[w,C]=(0,n.useState)(!1),[b,y]=(0,n.useState)(""),I=(0,n.useRef)(null),T=(e,s)=>{let a=[...u],n=parseInt(s,10);if(a[e]=n,h(a),0===e){var l,o;t||(i(!0),null==(o=r.current)||o.toggleComparisonModel(!1)),null==(l=r.current)||l.setFinetuneLevel(n)}},S=(e,s)=>{I.current&&clearTimeout(I.current),y(e),C(!0),I.current=setTimeout(()=>{C(!1)},s)},P=e=>{let s=c.current[e];if(s){let e=s.getBoundingClientRect().top+window.pageYOffset-60;window.scrollTo({top:e,behavior:"smooth"})}};(0,n.useEffect)(()=>{fetch("results.json").then(e=>e.json()).then(e=>o(e));let e=()=>{v(window.scrollY>10)};return window.addEventListener("scroll",e),S("体验版受算力影响暂时无法实时生成模型\n请先体验本组成员的真实模型吧～",3e3),()=>{window.removeEventListener("scroll",e)}},[]),(0,n.useEffect)(()=>{p&&f.current&&j(f.current.offsetTop)},[p]);let k={轻度:"degreeLight",中度:"degreeMedium",重度:"degreeSevere"};return(0,a.jsxs)("main",{className:_().mainContainer,"data-oid":"q-tcxdb",children:[(0,a.jsx)("img",{src:"images/result_bg2.png",alt:"背景",className:_().backgroundImage2}),(0,a.jsx)("img",{src:"images/result_bg.png",alt:"背景",className:_().backgroundImage}),(0,a.jsxs)("div",{className:"".concat(_().topBar," ").concat(N?_().scrolled:""),"data-oid":"xxt464q",children:[(0,a.jsx)("button",{className:_().backButton,"data-oid":"-m0e10:",onClick:()=>window.location.href="detail-customization.html",children:(0,a.jsx)("img",{src:"images/back_light.png",alt:"返回",className:_().backButtonImage})}),(0,a.jsx)("h1",{className:_().title,"data-oid":"f4jnu-.",children:"美学诊断"}),(0,a.jsx)("div",{className:_().historyLink,"data-oid":"dzgfofe",children:"历史报告"})]}),(0,a.jsxs)("div",{className:_().sceneContainer,"data-oid":"1y44pvp",children:[(0,a.jsx)("div",{className:_().sceneWrapper,"data-oid":"27rz397",children:(0,a.jsx)(d,{ref:r})}),w&&(0,a.jsx)("div",{className:_().toastMessage,children:b.split("\n").map((e,s)=>(0,a.jsxs)(n.Fragment,{children:[e,(0,a.jsx)("br",{})]},s))})]}),(0,a.jsxs)("div",{className:_().controlsContainer,children:[(0,a.jsx)("button",{className:_().finetuneButton,onClick:()=>{g(e=>!e)},children:(0,a.jsx)("img",{src:p?"images/result_finetune_active.png":"images/result_finetune.png",alt:"效果微调"})}),(0,a.jsx)("button",{className:_().toggleButton,onClick:()=>{var e;let s=!t;i(s),null==(e=r.current)||e.toggleComparisonModel(!s)},"data-oid":"toggle-button",children:(0,a.jsx)("img",{src:t?"images/result_diff_active.png":"images/result_diff.png",alt:"效果对比"})})]}),(0,a.jsx)("div",{ref:f,className:_().analysisResults,"data-oid":"g998.5q",children:(0,a.jsxs)("div",{className:_().contentLayer,children:[(0,a.jsxs)("div",{className:_().titleContainer,children:[(0,a.jsx)("h2",{className:_().sectionTitle,"data-oid":"7-4ha.a",children:"面部特征分析"}),(0,a.jsx)("img",{src:"images/result_title.png",alt:"面部特征分析",className:_().titleBg1})]}),(0,a.jsxs)("div",{className:_().analysisContentBox,"data-oid":"zjjvn53",children:[(0,a.jsxs)("div",{"data-oid":"c:k4:aj",children:[(0,a.jsx)("h3",{className:_().subSectionTitle,"data-oid":"h-6y.51",children:"这些无需改善"}),(0,a.jsxs)("p",{className:_().descriptionText,"data-oid":"4742fb6",children:["您的三庭五眼分布为黄金比例，五官很有辨识度，尤其是鼻子非常挺拔，给整个面部增添了立体感",(0,a.jsx)("br",{}),"眼睛是气质大方的杏眼，眼距适中，十分耐看",(0,a.jsx)("br",{}),"面部轮廓很柔和，是精致灵巧的江南小家碧玉脸，只需轻微调整流畅度，无需过度改善"]})]}),(0,a.jsx)("div",{className:_().divider}),(0,a.jsxs)("div",{"data-oid":"fpcayym",children:[(0,a.jsx)("h3",{className:_().subSectionTitle2,"data-oid":"pm5igv0",children:"这些可以关注"}),(0,a.jsx)("div",{className:_().issuesContainer,"data-oid":"a5_9as7",children:l.map((e,s)=>(0,a.jsx)("div",{className:_().issueItem,children:(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{style:{display:"flex",alignItems:"center"},children:[(0,a.jsx)("h4",{className:_().issueName,children:e.name}),(0,a.jsx)("button",{onClick:()=>P(s),className:_().jumpButton,children:(0,a.jsx)("img",{src:"images/result_jump.png",alt:"跳转至方案",className:_().jumpButtonImage})})]}),(0,a.jsxs)("p",{className:_().issueDegree,children:[(0,a.jsxs)("span",{className:_()[k[e.degree]],children:["● ",e.degree]}),(0,a.jsxs)("span",{className:_().issueDegreeText,children:[" | ",e.degreeDesc]})]})]})},s))})]})]}),(0,a.jsxs)("div",{className:_().titleContainer,children:[(0,a.jsx)("h2",{className:_().sectionTitle,"data-oid":"vwph0p:",children:"定制变美方案"}),(0,a.jsx)("img",{src:"images/result_title.png",alt:"定制变美方案",className:_().titleBg2})]}),l.map((e,s)=>(0,a.jsx)(m,{result:e,index:s,degreeClass:k,onRef:e=>{c.current[s]=e},finetuneLevel:s<=3?u[s]:void 0},s))]})}),p&&(0,a.jsxs)("div",{className:_().finetunePanel,style:{top:x?"".concat(x,"px"):"auto"},children:[(0,a.jsx)("h2",{className:_().finetuneTitle,children:"效果微调"}),(0,a.jsx)("div",{className:_().finetuneContent,children:(0,a.jsxs)("div",{className:_().finetuneGrid,children:[["瘦咬肌","填充太阳穴","提亮肤色"].map((e,s)=>{let t=u[s]/3*100;return(0,a.jsxs)(n.Fragment,{children:[(0,a.jsx)("div",{className:_().finetuneLabel,children:e}),(0,a.jsx)("div",{className:_().finetuneControl,onClick:()=>{s>0&&S("体验版暂时不支持调整哦～\n请先尝试调整“瘦下颌角”",2e3)},children:(0,a.jsxs)("div",{className:_().sliderContainer,children:[(0,a.jsx)("div",{className:_().sliderTrack}),(0,a.jsx)("div",{className:_().sliderProgress,style:{width:"".concat(t,"%")}}),(0,a.jsx)("input",{type:"range",min:"0",max:"3",step:"1",value:u[s],className:_().slider,onChange:e=>T(s,e.target.value),disabled:s>0}),(0,a.jsx)("div",{className:_().sliderNodes,children:[void 0,void 0,void 0,void 0].map((e,t)=>(0,a.jsx)("div",{className:"".concat(_().sliderNode," ").concat(u[s]>=t?_().activeNode:"")},t))})]})})]},e)}),["祛黑眼圈","祛色斑"].map(e=>(0,a.jsxs)(n.Fragment,{children:[(0,a.jsx)("div",{className:_().finetuneLabel,children:e}),(0,a.jsx)("div",{className:_().finetuneControl,children:(0,a.jsx)("div",{className:"".concat(_().switchContainer," ").concat(_().switchOn," ").concat(_().switchDisabled),onClick:()=>S("体验版暂时不支持调整哦～\n请先尝试调整“瘦下颌角”",2e3),children:(0,a.jsx)("div",{className:_().switchCircle})})})]},e))]})})]})]})}},4276:(e,s,t)=>{Promise.resolve().then(t.bind(t,57))},8334:e=>{e.exports={mainContainer:"page_mainContainer__uvqiI",backgroundImage:"page_backgroundImage__0ASkt",backgroundImage2:"page_backgroundImage2__LccNC",topBar:"page_topBar__N6Ucl",scrolled:"page_scrolled__GAObV",sceneContainer:"page_sceneContainer__FnRrX",sceneWrapper:"page_sceneWrapper__N_nJ4",analysisResults:"page_analysisResults__EEy4I",treatmentRecommendations:"page_treatmentRecommendations__9Tc41",backButton:"page_backButton__6QP6j",backButtonImage:"page_backButtonImage__nebiB",title:"page_title__3jonF",historyLink:"page_historyLink__jSuF3",titleContainer:"page_titleContainer__LkrOz",sectionTitle:"page_sectionTitle__hzsAh",titleBg1:"page_titleBg1__OTRoh",titleBg2:"page_titleBg2__AMMF0",effectTag:"page_effectTag__2GLuN",analysisContentBox:"page_analysisContentBox__JiSuS",subSectionTitle:"page_subSectionTitle__oZaLy",subSectionTitle2:"page_subSectionTitle2__nKb6M",descriptionText:"page_descriptionText__J1wVn",issuesContainer:"page_issuesContainer__OyP8y",issueItem:"page_issueItem__5_gwc",issueName:"page_issueName__rzwcK",issueDegree:"page_issueDegree__6fNHi",issueDegreeText:"page_issueDegreeText__RgzL5",jumpButton:"page_jumpButton__L_zoJ",jumpButtonImage:"page_jumpButtonImage__WAhpN",tabNavContainer:"page_tabNavContainer__otTTD",toggleButton:"page_toggleButton__8cAdz",finetuneButton:"page_finetuneButton__rGTj_",controlsContainer:"page_controlsContainer__ygMcm",contentLayer:"page_contentLayer__oNM0L",divider:"page_divider__Ku6aS",degreeLight:"page_degreeLight__uNhCj",degreeMedium:"page_degreeMedium__NKxnw",degreeSevere:"page_degreeSevere__DH5s_",resultItemContainer:"page_resultItemContainer__Po9Gc",resultHeader:"page_resultHeader__A22DF",resultTitleContainer:"page_resultTitleContainer__GqJCi",resultIndex:"page_resultIndex__Y7EFY",resultTitle:"page_resultTitle__nINtY",resultDegreeContainer:"page_resultDegreeContainer__usyoT",resultDegree:"page_resultDegree__j1h1t",resultDegreeText:"page_resultDegreeText__yXCDd",degreeSeparator:"page_degreeSeparator__1v24V",suggestionContainer:"page_suggestionContainer__doYp8",suggestionText:"page_suggestionText__7Nwgd",solutionContainer:"page_solutionContainer__Ztx2Z",solutionName:"page_solutionName__7UOtW",solutionDetails:"page_solutionDetails__vyNJW",solutionPrice:"page_solutionPrice__ncvtG",solutionInfoSeparator:"page_solutionInfoSeparator__8tiwY",solutionInfo:"page_solutionInfo__7VkHE",shopCard:"page_shopCard__Ybj7P",shopTopRow:"page_shopTopRow__GF1ev",shopHeader:"page_shopHeader__0mnJk",shopDetails:"page_shopDetails__IkIne",shopName:"page_shopName__vosWe",shopRow2:"page_shopRow2__cxtKO",shopRow2Left:"page_shopRow2Left__y_00o",shopRow2Right:"page_shopRow2Right__XzfIt",shopIcon:"page_shopIcon__kvHde",shopScore:"page_shopScore__papvH",shopCommentCount:"page_shopCommentCount__KEV_3",shopArea:"page_shopArea__e7TNR",shopDistance:"page_shopDistance__zsWXw",shopTags:"page_shopTags__wL6BG",shopTag:"page_shopTag__DnpmE",shopTagImage:"page_shopTagImage__BCdAH",shopDeals:"page_shopDeals__87D6Z",dealItem:"page_dealItem___pAZg",dealPrice:"page_dealPrice__C3VIt",dealName:"page_dealName__0bYEt",dealSales:"page_dealSales__AJ95p",finetunePanel:"page_finetunePanel__t6P73",finetuneTitle:"page_finetuneTitle__RoPKb",finetuneContent:"page_finetuneContent__4J9v_",finetuneGrid:"page_finetuneGrid__5rKhi",finetuneLabel:"page_finetuneLabel__uqZ0y",finetuneControl:"page_finetuneControl__GGw4z",sliderContainer:"page_sliderContainer__9hu_f",sliderTrack:"page_sliderTrack__Wi4fk",sliderProgress:"page_sliderProgress__KVQj8",slider:"page_slider__ByN4U",sliderNodes:"page_sliderNodes__fpLuX",sliderNode:"page_sliderNode__k0YAf",activeNode:"page_activeNode__GQlBp",switchContainer:"page_switchContainer__BKv67",switchOn:"page_switchOn__RjypO",switchDisabled:"page_switchDisabled__Hn_nU",switchCircle:"page_switchCircle__xOo3h",toastMessage:"page_toastMessage__353pO"}}},e=>{var s=s=>e(e.s=s);e.O(0,[397,367,831,699,441,684,358],()=>s(4276)),_N_E=e.O()}]);