* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html, body {
    height: 100%;
    width: 100%;
    overflow: hidden;
    position: fixed;
    touch-action: none;
    background-image: url("images/result_bg.png");
}

.app-container {
    width: 100%;
    height: 100%;
    position: relative;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    max-width: 100vw;
}

.content {
    flex: 1;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
    width: 100%;
    padding: 1rem;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 60px;
    width: 100%;
    padding: 0 16px;
    position: relative;
}

.back-button {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: #000;
    cursor: pointer;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    z-index: 5;
}

.back-button-image {
    width: 100%;
    height: 100%;
}

.title {
    flex: 1;
    text-align: center;
    font-size: 18px;
    font-weight: 500;
}

.history-link {
    font-size: 14px;
    color: #6B7280;
    cursor: pointer;
}

.description {
    font-size: 16px;
    line-height: 1.5;
    color: #333;
    margin-bottom: 24px;
}

.form-container {
    display: flex;
    flex-direction: column;
    gap: 24px;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.form-label {
    font-size: 16px;
    font-weight: 500;
    color: #333;
}

.option-group {
    display: flex;
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
}

.option-item {
    background-color: #fff;
    border-radius: 12px;
    padding: 12px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: pointer;
    transition: all 0.2s ease;
    border: 1px solid #FFFFFF;
}

.option-item:active {
    background-color: #f5f5f5;
}

/* 选中状态样式 */
.option-item.selected {
    background-color: #E5E3FC;
    border: 1px solid #6D45E5;
}

.option-item.selected .option-content,
.option-item.selected .edit-icon {
    color: #6D45E5;
}

.option-content {
    font-size: 15px;
    color: #333;
    line-height: 1.4;
    word-break: break-word;
}

/* 自定义输入选项样式 */
#customInputItem.selected .add-icon {
    display: none;
}

.add-icon {
    color: #6a7bff;
    display: flex;
    align-items: center;
    justify-content: center;
}

.edit-icon {
    color: #6a7bff;
    display: none;
    align-items: center;
    justify-content: center;
    margin-right: 8px;
}

#customInputItem.selected .edit-icon {
    display: flex;
}

.custom-input {
    flex: 1;
    border: none;
    outline: none;
    font-size: 15px;
    color: #333;
    background: transparent;
    padding: 0;
    margin-left: 8px;
}

.voice-input-btn {
    background: none;
    border: none;
    color: #6a7bff;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0;
    margin-left: 8px;
    width: 36px;
    height: 36px;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.2s ease;
}

.voice-input-btn:active {
    background-color: rgba(106, 123, 255, 0.1);
}

.voice-input-btn.recording {
    background-color: rgba(255, 59, 48, 0.1);
    color: #ff3b30;
    animation: pulse 1.5s infinite;
}

.add-demand-btn {
    background-color: #6a7bff;
    color: #fff;
    border: none;
    border-radius: 16px;
    padding: 6px 12px;
    font-size: 14px;
    font-weight: 500;
    margin-left: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.add-demand-btn:active {
    background-color: #5a6be0;
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(255, 59, 48, 0.4);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(255, 59, 48, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(255, 59, 48, 0);
    }
}

.bottom-area {
    width: 100%;
    padding: 1rem 1rem calc(1rem + env(safe-area-inset-bottom, 0));
    background-color: #FFFFFF;
    border-radius: 20px 20px 0 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 16px;
    box-shadow: 0 -1px 10px rgba(0, 0, 0, 0.05);
}

.submit-button {
    width: 100%;
    height: 50px;
    background-color: #111111;
    color: #fff;
    border: none;
    border-radius: 25px;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
}
