* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}
body, html {
    margin: 0;
    padding: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(180deg, rgba(0, 0, 0, 0.8) 27%, #000010 44%);
    color: white;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
}

.loading-icon {
    width: 25%;
    max-width: 200px;
    height: auto;
}

.progress-text {
    font-size: 2.5rem; /* 40px */
    line-height: 1.5;
    font-weight: bold;
    margin-top: 1.5rem;
}

.status-text {
    font-size: 1.3rem; /* 24px */
    line-height: 1.5;
    margin-top: 0.5rem;
}

.eta-text {
    font-size: 1rem; /* 16px */
    line-height: 1.5;
    margin-top: 0.5rem;
}

.header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 60px;
    width: 100%;
    padding: 0 16px;
    position: absolute;
    top: 0;
    left: 0;
}

.back-button {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: #000;
    cursor: pointer;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    z-index: 5;
}

.back-button-image {
    width: 100%;
    height: 100%;
}

