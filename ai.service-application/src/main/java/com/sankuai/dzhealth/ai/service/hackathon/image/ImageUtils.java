package com.sankuai.dzhealth.ai.service.hackathon.image;

import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import com.meituan.image.client.pojo.ImageResult;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;

/**
 * @author:chenwei
 * @time: 2025/3/13 14:56
 * @version: 0.0.1
 */
public class ImageUtils {

    private static final ImageUtils instance = new ImageUtils();

    private static volatile boolean init = false;

    private ImageUploadClientWrapper sender;

    public static ImageResult postHttpImage(String url, String srcFileName) {
        return ImageUtils.getSender().postHttpImage(url, srcFileName);
    }

    public static ImageResult postHttpImage(byte[] bytes, String srcFileName) {
        return ImageUtils.getSender().postHttpImage(bytes, srcFileName);
    }

    private static ImageUploadClientWrapper getSender() {
        if (!init) {
            synchronized (instance) {
                if (!init) {
                    String bucket = instance.getBucket();
                    String clientId = instance.getClientId();
                    String clientSecret = instance.getClientSecret();
                    instance.sender = new ImageUploadClientWrapper(bucket, clientId, clientSecret);
                    init = true;
                }
            }
        }
        return instance.sender;
    }

    public static byte[] convertPngToJpgWithWhiteBackground(byte[] pngImageData) throws IOException {
        // Read the PNG image data from byte array
        ByteArrayInputStream bais = new ByteArrayInputStream(pngImageData);
        BufferedImage originalImage = ImageIO.read(bais);

        if (originalImage == null) {
            throw new IOException("Could not read image from byte array. It might not be a valid image format.");
        }

        // Create a new RGB image with the same dimensions
        BufferedImage newImage = new BufferedImage(
                originalImage.getWidth(),
                originalImage.getHeight(),
                BufferedImage.TYPE_INT_RGB);

        // Create a graphics context and fill it with white
        Graphics2D g2d = newImage.createGraphics();
        g2d.setColor(Color.BLACK);
        g2d.fillRect(0, 0, newImage.getWidth(), newImage.getHeight());

        // Draw the original image (with transparency) over the white background
        g2d.drawImage(originalImage, 0, 0, null);
        g2d.dispose();

        // Write the new image to a byte array as a JPG
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        ImageIO.write(newImage, "jpg", baos);

        return baos.toByteArray();
    }

    private String getBucket() {
        return Lion.getString(Environment.getAppName(), "health.ai.image.bucket", "healthai");
    }

    private String getClientId() {
        return Lion.getString(Environment.getAppName(), "health.ai.image.clientid", "8k5zt6tqzmrl8njk0000000000694ba4");
    }

    private String getClientSecret() {
        return Lion.getString(Environment.getAppName(), "health.ai.client.secret", "jn5dfqbhnvfpwcrw2r9jgwjvhqjgzxrm");
    }

}
