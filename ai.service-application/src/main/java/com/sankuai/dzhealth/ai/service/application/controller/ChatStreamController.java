package com.sankuai.dzhealth.ai.service.application.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.dianping.account.UserAccountService;
import com.dianping.account.dto.VirtualBindUserInfoDTO;
import com.dianping.cat.Cat;
import com.dianping.rhino.Rhino;
import com.dianping.rhino.threadpool.DefaultThreadPoolProperties;
import com.dianping.rhino.threadpool.ThreadPool;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.meituan.beauty.fundamental.light.plat.Platform;
import com.meituan.mdp.boot.starter.pigeon.annotation.MdpPigeonClient;
import com.meituan.mtrace.Tracer;
import com.sankuai.dzhealth.ai.service.domain.evaluation.EvaluationService;
import com.sankuai.dzhealth.ai.service.domain.evaluation.entity.RagEvaluationRequest;
import com.sankuai.dzhealth.ai.service.domain.finetune.FineTuneService;
import com.sankuai.dzhealth.ai.service.domain.service.ChatSessionService;
import com.sankuai.dzhealth.ai.service.domain.utils.JsonUtils;
import com.sankuai.dzhealth.ai.service.domain.utils.context.AiAnswerContext;
import com.sankuai.dzhealth.ai.service.domain.utils.context.RequestContext;
import com.sankuai.dzhealth.ai.service.domain.utils.context.RequestContextConstant;
import com.sankuai.dzhealth.ai.service.dto.SseAwareException;
import com.sankuai.dzhealth.ai.service.dto.stream.StreamEventCardDataDTO;
import com.sankuai.dzhealth.ai.service.dto.stream.StreamEventDTO;
import com.sankuai.dzhealth.ai.service.dto.stream.StreamEventDataDTO;
import com.sankuai.dzhealth.ai.service.enums.*;
import com.sankuai.dzhealth.ai.service.infrastructure.acl.ShopAcl;
import com.sankuai.dzhealth.ai.service.infrastructure.dal.entity.ChatConversationEntity;
import com.sankuai.dzhealth.ai.service.infrastructure.dal.entity.ChatMessageEntity;
import com.sankuai.dzhealth.ai.service.infrastructure.observability.Span;
import com.sankuai.dzhealth.ai.service.request.AiAnswerRequest;
import com.sankuai.wpt.user.thrift.token.UserIdMsg;
import com.sankuai.wpt.user.thrift.token.UserValidateTokenService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;
import org.springframework.web.util.WebUtils;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import com.sankuai.dzhealth.ai.service.enums.MetadataKeyEnum;

/**
 * @author:chenwei
 * @time: 2025/3/19 14:23
 * @version: 0.0.1
 */
@Slf4j
@RestController
public class ChatStreamController {


    @Autowired
    private ShopAcl shopAcl;


    @Autowired
    private ChatSessionService chatSessionService;

    @Autowired
    private UserValidateTokenService.Iface mdpUserValidateTokenService;

    @Autowired
    private EvaluationService evaluationService;

    @Autowired
    private FineTuneService fineTuningService;


    @MdpPigeonClient(url = "http://service.dianping.com/userAccountService/userAccountService_2.0.0", timeout = 1000, testTimeout = 5000)
    private UserAccountService userAccountService;


    public static final ThreadPool SSE_CONNECTION_POOL = Rhino.newThreadPool("CHAT_SSE_CONNECTION_POOL",
            DefaultThreadPoolProperties.Setter().withCoreSize(100).withMaxSize(200).withMaxQueueSize(1000));

    /**
     * 流式输出聊天响应
     * 返回SSE流，自动转换为事件流供前端消费
     * 流结束后会发送一个特殊的结束消息(type="finish")
     * <p>
     * 流程说明：
     * 1. 首先通过intentionChatClientBuilder转换原始请求
     * 2. 使用answerChatClientBuilder生成流式响应
     * 3. 将每个响应转换为StreamEventDTO对象
     * 4. 在流结束时添加一个结束消息，使前端能够感知流已结束
     *
     * @param request 请求参数
     * @return SseEmitter
     */
    @PostMapping(value = "/hospital/chat/question", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public SseEmitter getChatResponse(@RequestBody AiAnswerRequest request,
                                      HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse) throws TException {
        setSSEHeader(httpServletResponse);
        authenticateUserAndSetUserId(request, httpServletRequest);
        return getChatResponseInner(request);
    }

    @PostMapping(value = "/hospital/chat/question/s", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public SseEmitter getChatResponse(@RequestBody String request, HttpServletRequest httpServletRequest,
                                      HttpServletResponse httpServletResponse) {
        try {
            setSSEHeader(httpServletResponse);
            String json = JSON.parseObject(request, String.class);
            AiAnswerRequest aiAnswerRequest = JSON.parseObject(json, AiAnswerRequest.class);
            authenticateUserAndSetUserId(aiAnswerRequest, httpServletRequest);
            return getChatResponseInner(aiAnswerRequest);
        } catch (TException e) {
            log.error("getChatResponse error", e);
            return null;
        }
    }

    private void setSSEHeader(HttpServletResponse httpServletResponse) {
        httpServletResponse.setHeader("X-Accel-Buffering", "no");
        httpServletResponse.setHeader("Cache-Control", "no-cache");
        httpServletResponse.setHeader("Connection", "keep-alive");
    }

    @PostMapping(value = "/hospital/chat/question/block", produces = MediaType.APPLICATION_JSON_VALUE)
    public AiAnswerContext getChatResponseBlock(@RequestBody AiAnswerRequest request,
                                                HttpServletRequest httpServletRequest) throws TException {
        Cat.logEvent("getChatResponse", "start");
        log.info("ChatStreamController.getChatResponse request={}", request);

        // 创建SseEmitter实例，设置超时时间为10分钟
        SseEmitter sseEmitter = new SseEmitter(600000L);

        CompletableFuture<AiAnswerContext> future = CompletableFuture.supplyAsync(() -> {
            try {
                return doSendMessage(sseEmitter, request, true);
            } catch (Exception e) {
                log.error("doSendMessage error");
                throw new RuntimeException(e);
            }
        }, SSE_CONNECTION_POOL.getExecutor());

        return future.join();
    }

    /**
     * 获取用户token，认证用户身份并设置userId到请求中
     *
     * @param request            请求参数
     * @param httpServletRequest HTTP请求
     * @throws TException Thrift异常
     */
    private void authenticateUserAndSetUserId(AiAnswerRequest request, HttpServletRequest httpServletRequest) throws TException {
        String token = getToken(httpServletRequest);
        Long userId = null;
        if (request.getPlatform() == Platform.DP.getCode()) {
            VirtualBindUserInfoDTO virtualBindUserInfoDTO = userAccountService.loadUserByToken(token, StringUtils.EMPTY, Maps.newHashMap());
            if (virtualBindUserInfoDTO != null) {
                userId = virtualBindUserInfoDTO.getDpid();
            }
        } else {
            UserIdMsg userIdByToken = mdpUserValidateTokenService.getUserIdByToken(token);
            if (userIdByToken != null) {
                userId = userIdByToken.getUserId();
            }
        }
        if (userId != null) {
            request.getBasicParam().setUserId(userId);
        }
    }

    private String getToken(HttpServletRequest request) {
        String token = request.getHeader("token");
        if (StringUtils.isBlank(token)) {
            Cookie tokenCookie = WebUtils.getCookie(request, "token");
            if (tokenCookie != null) {
                token = tokenCookie.getValue();
            }
        }
        if (StringUtils.isBlank(token)) {
            token = request.getParameter("token");
        }
        return token;
    }

    private SseEmitter getChatResponseInner(AiAnswerRequest aiAnswerRequest) {
        Cat.logEvent("getChatResponse", "start");
        log.info("ChatStreamController.getChatResponse request={}", aiAnswerRequest);

        // 创建SseEmitter实例，设置超时时间为10分钟
        SseEmitter sseEmitter = new SseEmitter(600000L);

        CompletableFuture.runAsync(() -> {
            try {
                doSendMessage(sseEmitter, aiAnswerRequest, false);
            } catch (Exception e) {
                log.error("doSendMessage error");
                throw new RuntimeException(e);
            }
        }, SSE_CONNECTION_POOL.getExecutor());

        return sseEmitter;
    }

    private AiAnswerContext doSendMessage(SseEmitter emitter, AiAnswerRequest request, boolean allowNullSessionId) throws IOException {
        CompletableFuture<List<StreamEventDTO>> completionFuture = null;
        AiAnswerContext context = new AiAnswerContext();

        try {
            Cat.logEvent("doSendMessage", "start");
            emitter.send(StreamEventDTO.builder().type(StreamEventTypeEnum.OPEN.getType()).build());

            if (request.getBasicParam().getUserId() == null || request.getBasicParam().getUserId() <= 0) {
                throw new SseAwareException(StreamEventErrorTypeEnum.UN_LOGIN);
            }
            if (!allowNullSessionId && request.getSessionId() == null) {
                throw new SseAwareException(StreamEventErrorTypeEnum.SESSION_NOT_FOUND_ERROR);
            }

            // 初始化请求上下文并设置SSE发射器
            RequestContext.init();
            RequestContext.setAttribute(RequestContextConstant.SSE_EMITTER, emitter);

            // 准备聊天上下文
            prepareContext(context, request);
            // 直接调用chatSessionService.chatStream并获取完成Future
            completionFuture = chatSessionService.chatStream(context);
            // 等待流处理完成
            List<StreamEventDTO> data = completionFuture.join();
            StreamEventDTO streamEventDTO = mergeMainText(data);
            Long replyId = 0L;
            Optional<StreamEventDTO> replyIdEventOptional = data.stream().filter(Objects::nonNull).filter(e -> StreamEventDataTypeEnum.REPLY_MESSAGE_ID.getType().equals(e.getData().getEvent())).findFirst();
            if (replyIdEventOptional.isPresent()) {
                replyId = NumberUtils.toLong(replyIdEventOptional.get().getData().getContent());
                chatSessionService.saveOrUpdateMsg(ChatMessageEntity.builder()
                        .id(replyId)
                        .conversationId(String.valueOf(context.getSessionId()))
                        .messageId(UUID.randomUUID().toString())
                        .auditStatus(((byte) 1))
                        .contentType((byte) 1)
                        .content(JSON.toJSONString(streamEventDTO))
                        .senderId(String.valueOf(context.getUserId()))
                        .role(MsgRoleEnum.ROBOT.getBiz())
                        .build());
            } else {
                replyId = chatSessionService.saveOrUpdateMsg(ChatMessageEntity.builder()
                        .conversationId(String.valueOf(context.getSessionId()))
                        .messageId(UUID.randomUUID().toString())
                        .auditStatus(((byte) 1))
                        .contentType((byte) 1)
                        .content(JSON.toJSONString(streamEventDTO))
                        .senderId(String.valueOf(context.getUserId()))
                        .role(MsgRoleEnum.ROBOT.getBiz())
                        .build());
            }
            chatSessionService.saveFeedback(replyId, context.getUserId());

            // 评估需求对象，用于存储评估的需求信息
            log.info("start evaluation");
            RagEvaluationRequest intentionEvaluationRequest = RagEvaluationRequest.builder()
                    .sessionId(context.getSessionId())
                    .msgId(context.getMsgId())
                    .bizScene(context.getBizScene())
                    .modelScene("intention")
                    .userText(context.getSpans().stream()
                            .filter(span -> "classifyPrompt".equals(span.getKey()))
                            .findFirst()
                            .map(Span::getValue)
                            .orElse(null))
                    .responseContent(context.getSpans().stream()
                            .filter(span -> "intentionLLM".equals(span.getKey()))
                            .findFirst()
                            .map(Span::getValue)
                            .orElse(null))
                    .build();

            // 获取重写后的query（rewriteText）和意图信息
            String rewriteText = context.getSpans().stream()
                    .filter(span -> "rewriteText".equals(span.getKey()))
                    .findFirst()
                    .map(Span::getValue)
                    .orElse(null);

            Map<String, Object> chatEvalMetadata = new HashMap<>();
            if (rewriteText != null) {
                chatEvalMetadata.put(MetadataKeyEnum.REWRITE_TEXT.getKey(), rewriteText);
            }

            // 添加shopId到metadata中，优先使用mtShopId，然后businessId，兜底使用shopId
            String mtShopIdForMetadata = null;
            if (context.getMtShopId() != null && context.getMtShopId() > 0) {
                mtShopIdForMetadata = String.valueOf(context.getMtShopId());
            } else if (StringUtils.isNotBlank(context.getBusinessId())) {
                mtShopIdForMetadata = context.getBusinessId();
            } else if (context.getShopId() != null && context.getShopId() > 0) {
                mtShopIdForMetadata = String.valueOf(context.getShopId());
            }
            if (StringUtils.isNotBlank(mtShopIdForMetadata)) {
                chatEvalMetadata.put(MetadataKeyEnum.SHOP_ID.getKey(), mtShopIdForMetadata);
                log.info("添加mtShopId到评价请求metadata: {}", mtShopIdForMetadata);
            }

            RagEvaluationRequest chatEvaluationRequest = RagEvaluationRequest.builder()
                    .sessionId(context.getSessionId())
                    .msgId(context.getMsgId())
                    .bizScene(context.getBizScene())
                    .modelScene("chat")
                    .userText(context.getSpans().stream()
                            .filter(span -> "answerPrompt".equals(span.getKey()))
                            .findFirst()
                            .map(Span::getValue)
                            .orElse(null))
                    .dataList(context.getSpans().stream()
                            .filter(span -> "ragInfo".equals(span.getKey()))
                            .findFirst()
                            .map(Span::getValue)
                            .map(JsonUtils::parseDocumentList)
                            .orElse(Collections.emptyList()))
                    .responseContent(context.getAssistantContent())
                    .metadata(chatEvalMetadata)
                    .build();

            evaluationService.executeStrategies(intentionEvaluationRequest);
            evaluationService.executeStrategies(chatEvaluationRequest);

            fineTuningService.generateFineTuningData(context);

            emitter.send(SseEmitter.event().data(StreamEventDTO.builder().type(StreamEventTypeEnum.MESSAGE.getType())
                    .data(StreamEventDataDTO.builder().event(StreamEventDataTypeEnum.REPLY_MESSAGE_ID.getType()).content(String.valueOf(replyId)).build()).build()));
            emitter.send(StreamEventDTO.builder().type(StreamEventTypeEnum.CLOSE.getType()).build());
            log.info("request={}, result={}", JSON.toJSONString(request), JSON.toJSONString(context));
            context.setStatus(StreamEventErrorTypeEnum.SUCCESS.getType());
        } catch (SseAwareException e) {
            log.error("SseAwareException processing chat response, request={}, result={}", request, JSON.toJSONString(context), e);
            emitter.send(StreamEventDTO.builder().type(StreamEventTypeEnum.ERROR.getType())
                    .data(StreamEventDataDTO.builder().event(e.getEvent()).content(e.getContent()).build()).build());
            emitter.send(StreamEventDTO.builder().type(StreamEventTypeEnum.CLOSE.getType()).build());
            context.setStatus(e.getEvent());
        } catch (Exception e) {
            log.error("Error processing chat response, request={}, result={}", request, JSON.toJSONString(context), e);
            chatSessionService.saveOrUpdateMsg(ChatMessageEntity.builder()
                    .conversationId(String.valueOf(context.getSessionId()))
                    .messageId(UUID.randomUUID().toString())
                    .auditStatus(((byte) 1))
                    .contentType((byte) 1)
                    .content(JSON.toJSONString(buildErrorEvent()))
                    .senderId(String.valueOf(context.getUserId()))
                    .role(MsgRoleEnum.ROBOT.getBiz())
                    .build());
            emitter.send(StreamEventDTO.builder().type(StreamEventTypeEnum.ERROR.getType())
                    .data(StreamEventDataDTO.builder().event(StreamEventErrorTypeEnum.SERVER_ERROR.getType())
                            .content("系统繁忙，请稍后再试").build()).build());
            emitter.send(StreamEventDTO.builder().type(StreamEventTypeEnum.CLOSE.getType()).build());
            context.setStatus(StreamEventErrorTypeEnum.SERVER_ERROR.getType());
            // 如果有未完成的Future，标记为异常完成
            if (completionFuture != null && !completionFuture.isDone()) {
                completionFuture.completeExceptionally(e);
            }
        } finally {
            emitter.complete();
            RequestContext.cleanup();
        }
        return context;
    }


    private StreamEventDTO buildErrorEvent() {
        return StreamEventDTO.builder().type(StreamEventTypeEnum.MESSAGE.getType())
                .data(StreamEventDataDTO.builder()
                        .event(StreamEventDataTypeEnum.MAIN_TEXT.getType())
                        .content("抱歉，网络出小差了").build()).build();
    }

    private StreamEventDTO mergeMainText(List<StreamEventDTO> eventDTOS) {
        List<StreamEventDTO> mainTextDTOs = eventDTOS.stream().filter(Objects::nonNull).filter(e -> StreamEventDataTypeEnum.MAIN_TEXT.getType().equals(e.getData().getEvent())).toList();
        StreamEventDTO mergeMainEventDTO = new StreamEventDTO();
        StreamEventDataDTO mergeMainDataDTO = new StreamEventDataDTO();

        mergeMainEventDTO.setType(StreamEventTypeEnum.MESSAGE.getType());
        mergeMainDataDTO.setEvent(StreamEventDataTypeEnum.MAIN_TEXT.getType());
        mergeMainDataDTO.setCardsData(Lists.newArrayList());
        mergeMainDataDTO.setContent(StringUtils.EMPTY);
        mainTextDTOs.stream()
                .filter(mainText -> Objects.nonNull(mainText.getData()))
                .forEach(mainText -> {
                    String existingContent = mergeMainDataDTO.getContent();
                    String newContent = mainText.getData().getContent();
                    mergeMainDataDTO.setContent(StringUtils.isBlank(existingContent)
                            ? newContent
                            : existingContent + newContent);

                    List<StreamEventCardDataDTO> cardsData = mergeMainDataDTO.getCardsData();
                    List<StreamEventCardDataDTO> newCardsData = Optional.ofNullable(mainText.getData().getCardsData())
                            .orElse(Lists.newArrayList());
                    cardsData.addAll(newCardsData);
                });
        mergeMainEventDTO.setData(mergeMainDataDTO);

        return mergeMainEventDTO;

    }

    /**
     * 准备聊天上下文
     * 从AiAnswerServiceImpl复制的逻辑
     */
    private void prepareContext(AiAnswerContext context, AiAnswerRequest request) {
        // 设置基本请求信息
        context.setPlatform(request.getPlatform());
        context.setRequestId(request.getRequestId());
        context.setSessionId(request.getSessionId());
        context.setContent(request.getContent());
        context.setBizScene(request.getBizType());
        context.setType(request.getType());
        context.setRole(request.getRole());
        context.setExtension(request.getExtension());
        context.setShopId(request.getShopId());
        context.setStream(request.getStream());
        context.setMsgId(request.getMsgId());
        context.setTraceId(Tracer.id());
        context.setMtShopId(request.getShopId());

        // 设置用户基本信息（从 BasicParam 中获取）
        if (request.getBasicParam() != null) {
            context.setUserId(request.getPlatform() == Platform.MT.getCode() ? "m-" + request.getBasicParam().getUserId() : "d-" + request.getBasicParam().getUserId());
            context.setLat(request.getBasicParam().getLat());
            context.setLng(request.getBasicParam().getLng());
            context.setClientType(request.getBasicParam().getClientType());
            context.setUuid(request.getBasicParam().getUuid());
            context.setAppVersion(request.getBasicParam().getAppVersion());
            context.setCityId(request.getBasicParam().getCityId());
            context.setUserCityId(request.getBasicParam().getUserCityId());
            context.setLocationAuthorized(request.getBasicParam().getLocationAuthorized());
        }

        //处理用户点击卡片信息和扩展字段
        if (StringUtils.isNotBlank(request.getExtension())) {
            // 将字符串解析为 Map
            Map<String, String> extensionMap = JSON.parseObject(request.getExtension(),
                    new TypeReference<Map<String, String>>() {
                    });

            // 获取特定的 key 值处理点击卡片信息
            String sourceValue = extensionMap.get("inputSource");
            if (NumberUtils.toInt(sourceValue) == 2) {
                Pattern pattern = Pattern.compile("\\[(.*?)\\]");
                Matcher matcher = pattern.matcher(request.getContent());

                List<String> bracketsContent = new ArrayList<>();
                while (matcher.find()) {
                    bracketsContent.add(matcher.group(1));
                }
                if (CollectionUtils.isNotEmpty(bracketsContent)) {
                    context.setContent(String.join(",", bracketsContent));
                }
            }

            // 解析业务类型和业务ID
            if (extensionMap.containsKey("businessType")) {
                String businessType = extensionMap.get("businessType");
                context.setBusinessType(businessType);
            } else {
                // 默认设置为SHOP业务类型
                context.setBusinessType(BusinessTypeEnum.SHOP.getType());
            }

            if (extensionMap.containsKey("businessId")) {
                String businessId = extensionMap.get("businessId");
                context.setBusinessId(businessId);
            }

            log.info("解析业务关联信息: businessType={}, businessId={}",
                    context.getBusinessType(), context.getBusinessId());
        } else {
            // 设置默认业务类型
            context.setBusinessType(BusinessTypeEnum.SHOP.getType());
        }

        // 特殊处理PUBLIC_HOSPITAL场景：使用shopId作为businessId
        if (BizSceneEnum.PUBLIC_HOSPITAL.getBizScene().equals(context.getBizScene()) &&
                context.getShopId() != null && context.getShopId() > 0) {
            context.setBusinessType(BusinessTypeEnum.SHOP.getType());
            context.setBusinessId(String.valueOf(context.getShopId()));
            log.info("PUBLIC_HOSPITAL场景：使用shopId设置businessId: shopId={}, businessId={}",
                    context.getShopId(), context.getBusinessId());
        }

        if (request.getPlatform() != null && request.getPlatform() == 1) {
            Long mtShopId = shopAcl.queryMtIdByDpId(request.getShopId());
            context.setMtShopId(mtShopId);
            context.setBusinessId(String.valueOf(mtShopId));
        }

        // 兜底 设置创建时间
        context.setRequestTime(String.valueOf(System.currentTimeMillis()));
        // 处理会话相关逻辑
        if (request.getSessionId() == null) {
            // 创建会话（带业务关联信息）
            ChatConversationEntity conversationEntity = null;
            if (StringUtils.isNotBlank(context.getBusinessType())) {
                // 使用带业务类型参数的方法创建会话
                conversationEntity = chatSessionService.createSession(
                        context,
                        context.getBusinessType(),
                        context.getBusinessId());
            } else {
                // 使用默认方法创建会话
                conversationEntity = chatSessionService.createSession(context);
            }
            context.setSessionId(conversationEntity.getId());
            context.setConversationIdStr(conversationEntity.getConversationId());
        } else {
            // 检查会话是否存在
            ChatConversationEntity conversationEntity = chatSessionService.checkSession(context.getSessionId(), context.getUserId());
            if (conversationEntity == null || conversationEntity.getStatus() == 0) {
                throw new IllegalStateException("没有找到对应会话详细内容");
            }
            context.setHistory(conversationEntity.getTitle());
            if (StringUtils.isBlank(conversationEntity.getTitle())) {
                String title = StringUtils.substring(context.getContent(), 0, Math.min(context.getContent().length(), 100));
                chatSessionService.updateSession(context.getSessionId(), (byte) 1, title);

            }
            // 如果提供了业务关联信息且会话存在，更新会话的业务关联信息
            if (StringUtils.isNotBlank(context.getBusinessType()) &&
                    (StringUtils.isBlank(conversationEntity.getBusinessType()) ||
                            !context.getBusinessType().equals(conversationEntity.getBusinessType()) ||
                            !Objects.equals(context.getBusinessId(), conversationEntity.getBusinessId()))) {
                log.info("更新会话业务关联信息: sessionId={}, businessType={}, businessId={}",
                        context.getSessionId(), context.getBusinessType(), context.getBusinessId());
                chatSessionService.updateSessionBusinessInfo(
                        context.getSessionId(),
                        context.getBusinessType(),
                        context.getBusinessId());
            }
        }

        // 保存用户消息
        Long msgId = chatSessionService.saveMessage(context);
        context.setMsgId(msgId);
    }

}
