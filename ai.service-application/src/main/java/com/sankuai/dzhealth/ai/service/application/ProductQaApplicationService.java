package com.sankuai.dzhealth.ai.service.application;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dianping.haima.entity.haima.HaimaContent;
import com.dianping.lion.client.Lion;
import com.sankuai.beautycontent.beautylaunchapi.guide.dto.GuideModel;
import com.sankuai.beautycontent.beautylaunchapi.guide.dto.TabModel;
import com.sankuai.beautycontent.beautylaunchapi.guide.request.AvoidGuideQuery;
import com.sankuai.dzhealth.ai.service.agent.domain.model.product.Product;
import com.sankuai.dzhealth.ai.service.agent.infrastructure.proxy.avoidguide.GuideAggregateProxy;
import com.sankuai.dzhealth.ai.service.domain.converter.Guide2QaConverter;
import com.sankuai.dzhealth.ai.service.domain.service.MedicalTagDomainService;
import com.sankuai.dzhealth.ai.service.domain.service.ProductDomainService;
import com.sankuai.dzhealth.ai.service.domain.utils.AestheticMedicineAiUrlUtils;
import com.sankuai.dzhealth.ai.service.dto.MedicalTagItem;
import com.sankuai.dzhealth.ai.service.dto.ProductQaDTO;
import com.sankuai.dzhealth.ai.service.dto.QaItemDTO;
import com.sankuai.dzhealth.ai.service.infrastructure.acl.HaimaAcl;
import com.sankuai.dzhealth.ai.service.request.ProductQaRequest;
import com.sankuai.general.product.query.center.client.enums.IdTypeEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
@RequiredArgsConstructor
@Slf4j
public class ProductQaApplicationService {

    private final GuideAggregateProxy guideAggregateProxy;
    private final MedicalTagDomainService medicalTagDomainService;
    private final HaimaAcl haimaAcl;
    private final ProductDomainService productDomainService; // 用于统一查询商品信息，避免重复RPC调用

    public ProductQaDTO getProductQa(ProductQaRequest request) {
        // 0. 统一查询一次商品信息，后续复用
        Product product = null;
        String productTitle = "";
        try {
            IdTypeEnum idTypeEnum = determineIdTypeEnum(request.getIdType());
            product = productDomainService.findProduct(Long.valueOf(request.getProductId()), idTypeEnum);
            if (product != null && product.getBasic() != null && org.apache.commons.lang3.StringUtils.isNotBlank(product.getBasic().getTitle())) {
                productTitle = product.getBasic().getTitle();
            }
        } catch (Exception ex) {
            log.warn("buildProductQa: failed to query product info, productId={}, error={}", request.getProductId(), ex.getMessage());
        }

        // 根据类目ID判断业务类型
        String level2CategoryId = request.getCategoryId();

        ProductQaDTO result;
        if ("850".equals(level2CategoryId)) {
            result = buildProductQaForMedical(request, product, productTitle);
        } else if ("506".equals(level2CategoryId)) {
            result = buildProductQaForDental(request, product, productTitle);
        } else {
            // 默认兜底：空内容
            result = ProductQaDTO.builder()
                    .qaItems(new ArrayList<>())
                    .agentEntranceText("遇到不懂？问问AI")
                    .agentEntranceUrl(buildAgentEntranceUrl(request, productTitle))
                    .build();
        }

        // 统一应用问题筛选逻辑
        if (StringUtils.isNotBlank(request.getQuestion()) && CollectionUtils.isNotEmpty(result.getQaItems())) {
            List<QaItemDTO> filteredQuestions = filterQuestionsByKeyword(result.getQaItems(), request.getQuestion());
            result = result.toBuilder().qaItems(filteredQuestions).build();
        }

        return result;
    }

    private ProductQaDTO buildProductQaForMedical(ProductQaRequest request, Product product, String productTitle) {
        // 直接获取选中的指南，如果找不到就直接走兜底逻辑
        GuideModel selectedGuide = selectProjectGuide(request, product);
        if (selectedGuide == null) {
            return ProductQaDTO.builder()
                    .qaItems(new ArrayList<>())
                    .agentEntranceText("遇到不懂？问问AI")
                    .agentEntranceUrl(buildAgentEntranceUrl(request, productTitle))
                    .build();
        }

        // 直接使用选中的指南
        List<QaItemDTO> qaList = Guide2QaConverter.convert(selectedGuide, request.getProductId(), productTitle,
                request.getPlatform(),
                request.getIdType() ,
                Integer.parseInt(request.getCategoryId()));
        var doctorInfo = Guide2QaConverter.buildDoctorInfo(selectedGuide.getAuthor());

        return ProductQaDTO.builder()
                .qaItems(qaList)
                .agentEntranceText("遇到不懂？问问AI")
                .agentEntranceUrl(buildAgentEntranceUrl(request, productTitle))
                .doctorInfo(doctorInfo)
                .build();
    }

    private ProductQaDTO buildProductQaForDental(ProductQaRequest request, Product product, String productTitle) {
        // 使用固定的海马 sceneKey，通过 fields 进行过滤
        String sceneKey = "dental_product_qa";
        Map<String, String> fields = new HashMap<>();

        // 设置二级类目过滤
        fields.put("categoryId", request.getCategoryId());

        // 尝试获取三级类目ID，用于更精确的过滤（直接使用已查询到的商品信息）
        if (product != null && product.getCategory() != null) {
            String thirdCategoryId = product.getCategory().getMostPreciseCategoryId();
            if (org.apache.commons.lang3.StringUtils.isNotBlank(thirdCategoryId)) {
                fields.put("thirdCategoryId", thirdCategoryId);
                log.info("buildProductQaForDental: using third category filter thirdCategoryId={} for productId={}", thirdCategoryId, request.getProductId());
            } else {
                log.warn("buildProductQaForDental: no third category found for productId={}, using level2 category filter only", request.getProductId());
            }
        }

        List<HaimaContent> contents = haimaAcl.getContent(sceneKey, fields);
        List<QaItemDTO> qaItems = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(contents)) {
            for (HaimaContent content : contents) {
                try {
                    JSONObject obj = JSON.parseObject(content.getExtJson());
                    String q = obj.getString("question");
                    String a = obj.getString("answer");
                    
                    if (q != null && a != null) {
                        qaItems.add(QaItemDTO.builder()
                                .question(q)
                                .answer(a)
                                .imageUrl(null)
                                .agentQaUrl(AestheticMedicineAiUrlUtils.buildAgentUrl(request.getProductId(), productTitle,
                                        request.getPlatform() != null ? request.getPlatform() : 2,
                                        request.getIdType() != null ? request.getIdType() : 3,
                                        Integer.parseInt(request.getCategoryId() != null ? request.getCategoryId() : "506"),
                                        q, qaItems.isEmpty()))
                                .build());
                    }
                } catch (Exception e) {
                    // ignore malformed extJson
                }
            }
        }

        return ProductQaDTO.builder()
                .qaItems(qaItems)
                .agentEntranceText("遇到不懂？问问AI")
                .agentEntranceUrl(buildAgentEntranceUrl(request, productTitle))
                .doctorInfo(null)
                .build();
    }



    /**
     * Replicate AvoidGuideVersionAdaptor#getSelectedProjectTag logic (simplified):
     * 1. query medical project tags for product
     * 2. fetch guides under these tags
     * 3. pick one guide according to the priority rules
     */
    private GuideModel selectProjectGuide(ProductQaRequest request, Product product) {
        Map<Integer, MedicalTagItem> projectTagsForProduct = medicalTagDomainService.queryProjectTagLevels(product);
        if (MapUtils.isEmpty(projectTagsForProduct)) {
            return null;
        }

        // fetch guides under these tag ids
        AvoidGuideQuery tempQuery = AvoidGuideQuery.builder()
                .source("product")
                .category(Arrays.asList(0,5,6))
                .projectTagId(new ArrayList<>(projectTagsForProduct.keySet()))
                .includeOffline(false)
                .clientContext(AvoidGuideQuery.ClientContext.builder()
                        .platform(request.getPlatform() != null ? request.getPlatform() : 2)
                        .inApp(true)
                        .userId(0L)
                        .needAb(false)
                        .build())
                .returnModel(AvoidGuideQuery.ReturnModel.builder()
                        .pitSizePerTab(-1)
                        .returnShare(false)
                        .returnStructuredInfo(true)
                        .build())
                .build();
        List<GuideModel> guideModels = guideAggregateProxy.queryGuide(tempQuery);
        if (CollectionUtils.isEmpty(guideModels)) {
            return null;
        }

        List<GuideModel> firstAndSecondGuideModels = new ArrayList<>();
        List<GuideModel> thirdGuideModels = new ArrayList<>();

        separateGuideModelsByTagLevel(new ArrayList<>(projectTagsForProduct.values()), guideModels,
                firstAndSecondGuideModels, thirdGuideModels);

        GuideModel selectedGuide = chooseProjectGuideByPriority(firstAndSecondGuideModels, thirdGuideModels);
        return selectedGuide;
    }

    private void separateGuideModelsByTagLevel(List<MedicalTagItem> projectTags,
                                               List<GuideModel> guideModels,
                                               List<GuideModel> firstAndSecondGuideModels,
                                               List<GuideModel> thirdGuideModels) {
        Set<Integer> firstSecondSet = new HashSet<>();
        Set<Integer> thirdSet = new HashSet<>();

        for (MedicalTagItem item : projectTags) {
            if (item.getTagLevel() == 1 || item.getTagLevel() == 2) {
                firstSecondSet.add(item.getId());
            } else if (item.getTagLevel() == 3) {
                thirdSet.add(item.getId());
            }
        }

        if (CollectionUtils.isNotEmpty(guideModels)) {
            for (GuideModel gm : guideModels) {
                Integer tagId = gm.getProjectTagId();
                if (tagId == null || tagId == 0) {
                    continue;
                }

                // 在分类的同时检查指南是否有有效内容
                if (!hasValidContent(gm)) {
                    continue;
                }

                if (firstSecondSet.contains(tagId)) {
                    firstAndSecondGuideModels.add(gm);
                } else if (thirdSet.contains(tagId)) {
                    thirdGuideModels.add(gm);
                }
            }
        }
    }

    /**
     * 根据优先级选择指南
     * 注意：传入的firstAndSecond和third列表已经在separateGuideModelsByTagLevel中过滤过，只包含有有效内容的指南
     */
    private GuideModel chooseProjectGuideByPriority(List<GuideModel> firstAndSecond,
                                                    List<GuideModel> third) {
        // 合并所有指南
        List<GuideModel> all = new ArrayList<>();
        all.addAll(firstAndSecond);
        all.addAll(third);

        if (CollectionUtils.isEmpty(all)) {
            return null;
        }

        // 单个指南直接返回，优先三级标签
        if (all.size() == 1) {
            return all.get(0);
        }
        if (CollectionUtils.isNotEmpty(third) && firstAndSecond.isEmpty()) {
            return third.get(0);
        }
        if (CollectionUtils.isNotEmpty(firstAndSecond) && third.isEmpty()) {
            return firstAndSecond.get(0);
        }

        // 多个指南时按优先级选择
        Map<Integer, GuideModel> guideMap = new HashMap<>();
        for (GuideModel gm : all) {
            guideMap.putIfAbsent(gm.getProjectTagId(), gm);
        }

        List<Integer> priorityTags = Lion.getList(
                "com.sankuai.beautycontent.launchweb", "product.priority.tag", Integer.class, new ArrayList<>());

        for (Integer tagId : priorityTags) {
            if (guideMap.containsKey(tagId)) {
                return guideMap.get(tagId);
            }
        }

        // 优先返回三级标签指南，否则返回第一个
        return CollectionUtils.isNotEmpty(third) ? third.get(0) : all.get(0);
    }

    /**
     * 根据关键词筛选问答列表
     * @param questions 原始问答列表
     * @param keyword 筛选关键词
     * @return 筛选后的问答列表
     */
    private List<QaItemDTO> filterQuestionsByKeyword(List<QaItemDTO> questions, String keyword) {
        if (CollectionUtils.isEmpty(questions) || StringUtils.isBlank(keyword)) {
            return questions;
        }

        String normalizedKeyword = keyword.toLowerCase().trim();
        List<QaItemDTO> filteredQuestions = new ArrayList<>();

        for (QaItemDTO qa : questions) {
            // 检查问题标题是否包含关键词
            if (qa.getQuestion() != null && qa.getQuestion().toLowerCase().contains(normalizedKeyword)) {
                filteredQuestions.add(qa);
                continue;
            }

            // 检查答案内容是否包含关键词
            if (qa.getAnswer() != null && qa.getAnswer().toLowerCase().contains(normalizedKeyword)) {
                filteredQuestions.add(qa);
            }
        }

        // 如果筛选后没有结果，返回原始列表
        if (filteredQuestions.isEmpty()) {
            log.info("No questions found for keyword: {}, returning original list", keyword);
            return questions;
        }

        log.info("Filtered {} questions to {} questions for keyword: {}",
                questions.size(), filteredQuestions.size(), keyword);
        return filteredQuestions;
    }

    private String buildAgentEntranceUrl(ProductQaRequest request, String productTitle) {
        // 使用统一的URL构造方法，不包含问题参数
        return AestheticMedicineAiUrlUtils.buildAgentUrl(
                request.getProductId(),
                productTitle,
                request.getPlatform() != null ? request.getPlatform() : 2,
                request.getIdType() != null ? request.getIdType() : 3,
                Integer.parseInt(request.getCategoryId() != null ? request.getCategoryId() : "850"),
                null, null
        );
    }


    /**
     * 检查指南是否包含有效内容
     */
    private boolean hasValidContent(GuideModel guide) {
        if (guide == null) {
            return false;
        }

        // 检查直接的pits
        if (CollectionUtils.isNotEmpty(guide.getPits())) {
            return true;
        }

        // 检查tabs中是否包含pits
        if (CollectionUtils.isNotEmpty(guide.getTabs())) {
            return hasValidPitsInTabs(guide.getTabs());
        }

        return false;
    }

    /**
     * 递归检查tabs中是否包含有效的pits
     */
    private boolean hasValidPitsInTabs(List<TabModel> tabs) {
        if (CollectionUtils.isEmpty(tabs)) {
            return false;
        }

        for (var tab : tabs) {
            // 检查当前tab的pits
            if (CollectionUtils.isNotEmpty(tab.getPits())) {
                return true;
            }

            // 递归检查子tabs
            if (CollectionUtils.isNotEmpty(tab.getTabs())) {
                if (hasValidPitsInTabs(tab.getTabs())) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * 根据idType确定IdTypeEnum
     */
    private IdTypeEnum determineIdTypeEnum(Integer idType) {
        if (idType != null && idType == 1) {
            return IdTypeEnum.DP;
        } else if (idType != null && idType == 2) {
            return IdTypeEnum.MT;
        } else if (idType != null && idType == 3) {
            return IdTypeEnum.BIZ_PRODUCT;
        }
        return IdTypeEnum.BIZ_PRODUCT;
    }
} 