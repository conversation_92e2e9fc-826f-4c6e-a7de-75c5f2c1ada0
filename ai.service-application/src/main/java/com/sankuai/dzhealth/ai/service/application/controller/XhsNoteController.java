package com.sankuai.dzhealth.ai.service.application.controller;

import com.fasterxml.jackson.databind.JsonNode;
import com.sankuai.dzhealth.ai.service.api.RemoteResponse;
import com.sankuai.dzhealth.ai.service.domain.entity.xhs.SaveHotNoteRequest;
import com.sankuai.dzhealth.ai.service.domain.entity.xhs.XhsNoteRequest;
import com.sankuai.dzhealth.ai.service.domain.entity.xhs.XhsNoteStoreDTO;
import com.sankuai.dzhealth.ai.service.domain.entity.xhs.XhsPromptEditRequest;
import com.sankuai.dzhealth.ai.service.domain.service.XhsNoteAbstractService;
import com.sankuai.dzhealth.ai.service.domain.service.impl.xhs.XhsNoteStoreDomainService;
import com.sankuai.dzhealth.ai.service.domain.utils.JsonUtils;
import com.sankuai.dzhealth.ai.service.request.xhs.YushuNoteRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@RestController
public class XhsNoteController {

    private final XhsNoteAbstractService<XhsNoteRequest> buildNoteService;
    private final XhsNoteAbstractService<List<SaveHotNoteRequest>> saveHotNotesService;
    private final XhsNoteAbstractService<String> searchHotNoteServeImpl;
    private final XhsNoteAbstractService<XhsPromptEditRequest> xhsPromptEditServeImpl;

    @Autowired
    private XhsNoteStoreDomainService xhsNoteStoreDomainService;


    private static final String XHS_NOTE_LINK = "https://www.xiaohongshu.com/discovery/item/%s";

    @Autowired
    public XhsNoteController(
            @Qualifier("xhsBuildNoteServeImpl")
            XhsNoteAbstractService<XhsNoteRequest> buildNoteService,
            @Qualifier("saveHotNoteServeImpl")
            XhsNoteAbstractService<List<SaveHotNoteRequest>> saveHotNotesService,
            @Qualifier("searchHotNoteServeImpl")
            XhsNoteAbstractService<String> searchHotNoteServeImpl,
            @Qualifier("xhsPromptEditServeImpl")
            XhsNoteAbstractService<XhsPromptEditRequest> xhsPromptEditServeImpl
    ) {
        this.buildNoteService = buildNoteService;
        this.saveHotNotesService = saveHotNotesService;
        this.searchHotNoteServeImpl = searchHotNoteServeImpl;
        this.xhsPromptEditServeImpl = xhsPromptEditServeImpl;
    }

    @PostMapping("/buildXhsNote")
    public String buildXhsNote(@RequestBody XhsNoteRequest request) {
        return buildNoteService.doTask(request);
    }

    @PostMapping("/saveHotNotes")
    public String saveHotNotes(@RequestBody List<SaveHotNoteRequest> requests) {
        return saveHotNotesService.doTask(requests);
    }

    @PostMapping("/hotNoteSearch")
    public String hotNoteSearch(@RequestBody String request) {
        return searchHotNoteServeImpl.doTask(request);
    }

    @PostMapping("/editPrompt")
    public String editPrompt(@RequestBody XhsPromptEditRequest request) {
        return xhsPromptEditServeImpl.doTask(request);
    }

    @PostMapping(value = "/yushu/saveNote")
    public RemoteResponse<String> saveNoteFromYushu(@RequestBody YushuNoteRequest request) {
        // 参数校验
        if (StringUtils.isBlank(request.getXhsJson())) {
            return RemoteResponse.buildFail("请求数据为空");
        }
        String xhsJson = request.getXhsJson();

        Boolean res = processSingleNote(xhsJson, request.getBatch());

        return res ? RemoteResponse.buildSuccess("success") : RemoteResponse.buildFail("fail");

    }

    /**
     * 处理单个JSON字符串
     * @param jsonString JSON字符串
     * @return 处理结果，true表示成功，false表示失败
     */
    private Boolean processSingleNote(String jsonString, String batch) {
        try {
            // 解析JSON
            JsonNode jsonNode = JsonUtils.parseJsonNode(jsonString);
            if (jsonNode == null) {
                log.warn("[jsonString], jsonString: {}", jsonString);
                return false;
            }

            // 构建笔记存储对象
            XhsNoteStoreDTO noteStoreDTO = buildNoteStoreDTO(jsonNode, batch);

            log.info("[jsonString]: {}", jsonString);
            // 保存笔记
            xhsNoteStoreDomainService.saveNote(noteStoreDTO);

            return true;

        } catch (Exception e) {
            log.error("处理单个JSON失败, json: {}", jsonString, e);
            return false;
        }
    }

    /**
     * 构建笔记存储DTO对象
     * @param jsonNode JSON节点
     * @return 笔记存储DTO
     */
    private XhsNoteStoreDTO buildNoteStoreDTO(JsonNode jsonNode, String batch) {
        XhsNoteStoreDTO noteStoreDTO = new XhsNoteStoreDTO();

        // 设置基本信息
        noteStoreDTO.setStatus(0);
        noteStoreDTO.setBatch(batch);
        noteStoreDTO.setRawData(jsonNode.toString());
        noteStoreDTO.setLink(String.format(XHS_NOTE_LINK, getTextValue(jsonNode, "id")));
        noteStoreDTO.setRelationId(getTextValue(jsonNode, "id"));
        noteStoreDTO.setTitle(getTitleValue(jsonNode));
        noteStoreDTO.setContent(getTextValue(jsonNode, "desc"));
        noteStoreDTO.setHead(getTextValue(jsonNode, "cover"));
        noteStoreDTO.setType(getTextValue(jsonNode, "type"));

        // 设置用户信息
        setUserInfo(noteStoreDTO, jsonNode.get("user"));

        // 设置话题标签
        setTopics(noteStoreDTO, jsonNode.get("topics"));

        // 设置互动数据
        JsonNode interactiveCountNode = jsonNode.get("interactiveCount");
        if (interactiveCountNode != null) {
            noteStoreDTO.setInteract(interactiveCountNode.asLong(0));
        }

        return noteStoreDTO;
    }

    private String getTextValue(JsonNode parentNode, String fieldName) {
        JsonNode node = parentNode.get(fieldName);
        return node != null ? node.asText() : StringUtils.EMPTY;
    }
    private String getTitleValue(JsonNode jsonNode) {
        JsonNode titleNode = jsonNode.get("title");
        if (titleNode != null && !titleNode.asText().trim().isEmpty()) {
            return titleNode.asText();
        }
        return StringUtils.EMPTY;
    }
    private void setUserInfo(XhsNoteStoreDTO noteStoreDTO, JsonNode userNode) {
        if (userNode != null) {
            JsonNode nicknameNode = userNode.get("nickname");
            if (nicknameNode != null) {
                noteStoreDTO.setAuthorName(nicknameNode.asText());
            }

            JsonNode redIdNode = userNode.get("redId");
            if (redIdNode != null) {
                noteStoreDTO.setAuthorId(redIdNode.asText());
            }
        }
    }
    private void setTopics(XhsNoteStoreDTO noteStoreDTO, JsonNode topicsNode) {
        if (topicsNode != null && topicsNode.isArray()) {
            List<String> topicNames = new ArrayList<>();
            for (JsonNode topic : topicsNode) {
                JsonNode nameNode = topic.get("name");
                if (nameNode != null) {
                    topicNames.add(nameNode.asText());
                }
            }
            noteStoreDTO.setTopic(topicNames);
        }
    }
}
