package com.sankuai.dzhealth.ai.service.application.facade;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.dianping.rhino.Rhino;
import com.dianping.rhino.threadpool.DefaultThreadPoolProperties;
import com.dianping.rhino.threadpool.ThreadPool;
import com.meituan.mdp.boot.starter.thrift.annotation.MdpThriftServer;
import com.sankuai.dzhealth.ai.service.api.RemoteResponse;
import com.sankuai.dzhealth.ai.service.api.xhs.XhsNoteStoreService;
import com.sankuai.dzhealth.ai.service.domain.service.impl.xhs.MedicalNoteService;
import com.sankuai.dzhealth.ai.service.domain.service.impl.xhs.XhsNoteStoreDomainService;
import com.sankuai.dzhealth.ai.service.enums.xhs.BizTypeEnum;
import com.sankuai.dzhealth.ai.service.request.xhs.XhsBuildNoteRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

/**
 * @author:chenwei
 * @time: 2025/6/16 16:29
 * @version: 0.0.1
 */
@MdpThriftServer
@Slf4j
@RequiredArgsConstructor
public class XhsNoteStoreServiceImpl implements XhsNoteStoreService {
    // 线程池用于异步执行笔记生成任务
    public static final ThreadPool TASK_POOL = Rhino.newThreadPool("AnalyzeNoteTaskPool",
            DefaultThreadPoolProperties.Setter().withCoreSize(5).withMaxSize(20).withMaxQueueSize(1000));

    @Autowired
    private XhsNoteStoreDomainService xhsNoteStoreDomainService;

    @Autowired
    private MedicalNoteService medicalNoteService;

    @Override
    public RemoteResponse<String> generateNote(XhsBuildNoteRequest request) {
        // 参数校验
        String checkRes = checkParams(request);
        if (StringUtils.isNotBlank(checkRes)) {
            return RemoteResponse.buildFail(checkRes);
        }

        xhsNoteStoreDomainService.generateNotes(request);
        
        return RemoteResponse.buildSuccess(null);
    }

    @Override
    public RemoteResponse<String> analyzeNote(XhsBuildNoteRequest request) {
        JSONArray batches = JSON.parseArray(request.getParams().get("batch"));
        for (int i = 0; i < batches.size(); i++) {
            JSONObject jsonObject = batches.getJSONObject(i);
            String batch = jsonObject.getString("batch");
            String word = jsonObject.getString("word");
            CompletableFuture.delayedExecutor(5L * i, TimeUnit.MINUTES, TASK_POOL.getExecutor())
                    .execute(() -> medicalNoteService.processAnalyseSafely(request.getBatch() + "-" + batch, word));
        }
        return RemoteResponse.buildSuccess(null);
    }

    private String checkParams(XhsBuildNoteRequest request) {
        if (StringUtils.isBlank(request.getBatch())) {
            return "批次号为空";
        }
        if (BizTypeEnum.getByBizCode(request.getBizCode()) == null) {
            return "未知业务场景";
        }
        if (MapUtils.isEmpty(request.getParams())) {
            return "业务生产参数为空";
        }
        if (StringUtils.isBlank(request.getBizCode())) {
            return "无回调通知";
        }
        return "";
    }
}
