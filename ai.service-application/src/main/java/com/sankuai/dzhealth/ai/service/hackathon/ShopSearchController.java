package com.sankuai.dzhealth.ai.service.hackathon;

import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import com.google.common.collect.Lists;
import com.sankuai.dzhealth.ai.service.agent.domain.model.SupplyRecommendModel;
import com.sankuai.dzhealth.ai.service.domain.utils.JsonUtils;
import com.sankuai.medicalcosmetology.mainpath.listingapi.common.ResponseDTO;
import com.sankuai.medicalcosmetology.mainpath.listingapi.dto.FillInfoDTO;
import com.sankuai.medicalcosmetology.mainpath.listingapi.dto.GoodsRecallDTO;
import com.sankuai.medicalcosmetology.mainpath.listingapi.dto.ReCallSortIdsDTO;
import com.sankuai.medicalcosmetology.mainpath.listingapi.dto.ShopGoodsRecallDTO;
import com.sankuai.medicalcosmetology.mainpath.listingapi.request.*;
import com.sankuai.medicalcosmetology.mainpath.listingapi.service.ListingFacade;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> youtianyu
 * @version : 0.1
 * @since : 2025/9/11
 */
@Slf4j
@RestController
public class ShopSearchController {

    private static final long USER_ID = 80987486L;

    private static final String UUID = "0000000000000693059EE4CE84BDEBA54741CBD4D066CA174105041659501544";

    @Autowired
    private ListingFacade listingFacade;

    @GetMapping(value = "/hackathon/search", produces = MediaType.APPLICATION_JSON_VALUE)
    public List<FillInfoDTO> analysis(@RequestParam("lng") Double lng,
                                      @RequestParam("lat") Double lat,
                                      @RequestParam("queryStr") String queryStr) {
        Transaction transaction = Cat.newTransaction("hackathon", "search");
        transaction.setSuccessStatus();
        try {
            lng = lng == null ? 116.488126 : lng;
            lat = lat == null ? 40.008776 : lat;

            String templateKey = Lion.getString(Environment.getAppName(), "health.recommend.list.template.key", "111");
            ReCallSortIdsQry reCallSortIdsQry = new ReCallSortIdsQry();
            reCallSortIdsQry.setUserId(USER_ID);
            reCallSortIdsQry.setPlatform(2);
            reCallSortIdsQry.setCityId(1);
            reCallSortIdsQry.setQueryStr(queryStr);
            reCallSortIdsQry.setTemplateKey(templateKey);
            reCallSortIdsQry.setUuid(UUID);
            reCallSortIdsQry.setSortType(SupplyRecommendModel.ShopSortType.DEFAULT.getCode());
            reCallSortIdsQry.setLat(lng);
            reCallSortIdsQry.setLng(lat);
            reCallSortIdsQry.setPoiBackCateId("20423");
            reCallSortIdsQry.setPageNum(0);
            reCallSortIdsQry.setPageSize(3);
            ResponseDTO<ReCallSortIdsDTO> response = listingFacade.reCallSortIds(reCallSortIdsQry);
            if (!response.isSuccess() ||
                response.getData() == null ||
                CollectionUtils.isEmpty(response.getData().getShopGoodsRecallDTOs())) {
                return new ArrayList<>();
            }

            List<ShopGoodsRecallDTO> shopGoodsRecallDTOs = response.getData().getShopGoodsRecallDTOs();
            List<Long> shopIds =
                    shopGoodsRecallDTOs.stream().map(ShopGoodsRecallDTO::getShopId).collect(Collectors.toList());

            // 批量获取商户详情信息
            return getShopFillInfoMap(shopIds, shopGoodsRecallDTOs, lng, lat);
        } catch (Exception e) {
            transaction.setStatus(e);
            return new ArrayList<>();
        } finally {
            transaction.complete();
        }
    }

    /**
     * 获取商户详情信息映射
     */
    private List<FillInfoDTO> getShopFillInfoMap(List<Long> shopIds,
                                                 List<ShopGoodsRecallDTO> shopGoodsRecallDTOs,
                                                 Double lng,
                                                 Double lat) {
        Map<Long, ShopGoodsRecallDTO> shopGoodsRecallDTOMap = shopGoodsRecallDTOs.stream()
                .collect(Collectors.toMap(ShopGoodsRecallDTO::getShopId, Function.identity(), (a, b) -> a));

        // 构建批量FillInfo请求
        List<com.sankuai.medicalcosmetology.mainpath.listingapi.request.ShopQry> batchShopQryList = new ArrayList<>();
        List<GoodsQry> batchGoodsQryList = new ArrayList<>();

        Map<Long, Long> shop2GoodsMap =
                buildBatchQueries(shopIds, shopGoodsRecallDTOMap, batchShopQryList, batchGoodsQryList);

        // 批量调用fillInfo
        FillInfoQry fillInfoQry = new FillInfoQry();
        fillInfoQry.setShopQryList(batchShopQryList);
        fillInfoQry.setGoodsQryList(batchGoodsQryList);
        String templateKey =
                Lion.getString(Environment.getAppName(), "health.recommend.list.template.key", "ListingShow");
        fillInfoQry.setTemplateKey(templateKey);
        fillInfoQry.setUserId(USER_ID);
        fillInfoQry.setPlatform(2);
        fillInfoQry.setCityId(10);
        fillInfoQry.setAppVersion("12.41.403");
        fillInfoQry.setOs("ios");
        fillInfoQry.setUuid(UUID);
        fillInfoQry.setLat(lat);
        fillInfoQry.setLng(lng);

        ResponseDTO<FillInfoDTO> batchFillInfoResponse = listingFacade.fillInfo(fillInfoQry);
        log.info("[Listing]batchFillInfoResponse={},req={}", JsonUtils.toJsonString(batchFillInfoResponse),
                JsonUtils.toJsonString(fillInfoQry));

        return buildShopFillInfoMap(batchFillInfoResponse, shop2GoodsMap);
    }

    private ShopGoodsRecallDTO createDefaultShopGoodsRecallDTO(Long shopId, SupplyRecommendModel supplyRecommendModel) {
        ShopGoodsRecallDTO shopGoodsRecallDTO = new ShopGoodsRecallDTO();
        shopGoodsRecallDTO.setShopId(shopId);
        GoodsRecallDTO goodsRecallDTO = new GoodsRecallDTO();
        if (StringUtils.isNotBlank(supplyRecommendModel.getGoodsId())) {
            goodsRecallDTO.setGoodsId(NumberUtils.toLong(supplyRecommendModel.getGoodsId()));
            goodsRecallDTO.setGoodsType(supplyRecommendModel.getGoodsType());
            shopGoodsRecallDTO.setShopGoodsRecallDTOs(Lists.newArrayList(goodsRecallDTO));
        }
        return shopGoodsRecallDTO;
    }

    private Map<Long, Long> buildBatchQueries(List<Long> shopIds,
                                              Map<Long, ShopGoodsRecallDTO> shopGoodsRecallDTOMap,
                                              List<ShopQry> batchShopQryList,
                                              List<GoodsQry> batchGoodsQryList) {
        Map<Long, Long> shop2GoodsMap = new HashMap<>();
        for (Long shopId : shopIds) {
            ShopGoodsRecallDTO shopGoodsRecallDTO;
            shopGoodsRecallDTO = shopGoodsRecallDTOMap.get(shopId);

            // 添加shop查询
            ShopQry shopQry = new ShopQry();
            shopQry.setShopId(shopId);
            batchShopQryList.add(shopQry);

            // 添加goods查询并维护映射关系
            if (CollectionUtils.isNotEmpty(shopGoodsRecallDTO.getShopGoodsRecallDTOs())) {
                GoodsRecallDTO goodsRecallDTO = shopGoodsRecallDTO.getShopGoodsRecallDTOs().get(0);
                GoodsQry goodsQry = new GoodsQry();
                goodsQry.setGoodsId(goodsRecallDTO.getGoodsId());
                goodsQry.setGoodsType(goodsRecallDTO.getGoodsType());
                GoodsShopQry goodsShopQry = new GoodsShopQry();
                goodsShopQry.setShopId(shopId);
                goodsQry.setShopQry(goodsShopQry);
                batchGoodsQryList.add(goodsQry);

                // 维护商户ID到商品ID的映射
                shop2GoodsMap.put(shopId, goodsRecallDTO.getGoodsId());
            }
        }
        return shop2GoodsMap;
    }

    /**
     * 构建商户详情信息映射
     */
    private List<FillInfoDTO> buildShopFillInfoMap(ResponseDTO<FillInfoDTO> batchFillInfoResponse,
                                                   Map<Long, Long> shop2GoodsMap) {
        List<FillInfoDTO> shopId2FillInfoMap = new ArrayList<>();

        if (!batchFillInfoResponse.isSuccess() || batchFillInfoResponse.getData() == null) {
            return new ArrayList<>();
        }

        FillInfoDTO batchFillInfoDTO = batchFillInfoResponse.getData();

        // 构建商品ID到商户ID的反向映射（支持一个商品对应多个商户）
        Map<Long, List<Long>> goods2ShopsMap = new HashMap<>();
        for (Map.Entry<Long, Long> entry : shop2GoodsMap.entrySet()) {
            Long shopId = entry.getKey();
            Long goodsId = entry.getValue();
            goods2ShopsMap.computeIfAbsent(goodsId, k -> new ArrayList<>()).add(shopId);
        }

        // 将商品按商户ID分组
        Map<Long, List<com.sankuai.medicalcosmetology.mainpath.listingapi.dto.GoodsInfoDTO>> goodsByShopMap =
                new HashMap<>();
        if (CollectionUtils.isNotEmpty(batchFillInfoDTO.getGoodsInfoDTOs())) {
            for (com.sankuai.medicalcosmetology.mainpath.listingapi.dto.GoodsInfoDTO goods : batchFillInfoDTO.getGoodsInfoDTOs()) {
                List<Long> shopIds = goods2ShopsMap.get(goods.getGoodsId());
                if (CollectionUtils.isNotEmpty(shopIds)) {
                    for (Long shopId : shopIds) {
                        goodsByShopMap.computeIfAbsent(shopId, k -> new ArrayList<>()).add(goods);
                    }
                }
            }
        }

        // 为每个商户构建独立的FillInfoDTO
        if (CollectionUtils.isNotEmpty(batchFillInfoDTO.getShopInfos())) {
            for (com.sankuai.medicalcosmetology.mainpath.listingapi.dto.ShopInfoDTO shopInfo : batchFillInfoDTO.getShopInfos()) {
                FillInfoDTO individualFillInfo = new FillInfoDTO();
                individualFillInfo.setShopInfos(Lists.newArrayList(shopInfo));

                // 设置该商户对应的商品信息
                List<com.sankuai.medicalcosmetology.mainpath.listingapi.dto.GoodsInfoDTO> shopGoods =
                        goodsByShopMap.get(shopInfo.getShopId());
                if (CollectionUtils.isNotEmpty(shopGoods)) {
                    // 直接修改商品信息中的goodsRedirectUrl
                    for (com.sankuai.medicalcosmetology.mainpath.listingapi.dto.GoodsInfoDTO goods : shopGoods) {
                        String originalUrl = goods.getGoodsRedirectUrl();
                        String updatedUrl = replaceShopIdInUrl(originalUrl, shopInfo.getShopId());
                        goods.setGoodsRedirectUrl(updatedUrl);
                    }
                    individualFillInfo.setGoodsInfoDTOs(shopGoods);
                }

                shopId2FillInfoMap.add(individualFillInfo);
            }
        }

        return shopId2FillInfoMap;
    }

    /**
     * 替换URL中的shopid参数值
     *
     * @param originalUrl 原始URL
     * @param newShopId   新的shopid值
     * @return 替换后的URL
     */
    private String replaceShopIdInUrl(String originalUrl, Long newShopId) {
        if (originalUrl == null || newShopId == null) {
            return originalUrl;
        }

        try {
            // 使用正则表达式替换shopid参数
            // 匹配 shopid=数字 的模式
            String pattern = "shopid=\\d+";
            String replacement = "shopid=" + newShopId;
            return originalUrl.replaceFirst(pattern, replacement);
        } catch (Exception e) {
            log.warn("Failed to replace shopid in URL: {}, error: {}", originalUrl, e.getMessage());
            return originalUrl;
        }
    }
}
