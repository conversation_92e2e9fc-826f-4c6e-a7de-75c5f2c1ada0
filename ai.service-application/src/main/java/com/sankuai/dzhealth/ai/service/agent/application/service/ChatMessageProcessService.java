package com.sankuai.dzhealth.ai.service.agent.application.service;

import com.meituan.mtrace.Tracer;
import com.sankuai.dzhealth.ai.service.agent.domain.context.MessageContext;
import com.sankuai.dzhealth.ai.service.agent.domain.memory.ChatHistoryMemory;
import com.sankuai.dzhealth.ai.service.agent.domain.repository.chatSession.ChatSessionMessageRepository;
import com.sankuai.dzhealth.ai.service.agent.domain.repository.chatSession.ChatSessionRepository;
import com.sankuai.dzhealth.ai.service.agent.domain.service.ChatTaskProcessService;
import com.sankuai.dzhealth.ai.service.agent.domain.utils.SseUtils;
import com.sankuai.dzhealth.ai.service.agent.infrastructure.model.MultiEvaluationRequest;
import com.sankuai.dzhealth.ai.service.domain.evaluation.MultiDialogueEvaluation;
import com.sankuai.dzhealth.ai.service.domain.evaluation.entity.EvaluationMessage;
import com.sankuai.dzhealth.ai.service.domain.utils.JsonUtils;
import com.sankuai.dzhealth.ai.service.domain.utils.context.RequestContext;
import com.sankuai.dzhealth.ai.service.domain.utils.context.RequestContextConstant;
import com.sankuai.dzhealth.ai.service.dto.SseAwareException;
import com.sankuai.dzhealth.ai.service.dto.stream.BufferMergedDTO;
import com.sankuai.dzhealth.ai.service.dto.stream.StreamEventDTO;
import com.sankuai.dzhealth.ai.service.enums.QuerySourceEnum;
import com.sankuai.dzhealth.ai.service.enums.StreamEventDataTypeEnum;
import com.sankuai.dzhealth.ai.service.enums.StreamEventErrorTypeEnum;
import com.sankuai.dzhealth.ai.service.enums.StreamEventTypeEnum;
import com.sankuai.dzhealth.ai.service.infrastructure.utils.UidUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.ai.chat.messages.Message;
import org.springframework.ai.chat.messages.MessageType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.IOException;
import java.io.Serializable;
import java.util.HashMap;
import java.util.List;

/**
 * @author:chenwei
 * @time: 2025/7/10 16:58
 * @version: 0.0.1
 */

@Slf4j
@Service
public class ChatMessageProcessService {


    @Autowired
    private UidUtils uidUtils;

    @Autowired
    private ChatTaskProcessService chatTaskProcessService;

    @Autowired
    private ChatSessionMessageRepository chatSessionMessageRepository;

    @Autowired
    private ChatSessionRepository chatSessionRepository;

    @Autowired
    private MultiDialogueEvaluation multiDialogueEvaluation;

    @Autowired
    private ChatHistoryMemory chatHistoryMemory;

    public boolean sendChatMessage(MessageContext messageContext) {

        SseEmitter sseEmitter = RequestContext.getAttribute(RequestContextConstant.SSE_EMITTER);

        if (sseEmitter == null) {
            throw new SseAwareException(StreamEventErrorTypeEnum.SSE_ERROR);
        }
        try {
            SseUtils.sendSseContent(sseEmitter, StreamEventTypeEnum.MESSAGE.getType(),
                    StreamEventDataTypeEnum.SESSION_ID.getType(), messageContext.getSessionId());
            SseUtils.sendSseContent(sseEmitter, StreamEventTypeEnum.MESSAGE.getType(),
                    StreamEventDataTypeEnum.MESSAGE_ID.getType(), messageContext.getMsgId());

            if (CollectionUtils.isNotEmpty(messageContext.getImgUrls())) {
                log.info("[img],id={}", Tracer.id());
            }

            // 用户消息插入
            chatSessionMessageRepository.insertMessage(messageContext.getMsgId(), messageContext.getSessionId(),
                    messageContext.getUserId(), messageContext.getPlatform(), MessageType.USER.getValue(),
                    StringUtils.isBlank(messageContext.getMsg()) ? (CollectionUtils.isEmpty(messageContext.getImgUrls()) ? "" : "![脸部照片](" + messageContext.getImgUrls().get(0)+ ")") : messageContext.getMsg(),
                    JsonUtils.toJsonString(messageContext.getExtra()));

            // 回复消息id
            String replyMsgId = String.valueOf(uidUtils.getNextId(UidUtils.CHAT_MESSAGE_LEAF_KEY));
            messageContext.setReplyMsgId(replyMsgId);

            SseUtils.sendSseContent(sseEmitter, StreamEventTypeEnum.MESSAGE.getType(),
                    StreamEventDataTypeEnum.REPLY_MESSAGE_ID.getType(), messageContext.getReplyMsgId());


            BufferMergedDTO bufferMergedDTO = chatTaskProcessService.process(messageContext);


            // 会话回复消息

            List<StreamEventDTO> reply = bufferMergedDTO.getStreamEventDTOS();
            chatSessionMessageRepository.insertMessage(replyMsgId, messageContext.getSessionId(),
                    messageContext.getUserId(), messageContext.getPlatform(), MessageType.ASSISTANT.getValue(), JsonUtils.toJsonString(reply), StringUtils.EMPTY);

            // 更新记忆
            MessageContext updateMessageContext = RequestContext.getAttribute(RequestContextConstant.MESSAGE_CONTEXT);
            if (updateMessageContext != null) {
                updateMemory(updateMessageContext);
            }
            

            try {
                MultiEvaluationRequest multiEvaluationRequest = buildMultiDialogueEvaluationRequest(messageContext);
                messageContext.setMultiEvaluationRequest(multiEvaluationRequest);
                updateMessageExtra(messageContext);
            } catch (Exception e) {
                log.error("evaluation error", e);
            }

        } catch (IOException e) {
            throw new SseAwareException(StreamEventErrorTypeEnum.SSE_ERROR);
        } catch (Exception e) {
            log.error("sendChatMessage error", e);
        }


        return true;
    }

    private void updateMessageExtra(MessageContext messageContext) {
        if (messageContext.getExtra() == null) {
            messageContext.setExtra(new HashMap<>());
        }
        messageContext.getExtra().put("multiEvaluationRequest", messageContext.getMultiEvaluationRequest());
        messageContext.getExtra().put("multiEvaluationRequests", (Serializable) messageContext.getMultiEvaluationRequests());
        chatSessionMessageRepository.updateExtra(messageContext.getMsgId(), messageContext.getUserId(), messageContext.getPlatform(), JsonUtils.toJsonString(messageContext.getExtra()));
    }

    private void updateMemory(MessageContext messageContext) {

        chatSessionMessageRepository.updateMemory(messageContext.getReplyMsgId(),
                messageContext.getUserId(), messageContext.getPlatform(), JsonUtils.toJsonString(messageContext.getMemoryModel()));

        chatSessionRepository.updateSession(messageContext.getSessionId(), null, null,
                JsonUtils.toJsonString(messageContext.getMemoryModel()), null);
    }


    public MultiEvaluationRequest buildMultiDialogueEvaluationRequest(MessageContext messageContext) {
        String querySource = messageContext.getQuerySource();
        QuerySourceEnum querySourceEnum = QuerySourceEnum.getByType(querySource);
        List<Message> messages = chatHistoryMemory.getAllMemory(messageContext.getSessionId());
        List<EvaluationMessage> evaluationMessages = messages.stream().map(ms -> new EvaluationMessage(ms.getMessageType(), ms.getText())).toList();
        String chatHistoryMessages = JsonUtils.toJsonString(evaluationMessages);
        MultiEvaluationRequest multiEvaluationRequest = MultiEvaluationRequest.builder()
                .bizScene(messageContext.getBizType())
                .sessionId(messageContext.getSessionId())
                .messageId(messageContext.getMsgId())
                // 端到端测试场景
                .modelScene("endToEnd")
                .context(chatHistoryMessages)
                .source(querySourceEnum.getCode())
                .build();
        return multiEvaluationRequest;
    }
}
