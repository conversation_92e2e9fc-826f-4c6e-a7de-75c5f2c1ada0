package com.sankuai.dzhealth.ai.service.hackathon;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dianping.rhino.Rhino;
import com.dianping.rhino.threadpool.DefaultThreadPoolProperties;
import com.dianping.rhino.threadpool.ThreadPool;
import com.meituan.mdp.boot.starter.config.annotation.MdpConfig;
import com.sankuai.dzhealth.ai.service.hackathon.analyze.AnalyzeService;
import com.sankuai.dzhealth.ai.service.hackathon.image.ImageUtils;
import com.sankuai.dzhealth.ai.service.hackathon.image.s3PlusUtils;
import com.sankuai.medicalcosmetology.mainpath.listingapi.service.ListingFacade;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR> youtianyu
 * @version : 0.1
 * @since : 2025/9/11
 */
@Slf4j
@RestController
public class HackathonController {

    public static final ThreadPool HACKATHON_POOL = Rhino.newThreadPool("HACKATHON_POOL",
            DefaultThreadPoolProperties.Setter().withCoreSize(100).withMaxSize(200).withMaxQueueSize(1000));

    private static final String BUCKET_NAME = "beautycontent-file";

    private static final String SCALE_PARAM = "@4096h_4096w_0e_1l";

    @Autowired
    private AnalyzeService analyzeService;

    private final RestTemplate restTemplate = new RestTemplate();

    @MdpConfig(key = "hackathon.faceApiSecret")
    private String faceApiSecret;

    @MdpConfig(key = "hackathon.faceApiKey")
    private String faceApiKey;

    @Autowired
    private ListingFacade listingFacade;

    @MdpConfig(key = "hackathon.cookie")
    private String cookie;

    @NotNull
    private static CompletableFuture<String> getUploadFuture(MultipartFile leftFace, String uuid) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                return ImageUtils.postHttpImage(leftFace.getBytes(), uuid).getOriginalLink();
            } catch (Exception e) {
                log.error("Failed to upload image to venus", e);
                return null;
            }
        }, HACKATHON_POOL.getExecutor());
    }

    @PostMapping(value = "/hackathon/analysis",
                 consumes = MediaType.MULTIPART_FORM_DATA_VALUE,
                 produces = MediaType.APPLICATION_JSON_VALUE)
    public Map<String, Object> analysis(@RequestParam("leftFace") MultipartFile leftFace,
                                        @RequestParam("frontFace") MultipartFile frontFace,
                                        @RequestParam("rightFace") MultipartFile rightFace) {

        Map<String, Object> result = new HashMap<>();

        String uuid = UUID.randomUUID().toString().replace("-", "");
        uuid = uuid.substring(uuid.length() - 10);
        try {
            String s = s3PlusUtils.putObjectExample(BUCKET_NAME, uuid + leftFace.getOriginalFilename(),
                    leftFace.getBytes());
            System.out.println(s);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        // 1. 上传图片获取URL
        CompletableFuture<String> leftUploadFuture = getUploadFuture(leftFace, uuid + "raw_left.jpg");

        CompletableFuture<String> frontUploadFuture = getUploadFuture(frontFace, uuid + "raw_front.jpg");

        CompletableFuture<String> rightUploadFuture = getUploadFuture(rightFace, uuid + "raw_right.jpg");

        String leftUrl = leftUploadFuture.join();
        String frontUrl = frontUploadFuture.join();
        String rightUrl = rightUploadFuture.join();

        if (StringUtils.isBlank(leftUrl) || StringUtils.isBlank(frontUrl) || StringUtils.isBlank(rightUrl)) {
            return result;
        }

        // 2. 并行调用抠图服务
        leftUrl = leftUrl + SCALE_PARAM;
        frontUrl = frontUrl + SCALE_PARAM;
        rightUrl = rightUrl + SCALE_PARAM;

        result.put("leftUrl", leftUrl);
        result.put("frontUrl", frontUrl);
        result.put("rightUrl", rightUrl);

        CompletableFuture<String> leftMattingFuture = getMattingFuture(leftUrl, uuid + "left.jpg");
        CompletableFuture<String> frontMattingFuture = getMattingFuture(frontUrl, uuid + "front.jpg");
        CompletableFuture<String> rightMattingFuture = getMattingFuture(rightUrl, uuid + "right.jpg");

        // 3. 收集抠图结果
        String finalLeftUrl = leftMattingFuture.join();
        String finalFrontUrl = frontMattingFuture.join();
        String finalRightUrl = rightMattingFuture.join();
        result.put("finalLeftUrl", finalLeftUrl);
        result.put("finalFrontUrl", finalFrontUrl);
        result.put("finalRightUrl", finalRightUrl);

        // 4.1. 并行调用分析
        AnalyzeService.FaceFeaturesPromptResult analyzeResult = analyzeService.analyze(
                null, finalFrontUrl, uuid
        );

        // 4.2 调用Face++ 3D人脸重建服务
        d3d(finalLeftUrl, finalFrontUrl, finalRightUrl, uuid, result);

        return result;
    }

    @NotNull
    private CompletableFuture<String> getMattingFuture(String body, String fileName) {
        if (StringUtils.isBlank(body)) {
            return CompletableFuture.completedFuture(null);
        }
        return CompletableFuture.supplyAsync(() -> {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("Accept", "application/json, text/plain, */*");
            headers.set("Cookie", cookie);
            JSONObject requestBody = new JSONObject();
            requestBody.put("src", body);
            requestBody.put("serviceInterface", "com.meituan.horus.service.MapAIMattingPredict#getResult");
            HttpEntity<JSONObject> entity = new HttpEntity<>(requestBody, headers);

            try {
                ResponseEntity<String> response =
                        restTemplate.exchange("https://horus.sankuai.com/horus-api/thriftapi/common_generation_call",
                                HttpMethod.POST, entity, String.class);

                if (response.getBody() != null) {
                    JSONObject bodyJson = JSON.parseObject(response.getBody());
                    JSONObject dataJson = bodyJson.getJSONObject("data");
                    if (dataJson != null) {
                        String base64 = dataJson.getString("segmentation_result");
                        byte[] originalImageData = Base64.getDecoder().decode(base64);
                        // Convert PNG with transparency to JPG with white background
                        byte[] jpgImageData = ImageUtils.convertPngToJpgWithWhiteBackground(originalImageData);
                        return ImageUtils.postHttpImage(jpgImageData, fileName).getOriginalLink();
                    }
                }
            } catch (Exception e) {
                log.error("Failed to call matting service for url: {}", body, e);
            }
            return null;
        }, HACKATHON_POOL.getExecutor());
    }

    private void d3d(String finalLeftUrl,
                     String finalFrontUrl,
                     String finalRightUrl,
                     String uuid,
                     Map<String, Object> result) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.MULTIPART_FORM_DATA);

        MultiValueMap<String, String> body = new LinkedMultiValueMap<>();
        body.add("api_key", faceApiKey);
        body.add("api_secret", faceApiSecret);
        body.add("image_url_1", finalLeftUrl);
        body.add("image_url_2", finalFrontUrl);
        body.add("image_url_3", finalRightUrl);
        body.add("texture", "1");
        body.add("mtl", "1");

        HttpEntity<MultiValueMap<String, String>> requestEntity = new HttpEntity<>(body, headers);

        ResponseEntity<String> responseEntity =
                restTemplate.exchange("https://api-cn.faceplusplus.com/facepp/v1/3dface", HttpMethod.POST,
                        requestEntity, String.class);

        JSONObject facePlusPlusResponse = JSON.parseObject(responseEntity.getBody());

        // 6. 解码Base64内容
        if (facePlusPlusResponse != null && facePlusPlusResponse.getString("error_message") == null) {
            byte[] objFile = Base64.getDecoder().decode(facePlusPlusResponse.getString("obj_file"));
            byte[] textureImg = Base64.getDecoder().decode(facePlusPlusResponse.getString("texture_img"));
            byte[] mtlFile = Base64.getDecoder().decode(facePlusPlusResponse.getString("mtl_file"));

            CompletableFuture<String> objUrlFuture = CompletableFuture.supplyAsync(
                    () -> s3PlusUtils.putObjectExample(BUCKET_NAME, uuid + "face.obj", objFile),
                    HACKATHON_POOL.getExecutor());
            CompletableFuture<String> textureUrlFuture = CompletableFuture.supplyAsync(
                    () -> s3PlusUtils.putObjectExample(BUCKET_NAME, uuid + "texture.jpg", textureImg),
                    HACKATHON_POOL.getExecutor());
            CompletableFuture<String> mtlUrlFuture = CompletableFuture.supplyAsync(
                    () -> s3PlusUtils.putObjectExample(BUCKET_NAME, uuid + "face.mtl", mtlFile),
                    HACKATHON_POOL.getExecutor());

            CompletableFuture.allOf(objUrlFuture, textureUrlFuture, mtlUrlFuture).join();

            result.put("obj_file_url", objUrlFuture.join());
            result.put("texture_img_url", textureUrlFuture.join());
            result.put("mtl_file_url", mtlUrlFuture.join());
        }
    }

}
