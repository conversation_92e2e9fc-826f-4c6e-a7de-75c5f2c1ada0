package com.sankuai.dzhealth.ai.service.application.facade;

import com.alibaba.fastjson.JSON;
import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.meituan.mdp.boot.starter.thrift.annotation.MdpThriftServer;
import com.sankuai.dzhealth.ai.service.api.RemoteResponse;
import com.sankuai.dzhealth.ai.service.api.ThinkingService;
import com.sankuai.dzhealth.ai.service.domain.model.thinking.SequentialThought;
import com.sankuai.dzhealth.ai.service.domain.service.thinking.ThinkingEngineService;
import com.sankuai.dzhealth.ai.service.dto.ThinkingSessionDTO;
import com.sankuai.dzhealth.ai.service.dto.ThinkingStepDTO;
import com.sankuai.dzhealth.ai.service.infrastructure.repository.deepsearch.DeepSearchResultRepository;
import com.sankuai.dzhealth.ai.service.request.ThinkingSessionRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 思考引擎RPC服务实现
 */
@MdpThriftServer
@Slf4j
public class ThinkingServiceImpl implements ThinkingService {

    @Autowired
    private ThinkingEngineService thinkingEngineService;

    @Autowired
    private DeepSearchResultRepository deepSearchResultRepository;

    @Override
    public RemoteResponse<ThinkingSessionDTO> createAndExecuteThinking(ThinkingSessionRequest request) {
        if (request == null || StringUtils.isBlank(request.getQuery())) {
            return RemoteResponse.buildFail("参数错误: 查询内容不能为空");
        }
        
        Transaction transaction = Cat.newTransaction(getClass().getSimpleName(), "createAndExecuteThinking");
        try {
            // 构建业务参数
            Map<String, Object> businessParams = new HashMap<>();
            if (request.getBusinessParams() != null) {
                businessParams.putAll(request.getBusinessParams());
            }
            
            // 添加元数据参数
            if (request.getMetadata() != null && !request.getMetadata().isEmpty()) {
                for (Map.Entry<String, String> entry : request.getMetadata().entrySet()) {
                    businessParams.put(entry.getKey(), entry.getValue());
                }
            }
            
            // 创建思考会话
            Long sessionId = thinkingEngineService.createThinkingSession(request.getQuery(), businessParams);
            
            // 执行思考过程
            Map<String, Object> result = thinkingEngineService.executeThinking(sessionId);
            
            // 存储深度搜索结果
            String businessSource = (String) businessParams.getOrDefault("businessSource", "default");
            String query = (String) result.get("query");
            String finalAnswer = (String) result.get("finalAnswer");
            @SuppressWarnings("unchecked")
            List<String> sourceUrls = (List<String>) result.get("sourceUrls");
            BigDecimal confidenceScore = null;
            Object conf = result.get("confidenceScore");
            if (conf != null) confidenceScore = new BigDecimal(conf.toString());
            
            Long recordId = deepSearchResultRepository.save(
                businessSource,
                query,
                finalAnswer,
                sourceUrls,
                confidenceScore
            );
            result.put("recordId", recordId);
            
            // 转换为DTO
            ThinkingSessionDTO sessionDTO = convertToThinkingSessionDTO(result);
            
            transaction.setSuccessStatus();
            return RemoteResponse.buildSuccess(sessionDTO);
        } catch (Exception e) {
            log.error("执行思考过程失败, request={}", JSON.toJSONString(request), e);
            transaction.setStatus(e);
            return RemoteResponse.buildFail("执行思考过程失败: " + e.getMessage());
        } finally {
            transaction.complete();
        }
    }

    @Override
    public RemoteResponse<ThinkingSessionDTO> getThinkingResult(Long sessionId) {
        if (sessionId == null) {
            return RemoteResponse.buildFail("参数错误: 会话ID不能为空");
        }
        
        Transaction transaction = Cat.newTransaction(getClass().getSimpleName(), "getThinkingResult");
        try {
            Map<String, Object> result = thinkingEngineService.getThinkingResult(sessionId);
            
            // 转换为DTO
            ThinkingSessionDTO sessionDTO = convertToThinkingSessionDTO(result);
            
            transaction.setSuccessStatus();
            return RemoteResponse.buildSuccess(sessionDTO);
        } catch (Exception e) {
            log.error("获取思考结果失败, sessionId={}", sessionId, e);
            transaction.setStatus(e);
            return RemoteResponse.buildFail("获取思考结果失败: " + e.getMessage());
        } finally {
            transaction.complete();
        }
    }

    
    /**
     * 将思考结果转换为DTO
     *
     * @param result 思考结果
     * @return 思考会话DTO
     */
    private ThinkingSessionDTO convertToThinkingSessionDTO(Map<String, Object> result) {
        if (result == null) {
            return null;
        }
        
        ThinkingSessionDTO.ThinkingSessionDTOBuilder builder = ThinkingSessionDTO.builder()
                .id((Long) result.get("sessionId"))
                .query((String) result.get("query"))
                .status((String) result.get("status"))
                .finalAnswer((String) result.get("finalAnswer"));
        
        // 处理思考步骤
        @SuppressWarnings("unchecked")
        List<SequentialThought> thoughts = (List<SequentialThought>) result.get("thoughts");
        
        if (thoughts != null && !thoughts.isEmpty()) {
            List<ThinkingStepDTO> thoughtDTOs = thoughts.stream()
                    .map(this::convertToThinkingStepDTO)
                    .collect(Collectors.toList());
            
            builder.thoughts(thoughtDTOs);
        }
        
        // 处理置信度
        if (result.get("confidenceScore") instanceof BigDecimal) {
            BigDecimal confidenceScore = (BigDecimal) result.get("confidenceScore");
            builder.confidenceScore(confidenceScore != null ? confidenceScore.doubleValue() : null);
        } else if (result.get("confidenceScore") instanceof Double) {
            builder.confidenceScore((Double) result.get("confidenceScore"));
        }
        
        return builder.build();
    }
    
    /**
     * 将思考步骤转换为DTO
     *
     * @param thought 思考步骤
     * @return 思考步骤DTO
     */
    private ThinkingStepDTO convertToThinkingStepDTO(SequentialThought thought) {
        if (thought == null) {
            return null;
        }
        
        ThinkingStepDTO.ThinkingStepDTOBuilder builder = ThinkingStepDTO.builder()
                .thought(thought.getThought())
                .thoughtNumber(thought.getThoughtNumber())
                .totalThoughts(thought.getTotalThoughts())
                .isRevision(thought.getIsRevision())
                .revisesThought(thought.getRevisesThought())
                .branchFromThought(thought.getBranchFromThought())
                .branchId(thought.getBranchId())
                .needsMoreThoughts(thought.getNeedsMoreThoughts())
                .nextThoughtNeeded(thought.getNextThoughtNeeded())
                .searchResults(thought.getSearchResults());
        
        // 处理置信度
        if (thought.getConfidenceScore() != null) {
            builder.confidenceScore(thought.getConfidenceScore().doubleValue());
        }
        
        return builder.build();
    }
} 