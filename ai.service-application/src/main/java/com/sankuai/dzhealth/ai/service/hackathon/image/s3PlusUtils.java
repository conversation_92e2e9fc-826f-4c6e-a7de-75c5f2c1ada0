package com.sankuai.dzhealth.ai.service.hackathon.image;

import com.amazonaws.AmazonClientException;
import com.amazonaws.AmazonServiceException;
import com.amazonaws.ClientConfiguration;
import com.amazonaws.Protocol;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3KmsClient;
import com.amazonaws.services.s3.S3ClientOptions;
import com.amazonaws.services.s3.exceptions.GetS3CredentialFailedAfterRetryException;
import com.dianping.lion.Environment;

import java.io.ByteArrayInputStream;

/**
 * <AUTHOR> youtianyu
 * @version : 0.1
 * @since : 2025/9/11
 */
public class s3PlusUtils {

    private static final String endpoint =
            Environment.isTestEnv() ? "https://msstest.vip.sankuai.com" : "https://s3plus.meituan.net";

    private static final String cdn =
            Environment.isTestEnv() ? "https://msstest.sankuai.com" : "https://mss.vip.sankuai.com";

    private static volatile AmazonS3 s3client;

    public static String putObjectExample(String bucketName, String objectName, byte[] content) {
        try {
            getS3client().putObject(bucketName, objectName, new ByteArrayInputStream(content), null);
            return cdn + "/" + bucketName + "/" + objectName;
        } catch (AmazonServiceException ase) {
            throw new RuntimeException("存储服务端处理异常", ase);
        } catch (AmazonClientException ace) {
            throw new RuntimeException("客户端处理异常", ace);
        } catch (GetS3CredentialFailedAfterRetryException e) {
            throw new RuntimeException(e);
        }
    }

    private static AmazonS3 getS3client() throws GetS3CredentialFailedAfterRetryException {
        if (s3client == null) {
            synchronized (s3PlusUtils.class) {
                if (s3client == null) {
                    ClientConfiguration configuration = new ClientConfiguration();
                    configuration.setProtocol(Protocol.HTTPS);

                    //设置客户端生成的http请求hos格式，目前只支持path type的格式，不支持bucket域名的格式
                    S3ClientOptions s3ClientOptions = new S3ClientOptions();
                    // 目前s3只支持path style,下面选项需要设置为true
                    s3ClientOptions.setPathStyleAccess(true);

                    //生成云存储api client
                    s3client =
                            new AmazonS3KmsClient(endpoint, Environment.getAppName(), configuration, s3ClientOptions);
                }
            }
        }
        return s3client;
    }
}
