package com.sankuai.dzhealth.ai.service.agent.application.service;


import com.alibaba.fastjson.JSON;
import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.dianping.lion.client.util.StringUtils;
import com.meituan.mdp.boot.starter.thrift.annotation.MdpThriftServer;
import com.sankuai.dzhealth.ai.service.agent.api.AppointMentService;
import com.sankuai.dzhealth.ai.service.agent.domain.context.MessageContext;
import com.sankuai.dzhealth.ai.service.agent.domain.mafka.AppointmentAiCancelProducer;
import com.sankuai.dzhealth.ai.service.agent.domain.model.AppointmentReturn;
import com.sankuai.dzhealth.ai.service.agent.domain.model.ShopRecommendation;
import com.sankuai.dzhealth.ai.service.agent.domain.process.card.data.AppointmentCardData;
import com.sankuai.dzhealth.ai.service.agent.domain.service.AppointmentService;
import com.sankuai.dzhealth.ai.service.agent.domain.service.BusinessHourService;
import com.sankuai.dzhealth.ai.service.agent.domain.service.RecommendService;
import com.sankuai.dzhealth.ai.service.agent.domain.tools.GeocodingTool;
import com.sankuai.dzhealth.ai.service.agent.domain.tools.PoiReviewService;
import com.sankuai.dzhealth.ai.service.agent.domain.tools.UserPhoneService;
import com.sankuai.dzhealth.ai.service.agent.domain.tools.param.GeocodingDescRequest;
import com.sankuai.dzhealth.ai.service.agent.domain.tools.param.GeocodingRequest;
import com.sankuai.dzhealth.ai.service.agent.dto.AppointmentRequestDTO;
import com.sankuai.dzhealth.ai.service.agent.infrastructure.cache.PhoneEncryptionUtils;
import com.sankuai.dzhealth.ai.service.agent.infrastructure.repository.appointment.AppointmentInfoRepository;
import com.sankuai.dzhealth.ai.service.agent.request.ShopQry;
import com.sankuai.dzhealth.ai.service.api.RemoteResponse;
import com.sankuai.dzhealth.ai.service.request.common.BasicParam;
import com.sankuai.dzim.pilot.api.data.aiphonecall.AIPhoneCallBackDTO;
import com.sankuai.leads.common.thrift.enums.PlatformEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;
import java.util.List;

@MdpThriftServer
@Slf4j
public class AppointMentServiceImpl implements AppointMentService {

    @Autowired
    private UserPhoneService userPhoneService;

    @Autowired
    private GeocodingTool geocodingTool;

    @Autowired
    private PoiReviewService poiReviewService;

    @Autowired
    private AppointmentService appointmentService;

    @Autowired
    private PhoneEncryptionUtils encryptService;

    @Autowired
    private AppointmentInfoRepository appointmentInfoRepository;

    @Autowired
    private RecommendService recommendService;

    @Autowired
    private BusinessHourService businessService;

    @Autowired
    private AppointmentAiCancelProducer appointmentAiCancelProducer;

    private final static String CAT_TYPE = AppointMentService.class.getSimpleName();

    @Override
    public String queryUserPhoneNum(Long userId) {
        return userPhoneService.getUserPhoneById(userId);
    }

    @Override
    public String parseAddressByLatLng(Double lng, Double lat) {
        if (lng == null || lat == null) {
            return null;
        }
        GeocodingRequest request = new GeocodingRequest();
        request.setLat(lat);
        request.setLng(lng);
        return geocodingTool.getAddressByLocation(request);
    }

    @Override
    public String parseLatLngByAddress(String cityId, String address) {
        if (StringUtils.isBlank(cityId) || StringUtils.isBlank(address)) {
            return null;
        }
        GeocodingDescRequest request = new GeocodingDescRequest();
        request.setCityId(cityId);
        request.setAddress(address);
        return geocodingTool.getLocationByAddress(request);
    }



    @Override
    public RemoteResponse<String> pushAppointment(String input, String msgId) {
        Transaction transaction = Cat.newTransaction(CAT_TYPE, "pushAppointment");
        try {
            log.info("pushAppointment input:{}, msgId:{}", input, msgId);
            MessageContext messageContext = getBaseInfo(msgId);
            AppointmentCardData card = appointmentService.pushAppointment(input, messageContext);
            if (card == null) {
                throw new IllegalArgumentException("推送预约卡片失败");
            }
            return RemoteResponse.buildSuccess(JSON.toJSONString(card));
        } catch (Exception e) {
            log.error("pushAppointment error, input:{}, msgId:{}", input, msgId, e);
            transaction.setStatus(e);
            return RemoteResponse.buildFail(e.getMessage());
        } finally {
            transaction.complete();
        }
    }

    @Override
    public RemoteResponse<String> confirmAppointment(AppointmentRequestDTO request) {
        Transaction transaction = Cat.newTransaction(CAT_TYPE, "confirmAppointment");
        try {
            log.info("confirmAppointment request:{}", JSON.toJSONString(request));
            MessageContext messageContext = getBaseInfo(request.getMsgId());
            AppointmentReturn result = appointmentService.confirmAppointment(request, messageContext.getBasicParam());
            if (result == null) {
                throw new IllegalArgumentException("确认预约失败");
            }
            return RemoteResponse.buildSuccess(JSON.toJSONString(result));
        } catch (Exception e) {
            log.error("confirmAppointment error, request:{}", request, e);
            transaction.setStatus(e);
            return RemoteResponse.buildFail(e.getMessage());
        } finally {
            transaction.complete();
        }
    }

    @Override
    public RemoteResponse<String> rescheduleAppointment(AppointmentRequestDTO request) {
        Transaction transaction = Cat.newTransaction(CAT_TYPE, "rescheduleAppointment");
        try {
            log.info("rescheduleAppointment request:{}", JSON.toJSONString(request));
            MessageContext messageContext = getBaseInfo(request.getMsgId());
            AppointmentReturn result = appointmentService.rescheduleAppointment(request, messageContext.getBasicParam());
            if (result == null) {
                throw new IllegalArgumentException("重新安排预约失败");
            }
            return RemoteResponse.buildSuccess(JSON.toJSONString(result));
        } catch (Exception e) {
            log.error("rescheduleAppointment error, request:{}", request, e);
            transaction.setStatus(e);
            return RemoteResponse.buildFail(e.getMessage());
        } finally {
            transaction.complete();
        }
    }

    @Override
    public RemoteResponse<String> cancelAppointment(AppointmentRequestDTO request) {
        Transaction transaction = Cat.newTransaction(CAT_TYPE, "cancelAppointment");
        try {
            log.info("cancelAppointment request:{}", JSON.toJSONString(request));
            AppointmentReturn result = appointmentService.cancelAppointment(request);
            if (result == null) {
                throw new IllegalArgumentException("取消预约失败");
            }
            return RemoteResponse.buildSuccess(JSON.toJSONString(result));
        } catch (Exception e) {
            log.error("cancelAppointment error, request:{}", request, e);
            transaction.setStatus(e);
            return RemoteResponse.buildFail(e.getMessage());
        } finally {
            transaction.complete();
        }
    }

    @Override
    public RemoteResponse<Boolean> aiAppointmentSignalISProcess(String callBackDTO) {
        Transaction transaction = Cat.newTransaction(CAT_TYPE, "aiAppointmentSignalISProcess");
        try {
            log.info("aiAppointmentSignalISProcess callBackDTO:{}", callBackDTO);
            AIPhoneCallBackDTO dto = JSON.parseObject(callBackDTO, AIPhoneCallBackDTO.class);
            if (dto == null) {
                throw new IllegalArgumentException("解析AI外呼回调数据失败");
            }
            appointmentService.aiAppointmentSignalISProcess(dto);
            return RemoteResponse.buildSuccess(true);
        } catch (Exception e) {
            log.error("aiAppointmentSignalISProcess error, callBackDTO:{}", callBackDTO, e);
            transaction.setStatus(e);
            return RemoteResponse.buildFail(e.getMessage());
        } finally {
            transaction.complete();
        }
    }

    @Override
    public RemoteResponse<String> cancelSuccessAppointByButton(AppointmentRequestDTO request) {
        Transaction transaction = Cat.newTransaction(CAT_TYPE, "cancelSuccessAppointByButton");
        try {
            log.info("cancelSuccessAppointByButton request:{}", JSON.toJSONString(request));
            AppointmentReturn result = appointmentService.cancelSuccessAppointByButton(request);
            if (result == null) {
                throw new IllegalArgumentException("按钮取消成功预约失败");
            }
            return RemoteResponse.buildSuccess(JSON.toJSONString(result));
        } catch (Exception e) {
            log.error("cancelSuccessAppointByButton error, request:{}", request, e);
            transaction.setStatus(e);
            return RemoteResponse.buildFail(e.getMessage());
        } finally {
            transaction.complete();
        }
    }

    @Override
    public RemoteResponse<Boolean> cancelSuccessAppointByMessage(Long leadId) {
        Transaction transaction = Cat.newTransaction(CAT_TYPE, "cancelSuccessAppointByMessage");
        try {
            log.info("cancelSuccessAppointByMessage leadId:{}", leadId);
            appointmentService.cancelSuccessAppointByMessage(leadId);
            return RemoteResponse.buildSuccess(true);
        } catch (Exception e) {
            log.error("cancelSuccessAppointByMessage error, leadId:{}", leadId, e);
            transaction.setStatus(e);
            return RemoteResponse.buildFail(e.getMessage());
        } finally {
            transaction.complete();
        }
    }

    @Override
    public RemoteResponse<String> reAppointment(AppointmentRequestDTO request) {
        Transaction transaction = Cat.newTransaction(CAT_TYPE, "reAppointment");
        try {
            log.info("reAppointment request:{}", JSON.toJSONString(request));
            AppointmentReturn result = appointmentService.reAppointment(request);
            if (result == null) {
                throw new IllegalArgumentException("再次预约失败");
            }
            return RemoteResponse.buildSuccess(JSON.toJSONString(result));
        } catch (Exception e) {
            log.error("reAppointment error, request:{}", request, e);
            transaction.setStatus(e);
            return RemoteResponse.buildFail(e.getMessage());
        } finally {
            transaction.complete();
        }
    }


    @Override
    public RemoteResponse<String> parseStringToDate(String dateStr) {
        Transaction transaction = Cat.newTransaction(CAT_TYPE, "parseStringToDate");
        try {
            log.info("parseStringToDate dateStr:{}", dateStr);
            Date date = appointmentService.parseStringToDate(dateStr);
            if (date == null) {
                throw new IllegalArgumentException("解析日期字符串失败");
            }
            return RemoteResponse.buildSuccess(date.toString());
        } catch (Exception e) {
            log.error("parseStringToDate error, dateStr:{}", dateStr, e);
            transaction.setStatus(e);
            return RemoteResponse.buildFail(e.getMessage());
        } finally {
            transaction.complete();
        }
    }

    @Override
    public RemoteResponse<String> testSelectShop(List<ShopQry> shopQryList, String msgId) {
        MessageContext messageContext = getBaseInfo(msgId);

        List<ShopRecommendation> ans = recommendService.selectShopAndRecommend(null, messageContext, "推荐祛痘商户", "");
        return RemoteResponse.buildSuccess(JSON.toJSONString(ans));
    }

    @Override
    public boolean testBusinessPoint(Long shopId) {
        return businessService.isBusinessHourByPoint(shopId, new Date());
    }

    @Override
    public boolean testBusinessRange(Long shopId, Date date, Date endDate) {
        return businessService.isBusinessHourByRange(shopId, date, endDate);
    }

    @Override
    public boolean testMessage(Long shopId, Long time) {
         appointmentAiCancelProducer.sendDelayMessage(String.valueOf(shopId), time);
          return true;
    }

    private MessageContext getBaseInfo(String msgId) {
        MessageContext messageContext = new MessageContext();
        messageContext.setUserId(2587202145L);
        messageContext.setMsg(msgId);
        messageContext.setPlatform(PlatformEnum.MT.getValue());
        BasicParam basicParam = new BasicParam();
        basicParam.setLat(121.528962);
        basicParam.setLng(31.271581);
        basicParam.setAppVersion("12.32.402");
        basicParam.setClientType("ios");
        basicParam.setUserCityId(1);
        basicParam.setUuid("0000000000000DAE2EDCF7C144F05BC4275D8582A5DB8A168641995004687179");
        messageContext.setBasicParam(basicParam);
        return messageContext;
    }


}
