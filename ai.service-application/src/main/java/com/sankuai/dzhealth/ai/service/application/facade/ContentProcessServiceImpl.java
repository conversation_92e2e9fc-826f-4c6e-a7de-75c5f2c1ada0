package com.sankuai.dzhealth.ai.service.application.facade;

import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.meituan.mdp.boot.starter.thrift.annotation.MdpThriftServer;
import com.sankuai.dzhealth.ai.service.api.ContentProcessService;
import com.sankuai.dzhealth.ai.service.api.RemoteResponse;
import com.sankuai.dzhealth.ai.service.domain.contenttransfer.service.MarkdownConvertService;
import com.sankuai.dzhealth.ai.service.dto.MarkdownResponseDTO;
import com.sankuai.dzhealth.ai.service.request.ContentTransferRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

/**
 * 内容转Markdown服务接入层实现
 *
 * 根据到店架构标准化规范，接入层职责：
 * - 接口协议适配
 * - 基础参数校验
 * - 请求转发
 * - 响应结果封装
 * - 异常处理
 * - 日志打点
 *
 * <AUTHOR> based on 到店架构标准化规范（2024版）
 */
@MdpThriftServer
@Service
@Slf4j
@RequiredArgsConstructor
public class ContentProcessServiceImpl implements ContentProcessService {

    private final MarkdownConvertService markdownConvertService;

    @Override
    public RemoteResponse<MarkdownResponseDTO> content2md(ContentTransferRequest request) {
        Transaction transaction = Cat.newTransaction(getClass().getSimpleName(), "convert");

        try {
            log.info("收到内容转换请求，内容长度: {}",
                    request != null && request.getOriginTextContent() != null
                    ? request.getOriginTextContent().length() : 0);

            // 基础参数校验
            RemoteResponse<MarkdownResponseDTO> validationResult = validateRequest(request);
            if (validationResult != null) {
                return validationResult;
            }

            // 请求转发到领域服务进行业务处理
            String markdown = markdownConvertService.convertToMarkdown(request.getOriginTextContent());
            MarkdownResponseDTO result = new MarkdownResponseDTO(markdown);

            log.info("内容转换完成，结果长度: {}", markdown.length());

            transaction.setSuccessStatus();
            return RemoteResponse.buildSuccess(result);

        } catch (IllegalArgumentException e) {
            log.warn("参数校验失败: {}", e.getMessage());
            transaction.setStatus(e);
            return RemoteResponse.buildIllegalArgument(e.getMessage());

        } catch (Exception e) {
            log.error("内容转换处理失败", e);
            transaction.setStatus(e);
            return RemoteResponse.buildFail("转换失败: " + e.getMessage());

        } finally {
            transaction.complete();
        }
    }

    /**
     * 请求参数校验
     *
     * @param request 请求对象
     * @return 校验失败时返回错误响应，校验通过返回null
     */
    private RemoteResponse<MarkdownResponseDTO> validateRequest(ContentTransferRequest request) {
        if (request == null) {
            log.warn("请求对象为空");
            return RemoteResponse.buildIllegalArgument("请求不能为空");
        }

        if (StringUtils.isBlank(request.getOriginTextContent())) {
            log.warn("请求内容为空");
            return RemoteResponse.buildIllegalArgument("originContent 不能为空");
        }

        // 可以添加更多校验逻辑，如内容长度限制等
        if (request.getOriginTextContent().length() > 100000) {
            log.warn("请求内容过长: {}", request.getOriginTextContent().length());
            return RemoteResponse.buildIllegalArgument("内容长度不能超过10万字符");
        }

        return null;
    }
}