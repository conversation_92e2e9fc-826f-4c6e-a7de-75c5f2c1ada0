package com.sankuai.dzhealth.ai.service.hackathon.analyze;

import com.alibaba.fastjson.JSONObject;
import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import com.dianping.rhino.Rhino;
import com.dianping.rhino.threadpool.DefaultThreadPoolProperties;
import com.dianping.rhino.threadpool.ThreadPool;
import com.meituan.mdp.boot.starter.config.annotation.MdpConfig;
import com.sankuai.dzhealth.ai.service.hackathon.image.ImageUtils;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.MultipartBody;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.apache.commons.lang3.StringUtils;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.model.ChatResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.CompletableFuture;

/**
 * @Author: guangyujie
 * @Date: 2025/9/11 18:02
 */
@Component
@Slf4j
public class AnalyzeService {

    public static final ThreadPool HACKATHON_ANALYZE_POOL = Rhino.newThreadPool("HACKATHON_ANALYZE_POOL",
            DefaultThreadPoolProperties.Setter().withCoreSize(100).withMaxSize(200).withMaxQueueSize(1000));

    @MdpConfig(key = "hackathon.faceApiSecret", value = "p6Qko8-NDAptyQwW9IYfZ5rtufBK-MyH")
    private final String faceApiSecret = "p6Qko8-NDAptyQwW9IYfZ5rtufBK-MyH";

    @MdpConfig(key = "hackathon.faceApiKey", value = "WUDWdHLgOhyZOJu-nF2gmfdqBHi6cz55")
    private String faceApiKey = "WUDWdHLgOhyZOJu-nF2gmfdqBHi6cz55";

    @Autowired
    private ChatClient analyzeChatClient;

    public FaceFeaturesPromptResult analyze(final String userRequest,
                                            final String finalFrontUrl,
                                            final String uuid) {
        CompletableFuture<AnalyzeFaceSkinResult> skinAnalyzeResultCF = analyzeFaceSkin(finalFrontUrl, uuid);
        CompletableFuture<FaceFeaturesPromptResult> faceSkinResultCF = skinAnalyzeResultCF
                .thenApply(result -> promptFaceSkin(userRequest, result));
        CompletableFuture<AnalyzeFaceFeaturesResult> facialFeaturesAnalyzeResultCF = analyzeFacialFeatures(finalFrontUrl, uuid);
        CompletableFuture<FaceFeaturesPromptResult> faceFeaturesResultCF = facialFeaturesAnalyzeResultCF
                .thenApply(result -> promptFacialFeatures(userRequest, result));
        FaceFeaturesPromptResult faceSkinResult = faceSkinResultCF.join();
        FaceFeaturesPromptResult faceFeaturesResult = faceFeaturesResultCF.join();

        StringBuilder noNeedToChange = new StringBuilder();
        Map<String, String> problem = new HashMap<>();
        Map<String, String> items = new HashMap<>();
        if (faceFeaturesResult != null) {
            noNeedToChange.append("脸型：").append(faceFeaturesResult.getNoNeedToChange()).append("\n");
            problem.putAll(faceFeaturesResult.getProblem());
            items.putAll(faceFeaturesResult.getItems());
        }
        if (faceSkinResult != null) {
            noNeedToChange.append("肤质：").append(faceSkinResult.getNoNeedToChange()).append("\n");
            problem.putAll(faceSkinResult.getProblem());
            items.putAll(faceSkinResult.getItems());
        }
        return new FaceFeaturesPromptResult(noNeedToChange.toString(), problem, items);
    }

    /**
     * 分析皮肤
     */
    public CompletableFuture<AnalyzeFaceSkinResult> analyzeFaceSkin(final String finalFrontUrl, final String uuid) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                OkHttpClient client = new OkHttpClient();
                String url = "https://api-cn.faceplusplus.com/facepp/v1/skinanalyze_advanced";
                MultipartBody requestBody = new MultipartBody.Builder().setType(MultipartBody.FORM).addFormDataPart("api_key", faceApiKey).addFormDataPart("api_secret", faceApiSecret).addFormDataPart("image_url", finalFrontUrl)
//                .addFormDataPart("left_side_image_url", "https://p0.meituan.net/shaitu/b67903a72e6d92edf971524528544671316354.jpg")
//                .addFormDataPart("right_side_image_url", "https://p0.meituan.net/shaitu/a6d5a432aecb8c208924bbfe4504b08f84516.jpg")
//                .addFormDataPart("return_maps", "red_area")
//                .addFormDataPart("return_marks", "")
                        .build();
                Request request = new Request.Builder().url(url).post(requestBody).build();
                try (Response response = client.newCall(request).execute()) {
                    if (response.code() != 200) {
                        throw new RuntimeException("请求失败:" + response.code() + ";msg:" + response.message());
                    }
                    String jsonStr = Objects.requireNonNull(response.body()).string();
                    return new AnalyzeFaceSkinResult(JSONObject.parseObject(jsonStr), SkinAnalyzeResultFormatUtils.parseSkinResult(jsonStr));
                }
            } catch (Exception e) {
                log.error("Failed to analyzeFaceSkin", e);
                return null;
            }
        }, HACKATHON_ANALYZE_POOL.getExecutor());
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AnalyzeFaceSkinResult {

        private JSONObject originalResult;

        private Map<String, String> itemResultInChinese;

    }

    /**
     * 皮肤分析
     */
    public FaceFeaturesPromptResult promptFaceSkin(final String userRequest,
                                                   final AnalyzeFaceSkinResult analyzeFaceSkinResult) {
        if (analyzeFaceSkinResult == null
                || analyzeFaceSkinResult.getOriginalResult() == null
                || analyzeFaceSkinResult.getItemResultInChinese() == null) {
            throw new IllegalArgumentException("无效脸型检测数据!!!");
        }
        String prompt = Lion.getString(Environment.getAppName(), "com.sankuai.dzhealth.ai.service.analyzeFaceSkin.prompt");
        prompt = prompt.replace("{{{userRequest}}}", Optional.ofNullable(userRequest).orElse("变好看一点"));
        prompt = prompt.replace("{{{userData}}}", analyzeFaceSkinResult.getOriginalResult().toJSONString());
        ChatResponse response = analyzeChatClient.prompt(prompt).call().chatResponse();
        String answer = response != null ? response.getResult().getOutput().getText() : null;
        if (answer == null) {
            throw new IllegalStateException("皮肤分析大模型返回值为空");
        }
        FaceFeaturesPromptResult result = JSONObject.parseObject(answer, FaceFeaturesPromptResult.class);
        result.checkParams();
        return result;
    }

    /**
     * 三庭五眼检测
     */
    public CompletableFuture<AnalyzeFaceFeaturesResult> analyzeFacialFeatures(final String finalFrontUrl, final String uuid) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                OkHttpClient client = new OkHttpClient();
                String url = "https://api-cn.faceplusplus.com/facepp/v1/facialfeatures";
                MultipartBody requestBody = new MultipartBody.Builder().setType(MultipartBody.FORM).addFormDataPart("api_key", faceApiKey).addFormDataPart("api_secret", faceApiSecret).addFormDataPart("image_url", finalFrontUrl).addFormDataPart("return_imagereset", "1").build();
                Request request = new Request.Builder().url(url).post(requestBody).build();
                try (Response response = client.newCall(request).execute()) {
                    if (response.code() != 200) {
                        throw new RuntimeException("请求失败:" + response.code() + ";msg:" + response.message());
                    }
                    String responseBodyStr = Objects.requireNonNull(response.body()).string();
                    JSONObject jsonObject = JSONObject.parseObject(responseBodyStr);
                    String imageReset = ImageUtils.postHttpImage(Base64.getDecoder().decode(jsonObject.getString("image_reset")), uuid).getOriginalLink();
                    jsonObject.remove("image_reset");
                    return new AnalyzeFaceFeaturesResult(imageReset, jsonObject);
                }
            } catch (Exception e) {
                log.error("Failed to analyze facial features", e);
                return null;
            }
        }, HACKATHON_ANALYZE_POOL.getExecutor());
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AnalyzeFaceFeaturesResult {

        private String imageReset;

        private JSONObject facialFeatures;

    }

    /**
     * 三庭五眼检测
     */
    public FaceFeaturesPromptResult promptFacialFeatures(final String userRequest,
                                                         final AnalyzeFaceFeaturesResult analyzeFaceFeaturesResult) {
        if (analyzeFaceFeaturesResult == null
                || analyzeFaceFeaturesResult.getFacialFeatures() == null
                || analyzeFaceFeaturesResult.getImageReset() == null) {
            throw new IllegalArgumentException("无效脸型检测数据!!!");
        }
        String prompt = Lion.getString(Environment.getAppName(), "com.sankuai.dzhealth.ai.service.analyzeFacialFeatures.prompt");
        prompt = prompt.replace("{{{userRequest}}}", Optional.ofNullable(userRequest).orElse("变好看一点"));
        prompt = prompt.replace("{{{userData}}}", analyzeFaceFeaturesResult.getFacialFeatures().toJSONString());
        ChatResponse response = analyzeChatClient.prompt(prompt).call().chatResponse();
        String answer = response != null ? response.getResult().getOutput().getText() : null;
        if (answer == null) {
            throw new IllegalStateException("脸型分析大模型返回值为空");
        }
        FaceFeaturesPromptResult result = JSONObject.parseObject(answer, FaceFeaturesPromptResult.class);
        result.checkParams();
        return result;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class FaceFeaturesPromptResult {

        private String noNeedToChange;

        private Map<String, String> problem;

        private Map<String, String> items;

        public void checkParams() {
            if (StringUtils.isBlank(noNeedToChange)) {
                throw new IllegalArgumentException("noNeedToChange is null");
            }
            if (problem == null || problem.isEmpty()) {
                throw new IllegalArgumentException("problem is null");
            }
            if (items == null || items.isEmpty()) {
                throw new IllegalArgumentException("items is null");
            }
        }

    }

}
