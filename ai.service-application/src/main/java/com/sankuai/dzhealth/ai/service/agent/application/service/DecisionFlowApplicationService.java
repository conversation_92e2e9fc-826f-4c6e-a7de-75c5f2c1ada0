package com.sankuai.dzhealth.ai.service.agent.application.service;

import com.sankuai.dzhealth.ai.service.agent.domain.model.decisionflow.*;
import com.sankuai.dzhealth.ai.service.api.RemoteResponse;
import com.sankuai.dzhealth.ai.service.domain.utils.DecisionTreeModelConverter;
import com.sankuai.dzhealth.ai.service.domain.utils.JsonUtils;
import com.sankuai.dzhealth.ai.service.domain.service.decisionflow.DecisionFlowDomainService;
import com.sankuai.dzhealth.ai.service.domain.service.decisionflow.ResourceRecommendationDomainService;
import com.sankuai.dzhealth.ai.service.dto.decision.*;
import com.sankuai.dzhealth.ai.service.enums.DecisionBizSceneEnum;
import com.sankuai.dzhealth.ai.service.enums.DecisionFlowElementStatusEnum;
import com.sankuai.dzhealth.ai.service.enums.DicisionFlowStatusEnum;
import com.sankuai.dzhealth.ai.service.infrastructure.dal.entity.DecisionNodeEdgeDO;
import com.sankuai.dzhealth.ai.service.request.*;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 决策流应用服务
 * 职责：用例实现，串联内部领域服务接口，实现流程编排、聚合查询等工作流操作
 */
@Service
@RequiredArgsConstructor
public class DecisionFlowApplicationService {

    private final DecisionFlowDomainService decisionFlowDomainService;
    private final ResourceRecommendationDomainService resourceRecommendationDomainService;

    /**
     * 决策下一节点
     */
    public RemoteResponse<List<DecisionNodeDTO>> decideNext(DecideNextRequest request) {
        List<DecisionNodeBO> list = decisionFlowDomainService.decideNext(request.getNodeId(), request.getEdgeValue());
        List<DecisionNodeDTO> dtoList = list.stream().map(this::convertNodeEntity).collect(Collectors.toList());
        return RemoteResponse.buildSuccess(dtoList);
    }

    /**
     * 查询推荐资源
     */
    public RemoteResponse<List<ResourceRecommendationDTO>> listTopResources(ListTopResourcesRequest request) {
        int limit = request.getLimit() == null ? 20 : request.getLimit();
        List<ResourceRecommendationBO> list = resourceRecommendationDomainService.listTop(request.getNodeId(), limit);
        List<ResourceRecommendationDTO> dtoList = list.stream().map(this::convert).collect(Collectors.toList());
        return RemoteResponse.buildSuccess(dtoList);
    }

    /**
     * 导入决策流
     */
    public RemoteResponse<ImportResultDTO> importFlow(DecisionFlowImportRequest request) {
        try {
            // 检查是否有任何变更
            boolean hasChanges = (request.getNodes() != null && !request.getNodes().isEmpty()) ||
                    (request.getEdges() != null && !request.getEdges().isEmpty()) ||
                    (request.getRelations() != null && !request.getRelations().isEmpty()) ||
                    (request.getResources() != null && !request.getResources().isEmpty()) ||
                    (request.getDeletedNodeIds() != null && !request.getDeletedNodeIds().isEmpty()) ||
                    (request.getDeletedEdgeIds() != null && !request.getDeletedEdgeIds().isEmpty()) ||
                    (request.getDeletedRelationIds() != null && !request.getDeletedRelationIds().isEmpty()) ||
                    (request.getDeletedResourceIds() != null && !request.getDeletedResourceIds().isEmpty());

            if (!hasChanges) {
                return RemoteResponse.buildSuccess(ImportResultDTO.builder()
                        .success(true)
                        .message("无修改，无需保存")
                        .hasChanges(false)
                        .build());
            }

            boolean success = decisionFlowDomainService.importFlow(request);

            if (success) {
                // 统计变更数量
                int nodeCount = request.getNodes() != null ? request.getNodes().size() : 0;
                int edgeCount = request.getEdges() != null ? request.getEdges().size() : 0;
                int resourceCount = request.getResources() != null ? request.getResources().size() : 0;
                int relationCount = request.getRelations() != null ? request.getRelations().size() : 0;
                int deletedNodeCount = request.getDeletedNodeIds() != null ? request.getDeletedNodeIds().size() : 0;
                int deletedEdgeCount = request.getDeletedEdgeIds() != null ? request.getDeletedEdgeIds().size() : 0;

                String message = String.format("测试版本已保存，可发布灰度。变更统计：节点 %d，边 %d，资源 %d，关联 %d，删除节点 %d，删除边 %d",
                        nodeCount, edgeCount, resourceCount, relationCount, deletedNodeCount, deletedEdgeCount);

                return RemoteResponse.buildSuccess(ImportResultDTO.builder()
                        .success(true)
                        .message(message)
                        .hasChanges(true)
                        .nodeCount(nodeCount)
                        .edgeCount(edgeCount)
                        .resourceCount(resourceCount)
                        .relationCount(relationCount)
                        .deletedNodeCount(deletedNodeCount)
                        .deletedEdgeCount(deletedEdgeCount)
                        .build());
            } else {
                return RemoteResponse.buildFail("保存测试版本失败");
            }
        } catch (Exception e) {
            return RemoteResponse.buildFail("保存测试版本异常：" + e.getMessage());
        }
    }

    /**
     * 删除决策流
     */
    public RemoteResponse<Boolean> deleteFlow(BizSceneRequest request) {
        try {
            boolean ok = decisionFlowDomainService.deleteFlow(request.getBizScene());
            return RemoteResponse.buildSuccess(ok);
        } catch (Exception e) {
            return RemoteResponse.buildFail(e.getMessage());
        }
    }

    /**
     * 搜索决策流（仅决策节点），返回决策树 JSON 字符串列表
     * 支持控制搜索深度：-1表示搜索完整树，其他值表示指定层数
     */
    public RemoteResponse<List<DecisionFlowDTO>> search(DecisionTreeSearchRequest request) {
        String bizScene = request.getBizScene();
        String keyword = request.getQuery();
        int topK = request.getTopK() == null ? 20 : request.getTopK();
        int maxDepth = request.getMaxDepth() == null ? -1 : request.getMaxDepth();

        if (bizScene == null || bizScene.isEmpty()) {
            return RemoteResponse.buildFail("bizScene is required");
        }

        // 1. 搜索匹配的节点（仅决策节点），是否合并 GRAY
        boolean previewGray = request.getPreviewGray() != null && request.getPreviewGray();
        List<DecisionNodeBO> matchedNodes = decisionFlowDomainService.searchNodes(bizScene, keyword, topK, previewGray);

        // 2. 对每个匹配节点，获取其所在的完整决策树（从该节点向上追溯到根，再向下遍历全部）
        // 记录已处理过的节点（整棵树级别去重）
        Set<String> processedNodeIds = new HashSet<>();
        List<DecisionFlowDTO> result = new ArrayList<>();

        for (DecisionNodeBO nodeBO : matchedNodes) {
            // 若该节点所属决策树已处理过，则跳过
            if (processedNodeIds.contains(nodeBO.getNodeId())) {
                continue;
            }

            // 根据 maxDepth 决定查询树的根：
            // - 当 maxDepth < 0 时，返回完整树，需要先回溯到根节点
            // - 当 maxDepth >= 0 时，只从匹配节点开始向下追溯 maxDepth 层
            String rootNodeId;
            if (maxDepth < 0) {
                // 回溯到整棵树的根节点
                rootNodeId = findRootNodeId(nodeBO.getNodeId(), bizScene, previewGray);
            } else {
                // 直接以当前匹配节点作为根节点
                rootNodeId = nodeBO.getNodeId();
            }

            DecisionFlowBO tree = decisionFlowDomainService.queryDecisionTree(bizScene, rootNodeId, maxDepth, previewGray, true);
            if (tree == null) {
                continue;
            }

            DecisionFlowDTO dto = buildDecisionFlowDTO(tree);
            if (dto != null) {
                // 添加决策树模型 JSON 转换逻辑
                try {
                    int depth = request.getMaxDepth() == null ? 3 : request.getMaxDepth();
                    String json = JsonUtils.toGsonString(
                            DecisionTreeModelConverter.convertToDecisionTreeModels(dto, depth)
                    );
                    dto.setDecisionTreeModelsJson(json);
                } catch (Exception ignore) {
                    // JSON 转换失败时忽略，保持原有行为
                }

                result.add(dto);
                // 标记整棵树中的节点为已处理，避免重复
                tree.getNodes().forEach(n -> processedNodeIds.add(n.getNodeId()));
            }
        }

        return RemoteResponse.buildSuccess(result);
    }

    public List<DecisionFlowDTO> getDecisionByID(DecisionTreeSearchRequest request, String rootNodeId) {
        DecisionFlowBO tree = decisionFlowDomainService.queryDecisionTree(request.getBizScene(), rootNodeId, 3, false, true);
        if (tree == null) {
            return null;
        }

        List<DecisionFlowDTO> result = new ArrayList<>();
        DecisionFlowDTO dto = buildDecisionFlowDTO(tree);
        if (dto != null) {
            // 添加决策树模型 JSON 转换逻辑
            try {
                int depth = request.getMaxDepth() == null ? 3 : request.getMaxDepth();
                String json = JsonUtils.toGsonString(
                        DecisionTreeModelConverter.convertToDecisionTreeModels(dto, depth)
                );
                dto.setDecisionTreeModelsJson(json);
            } catch (Exception ignore) {
                // JSON 转换失败时忽略，保持原有行为
            }
            result.add(dto);
        }
        return result;
    }

    /**
     * 向上回溯找到决策树根节点
     */
    private String findRootNodeId(String nodeId, String bizScene, boolean previewGray) {
        // 根据是否预览灰度过滤边
        List<DecisionNodeEdgeDO> edges;
        if (previewGray) {
            List<DecisionNodeEdgeDO> all = decisionFlowDomainService.listEdges(bizScene); // 全状态
            // 1) 记录被灰度删除的 edgeId
            Set<String> deletedIds = all.stream()
                    .filter(e -> DecisionFlowElementStatusEnum.GRAY_DELETE.code().equals(e.getStatus()))
                    .map(DecisionNodeEdgeDO::getEdgeId)
                    .collect(Collectors.toSet());

            // 2) 仅保留 ONLINE / GRAY 且未被删除的记录
            edges = all.stream()
                    .filter(e -> (DecisionFlowElementStatusEnum.ONLINE.code().equals(e.getStatus()) ||
                            DecisionFlowElementStatusEnum.GRAY.code().equals(e.getStatus())) &&
                            !deletedIds.contains(e.getEdgeId()))
                    .toList();
        } else {
            edges = decisionFlowDomainService.listEdges(bizScene, DecisionFlowElementStatusEnum.ONLINE);
        }
        Map<String, List<DecisionNodeEdgeDO>> childToParent = edges.stream()
                .collect(Collectors.groupingBy(DecisionNodeEdgeDO::getChildId));

        String current = nodeId;
        Set<String> visited = new HashSet<>();
        while (true) {
            if (!childToParent.containsKey(current)) {
                return current; // 没有父节点，当前即根节点
            }
            // 取第一个父节点继续回溯
            DecisionNodeEdgeDO parentEdge = childToParent.get(current).get(0);
            String parentId = parentEdge.getParentId();
            if (parentId == null || visited.contains(parentId)) {
                return current;
            }
            visited.add(parentId);
            current = parentId;
        }
    }

    /**
     * 查询决策流概览
     */
    public RemoteResponse<DecisionFlowSummaryDTO> queryFlowSummary(QueryFlowSummaryRequest request) {
        try {
            boolean previewGray = request.getPreviewGray() != null ? request.getPreviewGray() : false;
            DecisionFlowSummaryBO summaryBO = decisionFlowDomainService.getFlowSummary(request.getBizScene(), previewGray);

            List<DecisionNodeDTO> rootNodeDTOs = summaryBO.getRootNodes().stream()
                    .map(this::convertNodeEntity)
                    .collect(Collectors.toList());

            DecisionFlowSummaryDTO dto = DecisionFlowSummaryDTO.builder()
                    .bizScene(summaryBO.getBizScene())
                    .totalNodes(summaryBO.getTotalNodes())
                    .totalEdges(summaryBO.getTotalEdges())
                    .rootNodes(rootNodeDTOs)
                    .leafNodeCount(summaryBO.getLeafNodeCount())
                    .flowStatus(summaryBO.getFlowStatus())
                    .lastUpdateTime(summaryBO.getLastUpdateTime())
                    .build();
            return RemoteResponse.buildSuccess(dto);
        } catch (Exception e) {
            return RemoteResponse.buildFail("Failed to get flow summary: " + e.getMessage());
        }
    }

    /**
     * 查询决策树
     */
    public RemoteResponse<DecisionFlowDTO> queryDecisionTree(DecisionTreeQueryRequest request) {
        try {
            boolean previewGray = request.getPreviewGray() != null ? request.getPreviewGray() : false;
            int maxDepth = request.getMaxDepth() != null ? request.getMaxDepth() : -1;
            boolean includeResources = request.getIncludeResources() != null && request.getIncludeResources();

            DecisionFlowBO flowBO = decisionFlowDomainService.queryDecisionTree(
                    request.getBizScene(),
                    request.getRootNodeId(),
                    maxDepth,
                    previewGray,
                    includeResources);
            if (flowBO == null) {
                return RemoteResponse.buildFail("No decision tree found for the given parameters");
            }

            List<DecisionNodeDTO> nodeDTOs = flowBO.getNodes().stream()
                    .map(this::convertNodeEntity)
                    .collect(Collectors.toList());
            List<DecisionEdgeDTO> edgeDTOs = flowBO.getEdges().stream()
                    .map(this::convertEdge)
                    .collect(Collectors.toList());

            // === 处理资源和关联 ===
            List<ResourceRecommendationDTO> resourceDTOs = null;
            List<NodeResourceRelationDTO> relationDTOs = null;

            if (includeResources) {
                if (flowBO.getResources() != null) {
                    resourceDTOs = flowBO.getResources().stream()
                            .map(this::convert)
                            .collect(Collectors.toList());
                }

                if (flowBO.getRelations() != null) {
                    relationDTOs = flowBO.getRelations().stream()
                            .map(this::convertRelation)
                            .collect(Collectors.toList());
                }
            }

            DecisionFlowDTO dto = DecisionFlowDTO.builder()
                    .bizScene(flowBO.getBizScene())
                    .nodes(nodeDTOs)
                    .edges(edgeDTOs)
                    .resources(resourceDTOs)
                    .relations(relationDTOs)
                    .build();
            return RemoteResponse.buildSuccess(dto);
        } catch (Exception e) {
            return RemoteResponse.buildFail("Failed to query decision tree: " + e.getMessage());
        }
    }

    /**
     * 获取业务场景列表
     */
    public RemoteResponse<List<BizSceneDTO>> listBizScenes() {
        try {
            // 从数据库获取所有不同的业务场景
            List<String> bizScenes = decisionFlowDomainService.listAllBizScenes();

            List<BizSceneDTO> list = bizScenes.stream()
                    .map(bizScene -> {
                        DicisionFlowStatusEnum status = decisionFlowDomainService.getBizSceneVersionStatus(bizScene);
                        return BizSceneDTO.builder()
                                .code(bizScene.hashCode())
                                .bizScene(bizScene)
                                .desc(getDescForBizScene(bizScene))
                                .status(status)
                                .build();
                    })
                    .collect(Collectors.toList());
            return RemoteResponse.buildSuccess(list);
        } catch (Exception ex) {
            return RemoteResponse.buildFail("Failed to list bizScenes: " + ex.getMessage());
        }
    }

    /**
     * 回滚灰度
     */
    public RemoteResponse<Boolean> rollbackGray(BizSceneRequest request) {
        try {
            decisionFlowDomainService.rollbackGray(request.getBizScene());
            return RemoteResponse.buildSuccess(true);
        } catch (Exception ex) {
            return RemoteResponse.buildFail("rollbackGray failed: " + ex.getMessage());
        }
    }

    /**
     * 灰度转正式上线
     */
    public RemoteResponse<Boolean> grayToOnline(BizSceneRequest request) {
        try {
            decisionFlowDomainService.grayToOnline(request.getBizScene());
            return RemoteResponse.buildSuccess(true);
        } catch (Exception ex) {
            return RemoteResponse.buildFail("grayToOnline failed: " + ex.getMessage());
        }
    }


// ========== 私有转换方法 ==========

    private DecisionNodeDTO convertNodeEntity(DecisionNodeBO bo) {
        return DecisionNodeDTO.builder()
                .bizScene(bo.getBizScene())
                .nodeId(bo.getNodeId())
                .nodeName(bo.getNodeName())
                .assessmentText(bo.getAssessmentText())
                .assessmentImg(bo.getAssessmentImg())
                .guidanceText(bo.getGuidanceText())
                .status(bo.getStatus())
                .needSupply(bo.getNeedSupply())
                .needDoctor(bo.getNeedDoctor())
                .build();
    }

    private DecisionEdgeDTO convertEdge(DecisionEdgeBO edge) {
        return DecisionEdgeDTO.builder()
                .bizScene(edge.getBizScene())
                .edgeId(edge.getEdgeId())
                .parentId(edge.getParentNodeId())
                .childId(edge.getChildNodeId())
                .edgeType(edge.getEdgeType())
                .edgeDesc(edge.getEdgeDesc())
                .sortOrder(edge.getSortOrder())
                .status(edge.getStatus())
                .build();
    }

    private ResourceRecommendationDTO convert(ResourceRecommendationBO bo) {
        if (bo == null) return null;
        return ResourceRecommendationDTO.builder()
                .bizScene(bo.getBizScene())
                .resourceId(bo.getResourceId())
                .resourceType(bo.getResourceType())
                .resourceName(bo.getResourceName())
                .shortDesc(bo.getShortDesc())
                .attributes(bo.getAttributes())
                .tags(bo.getTags())
                .build();
    }

    private NodeResourceRelationDTO convertRelation(NodeResourceRelationBO rel) {
        if (rel == null) return null;
        return NodeResourceRelationDTO.builder()
                .bizScene(rel.getBizScene())
                .nodeId(rel.getNodeId())
                .resourceId(rel.getResourceId())
                .resourceType(rel.getResourceType())
                .sortOrder(rel.getSortOrder())
                .status(rel.getStatus())
                .rationale(rel.getRationale())
                .build();
    }

    /**
     * 根据业务场景获取描述
     */
    private String getDescForBizScene(String bizScene) {
        // 可以从枚举中获取已知场景的描述，未知场景使用默认描述
        for (DecisionBizSceneEnum enumValue : DecisionBizSceneEnum.values()) {
            if (enumValue.getBizScene().equals(bizScene)) {
                return enumValue.getDesc();
            }
        }
        // 未知场景的默认描述
        return bizScene + "场景";
    }

// 删除内部判定方法，逻辑已下沉到领域服务

    /**
     * 将领域层 BO 转换为 API 层 DTO
     */
    private DecisionFlowDTO buildDecisionFlowDTO(DecisionFlowBO flowBO) {
        List<DecisionNodeDTO> nodeDTOs = flowBO.getNodes().stream()
                .map(this::convertNodeEntity)
                .collect(Collectors.toList());

        List<DecisionEdgeDTO> edgeDTOs = flowBO.getEdges().stream()
                .map(this::convertEdge)
                .collect(Collectors.toList());

        List<ResourceRecommendationDTO> resourceDTOs = null;
        if (flowBO.getResources() != null) {
            resourceDTOs = flowBO.getResources().stream()
                    .map(this::convert)
                    .collect(Collectors.toList());
        }

        List<NodeResourceRelationDTO> relationDTOs = null;
        if (flowBO.getRelations() != null) {
            relationDTOs = flowBO.getRelations().stream()
                    .map(this::convertRelation)
                    .collect(Collectors.toList());
        }

        return DecisionFlowDTO.builder()
                .bizScene(flowBO.getBizScene())
                .nodes(nodeDTOs)
                .edges(edgeDTOs)
                .resources(resourceDTOs)
                .relations(relationDTOs)
                .build();
    }

}

