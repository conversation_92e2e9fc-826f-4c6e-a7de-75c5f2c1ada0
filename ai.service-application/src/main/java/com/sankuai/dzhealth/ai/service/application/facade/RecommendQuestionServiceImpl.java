package com.sankuai.dzhealth.ai.service.application.facade;

import com.dianping.haima.entity.haima.HaimaContent;
import com.meituan.mdp.boot.starter.thrift.annotation.MdpThriftServer;
import com.sankuai.dzhealth.ai.service.agent.domain.cellar.HistorySearchRecordRepository;
import com.sankuai.dzhealth.ai.service.agent.domain.entity.ChatSessionEntity;
import com.sankuai.dzhealth.ai.service.agent.domain.enums.CategoryEnum;
import com.sankuai.dzhealth.ai.service.agent.domain.repository.chatSession.ChatSessionRepository;
import com.sankuai.dzhealth.ai.service.agent.domain.service.QuestionRecommendService;
import com.sankuai.dzhealth.ai.service.agent.infrastructure.model.RecommendQuestionAnswer;
import com.sankuai.dzhealth.ai.service.api.RecommendQuestionService;
import com.sankuai.dzhealth.ai.service.api.RemoteResponse;
import com.sankuai.dzhealth.ai.service.domain.utils.JsonUtils;
import com.sankuai.dzhealth.ai.service.dto.RecommendQuestionDTO;
import com.sankuai.dzhealth.ai.service.dto.haima.ResourceConfig;
import com.sankuai.dzhealth.ai.service.infrastructure.acl.HaimaAcl;
import com.sankuai.dzhealth.ai.service.request.RecommendQuestionRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;
import java.util.concurrent.CompletableFuture;

import static com.sankuai.dzhealth.ai.service.agent.domain.repository.chatSession.ChatSessionRepository.STATUS_NORMAL;

/**
 * 推荐问题服务实现类
 */
@Slf4j
@MdpThriftServer
@RequiredArgsConstructor
public class RecommendQuestionServiceImpl implements RecommendQuestionService {
    @Autowired
    private QuestionRecommendService questionRecommendService;

    @Autowired
    private ChatSessionRepository chatSessionRepository;

    @Autowired
    private HistorySearchRecordRepository historySearchRecordRepository;

    @Autowired
    private HaimaAcl haimaAcl;

    private static String MEDICAL_AI_RESOURCE_CONFIG = "medical_ai_resource_config";

    @Override
    public RemoteResponse<RecommendQuestionDTO> getRecommendQuestions(RecommendQuestionRequest request) {
        try {
            log.info("开始获取推荐问题, request: {}", request);
            // 1. 参数校验
            validateRequest(request);

            // 2. 参数处理
            CategoryEnum categoryEnum = CategoryEnum.getByCode(request.getCategoryId());

            RecommendQuestionDTO recommendQuestion;

            // 判断商品标题是否为空
            if (StringUtils.isNotBlank(request.getProductName())) {
                // 如果商品标题不为空，基于商品标题调用大模型生成推荐问题
                log.info("基于商品标题生成推荐问题, productName: {}", request.getProductName());
                recommendQuestion = generateQuestionsByProductName(request.getProductName(), categoryEnum);
                return RemoteResponse.buildSuccess(recommendQuestion);
            }

            // 使用并行处理获取搜索记录和会话信息
            List<String> searchRecords = new ArrayList<>();
            List<String> chatDigests = new ArrayList<>();

            var voidCompletableFuture = CompletableFuture.runAsync(() -> {
                List<String> records = historySearchRecordRepository.selectHistorySearchRecord(Long.parseLong(request.getUserId()), categoryEnum);
                if (records != null) {
                    searchRecords.addAll(records.stream().filter(e -> !e.isBlank()).toList());
                }
            });
            var voidCompletableFuture1 = CompletableFuture.runAsync(() -> {
                List<ChatSessionEntity> sessionList = chatSessionRepository.findSessions(Long.parseLong(request.getUserId()),
                        2, categoryEnum.bizType, STATUS_NORMAL, null, 5, 0, true);
                if (sessionList != null) {
                    chatDigests.addAll(sessionList.stream()
                            // 会话摘要为空，实用标题
                            .map(e -> {
                                if (e.getDigest().isBlank()) {
                                    return e.getTitle();
                                }
                                return e.getDigest();
                            })
                            .filter(e -> !e.isBlank())
                            .toList());
                }
            });

            CompletableFuture.allOf(voidCompletableFuture1, voidCompletableFuture).join();

            if (searchRecords.isEmpty() && chatDigests.isEmpty()) {
                recommendQuestion = generateGeneralQuestions(categoryEnum);
                return RemoteResponse.buildSuccess(recommendQuestion);
            }
            recommendQuestion = generateQuestionsByRecord(searchRecords, chatDigests, categoryEnum);
            if (recommendQuestion != null
                    && recommendQuestion.recommendItems != null
                    && !recommendQuestion.recommendItems.isEmpty()
                    && recommendQuestion.recommendQuestions != null
                    && !recommendQuestion.recommendQuestions.isEmpty()) {
                return RemoteResponse.buildSuccess(recommendQuestion);
            }

            RecommendQuestionDTO generalQuestions = generateGeneralQuestions(categoryEnum);
            RecommendQuestionDTO recommendQuestionDTO = mergeRecommendQuestionDTO(recommendQuestion, generalQuestions);
            return RemoteResponse.buildSuccess(recommendQuestionDTO);
        } catch (Exception e) {
            log.error("获取推荐问题列表失败, userId: {}, error: {}",
                    request.getUserId(), e.getMessage());
            return RemoteResponse.buildFail("系统繁忙，请稍后再试");
        }
    }

    private RecommendQuestionDTO mergeRecommendQuestionDTO(RecommendQuestionDTO recommendQuestion, RecommendQuestionDTO generalQuestions) {
        if (recommendQuestion == null) {
            return generalQuestions;
        }
        if (recommendQuestion.recommendItems.isEmpty()) {
            recommendQuestion.recommendItems = generalQuestions.recommendItems;
        }
        if (recommendQuestion.recommendQuestions.isEmpty()) {
            recommendQuestion.recommendQuestions = generalQuestions.recommendQuestions;
        }
        return recommendQuestion;
    }

    /**
     * 参数校验
     */
    private void validateRequest(RecommendQuestionRequest request) {
        if (request == null) {
            throw new IllegalArgumentException("请求参数不能为空");
        }

        // 参数校验
        if (StringUtils.isBlank(request.getUserId())) {
            throw new IllegalArgumentException("userId不能为空");
        }
    }


    private ResourceConfig getBackUpResource(CategoryEnum categoryEnum) {
        var resourceKey = "";
        switch (categoryEnum) {
            case MEDICAL_CONSULT: // 医美
                resourceKey = "beauty_backup_question";
                break;
            case MOUTH_CONSULT:// 口腔
                resourceKey = "oral_backup_question";
                break;
            default:
                return null;
        }

        List<HaimaContent> medicalTaskAiConfig = haimaAcl.getContent(MEDICAL_AI_RESOURCE_CONFIG, null);

        String finalResourceKey = resourceKey;
        Optional<ResourceConfig> config = medicalTaskAiConfig.stream()
                .filter(e -> e.getContentString("resource_key").equals(finalResourceKey))
                .map(e -> JsonUtils.parseObject(e.getContentString("resource_config"), ResourceConfig.class))
                .findFirst();

        return config.orElse(null);
    }


    /**
     * 基于商品标题调用大模型生成推荐问题
     *
     * @param productName  商品标题
     * @param categoryEnum
     * @return 推荐问题列表
     */

    private RecommendQuestionDTO generateQuestionsByProductName(String productName, CategoryEnum categoryEnum) {
        // 构建提示词
        RecommendQuestionAnswer llmAnswer = null;
        try {
            String s = questionRecommendService.recommendQuestionByProduct(productName, categoryEnum);
            s = s.replace("```json", "").replace("```", "");
            llmAnswer = JsonUtils.parseObject(s, RecommendQuestionAnswer.class);
        } catch (Exception e) {
            log.error("recommendQuestionByProduct error", e);
        }
        if (llmAnswer == null) {
            return null;
        }
        RecommendQuestionDTO result = new RecommendQuestionDTO();
        result.setRecommendItems(llmAnswer.keyWord);
        result.setRecommendQuestions(llmAnswer.question);
        return result;
    }


    private RecommendQuestionDTO generateQuestionsByRecord(List<String> searchRecords, List<String> chatRecords, CategoryEnum categoryEnum) {
        // 构建提示词
        RecommendQuestionAnswer llmAnswer = null;
        try {
            String s = questionRecommendService.recommendQuestionByRecord(searchRecords, chatRecords, categoryEnum);
            s = s.replace("```json", "").replace("```", "");
            llmAnswer = JsonUtils.parseObject(s, RecommendQuestionAnswer.class);
        } catch (Exception e) {
            log.error("recommendQuestionByProduct error", e);
        }
        if (llmAnswer == null) {
            return null;
        }
        RecommendQuestionDTO result = new RecommendQuestionDTO();
        result.setRecommendItems(llmAnswer.keyWord);
        result.setRecommendQuestions(llmAnswer.question);
        return result;
    }

    private RecommendQuestionDTO generateGeneralQuestions(CategoryEnum categoryEnum) {
        ResourceConfig backUpResource = getBackUpResource(categoryEnum);
        if (backUpResource == null) {
            return null;
        }

        List<String> project = backUpResource.getProject();
        List<String> efficacy = backUpResource.getEfficacy();
        List<String> items = new ArrayList<>();
        items.addAll(weightedRandomSample(project, 4));
        items.addAll(weightedRandomSample(efficacy, 4));
        return RecommendQuestionDTO
                .builder()
                .recommendItems(items)
                .recommendQuestions(backUpResource.getQuestion())
                .build();
    }


    public static <T> List<T> weightedRandomSample(List<T> sourceList, int n) {
        if (sourceList == null || sourceList.isEmpty()) {
            return Collections.emptyList();
        }

        if (n <= 0) {
            return Collections.emptyList();
        }

        if (n >= sourceList.size()) {
            return new ArrayList<>(sourceList);
        }

        // 创建权重列表，权重与位置成反比
        List<Double> weights = new ArrayList<>(sourceList.size());
        int size = sourceList.size();
        for (int i = 0; i < size; i++) {
            // 使用倒数作为权重，位置越靠前权重越大
            weights.add(1.0 / (i + 1));
        }

        // 计算权重总和
        double totalWeight = weights.stream().mapToDouble(Double::doubleValue).sum();

        // 归一化权重
        for (int i = 0; i < weights.size(); i++) {
            weights.set(i, weights.get(i) / totalWeight);
        }

        // 使用轮盘赌算法选择元素
        List<T> result = new ArrayList<>(n);
        List<Integer> selectedIndices = new ArrayList<>(n);
        Random random = new Random();

        for (int i = 0; i < n; i++) {
            double randomValue = random.nextDouble();
            double cumulativeProbability = 0.0;

            for (int j = 0; j < size; j++) {
                if (selectedIndices.contains(j)) {
                    continue; // 跳过已选择的元素
                }

                cumulativeProbability += weights.get(j);

                if (randomValue <= cumulativeProbability) {
                    result.add(sourceList.get(j));
                    selectedIndices.add(j);
                    break;
                }
            }
        }

        return result;
    }
}