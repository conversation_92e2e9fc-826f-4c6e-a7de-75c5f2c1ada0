package com.sankuai.dzhealth.ai.service.agent.application.service;

import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.dianping.haima.entity.haima.HaimaContent;
import com.google.common.collect.Lists;
import com.meituan.beauty.fundamental.light.plat.Platform;
import com.sankuai.dzhealth.ai.service.agent.domain.entity.ChatSessionEntity;
import com.sankuai.dzhealth.ai.service.agent.domain.entity.ChatSessionMessageEntity;
import com.sankuai.dzhealth.ai.service.agent.domain.repository.chatSession.ChatSessionMessageRepository;
import com.sankuai.dzhealth.ai.service.agent.domain.repository.chatSession.ChatSessionRepository;
import com.sankuai.dzhealth.ai.service.agent.dto.*;
import com.sankuai.dzhealth.ai.service.agent.enums.SessionSceneEnum;
import com.sankuai.dzhealth.ai.service.agent.infrastructure.repository.appointment.AppointmentInfoRepository;
import com.sankuai.dzhealth.ai.service.agent.request.SessionManageRequest;
import com.sankuai.dzhealth.ai.service.api.RemoteResponse;
import com.sankuai.dzhealth.ai.service.domain.utils.JsonUtils;
import com.sankuai.dzhealth.ai.service.infrastructure.acl.HaimaAcl;
import com.sankuai.dzhealth.ai.service.infrastructure.utils.UidUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.ai.chat.messages.MessageType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import static com.sankuai.dzhealth.ai.service.agent.domain.repository.chatSession.ChatSessionRepository.STATUS_DELETED;
import static com.sankuai.dzhealth.ai.service.agent.domain.repository.chatSession.ChatSessionRepository.STATUS_NORMAL;

/**
 * @author:chenwei
 * @time: 2025/7/12 13:46
 * @version: 0.0.1
 */

@Service
@Slf4j
public class SessionManageApplicationService {

    @Autowired
    private ChatSessionMessageRepository chatSessionMessageRepository;

    @Autowired
    private ChatSessionRepository chatSessionRepository;


    @Autowired
    private AppointmentInfoRepository appointmentInfoRepository;

    @Autowired
    private UidUtils uidUtils;

    @Autowired
    private HaimaAcl haimaAcl;


    private static final String  MT_LIST= "imeituan://www.meituan.com/mrn?mrn_biz=gcbu&mrn_entry=aesthetic-ai-app" +
            "&mrn_component=AestheticMedicineAIRecordList&bizType=%s";

    private static final String  DP_LIST= "dianping://mrn?mrn_biz=gcbu&mrn_entry=aesthetic-ai-app" +
            "&mrn_component=AestheticMedicineAIRecordList&scene=1&bizType=%s";


    private static final String MT_DETAIL = "imeituan://www.meituan.com/mrn?mrn_biz=gcbu&mrn_entry=aesthetic-ai-app" +
            "&mrn_component=AestheticMedicineAI&scene=4&bizType=%s&sessionId=%s";

    private static final String DP_DETAIL = "dianping://mrn?mrn_biz=gcbu&mrn_entry=aesthetic-ai-app&mrn_component=AestheticMedicineAI" +
            "&scene=4&bizType=%s&sessionId=%s";




    public RemoteResponse<ChatMessageResponse> processMessages(SessionManageRequest request) {
        Transaction transaction = Cat.newTransaction("SessionManageApplicationService", "queryMessages");
        try {
            transaction.setStatus(Transaction.SUCCESS);

            int offset = (request.getPageNo() - 1) * request.getPageSize();

            ChatMessageResponse response = new ChatMessageResponse();
            List<ChatMessageDTO> messageList = new ArrayList<>();

            //商详页进入  查询会话历史消息但是没有传会话id，携带了历史消息需要先新建会话
            if (request.getScene() == SessionSceneEnum.PRODUCT_DETAIL.getCode() && StringUtils.isBlank(request.getSessionId())
                    && StringUtils.isNotBlank(request.getHistoryMsg())) {

                List<String> historyList = JsonUtils.parseArray(request.getHistoryMsg(), String.class);

                if (CollectionUtils.isEmpty(historyList) || historyList.size() < 2) {
                    return RemoteResponse.buildIllegalArgument("商详页信息json错误");
                }


                String userMsg = historyList.get(0);
                String assistantMsg = historyList.get(1);

                // 1.创建会话id
                String sessionId = String.valueOf(uidUtils.getNextId(UidUtils.CHAT_SESSION_LEAF_KEY));
                ChatSessionEntity chatSessionEntity = chatSessionRepository.buildSessionEntity(sessionId,
                        request.getUserId(), request.getBizType(), request.getPlatform(), userMsg, null);
                chatSessionRepository.insert(chatSessionEntity);

                // 2.用户消息
                String userMsgId = String.valueOf(uidUtils.getNextId(UidUtils.CHAT_MESSAGE_LEAF_KEY));

                ChatSessionMessageEntity userMessageEntity = chatSessionMessageRepository.buildMessageEntity(
                        userMsgId, sessionId, request.getUserId(), request.getPlatform(),
                        MessageType.USER.getValue(), userMsg, StringUtils.EMPTY);

                chatSessionMessageRepository.insert(userMessageEntity);

                ChatMessageDTO userMessageDTO = buildChatMessageDTO(userMessageEntity);


                // 3.assistant消息
                String assistantMsgId = String.valueOf(uidUtils.getNextId(UidUtils.CHAT_MESSAGE_LEAF_KEY));

                ChatSessionMessageEntity assistantMessageEntity = chatSessionMessageRepository.buildMessageEntity(
                        assistantMsgId, sessionId, request.getUserId(), request.getPlatform(),
                        MessageType.ASSISTANT.getValue(), assistantMsg, StringUtils.EMPTY);

                chatSessionMessageRepository.insert(assistantMessageEntity);

                ChatMessageDTO assistantMessageDTO = buildChatMessageDTO(assistantMessageEntity);

                //组装
                messageList.add(assistantMessageDTO);
                messageList.add(userMessageDTO);

                response.setMessageList(messageList);
                response.setSessionId(sessionId);

                return RemoteResponse.buildSuccess(response);

            }

            // 会话id不为空根据会话id来查
            if (request.getScene() == SessionSceneEnum.HISTORY_MESSAGE.getCode() &&
                    StringUtils.isNotBlank(request.getSessionId())) {
                //先看下会话是不是有效
                ChatSessionEntity validSession = chatSessionRepository.findBySessionId(request.getSessionId(), request.getUserId(), request.getPlatform(), request.getBizType());
                if (validSession == null || Objects.equals(validSession.getStatus(), STATUS_DELETED)) {
                    return RemoteResponse.buildFail("会话已删除，请新建会话~");
                }

                List<ChatSessionMessageEntity> messageEntityList = chatSessionMessageRepository.findBySessionIdAndStatus(request.getSessionId(), request.getPageSize(), offset);
                List<ChatMessageDTO> collect = messageEntityList.stream().map(this::buildChatMessageDTO).collect(Collectors.toList());
                response.setMessageList(collect);
                response.setSessionId(request.getSessionId());
                return RemoteResponse.buildSuccess(response);
            }

            if (request.getScene() == SessionSceneEnum.LIKE_MESSAGE.getCode() && StringUtils.isNotBlank(request.getMsgId())) {
                int res = chatSessionMessageRepository.updateFeedbackByMessageId(
                        request.getMsgId(), request.getUserId(), request.getOperateType(),
                        request.getPlatform());
                response.setLikeStatus(res > 0);
                return RemoteResponse.buildSuccess(response);
            }

            if (request.getScene() == SessionSceneEnum.MESSAGE_DETAIL.getCode() && StringUtils.isNotBlank(request.getMsgId())) {
                ChatSessionMessageEntity chatSessionMessageEntity = chatSessionMessageRepository.findByMessageIdAndUserId(request.getMsgId(), request.getUserId());
                if (chatSessionMessageEntity != null) {
                    ChatMessageDTO chatMessageDTO = buildChatMessageDTO(chatSessionMessageEntity);
                    messageList.add(chatMessageDTO);
                    response.setMessageList(messageList);
                }
                return RemoteResponse.buildSuccess(response);
            }

            return RemoteResponse.buildSuccess(null);

        } catch (Exception e) {
            transaction.setStatus(e);
            log.error("<SessionManageApplicationService.queryMessages> error, req={}", JsonUtils.toJsonString(request), e);
            return RemoteResponse.buildFail(e.getMessage());
        } finally {
            transaction.complete();
        }
    }


    public RemoteResponse<ChatSessionResponse> operateSessions(SessionManageRequest request) {
        Transaction transaction = Cat.newTransaction("SessionManageApplicationService", "querySessions");
        try {
            transaction.setStatus(Transaction.SUCCESS);
            ChatSessionResponse response = new ChatSessionResponse();

            response.setRecordListUrl(String.format(request.getPlatform() == Platform.DP.getCode() ?
                    DP_LIST :  MT_LIST, request.getBizType()));

            if (request.getScene() == SessionSceneEnum.ENTRY_PAGE.getCode()) {

                if (StringUtils.isNotBlank(request.getSessionId())) {
                    ChatSessionDTO sessionDTO = new ChatSessionDTO();
                    sessionDTO.setSessionId(request.getSessionId());
                    response.setSessionList(Lists.newArrayList(sessionDTO));
                    return RemoteResponse.buildSuccess(response);
                }
                // 计算3分钟前的时间
                Date minutesGap = new Date(System.currentTimeMillis() - 3 * 60 * 1000);

                List<ChatSessionEntity> sessions = chatSessionRepository.findSessionsByUpdateTime(request.getUserId(), request.getPlatform(), request.getBizType(), 0, null, 1, 0, true);



                if (CollectionUtils.isNotEmpty(sessions)) {
                    String oldSessionId = sessions.get(0).getSessionId();
                    boolean hasAppointment = appointmentInfoRepository.findAppointmentBySessionId(oldSessionId);
                    if (hasAppointment || sessions.get(0).getUpdateTime().after(minutesGap)) {
                        ChatSessionDTO sessionDTO = new ChatSessionDTO();
                        sessionDTO.setSessionId(oldSessionId);
                        response.setSessionList(Lists.newArrayList(sessionDTO));
                        return RemoteResponse.buildSuccess(response);
                    }
                }

                String sessionId = String.valueOf(uidUtils.getNextId(UidUtils.CHAT_SESSION_LEAF_KEY));

                ChatSessionEntity chatSessionEntity = chatSessionRepository.buildSessionEntity(sessionId,
                        request.getUserId(), request.getBizType(), request.getPlatform(),
                        StringUtils.EMPTY, StringUtils.EMPTY);
                chatSessionRepository.insert(chatSessionEntity);

                response.setNextSessionId(sessionId);
                return RemoteResponse.buildSuccess(response);

            }

            // 删除会话逻辑
            if (request.getScene() == SessionSceneEnum.DELETE_SESSION.getCode()) {
                int res = 0;
                if (Boolean.TRUE.equals(request.getDeleteTotalSession())) {
                    res = chatSessionRepository.deleteAllSession(request.getUserId(), request.getPlatform(), request.getBizType());

                } else {
                    res = chatSessionRepository.deleteSingleSession(request.getSessionId());
                }
                response.setDeleteStatus(res > 0);
                return RemoteResponse.buildSuccess(response);
            }

            List<ChatSessionDTO> sessionList = new ArrayList<>();

            // 查询历史会话逻辑
            if (request.getScene() == SessionSceneEnum.HISTORY_SESSION.getCode()) {
                int offset = (request.getPageNo() - 1) * request.getPageSize();
                List<ChatSessionEntity> sessions = chatSessionRepository.findSessionsByUpdateTime(request.getUserId(), request.getPlatform(), request.getBizType(),
                        STATUS_NORMAL, null, request.getPageSize(), offset, false);

                List<ChatSessionDTO> collect = sessions.stream().map(e ->
                        buildChatSessionDTO(e, request.getPlatform(), request.getBizType()))
                        .collect(Collectors.toList());

                response.setSessionList(collect);

                return RemoteResponse.buildSuccess(response);
            }

            // 新建会话逻辑
            if (request.getScene() == SessionSceneEnum.NEW_SESSION.getCode()) {
                String sessionId = String.valueOf(uidUtils.getNextId(UidUtils.CHAT_SESSION_LEAF_KEY));

                ChatSessionEntity chatSessionEntity = chatSessionRepository.buildSessionEntity(sessionId,
                        request.getUserId(), request.getBizType(), request.getPlatform(),
                        StringUtils.EMPTY, StringUtils.EMPTY);
                chatSessionRepository.insert(chatSessionEntity);
                response.setNextSessionId(sessionId);

                return RemoteResponse.buildSuccess(response);
            }

            return RemoteResponse.buildSuccess(null);

        } catch (Exception e) {
            transaction.setStatus(e);
            log.error("<SessionManageApplicationService.querySessions> error, req={}", JsonUtils.toJsonString(request), e);
            return RemoteResponse.buildFail(e.getMessage());
        } finally {
            transaction.complete();
        }
    }

    public RemoteResponse<NavigateDTOResponse> queryNavigate(String bizType) {

        Transaction transaction = Cat.newTransaction("SessionManageApplicationService", "queryNavigate");
        transaction.setStatus(Transaction.SUCCESS);
        NavigateDTOResponse response = new NavigateDTOResponse();
        try {
            Map<String, String> fields = new HashMap<>();
            fields.put("bizType", bizType);
            List<HaimaContent> medicalAgentNavigateResources = haimaAcl.getContent("medical_agent_navigate_resource", fields);
            List<NavigateDTO> navigateDTOS = medicalAgentNavigateResources.stream().map(config -> {
                NavigateDTO navigateDTO = new NavigateDTO();
                navigateDTO.setIcon(config.getContentString("icon"));
                navigateDTO.setText(config.getContentString("text"));
                navigateDTO.setUrl(config.getContentString("url"));
                navigateDTO.setType(config.getContentInt("type"));
                navigateDTO.setQuery(config.getContentString("query"));
                return navigateDTO;
            }).collect(Collectors.toList());
            response.setNavigateDTOList(navigateDTOS);
            return RemoteResponse.buildSuccess(response);

        } catch (Exception e) {
            transaction.setStatus(e);
            log.error("<SessionManageApplicationService>queryNavigate error", e);
            return RemoteResponse.buildFail(e.getMessage());
        } finally {
            transaction.complete();
        }



    }




    private ChatSessionDTO buildChatSessionDTO(ChatSessionEntity sessionEntity, int platform, String bizType) {
        ChatSessionDTO chatSessionDTO = new ChatSessionDTO();
        chatSessionDTO.setSessionId(sessionEntity.getSessionId());
        chatSessionDTO.setTitle(StringUtils.isBlank(sessionEntity.getDigest()) ?
                sessionEntity.getTitle() : sessionEntity.getDigest());

        Date updatedTime = sessionEntity.getUpdateTime();
        // 创建日期时间格式 (yyyy-MM-dd)
        String dateTime = "";
        // 创建准确时间格式 (HH:mm)
        String explicitTime = "";
        if (updatedTime != null) {
            // 创建SimpleDateFormat来格式化日期
            SimpleDateFormat dateFormat = new java.text.SimpleDateFormat("yyyy-MM-dd");
            SimpleDateFormat timeFormat = new java.text.SimpleDateFormat("HH:mm");
            dateTime = dateFormat.format(updatedTime);
            explicitTime = timeFormat.format(updatedTime);
        }
        chatSessionDTO.setDateTime(dateTime);
        chatSessionDTO.setExplicitTime(explicitTime);
        chatSessionDTO.setSessionUrl(String.format(platform == Platform.DP.getCode() ? DP_DETAIL : MT_DETAIL, bizType, sessionEntity.getSessionId()));

        return chatSessionDTO;
    }




    private ChatMessageDTO buildChatMessageDTO(ChatSessionMessageEntity messageEntity) {
        ChatMessageDTO chatMessageDTO = new ChatMessageDTO();
        chatMessageDTO.setContent(messageEntity.getContent());
        chatMessageDTO.setRole(messageEntity.getRole());
        chatMessageDTO.setMsgId(messageEntity.getMessageId());
        chatMessageDTO.setLikeType(messageEntity.getType());

        return chatMessageDTO;
    }



}
