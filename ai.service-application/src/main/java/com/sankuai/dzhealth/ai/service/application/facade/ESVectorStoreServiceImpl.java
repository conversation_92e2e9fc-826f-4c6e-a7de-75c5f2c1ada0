package com.sankuai.dzhealth.ai.service.application.facade;

import com.alibaba.fastjson.JSON;
import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.meituan.mdp.boot.starter.thrift.annotation.MdpThriftServer;
import com.sankuai.dzhealth.ai.service.api.ESVectorStoreService;
import com.sankuai.dzhealth.ai.service.api.RemoteResponse;
import com.sankuai.dzhealth.ai.service.dto.DocumentDTO;
import com.sankuai.dzhealth.ai.service.infrastructure.vectorstore.config.EsVectorStoreConfig;
import com.sankuai.dzhealth.ai.service.request.DocumentSearchRequest;
import com.sankuai.meituan.poros.client.PorosApiClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.springframework.ai.document.Document;
import org.springframework.ai.vectorstore.SearchRequest;
import org.springframework.ai.vectorstore.VectorStore;
import org.springframework.ai.vectorstore.filter.FilterExpressionBuilder;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> youtianyu
 * @version : 0.1
 * @since : 2025/3/17
 */
@MdpThriftServer
@Slf4j
public class ESVectorStoreServiceImpl implements ESVectorStoreService {
    @Autowired
    private VectorStore esVectorStore;

    @Autowired
    private PorosApiClient porosApiClient;

    @Override
    public RemoteResponse<Boolean> buildIndex(List<DocumentDTO> documents) {
        Transaction transaction = Cat.newTransaction("ESVectorStoreService", "buildIndex");
        try {
            esVectorStore.add(documents.stream().map(this::convertToDocument).collect(Collectors.toList()));
            transaction.setSuccessStatus();
            log.info("buildIndex,documents={}", JSON.toJSONString(documents));
            return RemoteResponse.buildSuccess(true);
        } catch (Exception e) {
            log.error("buildIndex,documents={},e={}", JSON.toJSONString(documents), e, e);
            transaction.setStatus(e);
            return RemoteResponse.buildFail(e.getLocalizedMessage());
        } finally {
            transaction.complete();
        }
    }

    @Override
    public RemoteResponse<List<DocumentDTO>> similaritySearch(DocumentSearchRequest request) {
        Transaction transaction = Cat.newTransaction("ESVectorStoreService", "buildIndex");
        try {
            FilterExpressionBuilder.Op op = null;
            if (MapUtils.isNotEmpty(request.getMetaData())) {
                for (Map.Entry<String, List<String>> entry : request.getMetaData().entrySet()) {
                    if (entry.getValue() == null) {
                        continue;
                    }
                    List<String> filteredValues = entry.getValue().stream()
                            .filter(Objects::nonNull)
                            .filter(s -> !s.isEmpty())
                            .collect(Collectors.toList());

                    if (filteredValues.isEmpty()) {
                        continue;
                    }

                    op = op == null ?
                            new FilterExpressionBuilder().in(entry.getKey(), filteredValues.toArray(new String[0])) :
                            new FilterExpressionBuilder().and(op,
                                    new FilterExpressionBuilder().in(entry.getKey(), filteredValues.toArray(new String[0])));
                }
            }
            List<Document> documentList = esVectorStore.similaritySearch(SearchRequest.builder()
                    .query(request.getQuery())
                    .topK(request.getTopK())
                    .similarityThresholdAll()
                    .filterExpression(op != null ? op.build() : null)
                    .build());
            transaction.setSuccessStatus();
            List<DocumentDTO> result = Optional.ofNullable(documentList)
                    .orElse(Collections.emptyList())
                    .stream()
                    .map(this::convertToDocumentDTO)
                    .collect(Collectors.toList());
            log.info("similaritySearch,documents={},result={}", JSON.toJSONString(request), JSON.toJSONString(result));
            return RemoteResponse.buildSuccess(result);
        } catch (Exception e) {
            log.error("similaritySearch,documents={},e={}", JSON.toJSONString(request), e, e);
            transaction.setStatus(e);
            return RemoteResponse.buildFail(e.getLocalizedMessage());
        } finally {
            transaction.complete();
        }
    }

    @Override
    public RemoteResponse<Boolean> deleteIndex(List<String> ids) {
        Transaction transaction = Cat.newTransaction(getClass().getSimpleName(), "deleteIndex");
        try {
            porosApiClient.deleteByQuery(builder -> builder.index(EsVectorStoreConfig.INDEX_NAME)
                    .query(query -> query.ids(id -> id.values(ids))));
            transaction.setSuccessStatus();
            return RemoteResponse.buildSuccess(true);
        } catch (Exception e) {
            log.error("deleteIndex,ids={},e={}", ids, e, e);
            transaction.setStatus(e);
            return RemoteResponse.buildFail(e.getLocalizedMessage());
        } finally {
            transaction.complete();
        }
    }

    private DocumentDTO convertToDocumentDTO(Document document) {
        DocumentDTO documentDTO = new DocumentDTO();
        documentDTO.setId(document.getId());
        documentDTO.setText(document.getText());
        Map<String, String> metadata = Optional.of(document.getMetadata())
                .orElse(Collections.emptyMap())
                .entrySet()
                .stream()
                .collect(Collectors.toMap(Map.Entry::getKey, e -> String.valueOf(e.getValue())));
        documentDTO.setMetadata(metadata);
        documentDTO.setScore(document.getScore());
        return documentDTO;
    }

    private Document convertToDocument(DocumentDTO documentDTO) {
        Map<String, Object> metadata = Optional.of(documentDTO.getMetadata())
                .orElse(Collections.emptyMap())
                .entrySet()
                .stream()
                .collect(Collectors.toMap(Map.Entry::getKey, e -> String.valueOf(e.getValue())));
        return new Document.Builder().id(documentDTO.getId()).text(documentDTO.getText()).metadata(metadata).build();
    }

}