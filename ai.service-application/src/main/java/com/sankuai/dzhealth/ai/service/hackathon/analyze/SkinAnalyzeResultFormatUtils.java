package com.sankuai.dzhealth.ai.service.hackathon.analyze;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * @Author: guangyujie
 * @Date: 2025/9/12 16:00
 */
public class SkinAnalyzeResultFormatUtils {

    public static Map<String, String> parseSkinResult(String json) {
        Map<String, String> resultMap = new LinkedHashMap<>(); // 保证有序
        try {
            ObjectMapper mapper = new ObjectMapper();
            JsonNode root = mapper.readTree(json);
            JsonNode result = root.path("result");

            if (result.isMissingNode()) {
                resultMap.put("错误", "未检测到皮肤分析结果");
                return resultMap;
            }

            // 肤色
            int skinColor = result.path("skin_color").path("value").asInt();
            String skinColorDesc = "未知";
            if (skinColor == 0) {
                skinColorDesc = "偏白";
            } else if (skinColor == 1) {
                skinColorDesc = "自然";
            } else if (skinColor == 2) {
                skinColorDesc = "偏黑";
            }
            resultMap.put("肤色", skinColorDesc);

            // 肤龄
            int skinAge = result.path("skin_age").path("value").asInt();
            resultMap.put("肤龄", "大约 " + skinAge + " 岁");

            // 上下眼睑
            int leftEyelids = result.path("left_eyelids").path("value").asInt();
            int rightEyelids = result.path("right_eyelids").path("value").asInt();
            resultMap.put("眼睑", (leftEyelids == 1 || rightEyelids == 1) ? "可能存在下垂" : "正常");

            // 眼袋
            int eyePouch = result.path("eye_pouch").path("value").asInt();
            resultMap.put("眼袋", eyePouch == 1 ? "有眼袋" : "无明显眼袋");

            // 黑眼圈
            int darkCircle = result.path("dark_circle").path("value").asInt();
            String darkCircleDesc = "未知";
            switch (darkCircle) {
                case 0:
                    darkCircleDesc = "无明显黑眼圈";
                    break;
                case 1:
                    darkCircleDesc = "轻度黑眼圈";
                    break;
                case 2:
                    darkCircleDesc = "中度黑眼圈";
                    break;
                case 3:
                    darkCircleDesc = "重度黑眼圈";
                    break;
            }
            resultMap.put("黑眼圈", darkCircleDesc);

            // 法令纹
            int nasolabial = result.path("nasolabial_fold").path("value").asInt();
            resultMap.put("法令纹", nasolabial == 1 ? "存在法令纹" : "无明显法令纹");

            // 额头皱纹
            int foreheadWrinkle = result.path("forehead_wrinkle").path("value").asInt();
            resultMap.put("额头皱纹", foreheadWrinkle == 1 ? "有皱纹" : "光滑");

            // 鱼尾纹
            int crowsFeet = result.path("crows_feet").path("value").asInt();
            resultMap.put("鱼尾纹", crowsFeet == 1 ? "存在" : "无明显");

            // 黑头
            int blackhead = result.path("blackhead").path("value").asInt();
            resultMap.put("黑头", blackhead == 1 ? "有黑头" : "无明显黑头");

            // 毛孔情况（额头）
            int poresForehead = result.path("pores_forehead").path("value").asInt();
            resultMap.put("额头毛孔", poresForehead == 1 ? "毛孔明显" : "毛孔正常");

            // 色斑
            JsonNode spots = result.path("skin_spot").path("rectangle");
            if (spots.isArray() && spots.size() > 0) {
                resultMap.put("色斑", "检测到 " + spots.size() + " 处");
            } else {
                resultMap.put("色斑", "未检测到");
            }

            // ITA 肤色分级
            int skintone = result.path("skintone_ita").path("skintone").asInt();
            resultMap.put("肤色分级(ITA)", "等级 " + skintone);

        } catch (Exception e) {
            resultMap.clear();
            resultMap.put("错误", "解析皮肤数据失败：" + e.getMessage());
        }
        return resultMap;
    }

}
