package com.sankuai.dzhealth.ai.service.hackathon.image;

import com.meituan.image.client.ImageUploadClient;
import com.meituan.image.client.impl.ImageUploadClientImpl;
import com.meituan.image.client.pojo.ImageRequest;
import com.meituan.image.client.pojo.ImageResult;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;

import java.io.IOException;
import java.util.concurrent.TimeUnit;

/**
 * @author:chenwei
 * @time: 2025/3/13 15:07
 * @version: 0.0.1
 */
@Slf4j
public class ImageUploadClientWrapper {

    private ImageUploadClient client;

    public ImageUploadClientWrapper(String bucket, String clientId, String clientSecret) {
        client = new ImageUploadClientImpl(bucket, clientId, clientSecret, 5000, 5000);
    }

    public ImageResult postHttpImage(String url, String srcFileName) {
        ImageResult res;
        try {
            ImageRequest request = new ImageRequest(0, toByteArray(url), srcFileName, true, false);
            res = client.postImage(request);
        } catch (Exception e) {
            log.error("ImageUploadClientWrapper postHttpImage error", e);
            res = new ImageResult();
            res.setSuccess(false);
            res.setErrMsg("IOException,message:" + e.getMessage());
        }
        return res;
    }

    public ImageResult postHttpImage(byte[] bytes, String srcFileName) {
        ImageResult res;
        try {
            ImageRequest request = new ImageRequest(0, bytes, srcFileName, true, false);
            res = client.postImage(request);
        } catch (Exception e) {
            log.error("ImageUploadClientWrapper postHttpImage error", e);
            res = new ImageResult();
            res.setSuccess(false);
            res.setErrMsg("IOException,message:" + e.getMessage());
        }
        return res;
    }

    public byte[] toByteArray(String httpUrl) throws IOException {
        OkHttpClient client = new OkHttpClient.Builder().connectTimeout(5, TimeUnit.SECONDS)
                .readTimeout(5, TimeUnit.SECONDS)
                .writeTimeout(5, TimeUnit.SECONDS)
                .build();

        log.info("httpUrl:{}", httpUrl);
        Request request = new Request.Builder().url(httpUrl).build();

        try (Response response = client.newCall(request).execute()) {
            if (response.body() != null) {
                return response.body().bytes();
            }
        }
        return new byte[0];
    }
}
