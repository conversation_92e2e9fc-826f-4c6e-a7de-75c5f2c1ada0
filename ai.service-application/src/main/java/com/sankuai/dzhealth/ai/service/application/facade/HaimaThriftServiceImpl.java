package com.sankuai.dzhealth.ai.service.application.facade;

import com.alibaba.fastjson.JSON;
import com.dianping.haima.entity.haima.HaimaContent;
import com.meituan.mdp.boot.starter.thrift.annotation.MdpThriftServer;
import com.sankuai.dzhealth.ai.service.api.HaimaThriftService;
import com.sankuai.dzhealth.ai.service.dto.HaimaContentDTO;
import com.sankuai.dzhealth.ai.service.infrastructure.acl.HaimaAcl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 海马运营位服务 Thrift 接口实现
 * 提供与海马系统交互的方法，用于获取运营配置内容
 */
@Service
@Slf4j
@MdpThriftServer
public class HaimaThriftServiceImpl implements HaimaThriftService {

    @Resource
    private HaimaAcl haimaAcl;

    /**
     * 根据场景键和字段获取内容
     * @param sceneKey 场景键
     * @param fields 字段
     * @return 内容列表
     */
    @Override
    public List<HaimaContentDTO> getContent(String sceneKey, Map<String, String> fields) {
        try {
            if (StringUtils.isBlank(sceneKey)) {
                log.warn("getContent failed: sceneKey is blank");
                return Collections.emptyList();
            }
            // 调用 HaimaAcl 的 getContent 方法
            List<HaimaContent> contentList = haimaAcl.getContent(sceneKey, fields);
            if (CollectionUtils.isEmpty(contentList)) {
                log.info("getContent: no content found for sceneKey={}, fields={}", sceneKey, fields);
                return Collections.emptyList();
            }
            // 将 HaimaContent 转换为 HaimaContentDTO
            return contentList.stream()
                    .map(this::convertToDTO)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("getContent error: sceneKey={}, fields={}", sceneKey, fields, e);
            return Collections.emptyList();
        }
    }
    /**
     * 将 HaimaContent 转换为 HaimaContentDTO
     * @param content HaimaContent 对象
     * @return HaimaContentDTO 对象
     */
    private HaimaContentDTO convertToDTO(HaimaContent content) {
        if (content == null) {
            return null;
        }
        HaimaContentDTO dto = new HaimaContentDTO();
        dto.setConfigId(content.getConfigId());
        dto.setContentId(content.getContentId());
        dto.setExtJson(content.getExtJson());
        dto.setCreateTime(content.getCreateTime());
        dto.setUpdateTime(content.getUpdateTime());
        // 解析 extJson 到 contentMap
        try {
            Map<String, Object> jsonMap = JSON.parseObject(content.getExtJson(), Map.class);
            if (jsonMap != null) {
                Map<String, String> contentMap = new HashMap<>();
                jsonMap.forEach((key, value) -> {
                    if (value != null) {
                        contentMap.put(key, String.valueOf(value));
                    }
                });
                dto.setContentMap(contentMap);
            }
        } catch (Exception e) {
            log.warn("Failed to parse extJson to contentMap: {}", content.getExtJson(), e);
        }
        return dto;
    }
}