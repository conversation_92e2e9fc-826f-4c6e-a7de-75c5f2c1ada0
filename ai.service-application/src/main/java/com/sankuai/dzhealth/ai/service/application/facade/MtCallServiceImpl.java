package com.sankuai.dzhealth.ai.service.application.facade;


import com.alibaba.fastjson.JSON;
import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.meituan.mdp.boot.starter.thrift.annotation.MdpThriftServer;
import com.sankuai.dzhealth.ai.service.api.MtCallService;
import com.sankuai.dzhealth.ai.service.api.RemoteResponse;
import com.sankuai.dzhealth.ai.service.domain.service.MtPhoneCallService;
import com.sankuai.dzhealth.ai.service.domain.utils.context.ChatSummaryContext;
import com.sankuai.dzhealth.ai.service.domain.utils.context.MtCallContext;
import com.sankuai.dzhealth.ai.service.domain.utils.context.MtPhoneCallContext;
import com.sankuai.dzhealth.ai.service.dto.MessageDTO;
import com.sankuai.dzhealth.ai.service.dto.StatusDTO;
import com.sankuai.dzhealth.ai.service.infrastructure.dal.entity.ChatMessageEntity;
import com.sankuai.dzhealth.ai.service.request.ChatSummaryRequest;
import com.sankuai.dzhealth.ai.service.request.MtCallRequest;
import com.sankuai.dzhealth.ai.service.request.MtCallTaskRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

@MdpThriftServer
@Slf4j
public class MtCallServiceImpl implements MtCallService {

    private static String TYPE = MtCallServiceImpl.class.getSimpleName();

    @Autowired
    private MtPhoneCallService mtPhoneCallService;

    @Override
    public RemoteResponse<MessageDTO> generateCallTask(MtCallTaskRequest request) {
        Transaction transaction = Cat.newTransaction(TYPE, "generateCallTask");
        transaction.setSuccessStatus();
        try {

            MtPhoneCallContext mtPhoneCallContext= MtPhoneCallContext.builder()
                    .question(request.getQuestion())
                    .sessionId(request.getSessionId())
                    .shopId(request.getMtShopId())
                    .userId(request.getUserId())
                    .build();
            ChatMessageEntity chatMessageEntity=mtPhoneCallService.generateCallTask(mtPhoneCallContext);
            MessageDTO messageDTO=new MessageDTO();
            messageDTO.setContent(chatMessageEntity.getContent());
            messageDTO.setMsgId(chatMessageEntity.getId());
            messageDTO.setRole(chatMessageEntity.getRole());
            return RemoteResponse.buildSuccess(messageDTO);
        } catch (Exception e) {
            transaction.setStatus(e);
            log.error("generateCallTask.fail,request={}", JSON.toJSONString(request), e);
            return RemoteResponse.buildFail(e.getMessage());
        } finally {
            transaction.complete();
        }
    }

    @Override
    public RemoteResponse<StatusDTO> mtCallHospital(MtCallRequest request){
        Transaction transaction = Cat.newTransaction(TYPE, "mtCallHospital");
        transaction.setSuccessStatus();
        try {
            checkMtCallParam(request);
            MtCallContext mtCallContext=MtCallContext.builder()
                    .taskId(request.getTaskId())
                    .userId(request.getUserId())
                    .conversationId(request.getConversationId())
                    .messageId(request.getMessageId())
                    .build();
            StatusDTO statusDTO=new StatusDTO();
            statusDTO.setStatus(mtPhoneCallService.mtCallHospital(mtCallContext));
            return RemoteResponse.buildSuccess(statusDTO);
        } catch (Exception e) {
            transaction.setStatus(e);
            log.error("mtCallHospital.fail,request={}", JSON.toJSONString(request), e);
            return RemoteResponse.buildFail(e.getMessage());
        } finally {
            transaction.complete();
        }
    }

    @Override
    public RemoteResponse<String> callTemplateMatch(String question) {
        Transaction transaction = Cat.newTransaction(TYPE, "callTemplateMatch");
        transaction.setSuccessStatus();
        try {
            if (StringUtils.isBlank(question)){
                return RemoteResponse.buildFail("问题为空");
            }
            return RemoteResponse.buildSuccess(mtPhoneCallService.callTemplateMatch(question));
        } catch (Exception e) {
            transaction.setStatus(e);
            log.error("callTemplateMatch.fail,request={}", JSON.toJSONString(question), e);
            return RemoteResponse.buildFail(e.getMessage());
        } finally {
            transaction.complete();
        }
    }

    @Override
    public RemoteResponse<StatusDTO> mtCallHospitalCancel(MtCallRequest request) {
        Transaction transaction = Cat.newTransaction(TYPE, "mtCallHospitalCancel");
        transaction.setSuccessStatus();
        try {
            checkMtCallParam(request);
            MtCallContext mtCallContext=MtCallContext.builder()
                    .taskId(request.getTaskId())
                    .userId(request.getUserId())
                    .conversationId(request.getConversationId())
                    .messageId(request.getMessageId())

                    .build();
            StatusDTO statusDTO=new StatusDTO();
            statusDTO.setStatus(mtPhoneCallService.mtCallHospitalCancel(mtCallContext));
            return RemoteResponse.buildSuccess(statusDTO);
        } catch (Exception e) {
            transaction.setStatus(e);
            log.error("mtCallHospitalCancel.fail,request={}", JSON.toJSONString(request), e);
            return RemoteResponse.buildFail(e.getMessage());
        } finally {
            transaction.complete();
        }
    }

    @Override
    public RemoteResponse<String> getChatSummary(ChatSummaryRequest request) {
        Transaction transaction = Cat.newTransaction(TYPE, "mtCallHospitalCancel");
        transaction.setSuccessStatus();
        try {

            ChatSummaryContext summaryContext=ChatSummaryContext.builder()
                    .taskId(request.getTaskId())
                    .userIntention(request.getUserIntention())
                    .sceneType(request.getSceneType())
                    .dialogueContent(request.getDialogueContent())
                    .build();
            return RemoteResponse.buildSuccess(mtPhoneCallService.getChatSummary(summaryContext).toString());
        } catch (Exception e) {
            transaction.setStatus(e);
            log.error("mtCallHospitalCancel.fail,request={}", JSON.toJSONString(request), e);
            return RemoteResponse.buildFail(e.getMessage());
        } finally {
            transaction.complete();
        }
    }

    @Override
    public RemoteResponse<MessageDTO> getDialogDate(MtCallRequest request) {
        Transaction transaction = Cat.newTransaction(TYPE, "getDialogDate");
        transaction.setSuccessStatus();
        try {
            if (request==null||request.getTaskId()==null||request.getTaskId()<=0){
                throw new IllegalArgumentException("请求参数异常");
            }
            MessageDTO messageDTO=new MessageDTO();
            messageDTO.setContent(mtPhoneCallService.getDialogDate(request.getTaskId()));
            return RemoteResponse.buildSuccess(messageDTO);
        } catch (Exception e) {
            transaction.setStatus(e);
            log.error("getDialogDate.fail,taskId={}", request.getTaskId(), e);
            return RemoteResponse.buildFail(e.getMessage());
        } finally {
            transaction.complete();
        }
    }


    private  void checkMtCallParam(MtCallRequest request){
        if (request == null||request.getTaskId()==null||request.getTaskId()<=0||request.getMessageId()==null||StringUtils.isBlank(request.getUserId())
                ||StringUtils.isBlank(request.getConversationId()))
        {
            throw new IllegalArgumentException("请求参数异常");
        }
    }
}
