package com.sankuai.dzhealth.ai.service.hackathon.analyze;

import com.dianping.lion.Environment;
import com.meituan.service.inf.kms.client.Kms;
import com.meituan.service.inf.kms.utils.KmsResultNullException;
import com.sankuai.dzhealth.ai.service.infrastructure.chatmodel.OneApiChatModel;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.openai.OpenAiChatOptions;
import org.springframework.ai.openai.api.OpenAiApi;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestClient;

import java.time.Duration;

/**
 * @Author: guangyujie
 * @Date: 2025/9/12 19:36
 */
@Component
public class AnalyzeChatClient {

    public static final String MODEL = "qwen3-235b-a22b-Instruct-2507-meituan";

    @Bean
    public ChatClient analyzeChatClient() throws KmsResultNullException {
        // 配置RestClient.Builder以设置超时时间
        RestClient.Builder restClientBuilder = RestClient.builder()
                .requestFactory(new org.springframework.http.client.SimpleClientHttpRequestFactory() {{
                    setConnectTimeout((int) Duration.ofMinutes(5).toMillis());
                    setReadTimeout((int) Duration.ofMinutes(5).toMillis());
                }});

        OpenAiApi openAiApi = new OpenAiApi.Builder()
                .completionsPath("/chat/completions")
                .baseUrl("https://aigc.sankuai.com/v1/openai/native")
                .apiKey(Kms.getByName(Environment.getAppName(), "friday.agent.appId"))
                .restClientBuilder(restClientBuilder)
                .build();
        OpenAiChatOptions openAiChatOptions = OpenAiChatOptions.builder()
                .model(MODEL)
                .temperature(0.0)
                .reasoningEffort("high")
                .maxTokens(10000)
                .build();
        OneApiChatModel chatModel = OneApiChatModel.builder()
                .openAiApi(openAiApi)
                .defaultOptions(openAiChatOptions)
                .build();

        return ChatClient.builder(chatModel).build();
    }

}
