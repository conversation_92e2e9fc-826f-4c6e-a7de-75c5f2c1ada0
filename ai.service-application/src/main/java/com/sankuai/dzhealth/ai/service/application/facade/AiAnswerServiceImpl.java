package com.sankuai.dzhealth.ai.service.application.facade;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import com.google.common.collect.Lists;
import com.meituan.beauty.fundamental.light.plat.Platform;
import com.meituan.mdp.boot.starter.thrift.annotation.MdpThriftServer;
import com.sankuai.dzhealth.ai.service.api.AiAnswerService;
import com.sankuai.dzhealth.ai.service.api.RemoteResponse;
import com.sankuai.dzhealth.ai.service.domain.chat.model.ChatSessionBO;
import com.sankuai.dzhealth.ai.service.domain.chat.model.ChatSessionMessageBO;
import com.sankuai.dzhealth.ai.service.domain.service.ChatSessionService;
import com.sankuai.dzhealth.ai.service.domain.utils.context.AiAnswerContext;
import com.sankuai.dzhealth.ai.service.dto.*;
import com.sankuai.dzhealth.ai.service.enums.BizSceneEnum;
import com.sankuai.dzhealth.ai.service.enums.BusinessTypeEnum;
import com.sankuai.dzhealth.ai.service.infrastructure.acl.ShopAcl;
import com.sankuai.dzhealth.ai.service.infrastructure.dal.entity.ChatConversationEntity;
import com.sankuai.dzhealth.ai.service.request.SessionRequest;
import com.sankuai.sinai.data.api.dto.MtPoiDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 * @author:chenwei
 * @time: 2025/3/20 13:37
 * @version: 0.0.1
 */
@MdpThriftServer
@Slf4j
public class AiAnswerServiceImpl implements AiAnswerService {

    private static final String TYPE = AiAnswerServiceImpl.class.getSimpleName();



    private static final byte SESSION_DELETE_STATUS = 0;
    private static final byte SESSION_FINISH_STATUS = 2;

    private static final List<Integer> PHONE_TASK_STATUS = Lists.newArrayList(2, 3, 4, 5);

    private static final String RELATED_QUESTION = ":::{<GuessAskQuestion>relatedQuestions</GuessAskQuestion>}:::";

    private static final String MT_SESSION_DETAIL_URL = "imeituan://www.meituan.com/mrn?mrn_biz=gcbu&mrn_entry=medical-aigc-guide&mrn_component=MedicalAIGuide&shopId=%s&sessionId=%s";

    private static final String DP_SESSION_DETAIL_URL = "dianping://mrn?mrn_biz=gcbu&mrn_entry=medical-aigc-guide&mrn_component=MedicalAIGuide&shopId=%s&sessionId=%s";

    private static final String MT_SESSION_LIST_URL = "imeituan://www.meituan.com/mrn?mrn_biz=gcbu&mrn_entry=medical-aigc-guide&mrn_component=MedicalAIGuideRecordList&shopId=%s";

    private static final String DP_SESSION_LIST_URL = "dianping://mrn?mrn_biz=gcbu&mrn_entry=medical-aigc-guide&mrn_component=MedicalAIGuideRecordList&shopId=%s";


    @Autowired
    private ChatSessionService chatSessionService;

    @Autowired
    private ShopAcl shopAcl;

    @Override
    public RemoteResponse<SessionResponse> queryHistorySessions(SessionRequest request) {
        Transaction transaction = Cat.newTransaction(TYPE, "queryHistorySessions");
        transaction.setSuccessStatus();
        try {
            checkParam(request);
            setDefaultPageParam(request);
            if (request.getScene() == null) {
                request.setScene(1);
            }
            Long requestShopId = request.getShopId();
            if (request.getPlatform() != null && request.getPlatform() == 1) {
                Long mtShopId = shopAcl.queryMtIdByDpId(request.getShopId());
                request.setShopId(mtShopId);
            }
            Long nextSessionId = 0L;
            String recordListUrl = "";
            SessionResponse response = new SessionResponse();
            recordListUrl = request.getPlatform() == 2 ?
                    String.format(MT_SESSION_LIST_URL, requestShopId) :
                    String.format(DP_SESSION_LIST_URL, requestShopId);
            response.setRecordListUrl(recordListUrl);
            if (request.getScene() == 2) {
                ChatConversationEntity newSession = chatSessionService.createSession(
                        AiAnswerContext.builder()
                                .userId(getPlatformUserId(request.getUserId(), request.getPlatform()))
                                .content("")
                                .shopId(request.getShopId())
                                .mtShopId(request.getShopId())
                                .bizScene(BizSceneEnum.PUBLIC_HOSPITAL.getBizScene())
                                .businessType(BusinessTypeEnum.SHOP.getType())
                                .build());
                nextSessionId = newSession.getId();
                response.setNextSessionId(nextSessionId);
                return RemoteResponse.buildSuccess(response);
            }
            List<ChatSessionBO> chatSessionBOS;
            List<SessionDTO> sessionDTOList = new ArrayList<>();
            if (request.getScene() == 1) {
                chatSessionBOS = chatSessionService.querySession(getPlatformUserId(request.getUserId(), request.getPlatform()), request.getPageNo(), request.getPageSize(), request.getShopId());
                sessionDTOList = chatSessionBOS.stream()
                        .map(bo -> {
                            SessionDTO dto = new SessionDTO();
                            dto.setSessionId(bo.getSessionId());
                            dto.setTitle(bo.getTitle());
                            dto.setDateTime(bo.getDateTime());
                            dto.setExplicitTime(bo.getExplicitTime());
                            dto.setSessionUrl(request.getPlatform() == 2 ?
                                    String.format(MT_SESSION_DETAIL_URL, requestShopId, bo.getSessionId()) :
                                    String.format(DP_SESSION_DETAIL_URL, requestShopId, bo.getSessionId()));
                            return dto;
                        }).toList();
            }
            if (request.getScene() == 0 ) {
                chatSessionBOS = chatSessionService.querySessionWithEmpty(getPlatformUserId(request.getUserId(), request.getPlatform()), request.getPageNo(), request.getPageSize(), request.getShopId());
                sessionDTOList = chatSessionBOS.stream()
                        .map(bo -> {
                            SessionDTO dto = new SessionDTO();
                            dto.setSessionId(bo.getSessionId());
                            dto.setTitle(bo.getTitle());
                            dto.setDateTime(bo.getDateTime());
                            dto.setExplicitTime(bo.getExplicitTime());
                            dto.setSessionUrl(request.getPlatform() == 2 ?
                                    String.format(MT_SESSION_DETAIL_URL, requestShopId, bo.getSessionId()) :
                                    String.format(DP_SESSION_DETAIL_URL, requestShopId, bo.getSessionId()));
                            return dto;
                        }).toList();
                if (!chatSessionBOS.isEmpty()) {
                    ChatSessionBO firstSession = chatSessionBOS.get(0);
                    String dateTime = firstSession.getDateTime();
                    String explicitTime = firstSession.getExplicitTime();

                    Boolean phoneTaskDoing = chatSessionService.hasPhoneTaskDoing(firstSession.getSessionId(),
                            PHONE_TASK_STATUS);

                    if (dateTime != null && explicitTime != null) {
                        // 将日期和时间组合成完整的日期时间字符串
                        String fullDateTime = dateTime + " " + explicitTime;

                        // 解析完整的日期时间
                        LocalDateTime sessionDateTime = LocalDateTime.parse(
                                fullDateTime,
                                DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm")
                        );
                        // 获取当前时间
                        LocalDateTime currentDateTime = LocalDateTime.now();
                        // 计算时间差（分钟）
                        long minutesDiff = Duration.between(sessionDateTime, currentDateTime).toMinutes();

                        //超过3分钟开启新会话
                        if (minutesDiff > 3 && Boolean.TRUE.equals(!phoneTaskDoing)) {
                            ChatConversationEntity session = chatSessionService.createSession(
                                AiAnswerContext.builder()
                                        .userId(getPlatformUserId(request.getUserId(), request.getPlatform()))
                                        .content("")
                                        .shopId(request.getShopId())
                                        .mtShopId(request.getShopId())
                                        .bizScene(BizSceneEnum.PUBLIC_HOSPITAL.getBizScene())
                                        .businessType(BusinessTypeEnum.SHOP.getType())
                                        .build());
                            nextSessionId = session.getId();
                        }
                    }
                } else {  //无历史会话，需要新建
                    ChatConversationEntity newSession = chatSessionService.createSession(
                            AiAnswerContext.builder()
                                    .userId(getPlatformUserId(request.getUserId(), request.getPlatform()))
                                    .content("")
                                    .shopId(request.getShopId())
                                    .mtShopId(request.getShopId())
                                    .bizScene(BizSceneEnum.PUBLIC_HOSPITAL.getBizScene())
                                    .businessType(BusinessTypeEnum.SHOP.getType())
                                    .build());
                    nextSessionId = newSession.getId();
                }

                String entranceConfig = Lion.getString(Environment.getAppName(), "entrance.config");
                JSONObject jsonConfig = JSON.parseObject(entranceConfig);
                response.setTitle(jsonConfig.getString("title"));
                response.setHeadPic(jsonConfig.getString("headPic"));
                String introduce = jsonConfig.getString("introduce");
                MtPoiDTO mtPoiDTO = shopAcl.getMtShop(request.getShopId());
                if (mtPoiDTO != null) {
                    String shopName = mtPoiDTO.getName();
                    String branchName = mtPoiDTO.getBranchName();

                    String fullShopName = StringUtils.isNotBlank(branchName) ? 
                            shopName + "(" + branchName.trim() + ")" :
                            shopName;

                    response.setShopName(fullShopName);
                    response.setIntroduce(String.format(introduce, fullShopName));
                    
                }


            }
            // 构建返回结果
            response.setSessionList(sessionDTOList);
            response.setNextSessionId(nextSessionId);
            return RemoteResponse.buildSuccess(response);
        } catch (Exception e) {
            transaction.setStatus(e);
            log.error("queryHistorySessions,request={}", JSON.toJSONString(request), e);
            return RemoteResponse.buildFail(e.getMessage());
        } finally {
            transaction.complete();
        }
    }

    private String getPlatformUserId(Long userId, int platform) {
        if (platform == Platform.MT.getCode()) {
            return "m-" + userId;
        }
        return "d-" + userId;
    }

    @Override
    public RemoteResponse<MessageResponse> querySessionMessages(SessionRequest request) {
        Transaction transaction = Cat.newTransaction(TYPE, "querySessionMessages");
        transaction.setSuccessStatus();
        try {
            MessageResponse response = new MessageResponse();
            checkParam(request);
            // 使用msgId参数直接查询
            List<ChatSessionMessageBO> chatSessionMessageBOS;
            if (request.getMsgId() != null && request.getMsgId() > 0) {
                log.info("直接根据msgId查询消息: msgId={}", request.getMsgId());
                // 修改这里的调用方式，处理单个对象而不是列表
                ChatSessionMessageBO messageBO = chatSessionService.queryMsgById(request.getMsgId(), getPlatformUserId(request.getUserId(), request.getPlatform()));
                if (messageBO != null) {
                    chatSessionMessageBOS = List.of(messageBO);
                } else {
                    chatSessionMessageBOS = List.of();
                }
            } else {
                // 只有在按会话查询时才检查sessionId
                if (request.getSessionId() == null || request.getSessionId() <= 0) {
                    return RemoteResponse.buildIllegalArgument("sessionId <=0 error!");
                }
                //会话已删除不展示消息
                ChatConversationEntity conversationEntity = chatSessionService.checkSession(request.getSessionId(), getPlatformUserId(request.getUserId(), request.getPlatform()));
                if (conversationEntity == null || conversationEntity.getStatus() == SESSION_DELETE_STATUS) {
                    return RemoteResponse.buildFail("当前会话已删除，请新建会话~");
                }

                setDefaultPageParam(request);
                log.info("根据sessionId查询会话消息: sessionId={}", request.getSessionId());
                chatSessionMessageBOS = chatSessionService.queryMsgBySessionId(
                        request.getSessionId(), getPlatformUserId(request.getUserId(), request.getPlatform()),
                        request.getPageNo(), request.getPageSize());
            }
            // 将ChatSessionMessageBO转换为MessageDTO
            List<MessageDTO> messageDTOList = chatSessionMessageBOS.stream()
                    .map(bo -> {
                        MessageDTO dto = new MessageDTO();
                        dto.setMsgId(bo.getMsgId());
                        dto.setRole(bo.getRole());
                        dto.setLikeType(bo.getLikeType());
                        dto.setContent(bo.getContent().replace(RELATED_QUESTION, ""));
                        return dto;
                    }).toList();
            // 构建返回结果

            response.setMessageList(messageDTOList);
            return RemoteResponse.buildSuccess(response);
        } catch (Exception e) {
            transaction.setStatus(e);
            log.error("querySessionMessages,request={}", JSON.toJSONString(request), e);
            return RemoteResponse.buildFail(e.getMessage());
        } finally {
            transaction.complete();
        }
    }

    @Override
    public RemoteResponse<StatusDTO> finishSession(SessionRequest request) {
        Transaction transaction = Cat.newTransaction(TYPE, "finishSession");
        transaction.setSuccessStatus();
        try {
            checkParam(request);
            if (request.getSessionId() == null || request.getSessionId() <= 0) {
                return RemoteResponse.buildIllegalArgument("sessionId error!");
            }
            boolean res = chatSessionService.updateSession(request.getSessionId(), SESSION_FINISH_STATUS, null);
            StatusDTO statusDTO = new StatusDTO();
            statusDTO.setStatus(res);
            return RemoteResponse.buildSuccess(statusDTO);
        } catch (Exception e) {
            transaction.setStatus(e);
            log.error("finishSession,request={}", JSON.toJSONString(request), e);
            return RemoteResponse.buildFail(e.getMessage());
        } finally {
            transaction.complete();
        }
    }

    @Override
    public RemoteResponse<StatusDTO> operateMessage(SessionRequest request) {
        Transaction transaction = Cat.newTransaction(TYPE, "operateMessage");
        transaction.setSuccessStatus();
        try {
            checkParam(request);
            if (request.getMsgId() <= 0) {
                return RemoteResponse.buildIllegalArgument("msgId error!");
            }
            boolean res = chatSessionService.updateFeedback(request.getMsgId(), getPlatformUserId(request.getUserId(), request.getPlatform()) , request.getOperateType());
            StatusDTO statusDTO = new StatusDTO();
            statusDTO.setStatus(res);
            return RemoteResponse.buildSuccess(statusDTO);
        } catch (Exception e) {
            transaction.setStatus(e);
            log.error("operateMessage,request={}", JSON.toJSONString(request), e);
            return RemoteResponse.buildFail(e.getMessage());
        } finally {
            transaction.complete();
        }
    }

    @Override
    public RemoteResponse<StatusDTO> deleteSession(SessionRequest request) {
        Transaction transaction = Cat.newTransaction(TYPE, "deleteSession");
        transaction.setSuccessStatus();
        try {
            checkParam(request);
            if ((request.getSessionId() == null || request.getSessionId() <= 0) && Boolean.FALSE.equals(request.getTotalSession())) {
                return RemoteResponse.buildIllegalArgument("sessionId error!");
            }
            boolean res = false;
            if (request.getSessionId() != null && request.getSessionId() > 0) {
                res = chatSessionService.updateSession(request.getSessionId(), SESSION_DELETE_STATUS, null);
            } else if (Boolean.TRUE.equals(request.getTotalSession())) {
                res = chatSessionService.updateSessionByUserId(getPlatformUserId(request.getUserId(), request.getPlatform()) , SESSION_DELETE_STATUS);
            }
            StatusDTO statusDTO = new StatusDTO();
            statusDTO.setStatus(res);
            return RemoteResponse.buildSuccess(statusDTO);
        } catch (Exception e) {
            transaction.setStatus(e);
            log.error("deleteSession,request={}", JSON.toJSONString(request), e);
            return RemoteResponse.buildFail(e.getMessage());
        } finally {
            transaction.complete();
        }
    }

    private void checkParam(SessionRequest request) {
        if (request == null || request.getUserId() <= 0) {
            throw new IllegalArgumentException("userId error!");
        }
        if (request.getPlatform() == null) {
            throw new IllegalArgumentException("platform is null!");
        }
    }

    private void setDefaultPageParam(SessionRequest request) {
        if (request.getPageNo() == null) {
            request.setPageNo(1);
        }
        if (request.getPageSize() == null || request.getPageSize() > 50) {
            request.setPageSize(50);
        }
    }

}
