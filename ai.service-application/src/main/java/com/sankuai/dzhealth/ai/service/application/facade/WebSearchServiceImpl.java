package com.sankuai.dzhealth.ai.service.application.facade;

import com.alibaba.fastjson.JSON;
import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.meituan.mdp.boot.starter.thrift.annotation.MdpThriftServer;
import com.sankuai.dzhealth.ai.service.api.RemoteResponse;
import com.sankuai.dzhealth.ai.service.api.WebSearchService;
import com.sankuai.dzhealth.ai.service.dto.WebSearchDTO;
import com.sankuai.dzhealth.ai.service.infrastructure.acl.FridayWebSearchAcl;
import com.sankuai.dzhealth.ai.service.infrastructure.model.FridaySearchResult;
import com.sankuai.dzhealth.ai.service.request.WebSearchRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> youtianyu
 * @version : 0.1
 * @since : 2025/4/8
 */
@MdpThriftServer
@Slf4j
public class WebSearchServiceImpl implements WebSearchService {
    @Autowired
    private FridayWebSearchAcl fridayWebSearchAcl;

    @Override
    public RemoteResponse<List<WebSearchDTO>> search(WebSearchRequest request) {
        if (request == null || StringUtils.isBlank(request.getQuery())) {
            return RemoteResponse.buildFail("参数错误");
        }
        Transaction transaction = Cat.newTransaction(getClass().getSimpleName(), "search");
        try {
            List<FridaySearchResult> search =
                    fridayWebSearchAcl.search(request.getQuery(), request.getSites(), request.getTopK());
            RemoteResponse<List<WebSearchDTO>> response =
                    RemoteResponse.buildSuccess(search.stream().map(this::toWebSearchDTO).collect(Collectors.toList()));
            transaction.setSuccessStatus();
            return response;
        } catch (Exception e) {
            log.error("request={},e={}", JSON.toJSONString(request), e, e);
            transaction.setStatus(e);
            return RemoteResponse.buildFail(e.getMessage());
        } finally {
            transaction.complete();
        }
    }

    private WebSearchDTO toWebSearchDTO(FridaySearchResult fridaySearchResult) {
        if (fridaySearchResult == null) {
            return null;
        }
        WebSearchDTO webSearchDTO = new WebSearchDTO();
        webSearchDTO.setUrl(fridaySearchResult.getUrl());
        webSearchDTO.setSnippet(fridaySearchResult.getSnippet());
        webSearchDTO.setName(fridaySearchResult.getName());
        webSearchDTO.setDatePublished(fridaySearchResult.getDatePublished());
        return webSearchDTO;

    }
}
