package com.sankuai.dzhealth.ai.service.application.facade;

import com.meituan.mdp.boot.starter.thrift.annotation.MdpThriftServer;
import com.sankuai.dzhealth.ai.service.api.xhs.XhsNoteCallbackService;
import com.sankuai.dzhealth.ai.service.domain.utils.JsonUtils;
import com.sankuai.dzhealth.ai.service.dto.xhs.XhsNoteResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * @author:chenwei
 * @time: 2025/6/25 19:17
 * @version: 0.0.1
 */
@MdpThriftServer
@Slf4j
@RequiredArgsConstructor
public class XhsNoteCallbackServiceImpl implements XhsNoteCallbackService {
    @Override
    public void onNoteGenerated(XhsNoteResponse response) {
        log.info("get note success, response={}", JsonUtils.toJsonString(response));
    }
}
