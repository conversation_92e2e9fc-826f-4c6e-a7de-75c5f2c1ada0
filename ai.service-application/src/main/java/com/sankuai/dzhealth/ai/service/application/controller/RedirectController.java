package com.sankuai.dzhealth.ai.service.application.controller;

import com.alibaba.fastjson.JSON;
import com.dianping.cat.Cat;
import com.meituan.mdp.boot.starter.config.annotation.MdpConfig;
import com.sankuai.security.sdk.SecSdk;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.view.RedirectView;

import java.net.URI;
import java.net.URISyntaxException;
import java.util.List;

/**
 * 处理HTTP重定向请求的控制器
 */
@RestController
@Slf4j
public class RedirectController {

    @MdpConfig("RedirectUrlWhitelist:[]")
    private String redirectWhitelist;

    @MdpConfig("EnableFlexibleRedirect:false")
    private boolean enableFlexibleRedirect;

    @MdpConfig("BlockedProtocols:[\"javascript:\",\"data:\",\"vbscript:\",\"file:\"]")
    private String blockedProtocols;

    @MdpConfig("BlockedDomains:[]")
    private String blockedDomains;

    /**
     * 使用RedirectView进行重定向
     *
     * @param url 目标URL
     * @return ModelAndView对象
     */
    @GetMapping("/hospital/redirect/view")
    public ModelAndView redirect(@RequestParam("url") String url) {
        Cat.logEvent("redirect", "start");
        log.info("RedirectController.redirect url={}", url);

        // 如果启用了灵活重定向模式，使用自定义安全检查
        if (enableFlexibleRedirect) {
            if (!isUrlSafe(url)) {
                Cat.logEvent("RedirectForbidden", url);
                log.warn("Redirect blocked for unsafe URL: {}", url);
                RedirectView redirectView = new RedirectView();
                redirectView.setUrl("https://h5.dianping.com/app/m-static-base-page/err_url.html");
                redirectView.setStatusCode(HttpStatus.FOUND); // 设置302状态码
                return new ModelAndView(redirectView);
            }
        } else {
            // 使用原有的白名单检查
            if (!SecSdk.securityUrlRedirect(url, JSON.parseArray(redirectWhitelist, String.class).toArray(new String[0]))) {
                Cat.logEvent("RedirectForbidden", url);
                RedirectView redirectView = new RedirectView();
                redirectView.setUrl("https://h5.dianping.com/app/m-static-base-page/err_url.html");
                redirectView.setStatusCode(HttpStatus.FOUND); // 设置302状态码
                return new ModelAndView(redirectView);
            }
        }

        // 通过安全检查，执行重定向
        log.info("bizName: BdbRedirect, input: security check, message: {}", url);
        RedirectView redirectView = new RedirectView();
        redirectView.setUrl(url);
        redirectView.setStatusCode(HttpStatus.FOUND); // 设置302状态码
        return new ModelAndView(redirectView);
    }

    /**
     * 自定义URL安全检查逻辑
     * 1. 检查URL格式是否有效
     * 2. 检查URL协议是否安全
     * 3. 检查域名是否在黑名单中
     *
     * @param url 需要检查的URL
     * @return 是否安全
     */
    private boolean isUrlSafe(String url) {
        if (StringUtils.isBlank(url)) {
            return false;
        }

        try {
            // 使用SecSdk的CRLF注入防护
            url = SecSdk.securityRSHeaderInjection(url);

            // 检查URL格式是否有效
            URI uri = new URI(url);
            String scheme = uri.getScheme();

            // 检查是否使用了不安全的协议
            if (scheme != null) {
                List<String> blockedProtocolList = JSON.parseArray(blockedProtocols, String.class);
                for (String protocol : blockedProtocolList) {
                    if (scheme.toLowerCase().startsWith(protocol.toLowerCase())) {
                        log.warn("URL contains unsafe protocol: {}", url);
                        return false;
                    }
                }
            }

            // 检查域名是否在黑名单中
            String host = uri.getHost();
            if (host != null) {
                List<String> blockedDomainList = JSON.parseArray(blockedDomains, String.class);
                for (String domain : blockedDomainList) {
                    if (host.toLowerCase().endsWith(domain.toLowerCase())) {
                        log.warn("URL contains blocked domain: {}", url);
                        return false;
                    }
                }
            }

            return true;
        } catch (URISyntaxException e) {
            log.warn("Invalid URL format: {}", url, e);
            return false;
        }
    }
}