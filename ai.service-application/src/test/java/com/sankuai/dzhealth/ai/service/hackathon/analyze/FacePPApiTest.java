package com.sankuai.dzhealth.ai.service.hackathon.analyze;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import okhttp3.MultipartBody;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.junit.jupiter.api.Test;

import java.io.IOException;
import java.util.Base64;
import java.util.Objects;

/**
 * @Author: guangyujie
 * @Date: 2025/9/11 14:24
 */
public class FacePPApiTest {

    public final static String API_KEY = "WUDWdHLgOhyZOJu-nF2gmfdqBHi6cz55";
    public final static String API_SECRET = "p6Qko8-NDAptyQwW9IYfZ5rtufBK-MyH";

    @Test
    public void test3DFaceApi() throws IOException {
        OkHttpClient client = new OkHttpClient();
        String url = "https://api-cn.faceplusplus.com/facepp/v1/3dface";
        MultipartBody requestBody = new MultipartBody.Builder()
                .setType(MultipartBody.FORM)
                .addFormDataPart("api_key", API_KEY)
                .addFormDataPart("api_secret", API_SECRET)
                .addFormDataPart("image_url_1", "https://img.meituan.net/beautyimg/ec8e850de55e8d6c5bcfbc2ec38dd022142860.jpg")
                .addFormDataPart("image_url_2", "https://img.meituan.net/beautyimg/8aed549c0a05502b5d166991ea3f6970605726.jpg")
                .addFormDataPart("image_url_3", "https://img.meituan.net/beautyimg/43735a28030bb2b300a484e874678277161890.jpg")
                .addFormDataPart("texture", "1")
                .addFormDataPart("mtl", "1")
                .build();
        Request request = new Request.Builder()
                .url(url)
                .post(requestBody)
                .build();
        try (Response response = client.newCall(request).execute()) {
            System.out.println("Status Code: " + response.code());
            String responseBodyStr = Objects.requireNonNull(response.body()).string();
            System.out.println("response: " + responseBodyStr);
        }
    }

    @Test
    public void testFacialFeatures() throws IOException {
        OkHttpClient client = new OkHttpClient();
        String url = "https://api-cn.faceplusplus.com/facepp/v1/facialfeatures";
        MultipartBody requestBody = new MultipartBody.Builder()
                .setType(MultipartBody.FORM)
                .addFormDataPart("api_key", API_KEY)
                .addFormDataPart("api_secret", API_SECRET)
                .addFormDataPart("image_url", "https://p0.meituan.net/shaitu/d6e2cf5494e662203c62bf4be8a44fc675027.jpg")
                .addFormDataPart("return_imagereset", "1")
                .build();
        Request request = new Request.Builder()
                .url(url)
                .post(requestBody)
                .build();
        try (Response response = client.newCall(request).execute()) {
            System.out.println("Status Code: " + response.code());
            String responseBodyStr = Objects.requireNonNull(response.body()).string();
            System.out.println("Response: " + responseBodyStr);
            JSONObject jsonObject = JSONObject.parseObject(responseBodyStr);
            Base64ToFile.saveJPG(jsonObject.getString("image_reset"));
        }
    }

    @Test
    public void testSkinAnalyzePro() throws IOException {
        OkHttpClient client = new OkHttpClient();
        String url = "https://api-cn.faceplusplus.com/facepp/v1/skinanalyze_advanced";
        MultipartBody requestBody = new MultipartBody.Builder()
                .setType(MultipartBody.FORM)
                .addFormDataPart("api_key", API_KEY)
                .addFormDataPart("api_secret", API_SECRET)
                .addFormDataPart("image_url", "https://img.meituan.net/beautyimg/32c36cf0ab6c15197f4abe8c65bd798182293.jpg")
//                .addFormDataPart("left_side_image_url", "https://p0.meituan.net/shaitu/b67903a72e6d92edf971524528544671316354.jpg")
//                .addFormDataPart("right_side_image_url", "https://p0.meituan.net/shaitu/a6d5a432aecb8c208924bbfe4504b08f84516.jpg")
//                .addFormDataPart("return_maps", "red_area")
//                .addFormDataPart("return_marks", "")
                .build();
        Request request = new Request.Builder()
                .url(url)
                .post(requestBody)
                .build();
        try (Response response = client.newCall(request).execute()) {
            System.out.println("Status Code: " + response.code());
            String responseStr = Objects.requireNonNull(response.body()).string();
            System.out.println("Original Response: " + responseStr);
            System.out.println("Response: " + parseSkinResult(responseStr));
        }
    }


    public static String parseSkinResult(String json) {
        try {
            ObjectMapper mapper = new ObjectMapper();
            JsonNode root = mapper.readTree(json);
            JsonNode result = root.path("result");

            if (result.isMissingNode()) {
                return "未检测到皮肤分析结果。";
            }

            StringBuilder sb = new StringBuilder();
            sb.append("皮肤分析结果：\n");

            // 肤色
            int skinColor = result.path("skin_color").path("value").asInt();
            String skinColorDesc = "未知";
            if (skinColor == 0) {
                skinColorDesc = "偏白";
            } else if (skinColor == 1) {
                skinColorDesc = "自然";
            } else if (skinColor == 2) {
                skinColorDesc = "偏黑";
            }
            sb.append("肤色：").append(skinColorDesc).append("\n");

            // 肤龄
            int skinAge = result.path("skin_age").path("value").asInt();
            sb.append("肤龄：大约 ").append(skinAge).append(" 岁\n");

            // 上下眼睑
            int leftEyelids = result.path("left_eyelids").path("value").asInt();
            int rightEyelids = result.path("right_eyelids").path("value").asInt();
            if (leftEyelids == 1 || rightEyelids == 1) {
                sb.append("眼睑：可能存在下垂\n");
            } else {
                sb.append("眼睑：正常\n");
            }

            // 眼袋
            int eyePouch = result.path("eye_pouch").path("value").asInt();
            sb.append("眼袋：").append(eyePouch == 1 ? "有眼袋" : "无明显眼袋").append("\n");

            // 黑眼圈
            int darkCircle = result.path("dark_circle").path("value").asInt();
            String darkCircleDesc = "未知";
            switch (darkCircle) {
                case 0:
                    darkCircleDesc = "无明显黑眼圈";
                    break;
                case 1:
                    darkCircleDesc = "轻度黑眼圈";
                    break;
                case 2:
                    darkCircleDesc = "中度黑眼圈";
                    break;
                case 3:
                    darkCircleDesc = "重度黑眼圈";
                    break;
                default:
                    break;
            }
            sb.append("黑眼圈：").append(darkCircleDesc).append("\n");

            // 法令纹
            int nasolabial = result.path("nasolabial_fold").path("value").asInt();
            sb.append("法令纹：").append(nasolabial == 1 ? "存在法令纹" : "无明显法令纹").append("\n");

            // 额头皱纹
            int foreheadWrinkle = result.path("forehead_wrinkle").path("value").asInt();
            sb.append("额头皱纹：").append(foreheadWrinkle == 1 ? "有皱纹" : "光滑").append("\n");

            // 鱼尾纹
            int crowsFeet = result.path("crows_feet").path("value").asInt();
            sb.append("鱼尾纹：").append(crowsFeet == 1 ? "存在" : "无明显").append("\n");

            // 黑头
            int blackhead = result.path("blackhead").path("value").asInt();
            sb.append("黑头：").append(blackhead == 1 ? "有黑头" : "无明显黑头").append("\n");

            // 毛孔情况（额头）
            int poresForehead = result.path("pores_forehead").path("value").asInt();
            sb.append("额头毛孔：").append(poresForehead == 1 ? "毛孔明显" : "毛孔正常").append("\n");

            // 色斑
            JsonNode spots = result.path("skin_spot").path("rectangle");
            if (spots.isArray() && spots.size() > 0) {
                sb.append("色斑：检测到 ").append(spots.size()).append(" 处\n");
            } else {
                sb.append("色斑：未检测到\n");
            }

            // ITA 肤色分级
            int skintone = result.path("skintone_ita").path("skintone").asInt();
            sb.append("肤色分级(ITA)：等级 ").append(skintone).append("\n");

            return sb.toString();

        } catch (Exception e) {
            return "解析皮肤数据失败：" + e.getMessage();
        }
    }

    @Test
    public void testBeautify() throws IOException {
        OkHttpClient client = new OkHttpClient();
        String url = "https://api-cn.faceplusplus.com/facepp/v2/beautify";
        MultipartBody requestBody = new MultipartBody.Builder()
                .setType(MultipartBody.FORM)
                .addFormDataPart("api_key", API_KEY)
                .addFormDataPart("api_secret", API_SECRET)
                .addFormDataPart("image_url", "https://img.meituan.net/beautyimg/047bd6c4456d85e82efc0d387d51c5b874007.jpg")
                .addFormDataPart("whitening", "0")//美白程度，取值范围[0,100]
                .addFormDataPart("smoothing", "0")//磨皮程度，取值范围 [0,100]
                .addFormDataPart("thinface", "100")//瘦脸程度，取值范围 [0,100]
                .addFormDataPart("shrink_face", "0")//小脸程度，取值范围 [0,100]
                .addFormDataPart("enlarge_eye", "0")//大眼程度，取值范围 [0,100]
                .addFormDataPart("remove_eyebrow", "0")//去眉毛程度，取值范围 [0,100]
                .addFormDataPart("filter_type", "")//滤镜名称，滤镜名称列表见下方，默认无滤镜效果
                .build();
        Request request = new Request.Builder()
                .url(url)
                .post(requestBody)
                .build();
        try (Response response = client.newCall(request).execute()) {
            System.out.println("Status Code: " + response.code());
            String responseBodyStr = Objects.requireNonNull(response.body()).string();
            System.out.println("Response: " + responseBodyStr);
            JSONObject jsonObject = JSONObject.parseObject(responseBodyStr);
            Base64ToFile.saveJPG(jsonObject.getString("result"));
            byte[] imageBytes = Base64.getDecoder().decode(jsonObject.getString("result"));
//            String originalLink = ImageUtils.postHttpImage(imageBytes, "test.jpg").getOriginalLink();
//            System.out.println("OriginalLink: " + originalLink);
        }
    }

}
