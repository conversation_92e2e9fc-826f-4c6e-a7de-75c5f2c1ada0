package com.sankuai.dzhealth.ai.service.hackathon.analyze;

import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.util.Base64;

public class Base64ToFile {

    public static void saveJPG(String base64String) {
        saveFile(base64String, "output.jpg");
    }

    public static void saveFile(String base64String, String fileName) {
        try {
            // Base64 解码
            byte[] imageBytes = Base64.getDecoder().decode(base64String);

            // 写入文件
            try (OutputStream out = new FileOutputStream(fileName)) {
                out.write(imageBytes);
            }

            System.out.println("文件已保存到: " + fileName);
        } catch (IllegalArgumentException e) {
            System.err.println("Base64 字符串无效！");
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

}
