<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.sankuai.dzhealth</groupId>
        <artifactId>ai.service</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>ai.service-application</artifactId>
    <version>${revision}</version>
    <packaging>jar</packaging>
    <name>ai.service-application</name>

    <dependencies>
        <!-- Project module -->
        <dependency>
            <groupId>com.sankuai.security</groupId>
            <artifactId>sec-sdk</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.dzhealth</groupId>
            <artifactId>ai.service-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.dzhealth</groupId>
            <artifactId>ai.service-domain</artifactId>
        </dependency>
    </dependencies>
</project>
