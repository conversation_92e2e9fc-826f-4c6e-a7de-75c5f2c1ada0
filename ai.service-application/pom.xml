<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.sankuai.dzhealth</groupId>
        <artifactId>ai.service</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>ai.service-application</artifactId>
    <version>${revision}</version>
    <packaging>jar</packaging>
    <name>ai.service-application</name>

    <dependencies>
        <!-- Project module -->
        <dependency>
            <groupId>com.sankuai.security</groupId>
            <artifactId>sec-sdk</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.dzhealth</groupId>
            <artifactId>ai.service-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.dzhealth</groupId>
            <artifactId>ai.service-domain</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.image</groupId>
            <artifactId>client</artifactId>
            <version>1.5.0</version>
        </dependency>
        <dependency>
            <groupId>com.amazonaws</groupId>
            <artifactId>mss-java-sdk-s3</artifactId>
            <version>1.10.0</version>
        </dependency>
        <dependency>
            <groupId>com.meituan.mdp.boot</groupId>
            <artifactId>mdp-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp</artifactId>
            <version>4.12.0</version>
        </dependency>
    </dependencies>
</project>
