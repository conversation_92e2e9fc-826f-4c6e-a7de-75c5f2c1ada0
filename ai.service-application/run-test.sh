#!/bin/bash

# AnalyzeService单测运行脚本
# Author: guangyujie
# Date: 2025/1/22

echo "================== AnalyzeService 单测开始 =================="

# 设置环境变量
export MAVEN_OPTS="-Xmx2g -XX:MaxPermSize=256m"
export JAVA_HOME=${JAVA_HOME:-/usr/lib/jvm/java-17-openjdk}

# 检查Java版本
echo "检查Java版本..."
java -version

# 切换到项目根目录
cd "$(dirname "$0")/.." || exit 1

echo "当前目录: $(pwd)"

# 编译项目
echo "编译项目..."
mvn clean compile -DskipTests=true

if [ $? -ne 0 ]; then
    echo "❌ 项目编译失败"
    exit 1
fi

echo "✅ 项目编译成功"

# 运行单测
echo "运行 AnalyzeService 单测..."
mvn test -Dtest=AnalyzeServiceTest -pl ai.service-application

if [ $? -eq 0 ]; then
    echo "✅ 单测执行成功！"
else
    echo "❌ 单测执行失败！"
    exit 1
fi

echo "================== AnalyzeService 单测结束 =================="

