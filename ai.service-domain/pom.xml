<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.sankuai.dzhealth</groupId>
        <artifactId>ai.service</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>ai.service-domain</artifactId>
    <version>${revision}</version>
    <packaging>jar</packaging>
    <name>ai.service-domain</name>

    <dependencies>
        <!-- Project module -->
        <dependency>
            <groupId>com.sankuai.horus</groupId>
            <artifactId>edfu-server-sdk</artifactId>
            <version>1.0.34-RELEASE</version>
        </dependency>
        <dependency>
            <groupId>com.meituan.mdp.boot</groupId>
            <artifactId>mdp-boot-starter-mafka</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.dzhealth.medical</groupId>
            <artifactId>dzhealth-medical-client</artifactId>
            <version>0.0.65</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.map.maf</groupId>
            <artifactId>openplatform-dependency</artifactId>
            <version>1.2.49</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.dzhealth</groupId>
            <artifactId>ai.service-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.dzhealth</groupId>
            <artifactId>ai.service-infrastructure</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.beautycontent</groupId>
            <artifactId>beauty-tag-api</artifactId>
            <version>0.0.58</version>
            <exclusions>
                <exclusion>
                    <groupId>*</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.meituan.beauty</groupId>
            <artifactId>beauty-arch-fundamental-light</artifactId>
            <version>3.0.10</version>
            <scope>compile</scope>
            <exclusions>
                <exclusion>
                    <groupId>dom4j</groupId>
                    <artifactId>dom4j</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.sankuai</groupId>
            <artifactId>call_sdk</artifactId>
            <version>3.2.10</version>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.logging.log4j</groupId>
                    <artifactId>log4j-slf4j-impl</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>javax.validation</groupId>
                    <artifactId>validation-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.sankuai</groupId>
            <artifactId>beautycontent.store.api</artifactId>
            <version>0.0.2.7</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.wpt.user.retrieve</groupId>
            <artifactId>retrieve-api</artifactId>
            <version>1.2.33</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.dzim</groupId>
            <artifactId>pilot-api</artifactId>
            <version>0.0.26</version>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>ugc-review-api</artifactId>
            <version>3.4.33</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.clr</groupId>
            <artifactId>clr-content-process-thrift</artifactId>
            <version>0.0.88</version>
            <exclusions>
                <exclusion>
                    <groupId>com.meituan.mdp.boot</groupId>
                    <artifactId>mdp-boot-starter-crypto</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.sankuai.leads</groupId>
            <artifactId>leads-process-thrift</artifactId>
            <version>0.3.6</version>
            <exclusions>
                <exclusion>
                    <groupId>com.meituan.mdp.boot</groupId>
                    <artifactId>mdp-boot-starter-crypto</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.sankuai.leads</groupId>
            <artifactId>leads-common</artifactId>
            <version>0.0.97</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.leads</groupId>
            <artifactId>leads-process-gateway-thrift</artifactId>
            <version>0.0.37</version>
        </dependency>

        <!-- Excel处理依赖 -->
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi</artifactId>
            <version>5.2.3</version>
        </dependency>
        <!-- 营业时间处理依赖 -->
        <dependency>
            <groupId>com.dianping.poi</groupId>
            <artifactId>poi-bizhour-api</artifactId>
            <version>1.2.4</version>
        </dependency>

        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml</artifactId>
            <version>5.2.3</version>
        </dependency>


        <dependency>
            <groupId>com.sankuai.medicalcosmetology.mainpath</groupId>
            <artifactId>listing-api</artifactId>
            <version>1.0.1</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-test</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-test</artifactId>
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-api</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-junit-jupiter</artifactId>
        </dependency>
    </dependencies>


</project>
