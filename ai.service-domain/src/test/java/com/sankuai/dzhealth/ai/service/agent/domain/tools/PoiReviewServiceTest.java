package com.sankuai.dzhealth.ai.service.agent.domain.tools;

import com.dianping.ugc.review.remote.enums.ReviewPlatFormEnum;
import com.dianping.ugc.review.remote.mt.MTReviewQueryServiceV2;
import com.sankuai.dzim.pilot.api.VectorSearchService;
import com.sankuai.dzim.pilot.api.data.ReviewDTO;
import com.sankuai.dzim.pilot.api.data.ReviewVectorSearchRequest;
import com.sankuai.dzim.pilot.api.data.VectorSearchResponse;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.*;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;




/**
 * <AUTHOR> youtianyu
 * @version : 0.1
 * @since : 2025/8/11
 */
@RunWith(MockitoJUnitRunner.class)
public class PoiReviewServiceTest {
    
    @InjectMocks
    private PoiReviewService poiReviewService;
    
    @Mock
    private VectorSearchService vectorSearchService;

    @Mock
    private MTReviewQueryServiceV2 mtReviewQueryServiceV2;


    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);

        // Mock Cat.newTransaction
    }

    /**
     * 测试正常场景：成功获取评价数据并返回映射
     */
    @Test
    public void testQueryReviewsSuccessCase() throws Throwable {
        // arrange
        Long poiId = 123456L;
        ReviewVectorSearchRequest request = new ReviewVectorSearchRequest();

        // 准备DP平台评价数据
        ReviewDTO dpReview1 = new ReviewDTO();
        dpReview1.setReviewId(1001L);
        dpReview1.setPlatform(0); // DP平台=0
        dpReview1.setContent("DP平台评价1");

        ReviewDTO dpReview2 = new ReviewDTO();
        dpReview2.setReviewId(1002L);
        dpReview2.setPlatform(0); // DP平台=0
        dpReview2.setContent("DP平台评价2");

        // 准备MT平台评价数据
        ReviewDTO mtReview1 = new ReviewDTO();
        mtReview1.setReviewId(2001L);
        mtReview1.setPlatform(1); // MT平台=1
        mtReview1.setContent("MT平台评价1");

        List<ReviewDTO> reviewList = Arrays.asList(dpReview1, dpReview2, mtReview1);

        VectorSearchResponse<ReviewDTO> response = new VectorSearchResponse<>();
        response.setDataList(reviewList);

        // DP平台评价映射
        Map<Long, Long> dpReviewMapping = new HashMap<>();
        dpReviewMapping.put(1001L, 5001L);
        dpReviewMapping.put(1002L, 5002L);

        // MT平台评价映射
        Map<Long, Long> mtReviewMapping = new HashMap<>();
        mtReviewMapping.put(2001L, 6001L);

        // Mock方法调用
        when(vectorSearchService.searchReview(any())).thenReturn(response);
        when(mtReviewQueryServiceV2.getReviewMapping(anyList(), eq(ReviewPlatFormEnum.DP.value))).thenReturn(dpReviewMapping);
        when(mtReviewQueryServiceV2.getReviewMapping(anyList(), eq(ReviewPlatFormEnum.MT.value))).thenReturn(mtReviewMapping);

        // act
        Map<Long, String> result = (Map<Long, String>) ReflectionTestUtils.invokeMethod(
                poiReviewService, "queryReviews", poiId, request);

        // assert
        assertNotNull(result);
        assertEquals(3, result.size());
        assertEquals("DP平台评价1", result.get(5001L));
        assertEquals("DP平台评价2", result.get(5002L));
        assertEquals("MT平台评价1", result.get(6001L));

        verify(vectorSearchService).searchReview(any());
        verify(mtReviewQueryServiceV2).getReviewMapping(anyList(), eq(ReviewPlatFormEnum.DP.value));
        verify(mtReviewQueryServiceV2).getReviewMapping(anyList(), eq(ReviewPlatFormEnum.MT.value));
    }

    /**
     * 测试边界场景1：vectorSearchService.searchReview返回null
     */
    @Test
    public void testQueryReviewsWhenSearchResponseIsNull() throws Throwable {
        // arrange
        Long poiId = 123456L;
        ReviewVectorSearchRequest request = new ReviewVectorSearchRequest();

        // Mock方法调用
        when(vectorSearchService.searchReview(any())).thenReturn(null);

        // act
        Map<Long, String> result = (Map<Long, String>) ReflectionTestUtils.invokeMethod(
                poiReviewService, "queryReviews", poiId, request);

        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());

        verify(vectorSearchService).searchReview(any());
        verify(mtReviewQueryServiceV2, never()).getReviewMapping(anyList(), anyInt());
    }

    /**
     * 测试边界场景2：vectorSearchService.searchReview返回的dataList为空
     */
    @Test
    public void testQueryReviewsWhenDataListIsEmpty() throws Throwable {
        // arrange
        Long poiId = 123456L;
        ReviewVectorSearchRequest request = new ReviewVectorSearchRequest();

        VectorSearchResponse<ReviewDTO> response = new VectorSearchResponse<>();
        response.setDataList(Collections.emptyList());

        // Mock方法调用
        when(vectorSearchService.searchReview(any())).thenReturn(response);

        // act
        Map<Long, String> result = (Map<Long, String>) ReflectionTestUtils.invokeMethod(
                poiReviewService, "queryReviews", poiId, request);

        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());

        verify(vectorSearchService).searchReview(any());
        verify(mtReviewQueryServiceV2, never()).getReviewMapping(anyList(), anyInt());
    }

    /**
     * 测试边界场景3：评价列表中没有DP或MT平台的评价
     */
    @Test
    public void testQueryReviewsWhenNoPlatformReviews() throws Throwable {
        // arrange
        Long poiId = 123456L;
        ReviewVectorSearchRequest request = new ReviewVectorSearchRequest();

        // 准备其他平台评价数据
        ReviewDTO otherReview = new ReviewDTO();
        otherReview.setReviewId(3001L);
        otherReview.setPlatform(999); // 非DP或MT平台
        otherReview.setContent("其他平台评价");

        List<ReviewDTO> reviewList = Collections.singletonList(otherReview);

        VectorSearchResponse<ReviewDTO> response = new VectorSearchResponse<>();
        response.setDataList(reviewList);

        // Mock方法调用
        when(vectorSearchService.searchReview(any())).thenReturn(response);

        // act
        Map<Long, String> result = (Map<Long, String>) ReflectionTestUtils.invokeMethod(
                poiReviewService, "queryReviews", poiId, request);

        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());

        verify(vectorSearchService).searchReview(any());
        verify(mtReviewQueryServiceV2, never()).getReviewMapping(anyList(), anyInt());
    }

    /**
     * 测试边界场景4：mtReviewQueryServiceV2.getReviewMapping返回空映射
     */
    @Test
    public void testQueryReviewsWhenReviewMappingIsEmpty() throws Throwable {
        // arrange
        Long poiId = 123456L;
        ReviewVectorSearchRequest request = new ReviewVectorSearchRequest();

        // 准备DP平台评价数据
        ReviewDTO dpReview = new ReviewDTO();
        dpReview.setReviewId(1001L);
        dpReview.setPlatform(0); // DP平台=0
        dpReview.setContent("DP平台评价");

        List<ReviewDTO> reviewList = Collections.singletonList(dpReview);

        VectorSearchResponse<ReviewDTO> response = new VectorSearchResponse<>();
        response.setDataList(reviewList);

        // Mock方法调用
        when(vectorSearchService.searchReview(any())).thenReturn(response);
        when(mtReviewQueryServiceV2.getReviewMapping(anyList(), eq(ReviewPlatFormEnum.DP.value)))
                .thenReturn(Collections.emptyMap());

        // act
        Map<Long, String> result = (Map<Long, String>) ReflectionTestUtils.invokeMethod(
                poiReviewService, "queryReviews", poiId, request);

        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());

        verify(vectorSearchService).searchReview(any());
        verify(mtReviewQueryServiceV2).getReviewMapping(anyList(), eq(ReviewPlatFormEnum.DP.value));
    }

    /**
     * 测试异常场景1：vectorSearchService.searchReview抛出异常
     */
    @Test
    public void testQueryReviewsWhenSearchReviewThrowsException() throws Throwable {
        // arrange
        Long poiId = 123456L;
        ReviewVectorSearchRequest request = new ReviewVectorSearchRequest();

        // Mock方法调用
        when(vectorSearchService.searchReview(any())).thenThrow(new RuntimeException("搜索服务异常"));

        // act
        Map<Long, String> result = (Map<Long, String>) ReflectionTestUtils.invokeMethod(
                poiReviewService, "queryReviews", poiId, request);

        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());

        verify(vectorSearchService).searchReview(any());
        verify(mtReviewQueryServiceV2, never()).getReviewMapping(anyList(), anyInt());
    }

    /**
     * 测试异常场景2：mtReviewQueryServiceV2.getReviewMapping抛出异常
     */
    @Test
    public void testQueryReviewsWhenGetReviewMappingThrowsException() throws Throwable {
        // arrange
        Long poiId = 123456L;
        ReviewVectorSearchRequest request = new ReviewVectorSearchRequest();

        // 准备DP平台评价数据
        ReviewDTO dpReview = new ReviewDTO();
        dpReview.setReviewId(1001L);
        dpReview.setPlatform(0); // DP平台=0
        dpReview.setContent("DP平台评价");

        // 准备MT平台评价数据
        ReviewDTO mtReview = new ReviewDTO();
        mtReview.setReviewId(2001L);
        mtReview.setPlatform(1); // MT平台=1
        mtReview.setContent("MT平台评价");

        List<ReviewDTO> reviewList = Arrays.asList(dpReview, mtReview);

        VectorSearchResponse<ReviewDTO> response = new VectorSearchResponse<>();
        response.setDataList(reviewList);

        // DP平台评价映射
        Map<Long, Long> dpReviewMapping = new HashMap<>();
        dpReviewMapping.put(1001L, 5001L);

        // Mock方法调用
        when(vectorSearchService.searchReview(any())).thenReturn(response);
        when(mtReviewQueryServiceV2.getReviewMapping(anyList(), eq(ReviewPlatFormEnum.DP.value)))
                .thenReturn(dpReviewMapping);
        when(mtReviewQueryServiceV2.getReviewMapping(anyList(), eq(ReviewPlatFormEnum.MT.value)))
                .thenThrow(new RuntimeException("获取评价映射异常"));

        // act
        Map<Long, String> result = (Map<Long, String>) ReflectionTestUtils.invokeMethod(
                poiReviewService, "queryReviews", poiId, request);

        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("DP平台评价", result.get(5001L));

        verify(vectorSearchService).searchReview(any());
        verify(mtReviewQueryServiceV2).getReviewMapping(anyList(), eq(ReviewPlatFormEnum.DP.value));
        verify(mtReviewQueryServiceV2).getReviewMapping(anyList(), eq(ReviewPlatFormEnum.MT.value));
    }

    /**
     * 测试换行符替换功能：验证评价内容中的\n被替换为空格
     */
    @Test
    public void testQueryReviewsWithNewlineReplacement() throws Throwable {
        // arrange
        Long poiId = 123456L;
        ReviewVectorSearchRequest request = new ReviewVectorSearchRequest();

        // 准备包含换行符的评价数据
        ReviewDTO dpReview = new ReviewDTO();
        dpReview.setReviewId(1001L);
        dpReview.setPlatform(0); // DP平台=0
        dpReview.setContent("这是第一行\\\\n这是第二行\\\\n这是第三行");

        ReviewDTO mtReview = new ReviewDTO();
        mtReview.setReviewId(2001L);
        mtReview.setPlatform(1); // MT平台=1
        mtReview.setContent("MT评价第一行\\\\n第二行内容\\\\n第三行结束");

        List<ReviewDTO> reviewList = Arrays.asList(dpReview, mtReview);

        VectorSearchResponse<ReviewDTO> response = new VectorSearchResponse<>();
        response.setDataList(reviewList);

        // DP平台评价映射
        Map<Long, Long> dpReviewMapping = new HashMap<>();
        dpReviewMapping.put(1001L, 5001L);

        // MT平台评价映射
        Map<Long, Long> mtReviewMapping = new HashMap<>();
        mtReviewMapping.put(2001L, 6001L);

        // Mock方法调用
        when(vectorSearchService.searchReview(any())).thenReturn(response);
        when(mtReviewQueryServiceV2.getReviewMapping(anyList(), eq(ReviewPlatFormEnum.DP.value))).thenReturn(dpReviewMapping);
        when(mtReviewQueryServiceV2.getReviewMapping(anyList(), eq(ReviewPlatFormEnum.MT.value))).thenReturn(mtReviewMapping);

        // act
        Map<Long, String> result = (Map<Long, String>) ReflectionTestUtils.invokeMethod(
                poiReviewService, "queryReviews", poiId, request);

        // assert
        assertNotNull(result);
        assertEquals(2, result.size());

        // 验证换行符被替换为空格
        assertEquals("这是第一行 这是第二行 这是第三行", result.get(5001L));
        assertEquals("MT评价第一行 第二行内容 第三行结束", result.get(6001L));

        // 确保结果中不包含换行符
        assertFalse("DP评价内容不应包含\\\\n", result.get(5001L).contains("\\\\n"));
        assertFalse("MT评价内容不应包含\\\\n", result.get(6001L).contains("\\\\n"));

        verify(vectorSearchService).searchReview(any());
        verify(mtReviewQueryServiceV2).getReviewMapping(anyList(), eq(ReviewPlatFormEnum.DP.value));
        verify(mtReviewQueryServiceV2).getReviewMapping(anyList(), eq(ReviewPlatFormEnum.MT.value));
    }
}