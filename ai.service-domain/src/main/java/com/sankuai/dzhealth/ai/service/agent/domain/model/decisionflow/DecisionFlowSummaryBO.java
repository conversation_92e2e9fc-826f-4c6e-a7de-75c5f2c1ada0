package com.sankuai.dzhealth.ai.service.agent.domain.model.decisionflow;

import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * 决策流概览业务对象
 */
@Data
@Builder
public class DecisionFlowSummaryBO {

    /** 业务场景 */
    private String bizScene;

    /** 节点总数 */
    private Integer totalNodes;

    /** 路径总数 */
    private Integer totalEdges;

    /** 根节点列表 */
    private List<DecisionNodeBO> rootNodes;

    /** 叶子节点数量 */
    private Integer leafNodeCount;

    /** 决策流状态 */
    private String flowStatus;

    /** 最后更新时间 */
    private Long lastUpdateTime;
}

