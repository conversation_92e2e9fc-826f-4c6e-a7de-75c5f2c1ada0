package com.sankuai.dzhealth.ai.service.agent.domain.tools;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.dianping.haima.entity.haima.HaimaContent;
import com.sankuai.dzhealth.ai.service.infrastructure.acl.FridayWebSearchAcl;
import com.sankuai.dzhealth.ai.service.infrastructure.acl.HaimaAcl;
import com.sankuai.dzhealth.ai.service.infrastructure.model.FridaySearchResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.tool.annotation.Tool;
import org.springframework.ai.tool.annotation.ToolParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

/**
 * 医美百科网络搜索工具
 * 用于在序列思考过程中进行网络搜索
 */
@Slf4j
@Component
public class MedicalBeautyWebSearchTool {
    @Autowired
    private FridayWebSearchAcl fridayWebSearchAcl;
    @Autowired
    private HaimaAcl haimaAcl;
    /**
     * 网络搜索工具方法
     * 通过Spring AI的Tool注解注册为工具方法
     *
     * @param query 搜索查询
     * @param topK 返回结果数量
     * @return 格式化的搜索结果
     */
    @Tool(description = "根据用户查询进行网络搜索，获取最新的相关信息。" +
            "适用于需要查找最新资讯、医疗知识、研究进展等外部信息的场景。" +
            "搜索结果将包含标题、摘要和URL，便于进一步分析和引用。")
    public String webSearch(
            @ToolParam(description = "搜索关键词，必填。例如：'高血压最新治疗进展'") String query,
            @ToolParam(description = "返回的搜索结果数量，默认为5", required = false) Integer topK
    ) {
        log.info("MedicalBeautyWebSearchTool 执行网络搜索: 查询={}, 结果数量={}", query, topK);
        // 默认值处理
        int resultCount = topK != null && topK > 0 ? topK : 5;
        List<String> sites = getSites();
        try {
            // 调用Friday搜索服务
            List<FridaySearchResult> results = fridayWebSearchAcl.search(
                query,
                sites,
                resultCount
            );
            // 格式化搜索结果
            if (results == null || results.isEmpty()) {
                return "未找到与\"" + query + "\"相关的搜索结果。";
            }
            StringBuilder formattedResults = new StringBuilder();
            formattedResults.append("以下是关于\"").append(query).append("\"的搜索结果：\n\n");
            for (int i = 0; i < results.size(); i++) {
                FridaySearchResult result = results.get(i);
                formattedResults.append(i + 1).append(". ");
                formattedResults.append("标题: ").append(result.getName()).append("\n");
                formattedResults.append("   摘要: ").append(result.getSnippet()).append("\n");
                formattedResults.append("   URL: ").append(result.getUrl()).append("\n\n");
            }
            log.info("MedicalBeautyWebSearchTool 搜索成功: {}，query: {}", formattedResults.toString(), query);
            return formattedResults.toString();
        } catch (Exception e) {
            log.error("MedicalBeautyWebSearchTool 搜索失败: {}", e.getMessage(), e);
            return "搜索过程中发生错误: " + e.getMessage();
        }
    }

    private List<String> getSites() {
        List<HaimaContent> haimaContents = haimaAcl.getContent("medical_ai_resource_config", null);
        Optional<List<String>> sites = haimaContents.stream()
                .filter(content -> "web_search_sites".equals(content.getContentString("resource_key")))
                .map(content -> JSON.parseObject(content.getContentString("resource_config"), new TypeReference<List<String>>() {
                }))
                .findFirst();

        return sites.orElse(Collections.emptyList());
    }
}