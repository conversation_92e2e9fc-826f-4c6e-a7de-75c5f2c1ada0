package com.sankuai.dzhealth.ai.service.agent.domain.model.product;

import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 商品聚合根 - Domain Model
 * 防腐层：屏蔽查询中心的复杂DTO结构，提供领域层统一的商品模型
 */
@Data
@Builder
public class Product {

    /**
     * 商品基本信息
     */
    private ProductBasic basic;

    /**
     * 商品类目信息
     */
    private ProductCategory category;

    /**
     * 商品标签列表
     */
    private List<ProductTag> tags;

    /**
     * 商品扩展属性（键值对形式）
     * 主要用于泛商品的扩展字段，如category3、category4等
     */
    private Map<String, String> attributes;

    /**
     * 是否为泛商品体系
     */
    private boolean isBizProduct;
}

