package com.sankuai.dzhealth.ai.service.agent.domain.process.task;

import com.sankuai.dzhealth.ai.service.agent.domain.context.MessageContext;
import com.sankuai.dzhealth.ai.service.agent.domain.context.TaskContext;
import com.sankuai.dzhealth.ai.service.agent.domain.model.TaskProcessResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @author:chenwei
 * @time: 2025/7/13 11:13
 * @version: 0.0.1
 */

@Component
public class TaskProcessor {

    @Autowired
    private List<Task> taskList;


    public TaskProcessResult process(TaskContext taskContext) {
        if (taskContext == null) {
            return null;
        }

        return taskList.stream().filter(task -> task.accept(taskContext.getTaskType()))
                .findFirst()
                .map(task -> task.process(taskContext))
                .orElse(null);
    }

    public void afterProcess(TaskProcessResult result, TaskProcessResult mainResult) {
        if (result == null || result.getTaskContext() == null) {
            return;
        }
        result.setMainProcessResult(mainResult);
        for (Task task : taskList) {
            if (task.accept(result.getTaskContext().getTaskType())) {
                task.after(result);
            }
        }
    }

    public TaskContext getMainTaskContext(MessageContext context) {
        TaskConfig mainTask = context.getAgentTaskConfig().getMainTask();
        return buildTaskContext(context, mainTask);
    }

    public List<TaskContext> getSubTaskContext(MessageContext context) {
        List<TaskConfig> subTasks = context.getAgentTaskConfig().getSubTask();
        return subTasks.stream().map(task -> buildTaskContext(context, task)).collect(Collectors.toList());
    }

    private TaskContext buildTaskContext(MessageContext context, TaskConfig taskConfig) {
        TaskContext taskContext = new TaskContext();
        taskContext.setTaskType(taskConfig.getType());
        taskContext.setTaskConfig(taskConfig);
        taskContext.setMessageContext(context);
        return taskContext;
    }


}
