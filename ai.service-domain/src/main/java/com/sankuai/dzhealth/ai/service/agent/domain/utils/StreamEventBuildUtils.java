package com.sankuai.dzhealth.ai.service.agent.domain.utils;

import com.google.common.collect.Lists;
import com.sankuai.dzhealth.ai.service.dto.stream.StreamEventCardDataDTO;
import com.sankuai.dzhealth.ai.service.dto.stream.StreamEventDTO;
import com.sankuai.dzhealth.ai.service.dto.stream.StreamEventDataDTO;
import com.sankuai.dzhealth.ai.service.enums.StreamEventCardTypeEnum;
import com.sankuai.dzhealth.ai.service.enums.StreamEventDataTypeEnum;
import com.sankuai.dzhealth.ai.service.enums.StreamEventTypeEnum;

import java.util.Map;

/**
 * @author:chenwei
 * @time: 2025/7/6 14:23
 * @version: 0.0.1
 */
public class StreamEventBuildUtils {

    public static StreamEventDTO buildOpenEvent() {
        return StreamEventDTO.builder()
                .type(StreamEventTypeEnum.OPEN.getType())
                .build();
    }

    public static StreamEventDTO buildCloseEvent() {
        return StreamEventDTO.builder()
                .type(StreamEventTypeEnum.CLOSE.getType())
                .build();
    }

    public static StreamEventDTO buildErrorEvent(String event, String content) {
        return StreamEventDTO.builder()
                .type(StreamEventTypeEnum.ERROR.getType())
                .data(StreamEventDataDTO.builder().event(event).content(content).build())
                .build();
    }

    public static StreamEventDTO buildCardEvent(StreamEventCardTypeEnum cardTypeEnum, String cardKey,
                                            Map<String, Object> cardProps, int index) {
        StreamEventDTO eventDTO = new StreamEventDTO();
        eventDTO.setType(StreamEventTypeEnum.MESSAGE.getType());
        eventDTO.setIndex(index);

        StreamEventDataDTO dataDTO = new StreamEventDataDTO();
        dataDTO.setEvent(StreamEventDataTypeEnum.MAIN_TEXT.getType());
        boolean isOutBubble = cardTypeEnum.isOutBubble();

        dataDTO.setContent(isOutBubble ? ":::{" + StreamEventCardTypeEnum.buildCardContent(cardTypeEnum, cardKey) + "}:::"
                : ":::}" + StreamEventCardTypeEnum.buildCardContent(cardTypeEnum, cardKey) + "{:::");

        StreamEventCardDataDTO cardDataDTO = new StreamEventCardDataDTO();
        cardDataDTO.setType(cardTypeEnum.getType());
        cardDataDTO.setKey(cardKey);
        cardDataDTO.setCardProps(cardProps);

        dataDTO.setCardsData(Lists.newArrayList(cardDataDTO));
        eventDTO.setData(dataDTO);

        return eventDTO;
    }


}
