package com.sankuai.dzhealth.ai.service.agent.domain.process.card.data;

import com.sankuai.dzhealth.ai.service.agent.domain.model.SupplyRecommendModel;
import com.sankuai.medicalcosmetology.mainpath.listingapi.dto.FillInfoDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @author:chenwei
 * @time: 2025/7/13 14:45
 * @version: 0.0.1
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ShopProductCardData {

    private FillInfoDTO detail;

    private String reason;

    private String reasonPrefix;

    private CaseCardData report;

    private List<SupplyRecommendModel> filterList;

    private Boolean needMore;

    private List<ReferData> refer;

    private Long count;

    private Integer index;

    private Integer cityId;

    private Double lat;

    private Double lng;

    private Boolean isCurrentLocation;

    public Map<String, Object> toMap() {
        Map<String, Object> map = new HashMap<>();
        map.put("detail", detail);
        map.put("reason", reason);
        map.put("reasonPrefix", reasonPrefix);
        map.put("report", report);
        map.put("filterList", filterList);
        map.put("needMore", needMore);
        map.put("refer", refer);
        map.put("count", count);
        map.put("index", index);
        map.put("cityId", cityId);
        map.put("lat", lat);
        map.put("lng", lng);
        map.put("isCurrentLocation", isCurrentLocation);
        return map;
    }
}
