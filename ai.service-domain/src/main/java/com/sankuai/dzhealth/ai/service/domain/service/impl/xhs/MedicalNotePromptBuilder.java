package com.sankuai.dzhealth.ai.service.domain.service.impl.xhs;

import com.google.common.collect.ImmutableMap;
import com.sankuai.dzhealth.ai.service.domain.entity.xhs.Direction;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 医疗笔记提示词构建器 - 负责构建生成医疗笔记的提示词
 * 
 * <AUTHOR>
 */
@Component
public class MedicalNotePromptBuilder {

    private static final Map<String, String> project2Description = new ImmutableMap.Builder<String, String>()
            .put("医美项目推荐", "医美项目推荐内容方向，应围绕用户在不同季节（如春秋季过敏、夏季出油、冬季干燥等）、节日（母亲节、情人节、五一、十一、春节等）、痛点（鼻基底凹陷、法令纹、泪沟、黑眼圈等）场景（加班熬夜等）下的真实需求，结合皮肤/美学问题，借助高性价比套餐和创新组合，提供科学、实用、有吸引力的项目盘点推荐、组合使用方案等，提升用户决策效率和转化率。")
            .put("低价爆品推荐", "针对羊毛党、学生党或医美小白人群，以极具吸引力低价项目为主打，突出\"低门槛体验\"、\"超高性价比\"等卖点，内容中制造快速囤货、购买焦虑和紧迫感，引导用户下单，商品推荐和宣导方向为9.9元脱毛、19.9元小气泡、69元光子嫩肤等等其他低价引流品类。")
            .put("医美项目科普", "讲解各医美项目（如激光美容、注射填充、射频紧肤等）的功效、作用、原理、成分、适合人群等科普类问题，例如一篇带你读懂光子嫩肤、什么是光子嫩肤？关于光子嫩肤你必须知道的几个问题、光子嫩肤的几大误区、光子嫩肤的原理和功效看这篇就够了，列举项目如：光子嫩肤（黄金超光子、黑金超光子）、热玛吉、水光针、黄金微针、玻尿酸/嗨体、刷酸、脱毛（冰点脱毛、激光脱毛）。")
            .put("项目组合使用方案", "医美项目组合使用，是指将两种及以上不同功能或作用机制的医美治疗项目，按照科学、安全、个性化原则进行搭配和联合应用。通过项目之间的协同效应，实现更优的美肤、美形或抗衰效果，满足求美者多层次、多方面的皮肤及形态改善需求。医美项目组合使用方式的介绍和科普，如{xxxx}怎么搭配？、{xxx+xxx}cp组合、刷酸+黄金微针搭配使用方式等。")
            .put("科普避雷", "以专业人士或医美老手身份，科普医美知识、误区，避坑某项目/机构/医生，输出干货性内容。")
            .put("变美经验分享", "多为讲述个人变美经历，分享变美 Tips 或项目心得。")
            .put("术后护理注意事项", "针对不同医美项目，给出术后清洁、护肤、饮食、作息等方面的具体建议，比如双眼皮手术后如何正确清洁伤口、如何选择合适的护肤品。介绍术后护理和建议，涵盖伤口处理、饮食调节、运动限制、可能存在的并发症及规避方式等方面，帮助用户恢复得更快更好。例如：热玛吉术后注意事项/打完热玛吉如何护理、光子嫩肤术后注意事项等。")
            .put("品类品牌推荐评测", "医美某个具体品类的品牌评测及推荐、对比，如玻尿酸品牌推荐、水光品牌如何选、同品类不同品牌的价格、优缺点对比等。")
            .put("医美项目对比", "对两个或多个不同医美项目进行优缺点分析、适应人群、效果分析等，帮助用户做出更好选择。例如：玻尿酸VS胶原蛋白的区别，热玛吉VS超声刀以及光子嫩肤、水光针、黄金微针、热玛吉、超声炮等项目介绍对比。")
            .put("打法分享", "展示医生操作实拍或治疗方法分布，针对某个问题的具体打法。痘肌，毛孔大有粉刺痘坑、脸部轮廓不清晰等。")
            .put("明星美学类", "通过明星/网红图片或视频素材，作为噱头，讲解某个审美或打法。")
            .build();

    public String buildNotePrompt(Direction direction, String knowledge, String hotNotes, String noteTemplate, String basePrompt) {
        return basePrompt
                .replace("{{{project}}}", project2Description.getOrDefault(direction.getProject(), direction.getProject()))
                .replace("{{{product}}}", direction.getProduct())
                .replace("{{{scene}}}", direction.getScene())
                .replace("{{{person}}}", direction.getPerson())
                .replace("{{{hotNotes}}}", hotNotes)
                .replace("{{{template}}}", noteTemplate)
                .replace("{{{knowledge}}}", knowledge);
    }
} 