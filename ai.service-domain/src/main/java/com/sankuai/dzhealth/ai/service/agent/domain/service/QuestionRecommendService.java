package com.sankuai.dzhealth.ai.service.agent.domain.service;

import com.sankuai.dzhealth.ai.service.agent.domain.enums.CategoryEnum;
import com.sankuai.dzhealth.ai.service.agent.domain.utils.HaimaAIUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.List;


@Slf4j
@Service
public class QuestionRecommendService {

    @Autowired
    @Qualifier("recommendQuestionChatClient")
    private ChatClient recommendQuestionChatClient;

    @Autowired
    private HaimaAIUtils haimaAIUtils;

    private static final String QUESTION_RECOMMEND_BY_PRODUCT = "question_recommend_by_product";
    private static final String QUESTION_RECOMMEND_BY_RECORD = "question_recommend_by_record";
    private static final String CHAT_RECORD_PlaceHolder = "${chat_record}";
    private static final String DOMAIN_PlaceHolder = "${domain}";
    private static final String SEARCH_RECORD_PlaceHolder = "${search_record}";
    private static final String PRODUCT_NAME_PlaceHolder = "${product_name}";

    public String recommendQuestionByProduct(String productName, CategoryEnum categoryEnum) {
        String systemPrompt = haimaAIUtils.getAIConfig(QUESTION_RECOMMEND_BY_PRODUCT).getSystemPrompt();
        if (categoryEnum == CategoryEnum.MOUTH_CONSULT) {
            systemPrompt = systemPrompt.replace(DOMAIN_PlaceHolder, "医学口腔");
        } else {
            systemPrompt = systemPrompt.replace(DOMAIN_PlaceHolder, "医学美容");
        }
        systemPrompt = systemPrompt.replace(PRODUCT_NAME_PlaceHolder, productName);
        return recommendQuestionChatClient.prompt()
                .system(systemPrompt)
                .call().content();
    }


    public String recommendQuestionByRecord(List<String> searchHistory, List<String> messageHistory, CategoryEnum categoryEnum) {
        String systemPrompt = haimaAIUtils.getAIConfig(QUESTION_RECOMMEND_BY_RECORD).getSystemPrompt();
        if (categoryEnum == CategoryEnum.MOUTH_CONSULT) {
            systemPrompt = systemPrompt.replace(DOMAIN_PlaceHolder, "医学口腔");
        } else {
            systemPrompt = systemPrompt.replace(DOMAIN_PlaceHolder, "医学美容");
        }
        systemPrompt = systemPrompt.replace(CHAT_RECORD_PlaceHolder, String.join("\n", messageHistory));
        systemPrompt = systemPrompt.replace(SEARCH_RECORD_PlaceHolder, String.join("\n", searchHistory));
        return recommendQuestionChatClient.prompt()
                .system(systemPrompt)
                .call().content();
    }
}
