package com.sankuai.dzhealth.ai.service.agent.domain.process.card;

import com.sankuai.beautycontent.experience.enums.FilterType;
import com.sankuai.dzhealth.ai.service.agent.domain.context.MessageContext;
import com.sankuai.dzhealth.ai.service.agent.domain.model.experiencereport.AiCaseInfo;
import com.sankuai.dzhealth.ai.service.agent.domain.model.experiencereport.AiExperienceReports;
import com.sankuai.dzhealth.ai.service.agent.domain.model.experiencereport.ExperienceReportRequestModel;
import com.sankuai.dzhealth.ai.service.agent.domain.process.card.data.CaseCardData;
import com.sankuai.dzhealth.ai.service.agent.domain.service.experiencereport.ExperienceReportDomainService;
import com.sankuai.dzhealth.ai.service.domain.utils.JsonUtils;
import com.sankuai.dzhealth.ai.service.dto.stream.StreamEventCardDataDTO;
import com.sankuai.dzhealth.ai.service.enums.StreamEventCardTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @author:chenwei
 * @time: 2025/7/23 17:26
 * @version: 0.0.1
 */

@Service
@Slf4j
public class MedicalCaseCardBuilder implements CardBuilder{

    @Autowired
    private ExperienceReportDomainService experienceReportDomainService;

    @Override
    public boolean accept(String cardType) {
        return StreamEventCardTypeEnum.CASE_CARD.getType().equals(cardType);
    }

    @Override
    public void padding(StreamEventCardDataDTO streamEventCardDataDTO, MessageContext messageContext) {

        String key = streamEventCardDataDTO.getKey();

        log.info("<MedicalCaseCardBuilder>key={}", key);

        // 兼容两种格式：1) 方圆脸:11,13;方脸:10,13 2) 11,13
        String processedKey;
        if (key.contains(";") && key.contains(":")) {
            // 格式1：先按分号分割，再按冒号分割取后半部分，用逗号连接
            processedKey = Arrays.stream(key.split(";"))
                    .map(String::trim)
                    .filter(s -> !s.isEmpty() && s.contains(":"))
                    .map(s -> s.split(":")[1].trim())
                    .collect(Collectors.joining(","));
        } else {
            // 格式2：直接使用原字符串
            processedKey = key;
        }
        
        List<Long> tagIds = Arrays.stream(processedKey.split(","))
                .map(String::trim)
                .filter(s -> !s.isEmpty() && NumberUtils.isDigits(s))
                .map(Long::parseLong)
                .distinct()
                .collect(Collectors.toList());

        ExperienceReportRequestModel requestModel = ExperienceReportRequestModel
                .builder()
                .aiRecommendTags(tagIds)
                .platform(messageContext.getPlatform())
                .type(FilterType.AI_CONSULTANT.getCode())
                .offset(0)
                .limit(5)
                .build();

        try {
            AiExperienceReports aiExperienceReportListByTags = experienceReportDomainService.getAiExperienceReportListInfo(requestModel);
            log.info("query={},[MedicalCaseCardBuilder]aiExperienceReportListByTags={},req={}", messageContext.getMsg(),JsonUtils.toJsonString(aiExperienceReportListByTags), JsonUtils.toJsonString(requestModel));
            if (aiExperienceReportListByTags != null) {
                List<AiCaseInfo> caseList = aiExperienceReportListByTags.getCaseList();
                CaseCardData caseCardData = CaseCardData.builder().caseInfoList(caseList)
                        .jumpUrl(aiExperienceReportListByTags.getDetailUrl())
                        .count(aiExperienceReportListByTags.getTotalCount() == null ? 0L : Long.valueOf(aiExperienceReportListByTags.getTotalCount())).build();
                log.info("query={},[caseCardData]:{}", messageContext.getMsg(), JsonUtils.toJsonString(caseCardData));
                streamEventCardDataDTO.setCardProps(caseCardData.toMap());
            }

        } catch (Exception e) {
            log.error("query={}, req={},<MedicalCaseCardBuilder>error", messageContext.getMsg(), JsonUtils.toJsonString(requestModel), e);
        }
    }

    @Override
    public String parseCardProps(StreamEventCardDataDTO streamEventCardDataDTO) {
        return "";
    }

}
