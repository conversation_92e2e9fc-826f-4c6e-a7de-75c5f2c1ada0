package com.sankuai.dzhealth.ai.service.agent.domain.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.sankuai.medicalcosmetology.display.dto.DoctorCardInfoAndCase;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.math.NumberUtils;

import java.io.Serializable;

/**
 * @author:chenwei
 * @time: 2025/7/18 16:34
 * @version: 0.0.1
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class SupplyRecommendModel implements Serializable {

    /**
     * 供给类型枚举
     */
    public enum SupplyType {
        /**
         * 商户
         */
        SHOP("shop", "商户"),

        /**
         * 医生
         */
        DOCTOR("doctor", "医生");

        /**
         * 类型编码
         */
        private final String code;

        /**
         * 类型描述
         */
        private final String desc;

        SupplyType(String code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }

        /**
         * 根据编码获取枚举值
         *
         * @param code 类型编码
         * @return 对应的枚举值，如果不存在则返回null
         */
        public static SupplyType fromCode(String code) {
            if (code == null) {
                return null;
            }

            for (SupplyType type : SupplyType.values()) {
                if (type.getCode().equals(code)) {
                    return type;
                }
            }
            return null;
        }
    }

    /**
     * 商户排序枚举
     */
    public enum ShopSortType {
        /**
         * 智能排序
         */
        DEFAULT("DEFAULT", "智能排序"),

        /**
         * 距离由近到远
         */
        DISTANCE_ASC("DISTANCE_ASC", "距离由近到远"),

        /**
         * 销量从高到低
         */
        SOLDS_DESC("SOLDS_DESC", "销量从高到低"),

        /**
         * 评分从高到低
         */
        RATING_SCORE_DESC("RATING_SCORE_DESC", "评分从高到低");

        /**
         * 排序编码
         */
        private final String code;

        /**
         * 排序描述
         */
        private final String desc;

        ShopSortType(String code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }

        /**
         * 根据编码获取枚举值
         *
         * @param code 排序编码
         * @return 对应的枚举值，如果不存在则返回默认排序(DEFAULT)
         */
        public static ShopSortType fromCode(String code) {
            if (code == null) {
                return DEFAULT;
            }

            for (ShopSortType type : ShopSortType.values()) {
                if (type.getCode().equals(code)) {
                    return type;
                }
            }
            return DEFAULT;
        }
    }

    /**
     * 医生排序枚举
     */
    public enum DoctorSortType {
        /**
         * 智能排序
         */
        DEFAULT("0", "DEFAULT"),

        /**
         * 工作年限从高到低
         */
        WORK_YEARS_DESC("1", "WORK_YEARS_DESC"),

        /**
         * 评价数量从高到低
         */
        REVIEW_NUMS_DESC("2", "REVIEW_NUMS_DESC"),
        DISTANCE_ASC("3", "DISTANCE_ASC");

        /**
         * 排序编码
         */
        private final String code;

        /**
         * 排序描述
         */
        private final String desc;

        DoctorSortType(String code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String getCode() {
            return code;
        }

        public int getIntCode() {
            return NumberUtils.toInt(code);
        }

        public String getDesc() {
            return desc;
        }

        /**
         * 根据编码获取枚举值
         *
         * @param desc 排序编码
         * @return 对应的枚举值，如果不存在则返回默认排序(DEFAULT)
         */
        public static DoctorSortType fromDesc(String desc) {
            if (desc == null) {
                return DEFAULT;
            }

            for (DoctorSortType type : DoctorSortType.values()) {
                if (type.getDesc().equals(desc)) {
                    return type;
                }
            }
            return DEFAULT;
        }
    }

    @SerializedName("供给ID")
    @Expose
    @JSONField(name = "供给ID")
    @JsonProperty("supplyId")
    private String supplyId;


    @Expose(serialize = false)
    @JSONField(name = "供给类目")
    @JsonProperty("supplyCate")
    private String supplyCate;


    @Expose(serialize = false)
    @JSONField(name = "供给类型")
    @JsonProperty("supplyType")
    @FieldDoc(description = "供给类型,SupplyRecommendModel的code")
    private String supplyType;

    @SerializedName("供给名称")
    @Expose
    @JSONField(name = "供给名称")
    @JsonProperty("supplyName")
    private String supplyName;


    @Expose(serialize = false)
    @JSONField(name = "供给搜索词")
    @JsonProperty("supplySearchWord")
    private String supplySearchWord;

    @SerializedName("供给推荐理由")
    @Expose
    @JSONField(name = "供给推荐理由")
    @JsonProperty("supplyRecommendReason")
    private String supplyRecommendReason;

    @SerializedName("案例标签")
    @Expose
    @JSONField(name = "案例标签")
    @JsonProperty("medicalCaseTag")
    private String medicalCaseTag;

    @SerializedName("商户筛选项")
    @Expose
    @JSONField(name = "商户筛选项")
    @JsonProperty("shopTagFilter")
    private String shopTagFilter;

    @Expose(serialize = false)
    @JSONField(name = "医生筛选项")
    @JsonProperty("doctorFilter")
    private Integer doctorFilter;


    @Expose(serialize = false)
    @JSONField(name = "价格筛选项")
    @JsonProperty("priceRangeFilter")
    private String priceRangeFilter;

    @Expose(serialize = false)
    @JSONField(name = "排序方式")
    @JsonProperty("sortFilter")
    @FieldDoc(description = "排序方式,参考ShopSortType或DoctorSortType的code")
    private String sortFilter;

    @Expose(serialize = false)
    @JSONField(name = "距离")
    @JsonProperty("distance")
    private Integer distance;

    @Expose(serialize = false)
    @JSONField(name = "经纬度")
    @JsonProperty("latLng")
    private String latLng;

    @Expose(serialize = false)
    @JSONField(serialize = false)
    private String topId;

    @Expose(serialize = false)
    @JSONField(serialize = false)
    private String goodsId;

    @Expose(serialize = false)
    @JSONField(serialize = false)
    private String goodsType;

    @Expose(serialize = false)
    @JSONField(serialize = false)
    private DoctorCardInfoAndCase doctorCardInfoAndCase;
}
