package com.sankuai.dzhealth.ai.service.domain.service.impl.xhs;

import com.dianping.haima.entity.haima.HaimaContent;
import com.dianping.rhino.Rhino;
import com.dianping.rhino.threadpool.DefaultThreadPoolProperties;
import com.dianping.rhino.threadpool.ThreadPool;
import com.dianping.squirrel.client.StoreKey;
import com.dianping.squirrel.client.impl.redis.RedisStoreClient;
import com.fasterxml.jackson.databind.JsonNode;
import com.meituan.mdp.boot.starter.util.Pair;
import com.sankuai.dzhealth.ai.service.api.xhs.XhsNoteCallbackService;
import com.sankuai.dzhealth.ai.service.domain.entity.xhs.IndustryTopic;
import com.sankuai.dzhealth.ai.service.domain.entity.xhs.TaskCenterData;
import com.sankuai.dzhealth.ai.service.domain.utils.JsonUtils;
import com.sankuai.dzhealth.ai.service.infrastructure.acl.HaimaAcl;
import com.sankuai.dzhealth.ai.service.request.xhs.XhsBuildNoteRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.sankuai.dzhealth.ai.service.domain.service.impl.xhs.MedicalNoteService.DELAY_TASK_POOL;

/**
 * 生活娱乐笔记服务 - 处理JOY_LIFE业务线的笔记生成
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class JoyLifeNoteService {

    @Autowired
    private HaimaAcl haimaAcl;

    @Autowired
    private XhsNoteGenerator xhsNoteGenerator;

    @Autowired
    private CallbackServiceFactory callbackServiceFactory;

    @Autowired
    @Qualifier("redisClient0")
    private RedisStoreClient redisStoreClient0;

    @Autowired
    private YushuScrapeService yushuScrapeService;

    @Autowired
    private XhsNoteDataEnricher dataEnricher;

    public static final ThreadPool TASK_POOL = Rhino.newThreadPool("joyLifeNote",
            DefaultThreadPoolProperties.Setter().withCoreSize(100).withMaxSize(200).withMaxQueueSize(1000));


    public void processJoyLifeNote(XhsBuildNoteRequest request) {
        try {
            Map<String, String> params = request.getParams();
            List<String> industryList = JsonUtils.parseArray(params.get("industry"), String.class);

            log.info("processJoyLifeNote,req={}", JsonUtils.toJsonString(request));

            List<TaskCenterData.CookieData> cookies = JsonUtils.parseArray(request.getCookie(), TaskCenterData.CookieData.class);
            if (CollectionUtils.isEmpty(cookies)) {
                throw new IllegalArgumentException("<cookie is error>");
            }

            String industryTopicJson = params.get("industry_topic");
            long timeout = request.getMaxTimeout() == null ? 600L : request.getMaxTimeout();

            for (String industry : industryList) {
                String[] split = industry.split("-");
                String searchWord = split[split.length - 1];
                String batch = request.getBatch() + "-" + request.getBizCode() + "-" + industry;

                if (StringUtils.isNotBlank(industryTopicJson)) {
                    List<IndustryTopic> industryTopicList = JsonUtils.parseArray(industryTopicJson, IndustryTopic.class);

                    Map<String, List<String>> industryTopicMap = industryTopicList.stream()
                            .filter(item -> item != null && StringUtils.isNotBlank(item.getIndustry())
                                    && CollectionUtils.isNotEmpty(item.getTopicList()))
                            .collect(Collectors.toMap(
                                    IndustryTopic::getIndustry,
                                    IndustryTopic::getTopicList,
                                    (existing, replacement) -> existing // 如果有重复key，保留第一个
                            ));

                    if (industryTopicMap.containsKey(industry) && CollectionUtils.isNotEmpty(industryTopicMap.get(industry))) {
                        List<String> topicList = industryTopicMap.get(industry);
                        for (String topic : topicList) {
                            String newBatch = batch + "-" + topic;
                            StoreKey storeKey = new StoreKey("xhs_request_cache", newBatch);
                            redisStoreClient0.set(storeKey, JsonUtils.toJsonString(request));
                            yushuScrapeService.scrape(topic, newBatch, cookies, request.getPrompt());
                            delayGenerate(timeout, request, newBatch);
                        }
                    } else {
                        StoreKey storeKey = new StoreKey("xhs_request_cache", batch);
                        redisStoreClient0.set(storeKey, JsonUtils.toJsonString(request));
                        yushuScrapeService.scrape(searchWord, batch, cookies, request.getPrompt());
                        delayGenerate(timeout, request, batch);
                    }
                } else {
                    StoreKey storeKey = new StoreKey("xhs_request_cache", batch);
                    redisStoreClient0.set(storeKey, JsonUtils.toJsonString(request));
                    yushuScrapeService.scrape(searchWord, batch, cookies, request.getPrompt());
                    delayGenerate(timeout, request, batch);
                }

            }


        } catch (Exception e) {
            log.error("<JoyLifeNoteService><processNote> error", e);
            throw new RuntimeException(e);
        }
    }

    private void delayGenerate(long timeoutSeconds, XhsBuildNoteRequest request, String batch) {
        // 异步延迟调用processNote，不阻塞当前线程，使用自定义线程池
        CompletableFuture.delayedExecutor(timeoutSeconds, TimeUnit.SECONDS, DELAY_TASK_POOL.getExecutor())
                .execute(() -> {
                    try {
                        log.info("开始处理超时后的笔记生成任务，batchId: {}, 延迟时间: {}秒", batch, timeoutSeconds);
                        processNote(request, batch, "兜底笔记");
                    } catch (Exception ex) {
                        log.error("延迟处理笔记生成任务失败，batchId: {}", batch, ex);
                    }
                });
    }


    /**
     * 处理生活娱乐笔记生成请求
     *
     * @param request 笔记生成请求
     */
    public void processNote(XhsBuildNoteRequest request, String batch, String msg) {
        try {
            if (getRequestContext(batch) == null) {
                return;
            }
            List<HaimaContent> basePromptConfigs = haimaAcl.getContent("xhs_note_base_prompt", null);
            Map<String, String> params = request.getParams();
            String noteTemplate = params.get("note_template");
            JsonNode templateJsonNode = JsonUtils.parseJsonNode(noteTemplate);
            String templateContent = getTextField(templateJsonNode, "templateContent");

            List<String> industryList = JsonUtils.parseArray(params.get("industry"), String.class);
            List<String> searchWordList = JsonUtils.parseArray(params.get("search_keyword"), String.class);
            List<String> coreInterestList = JsonUtils.parseArray(params.get("core_interest"), String.class);
            List<String> cityLimitList = JsonUtils.parseArray(params.get("city_limit"), String.class);
            List<String> extraInfoList = JsonUtils.parseArray(params.get("extra_info"), String.class);
            List<String> activityList = JsonUtils.parseArray(params.get("activity_info"), String.class);

            String searchWordText = String.join("\n\n", searchWordList);
            String coreInterestText = String.join("\n\n", coreInterestList);
            String citiesLimitText = String.join("\n\n", cityLimitList);
            String extraInfoText = String.join("\n\n", extraInfoList);
            String activityText = String.join("\n\n", activityList);

            int count = NumberUtils.toInt(params.get("count"), 5);

            Pair<String, String> promptConfig = getPromptConfig("joyLife", "joyLife_cover", basePromptConfigs);
            String basePrompt = promptConfig.getKey();
            String baseCoverPrompt = promptConfig.getValue();
            
            // 先替换所有不变的占位符
            String baseNotePrompt = basePrompt.replace("{{{template}}}", noteTemplate)
                    .replace("{{{searchWord}}}", searchWordText)
                    .replace("{{{coreInterest}}}", coreInterestText)
                    .replace("{{{cityLimit}}}", citiesLimitText)
                    .replace("{{{extraInfoList}}}", extraInfoText)
                    .replace("{{{activityInfo}}}", activityText);

            XhsNoteCallbackService callbackService = callbackServiceFactory.getCallbackService(request.getCallbackAppkey());

            String currentIndustry = extractSuffixFromBatchId(batch);


            String hotNotes = dataEnricher.getHotNotes(currentIndustry, batch);

            String notePrompt = baseNotePrompt.replace("{{{industry}}}", currentIndustry)
                    .replace("{{{hotNotes}}}", hotNotes);

            for (int i = 0; i < count; i++) {
                CompletableFuture.runAsync(() -> {
                    xhsNoteGenerator.generateSingleNote(
                            notePrompt,
                            baseCoverPrompt,
                            request.getBatch(),
                            request.getBizCode(),
                            false,
                            callbackService,
                            msg
                    );
                }, TASK_POOL.getExecutor());
            }


            int totalNotes = industryList.size() * count;
            log.info("Successfully submitted {} JoyLife note generation tasks ({} industries × {} notes each) for batchId: {}", 
                    totalNotes, industryList.size(), count, batch);
        } catch (Exception e) {
            log.error("Error processing JoyLife note request for batchId: {}", batch, e);
            throw new RuntimeException(e);
        } finally {
            clearRequestContext(batch);
        }
    }

    public XhsBuildNoteRequest getRequestContext(String batchId) {
        StoreKey storeKey = new StoreKey("xhs_request_cache", batchId);
        String cacheRequest =  redisStoreClient0.get(storeKey);
        return JsonUtils.parseObject(cacheRequest, XhsBuildNoteRequest.class);
    }

    public void clearRequestContext(String batchId) {
        if (StringUtils.isNotBlank(batchId) && getRequestContext(batchId) != null) {
            StoreKey storeKey = new StoreKey("xhs_request_cache", batchId);
            redisStoreClient0.delete(storeKey);
            log.info("Cleared request context cache for batchId: {}", batchId);
        }
    }

    private String extractSuffixFromBatchId(String batchId) {
        if (batchId == null || batchId.isEmpty()) {
            return "";
        }

        String[] parts = batchId.split("-");
        return parts.length > 0 ? parts[parts.length - 1] : "";
    }


    private String getTextField(JsonNode jsonNode, String fieldName) {
        JsonNode node = jsonNode.get(fieldName);
        return node != null ? node.asText() : StringUtils.EMPTY;
    }


    /**
     * 获取提示词配置
     *
     * @param firstBizCode 第一个业务代码
     * @param secondBizCode 第二个业务代码
     * @param basePromptConfigs 基础提示词配置列表
     * @return 提示词配置对
     */
    private Pair<String, String> getPromptConfig(String firstBizCode, String secondBizCode, List<HaimaContent> basePromptConfigs) {
        String basePrompt = "";
        String baseCoverPrompt = "";

        if (CollectionUtils.isNotEmpty(basePromptConfigs)) {
            Optional<HaimaContent> joyConfig = basePromptConfigs.stream()
                    .filter(e -> firstBizCode.equals(e.getContentString("bizCode")))
                    .findFirst();
            if (joyConfig.isPresent()) {
                basePrompt = joyConfig.get().getContentString("promptTemplate");
            }

            Optional<HaimaContent> coverConfig = basePromptConfigs.stream()
                    .filter(e -> secondBizCode.equals(e.getContentString("bizCode")))
                    .findFirst();
            if (coverConfig.isPresent()) {
                baseCoverPrompt = coverConfig.get().getContentString("promptTemplate");
            }
        }

        return new Pair<>(basePrompt, baseCoverPrompt);
    }
}
