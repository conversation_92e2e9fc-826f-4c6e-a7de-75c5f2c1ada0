package com.sankuai.dzhealth.ai.service.agent.domain.buffer.label;

import com.meituan.mdp.boot.starter.util.Pair;

import java.util.List;

/**
 * @author:chenwei
 * @time: 2025/7/7 16:22
 * @version: 0.0.1
 */
public interface TruncateLabel {

    boolean acceptTruncate(String data);

    List<Pair<String, String>> parseLabels(String data);

    String getIdentifier();

    String getContent(Pair<String, String> label);




}
