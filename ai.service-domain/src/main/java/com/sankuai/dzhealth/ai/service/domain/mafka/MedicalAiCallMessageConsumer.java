package com.sankuai.dzhealth.ai.service.domain.mafka;

import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.dianping.lion.client.util.JsonUtils;
import com.meituan.mafka.client.MafkaClient;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mafka.client.consumer.ConsumerConstants;
import com.meituan.mafka.client.consumer.IConsumerProcessor;
import com.sankuai.dzhealth.ai.service.domain.service.MtPhoneCallService;
import com.sankuai.dzhealth.ai.service.domain.utils.HistoryChatUtils;
import com.sankuai.dzhealth.ai.service.domain.utils.context.AiCallMessageContext;
import com.sankuai.dzhealth.ai.service.infrastructure.mafka.MafkaShutDownHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;
import java.util.Properties;


@Slf4j
@Component
public class MedicalAiCallMessageConsumer implements InitializingBean {


    @Autowired
    private MtPhoneCallService  mtPhoneCallService;

    private static final String CAT_TYPE = MedicalAiCallMessageConsumer.class.getSimpleName();

    @Override
    public void afterPropertiesSet() throws Exception {
        Properties properties = new Properties();
        properties.setProperty(ConsumerConstants.MafkaBGNamespace, "com.sankuai.mafka.castle.daojiacommon");
        properties.setProperty(ConsumerConstants.MafkaClientAppkey, "com.sankuai.dzhealth.ai.service");
        properties.setProperty(ConsumerConstants.SubscribeGroup, "medical.ai.call.consumer");
        properties.setProperty(ConsumerConstants.MafkaDelayRetryCount, "3");
        IConsumerProcessor processor = MafkaClient.buildConsumerFactory(properties, "MedicalAiCall");
        MafkaShutDownHelper.registerHook(processor);
        processor.recvMessageWithParallel(String.class, (message, context) -> {
            Transaction transaction = Cat.newTransaction(CAT_TYPE, "MedicalAiCallMessageConsumer");
            String messageStr=String.valueOf(message.getBody());
            try{
                log.info("[MedicalAiCallMessageConsumer] 处理延迟消息: message:{}", messageStr);
                if (StringUtils.isBlank(messageStr)) {
                    log.info("[MedicalAiCallMessageConsumer] 处理延迟消息: messageStr is null");
                    return ConsumeStatus.CONSUME_SUCCESS;
                }
                String dialog=HistoryChatUtils.getHistoryChat(messageStr);
                AiCallMessageContext aiCallMessage = JsonUtils.fromJson(messageStr, AiCallMessageContext.class);
                if (Objects.isNull(aiCallMessage)) {
                    log.info("[MedicalAiCallMessageConsumer] 处理延迟消息: medicalAiCallMessage is null");
                    return ConsumeStatus.CONSUME_SUCCESS;
                }
                mtPhoneCallService.getChatSummaryAndRetry(dialog,aiCallMessage.getContactId(),aiCallMessage.getReleaseReason());
                return ConsumeStatus.CONSUME_SUCCESS;

            }catch (Exception e){
                Cat.logEvent("MedicalAiCallMessageConsumer", "handle.exception", "1", e.getMessage());
                log.error("MedicalAiCallMessageConsumer handle exception for message:{}, e:", messageStr, e);
                transaction.setStatus(e);
                return ConsumeStatus.RECONSUME_LATER;
            }finally {
                transaction.complete();
            }
        });
    }
}
