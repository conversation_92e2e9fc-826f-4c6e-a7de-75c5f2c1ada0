package com.sankuai.dzhealth.ai.service.domain.evaluation.config;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @since : 2025/7/29 17:06
 */
@Data
@Slf4j
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class MultiEvaluationConfig {
    private String bizScene;
    private String modelScene;
    private String systemPrompt;
    private String userPrompt;
    /**
     * 0-单轮对话，1-多轮对话，2-单轮多轮
     */
    private String type;
}
