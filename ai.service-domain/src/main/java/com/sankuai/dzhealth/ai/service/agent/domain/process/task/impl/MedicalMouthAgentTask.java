package com.sankuai.dzhealth.ai.service.agent.domain.process.task.impl;

import com.alibaba.fastjson.JSON;
import com.dianping.cat.Cat;
import com.dianping.haima.entity.haima.HaimaContent;
import com.dianping.rhino.Rhino;
import com.dianping.rhino.threadpool.DefaultThreadPoolProperties;
import com.dianping.rhino.threadpool.ThreadPool;
import com.fasterxml.jackson.databind.JsonNode;
import com.google.common.collect.Lists;
import com.meituan.beauty.narcissus.constant.BusinessTypeEnum;
import com.sankuai.dzhealth.ai.service.agent.domain.config.IntentTypeConfig;
import com.sankuai.dzhealth.ai.service.agent.domain.context.TaskContext;
import com.sankuai.dzhealth.ai.service.agent.domain.enums.TaskTypeEnum;
import com.sankuai.dzhealth.ai.service.agent.domain.model.TaskProcessResult;
import com.sankuai.dzhealth.ai.service.agent.domain.model.decisionflow.DecisionTreeModel;
import com.sankuai.dzhealth.ai.service.agent.domain.process.task.GeneralTask;
import com.sankuai.dzhealth.ai.service.agent.domain.process.task.Task;
import com.sankuai.dzhealth.ai.service.agent.domain.process.task.TaskConfig;
import com.sankuai.dzhealth.ai.service.agent.domain.service.MedicalDecisionRagService;
import com.sankuai.dzhealth.ai.service.agent.enums.ContextExtraKey;
import com.sankuai.dzhealth.ai.service.agent.infrastructure.model.SkinReportTotalDegreeNameQuery;
import com.sankuai.dzhealth.ai.service.agent.infrastructure.proxy.skinreport.BeautyNarcissusSkinReportAcl;
import com.sankuai.dzhealth.ai.service.domain.utils.JsonUtils;
import com.sankuai.dzhealth.ai.service.domain.utils.context.RequestContext;
import com.sankuai.dzhealth.ai.service.domain.utils.context.RequestContextConstant;
import com.sankuai.dzhealth.ai.service.enums.BizSceneEnum;
import com.sankuai.dzhealth.ai.service.infrastructure.acl.HaimaAcl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.Serializable;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * @author:chenwei
 * @time: 2025/7/11 10:47
 * @version: 0.0.1
 */

@Component
@Slf4j
public class MedicalMouthAgentTask extends GeneralTask implements Task {

    // 常量定义
    private static final String MEDICAL_PRODUCT_CONFIG_KEY = "medicalProduct";

    private static final String IRRELEVANT = "irrelevant";
    private static final String MEDICAL_BAIKE_CONFIG_KEY = "medicalBaike";
    private static final String HAIMA_CONFIG_KEY = "medical_task_ai_config";
    private static final String DECISIONS_PLACEHOLDER = "{{{decisions}}}";
    private static final String HISTORY_DECISIONS_PLACEHOLDER = "{{{historyDecisions}}}";

    private static final String PRODUCT_CPV = "{{{productCpv}}}";
    private static final String SKIN_PLACEHOLDER = "{{{skin}}}";
    private static final String DEFAULT_SKIN_PREFIX = "当前皮肤状态为: ";
    private static final String JSON_MARKERS = "```json|```";

    @Autowired
    private HaimaAcl haimaAcl;

    @Autowired
    private MedicalBaiKeTask medicalBaiKeTask;

    @Autowired
    private MedicalProductTask medicalProductTask;

    @Autowired
    private MedicalAppointmentTask medicalAppointmentTask;

    @Autowired
    private MedicalDecisionRagService medicalDecisionRagService;

    @Autowired
    private BeautyNarcissusSkinReportAcl beautyNarcissusSkinReportAcl;

    @Autowired
    private IrrelevantTask irrelevantTask;

    public static final ThreadPool RAG_POOL = Rhino.newThreadPool("ragPool",
            DefaultThreadPoolProperties.Setter().withCoreSize(50).withMaxSize(100).withMaxQueueSize(500));


    @Override
    public boolean accept(String type) {
        return TaskTypeEnum.MEDICAL_AGENT_TASK.getType().equals(type);
    }

    @Override
    public TaskProcessResult process(TaskContext context) {
        Map<String, TaskConfig> configMap = loadTaskConfigs();

        String systemPrompt = context.getTaskConfig().getSystemPrompt();
        String replace = systemPrompt.replace("{{{bizType}}}", BizSceneEnum.MOUTH_CONSULT.getBizScene().equals(context.getMessageContext().getBizType()) ? "口腔" : "医美");
        context.getTaskConfig().setSystemPrompt(replace);
        if (StringUtils.isBlank(context.getTaskConfig().getUserPrompt())) {
            context.getTaskConfig().setUserPrompt(context.getMessageContext().getMsg());
        }
        Map<String, Serializable> extra = context.getMessageContext().getExtra();

        String answer = MapUtils.isNotEmpty(extra) && (extra.containsKey(ContextExtraKey.OPTION_INPUT.getKey()) || extra.containsKey(ContextExtraKey.SKIN_REPORT_ID.getKey()))
                ? processExtraContext(context, configMap, extra)
                : processIntentRecognition(context, configMap);

        return TaskProcessResult.builder()
                .taskContext(context)
                .answer(answer)
                .build();
    }

    /**
     * 加载任务配置
     */
    private Map<String, TaskConfig> loadTaskConfigs() {
        List<HaimaContent> medicalTaskAiConfig = haimaAcl.getContent(HAIMA_CONFIG_KEY, null);
        return medicalTaskAiConfig.stream().collect(Collectors.toMap(
                e -> e.getContentString("task"),
                this::buildTaskConfig,
                (a, b) -> a
        ));
    }

    /**
     * 构建任务配置
     */
    private TaskConfig buildTaskConfig(HaimaContent content) {
        TaskConfig config = JsonUtils.parseObject(content.getContentString("config"), TaskConfig.class);
        if (config == null) {
            config = new TaskConfig();
        }
        if (StringUtils.isNotBlank(content.getContentString("systemPrompt"))) {
            config.setSystemPrompt(content.getContentString("systemPrompt"));
        }
        return config;
    }

    /**
     * 处理额外上下文（选项输入或皮肤报告）
     */
    private String processExtraContext(TaskContext context, Map<String, TaskConfig> configMap,
                                       Map<String, Serializable> extra) {
        StringBuilder skinReportBuilder = new StringBuilder(DEFAULT_SKIN_PREFIX);

        if (extra.containsKey(ContextExtraKey.OPTION_INPUT.getKey())) {
            String optionValue = (String) extra.get(ContextExtraKey.OPTION_INPUT.getKey());
            context.getMessageContext().setMsg(optionValue);
            return processMedicalProduct(context, configMap, StringUtils.EMPTY);
        } else if (extra.containsKey(ContextExtraKey.SKIN_REPORT_ID.getKey())) {
            String skinReportId = (String) extra.get(ContextExtraKey.SKIN_REPORT_ID.getKey());
            String skinReport = buildSkinReport(context, skinReportId, skinReportBuilder);
            context.getTaskConfig().setUserPrompt(skinReport);
            String intent = getJsonAnswer(context).replaceAll(JSON_MARKERS, "");
            if (StringUtils.isBlank(intent)) {
                context.getMessageContext().setMsg(skinReport);
            } else {
                JsonNode intentNode = JsonUtils.parseJsonNode(intent);
                String rewriteText = intentNode.get("rewriteText").asText();
                context.getMessageContext().getExtra().put("rewriteText", rewriteText);
                context.getMessageContext().setMsg(rewriteText);
            }

            return processMedicalProduct(context, configMap, skinReport);
        }

        return StringUtils.EMPTY;
    }

    /**
     * 构建皮肤报告信息
     */
    private String buildSkinReport(TaskContext context, String skinReportId, StringBuilder skinReportBuilder) {
        SkinReportTotalDegreeNameQuery skinQuery = new SkinReportTotalDegreeNameQuery();
        skinQuery.setPlatform(context.getMessageContext().getPlatform() == 2 ? 0 : 1);
        skinQuery.setUserId(context.getMessageContext().getUserId());
        skinQuery.setReportUuid(skinReportId);
        skinQuery.setBusinessType(BusinessTypeEnum.SKIN_ARCHIVE.code);

        List<String> skinReportList = beautyNarcissusSkinReportAcl.queryTotalDegreeName(skinQuery);
        skinReportList.forEach(s -> skinReportBuilder.append(s).append(","));

        log.info("skinReq={},skinReport:{}", JsonUtils.toJsonString(skinQuery), skinReportBuilder.toString());
        return skinReportBuilder.toString();
    }

    /**
     * 处理意图识别逻辑
     */
    private String processIntentRecognition(TaskContext context, Map<String, TaskConfig> configMap) {
        String intent = getJsonAnswer(context).replaceAll(JSON_MARKERS, "");

        if (StringUtils.isBlank(intent)) {
            log.info("意图为空，点击选项或者识别失败，需要有兜底逻辑");
            return StringUtils.EMPTY;
        }

        log.info("query={},intent={}", context.getMessageContext().getMsg(), intent);
        JsonNode intentNode = JsonUtils.parseJsonNode(intent);
        String intentType = intentNode.get("type").asText();
        String rewriteText = intentNode.get("rewriteText").asText();

        context.getMessageContext().getExtra().put("intentType", intentType);
        context.getMessageContext().getExtra().put("rewriteText", rewriteText);

        RequestContext.setAttribute(RequestContextConstant.INTENT_TYPE, intentType);

        // 评测意图识别
        buildMultiDialogueEvaluationRequest(context.getMessageContext(), context.getTaskConfig(), intentType);

        return routeByIntentType(context, configMap, intentType, rewriteText);
    }

    /**
     * 根据意图类型路由到不同的任务处理
     */
    private String routeByIntentType(TaskContext context, Map<String, TaskConfig> configMap,
                                     String intentType, String rewriteText) {
        if (intentType.contains(IntentTypeConfig.MEDICAL_KE_PU)) {
            Cat.logEvent("routeByIntentType","MEDICAL_KE_PU");
            return processMedicalBaike(context, configMap, rewriteText);
        } else if (intentType.contains(IntentTypeConfig.SHOP_BUY)) {
            Cat.logEvent("routeByIntentType","SHOP_BUY");
            return processMedicalProductWithRewrite(context, configMap, rewriteText);
        } else if (intentType.contains(IntentTypeConfig.RESERVE)) {
            Cat.logEvent("routeByIntentType","RESERVE");
            return processMedicalAppointment(context, configMap);
        } else {
            Cat.logEvent("routeByIntentType","irrelevant");
            return processIrrelevant(context, configMap);
        }

    }

    /**
     * 处理医疗百科任务
     */
    private String processMedicalBaike(TaskContext context, Map<String, TaskConfig> configMap,
                                       String userPrompt) {
        TaskConfig config = configMap.get(MEDICAL_BAIKE_CONFIG_KEY);
        config.setUserPrompt(userPrompt);

        CompletableFuture<String> medicalCpvFuture = CompletableFuture.supplyAsync(() -> medicalDecisionRagService.search(context.getMessageContext().getMsg(), 5, Lists.newArrayList("medicalCpv")), RAG_POOL.getExecutor());

        CompletableFuture<String> baikeFuture = CompletableFuture.supplyAsync(() -> medicalDecisionRagService.search(context.getMessageContext().getMsg(), 10, Lists.newArrayList("百科", "避雷针")), RAG_POOL.getExecutor());
        String decisionRagStr = medicalDecisionRagService.queryDecisionRagInfo(context.getMessageContext().getMsg(), 2, context.getMessageContext().getBizType());

        List<DecisionTreeModel> decisionTreeModels = JsonUtils.parseArray(decisionRagStr, DecisionTreeModel.class);

        String productCpv = medicalCpvFuture.join();
        String baikeKnowledge = baikeFuture.join();

        productCpv = productCpv + "\n\n<<百科信息>>:\n" + baikeKnowledge;

        String finalPrompt = buildPromptWithDecisions(config.getSystemPrompt(), JSON.toJSONString(decisionTreeModels), context, productCpv);
        config.setSystemPrompt(finalPrompt);

        TaskContext taskContext = createTaskContext(config, context);
        return medicalBaiKeTask.process(taskContext).getAnswer();
    }

    /**
     * 处理医疗产品任务（带重写文本）
     */
    private String processMedicalProductWithRewrite(TaskContext context, Map<String, TaskConfig> configMap,
                                                    String rewriteText) {
        TaskConfig config = configMap.get(MEDICAL_PRODUCT_CONFIG_KEY);
        config.setUserPrompt(rewriteText);

        TaskContext taskContext = createTaskContext(config, context);
        return medicalProductTask.process(taskContext).getAnswer();
    }

    private String processIrrelevant(TaskContext context, Map<String, TaskConfig> configMap) {

        TaskConfig config = configMap.get(IRRELEVANT);
        config.setUserPrompt(context.getMessageContext().getMsg());

        TaskContext taskContext = createTaskContext(config, context);

        irrelevantTask.process(taskContext);


        return StringUtils.EMPTY;

    }


    /**
     * 处理医疗产品任务
     */
    private String processMedicalProduct(TaskContext context, Map<String, TaskConfig> configMap,
                                         String skinReport) {
        TaskConfig config = configMap.get(MEDICAL_PRODUCT_CONFIG_KEY);
        config.setUserPrompt(context.getMessageContext().getMsg());

        String finalPrompt = buildPromptWithSkin(config.getSystemPrompt(), skinReport);
        config.setSystemPrompt(finalPrompt);

        TaskContext taskContext = createTaskContext(config, context);
        String answer = medicalProductTask.process(taskContext).getAnswer();
        buildMultiDialogueEvaluationRequest(taskContext.getMessageContext(),config, answer);
        return answer;
    }

    /**
     * 处理医疗预约任务
     */
    private String processMedicalAppointment(TaskContext context, Map<String, TaskConfig> configMap) {
        TaskConfig config = configMap.get("medicalAppointment");
        TaskContext taskContext = createTaskContext(config, context);
        return medicalAppointmentTask.process(taskContext).getAnswer();
    }

    /**
     * 构建包含决策信息的提示词
     */
    private String buildPromptWithDecisions(String systemPrompt, String decisionRagStr, TaskContext context, String productCpv) {
        String historyDecisions = Optional.of(
                JSON.toJSONString(context.getMessageContext().getMemoryModel())
        ).orElse("");

        return systemPrompt
                .replace(DECISIONS_PLACEHOLDER, decisionRagStr)
                .replace(HISTORY_DECISIONS_PLACEHOLDER, historyDecisions)
                .replace(PRODUCT_CPV, productCpv);

    }

    /**
     * 构建包含皮肤信息的提示词
     */
    private String buildPromptWithSkin(String systemPrompt, String skinReport) {

        return systemPrompt
                .replace(SKIN_PLACEHOLDER, skinReport);
    }

    /**
     * 创建任务上下文
     */
    private TaskContext createTaskContext(TaskConfig config, TaskContext originalContext) {
        TaskContext taskContext = new TaskContext();
        taskContext.setTaskConfig(config);
        taskContext.setMessageContext(originalContext.getMessageContext());
        taskContext.setTaskType(originalContext.getTaskType());
        return taskContext;
    }

    @Override
    public void after(TaskProcessResult result) {

    }
}
