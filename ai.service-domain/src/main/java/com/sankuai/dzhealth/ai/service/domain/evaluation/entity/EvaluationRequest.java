package com.sankuai.dzhealth.ai.service.domain.evaluation.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 评估请求实体类
 *
 * <AUTHOR>
 * @since 2025/1/15
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EvaluationRequest {

    /**
     * 评估ID
     */
    @NotNull(message = "评估ID不能为空")
    private Long evaluationId;

    /**
     * 会话ID列表，为空就是所有evaluationId关联的sessionId
     */
    private List<String> sessionIds;

    /**
     * 版本号
     */
    @NotNull(message = "版本号不能为空")
    private Integer version;
}

