package com.sankuai.dzhealth.ai.service.domain.service.thinking;

import java.util.Map;

/**
 * 思考引擎服务接口
 */
public interface ThinkingEngineService {
    
    /**
     * 创建思考会话
     *
     * @param query 查询问题
     * @param searchConfig 搜索配置
     * @return 会话ID
     */
    Long createThinkingSession(String query, Map<String, Object> searchConfig);
    
    /**
     * 执行思考过程
     *
     * @param sessionId 会话ID
     * @return 思考结果
     */
    Map<String, Object> executeThinking(Long sessionId);
    
    /**
     * 获取思考结果
     *
     * @param sessionId 会话ID
     * @return 思考结果
     */
    Map<String, Object> getThinkingResult(Long sessionId);
}