package com.sankuai.dzhealth.ai.service.agent.domain.model;

import com.sankuai.dzhealth.ai.service.agent.domain.model.decisionflow.DecisionTreeModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * @author:chenwei
 * @time: 2025/7/8 17:10
 * @version: 0.0.1
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class MemoryModel implements Serializable {

    private List<MemoryData> memoryList;

    private List<DecisionTreeModel> decisionTreeModelList;

}
