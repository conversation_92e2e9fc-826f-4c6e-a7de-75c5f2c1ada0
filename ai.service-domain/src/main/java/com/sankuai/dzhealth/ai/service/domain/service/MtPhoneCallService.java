package com.sankuai.dzhealth.ai.service.domain.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dianping.appkit.client.AppkitClient;
import com.dianping.appkit.localcache.listeners.ChangeEvent;
import com.dianping.appkit.localcache.listeners.SceneListener;
import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.dianping.haima.entity.haima.HaimaContent;
import com.dianping.lion.client.Lion;
import com.meituan.mdp.boot.starter.MdpContextUtils;
import com.meituan.mtrace.scene.util.JsonUtil;
import com.sankuai.call.sdk.entity.aicall.AiCallParamBO;
import com.sankuai.call.sdk.entity.aicall.AiCallResponseDTO;
import com.sankuai.call.sdk.service.IAiCallService;
import com.sankuai.dzhealth.ai.service.domain.chat.model.ChatSummaryResult;
import com.sankuai.dzhealth.ai.service.domain.chat.model.MtCallCard;
import com.sankuai.dzhealth.ai.service.domain.chat.model.MtCallTenantInfo;
import com.sankuai.dzhealth.ai.service.domain.enums.CardStatusEnum;
import com.sankuai.dzhealth.ai.service.domain.enums.LionEnum;
import com.sankuai.dzhealth.ai.service.domain.enums.SummarySceneEnum;
import com.sankuai.dzhealth.ai.service.domain.service.strategy.SummaryFactory;
import com.sankuai.dzhealth.ai.service.domain.utils.context.ChatSummaryContext;
import com.sankuai.dzhealth.ai.service.domain.utils.context.MtCallContext;
import com.sankuai.dzhealth.ai.service.domain.utils.context.MtPhoneCallContext;
import com.sankuai.dzhealth.ai.service.dto.CallCardDTO;
import com.sankuai.dzhealth.ai.service.dto.stream.StreamEventCardDataDTO;
import com.sankuai.dzhealth.ai.service.dto.stream.StreamEventDTO;
import com.sankuai.dzhealth.ai.service.enums.MsgRoleEnum;
import com.sankuai.dzhealth.ai.service.infrastructure.acl.HaimaAcl;
import com.sankuai.dzhealth.ai.service.infrastructure.repository.conversation.ChatMessageRepository;
import com.sankuai.dzhealth.ai.service.infrastructure.dal.entity.ChatMessageEntity;
import com.sankuai.dzhealth.ai.service.infrastructure.dal.entity.ChatPhoneCallTaskEntity;
import com.sankuai.dzhealth.ai.service.infrastructure.phoneCallTask.ChatPhoneCallTaskRepository;
import com.sankuai.dzhealth.ai.service.request.ChatSummaryRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

import static java.util.stream.Collectors.toMap;


@Slf4j
@Service
public class MtPhoneCallService implements InitializingBean {

    private static final String CAT_TYPE = MtPhoneCallService.class.getSimpleName();

    @Autowired
    private ChatClient.Builder callTemplateMatchClientBuilder;
    @Autowired
    private SummaryFactory summaryFactory;

    @Autowired
    private IAiCallService aiCallService;

    @Autowired
    private ChatPhoneCallTaskRepository phoneCallRepository;

    @Autowired
    private ChatMessageRepository chatMessageRepository;

    @Autowired
    private HaimaAcl haimaAcl;

    @Autowired
    private AppkitClient appkitClient;


    private String callClassifyTemplate = "";
    private String telContentSummaryTemplate = "";

    private Map<String, String> promptMap=new HashMap<>();


    //初始化电话外呼任务
    @Transactional(rollbackFor = Exception.class)
    public  ChatMessageEntity  generateCallTask(MtPhoneCallContext  mtPhoneCallContext){
        Transaction transaction = Cat.newTransaction(CAT_TYPE, "generateCallTask");
        transaction.setSuccessStatus();
        try {
            checkMtPhoneCallContextParam(mtPhoneCallContext);
            MtCallCard card = getCard(CardStatusEnum.INITIALIZATION);
            String type=callTemplateMatch(mtPhoneCallContext.getQuestion());
            if(StringUtils.isEmpty(type))
            {
                throw new RuntimeException("电话外呼模板匹配失败");
            }
            String prompt=promptMap.getOrDefault(type, "");
            if(StringUtils.isEmpty(prompt))
            {
                throw new RuntimeException("在海马上未找到对应配置type:"+type);
            }
            ChatPhoneCallTaskEntity chatPhoneCallTaskEntity = ChatPhoneCallTaskEntity.builder()
                    .messageId(System.currentTimeMillis())
                    .status(CardStatusEnum.INITIALIZATION.getCode())
                    .question(mtPhoneCallContext.getQuestion())
                    .mtShopId(mtPhoneCallContext.getShopId())
                    .sessionId(mtPhoneCallContext.getSessionId())
                    .mediatxt(type)
                    .build();
            phoneCallRepository.saveTask(chatPhoneCallTaskEntity);
            if(chatPhoneCallTaskEntity.getId()==null)
            {
                throw new Exception("插入任务数据失败");
            }
            if(phoneCallByHaiMa(String.valueOf(mtPhoneCallContext.getShopId()))==null)
            {
                throw new NoSuchElementException("在海马上未找到对应配置电话配置:"+mtPhoneCallContext.getShopId());
            }
            card.setTaskId(chatPhoneCallTaskEntity.getId());
            card.setStatus(CardStatusEnum.INITIALIZATION.getCode());
            card.setMediaTxt(type);
            ChatMessageEntity chatMessageEntity=ChatMessageEntity.builder()
                    .conversationId(String.valueOf(mtPhoneCallContext.getSessionId()))
                    .auditStatus(((byte) 1))
                    .contentType((byte) 1)
                    .messageId(String.valueOf(chatPhoneCallTaskEntity.getMessageId()))
                    .role(MsgRoleEnum.MTCALL.getBiz())
                    .content(card.toString())
                    .senderId(mtPhoneCallContext.getUserId())
                    .build();
            chatMessageRepository.saveMsgByPrimaryId(chatMessageEntity);
            if(chatMessageEntity.getId()==null)
            {
                throw new Exception("插入消息数据失败");
            }
            ChatPhoneCallTaskEntity updateEntity=new ChatPhoneCallTaskEntity();
            updateEntity.setId(chatPhoneCallTaskEntity.getId());
            updateEntity.setMessageId(chatMessageEntity.getId());
            phoneCallRepository.updateTask(updateEntity);
            return chatMessageEntity;
        } catch (Exception e) {
            transaction.setStatus(e);
            log.error("generateCallTask.fail,request={}", JSON.toJSONString(mtPhoneCallContext), e);
            return null;
        } finally {
            transaction.complete();
        }
    }

    public AiCallParamBO callInfo(String mtShopId,String mediaTxt,String question) throws Exception
    {
        String callTenant=Lion.getString(MdpContextUtils.getAppKey(), LionEnum.AI_CALL_TENANT_ID.getKey());
        if(StringUtils.isEmpty(callTenant))
        {
            throw new Exception(String.format("获取Lion：%s失败",LionEnum.AI_CALL_TENANT_ID.getKey()));
        }
        MtCallTenantInfo aiCallInfo = JSON.parseObject(callTenant, MtCallTenantInfo.class);
        String prompt=promptMap.getOrDefault(mediaTxt, "");
        Map<String, Object> userData = new HashMap<>();
        userData.put("tts_prologue","喂你好");
        userData.put("tts_prompt",prompt.replace("${question}", question));
        AiCallParamBO aiCallParamBO =new  AiCallParamBO();
        aiCallParamBO.setMediaTxt(userData);
        String phoneCall=phoneCallByHaiMa(mtShopId);
        aiCallParamBO.setDeviceNum(phoneCall);
        aiCallParamBO.setTenantId(aiCallInfo.getTenantId());
        aiCallParamBO.setRoutePoint(aiCallInfo.getRoutePoint());
        return aiCallParamBO;
    }

    //“帮我问问吧”电话外呼发起
    public boolean mtCallHospital(MtCallContext mtCallContext)
    {
        Transaction transaction = Cat.newTransaction(CAT_TYPE, "mtCallHospital");
        transaction.setSuccessStatus();
        try {
            ChatPhoneCallTaskEntity chatPhoneCallTaskEntity=phoneCallRepository.getTaskById(mtCallContext.getTaskId());
            MtCallCard card=getCard(CardStatusEnum.IN_PROGRESS);
            AiCallParamBO aiCallParamBO =callInfo(String.valueOf(chatPhoneCallTaskEntity.getMtShopId()),chatPhoneCallTaskEntity.getMediatxt(),chatPhoneCallTaskEntity.getQuestion());
            int retryCount = 0;
            while (retryCount < 5) {
                AiCallResponseDTO<String> req = aiCallService.call(aiCallParamBO);
                if (req.getCode() == 0) {
                    log.info("外呼成功，任务ID: {}, 响应: {}", mtCallContext.getTaskId(), req);
                    ChatPhoneCallTaskEntity updateEntity=ChatPhoneCallTaskEntity.builder()
                            .id(mtCallContext.getTaskId())
                            .callCount(1)
                            .contactId(req.getData())
                            .startTime(new Date())
                            .status(CardStatusEnum.IN_PROGRESS.getCode())
                            .build();
                    boolean flag=phoneCallRepository.updateTask(updateEntity);
                    if(!flag)
                    {
                        log.warn("电话外呼任务更新表失败，updateEntity: {}", updateEntity);
                    }
                    card.setStatus(CardStatusEnum.IN_PROGRESS.getCode());
                    card.setCallTime(1);
                    card.setTaskId(mtCallContext.getTaskId());
                    card.setStartTime(String.valueOf(updateEntity.getStartTime().getTime()));
                    flag=chatMessageRepository.updateMessageContentByUserAndConversation(
                           mtCallContext.getConversationId(), mtCallContext.getMessageId(),JSON.toJSONString(buildContext(card.toString(),mtCallContext.getMessageId())));
                    if(!flag)
                    {
                        log.warn("消息更新任务失败 userId：{},conversationId：{},messageId:{},card:{}",  mtCallContext.getUserId(),mtCallContext.getConversationId(), mtCallContext.getMessageId(),card.toString());
                    }
                    return true;
                } else {
                    log.warn("外呼失败，任务ID: {}, 响应: {}, 重试次数: {}", mtCallContext.getTaskId(), req, retryCount + 1);
                    retryCount++;
                }
            }
            MtCallCard failCard=getCard(CardStatusEnum.FAILED);
            card.setSubTitle(card.getSubTitle()+chatPhoneCallTaskEntity.getQuestion());
            chatMessageRepository.updateMessageContentByUserAndConversation(
                    mtCallContext.getConversationId(), mtCallContext.getMessageId(),JSON.toJSONString(buildContext(failCard.toString(),mtCallContext.getMessageId())));
            transaction.setStatus(new Exception("外呼失败"));
            return false;
        }catch (Exception e){
            transaction.setStatus(e);
            log.error("mtCallHospital.fail,request={}", JSON.toJSONString(mtCallContext.getTaskId()), e);
            return false;
        }finally {
            transaction.complete();
        }
    }

    public boolean  mtCallHospitalCancel(MtCallContext mtCallContext)
    {

        Transaction transaction = Cat.newTransaction(CAT_TYPE, "mtCallHospital");
        transaction.setSuccessStatus();
        try {
            ChatPhoneCallTaskEntity entity=phoneCallRepository.getTaskById(mtCallContext.getTaskId());
            if(entity==null||entity.getMtShopId()==null)
            {
                throw new Exception("任务异常");
            }
            MtCallCard card=getCard(CardStatusEnum.USER_CANCELED);
            card.setSubTitle(card.getSubTitle()+entity.getQuestion());
            card.setStatus(CardStatusEnum.USER_CANCELED.getCode());
            card.setTaskId(entity.getId());
            String phoneCall=phoneCallByHaiMa(String.valueOf(entity.getMtShopId()));

            if(!StringUtils.isEmpty(phoneCall))
            {
                card.setTxt(card.getTxt()+":"+phoneCall);
            }
            entity.setStatus(CardStatusEnum.USER_CANCELED.getCode());
            entity.setEndTime(new Date());
            boolean flag=phoneCallRepository.updateTask(entity);
            if(!flag)
            {
                log.warn("更新任务失败 entity:{}",entity.toString());

            }
            flag=chatMessageRepository.updateMessageContentByUserAndConversation(
                  mtCallContext.getConversationId(), mtCallContext.getMessageId(),JSON.toJSONString(buildContext(card.toString(),mtCallContext.getMessageId())));
            if(!flag)
            {
                log.warn("消息更新任务失败 conversationId：{},messageId:{},card:{}",mtCallContext.getConversationId(), mtCallContext.getMessageId(),card.toString());
            }
            return true;
        }catch (Exception e){
            transaction.setStatus(e);
            log.error("mtCallHospitalCancel.fail,request={}", JSON.toJSONString(mtCallContext.getTaskId()), e);
            return false;
        }finally {
            transaction.complete();
        }

    }

    public void getChatSummaryAndRetry(String dialog,String contactId,String releaseReason){
        Transaction transaction = Cat.newTransaction(CAT_TYPE, "getChatSummaryAndRetry");
        transaction.setSuccessStatus();
        try {
            Integer  retryTime= Lion.getInt(MdpContextUtils.getAppKey(), LionEnum.AI_CALL_TIME.getKey());
            Integer  waitTime= Lion.getInt(MdpContextUtils.getAppKey(), LionEnum.AI_CALL_WAIT_TIME.getKey());
            ChatPhoneCallTaskEntity chatPhoneCallTaskEntity=phoneCallRepository.getTaskByContactId(contactId);
            if(chatPhoneCallTaskEntity==null)
            {
                throw new Exception("根据木星contactId取任务异常");
            }
            //  如果任务状态不是外呼，则直接返回，不进行后续操作。
            if(chatPhoneCallTaskEntity.getStatus()!=2) {
                return;
            }
            ChatSummaryResult summary=new ChatSummaryResult();
            summary.setSummaryResult(0);
            if (Arrays.asList("0", "1", "2").contains(releaseReason)&&!StringUtils.isBlank(dialog)) {
                ChatSummaryContext chatSummaryContext = ChatSummaryContext.builder()
                        .taskId(String.valueOf(chatPhoneCallTaskEntity.getId()))
                        .userIntention(chatPhoneCallTaskEntity.getQuestion())
                        .dialogueContent(dialog)
                        .sceneType(SummarySceneEnum.MEDICAL_AI_CALL_SUMMARY.getScene())
                        .build();
                summary = getChatSummary(chatSummaryContext);
            }
            //用户繁忙等待后重拨
            if(releaseReason.equals("9"))
            {
                Thread.sleep(waitTime);
            }
            ChatMessageEntity chatMessageEntity= chatMessageRepository.findByMessageId(chatPhoneCallTaskEntity.getMessageId());
            if(chatMessageEntity==null)
            {
                throw new Exception("根据messageId取消息异常");
            }
            AiCallParamBO aiCallParamBO=callInfo(String.valueOf(chatPhoneCallTaskEntity.getMtShopId()),chatPhoneCallTaskEntity.getMediatxt(),chatPhoneCallTaskEntity.getQuestion());
            //总结成功
            if(summary.getSummaryResult()==1)
            {

                MtCallCard card=getCard(CardStatusEnum.SUCCESS);
                card.setSubTitle(card.getSubTitle()+chatPhoneCallTaskEntity.getQuestion());
                card.setTxt(String.format("医院回复：\n%s",summary.getRelatedAnswers()));
                card.setRefinedTxt(summary.getConclusion());
                card.setStatus(CardStatusEnum.SUCCESS.getCode());
                card.setTaskId(chatPhoneCallTaskEntity.getId());
                chatPhoneCallTaskEntity.setEndTime(new Date());
                chatPhoneCallTaskEntity.setStatus(CardStatusEnum.SUCCESS.getCode());
                chatPhoneCallTaskEntity.setDialogRefinedData(summary.toString());
                JSONObject jsonDialog = new JSONObject();
                jsonDialog.put("dialog", dialog);
                chatPhoneCallTaskEntity.setDialogRawData(jsonDialog.toJSONString());
                boolean flag=phoneCallRepository.updateTask(chatPhoneCallTaskEntity);
                if(!flag)
                {
                    log.warn("更新任务失败 entity:{}",chatPhoneCallTaskEntity.toString());
                }
                flag=chatMessageRepository.updateMessageContentByUserAndConversation(
                        chatMessageEntity.getConversationId(), chatPhoneCallTaskEntity.getMessageId(),JSON.toJSONString(buildContext(card.toString(),chatPhoneCallTaskEntity.getMessageId())));
                if(!flag)
                {
                    log.warn("消息更新任务失败 conversationId：{},messageId:{},card:{}",chatMessageEntity.getConversationId(), chatMessageEntity.getMessageId(),card.toString());
                }
                return;
            }

            //用户负面情绪或者明确否定则不继续外呼
            if(summary.getSummaryResult()!=3&&summary.getSummaryResult()!=4)
            {
              while(chatPhoneCallTaskEntity.getCallCount()<retryTime)
              {
                  chatPhoneCallTaskEntity.setCallCount(chatPhoneCallTaskEntity.getCallCount()+1);
                  AiCallResponseDTO<String> req = aiCallService.call(aiCallParamBO);
                  if (req.getCode() == 0) {
                      chatPhoneCallTaskEntity.setContactId(req.getData());
                      MtCallCard card=getCard(CardStatusEnum.IN_PROGRESS);
                      card.setStartTime(String.valueOf(chatPhoneCallTaskEntity.getStartTime().getTime()));
                      card.setCallTime(chatPhoneCallTaskEntity.getCallCount());
                      card.setStatus(CardStatusEnum.IN_PROGRESS.getCode());
                      card.setTaskId(chatPhoneCallTaskEntity.getId());
                      boolean flag=phoneCallRepository.updateTask(chatPhoneCallTaskEntity);
                      if (!flag) {
                          log.warn("更新任务失败 entity:{}", chatPhoneCallTaskEntity.toString());
                      }
                     flag=chatMessageRepository.updateMessageContentByUserAndConversation(chatMessageEntity.getConversationId(), chatPhoneCallTaskEntity.getMessageId(),JSON.toJSONString(buildContext(card.toString(),chatPhoneCallTaskEntity.getMessageId())));
                      if (!flag){
                          log.warn("消息更新任务失败 conversationId：{},messageId:{},card:{}",chatMessageEntity.getConversationId(), chatMessageEntity.getMessageId(),card.toString());
                      }
                      return;
                  }
              }
            }
            generateFailInfo(chatMessageEntity,chatPhoneCallTaskEntity, releaseReason);
        }catch (Exception e){
            transaction.setStatus(e);
            log.error("getChatSummaryAndRetry.fail,dialog={},contactId={}",dialog,contactId , e);
        }

    }



    public void generateFailInfo (ChatMessageEntity chatMessageEntity,ChatPhoneCallTaskEntity chatPhoneCallTaskEntity,String dialog) throws Exception{
        JSONObject jsonDialog = new JSONObject();
        jsonDialog.put("dialog", dialog);
        MtCallCard card=getCard(CardStatusEnum.FAILED);
        card.setSubTitle(card.getSubTitle()+chatPhoneCallTaskEntity.getQuestion());
        card.setStatus(CardStatusEnum.FAILED.getCode());
        chatPhoneCallTaskEntity.setEndTime(new Date());
        chatPhoneCallTaskEntity.setStatus(CardStatusEnum.FAILED.getCode());
        chatPhoneCallTaskEntity.setDialogRawData(jsonDialog.toJSONString());
        phoneCallRepository.updateTask(chatPhoneCallTaskEntity);
        chatMessageRepository.updateMessageContentByUserAndConversation(chatMessageEntity.getConversationId(), chatPhoneCallTaskEntity.getMessageId(),JSON.toJSONString(buildContext(card.toString(),chatPhoneCallTaskEntity.getMessageId())));
    }

    public String  getDialogDate(Long taskId){
        Transaction transaction = Cat.newTransaction(CAT_TYPE, "getDialogDate");
        transaction.setSuccessStatus();
        try {
            ChatPhoneCallTaskEntity task = phoneCallRepository.getTaskById(taskId);
            if (task == null || StringUtils.isBlank(task.getDialogRawData())) {
                return null;
            }
            JSONObject json = JSON.parseObject(task.getDialogRawData());
            return String.format("（电话接通）\n%s（电话结束）", json.getString("dialog"));
        }catch (Exception e){
            transaction.setStatus(e);
            log.error("getDialogDate.fail,taskId={}",taskId , e);
            return null;
        }finally {
            transaction.complete();
        }
    }

    //获取卡片基础信息
   private MtCallCard getCard(CardStatusEnum statusEnum) throws Exception {
       Map<String, Object> cardBaseInfo = Lion.getMap(MdpContextUtils.getAppKey(), LionEnum.AI_CALL_CARD_INFO.getKey(), Object.class);
       if(MapUtils.isEmpty(cardBaseInfo))
       {
           throw new Exception(String.format("获取Lion：%s失败", LionEnum.AI_CALL_CARD_INFO.getKey()));
       }
       return JsonUtil.fromJson(JSON.toJSONString(cardBaseInfo.get(String.valueOf(statusEnum.getCode()))), MtCallCard.class);
   }

   //检查入参
    private void checkMtPhoneCallContextParam(MtPhoneCallContext mtPhoneCallContext) {
        if (mtPhoneCallContext==null||mtPhoneCallContext.getSessionId()==null||StringUtils.isBlank(mtPhoneCallContext.getQuestion())||mtPhoneCallContext.getShopId()==null
                ||StringUtils.isBlank(mtPhoneCallContext.getUserId())||mtPhoneCallContext.getShopId()<=0
                ||mtPhoneCallContext.getSessionId()<=0||mtPhoneCallContext.getQuestion().length()>=200)
        {
            throw new IllegalArgumentException("生成任务入参异常");
        }
    }

    //话术模版匹配
    public String callTemplateMatch(String question) {
        Transaction transaction = Cat.newTransaction(CAT_TYPE, "callTemplateMatch");
        transaction.setSuccessStatus();
        try {
            if(StringUtils.isBlank(question))
            {
                throw new Exception("话术模版匹配入参异常");
            }
            Map<String,String> type_map=Lion.getMap(MdpContextUtils.getAppKey(),LionEnum.AI_CALL_PROMPT_MATCH.getKey(),String.class);
            String callTemplateMatchPrompt = callClassifyTemplate.replace("${question}", question)
                    .replace("${type_map}", type_map.toString());
            ChatClient.CallResponseSpec call = callTemplateMatchClientBuilder.build().prompt().user(callTemplateMatchPrompt).call();
            return call.chatResponse().getResult().getOutput().getText();
        }catch (Exception e){
            transaction.setStatus(e);
            return "";
        }finally {
            transaction.complete();
        }
    }

    public String phoneCallByHaiMa(String mtShopId) {
        Map<String, String> map = new HashMap<>();
        map.put("mtShopId", mtShopId);
        List<HaimaContent> haimaContents=haimaAcl.getContent("ai_hospital_call",map);
        if(org.apache.commons.collections4.CollectionUtils.isEmpty(haimaContents))
        {
            return null;
        }
        return haimaContents.get(0).getContentString("callPhone");
    }

    public ChatSummaryResult getChatSummary(ChatSummaryContext chatSummaryContext) {

        ChatSummaryRequest request = buildChatSummaryRequest(chatSummaryContext);
        return summaryFactory.getSummaryResult(request);
    }

    private ChatSummaryRequest buildChatSummaryRequest(ChatSummaryContext chatSummaryContext) {
        ChatSummaryRequest request = new ChatSummaryRequest();
        BeanUtils.copyProperties(chatSummaryContext, request);
        return request;
    }

    public StreamEventDTO buildContext(String msgContent,Long messageId) throws Exception {
        ChatMessageEntity chatMessageEntity= chatMessageRepository.findByMessageId(messageId);
        if(chatMessageEntity==null)
        {
            throw new IllegalStateException("没有找到对应消息内容");
        }
        String content=chatMessageEntity.getContent();
        StreamEventDTO eventDTO =JSON.parseObject(content,StreamEventDTO.class);
        List<StreamEventCardDataDTO> cardsData=eventDTO.getData().getCardsData();
        for (StreamEventCardDataDTO card : cardsData) {
            if ("TelephoneConsult".equals(card.getKey())) {
                Map<String, Object> cardProps = new HashMap<>();
                CallCardDTO callCardDTO = JSON.parseObject(msgContent, CallCardDTO.class);
                cardProps.put("callCardDTO", callCardDTO);
                card.setCardProps(cardProps);
                break;
            }
        }
        return eventDTO;
    }


    @Override
    public void afterPropertiesSet() throws Exception {
        refreshPrompt();
        appkitClient.addSceneListener("ai_hospital_instruction", new SceneListener() {
            @Override
            public void onChanged(ChangeEvent changeEvent) {
                Cat.logEvent("prompt_change", changeEvent.getNewPkgId() + "-" + changeEvent.getOldPkgId());
                refreshPrompt();
            }
        });

    }

    private void refreshPrompt() {
        List<HaimaContent> haimaContents = haimaAcl.getContent("ai_hospital_instruction", null);
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(haimaContents)) {
            Cat.logEvent("MtPhoneCallService.refreshPrompt", "empty");
            return;
        }
        Cat.logEvent("MtPhoneCallService.refreshPrompt", "success");
         promptMap = haimaContents.stream().collect(toMap(
                e -> e.getContentString("promptKey"),
                e -> e.getContentString("promptContent"),
                (a, b) -> a
        ));
        callClassifyTemplate = promptMap.getOrDefault("tel_template_match", "");
        telContentSummaryTemplate =  promptMap.getOrDefault("tel_content_summary", "");

    }

}