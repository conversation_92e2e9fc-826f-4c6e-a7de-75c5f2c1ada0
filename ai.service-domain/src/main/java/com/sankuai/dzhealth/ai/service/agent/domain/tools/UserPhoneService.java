package com.sankuai.dzhealth.ai.service.agent.domain.tools;

import com.sankuai.wpt.user.retrieve.thrift.message.RpcUserRetrieveService;
import com.sankuai.wpt.user.retrieve.thrift.message.UserFields;
import com.sankuai.wpt.user.retrieve.thrift.message.UserModel;
import com.sankuai.wpt.user.retrieve.thrift.message.UserRespMsg;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 用户手机号服务
 *
 * @author: jiyizhou
 * @time: 2025/7/9 15:40
 * @version: 0.0.1
 */
@Slf4j
@Service
public class UserPhoneService {

    @Autowired
    private RpcUserRetrieveService.Iface rpcUserRetrieveService;

    /**
     * 根据用户ID获取用户手机号
     *
     * @param userId 用户ID
     * @return 用户手机号，查询失败或手机号为空时返回null
     */
    public String getUserPhoneById(Long userId) {
        try {
            if (userId == null || userId <= 0) {
                log.info("getUserPhoneById.paramError, userId={}", userId);
                return null;
            }

            // 构建UserFields，只查询手机号字段
            UserFields userFields = new UserFields();
            userFields.setMobile(true);

            log.info("getUserPhoneById.start, userId={}", userId);

            // 调用RPC服务
            UserRespMsg userRespMsg = rpcUserRetrieveService.getUserByIdWithMsg(userId, userFields);

            if (userRespMsg == null || !userRespMsg.isSuccess()) {
                log.info("getUserPhoneById.error, userId={}", userId);
                return null;
            }

            UserModel user = userRespMsg.getUser();
            if (user == null) {
                log.info("getUserPhoneById.userNotFound, userId={}", userId);
                return null;
            }

            String mobile = user.getMobile();
            if (StringUtils.isBlank(mobile)) {
                log.info("getUserPhoneById.mobileEmpty, userId={}", userId);
                return null;
            }

            return processPhoneNumber(mobile);

        } catch (Exception e) {
            log.error("getUserPhoneById.exception, userId={}", userId, e);
            return null;
        }
    }

    /**
     * 处理手机号格式
     * 根据文档：{国际码}_{手机号}，如果是中国手机号（86），则手机号格式为{手机号}
     *
     * @param phoneNumber 原始手机号
     * @return 处理后的手机号
     */
    private String processPhoneNumber(String phoneNumber) {

        // 如果包含下划线，说明是国际格式：{国际码}_{手机号}
        if (phoneNumber.contains("_")) {
            String[] parts = phoneNumber.split("_", 2);
            if (parts.length == 2 && "86".equals(parts[0])) {
                // 中国手机号，返回纯手机号
                return parts[1];
            }
            // 其他国家手机号，保持原格式
            return phoneNumber;
        }

        // 无下划线，直接返回（可能是中国手机号的简化格式）
        return phoneNumber;
    }
}

