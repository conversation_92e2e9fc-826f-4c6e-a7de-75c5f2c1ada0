package com.sankuai.dzhealth.ai.service.domain.ability.llm;

import java.util.Map;

/**
 * An interface for items that can be filtered by the LLMResultFilterService.
 */
public interface FilterableItem {

    /**
     * Gets the fields and their values to be presented to the LLM for evaluation.
     * The map keys are the field names (e.g., "标题", "内容") and the values are the field content.
     *
     * @return A map of fields for evaluation.
     */
    Map<String, String> getFieldsForEvaluation();
} 