package com.sankuai.dzhealth.ai.service.domain.utils.context;


import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.*;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AiCallMessageContext implements Serializable {
    private String appCallUuid;

    private String contactId;

    private Integer contactType;

    private String displayNum;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "GMT+8")
    private Date endTime;

    private List<String> keyPressList;

    private String oriDnis;

    private String rank;

    private String releaseReason;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "GMT+8")
    private Date ringStartTime;

    private Long ringTimeLen;

    private String routePoint;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "GMT+8")
    private Date startTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "GMT+8")
    private Date talkingStartTime;

    private String template;

    private String tenantId;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "GMT+8")
    private Date timePiece;

    private String trunkPrefix;
}
