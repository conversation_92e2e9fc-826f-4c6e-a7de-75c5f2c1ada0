package com.sankuai.dzhealth.ai.service.agent.domain.tools;

import com.alibaba.fastjson.JSON;
import com.sankuai.dzhealth.ai.service.agent.domain.tools.param.MedicalBeautyQueryRequest;
import com.sankuai.dzhealth.ai.service.api.ESVectorStoreService;
import com.sankuai.dzhealth.ai.service.api.RemoteResponse;
import com.sankuai.dzhealth.ai.service.dto.DocumentDTO;
import com.sankuai.dzhealth.ai.service.request.DocumentSearchRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.ai.tool.annotation.Tool;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * 查询医美百科避雷针的工具：根据搜索条件查询ES
 */
@Component
@Slf4j
public class MedicalBeautyQueryTool {
    @Autowired
    private ESVectorStoreService esVectorStoreService;

    /**
     * 查询医美百科避雷针的工具：根据搜索条件查询ES
     * @param request 查询请求
     * @return ES文档列表
     */
    @Tool(name = "queryMedicalBeauty", description = "医美百科与避雷针查询工具：用于检索医美项目的专业百科信息及避雷指南。")
    public List<DocumentDTO> queryMedicalBeauty(MedicalBeautyQueryRequest request) {
        log.info("MedicalBeautyQueryTool query request:{}", JSON.toJSONString(request));
        if(request == null || StringUtils.isBlank(request.getQuery())) {
            log.error("MedicalBeautyQueryTool query param error, query request:{}", JSON.toJSONString(request));
            return null;
        }

        int topK = request.getTopK() == null ? 10 : request.getTopK();

        Map<String, List<String>> metaData = Map.of("channel", List.of("medicalBeautyEncyclopedia", "medicalBeautyLightningRod","wordSelectionEncyclopedia"));
        // 将MedicalBeautyQueryRequest转换为DocumentSearchRequest
        DocumentSearchRequest documentSearchRequest = DocumentSearchRequest.builder()
                .query(request.getQuery())
                .topK(topK)
                .metaData(metaData)
                .build();

        RemoteResponse<List<DocumentDTO>> response = esVectorStoreService.similaritySearch(documentSearchRequest);

        if(response == null || !response.getSuccess()) {
            log.error("MedicalBeautyQueryTool query ES error, query request:{}, response:{}", JSON.toJSONString(request), JSON.toJSONString(response));
            return null;
        }

        log.info("MedicalBeautyQueryTool query response:{}, request: {}", JSON.toJSONString(response), request);
        return response.getData();
    }
}
