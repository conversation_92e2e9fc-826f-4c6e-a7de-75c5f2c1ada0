package com.sankuai.dzhealth.ai.service.agent.domain.service.experiencereport;

import com.sankuai.beautycontent.experience.dto.ExperienceReportVO;
import com.sankuai.beautycontent.experience.request.ExperienceReportRequest;
import com.sankuai.dzhealth.ai.service.agent.domain.model.experiencereport.AiCaseInfo;
import com.sankuai.dzhealth.ai.service.agent.domain.model.experiencereport.AiExperienceReports;
import com.sankuai.dzhealth.ai.service.agent.domain.model.experiencereport.ExperienceReportModel;
import com.sankuai.dzhealth.ai.service.agent.domain.model.experiencereport.ExperienceReportRequestModel;
import com.sankuai.dzhealth.ai.service.agent.domain.utils.ExperienceReportConverter;
import com.sankuai.dzhealth.ai.service.agent.infrastructure.proxy.experiencereport.ExperienceReportProxy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 体验报告领域服务
 * <p>
 * 负责体验报告相关的业务逻辑处理，调用基础设施层代理并进行领域模型转换
 *
 * <AUTHOR>
 * @version 0.0.1
 */
@Slf4j
@Service
public class ExperienceReportDomainService {

    @Autowired
    private ExperienceReportProxy experienceReportProxy;

    /**
     * 查询体验报告概要信息
     * <p>
     * 该方法接收领域模型作为参数，调用基础设施层代理，并将结果转换为领域模型返回
     *
     * @param requestModel 体验报告查询请求领域模型
     * @return 体验报告领域模型；若调用失败或数据为空，则返回 null
     */
    private ExperienceReportModel getExperienceReport(ExperienceReportRequestModel requestModel) {
        log.info("ExperienceReportDomainService.getExperienceReport 开始处理请求, requestModel={}", requestModel);

        if (requestModel == null) {
            log.warn("ExperienceReportDomainService.getExperienceReport 请求参数为空");
            return null;
        }

        try {
            // 1. 将领域请求模型转换为基础设施层请求 DTO
            ExperienceReportRequest infraRequest = ExperienceReportConverter.toInfrastructureRequest(requestModel);
            log.debug("ExperienceReportDomainService.getExperienceReport 转换请求参数完成, infraRequest={}", infraRequest);

            // 2. 调用基础设施层代理（代理已处理响应状态，直接返回业务数据）
            ExperienceReportVO infraData = experienceReportProxy.experienceReport(infraRequest);
            log.debug("ExperienceReportDomainService.getExperienceReport 调用基础设施层代理完成, infraData={}", infraData);

            if (infraData == null) {
                log.warn("ExperienceReportDomainService.getExperienceReport 基础设施层返回为空");
                return null;
            }

            // 3. 将基础设施层数据转换为领域模型
            ExperienceReportModel domainModel = ExperienceReportConverter.toExperienceReportModel(infraData);
            log.info("ExperienceReportDomainService.getExperienceReport 处理完成, domainModel={}", domainModel);

            return domainModel;

        } catch (Exception e) {
            log.error("ExperienceReportDomainService.getExperienceReport 处理异常, requestModel={}", requestModel, e);
            return null;
        }
    }

    /**
     * 获取AI助手完整体验报告信息
     * <p>
     * 包含体验报告列表、总数、微详情链接等完整展示信息，用于前端完整渲染体验报告模块
     *
     * @param requestModel 体验报告查询请求领域模型
     * @return AI体验报告列表信息；若调用失败或数据为空，则返回空对象
     */
    public AiExperienceReports getAiExperienceReportListInfo(ExperienceReportRequestModel requestModel) {
        log.info("ExperienceReportDomainService.getAiExperienceReportListInfo 开始获取完整体验报告信息, requestModel={}", requestModel);

        if (requestModel == null) {
            log.warn("ExperienceReportDomainService.getAiExperienceReportListInfo 请求参数为空");
            return AiExperienceReports.builder().build();
        }

        try {
            // 1. 调用体验报告服务获取原始数据
            ExperienceReportModel reportModel = getExperienceReport(requestModel);

            if (reportModel == null) {
                log.info("ExperienceReportDomainService.getAiExperienceReportListInfo 体验报告数据为空");
                return AiExperienceReports.builder().build();
            }

            // 2. 转换体验报告列表
            List<AiCaseInfo> aiCaseList = new ArrayList<>();
            if (!CollectionUtils.isEmpty(reportModel.getPicList())) {
                aiCaseList = ExperienceReportConverter.transformPicListToCaseList(
                        reportModel.getPicList()
                );
            }

            // 3. 构建完整体验报告信息
            AiExperienceReports caseListInfo = AiExperienceReports.builder()
                    .title("体验报告") // "AI推荐体验报告"
                    .totalCount(reportModel.getReportNum()) // 总体验报告数：4
                    .detailUrl(reportModel.getDetailUrl()) // 微详情链接
                    .caseList(aiCaseList) // 体验报告列表（前N个）
                    .build();

            log.info("ExperienceReportDomainService.getAiExperienceReportListInfo 处理完成, 标题={}, 总数={}, 体验报告数={}",
                    caseListInfo.getTitle(), caseListInfo.getTotalCount(), aiCaseList.size());

            return caseListInfo;

        } catch (Exception e) {
            log.error("ExperienceReportDomainService.getAiExperienceReportListInfo 处理异常, requestModel={}", requestModel, e);
            return AiExperienceReports.builder().build();
        }
    }


}

