package com.sankuai.dzhealth.ai.service.agent.domain.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 聊天会话消息领域实体
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ChatSessionMessageEntity {

    /**
     * 主键
     */
    private Long id;

    /**
     * 消息ID
     */
    private String messageId;

    /**
     * 会话ID
     */
    private String sessionId;

    /**
     * 角色（user、assistant、system等）
     */
    private String role;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 平台
     */
    private Integer platform;

    /**
     * 内容类型，1-文本
     */
    private Integer type;

    /**
     * 审核状态，0-通过，1-审核不通过，2-删除，3-审核中
     */
    private Integer status;

    /**
     * 消息点赞取消状态，0-无操作，1-点赞，2-点踩
     */
    private Integer feedback;

    /**
     * 消息内容
     */
    private String content;

    /**
     * 记忆快照
     */
    private String memorySnapshot;

    /**
     * 扩展数据
     */
    private String extra;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    // 业务常量定义
    public static class Role {
        public static final String USER = "user";
        public static final String ASSISTANT = "assistant";
        public static final String SYSTEM = "system";
    }

    public static class Type {
        public static final Integer TEXT = 1;
    }

    public static class Status {
        public static final Integer APPROVED = 0;
        public static final Integer REJECTED = 1;
        public static final Integer DELETED = 2;
        public static final Integer REVIEWING = 3;
    }

    public static class Feedback {
        public static final Integer NONE = 0;
        public static final Integer LIKE = 1;
        public static final Integer DISLIKE = 2;
    }

    // 业务方法

    /**
     * 判断是否为用户消息
     */
    public boolean isUserMessage() {
        return Role.USER.equals(this.role);
    }

    /**
     * 判断是否为助手消息
     */
    public boolean isAssistantMessage() {
        return Role.ASSISTANT.equals(this.role);
    }

    /**
     * 判断是否为系统消息
     */
    public boolean isSystemMessage() {
        return Role.SYSTEM.equals(this.role);
    }

    /**
     * 判断消息是否已通过审核
     */
    public boolean isApproved() {
        return Status.APPROVED.equals(this.status);
    }

    /**
     * 判断消息是否被删除
     */
    public boolean isDeleted() {
        return Status.DELETED.equals(this.status);
    }

    /**
     * 判断消息是否在审核中
     */
    public boolean isReviewing() {
        return Status.REVIEWING.equals(this.status);
    }

    /**
     * 判断消息是否被点赞
     */
    public boolean isLiked() {
        return Feedback.LIKE.equals(this.feedback);
    }

    /**
     * 判断消息是否被点踩
     */
    public boolean isDisliked() {
        return Feedback.DISLIKE.equals(this.feedback);
    }

    /**
     * 设置点赞状态
     */
    public void setLike() {
        this.feedback = Feedback.LIKE;
        this.updateTime = new Date();
    }

    /**
     * 设置点踩状态
     */
    public void setDislike() {
        this.feedback = Feedback.DISLIKE;
        this.updateTime = new Date();
    }

    /**
     * 取消点赞/点踩
     */
    public void cancelFeedback() {
        this.feedback = Feedback.NONE;
        this.updateTime = new Date();
    }

    /**
     * 标记为已删除
     */
    public void markAsDeleted() {
        this.status = Status.DELETED;
        this.updateTime = new Date();
    }

    /**
     * 审核通过
     */
    public void approve() {
        this.status = Status.APPROVED;
        this.updateTime = new Date();
    }

    /**
     * 审核拒绝
     */
    public void reject() {
        this.status = Status.REJECTED;
        this.updateTime = new Date();
    }

    /**
     * 验证实体的有效性
     */
    public boolean isValid() {
        return messageId != null && !messageId.trim().isEmpty()
                && sessionId != null && !sessionId.trim().isEmpty()
                && role != null && !role.trim().isEmpty()
                && userId != null && userId > 0
                && type != null
                && status != null;
    }

}
