package com.sankuai.dzhealth.ai.service.agent.domain.model.product;

import lombok.Builder;
import lombok.Data;

/**
 * 商品基本信息 - Value Object
 * 包含商品的核心基础字段
 */
@Data
@Builder
public class ProductBasic {

    /**
     * 点评商品ID
     */
    private Long dpProductId;

    /**
     * 美团商品ID
     */
    private Long mtProductId;

    /**
     * 泛商品业务ID（仅泛商品体系有值）
     */
    private Long bizProductId;

    /**
     * 商品标题
     */
    private String title;

    /**
     * 商品状态
     * @see com.sankuai.general.product.query.center.client.enums.DealGroupStatusEnum
     */
    private Integer status;

    /**
     * 开始售卖时间
     */
    private String beginSaleDate;

    /**
     * 结束售卖时间
     */
    private String endSaleDate;

    /**
     * 交易形态
     * @see com.sankuai.general.product.query.center.client.enums.TradeTypeEnum
     */
    private Integer tradeType;

    /**
     * 品牌名称
     */
    private String brandName;

    /**
     * 创建时间
     */
    private String addTime;

    /**
     * 修改时间
     */
    private String updateTime;
}

