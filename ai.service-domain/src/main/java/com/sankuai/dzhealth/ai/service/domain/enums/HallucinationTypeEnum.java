package com.sankuai.dzhealth.ai.service.domain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @author: yangwei<PERSON>
 * @date: 2025/4/14 16:56
 * @version: 1.0
 */
@Getter
@AllArgsConstructor
public enum HallucinationTypeEnum {
    DEFAULT("", "缺省值"),
    NOT_EXIST("not-exist", "没有幻觉"),
    FACTUAL("factual", "事实性错误"),
    OFF_TOPIC("off-topic", "偏离主题"),
    OVER_EXTENSION("over-extension", "偏离主题"),
    MISINTERPRETATION("misinterpretation", "错误解释"),
    OTHER("other", "其他类型");

    private final String code;
    private final String desc;

    public static HallucinationTypeEnum fromCode(String code) {
        for (HallucinationTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return DEFAULT;
    }
}
