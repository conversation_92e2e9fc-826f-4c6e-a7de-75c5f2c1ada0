package com.sankuai.dzhealth.ai.service.domain.chat.model;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @author:chenwei
 * @time: 2025/3/25 13:01
 * @version: 0.0.1
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ChatSessionBO implements Serializable {

    @FieldDoc(description = "会话ID")
    private Long sessionId;

    @FieldDoc(description = "会话标题")
    private String title;

    @FieldDoc(description = "会话日期时间")
    private String dateTime;

    @FieldDoc(description = "明确的时间表示")
    private String explicitTime;
}
