package com.sankuai.dzhealth.ai.service.agent.domain.mafka;
import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.meituan.mafka.client.MafkaClient;
import com.meituan.mafka.client.consumer.ConsumerConstants;
import com.meituan.mafka.client.producer.*;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Service;

import java.util.Properties;

@Service
@Slf4j
public class AppointmentAiCancelProducer  implements InitializingBean {


    private static final String CAT_TYPE = AppointmentAiCancelProducer.class.getSimpleName();

    private static IProducerProcessor producer;

    /**
     * 发送消息
     */
    public void sendDelayMessage(String taskId,Long delayTime) {
        Transaction transaction = Cat.newTransaction(CAT_TYPE, "sendFusionInfoFailMessage");
        try {
            AsyncDelayProducerResult sendResult = producer.sendAsyncDelayMessage(taskId, delayTime, new IDelayFutureCallback() {
                @Override
                public void onSuccess(AsyncDelayProducerResult result) {
                    log.info("发送成功回调 " + result.getProducerStatus() + " " + result.getMessage());
                }
                @Override
                public void onFailure(AsyncDelayProducerResult result) {
                 log.info("发送失败回调 " + result.getProducerStatus() + " " + result.getMessage());
                }
            });
        }catch (Exception e){
            transaction.setStatus(e);
            Cat.logError(e);
        }finally {
            transaction.complete();
        }
    }


    @Override
    public void afterPropertiesSet() throws Exception {
        Properties properties = new Properties();
        properties.setProperty(ConsumerConstants.MafkaBGNamespace, "com.sankuai.mafka.castle.daojiacommon");
        properties.setProperty(ConsumerConstants.MafkaClientAppkey, "com.sankuai.dzhealth.ai.service");
        properties.setProperty(ConsumerConstants.MafkaDelayServerTimeOut, "2000");
        properties.setProperty(ConsumerConstants.MafkaDelayServerConnTimeout, "1000");
        producer = MafkaClient.buildDelayProduceFactory(properties, "ai_assistant_appointment");
    }
}

