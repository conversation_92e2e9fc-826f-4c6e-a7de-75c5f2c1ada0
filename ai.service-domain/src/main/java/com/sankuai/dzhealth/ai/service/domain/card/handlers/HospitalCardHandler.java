package com.sankuai.dzhealth.ai.service.domain.card.handlers;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import com.dianping.rhino.Rhino;
import com.dianping.rhino.threadpool.DefaultThreadPoolProperties;
import com.dianping.rhino.threadpool.ThreadPool;
import com.sankuai.dzhealth.ai.service.domain.card.AbstractCardHandler;
import com.sankuai.dzhealth.ai.service.domain.service.HospitalRecommendService;
import com.sankuai.dzhealth.ai.service.domain.utils.context.AiAnswerContext;
import com.sankuai.dzhealth.ai.service.dto.DepartmentCardDTO;
import com.sankuai.dzhealth.ai.service.dto.HospitalCardDTO;
import com.sankuai.dzhealth.ai.service.dto.TagItemDTO;
import com.sankuai.dzhealth.ai.service.dto.stream.StreamEventDTO;
import com.sankuai.dzhealth.ai.service.enums.StreamEventCardTypeEnum;
import com.sankuai.dzhealth.ai.service.infrastructure.observability.Span;
import com.sankuai.dzhealth.medical.client.biz.channelpagelist.dto.*;
import com.sankuai.dzhealth.medical.client.biz.channelpagelist.service.ChannelPageListService;
import com.sankuai.dzhealth.medical.client.common.dto.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 医院推荐卡片处理器 - 专门处理医院推荐信息
 */
@Slf4j
@Component
@Order(5)
public class HospitalCardHandler extends AbstractCardHandler {

    @Autowired
    private ChannelPageListService channelPageListService;


    @Autowired
    private HospitalRecommendService hospitalRecommendService;
    
    private static final String FILTER_PARAM = "{\"scene\":{\"addZdcTagIds\":\"21514\",\"tabName\":\"附近医院\"},\"twoStructure\":{\"tabName\":\"%s\"},\"order\":{\"sortType\":\"AI\",\"tabName\":\"综合排序\"}}";
    
    // 定义线程池，用于异步处理医院推荐查询
    private static final ThreadPool HOSPITAL_TASK_POOL = Rhino.newThreadPool("HOSPITAL_TASK_POOL",
            DefaultThreadPoolProperties.Setter().withCoreSize(100).withMaxSize(200).withMaxQueueSize(1000));
    
    @Override
    public boolean supports(String fieldName) {
        return "nearbyDepartment".equals(fieldName);
    }
    
    @Override
    public List<StreamEventDTO> handle(JSONObject jsonObject, 
                                    AiAnswerContext context, 
                                    SseEmitter sseEmitter, 
                                    AtomicInteger sseIndex,
                                    long startChatTime) {

        // 从JSON中提取标准IDs
        List<Long> stdIds = Optional.ofNullable(jsonObject.getJSONArray("nearbyDepartment"))
                .map(array -> array.toJavaList(String.class))
                .orElse(Collections.emptyList())
                .stream()
                .filter(name -> context.getStdDepartName2IdMap() != null)
                .map(name -> context.getStdDepartName2IdMap().get(name))
                .filter(Objects::nonNull)
                .map(Integer::longValue)
                .collect(Collectors.toList());
        context.addSpan(Span.builder().key(getClass().getSimpleName()+"/stdIds").value(JSON.toJSONString(stdIds)).build());
        log.info("extract_hospitalIds from stdIds={}", stdIds);
        
        // 如果没有标准IDs，直接返回空列表
        if (CollectionUtils.isEmpty(stdIds)) {
            return Collections.emptyList();
        }
        
        List<StreamEventDTO> events = new ArrayList<>();
        Map<String, Object> cardProps = new HashMap<>();
        
        try {
            // 创建异步任务列表
            List<CompletableFuture<List<HospitalCardDTO>>> futures = stdIds.stream()
                .map(stdId -> CompletableFuture.supplyAsync(() -> {
                    try {
                        return queryHospitalCards(stdId, context);
                    } catch (Exception e) {
                        log.error("Async queryHospital error! standardId={}", stdId, e);
                        return Collections.<HospitalCardDTO>emptyList();
                    }
                }, HOSPITAL_TASK_POOL.getExecutor()))
                .collect(Collectors.toList());
            
            // 等待所有异步任务完成
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
            
            // 合并所有结果
            List<HospitalCardDTO> allHospitalCards = futures.stream()
                .map(CompletableFuture::join)
                .filter(CollectionUtils::isNotEmpty)
                .flatMap(Collection::stream)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

            log.info("allHospitalCards={}", JSON.toJSONString(allHospitalCards));
            long start = System.currentTimeMillis();
            List<HospitalCardDTO> recommend =
                    hospitalRecommendService.recommend(context, context.getContent(), allHospitalCards);
            recommend.forEach(e -> {
                if (CollectionUtils.isNotEmpty(e.getDepartmentCardDTOList())) {
                    DepartmentCardDTO departmentCardDTO = e.getDepartmentCardDTOList().get(0);
                    e.setDepartmentName(departmentCardDTO.getDepartmentName());
                    e.setDepartDetailUrl(departmentCardDTO.getDepartDetailUrl());
                    e.setRegisterCanTime(departmentCardDTO.getRegisterCanTime());
                    e.setRegisterStatus(departmentCardDTO.getRegisterStatus());
                    e.setButtonText(departmentCardDTO.getButtonText());
                }
            });
            context.addSpan(Span.builder()
                    .key(getClass().getSimpleName() + "/recommend")
                    .value(JSON.toJSONString(recommend))
                    .duration(System.currentTimeMillis() - start)
                    .build());

            // 处理合并后的结果
            if (CollectionUtils.isNotEmpty(recommend)) {
                cardProps.put("list", recommend);
                cardProps.put("channelUrl", recommend.get(0).getMoreJumpUrl());
                StreamEventDTO hospitalEvent = sendCardEvent(
                        StreamEventCardTypeEnum.HOSPITAL_CARD,
                        "hospitalCard", 
                        cardProps, 
                        sseEmitter, 
                        sseIndex.getAndIncrement()
                );
                addIfNotNull(events, hospitalEvent);
            }
        } catch (Exception e) {
            log.error("Process hospital card error! standardIds={} ", stdIds, e);
        }
        
        return events;
    }
    
    /**
     * 根据标准ID查询医院卡片数据
     * 
     * @param stdId 标准ID
     * @param context 上下文对象
     * @return 医院卡片DTO列表
     */
    private List<HospitalCardDTO> queryHospitalCards(Long stdId, AiAnswerContext context) {
        List<HospitalCardDTO> hospitalCards = new ArrayList<>();
        Integer nearbySize = Lion.getInt(Environment.getAppName(), "nearby.pagesize", 8);
        try {
            ChannelPageListRequest request = new ChannelPageListRequest();
            request.setPlatform(context.getPlatform());
            request.setAppVersion(context.getAppVersion());
            request.setBizCode("public.ai.navigation.find.hospital");
            request.setCityId(context.getCityId());
            request.setLat(context.getLat());
            request.setLng(context.getLng());
            request.setLocationAuthorized(context.getLocationAuthorized());
            request.setUserCityId(context.getUserCityId());
            request.setNativeClient(true);
            request.setPageNo(1);
            request.setPageSize(nearbySize);
            request.setPageSource("public.ai.navigation.find.hospital.page");
            request.setListCallParamJson(String.format(FILTER_PARAM, stdId));
            context.addSpan(Span.builder()
                    .key(getClass().getSimpleName() + "/request")
                    .value(JSON.toJSONString(request))
                    .build());
            Response<ChannelPageListResp> channelPageList = channelPageListService.getChannelPageList(request);
            log.info("Async ChannelPageListRequest_req for stdId={}, req={}, res={}", stdId, JSON.toJSONString(request), JSON.toJSONString(channelPageList));
            
            if (!channelPageList.respSuccess() || channelPageList.getData() == null) {
                return hospitalCards;
            }
            
            ChannelPageListResp resp = channelPageList.getData();
            List<RecomCardListModel> recomCardListModels = resp.getRecomCardListModels();

            if (CollectionUtils.isEmpty(recomCardListModels)) {
                return hospitalCards;
            }
            
            // 处理医院推荐卡片
            for (RecomCardListModel model : recomCardListModels) {
                if (model == null) {
                    continue;
                }
                
                ShopBaseInfoModule shopBaseInfo = model.getShopBaseInfo();
                if (shopBaseInfo == null) {
                    continue;
                }
                
                List<CardSummary> cardSummarys = model.getCardSummarys();
                if (CollectionUtils.isEmpty(cardSummarys)) {
                    // 没有科室信息，跳过当前医院
                    continue;
                }
                HospitalCardDTO hospitalCard = buildHospitalBaseInfo(shopBaseInfo);

                List<DepartmentCardDTO> departmentCardDTOS = cardSummarys.stream().map(e -> {
                    DepartmentCardDTO dto = new DepartmentCardDTO();
                    dto.setDepartmentName(e.getTitle());
                    dto.setRegisterStatus((e.getSummaryActionButton() != null && "立即挂号".equals(e.getSummaryActionButton().getText())) ? 1 : 0);
                    dto.setRegisterCanTime(e.getTag());
                    if (e.getSummaryActionButton() != null) {
                        dto.setButtonText(e.getSummaryActionButton().getText());
                        dto.setDepartDetailUrl(e.getSummaryActionButton().getJumpUrl());
                    }
                    return dto;
                }).collect(Collectors.toList());
                hospitalCard.setDepartmentCardDTOList(departmentCardDTOS);
                hospitalCard.setMoreJumpUrl(resp.getMoreJumpUrl());
                hospitalCards.add(hospitalCard);
            }
            log.info("hospitalCards={}", JSON.toJSONString(hospitalCards));
        } catch (Exception e) {
            log.error("queryHospital for single stdId error! standardId={}", stdId, e);
        }
        return hospitalCards;
    }
    
    /**
     * 构建医院基本信息
     * 
     * @param shopBaseInfo 商户基本信息模块
     * @return 医院卡片DTO
     */
    private HospitalCardDTO buildHospitalBaseInfo(ShopBaseInfoModule shopBaseInfo) {
        HospitalCardDTO hospitalCard = new HospitalCardDTO();
        hospitalCard.setHospitalId(Long.valueOf(shopBaseInfo.getShopIdLong()));
        hospitalCard.setHospitalName(shopBaseInfo.getShopName());
        hospitalCard.setPoiUrl(shopBaseInfo.getShopUrl());
        hospitalCard.setAddress(shopBaseInfo.getRegionName());
        hospitalCard.setDistance(shopBaseInfo.getDistance());
        hospitalCard.setHospitalCategory(shopBaseInfo.getCategoryName());
        hospitalCard.setHospitalTags(buildTags(shopBaseInfo.getPoiTags()));
        return hospitalCard;
    }

    private List<TagItemDTO> buildTags(List<PoiTagModel> poiTags) {
        if (CollectionUtils.isEmpty(poiTags)) {
            return Collections.emptyList();
        }


        List<TagItemDTO> res = new ArrayList<>();
        Optional<PoiTagModel> firstTagOptional = poiTags.stream().filter(e -> e.getTagType() == 2).findFirst();
        if (firstTagOptional.isPresent()) {
            PoiTagModel firstPoiTag = firstTagOptional.get();
            TagItemDTO first = new TagItemDTO();
            first.setText(firstPoiTag.getTagValue());
            first.setIconUrl(firstPoiTag.getLeftIcon().getTagIconUrl());
            first.setHeight(firstPoiTag.getLeftIcon().getTagIconHeight());
            first.setWidth(firstPoiTag.getLeftIcon().getTagIconWidth());
            first.setUrl(firstPoiTag.getJumpUrl());
            first.setType(2);
            res.add(first);
        }
        poiTags.stream().filter(e -> e.getTagType() == 1).forEach(e -> {
            TagItemDTO tagItemDTO = new TagItemDTO();
            tagItemDTO.setText(e.getTagText().getTagText());
            tagItemDTO.setBgColor(e.getBgColor());
            tagItemDTO.setTextColor(e.getTagText().getTextColor());
            tagItemDTO.setType(1);
            res.add(tagItemDTO);
        });

        return res;
    }

    @Override
    public int getOrder() {
        return 5;
    }
} 