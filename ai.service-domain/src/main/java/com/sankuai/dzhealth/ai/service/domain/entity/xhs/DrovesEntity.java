package com.sankuai.dzhealth.ai.service.domain.entity.xhs;

import com.sankuai.dzhealth.ai.service.domain.utils.JsonUtils;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.stream.Collectors;

@Data
@NoArgsConstructor
public class DrovesEntity {
    private List<String> values;

    public List<Pair> toPair() {
        return this.values.stream().distinct()
                .map(value -> new Pair("droves", value))
                .collect(Collectors.toList());
    }

    public String toJson() {
        return JsonUtils.toJsonString(this);
    }
}
