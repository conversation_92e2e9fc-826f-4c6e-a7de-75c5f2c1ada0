package com.sankuai.dzhealth.ai.service.agent.domain.mafka;

import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.dianping.lion.client.util.JsonUtils;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mchange.lang.LongUtils;
import com.meituan.mafka.client.MafkaClient;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mafka.client.consumer.ConsumerConstants;
import com.meituan.mafka.client.consumer.IConsumerProcessor;
import com.sankuai.dzhealth.ai.service.agent.domain.service.AppointmentService;
import com.sankuai.dzhealth.ai.service.infrastructure.mafka.MafkaShutDownHelper;
import com.sankuai.dzim.pilot.api.data.aiphonecall.AIPhoneCallBackDTO;
import com.sankuai.dzim.pilot.api.enums.aiphonecall.AIPhoneCallSceneTypeEnum;
import com.sankuai.dzim.pilot.api.enums.aiphonecall.AIPhoneCallSourceEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;
import java.util.Properties;

@Slf4j
@Component
public class AppointmentLeadMessageConsumer implements InitializingBean {



    @Autowired
    private AppointmentService appointmentService;

    private static final Integer APPOINTMENT_BIZID = 51034;

    private static final String CAT_TYPE = AppointmentLeadMessageConsumer.class.getSimpleName();

    @Override
    public void afterPropertiesSet() throws Exception {
        Properties properties = new Properties();
        properties.setProperty(ConsumerConstants.MafkaBGNamespace, "daozong");
        properties.setProperty(ConsumerConstants.MafkaClientAppkey, "com.sankuai.dzhealth.ai.service");
        properties.setProperty(ConsumerConstants.SubscribeGroup, "medical.appointment.lead");
        properties.setProperty(ConsumerConstants.MafkaDelayRetryCount, "3");
        IConsumerProcessor processor = MafkaClient.buildConsumerFactory(properties, "com.sankuai.leads.change");

        MafkaShutDownHelper.registerHook(processor);
        processor.recvMessageWithParallel(String.class, (message, context) -> {
            Transaction transaction = Cat.newTransaction(CAT_TYPE, "consume");
            String messageStr=String.valueOf(message.getBody());
            try{
                log.info("[AppointmentLeadMessageConsumer] 处理延迟消息: message:{}", messageStr);
                if (StringUtils.isBlank(messageStr)) {
                    log.info("[AppointmentLeadMessageConsumer] 处理延迟消息: messageStr is null");
                    return ConsumeStatus.CONSUME_SUCCESS;
                }

                // 使用 JsonNode 直接解析
                ObjectMapper mapper = new ObjectMapper();
                JsonNode rootNode = mapper.readTree(messageStr);
                String leadsOperateType = rootNode.path("leadsOperateType").asText();
                Long leadsId = rootNode.path("leadsId").asLong();
                String leadsDTOStr = rootNode.path("leadsDTO").asText();
                JsonNode leadsDTONode = mapper.readTree(leadsDTOStr);
                Integer bizSourceId = leadsDTONode.path("bizSourceId").asInt();

                log.info("leadsOperateType:{}, leadsId:{},bizSourceId:{}", leadsOperateType,leadsId,bizSourceId);
                if (StringUtils.isNotBlank(leadsOperateType)&& bizSourceId.equals(APPOINTMENT_BIZID)&&leadsOperateType.equals("2")) {
                    appointmentService.cancelSuccessAppointByMessage(leadsId);
                }
                return ConsumeStatus.CONSUME_SUCCESS;

            }catch (Exception e){
                Cat.logEvent("AppointmentAiCallMessageConsumer", "handle.exception", "1", e.getMessage());
                log.error("AppointmentAiCallMessageConsumer handle exception for message:{}, e:", messageStr, e);
                transaction.setStatus(e);
                return ConsumeStatus.RECONSUME_LATER;
            }finally {
                transaction.complete();
            }
        });

    }

}
