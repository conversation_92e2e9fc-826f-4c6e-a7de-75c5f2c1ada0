package com.sankuai.dzhealth.ai.service.agent.domain.model.decisionflow;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;
import com.sankuai.dzhealth.ai.service.agent.domain.model.SupplyRecommendModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * @author:chenwei
 * @time: 2025/7/2 14:52
 * @version: 0.0.1
 */

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DecisionTreeModel implements Serializable {

    /** 节点名称 */
    @SerializedName("策略名称")
    @Expose
    @JSONField(name = "策略名称")
    @JsonProperty("nodeName")
    private String nodeName;

    /** 判别文案 */
    @SerializedName("判别文案")
    @Expose
    @JSONField(name = "判别文案")
    @JsonProperty("assessmentText")
    private String assessmentText;

    /** 示例图 URL */
    @SerializedName("图片ID")
    @Expose
    @JSONField(name = "图片ID")
    @JsonProperty("assessmentImg")
    private String assessmentImg;

    /** 指导文案 */
    @SerializedName("指导文案")
    @Expose
    @JSONField(name = "指导文案")
    @JsonProperty("guidanceText")
    private String guidanceText;

    /** 是否推荐供给 */
    @SerializedName("是否推荐供给")
    @Expose
    @JSONField(name = "是否推荐供给")
    @JsonProperty("needSupply")
    private Boolean needSupply;

    /** 是否支持测脸诊断 */
    @SerializedName("是否支持测脸诊断")
    @Expose
    @JSONField(name = "是否支持测脸诊断")
    @JsonProperty("needFaceDiagnose")
    private Boolean needFaceDiagnose;

    @SerializedName("是否需要医生")
    @Expose
    @JSONField(name = "是否需要医生")
    @JsonProperty("needDoctor")
    private Boolean needDoctor;

    /** 供给 */
    @SerializedName("供给推荐资源")
    @Expose
    @JSONField(name = "供给推荐资源")
    @JsonProperty("supplyRecommendModel")
    private SupplyRecommendModel supplyRecommendModel;

    @SerializedName("下钻需求")
    @Expose
    @JSONField(name = "下钻需求")
    @JsonProperty("drillDownRequirements")
    private List<DecisionTreeModel> drillDownRequirements;

}
