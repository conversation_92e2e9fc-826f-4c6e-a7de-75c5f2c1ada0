package com.sankuai.dzhealth.ai.service.domain.evaluation.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.ai.chat.messages.MessageType;

/**
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @since : 2025/8/14 15:29
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class EvaluationMessage {

    private MessageType messageType;

    private String text;
}
