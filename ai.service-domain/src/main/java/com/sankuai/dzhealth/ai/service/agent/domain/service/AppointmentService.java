package com.sankuai.dzhealth.ai.service.agent.domain.service;


import com.alibaba.fastjson.JSON;
import com.dianping.haima.entity.haima.HaimaContent;
import com.dianping.lion.client.Lion;
import com.dianping.lion.client.util.StringUtils;
import com.dianping.rhino.Rhino;
import com.dianping.rhino.threadpool.DefaultThreadPoolProperties;
import com.dianping.rhino.threadpool.ThreadPool;
import com.dianping.squirrel.client.StoreKey;
import com.dianping.squirrel.client.impl.redis.RedisStoreClient;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.meituan.mdp.boot.starter.MdpContextUtils;
import com.meituan.mtrace.scene.util.JsonUtil;
import com.sankuai.clr.content.core.common.enums.ContentProjectTypeEnum;
import com.sankuai.dzhealth.ai.service.agent.domain.config.RecommendConfig;
import com.sankuai.dzhealth.ai.service.agent.domain.context.MessageContext;
import com.sankuai.dzhealth.ai.service.agent.domain.entity.ChatSessionMessageEntity;
import com.sankuai.dzhealth.ai.service.agent.domain.enums.AppointmentProjectEnum;
import com.sankuai.dzhealth.ai.service.agent.domain.enums.AppointmentReturnTypeEnum;
import com.sankuai.dzhealth.ai.service.agent.domain.enums.CatchAllTxtEnum;
import com.sankuai.dzhealth.ai.service.agent.domain.mafka.AppointmentAiCancelProducer;
import com.sankuai.dzhealth.ai.service.agent.domain.model.AppointmentContext;
import com.sankuai.dzhealth.ai.service.agent.domain.model.AppointmentReturn;
import com.sankuai.dzhealth.ai.service.agent.domain.model.BusinessTimeInfo;
import com.sankuai.dzhealth.ai.service.agent.domain.process.card.data.AppointmentCardData;
import com.sankuai.dzhealth.ai.service.agent.domain.process.card.data.ProductCardData;
import com.sankuai.dzhealth.ai.service.agent.domain.process.task.TaskConfig;
import com.sankuai.dzhealth.ai.service.agent.domain.repository.chatSession.ChatSessionMessageRepository;
import com.sankuai.dzhealth.ai.service.agent.dto.AppointmentRequestDTO;
import com.sankuai.dzhealth.ai.service.agent.infrastructure.enums.AppointmentStatusEnum;
import com.sankuai.dzhealth.ai.service.agent.infrastructure.model.AppointmentInfoBO;
import com.sankuai.dzhealth.ai.service.agent.infrastructure.repository.appointment.AppointmentInfoRepository;
import com.sankuai.dzhealth.ai.service.domain.enums.LionEnum;
import com.sankuai.dzhealth.ai.service.domain.utils.JsonUtils;
import com.sankuai.dzhealth.ai.service.dto.stream.StreamEventCardDataDTO;
import com.sankuai.dzhealth.ai.service.dto.stream.StreamEventDTO;
import com.sankuai.dzhealth.ai.service.dto.stream.StreamEventDataDTO;
import com.sankuai.dzhealth.ai.service.enums.StreamEventCardTypeEnum;
import com.sankuai.dzhealth.ai.service.infrastructure.acl.HaimaAcl;
import com.sankuai.dzhealth.ai.service.infrastructure.acl.ShopAcl;
import com.sankuai.dzhealth.ai.service.infrastructure.utils.UidUtils;
import com.sankuai.dzhealth.ai.service.request.common.BasicParam;
import com.sankuai.dzim.pilot.api.AIPhoneCallService;
import com.sankuai.dzim.pilot.api.data.aiphonecall.*;
import com.sankuai.dzim.pilot.api.enums.aiphonecall.AIPhoneCallBackTypeEnum;
import com.sankuai.dzim.pilot.api.enums.aiphonecall.AIPhoneCallSceneTypeEnum;
import com.sankuai.dzim.pilot.api.enums.aiphonecall.AIPhoneCallSourceEnum;
import com.sankuai.dzim.pilot.api.enums.assistant.PlatformEnum;
import com.sankuai.leads.common.thrift.enums.LeadsAttributeKeyEnum;
import com.sankuai.leads.process.gateway.thrift.api.LeadsWriteController;
import com.sankuai.leads.process.gateway.thrift.request.CancelLeadsReq;
import com.sankuai.leads.process.gateway.thrift.request.common.LeadsUserCommonReq;
import com.sankuai.leads.process.gateway.thrift.vo.CommonRespVO;
import com.sankuai.leads.process.thrift.resv.user.api.ResvUserOrderService;
import com.sankuai.leads.process.thrift.resv.user.request.CreateUserResvOrderReqDTO;
import com.sankuai.leads.process.thrift.resv.user.request.LeadsUserCommonReqDTO;
import com.sankuai.leads.process.thrift.resv.user.response.CreateUserResvOrderRespDTO;
import com.sankuai.medicalcosmetology.mainpath.listingapi.common.ResponseDTO;
import com.sankuai.medicalcosmetology.mainpath.listingapi.dto.*;
import com.sankuai.medicalcosmetology.mainpath.listingapi.request.*;
import com.sankuai.medicalcosmetology.mainpath.listingapi.service.ListingFacade;
import com.sankuai.sinai.data.api.dto.MtPoiDTO;
import com.sankuai.sinai.data.api.util.MtPoiUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

@Slf4j
@Service
public class AppointmentService {


    @Autowired
    private AppointmentInfoRepository appointmentRepository;

    @Autowired
    private ChatSessionMessageRepository chatSessionMessageRepository;

    @Autowired
    private AIPhoneCallService aiPhoneCallService;


    @Autowired
    private ResvUserOrderService resvUserOrderService;

    @Autowired
    private LeadsWriteController.Iface leadsWriteController;

    @Autowired
    private RecommendConfig recommendConfig;


    @Autowired
    private ListingFacade listingFacade;


    @Autowired
    private HaimaAcl haimaAcl;


    @Autowired
    private ShopAcl shopAcl;

    @Autowired
    private AppointmentAiCancelProducer appointmentAiCancelProducer;

    @Autowired
    @Qualifier("redisClient1")
    private RedisStoreClient redisStoreClient1;

    @Autowired
    private UidUtils uidUtils;

    @Autowired
    private BusinessHourService businessService;


    public static final ThreadPool APPOINTMNET_POOL = Rhino.newThreadPool("APPOINTMNET_POOL",
            DefaultThreadPoolProperties.Setter().withCoreSize(20).withMaxSize(50).withMaxQueueSize(1000));


    //调用预约的bizId
    private static final Integer APPOINTMENT_BIZID = 51034;


    // 预约调用商户的key
    private static final String APPOINTMENT_TEMPLATE = "appointmentGroupPurchaseCard";


    /**
     * 推送预约信息确认卡片
     *
     * @param input
     * @return
     */
    public AppointmentCardData pushAppointment(String input, MessageContext messageContext) {
        try {
            log.info("pushAppointment-input={}", input);
            ObjectMapper mapper = new ObjectMapper();
            AppointmentContext context = mapper.readValue(input, AppointmentContext.class);
            AppointmentInfoBO appointmentInfo = context.toAppointmentInfoBO();
            appointmentInfo.setStatus(AppointmentStatusEnum.PENDING);
            AppointmentCardData card = getCard(AppointmentStatusEnum.PENDING);
            card.setTitle(String.format(card.getTitle(), appointmentInfo.getProductName().getDesc()));
            card.setPersonDesc(appointmentInfo.getPersonDesc());
            card.setPosition(appointmentInfo.getPositionTxt());
            card.setProductName(appointmentInfo.getProductName().getDesc());
            card.setTime(getMsg(appointmentInfo, false, false, false, false));
            card.setPhone(appointmentInfo.getPhone());
            card.setRemark(appointmentInfo.getRemark());
            card.setMsgId(messageContext.getReplyMsgId());
            card.setClicked(false);
            appointmentInfo.setUserId(messageContext.getUserId());
            appointmentInfo.setSessionId(messageContext.getSessionId());
            appointmentInfo.setCardInfo(JSON.toJSONString(card));
            appointmentInfo.setMsgId(messageContext.getReplyMsgId());
            log.info("pushAppointment appointmentInfo:{}", JSON.toJSONString(appointmentInfo));
            appointmentRepository.save(appointmentInfo);
            return card;
        } catch (Exception e) {
            throw new IllegalArgumentException("JSON字符串解析失败: " + e.getMessage(), e);
        }
    }

    /**
     * 确认预约 for "没问题，预约吧"
     *
     * @param request
     * @return
     */
    public AppointmentReturn confirmAppointment(AppointmentRequestDTO request, BasicParam basicParam) {
        try {
            log.info("confirmAppointment request:{}", JSON.toJSONString(request));
            AppointmentInfoBO appointmentInfo = appointmentRepository.findByMsgIdAndUserId(request.getMsgId(), request.getUserId());
            AppointmentCardData oldCard = JSON.parseObject(appointmentInfo.getCardInfo(), AppointmentCardData.class);
            log.info("confirmAppointment appointmentInfo:{}", JSON.toJSONString(appointmentInfo));
            Date appointStartTime = appointmentInfo.getAppointmentStartTime();
            Date appointEndTime = appointmentInfo.getAppointmentEndTime();

            if (checkErrorTime(appointmentInfo, appointEndTime)) {
                return AppointmentReturn.builder()
                        .type(AppointmentReturnTypeEnum.TEXT)
                        .txt(CatchAllTxtEnum.RE_APPOINTMENT_TIME_ERROR.getDesc())
                        .build();
            }


            ReCallSortIdsQry reCallSortIdsQry = new ReCallSortIdsQry();
            reCallSortIdsQry.setUserId(request.getUserId());
            reCallSortIdsQry.setPlatform(2);
            reCallSortIdsQry.setCityId(appointmentInfo.getCityId()!=null?appointmentInfo.getCityId():basicParam.getUserCityId());
            String queryStr = appointmentInfo.getProductName().getDesc();
            reCallSortIdsQry.setQueryStr(queryStr);
            reCallSortIdsQry.setTemplateKey(APPOINTMENT_TEMPLATE);
            reCallSortIdsQry.setUuid(request.getUuid());
            reCallSortIdsQry.setLat(appointmentInfo.getLat());
            reCallSortIdsQry.setLng(appointmentInfo.getLng());
            reCallSortIdsQry.setPoiBackCateId("20276");
            reCallSortIdsQry.setPageNum(0);
            reCallSortIdsQry.setPageSize(50);


            log.info("confirmAppointment reCallSortIdsQry:{}", JSON.toJSONString(reCallSortIdsQry));
            ResponseDTO<ReCallSortIdsDTO> responseDTO = listingFacade.reCallSortIds(reCallSortIdsQry);
            if (responseDTO == null || !responseDTO.isSuccess() || responseDTO.getData() == null) {
                throw new IllegalArgumentException("获取商户失败");
            }
            if (CollectionUtils.isEmpty(responseDTO.getData().getShopGoodsRecallDTOs())) {
                return AppointmentReturn.builder()
                        .type(AppointmentReturnTypeEnum.TEXT)
                        .txt(CatchAllTxtEnum.APPOINTMENT_NO_SHOP.getDesc())
                        .build();
            }

            log.info("confirmAppointment responseDTO:{}", JSON.toJSONString(responseDTO));
            List<ShopGoodsRecallDTO> responseDTOData = responseDTO.getData().getShopGoodsRecallDTOs();
            //黑名单
            List<Long> blackListData = getHaiMaPrompt();
            Set<Long> blackListSet = new HashSet<>(blackListData);

            List<Long> allShopIds = responseDTOData.stream()
                    .map(ShopGoodsRecallDTO::getShopId)
                    .distinct()
                    .collect(Collectors.toList());
            Map<Long, BusinessTimeInfo> shopBusinessTimeMap = getShopTimeInfo(allShopIds, appointStartTime, appointEndTime, new Date());
            log.info("confirmAppointment shopBusinessTimeMap:{}", JSON.toJSONString(shopBusinessTimeMap));
            Map<Long, List<GoodsRecallDTO>> shopId2GoodsMap = responseDTOData.stream()
                    .filter(dto -> !blackListSet.contains(dto.getShopId()))
                    // 添加空值检查
                    .filter(dto -> !shopBusinessTimeMap.isEmpty() && shopBusinessTimeMap.containsKey(dto.getShopId()))
                    .filter(dto -> {
                        BusinessTimeInfo timeInfo = shopBusinessTimeMap.get(dto.getShopId());
                        return timeInfo != null && timeInfo.isAppointmentStatus();
                    })
                    .sorted((dto1, dto2) -> {
                        BusinessTimeInfo timeInfo1 = shopBusinessTimeMap.get(dto1.getShopId());
                        BusinessTimeInfo timeInfo2 = shopBusinessTimeMap.get(dto2.getShopId());
                        // 按当前营业状态排序：营业中的在前
                        boolean status1 = timeInfo1 != null && timeInfo1.isCurrentStatus();
                        boolean status2 = timeInfo2 != null && timeInfo2.isCurrentStatus();
                        return Boolean.compare(status2, status1);
                    })
                    .collect(Collectors.toMap(
                            ShopGoodsRecallDTO::getShopId,
                            ShopGoodsRecallDTO::getShopGoodsRecallDTOs,
                            (existing, replacement) -> existing,
                            LinkedHashMap::new
                    ));

            List<Long> shopIdList = new ArrayList<>(shopId2GoodsMap.keySet());
            log.info("confirmAppointment shopIdList:{}", JSON.toJSONString(shopIdList));
            for (Long shopId : shopIdList) {
                List<GoodsRecallDTO> goodsRecallDTOList = shopId2GoodsMap.get(shopId);
                if (CollectionUtils.isEmpty(goodsRecallDTOList)) {
                    continue;
                }
                StoreKey storeKey = new StoreKey("appointment_shop", shopId);
                redisStoreClient1.set(storeKey, JsonUtils.toJsonString(goodsRecallDTOList));
            }

            //线下测试门店
            // shopIdList = Lists.newArrayList(438355553L, 606908433L, 42252832L);
            //RC测试门店
//            shopIdList = Lists.newArrayList(1008597114553257L,179287060L, 582479934L, 1160088610L, 160292001L);

            appointmentInfo.setShopIdList(shopIdList);

            if (CollectionUtils.isEmpty(shopIdList)) {
                return AppointmentReturn.builder()
                        .type(AppointmentReturnTypeEnum.TEXT)
                        .txt(CatchAllTxtEnum.APPOINTMENT_NO_SHOP.getDesc())
                        .build();
            }

            AIPhoneCallCreateReqDTO reqDTO = buildAIPhoneCallRequest(request, appointEndTime, appointStartTime, shopIdList, appointmentInfo, basicParam);
            log.info("confirmAppointment reqDTO:{}", JSON.toJSONString(reqDTO));
            AIPhoneCallTaskCreateResDTO respDTO = aiPhoneCallService.createAIPhoneCallTask(reqDTO);
            if (respDTO == null) {
                throw new IllegalArgumentException("创建外呼任务失败");
            }
            log.info("confirmAppointment respDTO:{}", JSON.toJSONString(respDTO));
            AppointmentCardData cardInfo = initializeConfirmedAppointment(appointmentInfo, respDTO, shopIdList);
            cardInfo.setMsgId(request.getReplyMsgId());
            appointmentInfo.setReservedMsgId(request.getReplyMsgId());
            appointmentInfo.setCardInfo(JSON.toJSONString(cardInfo));
            oldCard.setClicked(true);
            updateMessageCard(request.getMsgId(), request.getUserId(), oldCard, null);
            log.info("confirmAppointment appointmentInfo:{}", JSON.toJSONString(appointmentInfo));
            appointmentAiCancelProducer.sendDelayMessage(String.valueOf(respDTO.getTaskId()), appointmentInfo.getAppointmentEndTime().getTime() - System.currentTimeMillis());
            appointmentRepository.updateAppointmentInfo(appointmentInfo);
            return AppointmentReturn.builder()
                    .type(AppointmentReturnTypeEnum.CARD)
                    .card(cardInfo)
                    .build();
        } catch (Exception e) {
            log.error("confirmAppointment error", e);
            return AppointmentReturn.builder()
                    .type(AppointmentReturnTypeEnum.TEXT)
                    .txt(CatchAllTxtEnum.EXCEPTION.getDesc())
                    .build();
        }
    }


    /**
     * 批量获取门店营业时间
     * @param shopIdList
     * @param appointStartTime
     * @param appointEndTime
     * @param now
     * @return
     */
    private Map<Long, BusinessTimeInfo> getShopTimeInfo(List<Long> shopIdList, Date appointStartTime, Date appointEndTime, Date now) {
        try {
            if (CollectionUtils.isEmpty(shopIdList)) {
                return Collections.emptyMap();
            }

            Map<Long, BusinessTimeInfo> result = new ConcurrentHashMap<>();

            // 创建所有异步任务
            List<CompletableFuture<Void>> futures = shopIdList.stream()
                    .map(shopId -> CompletableFuture.runAsync(() -> {
                        try {
                            BusinessTimeInfo businessTimeInfo = new BusinessTimeInfo();
                            businessTimeInfo.setAppointmentStatus(businessService.isBusinessHourByRange(shopId, appointStartTime, appointEndTime));
                            businessTimeInfo.setCurrentStatus(businessService.isBusinessHourByPoint(shopId, now));
                            result.put(shopId, businessTimeInfo);
                        } catch (Exception e) {
                            log.error("获取门店{}营业时间异常", shopId, e);
                        }
                    }, APPOINTMNET_POOL.getExecutor()))
                    .collect(Collectors.toList());

            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
                    .orTimeout(1, TimeUnit.SECONDS)
                    .exceptionally(e -> null)
                    .join();

            return result;
        } catch (Exception e) {
            log.error("批量获取门店营业时间异常, shopIds: {}", shopIdList, e);
            return Collections.emptyMap();
        }
    }

    /**
     * 重启本次任务
     *
     * @param request
     * @return
     */
    public AppointmentReturn rescheduleAppointment(AppointmentRequestDTO request, BasicParam basicParam) {
        try {
            log.info("rescheduleAppointment request:{}", JSON.toJSONString(request));
            AppointmentInfoBO appointmentInfo = appointmentRepository.findByReservedMsgIdAndUserId(request.getMsgId(), request.getUserId());
            log.info("rescheduleAppointment appointmentInfo:{}", JSON.toJSONString(appointmentInfo));

            Date appointStartTime = appointmentInfo.getAppointmentStartTime();
            Date appointEndTime = appointmentInfo.getAppointmentEndTime();

            if (checkErrorTime(appointmentInfo, appointEndTime)) {
                return AppointmentReturn.builder()
                        .type(AppointmentReturnTypeEnum.TEXT)
                        .txt(CatchAllTxtEnum.RE_APPOINTMENT_TIME_ERROR.getDesc())
                        .build();
            }

            List<Long> shopIdList = appointmentInfo.getShopIdList();
            if (CollectionUtils.isEmpty(shopIdList)) {
                throw new IllegalArgumentException("无可预约门店");
            }

            AIPhoneCallCreateReqDTO reqDTO = buildAIPhoneCallRequest(request, appointEndTime, appointStartTime, shopIdList, appointmentInfo, basicParam);
            log.info("rescheduleAppointment reqDTO:{}", JSON.toJSONString(reqDTO));

            AIPhoneCallTaskCreateResDTO respDTO = aiPhoneCallService.createAIPhoneCallTask(reqDTO);

            if (respDTO == null) {
                throw new IllegalArgumentException("创建外呼任务失败");
            }
            log.info("rescheduleAppointment respDTO:{}", JSON.toJSONString(respDTO));

            AppointmentCardData cardInfo = initializeConfirmedAppointment(appointmentInfo, respDTO, shopIdList);
            AppointmentCardData oldCard = JSON.parseObject(appointmentInfo.getCardInfo(), AppointmentCardData.class);
            cardInfo.setMsgId(oldCard.getMsgId());
            appointmentInfo.setCardInfo(JSON.toJSONString(cardInfo));
            // 更新消息
            updateMessageCard(request.getMsgId(), request.getUserId(), cardInfo, null);
            log.info("rescheduleAppointment appointmentInfo:{}", JSON.toJSONString(appointmentInfo));
            appointmentRepository.updateAppointmentInfo(appointmentInfo);
            appointmentAiCancelProducer.sendDelayMessage(String.valueOf(respDTO.getTaskId()), appointmentInfo.getAppointmentEndTime().getTime() - System.currentTimeMillis());
            return AppointmentReturn.builder()
                    .type(AppointmentReturnTypeEnum.TEXT)
                    .txt(CatchAllTxtEnum.RESCHEDULE_APPOINTMENT.getDesc())
                    .build();
        } catch (Exception e) {
            log.error("rescheduleAppointment error", e);
            return AppointmentReturn.builder()
                    .type(AppointmentReturnTypeEnum.TEXT)
                    .txt(CatchAllTxtEnum.EXCEPTION.getDesc())
                    .build();
        }
    }

    /**
     * 取消预约任务 for "取消本次任务"
     *
     * @param request
     * @return
     */
    public AppointmentReturn cancelAppointment(AppointmentRequestDTO request) {
        try {
            log.info("cancelAppointment request:{}", JSON.toJSONString(request));
            AppointmentInfoBO appointmentInfo = appointmentRepository.findByReservedMsgIdAndUserId(request.getMsgId(), request.getUserId());
            if (appointmentInfo == null) {
                throw new IllegalArgumentException("未找到预约信息");
            }
            log.info("cancelAppointment appointmentInfo:{}", JSON.toJSONString(appointmentInfo));

            AIPhoneCallTaskCancelReqDTO taskCancelReqDTO = AIPhoneCallTaskCancelReqDTO.builder().taskId(appointmentInfo.getTaskId()).build();

            log.info("cancelAppointment taskCancelReqDTO:{}", JSON.toJSONString(taskCancelReqDTO));
            AIPhoneCallTaskCancelResDTO aiPhoneCallTaskCancelResDTO = aiPhoneCallService.cancelAIPhoneCallTask(taskCancelReqDTO);
            log.info("cancelAppointment aiPhoneCallTaskCancelResDTO:{}", JSON.toJSONString(aiPhoneCallTaskCancelResDTO));

            if (aiPhoneCallTaskCancelResDTO == null || !aiPhoneCallTaskCancelResDTO.getResult()) {
                throw new IllegalArgumentException("取消外呼任务失败");
            }

            // 更新预约状态为已取消
            appointmentInfo.setStatus(AppointmentStatusEnum.TASK_CANCELLED);

            // 获取取消状态的卡片信息
            AppointmentCardData oldCard = JSON.parseObject(appointmentInfo.getCardInfo(), AppointmentCardData.class);
            AppointmentCardData card = getCard(AppointmentStatusEnum.TASK_CANCELLED);
            card.setMsg(getMsg(appointmentInfo, false, true, true, true));
            card.setMsgId(oldCard.getMsgId());
            appointmentInfo.setCardInfo(JSON.toJSONString(card));
            log.info("cancelAppointment appointmentInfo:{}", JSON.toJSONString(appointmentInfo));
            appointmentRepository.updateAppointmentInfo(appointmentInfo);
            // 更新消息
            updateMessageCard(request.getMsgId(), request.getUserId(), card, null);

            return AppointmentReturn.builder()
                    .type(AppointmentReturnTypeEnum.TEXT)
                    .txt(CatchAllTxtEnum.APPOINTMENT_CANCEL.getDesc())
                    .build();
        } catch (Exception e) {
            log.error("cancelAppointment", e);
            return AppointmentReturn.builder()
                    .type(AppointmentReturnTypeEnum.TEXT)
                    .txt(CatchAllTxtEnum.EXCEPTION.getDesc())
                    .build();
        }
    }

    //监听AI约的消息
    public Boolean listenToAIAppointmentSignal(AIPhoneCallBackDTO callBackDTO) {
        try {
            log.info("listenToAIAppointmentSignal callBackDTO:{}", JSON.toJSONString(callBackDTO));
            Integer type = callBackDTO.getCallBackType();
            // todo 上线前删除
//            Tracer.setCell("gray-release-plat-rc");
            if (type == AIPhoneCallBackTypeEnum.TASK_COMPLETE_CALLBACK.getCallBackType()) {
                aiAppointmentSignalISComplete(callBackDTO);
            } else if (type == AIPhoneCallBackTypeEnum.PROGRESS_SYNCHRONIZATION_CALLBACK.getCallBackType()) {
                aiAppointmentSignalISProcess(callBackDTO);
            }

        } catch (Exception e) {
            log.error("listenToAIAppointmentSignal error", e);
            return false;
        }
        return true;
    }

    /**
     * 延迟取消
     * @param taskId
     */
    public void cancelAppointmentByTime(String taskId){
        try {
            log.info("cancelAppointmentByTime taskId:{}", taskId);
            AppointmentInfoBO appointmentInfoBO = appointmentRepository.findByTaskId(Long.parseLong(taskId));
            log.info("cancelAppointmentByTime appointmentInfoBO:{}", JSON.toJSONString(appointmentInfoBO));
            if (appointmentInfoBO == null) {
                log.error("cancelAppointmentByTime error,未找到AI外呼信息,taskId:{}", taskId);
                return;
            }
            if (appointmentInfoBO.getStatus() == AppointmentStatusEnum.IN_PROGRESS) {
                AIPhoneCallTaskCancelReqDTO taskCancelReqDTO = AIPhoneCallTaskCancelReqDTO.builder().taskId(appointmentInfoBO.getTaskId()).build();
                log.info("cancelAppointmentByTime taskCancelReqDTO:{}", JSON.toJSONString(taskCancelReqDTO));
                try {
                    AIPhoneCallTaskCancelResDTO aiPhoneCallTaskCancelResDTO = aiPhoneCallService.cancelAIPhoneCallTask(taskCancelReqDTO);
                    log.info("cancelAppointmentByTime aiPhoneCallTaskCancelResDTO:{}", JSON.toJSONString(aiPhoneCallTaskCancelResDTO));
                }catch (Exception e){
                    log.error("cancelAppointmentByTime error", e);
                }
                appointmentInfoBO.setStatus(AppointmentStatusEnum.NO_SHOP);
                AppointmentCardData oldCard = JSON.parseObject(appointmentInfoBO.getCardInfo(), AppointmentCardData.class);
                AppointmentCardData card = getCard(AppointmentStatusEnum.NO_SHOP);
                card.setMsg(getMsg(appointmentInfoBO, false, true, true, true));
                card.setMsgId(oldCard.getMsgId());
                appointmentInfoBO.setCardInfo(JSON.toJSONString(card));
                log.info("aiAppointmentSignalISComplete appointmentInfoBO:{}", JSON.toJSONString(appointmentInfoBO));
                appointmentRepository.updateAppointmentInfo(appointmentInfoBO);
                updateMessageCard(appointmentInfoBO.getReservedMsgId(), appointmentInfoBO.getUserId(), card, null);
            }
        }catch (Exception e){
            log.error("cancelAppointmentByTime error", e);
        }
    }



    //  监听AI约信号，当AI约信号处理完成时执行的操作
    public void aiAppointmentSignalISComplete(AIPhoneCallBackDTO callBackDTO) {
        try {
            log.info("aiAppointmentSignalISComplete callBackDTO:{}", JSON.toJSONString(callBackDTO));
            AppointmentInfoBO appointmentInfoBO = appointmentRepository.findByTaskId(callBackDTO.getCallTaskId());
            if (appointmentInfoBO == null) {
                throw new IllegalArgumentException("未找到预约信息");
            }
            log.info("aiAppointmentSignalISComplete appointmentInfoBO:{}", JSON.toJSONString(appointmentInfoBO));
            // 获取第一个预约成功的门店
            AIPhoneCallBackItemDTO firstSuccess = findFirstSuccessfulAppointment(callBackDTO);
            if (firstSuccess == null) {
                appointmentInfoBO.setStatus(AppointmentStatusEnum.NO_SHOP);
                AppointmentCardData oldCard = JSON.parseObject(appointmentInfoBO.getCardInfo(), AppointmentCardData.class);
                AppointmentCardData card = getCard(AppointmentStatusEnum.NO_SHOP);
                card.setMsg(getMsg(appointmentInfoBO, false, true, true, true));
                card.setMsgId(oldCard.getMsgId());
                appointmentInfoBO.setCardInfo(JSON.toJSONString(card));
                log.info("aiAppointmentSignalISComplete appointmentInfoBO:{}", JSON.toJSONString(appointmentInfoBO));
                appointmentRepository.updateAppointmentInfo(appointmentInfoBO);
                // 更新消息
                updateMessageCard(appointmentInfoBO.getReservedMsgId(), appointmentInfoBO.getUserId(), card, null);
                return;
            }

            String beginTime = Optional.ofNullable(firstSuccess.getCallDialogExtractInfo())
                    .map(map -> map.get("beginTime"))
                    .map(String::valueOf)
                    .orElse(null);
            if (StringUtils.isBlank(beginTime)) {
                throw new Exception("查询门店预约信息中时间信息失败");
            }
            Date bookingTime = parseStringToDate(beginTime);
            CreateUserResvOrderReqDTO createUserResvOrderReqDTO = new CreateUserResvOrderReqDTO();
            createUserResvOrderReqDTO.setMtShopId(firstSuccess.getShopId());
            createUserResvOrderReqDTO.setBizSourceId(APPOINTMENT_BIZID);
            createUserResvOrderReqDTO.setProjectType(ContentProjectTypeEnum.CUSTOM.getCode());
            createUserResvOrderReqDTO.setProjectCode(AppointmentProjectEnum.getCode(appointmentInfoBO.getProductName()));
            createUserResvOrderReqDTO.setCount(1);
            Map<String, String> attributeMap = new HashMap<>();
            attributeMap.put(LeadsAttributeKeyEnum.AI_TOUCH_PHONE.getName(), firstSuccess.getCallPhone());
            attributeMap.put(LeadsAttributeKeyEnum.USER_DETAIL_BOOK_ITEM_NAME.getName(), appointmentInfoBO.getProductName().getDesc());
            createUserResvOrderReqDTO.setAttributeMap(attributeMap);
            createUserResvOrderReqDTO.setBookingTime(bookingTime.getTime());
            createUserResvOrderReqDTO.setPhone(appointmentInfoBO.getPhone());
            createUserResvOrderReqDTO.setUserRemark(appointmentInfoBO.getRemark());
            // 获取 successRemark，如果不存在则为 null
            String successRemark = Optional.ofNullable(firstSuccess.getCallDialogExtractInfo())
                    .map(map -> map.get("successRemark"))
                    .map(String::valueOf)
                    .orElse(null);
            createUserResvOrderReqDTO.setMerchantRemark(successRemark);
            LeadsUserCommonReqDTO leadsUserCommonReqDTO = new LeadsUserCommonReqDTO();
            leadsUserCommonReqDTO.setMtUserId(appointmentInfoBO.getUserId());
            leadsUserCommonReqDTO.setPlatform(PlatformEnum.MT.getType());
            log.info("aiAppointmentSignalISComplete createUserResvOrderReqDTO:{},leadsUserCommonReqDTO:{}", JSON.toJSONString(createUserResvOrderReqDTO),JSON.toJSONString(leadsUserCommonReqDTO));
            CreateUserResvOrderRespDTO resvOrderRespDTO = resvUserOrderService.createUserResvOrder(createUserResvOrderReqDTO, leadsUserCommonReqDTO, null);
            if (resvOrderRespDTO == null || resvOrderRespDTO.getCommonResp().getCode() != 200) {
                throw new Exception("创建用户预约订单失败");
            }
            log.info("aiAppointmentSignalISComplete resvOrderRespDTO:{}", JSON.toJSONString(resvOrderRespDTO));
            appointmentInfoBO.setAppointmentStartTime(bookingTime);
            appointmentInfoBO.setLeadId(resvOrderRespDTO.getLeadsId());
            appointmentInfoBO.setSuccessShopId(firstSuccess.getShopId());
            MtPoiDTO mtShop = shopAcl.getMtShop(firstSuccess.getShopId());
            String name = MtPoiUtil.getMtPoiName(mtShop.getName(), mtShop.getBranchName());
            AppointmentCardData oldCard = JSON.parseObject(appointmentInfoBO.getCardInfo(), AppointmentCardData.class);
            AppointmentCardData card = getCard(AppointmentStatusEnum.TASK_SUCCESS);
            card.setMsgId(oldCard.getMsgId());
            card.setSuccessShopId(firstSuccess.getShopId());
            card.setMsg(getMsg(appointmentInfoBO, true, false, true, true));
            card.setSuccessShop(name);
            card.setLeadUrl(String.format("imeituan://www.meituan.com/mrn?mrn_biz=gc&mrn_entry=vg-mrn-reserve&mrn_component=reserveresult&leadsId=%s", resvOrderRespDTO.getLeadsId()));
            card.setNavigateUrl(generatePoiJumpLink(String.valueOf(firstSuccess.getShopId()), mtShop.getLatitude(), mtShop.getLongitude()));
            card.setShopUrl(String.format("imeituan://www.meituan.com/gc/poi/detail?id=%s", firstSuccess.getShopId()));
            appointmentInfoBO.setCardInfo(JSON.toJSONString(card));
            log.info("aiAppointmentSignalISComplete appointmentInfoBO:{}", JSON.toJSONString(appointmentInfoBO));
            appointmentRepository.updateAppointmentInfo(appointmentInfoBO);
            ProductCardData productCardData = pushProductInfo(firstSuccess.getShopId(), callBackDTO.getBizData());
            // 更新消息
            updateMessageCard(appointmentInfoBO.getReservedMsgId(), appointmentInfoBO.getUserId(), card, productCardData);


        } catch (Exception e) {
            log.error("aiAppointmentSignalISComplete error", e);
        }
    }

    /**
     * 生成POI跳链
     *
     * @return 跳链字符串
     */
    public String generatePoiJumpLink(String poiId, Double latitude, Double longitude) {
        try {
            log.info("generatePoiJumpLink poiId:{},latitude,{},longitude:{}", poiId, latitude, longitude);
            StringBuilder jumpLink = new StringBuilder();
            jumpLink.append("imeituan://www.meituan.com/mapchannel");
            jumpLink.append("?mapsource=yuyue");
            jumpLink.append("&pagetype=1"); // POI单卡场景

            // POI ID
            jumpLink.append("&poi_id=").append(poiId);
            // 坐标信息
            if (latitude != null && longitude != null) {
                jumpLink.append("&latitude=").append(latitude);
                jumpLink.append("&longitude=").append(longitude);
                jumpLink.append("&coordtype=0"); // 坐标类型
            }
            // 阶段信息
            jumpLink.append("&stage=1");
            log.info("jumpLink:{}", jumpLink.toString());
            return jumpLink.toString();
        } catch (Exception e) {
            log.error("generatePoiJumpLink error", e);
            return "";
        }

    }


    /**
     * 推送团购商品
     * @param shopId
     * @param bizData
     * @return
     */
    public ProductCardData pushProductInfo(Long shopId, String bizData) {
        try {
            log.info("pushProductInfo shopId:{},bizData:{}", shopId, bizData);
            BasicParam basicParam = JSON.parseObject(bizData, BasicParam.class);
            if (basicParam.getClientType().equals("Ios")) {
                basicParam.setClientType("ios");
            }
            if (basicParam.getClientType().equals("android")) {
                basicParam.setClientType("Android");
            }

            StoreKey storeKey = new StoreKey("appointment_shop", shopId);
            String product = redisStoreClient1.get(storeKey);
            log.info("pushProductInfo product:{}", product);
            List<GoodsRecallDTO> goodsRecallDTOS = JsonUtils.parseArray(product, GoodsRecallDTO.class);
            if (CollectionUtils.isEmpty(goodsRecallDTOS)) {
                return null;
            }
            FillInfoQry fillInfoQry = new FillInfoQry();
            List<GoodsQry> goodsQryList = goodsRecallDTOS.stream().map(goodsRecallDTO -> {
                GoodsQry goodsQry = new GoodsQry();
                goodsQry.setGoodsId(goodsRecallDTO.getGoodsId());
                goodsQry.setGoodsType(goodsRecallDTO.getGoodsType());
                GoodsShopQry goodsShopQry = new GoodsShopQry();
                goodsShopQry.setShopId(shopId);
                goodsQry.setShopQry(goodsShopQry);
                return goodsQry;
            }).collect(Collectors.toList());
            ShopQry shopQry = new ShopQry();
            shopQry.setShopId(shopId);
            List<ShopQry> shopQryList = new ArrayList<>();
            shopQryList.add(shopQry);

            fillInfoQry.setShopQryList(shopQryList);
            fillInfoQry.setGoodsQryList(goodsQryList);
            fillInfoQry.setTemplateKey(APPOINTMENT_TEMPLATE);
            fillInfoQry.setUserId(basicParam.getUserId());
            fillInfoQry.setPlatform(2);
            fillInfoQry.setAppVersion(basicParam.getAppVersion());
            fillInfoQry.setCityId(basicParam.getUserCityId());
            fillInfoQry.setUuid(basicParam.getUuid());
            fillInfoQry.setOs(basicParam.getClientType());
            fillInfoQry.setLat(basicParam.getLat());
            fillInfoQry.setLng(basicParam.getLng());
            ResponseDTO<FillInfoDTO> fillInfoDTO = listingFacade.fillInfo(fillInfoQry);
            log.info("pushProductInfo fillInfoDTO:{}", JSON.toJSONString(fillInfoDTO));
            if (fillInfoDTO == null || fillInfoDTO.getCode() != 200 || fillInfoDTO.getData() == null) {
                return null;
            }
            FillInfoDTO fillInfoDTOS = fillInfoDTO.getData();
            List<ShopInfoDTO> shopInfos = fillInfoDTOS.getShopInfos();
            List<GoodsInfoDTO> goodsInfoDTOS = fillInfoDTOS.getGoodsInfoDTOs();
            if (CollectionUtils.isEmpty(goodsInfoDTOS) || CollectionUtils.isEmpty(shopInfos)) {
                return null;
            }
            Map<Long, GoodsInfoDTO> goodsInfoMap = goodsInfoDTOS.stream()
                    .filter(goods -> goods != null && goods.getGoodsId() != null)
                    .collect(Collectors.toMap(
                            GoodsInfoDTO::getGoodsId,
                            goods -> goods,
                            (existing, replacement) -> existing // 如果有重复的goodsId，保留第一个
                    ));
            List<String> goodsInfoForAi = new ArrayList<>();
            for (GoodsInfoDTO goodsInfoId : goodsInfoDTOS) {
                goodsInfoForAi.add(convertGoodToChineseJson(goodsInfoId));
            }
            TaskConfig recommendProductTask = getHaiMaPromptForAi("recommend_product_prompt");
            String recommendProductPrompt = recommendProductTask.getSystemPrompt().replace("${product_relation}", goodsInfoForAi.toString())
                    .replace("${size}", goodsInfoForAi.size() + "");
            recommendProductTask.setSystemPrompt(recommendProductPrompt);
            recommendProductTask.setUserPrompt("帮我推荐一个团购商品");
            String recommendProductResult =recommendConfig.chatWithJSON(recommendProductTask);
            log.info("pushProductInfo recommendProductResult:{}", recommendProductResult);
            if (StringUtils.isBlank(recommendProductResult)) {
                return null;
            }
            recommendProductResult = cleanJsonString(recommendProductResult);
            ProductCardData cardData = JSON.parseObject(recommendProductResult, ProductCardData.class);
            if (cardData == null || cardData.getReason() == null || !goodsInfoMap.containsKey(cardData.getGoodsId())) {
                return null;
            }
            GoodsInfoDTO goodsInfoDTO = goodsInfoMap.get(cardData.getGoodsId());
            ShopInfoDTO shopInfoDTO = shopInfos.get(0);
            cardData.setShopName(shopInfoDTO.getShopName());
            cardData.setShopDistance(shopInfoDTO.getShopDistance());
            cardData.setShopBusinessCircle(shopInfoDTO.getShopBusinessCircle());
            cardData.setGoodsName(goodsInfoDTO.getGoodsName());
            cardData.setGoodsHeadUrl(goodsInfoDTO.getGoodsHeadUrl());
            cardData.setGoodsFinalPrice(formatFloatString(goodsInfoDTO.getFinalPrice()));
            cardData.setGoodsOriginalPrice(formatFloatString(goodsInfoDTO.getOriginalPrice()));
            cardData.setGoodsRedirectUrl(goodsInfoDTO.getGoodsRedirectUrl());
            cardData.setGoodsVerifiedSales(goodsInfoDTO.getVerifiedSales());
            if (cardData.hasAllValues()) {
                return cardData;
            }
            return null;
        } catch (Exception e) {
            log.error("pushProductInfo error", e);
            return null;
        }
    }




    //监听AI约信号，当AI约信号处理同步时执行的操作
    public void aiAppointmentSignalISProcess(AIPhoneCallBackDTO callBackDTO) throws Exception {
        log.info("aiAppointmentSignalISProcess callBackDTO:{}", JSON.toJSONString(callBackDTO));
        Long callTaskId = callBackDTO.getCallTaskId();
        AppointmentInfoBO appointmentInfoBO = appointmentRepository.findByTaskId(callTaskId);
        log.info("aiAppointmentSignalISProcess appointmentInfoBO:{}", JSON.toJSONString(appointmentInfoBO));
        if (appointmentInfoBO == null) {
            log.error("aiAppointmentSignalISProcess error,未找到AI外呼信息,callTaskId:{}", callTaskId);
            throw new Exception("未找到AI外呼信息");
        }
        String cardInfo = appointmentInfoBO.getCardInfo();
        AppointmentCardData appointmentCardData = JSON.parseObject(cardInfo, AppointmentCardData.class);
        appointmentCardData.setCurrentNum(Math.max(callBackDTO.getCallBackItemList().size(), 1));
        appointmentInfoBO.setCardInfo(JSON.toJSONString(appointmentCardData));
        log.info("aiAppointmentSignalISProcess appointmentInfoBO:{}", JSON.toJSONString(appointmentInfoBO));
        appointmentRepository.updateAppointmentInfo(appointmentInfoBO);
        // 更新消息
        updateMessageCard(appointmentInfoBO.getReservedMsgId(), appointmentInfoBO.getUserId(), appointmentCardData, null);
    }

    /**
     * 取消成功任务 for “取消预约”
     *
     * @param request
     * @return
     */
    public AppointmentReturn cancelSuccessAppointByButton(AppointmentRequestDTO request) {
        try {
            log.info("cancelSuccessAppointByButton request:{}", JSON.toJSONString(request));
            AppointmentInfoBO appointmentInfoBO = appointmentRepository.findByReservedMsgIdAndUserId(request.getMsgId(), request.getUserId());
            log.info("cancelSuccessAppointByButton appointmentInfoBO:{}", JSON.toJSONString(appointmentInfoBO));
            if (appointmentInfoBO == null || appointmentInfoBO.getUserId() == null || appointmentInfoBO.getLeadId() == null) {
                log.error("cancelSuccess error,未找到预约信息,request:{}", request);
                throw new IllegalArgumentException("未找到预约信息");
            }
            CancelLeadsReq leadsReqDTO = new CancelLeadsReq();
            leadsReqDTO.setLeadsId(appointmentInfoBO.getLeadId());
            LeadsUserCommonReq commonReqDTO = new LeadsUserCommonReq();
            commonReqDTO.setMtUserId(appointmentInfoBO.getUserId());
            commonReqDTO.setPlatform(PlatformEnum.MT.getType());
            log.info("cancelLeads leadsReqDTO:{},commonReqDTO:{}", JSON.toJSONString(leadsReqDTO), JSON.toJSONString(commonReqDTO));
            CommonRespVO respVO = leadsWriteController.cancelLeads(leadsReqDTO, commonReqDTO);
            log.info("cancelLeads respVO:{}", JSON.toJSONString(respVO));
            if (respVO.getCode() != 200) {
                throw new Exception("取消预约失败");
            }
            appointmentInfoBO.setStatus(AppointmentStatusEnum.RESERVATION_CANCELLED);
            AppointmentCardData oldCard = JSON.parseObject(appointmentInfoBO.getCardInfo(), AppointmentCardData.class);
            AppointmentCardData card = getCard(AppointmentStatusEnum.RESERVATION_CANCELLED);
            card.setSuccessShopId(oldCard.getSuccessShopId());
            card.setSuccessShop(oldCard.getSuccessShop());
            card.setMsg(oldCard.getMsg());
            card.setMsgId(request.getMsgId());
            card.setShopUrl(oldCard.getShopUrl());
            card.setLeadUrl(oldCard.getLeadUrl());
            appointmentInfoBO.setCardInfo(JSON.toJSONString(card));
            log.info("cancelSuccessAppointByButton appointmentInfoBO:{}", JSON.toJSONString(appointmentInfoBO));
            appointmentRepository.updateAppointmentInfo(appointmentInfoBO);
            // 更新消息
            updateMessageCard(appointmentInfoBO.getReservedMsgId(), appointmentInfoBO.getUserId(), card, null);
            aiCallToShopCancel(appointmentInfoBO);
            return AppointmentReturn.builder()
                    .type(AppointmentReturnTypeEnum.TEXT)
                    .txt(CatchAllTxtEnum.CANCEL_SUCCESS_APPOINT.getDesc())
                    .build();
        } catch (Exception e) {
            log.error("cancelSuccess error", e);
            return AppointmentReturn.builder()
                    .type(AppointmentReturnTypeEnum.TEXT)
                    .txt(CatchAllTxtEnum.EXCEPTION.getDesc())
                    .build();
        }
    }




    /**
     * 监听消息取消 for “取消预约”
     *
     * @param leadId
     * @return
     */
    public void cancelSuccessAppointByMessage(Long leadId) {
        try {
            log.info("cancelSuccessAppointByMessage leadId:{}", leadId);
            AppointmentInfoBO appointmentInfoBO = appointmentRepository.findByLeadId(leadId);
            log.info("cancelSuccessAppointByMessage appointmentInfoBO:{}", JSON.toJSONString(appointmentInfoBO));
            if (appointmentInfoBO == null || appointmentInfoBO.getUserId() == null || appointmentInfoBO.getLeadId() == null) {
                log.error("cancelSuccess error,未找到预约信息,leadId:{}", leadId);
                throw new IllegalArgumentException("未找到预约信息");
            }
            if (appointmentInfoBO.getStatus() == AppointmentStatusEnum.RESERVATION_CANCELLED) {
                return;
            }
            AppointmentCardData oldCard = JSON.parseObject(appointmentInfoBO.getCardInfo(), AppointmentCardData.class);
            AppointmentCardData card = getCard(AppointmentStatusEnum.RESERVATION_CANCELLED);
            card.setSuccessShopId(oldCard.getSuccessShopId());
            card.setSuccessShop(oldCard.getSuccessShop());
            card.setMsg(oldCard.getMsg());
            card.setMsgId(oldCard.getMsgId());
            card.setShopUrl(oldCard.getShopUrl());
            card.setLeadUrl(oldCard.getLeadUrl());
            appointmentInfoBO.setCardInfo(JSON.toJSONString(card));
            log.info("cancelSuccessAppointByMessage appointmentInfoBO:{}", JSON.toJSONString(appointmentInfoBO));
            appointmentRepository.updateAppointmentInfo(appointmentInfoBO);
            // 更新消息
            updateMessageCard(appointmentInfoBO.getReservedMsgId(), appointmentInfoBO.getUserId(), card, null);
            // 外呼取消
            aiCallToShopCancel(appointmentInfoBO);
        } catch (Exception e) {
            log.error("cancelSuccess error", e);

        }
    }



    /**
     * 再次预约 for "再次预约"
     *
     * @param request
     * @return
     */
    public AppointmentReturn reAppointment(AppointmentRequestDTO request) {
        log.info("reAppointment request:{}", JSON.toJSONString(request));
        return AppointmentReturn.builder()
                .type(AppointmentReturnTypeEnum.TEXT)
                .txt(CatchAllTxtEnum.RE_APPOINTMENT.getDesc())
                .build();
    }

    /**
     *调用AI外呼取消
     * @param appointmentInfoBO
     */
    public void aiCallToShopCancel(AppointmentInfoBO appointmentInfoBO){
        try {
            log.info("aiCallToShopCancel appointmentInfoBO:{}", JSON.toJSONString(appointmentInfoBO));
            Long shopId = appointmentInfoBO.getSuccessShopId();
            MtPoiDTO shopInfo = shopAcl.getMtShop(shopId);
            List<AIPhoneCallDetailCreateReqDTO> aiPhoneCallDetailList = new ArrayList<>();
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
            String formattedAppointTime = dateFormat.format(appointmentInfoBO.getAppointmentEndTime());
            String reservationTime = getReservationTime(appointmentInfoBO.getAppointmentStartTime(), appointmentInfoBO.getAppointmentEndTime());
            AIPhoneCallDetailCreateReqDTO detail = new AIPhoneCallDetailCreateReqDTO();
            detail.setShopId(shopId);
            detail.setSequenceId(1);
            detail.setUserId(appointmentInfoBO.getUserId());
            detail.setDdlDate(formattedAppointTime);
            Map<String, String> dynamicParameterMap = getDynamicParameterMap(appointmentInfoBO, reservationTime, formattedAppointTime, MtPoiUtil.getMtPoiName(shopInfo.getName(), shopInfo.getBranchName()));
            detail.setDynamicParameterMap(dynamicParameterMap);
            aiPhoneCallDetailList.add(detail);
            AIPhoneCallCreateReqDTO reqDTO = new AIPhoneCallCreateReqDTO();
            reqDTO.setPlatform(PlatformEnum.MT.getType());
            reqDTO.setUserGender("unknown");
            reqDTO.setAiPhoneCallSceneTypeEnum(AIPhoneCallSceneTypeEnum.CANCEL_RESERVATION);
            reqDTO.setAiPhoneCallSourceEnum(AIPhoneCallSourceEnum.MEDICAL_AI_CALL);
            reqDTO.setBizId(String.valueOf(appointmentInfoBO.getUserId()));
            reqDTO.setAiPhoneCallDetailList(aiPhoneCallDetailList);
            log.info("aiCallToShopCancel reqDTO:{}", JSON.toJSONString(reqDTO));
            AIPhoneCallTaskCreateResDTO respDTO = aiPhoneCallService.createAIPhoneCallTask(reqDTO);
            log.info("aiCallToShopCancel respDTO:{}", JSON.toJSONString(respDTO));
            if (respDTO == null) {
                throw new IllegalArgumentException("创建取消外呼任务失败");
            }
        }catch (Exception e){
            log.error("aiCallToShopCancel error", e);
        }
    }


    /**
     * 团购商品信息AI友好处理
     * @param goodsInfoDTO
     * @return
     */
    public String convertGoodToChineseJson(GoodsInfoDTO goodsInfoDTO) {
        try {
            Map<String, Object> goodsInfo = new HashMap<>();
            goodsInfo.put("团购编号", goodsInfoDTO.getGoodsId());
            goodsInfo.put("名称", goodsInfoDTO.getGoodsName());
            goodsInfo.put("销量", goodsInfoDTO.getVerifiedSales());
            goodsInfo.put("价格", goodsInfoDTO.getFinalPrice());
            goodsInfo.put("折扣", goodsInfoDTO.getDiscountRate());
            ObjectMapper mapper = new ObjectMapper();
            return mapper.writeValueAsString(goodsInfo);
        } catch (Exception e) {
            log.error("convertGoodToChineseJson error", e);
            return null;
        }

    }


    // 解析日期字符串为Date对象，并将分钟数映射到最近的0、15、30、45
    public Date parseStringToDate(String dateStr) {
        try {
            log.info("parseStringToDate dateStr:{}", dateStr);
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm");
            LocalDateTime localDateTime = LocalDateTime.parse(dateStr, formatter);
            LocalDateTime adjustedDateTime = adjustToNearestQuarterPrecise(localDateTime);
            return Date.from(adjustedDateTime.atZone(ZoneId.systemDefault()).toInstant());
        } catch (Exception e) {
            log.error("日期解析失败: {}", dateStr, e);
            return null;
        }
    }


    /**
     * 格式化浮点数字符串
     * - 如果小数点后第一位是0，则只保留整数部分
     * - 如果小数点后第一位不是0，则保留一位小数
     *
     * @param numStr 格式为"ABC.DE"的浮点数字符串
     * @return 格式化后的字符串
     */
    public static String formatFloatString(String numStr) {
        if(StringUtils.isEmpty(numStr)){
            return numStr;
        }
        try {
            // 检查字符串是否包含小数点
            if (numStr.contains(".")) {
                // 分割整数部分和小数部分
                String[] parts = numStr.split("\\.");
                String integerPart = parts[0];
                String decimalPart = parts.length > 1 ? parts[1] : "";

                // 如果小数部分为空或长度不足，直接返回原字符串
                if (decimalPart.isEmpty() || decimalPart.length() < 1) {
                    return numStr;
                }

                // 获取小数点后第一位
                char firstDecimal = decimalPart.charAt(0);

                // 如果小数点后第一位是0，则只保留整数部分
                if (firstDecimal == '0') {
                    return integerPart;
                }
                // 如果小数点后第一位不是0，则保留一位小数
                else {
                    return integerPart + "." + firstDecimal;
                }
            }

            // 如果没有小数点，直接返回原字符串
            return numStr;
        } catch (Exception e) {
            // 发生异常时返回原字符串
            return numStr;
        }
    }

    /**
     * 将时间调整到最近的15分钟间隔（使用精确映射规则）
     * 0-7分钟 -> 0分钟
     * 8-22分钟 -> 15分钟
     * 23-37分钟 -> 30分钟
     * 38-52分钟 -> 45分钟
     * 53-59分钟 -> 下一小时0分钟
     */
    private LocalDateTime adjustToNearestQuarterPrecise(LocalDateTime dateTime) {
        int currentMinute = dateTime.getMinute();
        int adjustedMinute;
        boolean addHour = false;

        if (currentMinute <= 7) {
            adjustedMinute = 0;
        } else if (currentMinute <= 22) {
            adjustedMinute = 15;
        } else if (currentMinute <= 37) {
            adjustedMinute = 30;
        } else if (currentMinute <= 52) {
            adjustedMinute = 45;
        } else {
            adjustedMinute = 0;
            addHour = true;
        }

        LocalDateTime result = dateTime.withMinute(adjustedMinute).withSecond(0).withNano(0);

        if (addHour) {
            result = result.plusHours(1);
        }

        log.info("时间调整: {} -> {}", dateTime, result);
        return result;
    }

    /**
     * 获取第一个预约成功的门店信息
     *
     * @param callBackDTO AI外呼回调数据
     * @return 预约成功的门店信息，如果没有则返回null
     */
    public AIPhoneCallBackItemDTO findFirstSuccessfulAppointment(AIPhoneCallBackDTO callBackDTO) {
        log.info("findFirstSuccessfulAppointment callBackDTO:{}", JSON.toJSONString(callBackDTO));
        if (callBackDTO == null || callBackDTO.getCallBackItemList() == null) {
            return null;
        }

        return callBackDTO.getCallBackItemList().stream()
                .filter(item -> isAppointmentSuccessful(item))
                .findFirst()
                .orElse(null);
    }

    /**
     * 判断单个门店是否预约成功
     *
     * @param item 门店外呼信息
     * @return true表示预约成功
     */
    private boolean isAppointmentSuccessful(AIPhoneCallBackItemDTO item) {
        log.info("isAppointmentSuccessful item:{}", JSON.toJSONString(item));
        if (item == null || item.getCallDialogExtractInfo() == null) {
            return false;
        }
        Object result = item.getCallDialogExtractInfo().get("result");
        return "true".equals(String.valueOf(result));
    }

    private String getSubTitleByTime(String txt, AppointmentInfoBO appointmentInfo) {
        log.info("getSubTitleByTime appointmentInfo:{}", JSON.toJSONString(appointmentInfo));
        ZoneId beijingZone = ZoneId.of("Asia/Shanghai");
        LocalDateTime time = LocalDateTime.now(beijingZone);
        int hour = time.getHour();
        // 20:00-次日4:00 (晚上时间段)
        if (hour >= 20 || hour <= 4) {
            return "现在时间太晚，预约可能较慢，请耐心等待~";
        }
        // 4:01-8:00 (早上时间段)
        else if (hour <= 8) {
            return "现在时间太早，预约可能较慢，请耐心等待~";
        }
        // 其他时间段返回空字符串或默认提示
        else {
            List<Long> shopIdList = appointmentInfo.getShopIdList();
            return String.format(txt,
                    shopIdList.size(),
                    Math.max(shopIdList.size() / 10 + 1, 1));
        }
    }

    private String getMsg(AppointmentInfoBO appointmentInfo, boolean isSuccessfulAppointment, boolean needLocation, boolean needProductName, boolean needPersonDesc) {
        log.info("getMsg appointmentInfo:{}, isSuccessfulAppointment:{}, needLocation:{}", JSON.toJSONString(appointmentInfo), isSuccessfulAppointment, needLocation);
        Date appointStartTime = appointmentInfo.getAppointmentStartTime();
        Date appointEndTime = appointmentInfo.getAppointmentEndTime();

        if (appointEndTime == null) {
            return "";
        }

        // 获取当前时间和预约时间
        LocalDateTime now = LocalDateTime.now(ZoneId.of("Asia/Shanghai"));
        LocalDateTime appointDateTime = appointEndTime.toInstant()
                .atZone(ZoneId.of("Asia/Shanghai"))
                .toLocalDateTime();

        // 格式化日期（统一使用MM.dd格式，去掉年份）
        SimpleDateFormat dateFormat = new SimpleDateFormat("MM.dd");
        String dateStr = dateFormat.format(appointEndTime);

        // 判断是今天、明天还是其他日期
        StringBuilder sb = new StringBuilder();
        sb.append(dateStr);

        if (appointDateTime.toLocalDate().equals(now.toLocalDate())) {
            // 今天
            sb.append("（今天）");
        } else if (appointDateTime.toLocalDate().equals(now.toLocalDate().plusDays(1))) {
            // 明天
            sb.append("（明天）");
        }

        sb.append(" ");

        // 根据isSuccessfulAppointment参数决定时间格式
        String timeStr;
        if (isSuccessfulAppointment) {
            // 成功预约：只使用appointmentStartTime格式化为HH:mm
            if (appointStartTime != null) {
                SimpleDateFormat timeFormat = new SimpleDateFormat("HH:mm");
                timeStr = timeFormat.format(appointStartTime);
            } else {
                timeStr = "";
            }
        } else {
            // 未成功预约：使用getReservationTime()方法
            timeStr = getReservationTime(appointStartTime, appointEndTime);
        }

        sb.append(timeStr);

        // 根据needLocation参数决定是否显示地点
        if (needLocation) {
            sb.append("｜").append(appointmentInfo.getPositionTxt());
        }

        // 根据needProductName显示项目名称
        if (needProductName) {
            sb.append("｜").append(appointmentInfo.getProductName().getDesc());
        }

        // 根据needPersonDesc显示人数
        if (needPersonDesc) {
            sb.append("｜").append(appointmentInfo.getPersonDesc());
        }


        return sb.toString();
    }

    //获取卡片基础信息
    private AppointmentCardData getCard(AppointmentStatusEnum statusEnum) throws Exception {
        log.info("getCard statusEnum:{}", statusEnum);
        Map<String, Object> cardBaseInfo = Lion.getMap(MdpContextUtils.getAppKey(), LionEnum.AI_APPOINTMENT_CARD.getKey(), Object.class);
        if (MapUtils.isEmpty(cardBaseInfo)) {
            throw new Exception(String.format("获取Lion：%s失败", LionEnum.AI_CALL_CARD_INFO.getKey()));
        }
        return JsonUtil.fromJson(JSON.toJSONString(cardBaseInfo.get(String.valueOf(statusEnum.getCode()))), AppointmentCardData.class);
    }

    /**
     * 获取预约时间字符串
     * 如果appointmentStartTime和appointmentEndTime相同，返回单个时间如14:00
     * 如果appointmentStartTime和appointmentEndTime不同，返回时间范围如13:00-14:00
     *
     * @return 预约时间字符串
     */
    private String getReservationTime(Date appointmentStartTime, Date appointmentEndTime) {
        log.info("getReservationTime appointmentStartTime:{}, appointmentEndTime:{}", appointmentStartTime, appointmentEndTime);
        if (appointmentStartTime == null || appointmentEndTime == null) {
            return "";
        }

        SimpleDateFormat timeFormat = new SimpleDateFormat("HH:mm");
        String startTime = timeFormat.format(appointmentStartTime);
        String endTime = timeFormat.format(appointmentEndTime);

        // 如果开始时间和结束时间相同，只返回一个时间
        if (startTime.equals(endTime)) {
            return endTime;
        } else {
            return startTime + "-" + endTime;
        }
    }

    /**
     * 初始化帮约信息
     * @param appointmentInfo
     * @param respDTO
     * @param shopIdList
     * @return
     * @throws Exception
     */
    private AppointmentCardData initializeConfirmedAppointment(AppointmentInfoBO appointmentInfo, AIPhoneCallTaskCreateResDTO respDTO, List<Long> shopIdList) throws Exception {
        log.info("initializeConfirmedAppointment appointmentInfo:{}, respDTO:{}, shopIdList:{}", JSON.toJSONString(appointmentInfo), JSON.toJSONString(respDTO), JSON.toJSONString(shopIdList));
        appointmentInfo.setStatus(AppointmentStatusEnum.IN_PROGRESS);
        appointmentInfo.setTaskId(respDTO.getTaskId());
        AppointmentCardData card = getCard(AppointmentStatusEnum.IN_PROGRESS);
        card.setSubtitle(getSubTitleByTime(card.getSubtitle(), appointmentInfo));
        card.setTotalNum(shopIdList.size());
        card.setCurrentNum(1);
        card.setWaitTime(String.valueOf(shopIdList.size() / 10+1));
        card.setMsg(getMsg(appointmentInfo, false, true, true, true));
        return card;
    }

    private boolean checkErrorTime(AppointmentInfoBO appointmentInfo, Date appointEndTime) {
        log.info("checkErrorTime appointmentInfo:{}, appointEndTime:{}", JSON.toJSONString(appointmentInfo), appointEndTime);
        if (appointmentInfo == null) {
            throw new IllegalArgumentException("未找到预约信息");
        }
        Date currentTimePlus30Min = new Date(System.currentTimeMillis() + 30 * 60 * 1000);
        return appointEndTime == null || appointEndTime.before(currentTimePlus30Min);
    }

    private AIPhoneCallCreateReqDTO buildAIPhoneCallRequest(AppointmentRequestDTO request, Date appointEndTime, Date appointStartTime, List<Long> shopIdList, AppointmentInfoBO appointmentInfo, BasicParam basicParam) {
        log.info("buildAIPhoneCallRequest request:{}, appointEndTime:{}, appointStartTime:{}, shopIdList:{}, appointmentInfo:{},basicParam:{}",
                JSON.toJSONString(request), appointEndTime, appointStartTime, JSON.toJSONString(shopIdList), JSON.toJSONString(appointmentInfo), JSON.toJSONString(basicParam));
        Map<Long, MtPoiDTO> shopInfo = shopAcl.getMtShops(shopIdList);

        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        String formattedAppointTime = dateFormat.format(appointEndTime);
        String reservationTime = getReservationTime(appointStartTime, appointEndTime);
        AtomicInteger sequence = new AtomicInteger(1);
        List<AIPhoneCallDetailCreateReqDTO> detailCreateReqDTOList = shopIdList.stream()
                .map(shopId -> {
                    AIPhoneCallDetailCreateReqDTO detail = new AIPhoneCallDetailCreateReqDTO();
                    detail.setShopId(shopId);
                    detail.setSequenceId(sequence.getAndIncrement());
                    detail.setUserId(request.getUserId());
                    detail.setDdlDate(formattedAppointTime);
                    Map<String, String> dynamicParameterMap = getDynamicParameterMap(appointmentInfo, reservationTime, formattedAppointTime, MtPoiUtil.getMtPoiName(shopInfo.get(shopId).getName(), shopInfo.get(shopId).getBranchName()));
                    detail.setDynamicParameterMap(dynamicParameterMap);

                    return detail;
                })
                .collect(Collectors.toCollection(ArrayList::new));
        // 使用 new 方式创建对象，避免 Builder 可能的问题
        AIPhoneCallCreateReqDTO reqDTO = new AIPhoneCallCreateReqDTO();
        reqDTO.setPlatform(PlatformEnum.MT.getType());
        reqDTO.setUserGender("unknown");
        reqDTO.setAiPhoneCallSceneTypeEnum(AIPhoneCallSceneTypeEnum.RESERVATION);
        reqDTO.setAiPhoneCallSourceEnum(AIPhoneCallSourceEnum.MEDICAL_AI_CALL);
        reqDTO.setBizId(String.valueOf(request.getUserId()));
        reqDTO.setBizData(JSON.toJSONString(basicParam));
        reqDTO.setAiPhoneCallDetailList(detailCreateReqDTOList);
        log.info("rescheduleAppointment reqDTO:{}", JSON.toJSONString(reqDTO));
        return reqDTO;
    }

    private Map<String, String> getDynamicParameterMap(AppointmentInfoBO appointmentInfo, String reservationTime, String formattedAppointTime, String shopName) {
        log.info("getDynamicParameterMap appointmentInfo:{}, reservationTime:{}, formattedAppointTime:{}, shopName:{}", JSON.toJSONString(appointmentInfo), reservationTime, formattedAppointTime, shopName);
        Map<String, String> dynamicParameterMap = new HashMap<>();
        dynamicParameterMap.put("projectName", appointmentInfo.getProductName().getDesc());
        dynamicParameterMap.put("reservationTime", reservationTime);
        dynamicParameterMap.put("reservationDate", formattedAppointTime);
        dynamicParameterMap.put("peopleNumber", appointmentInfo.getPersonDesc());
        dynamicParameterMap.put("userPhone", appointmentInfo.getPhone());
        dynamicParameterMap.put("remark", appointmentInfo.getRemark());
        dynamicParameterMap.put("shopName", shopName);
        return dynamicParameterMap;
    }

    private void updateMessageCard(String messageId, Long userId, AppointmentCardData cardInfo, ProductCardData productCardData) {
        log.info("updateMessageCard messageId:{}, userId:{}, cardInfo:{}", messageId, userId, JSON.toJSONString(cardInfo));
        ChatSessionMessageEntity messageEntity = chatSessionMessageRepository
                .findByMessageIdAndUserId(messageId, userId);

        if (messageEntity == null) {
            throw new IllegalArgumentException("未找到消息");
        }

        List<StreamEventDTO> streamEventDTOS = JSON.parseArray(messageEntity.getContent(), StreamEventDTO.class);

        // 添加空值和边界检查
        if (CollectionUtils.isEmpty(streamEventDTOS)) {
            throw new IllegalArgumentException("消息内容解析失败或为空");
        }

        StreamEventDTO streamEventDTO = streamEventDTOS.get(0);


        StreamEventDataDTO streamEventDataDTO = streamEventDTO.getData();
        if (streamEventDataDTO == null) {
            throw new IllegalArgumentException("消息内容解析失败或为空");
        }
        StreamEventCardDataDTO cardDataDTO = Optional.ofNullable(streamEventDataDTO.getCardsData())
                .filter(cardsData -> !CollectionUtils.isEmpty(cardsData))
                .flatMap(cardsData -> cardsData.stream()
                        .filter(card -> card != null && "AppointmentInfoCard".equals(card.getType()))
                        .findFirst())
                .orElse(null);


        if (cardDataDTO == null) {
            throw new IllegalArgumentException("未找到appointment类型的卡片数据");
        }

        if (productCardData != null) {

            StreamEventCardDataDTO productCardDataDTO = new StreamEventCardDataDTO();
            productCardDataDTO.setType(StreamEventCardTypeEnum.PRODUCT_CARD.getType());
            productCardDataDTO.setCardProps(productCardData.toMap());
            productCardDataDTO.setKey(uidUtils.getRandomCardHashKey());
            streamEventDataDTO.getCardsData().add(productCardDataDTO);
            StringBuilder sb = new StringBuilder();
            sb.append(streamEventDataDTO.getContent());
            sb.append(":::}");
            sb.append(StreamEventCardTypeEnum.buildCardContent(StreamEventCardTypeEnum.PRODUCT_CARD, productCardDataDTO.getKey()));
            sb.append("{:::");
            streamEventDataDTO.setContent(sb.toString());
        }


        Map<String, Object> cardProps = cardInfo.toMap();
        cardDataDTO.setCardProps(cardProps);
        messageEntity.setContent(JSON.toJSONString(streamEventDTOS));
        chatSessionMessageRepository.updateSessionMessage(messageEntity);
    }


    private List<Long> getHaiMaPrompt() {
        Map<String, String> field = Maps.newHashMap();
        field.put("key", "appointment_shop");
        List<HaimaContent> haimaContents = haimaAcl.getContent("medical_agent_appointment_blacklist", field);

        if (CollectionUtils.isEmpty(haimaContents)) {
            return Lists.newArrayList();
        }

        HaimaContent content = haimaContents.get(0);
        List<String> list = (List<String>) content.getContent("list");
        return list.stream().map(Long::parseLong).collect(Collectors.toList());
    }

    private TaskConfig getHaiMaPromptForAi(String task) {
        Map<String, String> field = Maps.newHashMap();
        field.put("task", task);
        List<HaimaContent> haimaContents = haimaAcl.getContent("medical_task_ai_config", field);

        if (CollectionUtils.isEmpty(haimaContents)) {
            return new TaskConfig();
        }

        HaimaContent content = haimaContents.get(0);
        TaskConfig config = JsonUtils.parseObject(content.getContentString("config"), TaskConfig.class);
        if (config == null) {
            config = new TaskConfig();
        }
        if (org.apache.commons.lang.StringUtils.isNotBlank(content.getContentString("systemPrompt"))) {
            config.setSystemPrompt(content.getContentString("systemPrompt"));
        }
        return config;
    }

    private String cleanJsonString(String jsonString) {
        if (org.apache.commons.lang3.StringUtils.isBlank(jsonString)) {
            return jsonString;
        }
        // 去掉开头的 ```json 或 ```
        jsonString = jsonString.replaceAll("^```(json)?\\s*", "");
        // 去掉结尾的 ```
        jsonString = jsonString.replaceAll("```\\s*$", "");

        // 去掉首尾空白字符
        return jsonString.trim();
    }
}
