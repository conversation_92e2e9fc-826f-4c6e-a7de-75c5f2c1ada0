package com.sankuai.dzhealth.ai.service.agent.domain.process.task.impl;

import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.util.MetricHelper;
import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import com.dianping.rhino.Rhino;
import com.dianping.rhino.threadpool.DefaultThreadPoolProperties;
import com.dianping.rhino.threadpool.ThreadPool;
import com.google.common.collect.Lists;
import com.meituan.beauty.fundamental.light.plat.Platform;
import com.meituan.mdp.boot.starter.config.annotation.MdpConfig;
import com.sankuai.beautycontent.experience.enums.FilterType;
import com.sankuai.dzhealth.ai.service.agent.domain.buffer.MessageBufferEntity;
import com.sankuai.dzhealth.ai.service.agent.domain.context.MessageContext;
import com.sankuai.dzhealth.ai.service.agent.domain.context.TaskContext;
import com.sankuai.dzhealth.ai.service.agent.domain.enums.TaskTypeEnum;
import com.sankuai.dzhealth.ai.service.agent.domain.model.DoctorRecommendation;
import com.sankuai.dzhealth.ai.service.agent.domain.model.ShopRecommendation;
import com.sankuai.dzhealth.ai.service.agent.domain.model.SupplyRecommendModel;
import com.sankuai.dzhealth.ai.service.agent.domain.model.TaskProcessResult;
import com.sankuai.dzhealth.ai.service.agent.domain.model.experiencereport.AiExperienceReports;
import com.sankuai.dzhealth.ai.service.agent.domain.model.experiencereport.ExperienceReportRequestModel;
import com.sankuai.dzhealth.ai.service.agent.domain.process.card.data.*;
import com.sankuai.dzhealth.ai.service.agent.domain.process.task.GeneralTask;
import com.sankuai.dzhealth.ai.service.agent.domain.process.task.Task;
import com.sankuai.dzhealth.ai.service.agent.domain.service.RecommendService;
import com.sankuai.dzhealth.ai.service.agent.domain.service.experiencereport.ExperienceReportDomainService;
import com.sankuai.dzhealth.ai.service.agent.domain.utils.BufferUtils;
import com.sankuai.dzhealth.ai.service.agent.domain.utils.UrlUtils;
import com.sankuai.dzhealth.ai.service.agent.enums.ContextExtraKey;
import com.sankuai.dzhealth.ai.service.domain.utils.JsonUtils;
import com.sankuai.dzhealth.ai.service.dto.SseAwareException;
import com.sankuai.dzhealth.ai.service.enums.StreamEventCardTypeEnum;
import com.sankuai.dzhealth.ai.service.enums.StreamEventErrorTypeEnum;
import com.sankuai.dzhealth.ai.service.request.common.BasicParam;
import com.sankuai.medicalcosmetology.display.api.DoctorInfoQueryService;
import com.sankuai.medicalcosmetology.display.dto.DoctorCardInfoAndCase;
import com.sankuai.medicalcosmetology.display.dto.DoctorCardInfoAndCaseBatchQueryResponse;
import com.sankuai.medicalcosmetology.display.enums.DoctorRecommendFilterEnum;
import com.sankuai.medicalcosmetology.display.request.DoctorRecommendListRecallRequest;
import com.sankuai.medicalcosmetology.mainpath.listingapi.common.ResponseDTO;
import com.sankuai.medicalcosmetology.mainpath.listingapi.dto.FillInfoDTO;
import com.sankuai.medicalcosmetology.mainpath.listingapi.dto.GoodsRecallDTO;
import com.sankuai.medicalcosmetology.mainpath.listingapi.dto.ReCallSortIdsDTO;
import com.sankuai.medicalcosmetology.mainpath.listingapi.dto.ShopGoodsRecallDTO;
import com.sankuai.medicalcosmetology.mainpath.listingapi.request.*;
import com.sankuai.medicalcosmetology.mainpath.listingapi.service.ListingFacade;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.Serializable;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.sankuai.dzhealth.ai.service.agent.domain.process.task.impl.MedicalProductTask.DEFAULT_HEAD_PIC;

/**
 * @author:chenwei
 * @time: 2025/7/17 19:38
 * @version: 0.0.1
 */
@Slf4j
@Component
public class RecommendListTask extends GeneralTask implements Task {

    public static final String SCENE_TYPE_LIST_PAGE = "ListPage";
    @Autowired
    private ListingFacade listingFacade;

    @Autowired
    private DoctorInfoQueryService doctorInfoQueryService;

    @Autowired
    private RecommendService recommendService;

    @Autowired
    private ExperienceReportDomainService experienceReportDomainService;

    @MdpConfig("health.recommend.list.template.key:health_recommend_list")
    private String templateKey;

    public static final String RECOMMEND_REASON = "推荐理由";


    public static final ThreadPool LIST_POOL = Rhino.newThreadPool("listPool",
            DefaultThreadPoolProperties.Setter().withCoreSize(100).withMaxSize(200).withMaxQueueSize(500));



    @Override
    public boolean accept(String type) {
        return TaskTypeEnum.RECOMMEND_LIST_TASK.getType().equals(type);
    }

    @Override
    public TaskProcessResult process(TaskContext context) {
        // 参数校验和提取
        ProcessParams params = validateAndExtractParams(context);

        log.info("params={},context={}", JsonUtils.toJsonString(params), JsonUtils.toJsonString(context));
        TaskProcessResult taskProcessResult = new TaskProcessResult();
        
        if (params.getSupplyRecommendModel().getSupplyType().equals(SupplyRecommendModel.SupplyType.DOCTOR.getCode())) {
            // 医生推荐
            processDoctorRecommendation(params, taskProcessResult);
        } else {
            // 商户推荐
            processShopRecommendation(params, taskProcessResult);
        }
        
        return taskProcessResult;
    }

    /**
     * 参数校验和提取
     */
    private ProcessParams validateAndExtractParams(TaskContext context) {
        Map<String, Serializable> extra = context.getMessageContext().getExtra();
        if (MapUtils.isEmpty(extra)) {
            throw new SseAwareException(StreamEventErrorTypeEnum.PARAM_ERROR);
        }
        
        String dynamicTab = (String) extra.get(ContextExtraKey.RECOMMEND_DYNAMIC_FILTER.getKey());
        String positionTab = (String) extra.get(ContextExtraKey.RECOMMEND_POSITION_FILTER.getKey());
        String filterParams = (String) extra.get(ContextExtraKey.FIlTER_PARAMS.getKey());

        String sortType = (String) extra.get(ContextExtraKey.SORT_TYPE.getKey());
        
        String areaId = JsonUtils.getString(filterParams, "areaId");
        String stationId = JsonUtils.getString(filterParams, "stationId");
        String distance = JsonUtils.getString(filterParams, "distance");
        int pageNo = NumberUtils.toInt((String) extra.get(ContextExtraKey.PAGE_NO.getKey()), 1);
        int pageSize = NumberUtils.toInt((String) extra.get(ContextExtraKey.PAGE_SIZE.getKey()), 5);

        SupplyRecommendModel supplyRecommendModel = JsonUtils.parseObject(dynamicTab, SupplyRecommendModel.class);
        if (supplyRecommendModel == null) {
            throw new SseAwareException(StreamEventErrorTypeEnum.PARAM_ERROR);
        }
        
        Pair<Double, Double> position = parsePosition(positionTab, supplyRecommendModel, context.getMessageContext().getBasicParam());
        
        List<Long> tagIds = parseTagIds(supplyRecommendModel.getMedicalCaseTag());
        
        return ProcessParams.builder()
                .context(context)
                .supplyRecommendModel(supplyRecommendModel)
                .position(position)
                .areaId(areaId)
                .stationId(stationId)
                .distance(distance)
                .pageNo(pageNo)
                .pageSize(pageSize)
                .tagIds(tagIds)
                .sortType(sortType)
                .build();
    }

    /**
     * 解析标签ID列表
     */
    private List<Long> parseTagIds(String medicalCaseTag) {
        List<Long> tagIds = new ArrayList<>();
        if (StringUtils.isNotBlank(medicalCaseTag)) {
            tagIds = Arrays.stream(medicalCaseTag.split(","))
                    .map(String::trim)
                    .filter(s -> !s.isEmpty() && NumberUtils.isDigits(s))
                    .map(Long::parseLong)
                    .distinct()
                    .collect(Collectors.toList());
        }
        return tagIds;
    }

    /**
     * 处理医生推荐
     */
    private void processDoctorRecommendation(ProcessParams params, TaskProcessResult taskProcessResult) {
        // 构建医生推荐请求
        DoctorRecommendListRecallRequest doctorRequest = buildDoctorRequest(params);
        
        // 调用医生推荐服务
        DoctorCardInfoAndCaseBatchQueryResponse recommendListResponse = doctorInfoQueryService.queryDoctorRecommendList(doctorRequest);
        log.info("[Listing]FirstDoctorRecommendListResponse:{}, req={}", JsonUtils.toJsonString(recommendListResponse), JsonUtils.toJsonString(doctorRequest));

        if (!recommendListResponse.getSuccess() || CollectionUtils.isEmpty(recommendListResponse.getData())) {
            throw new IllegalArgumentException("获取推荐医生失败");
        }

        List<Long> mergeDoctorIds = extractDoctorIds(recommendListResponse.getData());
        boolean hasFilter = StringUtils.isNotBlank(params.getAreaId()) || StringUtils.isNotBlank(params.getDistance())
                || StringUtils.isNotBlank(params.getStationId()) || StringUtils.isNotBlank(params.getSortType());

        // 处理置顶逻辑
        if (params.getPageNo() == 1 && !hasFilter) {
            String topId = params.getSupplyRecommendModel().getTopId();
            handleTopId(mergeDoctorIds, NumberUtils.toLong(topId));
        }
        MetricHelper.build().name("推荐供给数").tag("type", "doctor")
                .tag("sceneType", SCENE_TYPE_LIST_PAGE).value(mergeDoctorIds.size());
        log.info("[mergeDoctorIds]={}, params={}, ", mergeDoctorIds, JsonUtils.toJsonString(params));

        // 异步获取医生推荐理由和案例信息
        CompletableFuture<List<DoctorRecommendation>> doctorRecommendationsFuture = getDoctorRecommendationsFuture(mergeDoctorIds, params);
        Map<Long, CompletableFuture<CaseCardData>> doctorCaseCardFuturesMap = getDoctorCaseCardFuturesMap(mergeDoctorIds, params);

        // 构建医生卡片数据
        processDoctorCardData(recommendListResponse.getData(), doctorRecommendationsFuture, doctorCaseCardFuturesMap, mergeDoctorIds, params);
    }

    /**
     * 构建医生推荐请求
     */
    private DoctorRecommendListRecallRequest buildDoctorRequest(ProcessParams params) {
        DoctorRecommendListRecallRequest doctorRequest = new DoctorRecommendListRecallRequest();
        MessageContext messageContext = params.getContext().getMessageContext();
        SupplyRecommendModel model = params.getSupplyRecommendModel();
        
        doctorRequest.setAreaIds(StringUtils.isBlank(params.getAreaId()) ? null : Lists.newArrayList(params.getAreaId()));
        doctorRequest.setSubwayStationIds(StringUtils.isBlank(params.getStationId()) ? null : Lists.newArrayList(params.getStationId()));
        doctorRequest.setQuery(model.getSupplySearchWord());
        doctorRequest.setSortType(SupplyRecommendModel.DoctorSortType.fromDesc(model.getSortFilter()).getIntCode());
        if (StringUtils.isNotBlank(params.getSortType())) {
            doctorRequest.setSortType(SupplyRecommendModel.DoctorSortType.fromDesc(params.getSortType()).getIntCode());
        }
        doctorRequest.setFilters(getDoctorFilter(params));
        doctorRequest.setOffset((params.getPageNo() - 1) * params.getPageSize());
        doctorRequest.setLimit(params.getPageSize());
        doctorRequest.setPlatform(messageContext.getPlatform());
        doctorRequest.setClientType(messageContext.getBasicParam().getClientType());
        doctorRequest.setUuid(messageContext.getBasicParam().getUuid());
        doctorRequest.setUserId(messageContext.getBasicParam().getUserId());
        doctorRequest.setVersion(messageContext.getBasicParam().getAppVersion());
        doctorRequest.setClient(messageContext.getBasicParam().getClientType());
        doctorRequest.setLng(messageContext.getBasicParam().getLng());
        doctorRequest.setLat(messageContext.getBasicParam().getLat());
        doctorRequest.setCityId(messageContext.getBasicParam().getCityId());
        if (doctorRequest.getCityId() == null) {
            doctorRequest.setCityId(messageContext.getBasicParam().getUserCityId());
        }
        
        return doctorRequest;
    }

    private List<Integer> getDoctorFilter(ProcessParams params) {
        SupplyRecommendModel model = params.getSupplyRecommendModel();
        List<Integer> doctorFilter = new ArrayList<>();
        if (model == null || model.getDoctorFilter() == null) {
            doctorFilter.add(DoctorRecommendFilterEnum.DEFAULT.getCode());
        } else {
            doctorFilter.add(DoctorRecommendFilterEnum.getByCode(model.getDoctorFilter()).getCode());
        }
        if (StringUtils.isNotBlank(params.getAreaId())) {
            doctorFilter.add(DoctorRecommendFilterEnum.AREA.getCode());
        } else if (StringUtils.isNotBlank(params.getStationId())) {
            doctorFilter.add(DoctorRecommendFilterEnum.SUBWAY_STATION.getCode());
        }
        return doctorFilter;

    }

    /**
     * 提取医生ID列表
     */
    private List<Long> extractDoctorIds(List<DoctorCardInfoAndCase> data) {
        return data.stream()
                .filter(Objects::nonNull)
                .map(DoctorCardInfoAndCase::getMergeDoctorId)
                .collect(Collectors.toList());
    }

    /**
     * 异步获取医生推荐理由
     */
    private CompletableFuture<List<DoctorRecommendation>> getDoctorRecommendationsFuture(List<Long> mergeDoctorIds, ProcessParams params) {
        String reason = StringUtils.isNotBlank(params.getSupplyRecommendModel().getSupplySearchWord()) ? params.getSupplyRecommendModel().getSupplySearchWord() :
                Optional.ofNullable(params.getSupplyRecommendModel().getSupplyName()).orElse("");

        return CompletableFuture.supplyAsync(() -> {
            return recommendService.recommendDoctorInfo(mergeDoctorIds, params.getContext().getMessageContext(), SCENE_TYPE_LIST_PAGE, reason);
        }, LIST_POOL.getExecutor());
    }

    /**
     * 异步获取医生案例信息
     */
    private Map<Long, CompletableFuture<CaseCardData>> getDoctorCaseCardFuturesMap(List<Long> mergeDoctorIds, ProcessParams params) {
        Map<Long, CompletableFuture<CaseCardData>> doctorCaseCardFuturesMap = new HashMap<>();
        
        if (StringUtils.isNotBlank(params.getSupplyRecommendModel().getMedicalCaseTag())) {
            for (Long doctorId : mergeDoctorIds) {
                CompletableFuture<CaseCardData> caseCardDataFuture = CompletableFuture.supplyAsync(() -> {
                    return getDoctorCaseCardData(doctorId, params.getTagIds(), params.getContext());
                }, LIST_POOL.getExecutor());
                
                doctorCaseCardFuturesMap.put(doctorId, caseCardDataFuture);
            }
        }
        
        return doctorCaseCardFuturesMap;
    }

    /**
     * 获取医生案例信息
     */
    private CaseCardData getDoctorCaseCardData(Long doctorId, List<Long> tagIds, TaskContext context) {
        CaseCardData caseCardData = new CaseCardData();
        ExperienceReportRequestModel requestModel = ExperienceReportRequestModel
                .builder()
                .mergeDoctorId(doctorId)
                .aiRecommendTags(tagIds)
                .platform(Optional.ofNullable(context.getMessageContext()).map(MessageContext::getPlatform).orElse(Platform.MT.getCode()))
                .type(FilterType.AI_CONSULTANT.getCode())
                .offset(0)
                .limit(4)
                .build();
        try {
            AiExperienceReports aiExperienceReportListInfo = experienceReportDomainService.getAiExperienceReportListInfo(requestModel);
            log.info("[Listing]doctorReport,req={},res={}", JsonUtils.toJsonString(requestModel), JsonUtils.toJsonString(aiExperienceReportListInfo));

            if (aiExperienceReportListInfo != null) {
                caseCardData.setCaseInfoList(aiExperienceReportListInfo.getCaseList());
                caseCardData.setCount(Optional.ofNullable(aiExperienceReportListInfo.getTotalCount()).map(Long::valueOf).orElse(0L));
                caseCardData.setTitle(aiExperienceReportListInfo.getTitle());
                caseCardData.setJumpUrl(aiExperienceReportListInfo.getDetailUrl());
            }
        } catch (Exception ex) {
            log.error("[Listing]doctorRequestModel={}", JsonUtils.toJsonString(requestModel), ex);
        }
        return caseCardData;
    }

    /**
     * 处理医生卡片数据
     */
    private void processDoctorCardData(List<DoctorCardInfoAndCase> doctorData,
                                     CompletableFuture<List<DoctorRecommendation>> doctorRecommendationsFuture,
                                     Map<Long, CompletableFuture<CaseCardData>> doctorCaseCardFuturesMap, List<Long> mergeDoctorIds, ProcessParams params) {

        Map<Long, DoctorCardInfoAndCase> doctorIdMap = doctorData.stream()
                .collect(Collectors.toMap(DoctorCardInfoAndCase::getMergeDoctorId, Function.identity(), (a, b) -> a));

        // 构建医生卡片数据
        List<DoctorProductCardData> doctorProductCardDataList = buildDoctorProductCardDataList(mergeDoctorIds, doctorIdMap, doctorCaseCardFuturesMap, params);

        Map<Long, DoctorProductCardData> doctorProductCardDataMap = doctorProductCardDataList.stream()
                .filter(e -> e.getDetail() != null)
                .collect(Collectors.toMap(e -> e.getDetail().getMergeDoctorId(), Function.identity(), (a, b) -> a));

        // 处理推荐理由
        List<DoctorRecommendation> doctorRecommendations = doctorRecommendationsFuture.join();
        log.info("[Listing]SecondDoctorRecommendListResponse:{}", JsonUtils.toJsonString(doctorRecommendations));
        
        updateDoctorCardDataWithRecommendations(doctorRecommendations, doctorProductCardDataMap);
    }

    private void processDoctorCardInfoAndCase(DoctorCardInfoAndCase doctorCardInfoAndCase) {
        if (doctorCardInfoAndCase != null && doctorCardInfoAndCase.getShopInfo() != null) {
            String distance = doctorCardInfoAndCase.getShopInfo().getDistance();
            doctorCardInfoAndCase.getShopInfo().setDistance(processDistance(distance));
        }

    }

    /**
     * 构建医生产品卡片数据列表
     */
    private List<DoctorProductCardData> buildDoctorProductCardDataList(List<Long> mergeDoctorIds,
                                                                      Map<Long, DoctorCardInfoAndCase> doctorIdMap,
                                                                      Map<Long, CompletableFuture<CaseCardData>> doctorCaseCardFuturesMap,
                                                                       ProcessParams params) {
        return mergeDoctorIds.stream().map(doctorId -> {
            DoctorProductCardData doctorProductCardData = new DoctorProductCardData();
            
            // 设置医生详情
            if (doctorIdMap.containsKey(doctorId)) {
                DoctorCardInfoAndCase doctorCardInfoAndCase = doctorIdMap.get(doctorId);
                processDoctorCardInfoAndCase(doctorCardInfoAndCase);
                doctorProductCardData.setDetail(doctorCardInfoAndCase);
            } else {
                doctorProductCardData.setDetail(params.getSupplyRecommendModel().getDoctorCardInfoAndCase());
            }

            // 获取案例信息
            setCaseCardData(doctorProductCardData, doctorId, doctorCaseCardFuturesMap);

            // 发送卡片数据
            BufferUtils.writeMainTextBuffer(MessageBufferEntity.builder()
                    .data(StreamEventCardTypeEnum.buildCardContent(StreamEventCardTypeEnum.DOCTOR_CARD, String.valueOf(doctorId)))
                    .extra(doctorProductCardData.toMap())
                    .build(), null);

            return doctorProductCardData;
        }).collect(Collectors.toList());
    }

    /**
     * 设置案例卡片数据
     */
    private void setCaseCardData(DoctorProductCardData doctorProductCardData, Long doctorId,
                               Map<Long, CompletableFuture<CaseCardData>> doctorCaseCardFuturesMap) {
        CompletableFuture<CaseCardData> caseCardDataFuture = doctorCaseCardFuturesMap.get(doctorId);
        if (caseCardDataFuture != null) {
            try {
                CaseCardData caseCardData = caseCardDataFuture.join();
                doctorProductCardData.setReport(caseCardData);
            } catch (Exception ex) {
                log.error("[Listing]获取doctorId={}的案例信息失败", doctorId, ex);
            }
        }
    }

    /**
     * 更新医生卡片数据的推荐信息
     */
    private void updateDoctorCardDataWithRecommendations(List<DoctorRecommendation> doctorRecommendations,
                                                        Map<Long, DoctorProductCardData> doctorProductCardDataMap) {
        doctorRecommendations.stream().filter(Objects::nonNull).forEach(doctorRecommendation -> {
            DoctorProductCardData doctorProductCardData = doctorProductCardDataMap.get(doctorRecommendation.getDoctorId());
            if (doctorProductCardData != null) {
                updateDoctorProductCardData(doctorProductCardData, doctorRecommendation);
                
                BufferUtils.writeMainTextBuffer(MessageBufferEntity.builder()
                        .data(StreamEventCardTypeEnum.buildCardContent(StreamEventCardTypeEnum.DOCTOR_CARD, String.valueOf(doctorRecommendation.getDoctorId())))
                        .extra(doctorProductCardData.toMap())
                        .build(), null);
            }
            log.info("[Listing]DoctorProductCardData:{}", JsonUtils.toJsonString(doctorProductCardData));
        });
    }

    /**
     * 更新医生产品卡片数据
     */
    private void updateDoctorProductCardData(DoctorProductCardData doctorProductCardData, DoctorRecommendation doctorRecommendation) {
        doctorProductCardData.setReason(doctorRecommendation.getReason());
        
        // 设置产品信息
        DoctorProductSimpleData simpleData = new DoctorProductSimpleData();
        simpleData.setName(doctorRecommendation.getName());
        String finalPrice = formatPrice(doctorRecommendation.getPromoPrice());
        simpleData.setPromoPrice(finalPrice);
        simpleData.setDetailLink(doctorRecommendation.getDetailLink());
        simpleData.setSaleNum("已售" + doctorRecommendation.getSaleNum());
        simpleData.setProductId(NumberUtils.toLong(UrlUtils.getParamValue(simpleData.getDetailLink(), "productid")));
        doctorProductCardData.setProduct(simpleData);
        doctorProductCardData.setReasonPrefix(RECOMMEND_REASON);
        
        // 设置评价信息
        setReviewData(doctorProductCardData, doctorRecommendation);
    }

    /**
     * 设置评价数据
     */
    private void setReviewData(DoctorProductCardData doctorProductCardData, DoctorRecommendation doctorRecommendation) {
        if (CollectionUtils.isNotEmpty(doctorRecommendation.getReviewURL()) && 
            CollectionUtils.isNotEmpty(doctorRecommendation.getReviewContext())) {
            
            List<String> reviewUrls = doctorRecommendation.getReviewURL();
            List<String> reviewContents = doctorRecommendation.getReviewContext();
            doctorProductCardData.setCount((long) reviewUrls.size());

            List<ReferData> refer = new ArrayList<>();
            for (int i = 0; i < reviewUrls.size(); i++) {
                ReferData referData = new ReferData();
                referData.setUrl(reviewUrls.get(i));
                referData.setContent(reviewContents.get(i));
                referData.setSource("review");
                referData.setHeadPic(DEFAULT_HEAD_PIC);
                refer.add(referData);
            }
            doctorProductCardData.setRefer(refer);
        }
    }

    /**
     * 处理商户推荐
     */
    private void processShopRecommendation(ProcessParams params, TaskProcessResult taskProcessResult) {
        // 构建商户推荐请求
        ReCallSortIdsQry reCallSortIdsQry = buildShopRecommendRequest(params);
        
        // 调用商户推荐服务
        ResponseDTO<ReCallSortIdsDTO> response = listingFacade.reCallSortIds(reCallSortIdsQry);
        log.info("<recommendListTask>ReCallSortIdsDTO:{}, req={}", JsonUtils.toJsonString(response), JsonUtils.toJsonString(reCallSortIdsQry));
        
        if (!response.isSuccess() || response.getData() == null || CollectionUtils.isEmpty(response.getData().getShopGoodsRecallDTOs())) {
            return;
        }

        List<ShopGoodsRecallDTO> shopGoodsRecallDTOs = response.getData().getShopGoodsRecallDTOs();
        List<Long> shopIds = shopGoodsRecallDTOs.stream().map(ShopGoodsRecallDTO::getShopId).collect(Collectors.toList());

        boolean hasFilter = StringUtils.isNotBlank(params.getAreaId()) || StringUtils.isNotBlank(params.getDistance())
                || StringUtils.isNotBlank(params.getStationId()) || StringUtils.isNotBlank(params.getSortType());
        // 处理置顶逻辑
        if (params.getPageNo() == 1 && !hasFilter) {
            String topId = params.getSupplyRecommendModel().getTopId();
            handleTopId(shopIds, NumberUtils.toLong(topId));
        }

        MetricHelper.build().name("推荐供给数").tag("type", "shop")
                .tag("sceneType", SCENE_TYPE_LIST_PAGE).value(shopIds.size());
        log.info("[shopIds]={}, params={}", JsonUtils.toJsonString(shopIds), JsonUtils.toJsonString(params));

        // 异步获取案例信息和推荐理由
        Map<Long, CompletableFuture<CaseCardData>> caseCardFuturesMap = getShopCaseCardFuturesMap(shopIds, params);
        CompletableFuture<List<ShopRecommendation>> shopRecommendationsFuture = getShopRecommendationsFuture(shopIds, params);

        // 批量获取商户详情信息
        Map<Long, FillInfoDTO> shopId2FillInfoMap = getShopFillInfoMap(shopIds, shopGoodsRecallDTOs, params);

        // 构建商户卡片数据
        processShopCardData(shopIds, shopId2FillInfoMap, caseCardFuturesMap, shopRecommendationsFuture, params.getSupplyRecommendModel().getSupplySearchWord());
    }

    /**
     * 构建商户推荐请求
     */
    private ReCallSortIdsQry buildShopRecommendRequest(ProcessParams params) {
        ReCallSortIdsQry reCallSortIdsQry = new ReCallSortIdsQry();
        MessageContext messageContext = params.getContext().getMessageContext();
        SupplyRecommendModel model = params.getSupplyRecommendModel();
        
        reCallSortIdsQry.setUserId(messageContext.getBasicParam().getUserId());
        reCallSortIdsQry.setPlatform(messageContext.getPlatform());
        reCallSortIdsQry.setCityId(messageContext.getBasicParam().getCityId());
        if (messageContext.getBasicParam().getCityId() == null || messageContext.getBasicParam().getCityId() <= 0) {
            reCallSortIdsQry.setCityId(messageContext.getBasicParam().getUserCityId());
        }
        
        String queryStr = StringUtils.isBlank(model.getSupplySearchWord()) ? model.getSupplyName() : model.getSupplySearchWord();
        queryStr = queryStr.split(" ")[0];
        reCallSortIdsQry.setQueryStr(queryStr);
        reCallSortIdsQry.setTemplateKey(templateKey);
        reCallSortIdsQry.setUuid(messageContext.getBasicParam().getUuid());
        reCallSortIdsQry.setSortType(SupplyRecommendModel.ShopSortType.fromCode(model.getSortFilter()).getCode());
        if (StringUtils.isNotBlank(params.getSortType())) {
            reCallSortIdsQry.setSortType(params.getSortType());
        }
        reCallSortIdsQry.setLat(params.getPosition() == null ? null : params.getPosition().getRight());
        reCallSortIdsQry.setLng(params.getPosition() == null ? null : params.getPosition().getLeft());
        
        if (isValidTagFilter(model.getShopTagFilter())) {
            reCallSortIdsQry.setLabels(model.getShopTagFilter());
        }
        
        reCallSortIdsQry.setPoiBackCateId(StringUtils.isBlank(model.getSupplyCate()) ? "20423" : model.getSupplyCate());
        reCallSortIdsQry.setPrices(model.getPriceRangeFilter());
        reCallSortIdsQry.setPageNum(params.getPageNo() - 1);
        reCallSortIdsQry.setPageSize(params.getPageSize());
        reCallSortIdsQry.setSubwayStation(params.getStationId());
        reCallSortIdsQry.setAreaId(params.getAreaId());
        
        if (StringUtils.isNotBlank(params.getDistance())) {
            reCallSortIdsQry.setDistance(params.getDistance());
        }
        
        return reCallSortIdsQry;
    }

    private boolean isValidTagFilter(String str) {
        if (StringUtils.isBlank(str)) {
            return true;
        }
        return str.matches("[0-9,;^]+");
    }

    /**
     * 异步获取商户案例信息
     */
    private Map<Long, CompletableFuture<CaseCardData>> getShopCaseCardFuturesMap(List<Long> shopIds, ProcessParams params) {
        Map<Long, CompletableFuture<CaseCardData>> caseCardFuturesMap = new HashMap<>();

        if (StringUtils.isNotBlank(params.getSupplyRecommendModel().getMedicalCaseTag())) {
            for (Long shopId : shopIds) {
                CompletableFuture<CaseCardData> caseCardFuture = CompletableFuture.supplyAsync(() -> {
                    return getShopCaseCardData(shopId, params.getTagIds(), params.getContext());
                }, LIST_POOL.getExecutor());

                caseCardFuturesMap.put(shopId, caseCardFuture);
            }
        }
        
        return caseCardFuturesMap;
    }

    /**
     * 获取商户案例信息
     */
    private CaseCardData getShopCaseCardData(Long shopId, List<Long> tagIds, TaskContext context) {
        CaseCardData caseCardData = new CaseCardData();
        ExperienceReportRequestModel requestModel = ExperienceReportRequestModel.builder()
                .shopId(shopId)
                .aiRecommendTags(tagIds)
                .platform(Optional.ofNullable(context.getMessageContext()).map(MessageContext::getPlatform).orElse(Platform.MT.getCode()))
                .type(FilterType.AI_CONSULTANT.getCode())
                .offset(0)
                .limit(4)
                .build();
        try {
            AiExperienceReports aiExperienceReportListInfo = experienceReportDomainService.getAiExperienceReportListInfo(requestModel);
            log.info("batchReport,req={},res={}", JsonUtils.toJsonString(requestModel), JsonUtils.toJsonString(aiExperienceReportListInfo));

            if (aiExperienceReportListInfo != null) {
                caseCardData.setCaseInfoList(aiExperienceReportListInfo.getCaseList());
                caseCardData.setCount(Optional.ofNullable(aiExperienceReportListInfo.getTotalCount()).map(Long::valueOf).orElse(0L));
                caseCardData.setTitle(aiExperienceReportListInfo.getTitle());
                caseCardData.setJumpUrl(aiExperienceReportListInfo.getDetailUrl());
            }
        } catch (Exception ex) {
            log.error("batchRequestModel={}", JsonUtils.toJsonString(requestModel), ex);
        }
        return caseCardData;
    }

    /**
     * 异步获取商户推荐理由
     */
    private CompletableFuture<List<ShopRecommendation>> getShopRecommendationsFuture(List<Long> shopIds, ProcessParams params) {
        String reason = StringUtils.isNotBlank(params.getSupplyRecommendModel().getSupplySearchWord()) ?
                params.getSupplyRecommendModel().getSupplySearchWord() :
                params.getSupplyRecommendModel().getSupplyName();
        return CompletableFuture.supplyAsync(() ->
                recommendService.recommendShopInfo(shopIds, reason, SCENE_TYPE_LIST_PAGE, params.getSupplyRecommendModel().getSupplyName()),
                LIST_POOL.getExecutor());
    }

    /**
     * 获取商户详情信息映射
     */
    private Map<Long, FillInfoDTO> getShopFillInfoMap(List<Long> shopIds, List<ShopGoodsRecallDTO> shopGoodsRecallDTOs, ProcessParams params) {
        Map<Long, ShopGoodsRecallDTO> shopGoodsRecallDTOMap = shopGoodsRecallDTOs.stream()
                .collect(Collectors.toMap(ShopGoodsRecallDTO::getShopId, Function.identity(), (a, b) -> a));

        // 构建批量FillInfo请求
        List<com.sankuai.medicalcosmetology.mainpath.listingapi.request.ShopQry> batchShopQryList = new ArrayList<>();
        List<GoodsQry> batchGoodsQryList = new ArrayList<>();
        Map<Long, Long> shop2GoodsMap = new HashMap<>();
        
        buildBatchQueries(shopIds, shopGoodsRecallDTOMap, params.getSupplyRecommendModel(), 
                         batchShopQryList, batchGoodsQryList, shop2GoodsMap);

        // 批量调用fillInfo
        FillInfoQry batchFillInfoQry = new FillInfoQry();
        batchFillInfoQry.setShopQryList(batchShopQryList);
        batchFillInfoQry.setGoodsQryList(batchGoodsQryList);
        buildBatchFillInfoQuery(batchFillInfoQry, params.getContext().getMessageContext());
        
        ResponseDTO<FillInfoDTO> batchFillInfoResponse = listingFacade.fillInfo(batchFillInfoQry);
        log.info("[Listing]batchFillInfoResponse={},req={}", JsonUtils.toJsonString(batchFillInfoResponse), JsonUtils.toJsonString(batchFillInfoQry));
        
        return buildShopFillInfoMap(batchFillInfoResponse, shop2GoodsMap);
    }

    /**
     * 构建批量查询请求
     */
    private void buildBatchQueries(List<Long> shopIds, Map<Long, ShopGoodsRecallDTO> shopGoodsRecallDTOMap,
                                  SupplyRecommendModel supplyRecommendModel,
                                  List<com.sankuai.medicalcosmetology.mainpath.listingapi.request.ShopQry> batchShopQryList,
                                  List<GoodsQry> batchGoodsQryList,
                                  Map<Long, Long> shop2GoodsMap) {
        for (Long shopId : shopIds) {
            ShopGoodsRecallDTO shopGoodsRecallDTO;
            // 如果shopId等于supplyRecommendModel里的topId，必须使用createDefaultShopGoodsRecallDTO
            if (supplyRecommendModel != null &&
                StringUtils.isNotBlank(supplyRecommendModel.getTopId()) &&
                shopId.equals(NumberUtils.toLong(supplyRecommendModel.getTopId()))) {
                shopGoodsRecallDTO = createDefaultShopGoodsRecallDTO(shopId, supplyRecommendModel);
            } else {
                shopGoodsRecallDTO = shopGoodsRecallDTOMap.getOrDefault(shopId, createDefaultShopGoodsRecallDTO(shopId, supplyRecommendModel));
            }
            
            // 添加shop查询
            com.sankuai.medicalcosmetology.mainpath.listingapi.request.ShopQry shopQry = new com.sankuai.medicalcosmetology.mainpath.listingapi.request.ShopQry();
            shopQry.setShopId(shopId);
            batchShopQryList.add(shopQry);
            
            // 添加goods查询并维护映射关系
            if (CollectionUtils.isNotEmpty(shopGoodsRecallDTO.getShopGoodsRecallDTOs())) {
                GoodsRecallDTO goodsRecallDTO = shopGoodsRecallDTO.getShopGoodsRecallDTOs().get(0);
                GoodsQry goodsQry = new GoodsQry();
                goodsQry.setGoodsId(goodsRecallDTO.getGoodsId());
                goodsQry.setGoodsType(goodsRecallDTO.getGoodsType());
                GoodsShopQry goodsShopQry = new GoodsShopQry();
                goodsShopQry.setShopId(shopId);
                goodsQry.setShopQry(goodsShopQry);
                batchGoodsQryList.add(goodsQry);
                
                // 维护商户ID到商品ID的映射
                shop2GoodsMap.put(shopId, goodsRecallDTO.getGoodsId());
            }
        }
    }

    /**
     * 构建商户详情信息映射
     */
    private Map<Long, FillInfoDTO> buildShopFillInfoMap(ResponseDTO<FillInfoDTO> batchFillInfoResponse, Map<Long, Long> shop2GoodsMap) {
        Map<Long, FillInfoDTO> shopId2FillInfoMap = new HashMap<>();
        
        if (!batchFillInfoResponse.isSuccess() || batchFillInfoResponse.getData() == null) {
            return shopId2FillInfoMap;
        }

        FillInfoDTO batchFillInfoDTO = batchFillInfoResponse.getData();
        
        // 构建商品ID到商户ID的反向映射（支持一个商品对应多个商户）
        Map<Long, List<Long>> goods2ShopsMap = new HashMap<>();
        for (Map.Entry<Long, Long> entry : shop2GoodsMap.entrySet()) {
            Long shopId = entry.getKey();
            Long goodsId = entry.getValue();
            goods2ShopsMap.computeIfAbsent(goodsId, k -> new ArrayList<>()).add(shopId);
        }
        
        // 将商品按商户ID分组
        Map<Long, List<com.sankuai.medicalcosmetology.mainpath.listingapi.dto.GoodsInfoDTO>> goodsByShopMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(batchFillInfoDTO.getGoodsInfoDTOs())) {
            for (com.sankuai.medicalcosmetology.mainpath.listingapi.dto.GoodsInfoDTO goods : batchFillInfoDTO.getGoodsInfoDTOs()) {
                List<Long> shopIds = goods2ShopsMap.get(goods.getGoodsId());
                if (CollectionUtils.isNotEmpty(shopIds)) {
                    for (Long shopId : shopIds) {
                        goodsByShopMap.computeIfAbsent(shopId, k -> new ArrayList<>()).add(goods);
                    }
                }
            }
        }
        
        // 为每个商户构建独立的FillInfoDTO
        if (CollectionUtils.isNotEmpty(batchFillInfoDTO.getShopInfos())) {
            for (com.sankuai.medicalcosmetology.mainpath.listingapi.dto.ShopInfoDTO shopInfo : batchFillInfoDTO.getShopInfos()) {
                FillInfoDTO individualFillInfo = new FillInfoDTO();
                individualFillInfo.setShopInfos(Lists.newArrayList(shopInfo));
                
                // 设置该商户对应的商品信息
                List<com.sankuai.medicalcosmetology.mainpath.listingapi.dto.GoodsInfoDTO> shopGoods = goodsByShopMap.get(shopInfo.getShopId());
                if (CollectionUtils.isNotEmpty(shopGoods)) {
                    // 直接修改商品信息中的goodsRedirectUrl
                    for (com.sankuai.medicalcosmetology.mainpath.listingapi.dto.GoodsInfoDTO goods : shopGoods) {
                        String originalUrl = goods.getGoodsRedirectUrl();
                        String updatedUrl = replaceShopIdInUrl(originalUrl, shopInfo.getShopId());
                        goods.setGoodsRedirectUrl(updatedUrl);
                    }
                    individualFillInfo.setGoodsInfoDTOs(shopGoods);
                }
                
                shopId2FillInfoMap.put(shopInfo.getShopId(), individualFillInfo);
            }
        }
        
        return shopId2FillInfoMap;
    }

    /**
     * 处理商户卡片数据
     */
    private void processShopCardData(List<Long> shopIds, 
                                   Map<Long, FillInfoDTO> shopId2FillInfoMap,
                                   Map<Long, CompletableFuture<CaseCardData>> caseCardFuturesMap,
                                   CompletableFuture<List<ShopRecommendation>> shopRecommendationsFuture,
                                     String searchWord) {
        
        // 构建ShopProductCardData列表
        List<ShopProductCardData> shopProductCardDataList = buildShopProductCardDataList(shopIds, shopId2FillInfoMap, caseCardFuturesMap, searchWord);

        Map<Long, ShopProductCardData> shopId2ProductMap = shopProductCardDataList.stream()
                .filter(e -> e.getDetail() != null && CollectionUtils.isNotEmpty(e.getDetail().getShopInfos()))
                .collect(Collectors.toMap(e -> e.getDetail().getShopInfos().get(0).getShopId(),
                        Function.identity(), (a, b) -> a));

        // 处理推荐理由
        List<ShopRecommendation> shopRecommendations = shopRecommendationsFuture.join();
        log.info("[Listing]ShopRecommendation:{}", JsonUtils.toJsonString(shopRecommendations));

        updateShopCardDataWithRecommendations(shopRecommendations, shopId2ProductMap);
    }

    /**
     * 构建商户产品卡片数据列表
     */
    private List<ShopProductCardData> buildShopProductCardDataList(List<Long> shopIds,
                                                                  Map<Long, FillInfoDTO> shopId2FillInfoMap,
                                                                  Map<Long, CompletableFuture<CaseCardData>> caseCardFuturesMap,
                                                                   String searchWord) {
        return shopIds.stream().map(shopId -> {
            ShopProductCardData shopProductCardData = new ShopProductCardData();
            
            // 设置商户详情信息
            FillInfoDTO fillInfoDTO = shopId2FillInfoMap.get(shopId);
            if (fillInfoDTO != null) {
                formatShopPriceAndDistance(fillInfoDTO, searchWord);
                shopProductCardData.setDetail(fillInfoDTO);
            }

            // 获取案例信息
            setShopCaseCardData(shopProductCardData, shopId, caseCardFuturesMap);

            // 发送卡片数据到前端
            BufferUtils.writeMainTextBuffer(MessageBufferEntity.builder()
                    .data(StreamEventCardTypeEnum.buildCardContent(
                            StreamEventCardTypeEnum.SHOP_PRODUCT_CARD,
                            String.valueOf(shopId)))
                    .extra(shopProductCardData.toMap())
                    .build(), null);
            
            return shopProductCardData;
        }).collect(Collectors.toList());
    }

    /**
     * 格式化商户商品价格
     */
    private void formatShopPriceAndDistance(FillInfoDTO fillInfoDTO, String searchWord) {
        if (CollectionUtils.isNotEmpty(fillInfoDTO.getGoodsInfoDTOs())) {
            final String[] productUrl = new String[1];
            fillInfoDTO.getGoodsInfoDTOs().forEach(goodsInfoDTO -> {
                String finalPrice = goodsInfoDTO.getFinalPrice();
                if (finalPrice != null && finalPrice.contains(".")) {
                    finalPrice = formatPrice(finalPrice);
                }
                productUrl[0] = goodsInfoDTO.getGoodsRedirectUrl();
                goodsInfoDTO.setFinalPrice(finalPrice);
            });
            fillInfoDTO.getShopInfos().forEach(shopInfoDTO -> {
                shopInfoDTO.setShopJumpUrl(getAnchorUrl(shopInfoDTO.getShopJumpUrl(), productUrl[0], searchWord));
                shopInfoDTO.setShopDistance(processDistance(shopInfoDTO.getShopDistance()));
            });
        }
    }

    private String processDistance(String distance) {
        if (StringUtils.isBlank(distance)) {
            return distance;
        }

        try {
            // 移除所有空格
            String cleanDistance = distance.trim().replaceAll("\\s+", "");

            // 匹配数字部分，支持≥符号
            String numberStr = cleanDistance.replaceAll("[^0-9.]", "");
            if (StringUtils.isBlank(numberStr)) {
                return distance; // 无法解析数字，返回原字符串
            }

            double distanceValue = Double.parseDouble(numberStr);

            // 判断单位并转换为km
            if (cleanDistance.toLowerCase().contains("km")) {
                // 已经是km单位
                if (distanceValue >= 30) {
                    return StringUtils.EMPTY; // 距离≥30km，置空
                }
            } else if (cleanDistance.toLowerCase().contains("m")) {
                // 米单位转换为km
                double distanceInKm = distanceValue / 1000.0;
                if (distanceInKm >= 30) {
                    return StringUtils.EMPTY; // 距离≥30km，置空
                }
            } else {
                // 默认当作km处理
                if (distanceValue >= 30) {
                    return StringUtils.EMPTY; // 距离≥30km，置空
                }
            }

            return distance; // 距离<30km，返回原字符串

        } catch (Exception e) {
            log.error("距离字符串解析异常，返回原始值={}", distance, e);
            return distance; // 解析失败，返回原字符串
        }
    }

    private String formatPrice(String price) {
        if (StringUtils.isBlank(price) || !price.contains(".")) {
            return price;
        }

        try {
            String[] parts = price.split("\\.");
            if (parts.length != 2) {
                return price;
            }

            String integerPart = parts[0];
            String decimalPart = parts[1];

            // 如果小数部分为00，则只返回整数部分
            if (decimalPart.equals("00") || decimalPart.equals("0")) {
                return integerPart;
            } else {
                // 只保留第一位小数
                char firstDecimal = decimalPart.charAt(0);
                return integerPart + "." + firstDecimal;
            }
        } catch (Exception e) {
            log.error("价格格式异常，返回原始值: {}", price, e);
            return price;
        }
    }



    /**
     * 设置商户案例卡片数据
     */
    private void setShopCaseCardData(ShopProductCardData shopProductCardData, Long shopId,
                                   Map<Long, CompletableFuture<CaseCardData>> caseCardFuturesMap) {
        CompletableFuture<CaseCardData> caseCardFuture = caseCardFuturesMap.get(shopId);
        if (caseCardFuture != null) {
            try {
                CaseCardData caseCardData = caseCardFuture.join();
                if (caseCardData != null && CollectionUtils.isNotEmpty(caseCardData.getCaseInfoList())) {
                    shopProductCardData.setReport(caseCardData);
                }
            } catch (Exception ex) {
                log.error("获取shopId={}的案例信息失败", shopId, ex);
            }
        }
    }

    /**
     * 更新商户卡片数据的推荐信息
     */
    private void updateShopCardDataWithRecommendations(List<ShopRecommendation> shopRecommendations,
                                                      Map<Long, ShopProductCardData> shopId2ProductMap) {
        shopRecommendations.forEach(recommendation -> {
            ShopProductCardData shopProductCardData = shopId2ProductMap.get(recommendation.getShopId());
            if (shopProductCardData != null) {
                updateShopProductCardData(shopProductCardData, recommendation);
                
                BufferUtils.writeMainTextBuffer(MessageBufferEntity.builder().data(
                        StreamEventCardTypeEnum.buildCardContent(
                                StreamEventCardTypeEnum.SHOP_PRODUCT_CARD,
                                String.valueOf(recommendation.getShopId()))).extra(shopProductCardData.toMap()).build(), null);
            }
            log.info("[Listing]ShopProductCardData:{}", JsonUtils.toJsonString(shopProductCardData));
        });
    }

    /**
     * 更新商户产品卡片数据
     */
    private void updateShopProductCardData(ShopProductCardData shopProductCardData, ShopRecommendation recommendation) {
        if (StringUtils.isNotBlank(recommendation.getReason())) {
            shopProductCardData.setReason(recommendation.getReason());
            shopProductCardData.setReasonPrefix(RECOMMEND_REASON);
        }
        
        if (CollectionUtils.isNotEmpty(recommendation.getReviewIds()) && 
            CollectionUtils.isNotEmpty(recommendation.getReviewContext())) {
            
            List<ReferData> refer = new ArrayList<>();
            List<String> reviewUrls = recommendation.getReviewUrl();
            List<String> reviewContents = recommendation.getReviewContext();
            
            for (int i = 0; i < reviewUrls.size(); i++) {
                ReferData referData = new ReferData();
                referData.setUrl(reviewUrls.get(i));
                referData.setContent(reviewContents.get(i));
                referData.setSource("review");
                referData.setHeadPic(DEFAULT_HEAD_PIC);
                refer.add(referData);
            }
            shopProductCardData.setRefer(refer);
            shopProductCardData.setCount((long) reviewUrls.size());
        }
    }

    /**
     * 处理参数封装类
     */
    @Builder
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    private static class ProcessParams {
        private TaskContext context;
        private SupplyRecommendModel supplyRecommendModel;
        private Pair<Double, Double> position;
        private String areaId;
        private String stationId;
        private String distance;
        private int pageNo;
        private int pageSize;
        private List<Long> tagIds;
        private String sortType;
    }

    private void handleTopId(List<Long> ids, Long topId) {
        if (topId == null || topId <= 0 || ids == null || ids.isEmpty()) {
            return;
        }

        // 检查topId是否已经在列表中
        int existingIndex = ids.indexOf(topId);

        if (existingIndex >= 0) {
            // topId已存在，将其移动到第一位
            if (existingIndex != 0) {
                ids.remove(existingIndex);
                ids.add(0, topId);
            }
        } else {
            // topId不存在，添加到第一位并移除最后一位
            ids.add(0, topId);
            if (ids.size() > 1) {
                ids.remove(ids.size() - 1);
            }
        }
    }

    private void buildBatchFillInfoQuery(FillInfoQry fillInfoQry, MessageContext messageContext) {
        String templateKey = Lion.getString(Environment.getAppName(),
                "health.recommend.list.template.key", "ListingShow");
        BasicParam basicParam = messageContext.getBasicParam();
        fillInfoQry.setTemplateKey(templateKey);
        fillInfoQry.setUserId(basicParam.getUserId());
        fillInfoQry.setPlatform(messageContext.getPlatform());
        fillInfoQry.setCityId(basicParam.getCityId());
        fillInfoQry.setAppVersion(basicParam.getAppVersion());
        fillInfoQry.setOs("ios".equals(basicParam.getClientType()) ? "ios" : "Android");
        fillInfoQry.setUuid(basicParam.getUuid());
        fillInfoQry.setLat(basicParam.getLat());
        fillInfoQry.setLng(basicParam.getLng());
    }

    private ShopGoodsRecallDTO createDefaultShopGoodsRecallDTO(Long shopId, SupplyRecommendModel supplyRecommendModel) {
        ShopGoodsRecallDTO shopGoodsRecallDTO = new ShopGoodsRecallDTO();
        shopGoodsRecallDTO.setShopId(shopId);
        GoodsRecallDTO goodsRecallDTO = new GoodsRecallDTO();
        if (StringUtils.isNotBlank(supplyRecommendModel.getGoodsId())) {
            goodsRecallDTO.setGoodsId(NumberUtils.toLong(supplyRecommendModel.getGoodsId()));
            goodsRecallDTO.setGoodsType(supplyRecommendModel.getGoodsType());
            shopGoodsRecallDTO.setShopGoodsRecallDTOs(Lists.newArrayList(goodsRecallDTO));
        }
        return shopGoodsRecallDTO;
    }

    /**
     * 解析经纬度信息
     * 优先级：positionTab > supplyRecommendModel > basicParam
     *
     * @param positionTab          位置标签
     * @param supplyRecommendModel 供给推荐模型
     * @param basicParam           基础参数
     * @return 经纬度对，第一个元素为经度(lng)，第二个元素为纬度(lat)
     */
    private Pair<Double, Double> parsePosition(String positionTab,
                                               SupplyRecommendModel supplyRecommendModel,
                                               BasicParam basicParam) {
        Double lng = null;
        Double lat = null;

        // 1. 优先从positionTab中获取经纬度
        if (org.apache.commons.lang3.StringUtils.isNotBlank(positionTab)) {
            try {
                // 假设positionTab格式为"lng,lat"
                String[] positions = positionTab.split(",");
                if (positions.length == 2) {
                    lng = Double.parseDouble(positions[0]);
                    lat = Double.parseDouble(positions[1]);
                    log.info("从positionTab获取经纬度成功: lng={}, lat={}", lng, lat);
                    return Pair.of(lng, lat);
                }
            } catch (Exception e) {
                log.warn("解析positionTab经纬度失败: {}", positionTab, e);
            }
        }

        // 2. 其次从supplyRecommendModel中获取经纬度
//        if (supplyRecommendModel != null &&
//            org.apache.commons.lang3.StringUtils.isNotBlank(supplyRecommendModel.getLatLng())) {
//            try {
//                // 假设latLng格式为"lat,lng"
//                String[] latLngArray = supplyRecommendModel.getLatLng().split(",");
//                if (latLngArray.length == 2) {
//                    lat = Double.parseDouble(latLngArray[0]);
//                    lng = Double.parseDouble(latLngArray[1]);
//                    log.info("从supplyRecommendModel获取经纬度成功: lng={}, lat={}", lng, lat);
//                    return Pair.of(lng, lat);
//                }
//            } catch (Exception e) {
//                log.warn("解析supplyRecommendModel经纬度失败: {}", supplyRecommendModel.getLatLng(), e);
//            }
//        }

        // 3. 最后从basicParam中获取经纬度
        if (basicParam != null) {
            lng = basicParam.getLng();
            lat = basicParam.getLat();
            if (lng != null && lat != null) {
                log.info("从basicParam获取经纬度成功: lng={}, lat={}", lng, lat);
                return Pair.of(lng, lat);
            }
        }

        // 如果所有来源都没有有效的经纬度，返回null
        log.warn("无法从任何来源获取有效的经纬度信息");
        return null;
    }

    private void buildDoctorRequest(DoctorRecommendListRecallRequest doctorRequest,
                                    SupplyRecommendModel supplyRecommendModel,
                                    int pageNo,
                                    int pageSize,
                                    MessageContext context) {

        if (supplyRecommendModel.getDoctorFilter() != null) {

        }
    }

    @Override
    public void after(TaskProcessResult result) {

    }

    /**
     * 替换URL中的shopid参数值
     * @param originalUrl 原始URL
     * @param newShopId 新的shopid值
     * @return 替换后的URL
     */
    private String replaceShopIdInUrl(String originalUrl, Long newShopId) {
        if (originalUrl == null || newShopId == null) {
            return originalUrl;
        }
        
        try {
            // 使用正则表达式替换shopid参数
            // 匹配 shopid=数字 的模式
            String pattern = "shopid=\\d+";
            String replacement = "shopid=" + newShopId;
            return originalUrl.replaceFirst(pattern, replacement);
        } catch (Exception e) {
            log.warn("Failed to replace shopid in URL: {}, error: {}", originalUrl, e.getMessage());
            return originalUrl;
        }
    }

    private String getAnchorUrl(String jumpUrl, String productUrl, String keyword) {
        StringBuilder builder = new StringBuilder(jumpUrl);
        JSONObject jsonObject = new JSONObject();
        String productId = UrlUtils.getParamValue(productUrl, "productid");
        if (StringUtils.isNotBlank(productId)) {
            jsonObject.put("spu", productId);
        } else {
            productId = UrlUtils.getParamValue(productUrl, "did");
            if (StringUtils.isNotBlank(productId)) {
                jsonObject.put("did", productId);
            }
        }

        if (StringUtils.isNotBlank(jumpUrl) && StringUtils.isNotBlank(productId)) {
            builder.append("&").append("summarypids=").append((URLEncoder.encode(jsonObject.toString(), StandardCharsets.UTF_8)));
            builder.append("&").append("anchorgoodflag=").append(1);
            builder.append("&").append("anchorgoodid=").append(productId);
            builder.append("&").append("anchor=").append("deal_shelf");
            builder.append("&").append("keyword=").append(URLEncoder.encode(keyword, StandardCharsets.UTF_8));
            return builder.toString();
        }
        return builder.toString();

    }
}
