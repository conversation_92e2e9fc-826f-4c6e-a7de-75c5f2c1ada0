package com.sankuai.dzhealth.ai.service.domain.card;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.sankuai.dzhealth.ai.service.dto.stream.StreamEventCardDataDTO;
import com.sankuai.dzhealth.ai.service.dto.stream.StreamEventDTO;
import com.sankuai.dzhealth.ai.service.dto.stream.StreamEventDataDTO;
import com.sankuai.dzhealth.ai.service.enums.StreamEventCardTypeEnum;
import com.sankuai.dzhealth.ai.service.enums.StreamEventDataTypeEnum;
import com.sankuai.dzhealth.ai.service.enums.StreamEventTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.Ordered;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * 卡片处理器抽象基类，提供通用的卡片发送方法
 */
@Slf4j
public abstract class AbstractCardHandler implements CardHandler {
    
    /**
     * 默认获取处理器顺序的实现，通常会被子类覆盖
     */
    @Override
    public int getOrder() {
        return Ordered.LOWEST_PRECEDENCE;
    }
    
    /**
     * 发送卡片事件的通用方法
     * @param cardTypeEnum 卡片类型
     * @param cardKey 卡片键名
     * @param cardProps 卡片属性
     * @param sseEmitter SSE发射器
     * @param index 索引
     * @return 构建的事件DTO
     */
    protected StreamEventDTO sendCardEvent(StreamEventCardTypeEnum cardTypeEnum, String cardKey, 
                                        Map<String, Object> cardProps, SseEmitter sseEmitter, int index) {
        try {
            StreamEventDTO eventDTO = new StreamEventDTO();
            eventDTO.setType(StreamEventTypeEnum.MESSAGE.getType());
            eventDTO.setIndex(index);
            
            StreamEventDataDTO dataDTO = new StreamEventDataDTO();
            dataDTO.setEvent(StreamEventDataTypeEnum.MAIN_TEXT.getType());
            boolean isOutBubble = cardTypeEnum.isOutBubble();

            dataDTO.setContent(isOutBubble ? ":::{" + StreamEventCardTypeEnum.buildCardContent(cardTypeEnum, cardKey) + "}:::"
                    : ":::}" + StreamEventCardTypeEnum.buildCardContent(cardTypeEnum, cardKey) + "{:::");
            
            StreamEventCardDataDTO cardDataDTO = new StreamEventCardDataDTO();
            cardDataDTO.setType(cardTypeEnum.getType());
            cardDataDTO.setKey(cardKey);
            cardDataDTO.setCardProps(cardProps);
            
            dataDTO.setCardsData(Lists.newArrayList(cardDataDTO));
            eventDTO.setData(dataDTO);
            log.info("sendCardEvent:{}", JSON.toJSONString(eventDTO));
            
            sseEmitter.send(eventDTO);
            return eventDTO;
        } catch (Exception e) {
            log.error("Error sending card event, cardType={}, cardKey={}", cardTypeEnum.getType(), cardKey, e);
            return null;
        }
    }
    
    /**
     * 生成空的事件列表
     */
    protected List<StreamEventDTO> emptyEvents() {
        return Collections.emptyList();
    }
    
    /**
     * 生成包含单个事件的列表
     */
    protected List<StreamEventDTO> singletonEvent(StreamEventDTO eventDTO) {
        if (eventDTO == null) {
            return emptyEvents();
        }
        return Collections.singletonList(eventDTO);
    }
    
    /**
     * 向事件列表中添加非空事件
     */
    protected void addIfNotNull(List<StreamEventDTO> events, StreamEventDTO eventDTO) {
        if (eventDTO != null) {
            events.add(eventDTO);
        }
    }
} 