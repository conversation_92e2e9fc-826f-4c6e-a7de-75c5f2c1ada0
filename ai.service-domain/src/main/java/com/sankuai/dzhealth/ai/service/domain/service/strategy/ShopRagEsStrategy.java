package com.sankuai.dzhealth.ai.service.domain.service.strategy;

import com.alibaba.fastjson.JSON;
import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.dianping.rhino.threadpool.ThreadPool;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.meituan.beauty.fundamental.light.plat.Platform;
import com.meituan.mdp.boot.starter.config.annotation.MdpConfig;
import com.sankuai.dzhealth.ai.service.api.RemoteResponse;
import com.sankuai.dzhealth.ai.service.domain.chat.model.IntentionResult;
import com.sankuai.dzhealth.ai.service.domain.service.ChatSessionService;
import com.sankuai.dzhealth.ai.service.domain.ability.llm.FilterableItem;
import com.sankuai.dzhealth.ai.service.domain.ability.llm.LLMResultFilterService;
import com.sankuai.dzhealth.ai.service.domain.utils.context.AiAnswerContext;
import com.sankuai.dzhealth.ai.service.dto.DocumentDTO;
import com.sankuai.dzhealth.ai.service.infrastructure.observability.Span;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.document.Document;
import org.springframework.ai.vectorstore.SearchRequest;
import org.springframework.ai.vectorstore.VectorStore;
import org.springframework.ai.vectorstore.filter.FilterExpressionBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.sankuai.dzhealth.ai.service.enums.MetadataKeyEnum;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static java.util.stream.Collectors.toList;

/**
 * @author:chenwei
 * @time: 2025/4/17 21:32
 * @version: 0.0.1
 */

@Service
@Slf4j
public class ShopRagEsStrategy implements DataSourceStrategy<ShopRagEsStrategy.ShopRagStrategyInfo> {

    @MdpConfig("shop_rag_cnt:10")
    private Integer shop_rag_cnt = 10;

    /**
     * 是否启用商户RAG结果过滤功能
     * 配置项：public_hospital.shop_rag.enable_result_filtering
     * 默认值：true（商户RAG结果可能存在噪音，建议默认开启过滤）
     */
    @MdpConfig("public_hospital.shop_rag.enable_result_filtering:false")
    private Boolean enableShopRagResultFiltering;

    @Autowired
    private ChatClient retrieveStrategyResEvaChatClient;

    @Autowired
    private LLMResultFilterService llmResultFilterService;

    private final String REVIEW_URL = "https://i.meituan.com/mttouch/page/feedback?sourceId=%s";

    private final String DP_H5_REVIEW = "https://m.dianping.com/ugcdetail/%s?sceneType=1&bizType=1&utm_source=ugcshare&msource=Appshare2021&utm_medium=h5&shareid=cpH2hVWgKs_1745398089";

    private final String NOTE_URL = "https://m.dianping.com/ugcdetail/%s?sceneType=0&bizType=29";

    private final String MT_QA = "https://m.dianping.com/forum/question/mt/shop/detail/%s?shopId=%s";

    private final String DP_QA = "https://m.dianping.com/forum/question/shop/detail/%s?shopId=%s";

    private final int PULISH_TIME_YEAR_LIMIT = -2;

//    @Autowired
//    private ESVectorStoreService esVectorStoreService;

    @Autowired
    private VectorStore esVectorStore;

    private static final ThreadPool taskPool = ChatSessionService.TASK_POOL;

    /**
     * 是否应该执行该策略
     *
     * @param intentionResult 意图识别结果
     * @return 是否执行
     */
    @Override
    public boolean shouldExecute(IntentionResult intentionResult) {
        return intentionResult != null && intentionResult.getShopRagNeed();
    }

    /**
     * 执行策略
     *
     * @param context     上下文
     * @param rewriteText 改写后的文本
     * @return 策略执行结果
     */
    @Override
    public CompletableFuture<ShopRagStrategyInfo> execute(AiAnswerContext context, String rewriteText) {
        String mtShopID = String.valueOf(context.getMtShopId());
        List<String> channels = Lists.newArrayList("leadWechat", "ugcReview", "ugcRedbook", "ugcDianping", "ugcQa");

        return CompletableFuture.supplyAsync(() -> queryEsVector(context, rewriteText, mtShopID, channels, shop_rag_cnt),
                taskPool.getExecutor());
    }


    private ShopRagEsStrategy.ShopRagStrategyInfo queryEsVector(AiAnswerContext context,
                                                                String rewriteText,
                                                                String mtShopId,
                                                                List<String> channels,
                                                                int topK) {
        Transaction transaction = Cat.newTransaction(getClass().getSimpleName(), "queryEsVector");
        long startTimeMillis = System.currentTimeMillis();
        try {
            RemoteResponse<List<DocumentDTO>> listRemoteResponse = similaritySearch(rewriteText, topK, mtShopId, channels);

            ShopRagEsStrategy.ShopRagStrategyInfo ragEsInfo = new ShopRagStrategyInfo();
            if (listRemoteResponse.getSuccess() && CollectionUtils.isNotEmpty(listRemoteResponse.getData())) {
                List<ShopRagStrategyInfo.ShopBaseRagStrategyInfo> ragStrategyInfos =
                        listRemoteResponse.getData().stream().map(e -> {
                            ShopRagStrategyInfo.ShopBaseRagStrategyInfo baseRagStrategyInfo =
                                    new ShopRagStrategyInfo.ShopBaseRagStrategyInfo();
                            baseRagStrategyInfo.setContent(e.getText().replace("/", "")
                                    .replace("<", "").replace(">", "").replace("**", ""));
                            baseRagStrategyInfo.setUri(getUrlByChannel(e.getMetadata().get(MetadataKeyEnum.RESOURCE_URI.getKey()),
                                    e.getMetadata().get(MetadataKeyEnum.CHANNEL.getKey()), context.getPlatform(), context.getShopId()));
                            baseRagStrategyInfo.setPublishTime(e.getMetadata().get(MetadataKeyEnum.PUBLISH_TIME.getKey()));
                            baseRagStrategyInfo.setResourceChannel(e.getMetadata().get(MetadataKeyEnum.CHANNEL.getKey()));
                            return baseRagStrategyInfo;
                        }).collect(toList());
                ragEsInfo.setRagInfo(ragStrategyInfos);
            }
            Cat.newTransactionWithDuration(getClass().getSimpleName(), "ShopEsRag", System.currentTimeMillis() - startTimeMillis)
                    .complete();

            Map<String, Object> requestInfo = new HashMap<>();
            requestInfo.put("query", rewriteText);
            requestInfo.put("topK", topK);
            requestInfo.put("shopId", mtShopId);
            requestInfo.put("channels", channels);

            ragEsInfo.setSpan(Collections.singletonList(Span.builder()
                    .key(getClass().getSimpleName())
                    .value(JSON.toJSONString(ImmutableMap.of("request", requestInfo, "response", listRemoteResponse)))
                    .duration(System.currentTimeMillis() - startTimeMillis)
                    .build()));
            transaction.setSuccessStatus();
            return ragEsInfo;
        } catch (Exception e) {
            transaction.setStatus(e);
            return new ShopRagStrategyInfo();
        } finally {
            transaction.complete();
        }
    }

    public RemoteResponse<List<DocumentDTO>> similaritySearch(String query, int topK, String shopId, List<String> channels) {
        Transaction transaction = Cat.newTransaction("ShopRagEsStrategy", "similaritySearch");
        try {
            FilterExpressionBuilder.Op op = null;
            if (channels != null) {
                Calendar calendar = Calendar.getInstance();
                calendar.add(Calendar.YEAR, PULISH_TIME_YEAR_LIMIT);
                String twoYearsAgo = String.format("%04d-%02d-%02d",
                        calendar.get(Calendar.YEAR),
                        calendar.get(Calendar.MONTH) + 1,
                        calendar.get(Calendar.DAY_OF_MONTH));

                List<String> timeFilterChannels = channels.stream()
                        .filter(ch -> Arrays.asList("ugcReview", "ugcRedbook", "ugcDianping", "ugcQa").contains(ch))
                        .collect(Collectors.toList());
                List<String> otherChannels = channels.stream()
                        .filter(ch -> !Arrays.asList("leadWechat").contains(ch))
                        .collect(Collectors.toList());

                FilterExpressionBuilder.Op channelOp = null;
                if (!timeFilterChannels.isEmpty()) {
                    channelOp = new FilterExpressionBuilder().in("channel", timeFilterChannels.toArray());
                    channelOp = new FilterExpressionBuilder().and(channelOp,
                            new FilterExpressionBuilder().gte("publish_time.keyword", twoYearsAgo));
                }
                if (!otherChannels.isEmpty()) {
                    FilterExpressionBuilder.Op otherChannelOp = new FilterExpressionBuilder().in("channel", otherChannels.toArray());
                    channelOp = channelOp == null ? otherChannelOp :
                            new FilterExpressionBuilder().or(channelOp, otherChannelOp);
                }
                op = channelOp;
            }

            if (StringUtils.isNotBlank(shopId)) {
                FilterExpressionBuilder.Op shopIdOp = new FilterExpressionBuilder().eq("shopId", shopId);
                op = op == null ? shopIdOp :
                     new FilterExpressionBuilder().and(op, shopIdOp);
            }

            List<Document> documentList = esVectorStore.similaritySearch(SearchRequest.builder()
                    .query(query)
                    .topK(topK)
                    .similarityThresholdAll()
                    .filterExpression(op != null ? op.build() : null)
                    .build());
            transaction.setSuccessStatus();
            List<DocumentDTO> result = Optional.ofNullable(documentList)
                    .orElse(Collections.emptyList())
                    .stream()
                    .map(this::convertToDocumentDTO)
                    .collect(Collectors.toList());
            log.info("similaritySearch,rewriteText={},topK={},shopId={},channels={},result={}",
                    query, topK, shopId, channels, JSON.toJSONString(result));
            return RemoteResponse.buildSuccess(result);
        } catch (Exception e) {
            log.error("similaritySearch,rewriteText={},topK={},shopId={},channels={},error={}",
                    query, topK, shopId, channels, e.getMessage(), e);
            transaction.setStatus(e);
            return RemoteResponse.buildFail(e.getLocalizedMessage());
        } finally {
            transaction.complete();
        }
    }

    private DocumentDTO convertToDocumentDTO(Document document) {
        DocumentDTO documentDTO = new DocumentDTO();
        documentDTO.setId(document.getId());
        documentDTO.setText(document.getText());
        Map<String, String> metadata = Optional.of(document.getMetadata())
                .orElse(Collections.emptyMap())
                .entrySet()
                .stream()
                .collect(Collectors.toMap(Map.Entry::getKey, e -> String.valueOf(e.getValue())));
        documentDTO.setMetadata(metadata);
        documentDTO.setScore(document.getScore());
        return documentDTO;
    }

    private String getUrlByChannel(String originUri, String channel, int platform, long shopId) {
        if (StringUtils.isBlank(originUri)) {
            return StringUtils.EMPTY;
        }
        if ("ugcReview".equals(channel)) {
            String[] split = originUri.split("_");
            if (split.length > 1) {
                if (originUri.startsWith("d")) {
                    String dpReviewId = split[1];
                    return String.format(DP_H5_REVIEW, dpReviewId);
                }
                if (originUri.startsWith("m")) {
                    String mtReviewId = split[1];
                    return String.format(REVIEW_URL, mtReviewId) + "&platform=mt";
                }
            }
        }
        if ("ugcDianping".equals(channel)) {
            return String.format(NOTE_URL, originUri);
        }

        if ("ugcQa".equals(channel)) {
            return String.format(platform == Platform.DP.getCode() ? DP_QA : MT_QA, originUri, shopId);
        }
        return originUri;
    }

    @Override
    public ShopRagStrategyInfo filterResult(String rewriteText, ShopRagStrategyInfo strategyResult) {
        if (strategyResult == null || CollectionUtils.isEmpty(strategyResult.getRagInfo())) {
            return strategyResult;
        }

        // 根据配置决定是否执行过滤
        if (!enableShopRagResultFiltering) {
            log.debug("商户RAG结果过滤功能已禁用，返回原始结果");
            return strategyResult;
        }

        String systemMessageContent = "你是一个专业的商户知识库检索结果评估助手，专门负责评估商户相关内容的相关性。" +
                "请评估每个商户知识库检索结果是否有助于回答用户关于该医疗机构的问题。" +
                "评估标准：1)内容与问题高度相关 2)信息真实可靠 3)对用户有实际帮助 4)避免重复或无关内容。" +
                "返回格式：JSON数组，每个元素包含resultId（从1开始）和isValid（true/false）。" +
                "示例：[{\"resultId\":1,\"isValid\":true},{\"resultId\":2,\"isValid\":false}]";

        List<ShopRagStrategyInfo.ShopBaseRagStrategyInfo> filteredRagInfo = llmResultFilterService.filterResults(
                rewriteText,
                strategyResult.getRagInfo(),
                systemMessageContent,
                retrieveStrategyResEvaChatClient
        );

        ShopRagStrategyInfo newResult = new ShopRagStrategyInfo();
        newResult.setRagInfo(filteredRagInfo);
        newResult.setSpan(strategyResult.getSpan());

        return newResult;
    }

    @Data
    @EqualsAndHashCode(callSuper = true)
    public static class ShopRagStrategyInfo extends BaseStrategyInfo {

        private List<ShopBaseRagStrategyInfo> ragInfo;

        @Override
        public String toPrompt() {
            if (CollectionUtils.isEmpty(ragInfo)) {
                return "[]";
            }
            List<String> jsonResults = IntStream.range(0, ragInfo.size())
                    .mapToObj(index -> String.format(
                            "{\"内容\":\"%s\",\"序号\":\"%d\",\"发布时间\":\"%s\"，\"类型\":\"%s\"}",
                            Optional.ofNullable(ragInfo.get(index).getContent()).orElse(""),
                            index + 1,
                            Optional.ofNullable(ragInfo.get(index).getPublishTime()).orElse(""),
                            Optional.ofNullable(ragInfo.get(index).getResourceChannel()).orElse("")))
                    .collect(toList());

            return "[" + String.join(",", jsonResults) + "]";
        }

        @Data
        public static class ShopBaseRagStrategyInfo implements FilterableItem {

            private String content;

            private String uri;

            private String publishTime;

            private String resourceChannel;

            @Override
            public Map<String, String> getFieldsForEvaluation() {
                Map<String, String> fields = new LinkedHashMap<>();
                fields.put("内容", Optional.ofNullable(content).orElse(""));
                fields.put("来源渠道", Optional.ofNullable(resourceChannel).orElse(""));
                fields.put("发布时间", Optional.ofNullable(publishTime).orElse(""));
                return fields;
            }
        }
    }
}
