package com.sankuai.dzhealth.ai.service.domain.chat.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.ai.chat.messages.Message;
import org.springframework.ai.chat.messages.MessageType;

import java.util.Date;
import java.util.Map;

/**
 * @author: duan<PERSON><PERSON><PERSON>
 * @date: 2025/3/19
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ChatMessageBO implements Message {
    private String messageId;
    private String conversationId;
    private MessageType messageType;
    private String content;
    private String senderId;
    private Byte contentType;
    private Long ttft;
    private Long e2eTime;
    private String parentMessageId;
    private Long sequence;
    private Byte auditStatus;
    private String extraData;
    private Date createdAt;

    // Message接口实现
    @Override
    public MessageType getMessageType() {
        return messageType;
    }

    @Override
    public String getText() {
        return content;
    }


    @Override
    public Map<String, Object> getMetadata() {
        return Map.ofEntries(
            Map.entry("messageId", messageId),
            Map.entry("conversationId", conversationId),
            Map.entry("senderId", senderId),
            Map.entry("contentType", contentType),
            Map.entry("ttft", ttft),
            Map.entry("e2eTime", e2eTime),
            Map.entry("parentMessageId", parentMessageId),
            Map.entry("sequence", sequence),
            Map.entry("auditStatus", auditStatus),
            Map.entry("extraData", extraData),
            Map.entry("createdAt", createdAt)
        );
    }
}
