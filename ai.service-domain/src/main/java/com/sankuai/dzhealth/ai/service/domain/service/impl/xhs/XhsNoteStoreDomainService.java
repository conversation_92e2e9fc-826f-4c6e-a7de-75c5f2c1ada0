package com.sankuai.dzhealth.ai.service.domain.service.impl.xhs;

import com.alibaba.fastjson.JSON;
import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.dianping.rhino.Rhino;
import com.dianping.rhino.threadpool.DefaultThreadPoolProperties;
import com.dianping.rhino.threadpool.ThreadPool;
import com.meituan.beauty.fundamental.light.editor.EditorType;
import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.sankuai.beautycontent.store.storage.proxy.StoreCommandProxy;
import com.sankuai.beautycontent.store.storage.proxy.StoreQueryProxy;
import com.sankuai.beautycontent.store.storage.request.StoreSearchRequest;
import com.sankuai.dzhealth.ai.service.api.ESVectorStoreService;
import com.sankuai.dzhealth.ai.service.domain.entity.xhs.XhsNoteGenerateDTO;
import com.sankuai.dzhealth.ai.service.domain.entity.xhs.XhsNoteStoreDTO;
import com.sankuai.dzhealth.ai.service.domain.service.XhsNoteAbstractService;
import com.sankuai.dzhealth.ai.service.domain.utils.JsonUtils;
import com.sankuai.dzhealth.ai.service.dto.DocumentDTO;
import com.sankuai.dzhealth.ai.service.infrastructure.converter.CorpusLibraryConverter;
import com.sankuai.dzhealth.ai.service.infrastructure.model.CorpusLibrary;
import com.sankuai.dzhealth.ai.service.infrastructure.repository.CorpusLibraryRepository;
import com.sankuai.dzhealth.ai.service.request.xhs.XhsBuildNoteRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * @author:chenwei
 * @time: 2025/6/17 15:33
 * @version: 0.0.1
 */
@Slf4j
@Service
public class XhsNoteStoreDomainService {

    // 线程池用于异步执行笔记生成任务
    public static final ThreadPool TASK_POOL = Rhino.newThreadPool("XhsNoteStoreDomainService",
            DefaultThreadPoolProperties.Setter().withCoreSize(100).withMaxSize(200).withMaxQueueSize(1000));

    @Autowired
    @Qualifier("generalNoteServiceImpl")
    private XhsNoteAbstractService<XhsBuildNoteRequest> generalNoteService;


    @Autowired
    private StoreCommandProxy storeCommandProxy;
    @Autowired
    private StoreQueryProxy storeQueryProxy;

    @Autowired
    private CorpusLibraryRepository corpusLibraryRepository;

    @Autowired
    private ESVectorStoreService esVectorStoreService;


    /**
     * 根据请求生成笔记
     * 如果有回调地址，则异步生成并通过回调通知结果
     * 否则，同步执行（当前实现为demo，同步不执行实际操作）
     */
    public void generateNotes(XhsBuildNoteRequest request) {
        log.info("启动异步笔记生成任务，批次：{}，回调appkey：{}", request.getBatch(), request.getCallbackAppkey());
        CompletableFuture.runAsync(() -> generalNoteService.doTask(request), TASK_POOL.getExecutor());

    }


    public void saveGenerateNote(XhsNoteGenerateDTO xhsNoteStoreDTO) {

        Transaction transaction = Cat.newTransaction("XhsNoteStoreDomainService", "generateNote");
        transaction.setStatus(Transaction.SUCCESS);
        try {
            RemoteResponse<Long> addRes =
                    storeCommandProxy.add(xhsNoteStoreDTO, EditorType.OTHER.code, "xhs-generate-note");
            if (!addRes.isSuccess()) {
                log.warn("saveGenerateNote2Es,failReason={},noteId={}", addRes.getMsg(), xhsNoteStoreDTO.getTitle());
                return;
            }
            log.info("saveGenerateNote2Es, note={}", JsonUtils.toJsonString(xhsNoteStoreDTO));
        } catch (Exception e) {
            transaction.setStatus(e);
            log.error("XhsNoteStoreDomainService_saveGenerateNote2Es, req={}", JsonUtils.toJsonString(xhsNoteStoreDTO), e);
        } finally {
            transaction.complete();
        }

    }



    public void saveNote(XhsNoteStoreDTO xhsNoteStoreDTO) {

        Transaction transaction = Cat.newTransaction("XhsNoteStoreDomainService", "saveNote");
        transaction.setStatus(Transaction.SUCCESS);
        try {
            List<XhsNoteStoreDTO> existNotes = storeQueryProxy.searchDetail(
                    StoreSearchRequest.builder("xhsNote", StoreSearchRequest.Cluster.BEAUTY)
                            .page(0, 1)
                            .query(StoreSearchRequest.Field.STORE_TYPE, 54)
                            .query(StoreSearchRequest.Field.ATTRIBUTE,
                                    StoreSearchRequest.Value.attribute("ge", "batch", xhsNoteStoreDTO.getBatch()))
                            .query(StoreSearchRequest.Field.RELATION_ID, xhsNoteStoreDTO.getRelationId())
                            .query(StoreSearchRequest.Field.STATUS, 0)
                            .build(), XhsNoteStoreDTO.class);
            if (CollectionUtils.isNotEmpty(existNotes)) {
                log.info("existNotes,existNotes={}", JSON.toJSONString(existNotes));
                return;
            }
            RemoteResponse<Long> addRes =
                    storeCommandProxy.add(xhsNoteStoreDTO, EditorType.OTHER.code, "xhs-save-note");
            if (!addRes.isSuccess()) {
                log.warn("saveNote2Es,failReason={},noteId={}", addRes.getMsg(), xhsNoteStoreDTO.getTitle());
                return;
            }
            log.info("saveNote2Es, note={}", JsonUtils.toJsonString(xhsNoteStoreDTO));
            Long storeId = addRes.getData();
            CorpusLibrary corpusLibrary = CorpusLibrary.builder()
                    .corpusType(1)
                    .resourceId(storeId)
                    .resourceUri(xhsNoteStoreDTO.getHead())
                    .resourceChannel("redBookNote")
                    .corpusContent(xhsNoteStoreDTO.getContent())
                    .corpusSummary(xhsNoteStoreDTO.getBatch() + "***" + xhsNoteStoreDTO.getTitle())
                    .build();
            Long corpusLibraryId = corpusLibraryRepository.save(CorpusLibraryConverter.toDO(corpusLibrary));
            DocumentDTO documentDTO = buildNoteEsElement(xhsNoteStoreDTO, corpusLibraryId);
            esVectorStoreService.buildIndex(Collections.singletonList(documentDTO));
        } catch (Exception e) {
            transaction.setStatus(e);
            log.error("XhsNoteStoreDomainService_saveNote, req={}", JsonUtils.toJsonString(xhsNoteStoreDTO), e);
        } finally {
            transaction.complete();
        }

    }

    private DocumentDTO buildNoteEsElement(XhsNoteStoreDTO xhsNoteStoreDTO, Long corpusLibraryId) {
        DocumentDTO documentDTO = new DocumentDTO();
        documentDTO.setId("1_" + corpusLibraryId);
        documentDTO.setText(xhsNoteStoreDTO.getContent());
        Map<String, String> metaData = new HashMap<>();
        metaData.put("channel", "redBookNote");
        metaData.put("is_content_quantified", String.valueOf(true));
        metaData.put("resource_uri", xhsNoteStoreDTO.getHead());
        metaData.put("batch", xhsNoteStoreDTO.getBatch());
        metaData.put("title", xhsNoteStoreDTO.getTitle());
        documentDTO.setMetadata(metaData);
        return documentDTO;
    }
}
