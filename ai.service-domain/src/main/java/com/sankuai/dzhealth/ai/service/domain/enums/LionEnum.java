package com.sankuai.dzhealth.ai.service.domain.enums;


import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

@Getter
public enum LionEnum {

    AI_CALL_CARD_INFO("aicall.cardInfo","电话卡片"),
    AI_CALL_TENANT_ID("AiCall.TenantId","木星租户信息"),
    AI_CALL_PROMPT_MATCH("aicall.prompt.list","智能外呼话术模版"),
    AI_CALL_TIME("aicall.time","外呼重试次数"),
    AI_CALL_WAIT_TIME("aicall.waitTime","外呼用户繁忙等待时间"),
    AI_APPOINTMENT_CARD("appointment.cardinfo","预约卡片");



    private final String key;
    private final String desc;

    LionEnum(String key, String desc) {
        this.key =  key;
        this.desc = desc;
    }

    public static String getDesc(String key) {
        return Arrays.stream(values()).filter(LionEnum -> Objects.equals(LionEnum.key,key)).findFirst().map(LionEnum::getDesc).orElse(null);
    }

    public static String getKey(String desc){
        return Arrays.stream(values()).filter(LionEnum -> Objects.equals(LionEnum.desc, desc)).findFirst().map(LionEnum::getKey).orElse(null);
    }
}
