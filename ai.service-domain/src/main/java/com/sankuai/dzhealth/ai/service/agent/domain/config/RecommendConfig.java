package com.sankuai.dzhealth.ai.service.agent.domain.config;

import com.dianping.cat.util.MetricHelper;
import com.meituan.mdp.ai.friday.ExtraBodyConfig;
import com.meituan.mdp.ai.friday.FridayChatModel;
import com.meituan.mdp.ai.friday.FridayChatOptions;
import com.meituan.mdp.ai.friday.api.FridayApi;
import com.meituan.mdp.ai.friday.api.ResponseFormat;
import com.meituan.service.inf.kms.client.Kms;
import com.meituan.service.inf.kms.utils.KmsResultNullException;
import com.sankuai.dzhealth.ai.service.agent.domain.process.task.TaskConfig;
import com.sankuai.dzhealth.ai.service.dto.SseAwareException;
import com.sankuai.dzhealth.ai.service.infrastructure.chatmodel.OneApiChatModel;
import org.apache.commons.lang.StringUtils;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.openai.OpenAiChatOptions;
import org.springframework.ai.openai.api.OpenAiApi;
import org.springframework.context.annotation.Configuration;

import java.util.Optional;

@Configuration
public class RecommendConfig {

    public String chatWithAI(TaskConfig context) throws KmsResultNullException, SseAwareException {

        OpenAiApi openAiApi = OpenAiApi.builder()
                .completionsPath("/chat/completions")
                .baseUrl("https://aigc.sankuai.com/v1/openai/native")
                .apiKey(Kms.getByName("com.sankuai.dzhealth.ai.service", context.getKmsKey()))
                .build();

        OpenAiChatOptions openAiChatOptions = OpenAiChatOptions.builder()
                .model(StringUtils.isBlank(context.getModel()) ? "deepseek-v3-friday" : context.getModel())
                .temperature(0.1)
                .maxTokens(Optional.ofNullable(context.getMaxTokens()).orElse(20000))
                .build();

        OneApiChatModel oneApiChatModel = OneApiChatModel.builder()
                .openAiApi(openAiApi)
                .defaultOptions(openAiChatOptions)
                .build();

        ChatClient intentClient = ChatClient.builder(oneApiChatModel)
                .build();


        return intentClient.prompt()
                .system(context.getSystemPrompt())
                .user(context.getUserPrompt())
                .call().content();

    }

    public String chatWithJSON(TaskConfig context) throws KmsResultNullException, SseAwareException {
        return this.chatWithJSON(context,null);
    }

    public String chatWithJSON(TaskConfig context, String sceneType) throws KmsResultNullException, SseAwareException {

        FridayApi fridayApi = FridayApi.builder()
                .completionsPath("/chat/completions")
                .baseUrl("https://aigc.sankuai.com/v1/openai/native")
                .apiKey(Kms.getByName("com.sankuai.dzhealth.ai.service", context.getKmsKey()))
                .build();

        FridayChatOptions fridayChatOptions = FridayChatOptions.builder()
                .model(StringUtils.isBlank(context.getModel()) ? "deepseek-v3-friday" : context.getModel())
                .temperature(0.1)
                .extraBodyConfig(ExtraBodyConfig.builder().properties(context.getExtraBodyConfig()).build())
                .maxTokens(Optional.ofNullable(context.getMaxTokens()).orElse(20000))
                .responseFormat(com.meituan.mdp.ai.friday.api.ResponseFormat.builder().type(ResponseFormat.Type.JSON_OBJECT).build())
                .build();

        FridayChatModel fridayChatModel = FridayChatModel.builder()
                .fridayApi(fridayApi)
                .defaultOptions(fridayChatOptions)
                .build();

        ChatClient intentClient = ChatClient.builder(fridayChatModel)
                .build();


        long startTime = System.currentTimeMillis();
        String content = intentClient.prompt()
                .system(context.getSystemPrompt())
                .user(context.getUserPrompt())
                .call().content();
        MetricHelper tag = MetricHelper.build()
                .name("大模型推荐打点")
                // select_doctor_prompt 推荐医生，select_shop_prompt 推荐商户
                // recommend_doctor_prompt 推荐医生理由 recommend_shop_prompt 推荐商户理由
                .tag("taskType", context.getType())
                .tag("metricType", "costTime") // metricType：ttft|costTime
                .tag("method", "chatWithJSON")
                .tag("class", this.getClass().getSimpleName())
                .tag("model", context.getModel());
        if (StringUtils.isNotBlank(sceneType)) {
            tag.tag("sceneType", sceneType);// DialoguePage 对话页 ListPage 对话页
        }
        tag.value(System.currentTimeMillis() - startTime);
        return content;

    }


}
