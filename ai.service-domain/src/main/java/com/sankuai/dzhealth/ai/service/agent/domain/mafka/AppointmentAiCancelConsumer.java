package com.sankuai.dzhealth.ai.service.agent.domain.mafka;

import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.meituan.mafka.client.MafkaClient;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mafka.client.consumer.ConsumerConstants;
import com.meituan.mafka.client.consumer.IConsumerProcessor;
import com.sankuai.dzhealth.ai.service.agent.domain.service.AppointmentService;
import com.sankuai.dzhealth.ai.service.infrastructure.mafka.MafkaShutDownHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import java.util.Properties;

@Component
@Slf4j
public class AppointmentAiCancelConsumer implements InitializingBean {

    @Autowired
    private AppointmentService appointmentService;

    private static final String CAT_TYPE = AppointmentAiCancelConsumer.class.getSimpleName();


    @Override
    public void afterPropertiesSet() throws Exception {
        Properties properties = new Properties();
        properties.setProperty(ConsumerConstants.MafkaBGNamespace, "com.sankuai.mafka.castle.daojiacommon");
        properties.setProperty(ConsumerConstants.MafkaClientAppkey, "com.sankuai.dzhealth.ai.service");
        properties.setProperty(ConsumerConstants.SubscribeGroup, "medical.appointment.aicancel");
        IConsumerProcessor processor = MafkaClient.buildConsumerFactory(properties, "ai_assistant_appointment");
        MafkaShutDownHelper.registerHook(processor);
        processor.recvMessageWithParallel(String.class, (message, context) -> {
            Transaction transaction = Cat.newTransaction(CAT_TYPE, "consume");
            String messageStr=String.valueOf(message.getBody());
            try{
                log.info("[AppointmentAiCancelConsumer] 处理延迟消息: message:{}", messageStr);
                if (StringUtils.isBlank(messageStr)) {
                    log.info("[AppointmentAiCancelConsumer] 处理延迟消息: messageStr is null");
                    return ConsumeStatus.CONSUME_SUCCESS;
                }
                else{
                    appointmentService.cancelAppointmentByTime(messageStr);
                }
                return ConsumeStatus.CONSUME_SUCCESS;
            }catch (Exception e){
                Cat.logEvent("AppointmentAiCancelConsumer", "handle.exception", "1", e.getMessage());
                log.error("AppointmentAiCancelConsumer handle exception for message:{}, e:", messageStr, e);
                transaction.setStatus(e);
                return ConsumeStatus.RECONSUME_LATER;
            }finally {
                transaction.complete();
            }
        });

    }
}
