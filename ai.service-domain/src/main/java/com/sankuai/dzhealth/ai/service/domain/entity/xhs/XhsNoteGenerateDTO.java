package com.sankuai.dzhealth.ai.service.domain.entity.xhs;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.sankuai.beautycontent.store.storage.annotation.Store;
import com.sankuai.beautycontent.store.storage.annotation.StoreAttribute;
import com.sankuai.beautycontent.store.storage.annotation.StoreExtAttribute;
import com.sankuai.beautycontent.store.storage.annotation.StoreReference;
import com.sankuai.beautycontent.store.storage.dto.StoreBaseDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * @author:chenwei
 * @time: 2025/7/7 20:58
 * @version: 0.0.1
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Store(storeType = 57)
public class XhsNoteGenerateDTO extends StoreBaseDTO {

    @StoreExtAttribute(attributeKey = "content")
    @FieldDoc(description = "正文")
    private String content;

    @StoreExtAttribute(attributeKey = "title")
    @FieldDoc(description = "标题")
    private String title;

    @StoreAttribute(attributeKey = "type")
    @FieldDoc(description = "笔记类型,video:视频,图文:normal")
    private String type;

    @StoreAttribute(attributeKey = "batch")
    @FieldDoc(description = "批次")
    private String batch;

    @StoreReference(referenceKey = "topic")
    @FieldDoc(description = "话题")
    private List<String> topic;

    @StoreExtAttribute(attributeKey = "keyPoint")
    @FieldDoc(description = "要点摘要")
    private String keyPoint;

    @StoreExtAttribute(attributeKey = "subTitle")
    @FieldDoc(description = "副标题")
    private String subTitle;

    @StoreAttribute(attributeKey = "isMedicalBeauty")
    @FieldDoc(description = "识别结果-是否医美, 1是, 0否")
    private String isMedicalBeauty;
}
