package com.sankuai.dzhealth.ai.service.agent.domain.model.decisionflow;

import lombok.Builder;
import lombok.Data;

/**
 * 节点与资源关联的业务对象
 */
@Data
@Builder
public class NodeResourceRelationBO {

    /** 业务场景 */
    private String bizScene;

    /** 节点业务 ID */
    private String nodeId;

    /** 资源业务 ID */
    private String resourceId;

    /** 资源类型 */
    private String resourceType;

    /** 排序权重 */
    private Long sortOrder;

    /** 关联状态 */
    private String status;

    /** 推荐理由 / 关联说明 */
    private String rationale;
} 