package com.sankuai.dzhealth.ai.service.domain.service;

import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.sankuai.dzhealth.ai.service.domain.entity.xhs.ServeConfig;
import com.sankuai.dzhealth.ai.service.domain.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;

import java.util.function.Supplier;

@Slf4j
public abstract class XhsNoteAbstractService<T> {
    public String doTask(T request) {

        return trace(request);
    }

    private String serveContains(Supplier<String> service, T request) {
        boolean isTrace = ServeConfig.convertSelf(request).map(ServeConfig::isTrace).orElse(true);
        if (isTrace) {
            return trace(request);
        }

        try {
            return service.get();
        } catch (Exception e) {
            log.error("serveContains error, request:{}", JsonUtils.toJsonString(request), e);
            throw new RuntimeException(e);
        }
    }

    private String trace(T request) {
        Transaction transaction = Cat.newTransaction(getClass().getSimpleName(), request.getClass().getSimpleName());
        try {
            transaction.setSuccessStatus();
            return service(request);
        } catch (Exception e) {
            transaction.setStatus(e);
            transaction.addData(JsonUtils.toJsonString(request));
            log.error("trace error, request:{}", JsonUtils.toJsonString(request), e);
            throw new RuntimeException(e);
        } finally {
            transaction.complete();
        }
    }

    protected abstract String service(T request) throws Exception;
}
