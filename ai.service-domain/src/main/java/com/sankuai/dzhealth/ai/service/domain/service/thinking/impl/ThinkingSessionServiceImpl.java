package com.sankuai.dzhealth.ai.service.domain.service.thinking.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.sankuai.dzhealth.ai.service.domain.enums.thinking.ThinkingSessionStatusEnum;
import com.sankuai.dzhealth.ai.service.domain.model.thinking.ThinkingSession;
import com.sankuai.dzhealth.ai.service.domain.service.thinking.ThinkingSessionService;
import com.sankuai.dzhealth.ai.service.infrastructure.dal.entity.ThinkingSessionEntity;
import com.sankuai.dzhealth.ai.service.infrastructure.dal.mapper.ThinkingSessionEntityMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Map;

/**
 * 思考会话服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ThinkingSessionServiceImpl implements ThinkingSessionService {

    private final ThinkingSessionEntityMapper thinkingSessionEntityMapper;
    private final ObjectMapper objectMapper;

    @Override
    public Long createSession(String query, Map<String, Object> searchConfig) {
        ThinkingSessionEntity entity = new ThinkingSessionEntity();
        entity.setQuery(query);
        entity.setStatus(ThinkingSessionStatusEnum.INIT.getCode());

        try {
            if (searchConfig != null) {
                entity.setSearchConfig(objectMapper.writeValueAsString(searchConfig));
            }
        } catch (JsonProcessingException e) {
            log.error("序列化搜索配置失败", e);
            throw new RuntimeException("序列化搜索配置失败", e);
        }
        thinkingSessionEntityMapper.insertSelective(entity);
        return entity.getId();
    }

    @Override
    public void updateSessionStatus(Long sessionId, ThinkingSessionStatusEnum status) {
        ThinkingSessionEntity entity = new ThinkingSessionEntity();
        entity.setId(sessionId);
        entity.setStatus(status.getCode());

        if (ThinkingSessionStatusEnum.COMPLETED.equals(status) ||
            ThinkingSessionStatusEnum.FAILED.equals(status)) {
            entity.setEndTime(new Date());
        }
        thinkingSessionEntityMapper.updateByPrimaryKeySelective(entity);
    }

    @Override
    public ThinkingSession getSession(Long sessionId) {
        ThinkingSessionEntity entity = thinkingSessionEntityMapper.selectByPrimaryKey(sessionId);
        if (entity == null) {
            return null;
        }
        ThinkingSession.ThinkingSessionBuilder builder = ThinkingSession.builder()
                .id(entity.getId())
                .query(entity.getQuery())
                .status(entity.getStatus())
                .totalPlannedSteps(entity.getTotalPlannedSteps())
                .actualSteps(entity.getActualSteps())
                .startTime(entity.getStartTime())
                .endTime(entity.getEndTime());
        try {
            if (entity.getSearchResults() != null) {
                builder.searchResults(objectMapper.readValue(entity.getSearchResults(), Map.class));
            }
            if (entity.getSearchConfig() != null) {
                builder.searchConfig(objectMapper.readValue(entity.getSearchConfig(), Map.class));
            }
        } catch (JsonProcessingException e) {
            log.error("反序列化会话数据失败", e);
            throw new RuntimeException("反序列化会话数据失败", e);
        }
        return builder.build();
    }

    @Override
    public void updateSessionSearchResults(Long sessionId, Map<String, Object> searchResults) {
        ThinkingSessionEntity entity = new ThinkingSessionEntity();
        entity.setId(sessionId);

        try {
            if (searchResults != null) {
                entity.setSearchResults(objectMapper.writeValueAsString(searchResults));
            }
        } catch (JsonProcessingException e) {
            log.error("序列化搜索结果失败", e);
            throw new RuntimeException("序列化搜索结果失败", e);
        }
        thinkingSessionEntityMapper.updateByPrimaryKeySelective(entity);
    }

    @Override
    public void updateActualSteps(Long sessionId, Long actualSteps) {
        ThinkingSessionEntity entity = new ThinkingSessionEntity();
        entity.setId(sessionId);
        entity.setActualSteps(actualSteps);
        thinkingSessionEntityMapper.updateByPrimaryKeySelective(entity);
    }

    @Override
    public void finishSession(Long sessionId, ThinkingSessionStatusEnum status) {
        ThinkingSessionEntity entity = new ThinkingSessionEntity();
        entity.setId(sessionId);
        entity.setStatus(status.getCode());
        entity.setEndTime(new Date());

        thinkingSessionEntityMapper.updateByPrimaryKeySelective(entity);
    }
} 