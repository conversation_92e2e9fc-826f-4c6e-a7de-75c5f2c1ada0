package com.sankuai.dzhealth.ai.service.agent.domain.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 聊天会话领域实体
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ChatSessionEntity {

    /**
     * 主键
     */
    private Long id;

    /**
     * 会话ID
     */
    private String sessionId;

    /**
     * 业务场景
     */
    private String bizType;

    /**
     * 关联业务ID
     */
    private String bizId;

    /**
     * 会话标题
     */
    private String title;

    /**
     * 会话总结摘要
     */
    private String digest;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 平台
     */
    private Integer platform;

    /**
     * 状态(0:正常 1:删除)
     */
    private Integer status;

    /**
     * 记忆
     */
    private String memory;

    /**
     * 扩展数据
     */
    private String extra;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    // 业务常量定义
    public static class Status {
        public static final Integer NORMAL = 0;
        public static final Integer DELETED = 1;
    }

    public static class BizType {
    }

    // 业务方法

    /**
     * 判断会话是否正常
     */
    public boolean isNormal() {
        return Status.NORMAL.equals(this.status);
    }

    /**
     * 判断会话是否已删除
     */
    public boolean isDeleted() {
        return Status.DELETED.equals(this.status);
    }
    
    /**
     * 判断会话是否有标题
     */
    public boolean hasTitle() {
        return title != null && !title.trim().isEmpty();
    }

    /**
     * 判断会话是否有摘要
     */
    public boolean hasDigest() {
        return digest != null && !digest.trim().isEmpty();
    }

    /**
     * 判断会话是否有记忆
     */
    public boolean hasMemory() {
        return memory != null && !memory.trim().isEmpty();
    }

    /**
     * 标记为已删除
     */
    public void markAsDeleted() {
        this.status = Status.DELETED;
        this.updateTime = new Date();
    }

    /**
     * 恢复会话（取消删除）
     */
    public void restore() {
        this.status = Status.NORMAL;
        this.updateTime = new Date();
    }

    /**
     * 更新会话标题
     */
    public void updateTitle(String newTitle) {
        if (newTitle != null && !newTitle.trim().isEmpty()) {
            this.title = newTitle.trim();
            this.updateTime = new Date();
        }
    }

    /**
     * 更新会话摘要
     */
    public void updateDigest(String newDigest) {
        if (newDigest != null && !newDigest.trim().isEmpty()) {
            this.digest = newDigest.trim();
            this.updateTime = new Date();
        }
    }

    /**
     * 更新记忆
     */
    public void updateMemory(String newMemory) {
        this.memory = newMemory;
        this.updateTime = new Date();
    }

    /**
     * 清空记忆
     */
    public void clearMemory() {
        this.memory = null;
        this.updateTime = new Date();
    }

    /**
     * 更新扩展数据
     */
    public void updateExtra(String newExtra) {
        this.extra = newExtra;
        this.updateTime = new Date();
    }

    /**
     * 验证实体的有效性
     */
    public boolean isValid() {
        return sessionId != null && !sessionId.trim().isEmpty()
                && userId != null && userId > 0
                && bizType != null && !bizType.trim().isEmpty()
                && platform != null
                && status != null;
    }

    /**
     * 判断会话是否属于指定用户
     */
    public boolean belongsToUser(Long targetUserId) {
        return this.userId != null && this.userId.equals(targetUserId);
    }

    /**
     * 判断会话是否在指定平台
     */
    public boolean isOnPlatform(Integer targetPlatform) {
        return this.platform != null && this.platform.equals(targetPlatform);
    }

    /**
     * 创建新的聊天会话
     */
    public static ChatSessionEntity createNewSession(String sessionId, String bizType, String bizId, String title,
                                                   Long userId, Integer platform) {

        return ChatSessionEntity.builder()
                .sessionId(sessionId)
                .bizType(bizType)
                .bizId(bizId)
                .title(title)
                .userId(userId)
                .platform(platform)
                .status(Status.NORMAL)
                .createTime(new Date())
                .updateTime(new Date())
                .build();
    }

    /**
     * 获取会话的简要信息
     */
    public String getBriefInfo() {
        StringBuilder sb = new StringBuilder();
        sb.append("会话ID: ").append(sessionId);
        if (hasTitle()) {
            sb.append(", 标题: ").append(title);
        }
        sb.append(", 业务类型: ").append(bizType);
        sb.append(", 状态: ").append(isNormal() ? "正常" : "已删除");
        return sb.toString();
    }
}
