package com.sankuai.dzhealth.ai.service.domain.contenttransfer.config;

import com.dianping.lion.Environment;
import com.meituan.service.inf.kms.client.Kms;
import com.meituan.service.inf.kms.utils.KmsResultNullException;
import com.sankuai.dzhealth.ai.service.domain.thinking.tool.ImageAnalysisTool;
import lombok.RequiredArgsConstructor;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.openai.OpenAiChatModel;
import org.springframework.ai.openai.OpenAiChatOptions;
import org.springframework.ai.openai.api.OpenAiApi;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Markdown转换配置类
 */
@Configuration
@RequiredArgsConstructor
public class MarkdownConvertConfig {

    private final ImageAnalysisTool imageAnalysisTool;

    /**
     * 配置Markdown转换专用的ChatClient
     *
     * @return ChatClient实例
     */
    @Bean("markdownConvertChatClient")  // 显式指定 Bean 名称
    public ChatClient markdownConvertChatClient() throws KmsResultNullException {

        OpenAiApi openAiApi = new OpenAiApi.Builder()
                .completionsPath("/chat/completions")
                .baseUrl("https://aigc.sankuai.com/v1/openai/native")
                .apiKey(Kms.getByName(Environment.getAppName(), "friday.search.appId"))
                .build();

        OpenAiChatOptions openAiChatOptions = OpenAiChatOptions.builder()
                .model("LongCat-Large-32K-Chat")
                .temperature(0.0)  // 保持最低温度以确保确定性输出
                .topP(0.1)         // 降低随机性，让输出更加一致
                .maxTokens(40000)  // Markdown转换可能需要更多token
                .build();

        OpenAiChatModel chatModel = OpenAiChatModel.builder()
                .openAiApi(openAiApi)
                .defaultOptions(openAiChatOptions)
                .build();

        return ChatClient.builder(chatModel)
                .defaultTools(imageAnalysisTool)  // 注册图片分析工具
                .build();
    }
}