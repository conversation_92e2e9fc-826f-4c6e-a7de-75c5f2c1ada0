package com.sankuai.dzhealth.ai.service.agent.domain.utils;

import com.sankuai.dzhealth.ai.service.domain.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * JSON提取工具类
 */
@Slf4j
public class JsonExtractorUtil {

    /**
     * 从文本中提取JSON内容
     * 支持提取被```json和```包裹的JSON内容
     *
     * @param text 包含JSON的文本
     * @return 提取出的JSON字符串，如果未找到则返回null
     */
    public static String extractJson(String text) {
        if (text == null || text.isEmpty()) {
            return null;
        }

        // 匹配```json和```之间的内容
        Pattern pattern = Pattern.compile("```json\\s*(.*?)\\s*```", Pattern.DOTALL);
        Matcher matcher = pattern.matcher(text);

        if (matcher.find()) {
            return matcher.group(1).trim();
        }

        // 如果没有找到```json格式，尝试匹配普通的JSON对象或数组
        pattern = Pattern.compile("\\{[^{}]*((\\{[^{}]*\\})[^{}]*)*\\}|\\[[^\\[\\]]*((\\[[^\\[\\]]*\\])[^\\[\\]]*)*\\]");
        matcher = pattern.matcher(text);

        if (matcher.find()) {
            return matcher.group(0).trim();
        }

        return null;
    }

    /**
     * 从文本中提取JSON内容并验证其有效性
     *
     * @param text 包含JSON的文本
     * @return 提取出的有效JSON字符串，如果未找到或无效则返回null
     */
    public static <T> T extractValidJson(String text, Class<T> clazz) {
        String jsonStr = extractJson(text);
        if (jsonStr == null) {
            return null;
        }
        // 验证JSON有效性
        T o = JsonUtils.parseObject(jsonStr, clazz);
        if (o == null) {
            log.warn("提取的JSON内容无效: {}", jsonStr);
        }
        return o;
    }
}

