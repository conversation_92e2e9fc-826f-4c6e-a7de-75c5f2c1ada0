package com.sankuai.dzhealth.ai.service.domain.service.strategy;

import com.sankuai.dzhealth.ai.service.domain.chat.model.IntentionResult;
import com.sankuai.dzhealth.ai.service.domain.utils.context.AiAnswerContext;
import com.sankuai.dzhealth.ai.service.infrastructure.utils.DateTimeTools;
import lombok.*;
import org.springframework.stereotype.Component;

import java.util.concurrent.CompletableFuture;

/**
 * 时间策略实现
 */
@Component
public class TimeStrategy implements DataSourceStrategy<TimeStrategy.TimeStrategyInfo> {

    @Override
    public boolean shouldExecute(IntentionResult intentionResult) {
        return intentionResult.getTimeNeed() != null && intentionResult.getTimeNeed();
    }

    @Override
    public CompletableFuture<TimeStrategy.TimeStrategyInfo> execute(AiAnswerContext context, String rewriteText) {
        return CompletableFuture.completedFuture(TimeStrategyInfo.builder().time(System.currentTimeMillis()).build());
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @EqualsAndHashCode(callSuper = true)
    public static class TimeStrategyInfo extends BaseStrategyInfo {

        private long time;
        @Override
        public String toPrompt() {
            return DateTimeTools.getWeekDesc(time);
        }
    }

} 