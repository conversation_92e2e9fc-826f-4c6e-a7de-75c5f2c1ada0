package com.sankuai.dzhealth.ai.service.domain.service.thinking.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.sankuai.dzhealth.ai.service.domain.model.thinking.SequentialThought;
import com.sankuai.dzhealth.ai.service.domain.service.thinking.ThinkingStepService;
import com.sankuai.dzhealth.ai.service.domain.thinking.util.SearchResultExtractor;
import com.sankuai.dzhealth.ai.service.infrastructure.dal.entity.ThinkingStepEntity;
import com.sankuai.dzhealth.ai.service.infrastructure.dal.mapper.ThinkingStepEntityMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 思考步骤服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ThinkingStepServiceImpl implements ThinkingStepService {

    private final ThinkingStepEntityMapper thinkingStepEntityMapper;
    private final ObjectMapper objectMapper;
    // 使用ThreadLocal存储当前线程关联的会话ID
    private static final ThreadLocal<Long> CURRENT_SESSION_ID = new ThreadLocal<>();

    @Override
    public Long saveThinkingStep(Long sessionId, SequentialThought thought) {
        ThinkingStepEntity entity = new ThinkingStepEntity();
        entity.setSessionId(sessionId);
        entity.setStepNumber(thought.getThoughtNumber());
        entity.setTotalSteps(thought.getTotalThoughts());
        entity.setThought(thought.getThought());
        entity.setIsRevision(thought.getIsRevision());
        entity.setRevisesStepNumber(thought.getRevisesThought());
        entity.setNextThoughtNeeded(thought.getNextThoughtNeeded());
        entity.setBranchFromStep(thought.getBranchFromThought());
        entity.setBranchId(thought.getBranchId());
        entity.setConfidenceScore(thought.getConfidenceScore());
        entity.setAddTime(new Date());
        entity.setUpdateTime(new Date());
        try {
            // 初始化搜索结果列表
            if (thought.getSearchResults() == null) {
                thought.setSearchResults(new ArrayList<>());
            }
            // 从思考内容中提取URL
            if (thought.getThought() != null) {
                List<String> extractedUrls = SearchResultExtractor.extractUrlsFromThought(thought.getThought());
                if (!extractedUrls.isEmpty()) {
                    log.info("从思考内容中提取到{}个URL", extractedUrls.size());
                    // 将提取的URL添加到搜索结果中
                    for (String url : extractedUrls) {
                        if (!thought.getSearchResults().contains(url)) {
                            thought.getSearchResults().add(url);
                        }
                    }
                }
            }
            // 序列化并保存搜索结果
            if (!thought.getSearchResults().isEmpty()) {
                entity.setSearchResults(objectMapper.writeValueAsString(thought.getSearchResults()));
            }
        } catch (JsonProcessingException e) {
            log.error("序列化搜索结果失败", e);
            throw new RuntimeException("序列化搜索结果失败", e);
        }
        thinkingStepEntityMapper.insertSelective(entity);
        return entity.getId();
    }

    @Override
    public List<SequentialThought> getThinkingSteps(Long sessionId) {
        List<ThinkingStepEntity> entities = thinkingStepEntityMapper.selectBySessionId(sessionId);
        List<SequentialThought> thoughts = new ArrayList<>();
        for (ThinkingStepEntity entity : entities) {
            SequentialThought.SequentialThoughtBuilder builder = SequentialThought.builder()
                    .thought(entity.getThought())
                    .thoughtNumber(entity.getStepNumber())
                    .totalThoughts(entity.getTotalSteps())
                    .isRevision(entity.getIsRevision())
                    .revisesThought(entity.getRevisesStepNumber())
                    .nextThoughtNeeded(entity.getNextThoughtNeeded())
                    .branchFromThought(entity.getBranchFromStep())
                    .branchId(entity.getBranchId())
                    .confidenceScore(entity.getConfidenceScore());
            try {
                if (entity.getSearchResults() != null) {
                    builder.searchResults(objectMapper.readValue(entity.getSearchResults(), List.class));
                }
            } catch (JsonProcessingException e) {
                log.error("反序列化搜索结果失败", e);
                throw new RuntimeException("反序列化搜索结果失败", e);
            }
            thoughts.add(builder.build());
        }
        return thoughts;
    }

    @Override
    public Long getCurrentSessionId() {
        return CURRENT_SESSION_ID.get();
    }

    @Override
    public void setCurrentSessionId(Long sessionId) {
        CURRENT_SESSION_ID.set(sessionId);
    }

    @Override
    public void clearCurrentSessionId() {
        CURRENT_SESSION_ID.remove();
    }
} 