package com.sankuai.dzhealth.ai.service.domain.thinking.tool;

import com.sankuai.dzhealth.ai.service.infrastructure.acl.FridayWebSearchAcl;
import com.sankuai.dzhealth.ai.service.infrastructure.model.FridaySearchResult;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.tool.annotation.Tool;
import org.springframework.ai.tool.annotation.ToolParam;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 * 网络搜索工具
 * 用于在序列思考过程中进行网络搜索
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class WebSearchTool {
    private final FridayWebSearchAcl fridayWebSearchAcl;
    /**
     * 网络搜索工具方法
     * 通过Spring AI的Tool注解注册为工具方法
     *
     * @param query 搜索查询
     * @param topK 返回结果数量
     * @return 格式化的搜索结果
     */
    @Tool(description = "根据用户查询进行网络搜索，获取最新的相关信息。" +
            "适用于需要查找最新资讯、医疗知识、研究进展等外部信息的场景。" +
            "搜索结果将包含标题、摘要和URL，便于进一步分析和引用。")
    public String webSearch(
            @ToolParam(description = "搜索关键词，必填。例如：'高血压最新治疗进展'") String query,
            @ToolParam(description = "返回的搜索结果数量，默认为5", required = false) Integer topK
    ) {
        log.info("执行网络搜索: 查询={}, 结果数量={}", query, topK);
        // 默认值处理
        int resultCount = topK != null && topK > 0 ? topK : 5;
        try {
            // 调用Friday搜索服务
            List<FridaySearchResult> results = fridayWebSearchAcl.search(
                query,
                Collections.emptyList(), // 不限制站点，也可以从配置中获取
                resultCount
            );
            // 格式化搜索结果
            if (results == null || results.isEmpty()) {
                return "未找到与\"" + query + "\"相关的搜索结果。";
            }
            StringBuilder formattedResults = new StringBuilder();
            formattedResults.append("以下是关于\"").append(query).append("\"的搜索结果：\n\n");
            for (int i = 0; i < results.size(); i++) {
                FridaySearchResult result = results.get(i);
                formattedResults.append(i + 1).append(". ");
                formattedResults.append("标题: ").append(result.getName()).append("\n");
                formattedResults.append("   摘要: ").append(result.getSnippet()).append("\n");
                formattedResults.append("   URL: ").append(result.getUrl()).append("\n\n");
            }
            return formattedResults.toString();
        } catch (Exception e) {
            log.error("搜索失败: {}", e.getMessage(), e);
            return "搜索过程中发生错误: " + e.getMessage();
        }
    }
}