package com.sankuai.dzhealth.ai.service.agent.domain.utils;

import groovy.util.logging.Slf4j;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.poi.ss.SpreadsheetVersion;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.lang.reflect.Field;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Map;

import static org.reflections.Reflections.log;

@Service
@Slf4j
public class ExcelUtils {
    static {
        SpreadsheetVersion excel2007 = SpreadsheetVersion.EXCEL2007;
        if (Integer.MAX_VALUE != excel2007.getMaxTextLength()) {
            Field field;
            try {
                field = excel2007.getClass().getDeclaredField("_maxTextLength");
                field.setAccessible(true);
                field.set(excel2007, Integer.MAX_VALUE);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }


    /**
     * Sheet数据封装类
     */
    @Data
    @NoArgsConstructor
    @Builder
    public static class SheetData {
        private String sheetName;
        private List<String> headers;
        private List<Map<String, String>> dataList;

        public SheetData(String sheetName, List<String> headers, List<Map<String, String>> dataList) {
            this.sheetName = sheetName;
            this.headers = headers;
            this.dataList = dataList;
        }

        // Getters and Setters
        public String getSheetName() {
            return sheetName;
        }

        public void setSheetName(String sheetName) {
            this.sheetName = sheetName;
        }

        public List<String> getHeaders() {
            return headers;
        }

        public void setHeaders(List<String> headers) {
            this.headers = headers;
        }

        public List<Map<String, String>> getDataList() {
            return dataList;
        }

        public void setDataList(List<Map<String, String>> dataList) {
            this.dataList = dataList;
        }
    }

    /**
     * 创建表头样式
     */
    private CellStyle createHeaderStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();
        // 设置背景色
        style.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        // 设置边框
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        // 设置居中对齐
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        // 设置字体
        Font font = workbook.createFont();
        font.setBold(true);
        style.setFont(font);
        return style;
    }

    /**
     * 创建数据单元格样式
     */
    private CellStyle createDataStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();
        // 设置边框
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        // 设置垂直居中
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        return style;
    }

    /**
     * 创建灰色背景的数据单元格样式
     */
    private CellStyle createGreyDataStyle(Workbook workbook) {
        CellStyle style = createDataStyle(workbook);
        // 设置灰色背景
        style.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        return style;
    }

    /**
     * 创建白色背景的数据单元格样式
     */
    private CellStyle createWhiteDataStyle(Workbook workbook) {
        CellStyle style = createDataStyle(workbook);
        // 设置白色背景
        style.setFillForegroundColor(IndexedColors.WHITE1.getIndex());
        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        return style;
    }

    /**
     * 根据行数据中的颜色信息选择合适的样式
     *
     * @param rowData 行数据
     * @param defaultStyle 默认样式
     * @param greyStyle 灰色样式
     * @param whiteStyle 白色样式
     * @return 选择的样式
     */
    private CellStyle getDataStyleByColor(Map<String, String> rowData,
                                         CellStyle defaultStyle,
                                         CellStyle greyStyle,
                                         CellStyle whiteStyle) {
        if (rowData == null || !rowData.containsKey("color")) {
            return defaultStyle;
        }

        String color = rowData.get("color");
        if ("grey".equals(color)) {
            return greyStyle;
        } else if ("white".equals(color)) {
            return whiteStyle;
        } else {
            return defaultStyle;
        }
    }


    /**
     * 生成多个Sheet的Excel文件
     *
     * @param fileName      Excel文件名（包含路径）
     * @param sheetDataList 多个Sheet的数据
     * @return ResponseEntity包含Excel文件的字节数组
     */
    public ResponseEntity<byte[]> generateMultiSheetExcel(String fileName, List<SheetData> sheetDataList) {
        // 参数校验
        if (fileName == null || fileName.isEmpty() || sheetDataList == null || sheetDataList.isEmpty()) {
            log.error("生成多Sheet Excel参数错误: fileName={}, sheetDataList={}", fileName, sheetDataList);
            return null;
        }

            // 创建工作簿
        try (Workbook workbook = new XSSFWorkbook()) {
            // 创建表头样式
            CellStyle headerStyle = createHeaderStyle(workbook);

            // 预创建数据单元格样式，避免重复创建
            CellStyle defaultDataStyle = createDataStyle(workbook);
            CellStyle greyDataStyle = createGreyDataStyle(workbook);
            CellStyle whiteDataStyle = createWhiteDataStyle(workbook);

            // 为每个SheetData创建一个Sheet
            for (int sheetIndex = 0; sheetIndex < sheetDataList.size(); sheetIndex++) {
                SheetData sheetData = sheetDataList.get(sheetIndex);

                // 参数校验
                if (sheetData.getHeaders() == null || sheetData.getHeaders().isEmpty() || sheetData.getDataList() == null) {
                    log.warn("跳过无效的Sheet数据: sheetName={}", sheetData.getSheetName());
                    continue;
                }

                // 创建工作表，如果没有指定名称则使用默认名称
                String sheetName = sheetData.getSheetName();
                if (sheetName == null || sheetName.isEmpty()) {
                    sheetName = "Sheet" + (sheetIndex + 1);
                }
                Sheet sheet = workbook.createSheet(sheetName);

                // 创建表头行
                Row headerRow = sheet.createRow(0);
                List<String> headers = sheetData.getHeaders();
                for (int i = 0; i < headers.size(); i++) {
                    Cell cell = headerRow.createCell(i);
                    cell.setCellValue(headers.get(i));
                    cell.setCellStyle(headerStyle);
                }

                // 填充数据行
                List<Map<String, String>> dataList = sheetData.getDataList();
                for (int i = 0; i < dataList.size(); i++) {
                    Row dataRow = sheet.createRow(i + 1);
                    Map<String, String> rowData = dataList.get(i);

                    // 根据颜色选择合适的样式
                    CellStyle dataStyle = getDataStyleByColor(rowData, defaultDataStyle, greyDataStyle, whiteDataStyle);

                    for (int j = 0; j < headers.size(); j++) {
                        Cell cell = dataRow.createCell(j);
                        String header = headers.get(j);
                        String value = rowData.getOrDefault(header, "");
                        cell.setCellValue(value);
                        cell.setCellStyle(dataStyle);
                    }
                }

                // 调整列宽
                for (int i = 0; i < headers.size(); i++) {
                    sheet.autoSizeColumn(i);
                    // 设置一个最小宽度
                    int currentWidth = sheet.getColumnWidth(i);
                    if (currentWidth < 3000) {
                        sheet.setColumnWidth(i, 3000);
                    }
                }
            }

            // 将工作簿写入字节数组输出流
            try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
                workbook.write(outputStream);

                // 设置响应头
                HttpHeaders httpHeaders = new HttpHeaders();
                httpHeaders.setContentDispositionFormData("attachment", fileName + ".xlsx");
                httpHeaders.setContentType(MediaType.parseMediaType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"));

                return new ResponseEntity<>(outputStream.toByteArray(), httpHeaders, HttpStatus.OK);
            }

        } catch (IOException e) {
            log.error("生成多Sheet Excel文件失败: {}", fileName, e);
            return null;
        }
    }

    /**
     * 生成CSV文件
     *
     * @param fileName CSV文件名
     * @param headers  表头列表
     * @param dataList 数据列表
     * @return ResponseEntity包含CSV文件的字节数组
     */
    public ResponseEntity<byte[]> generateCSV(String fileName, List<String> headers, List<Map<String, String>> dataList) {
        // 参数校验
        if (fileName == null || fileName.isEmpty() || headers == null || headers.isEmpty() || dataList == null) {
            log.error("生成CSV参数错误: fileName={}, headers={}, dataList={}", fileName, headers, dataList);
            return null;
        }

        try {
            StringBuilder csvContent = new StringBuilder();

            // 添加BOM以支持中文显示
            csvContent.append('\ufeff');

            // 写入表头
            csvContent.append(String.join(",", headers.stream()
                            .map(this::escapeCsvValue)
                            .toArray(String[]::new)))
                    .append("\n");

            // 写入数据行
            for (Map<String, String> rowData : dataList) {
                String[] rowValues = headers.stream()
                        .map(header -> escapeCsvValue(rowData.getOrDefault(header, "")))
                        .toArray(String[]::new);
                csvContent.append(String.join(",", rowValues)).append("\n");
            }

            // 转换为字节数组
            byte[] csvBytes = csvContent.toString().getBytes(StandardCharsets.UTF_8);

            // 设置响应头
            HttpHeaders httpHeaders = new HttpHeaders();
            String finalFilename = fileName + ".csv";
            httpHeaders.setContentDispositionFormData("attachment", finalFilename);
            httpHeaders.setContentType(MediaType.parseMediaType("text/csv; charset=UTF-8"));
            httpHeaders.setContentLength(csvBytes.length);

            return new ResponseEntity<>(csvBytes, httpHeaders, HttpStatus.OK);

        } catch (Exception e) {
            log.error("生成CSV文件失败: {}", fileName, e);
            return null;
        }
    }

    /**
     * CSV数据封装类
     */
    @Data
    @NoArgsConstructor
    @Builder
    public static class CsvData {
        private String csvName;
        private List<String> headers;
        private List<Map<String, String>> dataList;

        public CsvData(String csvName, List<String> headers, List<Map<String, String>> dataList) {
            this.csvName = csvName;
            this.headers = headers;
            this.dataList = dataList;
        }

        // Getters and Setters
        public String getCsvName() {
            return csvName;
        }

        public void setCsvName(String csvName) {
            this.csvName = csvName;
        }

        public List<String> getHeaders() {
            return headers;
        }

        public void setHeaders(List<String> headers) {
            this.headers = headers;
        }

        public List<Map<String, String>> getDataList() {
            return dataList;
        }

        public void setDataList(List<Map<String, String>> dataList) {
            this.dataList = dataList;
        }
    }

    /**
     * 转义CSV值，处理包含逗号、引号、换行符的情况
     *
     * @param value 原始值
     * @return 转义后的值
     */
    private String escapeCsvValue(String value) {
        if (value == null) {
            return "";
        }

        // 如果包含逗号、引号、换行符，需要用引号包围并转义内部引号
        if (value.contains(",") || value.contains("\"") || value.contains("\n") || value.contains("\r")) {
            return "\"" + value.replace("\"", "\"\"") + "\"";
        }

        return value;
    }

}