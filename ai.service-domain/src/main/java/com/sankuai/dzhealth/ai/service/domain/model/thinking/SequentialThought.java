package com.sankuai.dzhealth.ai.service.domain.model.thinking;

import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 序列思考模型
 */
@Data
@Builder
public class SequentialThought {
    /**
     * 思考内容
     */
    private String thought;
    
    /**
     * 思考序号
     */
    private Long thoughtNumber;
    
    /**
     * 总思考步骤数
     */
    private Long totalThoughts;
    
    /**
     * 是否是对之前思考的修正
     */
    private Boolean isRevision;
    
    /**
     * 修正的是哪个思考步骤
     */
    private Long revisesThought;
    
    /**
     * 从哪个思考步骤分支出来的
     */
    private Long branchFromThought;
    
    /**
     * 分支ID
     */
    private String branchId;
    
    /**
     * 是否需要更多思考步骤
     */
    private Boolean needsMoreThoughts;
    
    /**
     * 是否需要下一个思考步骤
     */
    private Boolean nextThoughtNeeded;
    
    /**
     * 关联的搜索结果
     */
    private List<String> searchResults;
    
    /**
     * 思考的置信度评分
     */
    private BigDecimal confidenceScore;
}