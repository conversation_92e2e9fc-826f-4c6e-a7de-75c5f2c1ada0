package com.sankuai.dzhealth.ai.service.agent.domain.tools;

import org.springframework.ai.tool.annotation.Tool;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/**
 * @author:chenwei
 * @time: 2025/7/11 17:01
 * @version: 0.0.1
 */

@Component
public class DateTimeToolTest {

    @Tool(description = "Get the current date and time in the user's timezone", returnDirect = false)
    String getCurrentDateTime() {
        return LocalDateTime.now().atZone(LocaleContextHolder.getTimeZone().toZoneId()).toString();
    }
}
