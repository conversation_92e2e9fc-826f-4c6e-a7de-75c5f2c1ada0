package com.sankuai.dzhealth.ai.service.domain.finetune;

import com.sankuai.dzhealth.ai.service.domain.utils.context.AiAnswerContext;
import com.sankuai.dzhealth.ai.service.infrastructure.dal.entity.AiAnswerContextEntityWithBLOBs;
import com.sankuai.dzhealth.ai.service.infrastructure.dal.entity.FineTuneDataEntityWithBLOBs;
import com.sankuai.dzhealth.ai.service.infrastructure.dal.mapper.AiAnswerContextEntityMapper;
import com.sankuai.dzhealth.ai.service.infrastructure.dal.mapper.FineTuneDataEntityMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

import static com.sankuai.dzhealth.ai.service.domain.utils.JsonUtils.toJsonString;

/**
 * @author: yangweicheng
 * @date: 2025/5/7 15:20
 * @version: 1.0
 */

@Repository
@Slf4j
public class FineTuneRepository {

    @Resource
    private AiAnswerContextEntityMapper aiAnswerContextEntityMapper;

    @Resource
    private FineTuneDataEntityMapper fineTuneDataEntityMapper;

    public void insertEvaluations(List<FineTuneData> result) {
        if (CollectionUtils.isEmpty(result)) {
            return;
        }
        for (FineTuneData data : result) {
            FineTuneDataEntityWithBLOBs entity = FineTuneDataEntityWithBLOBs.builder()
                    .sessionId(data.getSessionId())
                    .msgId(data.getMsgId())
                    .dataKey(data.getDataKey())
                    .description(data.getDescription())
                    .success(data.getSuccess())
                    .feedback(data.getFeedback())
                    .metadata(data.getMetadata())
                    .createdAt(new Date())
                    .updatedAt(new Date())
                    .build();
            fineTuneDataEntityMapper.insertSelective(entity);
        }
    }

    public void insertAiAnswerContext(AiAnswerContext context) {
        AiAnswerContextEntityWithBLOBs entity = convertToEntityBLOBs(context);
        int res = aiAnswerContextEntityMapper.insertSelective(entity);
        log.info("insertAiAnswerContext success: {}", res);
    }

    private static AiAnswerContextEntityWithBLOBs convertToEntityBLOBs(AiAnswerContext context) {
        AiAnswerContextEntityWithBLOBs entity = new AiAnswerContextEntityWithBLOBs();

        entity.setUserid(context.getUserId());
        entity.setLat(context.getLat());
        entity.setLng(context.getLng());
        entity.setClienttype(context.getClientType());
        entity.setUuid(context.getUuid());
        entity.setAppversion(context.getAppVersion());
        entity.setCityid(context.getCityId());
        entity.setSessionid(context.getSessionId());
        entity.setMsgid(context.getMsgId());
        entity.setRequestid(context.getRequestId());
        entity.setExtension(context.getExtension());
        entity.setBizscene(context.getBizScene());
        entity.setRequesttime(context.getRequestTime());
        entity.setContent(context.getContent());
        entity.setType(context.getType());
        entity.setRole(context.getRole());
        entity.setHistory(context.getHistory());
        entity.setShopid(context.getShopId());
        entity.setMtshopid(context.getMtShopId());
        entity.setStream(context.getStream());
        entity.setPlatform(context.getPlatform());
        entity.setConversationidstr(context.getConversationIdStr());
        entity.setMessageidstr(context.getMessageIdStr());
        entity.setTraceid(context.getTraceId());
        entity.setBusinesstype(context.getBusinessType());
        entity.setBusinessid(context.getBusinessId());
        entity.setAssistantvisiblecontent(context.getAssistantVisibleContent());
        entity.setAssistantcontent(context.getAssistantContent());
        entity.setStatus(context.getStatus());

        // Convert complex objects to JSON
        entity.setMsgcontext(StringUtils.EMPTY);
        entity.setSpans(toJsonString(context.getSpans()));
        entity.setDepartname2idmap(toJsonString(context.getDepartName2IdMap()));
        entity.setStddepartname2idmap(toJsonString(context.getStdDepartName2IdMap()));
        entity.setSearchinfo(toJsonString(context.getSearchInfo()));
        entity.setRaginfo(toJsonString(context.getRagInfo()));

        return entity;
    }
}
