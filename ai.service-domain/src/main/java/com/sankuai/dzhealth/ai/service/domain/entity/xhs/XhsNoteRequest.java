package com.sankuai.dzhealth.ai.service.domain.entity.xhs;

import com.google.common.collect.Lists;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.util.HashSet;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

@Data
@NoArgsConstructor
public class XhsNoteRequest {
    // 批次
    private String batch;
    // 方向
    private ThemeEntity theme;
    // 品类
    private CategoryEntity category;
    // 场景
    private SceneEntity scene;
    // 人群
    private DrovesEntity droves;
    // 生成篇数
    private int count;

    private String hotNoteBatch;

    private Long id;

    public List<TCSD> buildCombine() {
        List<Pair> themePairs = theme.toPair();
        List<Pair> categoryPairs = category.toPair();
        List<Pair> scenePairs = scene.toPair();
        List<Pair> drovesPairs = droves.toPair();
        List<List<Pair>> pairs = Lists.newArrayList();
        if (!themePairs.isEmpty()) pairs.add(themePairs);
        if (!categoryPairs.isEmpty()) pairs.add(categoryPairs);
        if (!scenePairs.isEmpty()) pairs.add(scenePairs);
        if (!drovesPairs.isEmpty()) pairs.add(drovesPairs);
        Set<Set<Pair>> uniqueCombinations = generateUniqueCombinations(pairs);
        return uniqueCombinations.stream().map(TCSD::new).toList();
    }

    private Set<Set<Pair>> generateUniqueCombinations(List<List<Pair>> pairs) {
        Set<Set<Pair>> result = new HashSet<>();

        if (pairs.isEmpty()) {
            return result;
        }
        generateCombinations(pairs, 0, Lists.newArrayList(), result);
        return result;
    }

    private void generateCombinations(List<List<Pair>> pairs, int index, List<Pair> current, Set<Set<Pair>> result) {
        if (index == pairs.size()) {
            result.add(new HashSet<>(current));
            return;
        }
        for (Pair pair : pairs.get(index)) {
            current.add(pair);
            generateCombinations(pairs, index + 1, current, result);
            current.remove(current.size() - 1);
        }
    }

    public Long safeGetId() {
        return Optional.ofNullable(id).orElse(368763L);
    }

    @Data
    @NoArgsConstructor
    public static class TCSD {
        private String theme;
        private String category;
        private String scene;
        private String droves;

        public TCSD(Set<Pair> pairSet) {
            pairSet.forEach(pair -> {
                switch (pair.key) {
                    case "theme" -> this.setTheme(pair.value);
                    case "category" -> this.setCategory(pair.value);
                    case "scene" -> this.setScene(pair.value);
                    case "droves" -> this.setDroves(pair.value);
                    default -> {}
                }
            });
        }

        public String join() {
            return Lists.newArrayList(theme, category, scene, droves)
                    .stream().filter(StringUtils::isNotBlank)
                    .collect(Collectors.joining(","));
        }
    }

}
