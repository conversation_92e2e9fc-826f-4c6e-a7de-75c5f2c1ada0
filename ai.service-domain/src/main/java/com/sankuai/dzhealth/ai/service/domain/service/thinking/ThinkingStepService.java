package com.sankuai.dzhealth.ai.service.domain.service.thinking;

import com.sankuai.dzhealth.ai.service.domain.model.thinking.SequentialThought;

import java.util.List;

/**
 * 思考步骤服务接口
 */
public interface ThinkingStepService {
    
    /**
     * 保存思考步骤
     *
     * @param sessionId 会话ID
     * @param thought 思考步骤
     * @return 步骤ID
     */
    Long saveThinkingStep(Long sessionId, SequentialThought thought);
    
    /**
     * 获取会话的所有思考步骤
     *
     * @param sessionId 会话ID
     * @return 思考步骤列表
     */
    List<SequentialThought> getThinkingSteps(Long sessionId);
    
    /**
     * 获取当前线程关联的会话ID
     *
     * @return 会话ID
     */
    Long getCurrentSessionId();
    
    /**
     * 设置当前线程关联的会话ID
     *
     * @param sessionId 会话ID
     */
    void setCurrentSessionId(Long sessionId);
    
    /**
     * 清除当前线程关联的会话ID
     */
    void clearCurrentSessionId();
} 