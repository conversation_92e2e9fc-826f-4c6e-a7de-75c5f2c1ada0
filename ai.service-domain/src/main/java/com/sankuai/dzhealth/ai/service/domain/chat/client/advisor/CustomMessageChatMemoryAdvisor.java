package com.sankuai.dzhealth.ai.service.domain.chat.client.advisor;

import com.dianping.rhino.Rhino;
import com.dianping.rhino.threadpool.DefaultThreadPoolProperties;
import com.dianping.rhino.threadpool.ThreadPool;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.client.ChatClientMessageAggregator;
import org.springframework.ai.chat.client.ChatClientRequest;
import org.springframework.ai.chat.client.ChatClientResponse;
import org.springframework.ai.chat.client.advisor.api.*;
import org.springframework.ai.chat.memory.ChatMemory;
import org.springframework.ai.chat.messages.AssistantMessage;
import org.springframework.ai.chat.messages.Message;
import org.springframework.ai.chat.messages.UserMessage;
import org.springframework.util.Assert;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Scheduler;

import java.util.*;

/**
 * @author: duanxiaowen
 * @date: 2025/3/21
 */
@Slf4j
public class CustomMessageChatMemoryAdvisor implements BaseChatMemoryAdvisor {
    public static final ThreadPool CHAT_MESSAGE_POOL = Rhino.newThreadPool("CHAT_MESSAGE_POOL",
            DefaultThreadPoolProperties.Setter().withCoreSize(100).withMaxSize(200).withMaxQueueSize(1000));

    private ChatMemory chatMemory;

    private String defaultConversationId;

    private int order;

    private Scheduler scheduler;

    private boolean includeHistoryMessages = false;


    protected ChatMemory getChatMemoryStore() {
        return this.chatMemory;
    }

    public CustomMessageChatMemoryAdvisor(ChatMemory chatMemory) {
        this.chatMemory = chatMemory;
        log.info("CustomMessageChatMemoryAdvisor已初始化，默认包含历史消息: {}", includeHistoryMessages);
    }

    public CustomMessageChatMemoryAdvisor(ChatMemory chatMemory, boolean includeHistoryMessages) {
        this.chatMemory = chatMemory;
        this.includeHistoryMessages = includeHistoryMessages;
        log.info("CustomMessageChatMemoryAdvisor已初始化，包含历史消息: {}", includeHistoryMessages);
    }


    private CustomMessageChatMemoryAdvisor(ChatMemory chatMemory, String defaultConversationId, int order,
                                     Scheduler scheduler, boolean includeHistoryMessages) {
        Assert.notNull(chatMemory, "chatMemory cannot be null");
        Assert.hasText(defaultConversationId, "defaultConversationId cannot be null or empty");
        Assert.notNull(scheduler, "scheduler cannot be null");
        this.chatMemory = chatMemory;
        this.defaultConversationId = defaultConversationId;
        this.order = order;
        this.scheduler = scheduler;
        this.includeHistoryMessages = includeHistoryMessages;
    }

    /**
     * 设置是否包含历史消息
     * @param includeHistoryMessages true表示包含历史消息，false表示不包含
     */
    public void setIncludeHistoryMessages(boolean includeHistoryMessages) {
        this.includeHistoryMessages = includeHistoryMessages;
        log.info("设置包含历史消息: {}", includeHistoryMessages);
    }

    /**
     * 获取当前是否包含历史消息的设置
     * @return 当前是否包含历史消息
     */
    public boolean isIncludeHistoryMessages() {
        return includeHistoryMessages;
    }

    @Override
    public ChatClientRequest before(ChatClientRequest chatClientRequest, AdvisorChain advisorChain) {
        String conversationId = this.getConversationId(chatClientRequest.context(), defaultConversationId);
        log.info("执行doBefore, 会话ID: {}, 历史消息获取数量: {}, 是否包含历史消息: {}",
                conversationId, includeHistoryMessages);

        // 1. 准备新的消息列表
        List<Message> advisedMessages = new ArrayList<>(chatClientRequest.prompt().getInstructions());

        // 2.根据开关决定是否添加历史消息
        if (includeHistoryMessages) {
            // 1. 获取历史消息
            List<Message> memoryMessages = this.getChatMemoryStore().get(conversationId);
            advisedMessages.addAll(memoryMessages);
            log.info("已添加历史消息，合并后的消息总数: {}", advisedMessages.size());
        } else {
            log.info("未添加历史消息，请求消息数量: {}", advisedMessages.size());
        }

        // 3. 从advisorParams获取参数
        Map<String, Object> metadata = new HashMap<>();
        String messageId = UUID.randomUUID().toString();
        metadata.put("messageId", messageId);
        metadata.put("conversationId", conversationId);
        metadata.put("senderId", chatClientRequest.context().getOrDefault("senderId", "user"));
        metadata.put("contentType", chatClientRequest.context().getOrDefault("contentType", (byte)1));
        metadata.put("sequence", System.currentTimeMillis());
        metadata.put("auditStatus", chatClientRequest.context().getOrDefault("auditStatus", (byte)1));
        metadata.put("createdAt", new Date());
        log.info("创建用户消息metadata, messageId: {}", messageId);

        // 4. 创建新的请求
        ChatClientRequest advisedRequest = chatClientRequest;
        if (includeHistoryMessages && !advisedMessages.isEmpty()) {
            advisedRequest = chatClientRequest.mutate()
                    .prompt(chatClientRequest.prompt().mutate().messages(advisedMessages).build())
                    .build();
        }

        CHAT_MESSAGE_POOL.execute(() -> {
            // 5. 创建带metadata的UserMessage并存储
            UserMessage userMessage = new UserMessage.Builder().text("<<UserPrompt>>" + chatClientRequest.prompt().getUserMessage().getText()).media(chatClientRequest.prompt().getUserMessage().getMedia()).metadata(metadata).build();
            log.info("保存用户消息到数据库, 会话ID: {}, 消息ID: {}", conversationId, messageId);
            this.getChatMemoryStore().add(conversationId, List.of(userMessage));
        });

        return advisedRequest;
    }

    @Override
    public ChatClientResponse after(ChatClientResponse chatClientResponse, AdvisorChain advisorChain)  {
        try {
            String conversationId = this.getConversationId(chatClientResponse.context(), defaultConversationId);

            if (chatClientResponse == null || chatClientResponse.chatResponse() == null) {
                log.warn("advisedResponse为空或response为空, 无法处理模型响应");
                return chatClientResponse;
            }

            if (chatClientResponse.chatResponse().getResults() == null || chatClientResponse.chatResponse().getResults().isEmpty()) {
                log.warn("advisedResponse.response.results为空, 无法处理模型响应");
                return chatClientResponse;
            }

            List<Message> assistantMessages = chatClientResponse.chatResponse()
                .getResults()
                .stream()
                .map(g -> {
                    try {
                        Message originalMessage = (Message) g.getOutput();
                        String messageText = originalMessage.getText();
                        log.info("处理模型响应消息, 内容长度: {}",
                            messageText != null ? messageText.length() : 0);

                        String messageId = UUID.randomUUID().toString();
                        Map<String, Object> metadata = new HashMap<>();
                        metadata.put("messageId", messageId);
                        metadata.put("conversationId", conversationId);
                        metadata.put("senderId", chatClientResponse.context().getOrDefault("assistantId", "assistant"));
                        metadata.put("contentType", (byte)1);
                        metadata.put("sequence", System.currentTimeMillis());
                        metadata.put("auditStatus", (byte)1);
                        metadata.put("createdAt", new Date());

                        // 显式转换为 Message 类型
                        Message message = new AssistantMessage(messageText, metadata);
                        log.info("创建助手消息完成, messageId: {}", messageId);
                        return message;
                    } catch (Exception e) {
                        log.error("处理模型响应消息时出错: {}", e.getMessage(), e);
                        return null;
                    }
                })
                .filter(Objects::nonNull)
                .toList();

            log.info("准备保存 {} 条助手消息到数据库, 会话ID: {}", assistantMessages.size(), conversationId);
            if (!assistantMessages.isEmpty()) {
                this.getChatMemoryStore().add(conversationId, assistantMessages);
                log.info("助手消息保存到数据库完成, 会话ID: {}", conversationId);
            } else {
                log.warn("没有助手消息需要保存, 会话ID: {}", conversationId);
            }
        } catch (Exception e) {
            log.error("doObserveAfter处理时发生异常: {}", e.getMessage(), e);
        }
        return chatClientResponse;
    }


    @Override
    public Flux<ChatClientResponse> adviseStream(ChatClientRequest chatClientRequest,
                                                 StreamAdvisorChain streamAdvisorChain) {
        // Get the scheduler from BaseAdvisor
        Scheduler scheduler = this.getScheduler();

        // Process the request with the before method
        return Mono.just(chatClientRequest)
                .publishOn(scheduler)
                .map(request -> this.before(request, streamAdvisorChain))
                .flatMapMany(streamAdvisorChain::nextStream)
                .transform(flux -> new ChatClientMessageAggregator().aggregateChatClientResponse(flux,
                        response -> this.after(response, streamAdvisorChain)));
    }

    public static Builder builder(ChatMemory chatMemory) {
        return new Builder(chatMemory);
    }

    @Override
    public int getOrder() {
        return this.order;
    }

    public static class Builder {
        private String conversationId = ChatMemory.DEFAULT_CONVERSATION_ID;

        private int order = Advisor.DEFAULT_CHAT_MEMORY_PRECEDENCE_ORDER;

        private Scheduler scheduler = BaseAdvisor.DEFAULT_SCHEDULER;

        private ChatMemory chatMemory;

        private boolean includeHistoryMessages = false;

        private int chatMemoryRetrieveSize;

        private Builder(ChatMemory chatMemory) {
            this.chatMemory = chatMemory;
        }

        /**
         * Set the conversation id.
         * @param conversationId the conversation id
         * @return the builder
         */
        public CustomMessageChatMemoryAdvisor.Builder conversationId(String conversationId) {
            this.conversationId = conversationId;
            return this;
        }

        /**
         * Set the order.
         * @param order the order
         * @return the builder
         */
        public CustomMessageChatMemoryAdvisor.Builder order(int order) {
            this.order = order;
            return this;
        }

        public CustomMessageChatMemoryAdvisor.Builder scheduler(Scheduler scheduler) {
            this.scheduler = scheduler;
            return this;
        }

        public CustomMessageChatMemoryAdvisor.Builder includeHistoryMessages(boolean includeHistoryMessages) {
            this.includeHistoryMessages = includeHistoryMessages;
            return this;
        }

        public CustomMessageChatMemoryAdvisor.Builder chatMemoryRetrieveSize(int chatMemoryRetrieveSize) {
            this.chatMemoryRetrieveSize = chatMemoryRetrieveSize;
            return this;
        }

        /**
         * Build the advisor.
         * @return the advisor
         */
        public CustomMessageChatMemoryAdvisor build() {
            return new CustomMessageChatMemoryAdvisor(this.chatMemory, this.conversationId, this.order, this.scheduler, includeHistoryMessages);
        }
    }
}
