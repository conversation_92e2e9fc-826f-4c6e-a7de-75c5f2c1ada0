package com.sankuai.dzhealth.ai.service.domain.card.handlers;

import com.alibaba.fastjson.JSONObject;
import com.sankuai.dzhealth.ai.service.domain.card.AbstractCardHandler;
import com.sankuai.dzhealth.ai.service.domain.utils.context.AiAnswerContext;
import com.sankuai.dzhealth.ai.service.dto.stream.StreamEventDTO;
import com.sankuai.dzhealth.ai.service.enums.StreamEventCardTypeEnum;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 相关问题卡片处理器
 */
@Component
@Order(6)
public class RelatedQuestionCardHandler extends AbstractCardHandler {
    
    @Override
    public boolean supports(String fieldName) {
        return "related_question".equals(fieldName);
    }
    
    @Override
    public List<StreamEventDTO> handle(JSONObject jsonObject, 
                                    AiAnswerContext context, 
                                    SseEmitter sseEmitter, 
                                    AtomicInteger sseIndex,
                                    long startChatTime) {
        List<String> relatedQuestions = jsonObject.getJSONArray("related_question").toJavaList(String.class);
        if (CollectionUtils.isNotEmpty(relatedQuestions)) {
            Map<String, Object> cardProps = new HashMap<>();
            cardProps.put("questions", relatedQuestions);
            
            StreamEventDTO relatedQuestionsEvent = sendCardEvent(
                    StreamEventCardTypeEnum.RELATED_QUESTION_CARD,
                    "relatedQuestions", 
                    cardProps, 
                    sseEmitter, 
                    sseIndex.getAndIncrement()
            );
            
            return singletonEvent(relatedQuestionsEvent);
        }
        
        return emptyEvents();
    }

    @Override
    public int getOrder() {
        return 6;
    }
} 