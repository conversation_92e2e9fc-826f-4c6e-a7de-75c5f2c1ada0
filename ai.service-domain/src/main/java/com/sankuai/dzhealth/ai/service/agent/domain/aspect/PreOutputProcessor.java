package com.sankuai.dzhealth.ai.service.agent.domain.aspect;

import com.sankuai.dzhealth.ai.service.agent.domain.buffer.MessageBuffer;
import com.sankuai.dzhealth.ai.service.agent.domain.context.ChatClientContext;
import com.sankuai.dzhealth.ai.service.agent.domain.strategy.TaskTypeStrategy;
import com.sankuai.dzhealth.ai.service.agent.domain.strategy.TaskTypeStrategyFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 首字符输出前的预处理器
 * 用于统一处理不同任务类型在首次输出前的特殊逻辑
 * 
 * <AUTHOR>
 * @time 2025/1/15
 */
@Slf4j
@Component
public class PreOutputProcessor {
    
    @Autowired
    private TaskTypeStrategyFactory strategyFactory;
    
    /**
     * 处理首字符输出前的逻辑
     * 
     * @param context 聊天上下文
     * @param buffer 消息缓冲区
     * @param hasFirst 是否为首次输出的原子布尔值
     */
    public void processPreFirstOutput(ChatClientContext context, MessageBuffer buffer, AtomicBoolean hasFirst) {
        if (!hasFirst.get()) {
            return;
        }
        
        try {
            TaskTypeStrategy strategy = strategyFactory.getStrategy(context.getTaskType());
            strategy.preFirstOutput(context, buffer, hasFirst.get());
            
            // 设置为已处理首次输出
            hasFirst.set(false);
        } catch (Exception e) {
            log.error("Error in PreOutputProcessor for taskType: {}", context.getTaskType(), e);
            // 即使出错也要设置为已处理，避免重复处理
            hasFirst.set(false);
        }
    }
}
