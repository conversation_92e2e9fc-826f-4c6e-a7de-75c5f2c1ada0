package com.sankuai.dzhealth.ai.service.agent.domain.repository.chatSession;

import co.elastic.clients.elasticsearch.indices.ForcemergeRequest;
import com.dianping.lion.client.util.CollectionUtils;
import com.sankuai.dzhealth.ai.service.agent.domain.entity.ChatSessionMessageEntity;
import com.sankuai.dzhealth.ai.service.agent.infrastructure.dal.entity.ChatSessionMessageDOWithBLOBs;
import com.sankuai.dzhealth.ai.service.agent.infrastructure.dal.example.ChatSessionMessageDOExample;
import com.sankuai.dzhealth.ai.service.agent.infrastructure.dal.mapper.ChatSessionMessageDOMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.ai.chat.messages.MessageType;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 主要负责 chat_session_message 表的 CRUD 操作，供领域层调用。
 */
@Slf4j
@Repository
@RequiredArgsConstructor
public class ChatSessionMessageRepository {
    @Resource
    private ChatSessionMessageDOMapper chatSessionMessageDOMapper;

    // 审核状态
    public static final int STATUS_APPROVED = 0;       // 审核通过
    public static final int STATUS_REJECTED = 1;       // 审核不通过
    public static final int STATUS_DELETED = 2;        // 删除
    public static final int STATUS_PENDING = 3;        // 审核中

    // 消息点赞取消状态
    public static final int LIKE_STATUS_NONE = 0;  // 无操作
    public static final int LIKE_STATUS_LIKE = 1;  // 点赞
    public static final int LIKE_STATUS_DISLIKE = 2;  // 点踩

    private static final Integer STATUS_NORMAL = 0;

    private List<ChatSessionMessageDOWithBLOBs> selectWithBLOBs(ChatSessionMessageDOExample example) {
        return chatSessionMessageDOMapper.selectByExampleWithBLOBs(example)
                .stream()
                .filter(obj -> obj instanceof ChatSessionMessageDOWithBLOBs)
                .map(obj -> (ChatSessionMessageDOWithBLOBs) obj)
                .collect(Collectors.toList());
    }

    /**
     * 类型转换：ChatSessionMessageEntity -> ChatSessionMessageDOWithBLOBs
     */
    private ChatSessionMessageDOWithBLOBs convertToDO(ChatSessionMessageEntity messageEntity) {
        if (messageEntity == null) {
            return null;
        }
        return ChatSessionMessageDOWithBLOBs.builder()
                .id(messageEntity.getId())
                .messageId(messageEntity.getMessageId())
                .sessionId(messageEntity.getSessionId())
                .userId(messageEntity.getUserId())
                .role(messageEntity.getRole())
                .content(messageEntity.getContent())
                .feedback(messageEntity.getFeedback())
                .status(messageEntity.getStatus())
                .extra(messageEntity.getExtra())
                .memorySnapshot(messageEntity.getMemorySnapshot())
                .createTime(messageEntity.getCreateTime())
                .updateTime(Optional.ofNullable(messageEntity.getUpdateTime()).orElse(new Date()))
                .build();
    }

    private ChatSessionMessageEntity convertToEntity(ChatSessionMessageDOWithBLOBs messageDOWithBLOBs) {
        if (messageDOWithBLOBs == null) {
            return null;
        }
        return ChatSessionMessageEntity.builder()
                .id(messageDOWithBLOBs.getId())
                .messageId(messageDOWithBLOBs.getMessageId())
                .sessionId(messageDOWithBLOBs.getSessionId())
                .userId(messageDOWithBLOBs.getUserId())
                .role(messageDOWithBLOBs.getRole())
                .content(messageDOWithBLOBs.getContent())
                .feedback(messageDOWithBLOBs.getFeedback())
                .status(messageDOWithBLOBs.getStatus())
                .createTime(messageDOWithBLOBs.getCreateTime())
                .updateTime(messageDOWithBLOBs.getUpdateTime())
                .build();
    }

    public ChatSessionMessageEntity buildMessageEntity(String messageId, String sessionId,
                                                       Long userId, int platform, String role,
                                                       String content, String extra) {

        return ChatSessionMessageEntity.builder()
                .messageId(messageId)
                .sessionId(sessionId)
                .userId(userId)
                .role(role)
                .content(Optional.ofNullable(content).orElse(""))
                .feedback(0)
                .status(STATUS_NORMAL)
                .platform(platform)
                .extra(extra)
                .createTime(new Date())
                .updateTime(new Date())
                .build();
    }

    public void insertMessage(String messageId, String sessionId,
                              Long userId, int platform, String role,
                              String content, String extra) {
        ChatSessionMessageEntity chatSessionMessageEntity = buildMessageEntity(messageId, sessionId, userId, platform, role, content, extra);
        insert(chatSessionMessageEntity);
    }

    // 更新记忆
    public void updateMemory(String messageId, Long userId, Integer platform, String memorySnapshot) {

        updateSessionMessage(ChatSessionMessageEntity.builder()
                .messageId(messageId)
                .userId(userId)
                .platform(platform)
                .updateTime(new Date())
                .memorySnapshot(memorySnapshot)
                .build());

    }

    // 更新extra
    public void updateExtra(String messageId, Long userId, Integer platform, String extra) {

        updateSessionMessage(ChatSessionMessageEntity.builder()
                .messageId(messageId)
                .userId(userId)
                .platform(platform)
                .updateTime(new Date())
                .extra(extra)
                .build());

    }

    /**
     * 插入消息
     */
    public void insert(ChatSessionMessageEntity messageEntity) {
        // 校验必填字段
        if (messageEntity == null) {
            throw new IllegalArgumentException("messageEntity不能为空");
        }
        if (StringUtils.isBlank(messageEntity.getMessageId())) {
            throw new IllegalArgumentException("messageId不能为空");
        }
        if (StringUtils.isBlank(messageEntity.getSessionId())) {
            throw new IllegalArgumentException("sessionId不能为空");
        }
        if (messageEntity.getUserId() == null) {
            throw new IllegalArgumentException("userId不能为空");
        }
        if (StringUtils.isBlank(messageEntity.getRole())) {
            throw new IllegalArgumentException("role不能为空");
        }

        ChatSessionMessageDOWithBLOBs messageDO = convertToDO(messageEntity);
        chatSessionMessageDOMapper.insertSelective(messageDO);
    }

    /**
     * 批量插入消息
     */
    public void batchInsert(List<ChatSessionMessageEntity> messageEntities) {
        if (messageEntities == null || messageEntities.isEmpty()) {
            return;
        }

        for (ChatSessionMessageEntity messageEntity : messageEntities) {
            insert(messageEntity);
        }
    }

    /**
     * 根据会话ID和用户ID删除消息（软删除）
     */
    public int deleteBySessionId(String sessionId, Long userId) {
        ChatSessionMessageDOExample example = new ChatSessionMessageDOExample();
        example.createCriteria().andSessionIdEqualTo(sessionId)
                .andUserIdEqualTo(userId);

        ChatSessionMessageDOWithBLOBs updateMessage = ChatSessionMessageDOWithBLOBs.builder()
                .sessionId(sessionId)
                .userId(userId)
                .status(STATUS_DELETED)
                .build();

        return chatSessionMessageDOMapper.updateByExampleSelective(updateMessage, example);
    }

    /**
     * 根据消息ID和用户ID更新消息
     */
    public int updateSessionMessage(ChatSessionMessageEntity updateMessageEntity) {
        if (updateMessageEntity == null) {
            throw new IllegalArgumentException("updateMessageEntity不能为空");
        }
        String messageId = updateMessageEntity.getMessageId();
        Long userId = updateMessageEntity.getUserId();

        if (StringUtils.isBlank(messageId) || userId == null) {
            throw new IllegalArgumentException("messageId和userId不能为空");
        }

        ChatSessionMessageDOWithBLOBs updateMessage = convertToDO(updateMessageEntity);

        ChatSessionMessageDOExample example = new ChatSessionMessageDOExample();
        example.createCriteria().andMessageIdEqualTo(messageId)
                .andUserIdEqualTo(userId);
        return chatSessionMessageDOMapper.updateByExampleSelective(updateMessage, example);
    }

    /**
     * 根据消息ID和用户ID更新反馈状态
     */
    public int updateFeedbackByMessageId(String messageId, Long userId, Integer feedback, int platform) {
        return updateSessionMessage(
                ChatSessionMessageEntity.builder()
                        .messageId(messageId)
                        .userId(userId)
                        .platform(platform)
                        .feedback(feedback)
                        .updateTime(new Date())
                        .build());
    }

    /**
     * 根据会话ID、用户ID和状态查询消息列表（分页查询）
     */
    public List<ChatSessionMessageEntity> findBySessionIdAndStatus(String sessionId, Integer limit, Integer offset) {
        ChatSessionMessageDOExample example = new ChatSessionMessageDOExample();
        example.createCriteria()
                .andSessionIdEqualTo(sessionId)
                .andStatusEqualTo(STATUS_APPROVED);

        example.setOrderByClause("create_time DESC LIMIT " + limit + " OFFSET " + offset);
        return selectWithBLOBs(example).stream().map(this::convertToEntity).collect(Collectors.toList());
    }

    /**
     * 查询待审核的消息列表
     */
    public List<ChatSessionMessageDOWithBLOBs> findPendingReviewMessages() {
        ChatSessionMessageDOExample example = new ChatSessionMessageDOExample();
        example.createCriteria().andStatusEqualTo(STATUS_PENDING); // 审核中
        example.setOrderByClause("create_time ASC");
        return selectWithBLOBs(example);
    }

    /**
     * 根据消息ID和用户ID查询消息
     */
    public ChatSessionMessageEntity findByMessageIdAndUserId(String messageId, Long userId) {
        if (StringUtils.isBlank(messageId) || userId == null) {
            return null;
        }

        ChatSessionMessageDOExample example = new ChatSessionMessageDOExample();
        example.createCriteria()
                .andMessageIdEqualTo(messageId)
                .andUserIdEqualTo(userId);

        List<ChatSessionMessageDOWithBLOBs> results = selectWithBLOBs(example);
        if (results.isEmpty()) {
            return null;
        }

        return convertToEntity(results.get(0));
    }

    /**
     * 根据会话ID、用户ID和状态查询消息列表（不分页）
     */
    public List<ChatSessionMessageEntity> findBySessionIdAndStatus(String sessionId) {
        ChatSessionMessageDOExample example = new ChatSessionMessageDOExample();
        example.createCriteria()
                .andSessionIdEqualTo(sessionId)
                .andStatusEqualTo(STATUS_APPROVED);

        example.setOrderByClause("create_time asc");
        return selectWithBLOBs(example).stream().map(this::convertToEntity).collect(Collectors.toList());
    }

    /**
     * 根据会话ID查询所有消息（包含BLOB字段）
     */
    public List<ChatSessionMessageDOWithBLOBs> findAllMessagesBySessionId(String sessionId) {
        if (StringUtils.isBlank(sessionId)) {
            return Collections.emptyList();
        }

        ChatSessionMessageDOExample example = new ChatSessionMessageDOExample();
        example.createCriteria()
                .andSessionIdEqualTo(sessionId)
                .andStatusEqualTo(STATUS_APPROVED)
                .andRoleEqualTo(MessageType.USER.name());

        example.setOrderByClause("create_time ASC");
        return selectWithBLOBs(example);
    }

    public List<ChatSessionMessageDOWithBLOBs> findMessagesBySessionIds(List<String> sessionIds, long useID) {
        if (CollectionUtils.isEmpty(sessionIds)) {
            return null;
        }

        ChatSessionMessageDOExample example = new ChatSessionMessageDOExample();
        example.createCriteria()
                .andSessionIdIn(sessionIds)
                .andUserIdEqualTo(useID)
                .andStatusEqualTo(STATUS_APPROVED);
        return selectWithBLOBs(example);
    }


    public int batchInsertChatSessionMessageDOWithBLOBs(List<ChatSessionMessageDOWithBLOBs> messageDOWithBLOBs) {
        if (CollectionUtils.isEmpty(messageDOWithBLOBs)) {
            return 0;
        }
        int count = 0;
        for (ChatSessionMessageDOWithBLOBs messageDO : messageDOWithBLOBs) {
            count += chatSessionMessageDOMapper.insertSelective(messageDO);
        }
        return count;
    }
}
