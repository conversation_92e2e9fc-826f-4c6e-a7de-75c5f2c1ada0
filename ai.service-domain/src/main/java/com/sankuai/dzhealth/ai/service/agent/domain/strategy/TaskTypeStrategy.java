package com.sankuai.dzhealth.ai.service.agent.domain.strategy;

import com.sankuai.dzhealth.ai.service.agent.domain.buffer.MessageBuffer;
import com.sankuai.dzhealth.ai.service.agent.domain.context.ChatClientContext;

/**
 * TaskType 策略接口
 * 用于处理不同任务类型的差异化逻辑
 * 
 * <AUTHOR>
 * @time 2025/1/15
 */
public interface TaskTypeStrategy {
    
    /**
     * 获取策略支持的任务类型
     * 
     * @return 任务类型
     */
    String getTaskType();
    
    /**
     * 首字符输出前的预处理逻辑
     * 
     * @param context 聊天上下文
     * @param buffer 消息缓冲区
     * @param hasFirst 是否为首次输出标记
     */
    void preFirstOutput(ChatClientContext context, MessageBuffer buffer, boolean hasFirst);
    
    /**
     * 文本装饰处理
     * 
     * @param text 原始文本
     * @param context 聊天上下文
     * @return 处理后的文本，null 表示暂时缓存不输出
     */
    String processText(String text, ChatClientContext context);
    
    /**
     * 完成时的后处理逻辑
     * 
     * @param context 聊天上下文
     * @return 剩余需要输出的文本，null 表示无剩余内容
     */
    String onComplete(ChatClientContext context);
}
