package com.sankuai.dzhealth.ai.service.agent.domain.process.task.impl;

import com.fasterxml.jackson.databind.JsonNode;
import com.sankuai.dzhealth.ai.service.agent.domain.context.MessageContext;
import com.sankuai.dzhealth.ai.service.agent.domain.context.TaskContext;
import com.sankuai.dzhealth.ai.service.agent.domain.entity.ChatSessionEntity;
import com.sankuai.dzhealth.ai.service.agent.domain.enums.TaskTypeEnum;
import com.sankuai.dzhealth.ai.service.agent.domain.model.TaskProcessResult;
import com.sankuai.dzhealth.ai.service.agent.domain.process.task.GeneralTask;
import com.sankuai.dzhealth.ai.service.agent.domain.process.task.Task;
import com.sankuai.dzhealth.ai.service.agent.domain.repository.chatSession.ChatSessionRepository;
import com.sankuai.dzhealth.ai.service.domain.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * @author:chenwei
 * @time: 2025/7/21 14:33
 * @version: 0.0.1
 */

@Service
@Slf4j
public class UpdateSessionDigestTask extends GeneralTask implements Task {


    @Autowired
    private ChatSessionRepository chatSessionRepository;

    @Override
    public boolean accept(String type) {
        return TaskTypeEnum.UPDATE_SESSION_DIGEST_TASK.getType().equals(type);
    }

    @Override
    public TaskProcessResult process(TaskContext context) {
        MessageContext messageContext = context.getMessageContext();
        if (StringUtils.isBlank(context.getTaskConfig().getUserPrompt())) {
            context.getTaskConfig().setUserPrompt(messageContext.getMsg());
        }
        String answer = StringUtils.EMPTY;
        try {
            ChatSessionEntity existSession = chatSessionRepository.findBySessionId(messageContext.getSessionId(), messageContext.getUserId(), messageContext.getPlatform(), messageContext.getBizType());

            String systemPrompt = context.getTaskConfig().getSystemPrompt();
            String replaceSystemPrompt = systemPrompt.replace("{{{historyDigest}}}", Optional.ofNullable(existSession).map(ChatSessionEntity::getDigest).orElse(""));
            context.getTaskConfig().setSystemPrompt(replaceSystemPrompt);

            answer = getJsonAnswer(context);
            if (StringUtils.isNotBlank(answer)) {
                JsonNode jsonNode = JsonUtils.parseJsonNode(answer);
                String digest = StringUtils.EMPTY;
                if (jsonNode.get("digest") != null) {
                    digest = jsonNode.get("digest").asText();
                }

                // 更新内存上下文摘要
                messageContext.setDigest(digest);
                // 更新会话
                chatSessionRepository.updateSession(messageContext.getSessionId(), null, digest, null, 0);
            }
        } catch (Exception e) {
            log.error("msg={}", messageContext.getMsg(), e);
        }

        return TaskProcessResult.builder()
                .taskContext(context)
                .answer(answer)
                .build();
    }

    @Override
    public void after(TaskProcessResult result) {

    }
}
