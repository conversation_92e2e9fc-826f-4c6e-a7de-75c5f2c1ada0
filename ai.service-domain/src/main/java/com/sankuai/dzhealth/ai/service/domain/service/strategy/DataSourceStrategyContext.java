package com.sankuai.dzhealth.ai.service.domain.service.strategy;

import com.sankuai.dzhealth.ai.service.domain.chat.model.IntentionResult;
import com.sankuai.dzhealth.ai.service.domain.utils.context.AiAnswerContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 策略上下文类，管理所有策略
 */
@Component
@Slf4j
public class DataSourceStrategyContext {
    private final List<DataSourceStrategy<?>> strategies = new ArrayList<>();

    /**
     * 构造函数，注入所有策略实现
     */
    @Autowired
    public DataSourceStrategyContext(List<DataSourceStrategy<?>> strategies) {
        this.strategies.addAll(strategies);
    }

    /**
     * 执行所有应该执行的策略，统一走 execute → filter 流程
     * @param intentionResult 意图识别结果
     * @param context 上下文
     * @param rewriteText 改写后的文本
     * @return 策略执行结果（已根据各策略内部配置完成过滤）
     */
    public Map<String, BaseStrategyInfo> executeStrategies(IntentionResult intentionResult,
                                                           AiAnswerContext context,
                                                           String rewriteText) {
        // 线程安全的结果容器，避免并发写入问题
        Map<String, BaseStrategyInfo> results = new ConcurrentHashMap<>();
        List<CompletableFuture<Void>> futures = new ArrayList<>();

        // 异步执行所有符合条件的策略
        for (DataSourceStrategy<?> strategy : strategies) {
            if (!strategy.shouldExecute(intentionResult)) {
                continue;
            }

            String key = strategy.getClass().getSimpleName();

            // 使用模板方法，所有策略都统一走 execute → filter 流程
            @SuppressWarnings("unchecked")
            DataSourceStrategy<BaseStrategyInfo> typedStrategy =
                    (DataSourceStrategy<BaseStrategyInfo>) strategy;

            CompletableFuture<Void> future = typedStrategy
                    .executeWithFiltering(context, rewriteText)
                    .handle((result, throwable) -> {
                        if (throwable != null) {
                            log.error("策略 {} 执行异常", key, throwable);
                        } else if (result != null) {
                            results.put(key, result);
                            log.info("策略 {} 执行完成", key);
                        }
                        return null;
                    });

            futures.add(future);
            log.info("启动策略执行流水线: {}", key);
        }

        if (futures.isEmpty()) {
            log.info("没有需要执行的策略");
            return new HashMap<>();
        }

        // 等待所有策略执行完成
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();

        log.info("所有策略执行完成，总计: {}, 成功: {}", futures.size(), results.size());
        return new HashMap<>(results);
    }


} 