package com.sankuai.dzhealth.ai.service.domain.chat.client;

import com.dianping.lion.Environment;
import com.meituan.service.inf.kms.client.Kms;
import com.meituan.service.inf.kms.utils.KmsResultNullException;
import com.sankuai.dzhealth.ai.service.infrastructure.chatmodel.OneApiChatModel;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.openai.OpenAiChatOptions;
import org.springframework.ai.openai.api.OpenAiApi;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestClient;

import java.time.Duration;

/**
 * @author: yangweicheng
 * @date: 2025/4/17 14:47
 * @version: 1.0
 */
@Component
public class EvaluationChatConfig {

    public static final String MODEL = "deepseek-r1-huawei";

    @Bean
    public ChatClient evaluationChatClient() throws KmsResultNullException {
        // 配置RestClient.Builder以设置超时时间
        RestClient.Builder restClientBuilder = RestClient.builder()
                .requestFactory(new org.springframework.http.client.SimpleClientHttpRequestFactory() {{
                    setConnectTimeout((int) Duration.ofMinutes(5).toMillis());
                    setReadTimeout((int) Duration.ofMinutes(5).toMillis());
                }});
        
        OpenAiApi openAiApi = new OpenAiApi.Builder()
                .completionsPath("/chat/completions")
                .baseUrl("https://aigc.sankuai.com/v1/openai/native")
                .apiKey(Kms.getByName(Environment.getAppName(), "friday.agent.appId"))
                .restClientBuilder(restClientBuilder)
                .build();
        OpenAiChatOptions openAiChatOptions = OpenAiChatOptions.builder()
                .model(MODEL)
                .temperature(0.0)
                .reasoningEffort("high")
                .maxTokens(10000)
                .build();
        OneApiChatModel chatModel = OneApiChatModel.builder()
                .openAiApi(openAiApi)
                .defaultOptions(openAiChatOptions)
                .build();

        return ChatClient.builder(chatModel).build();
    }

    @Bean
    public ChatClient retrieveStrategyResEvaChatClient() throws KmsResultNullException {
        OpenAiApi openAiApi = new OpenAiApi.Builder()
                .completionsPath("/chat/completions")
                .baseUrl("https://aigc.sankuai.com/v1/openai/native")
                .apiKey(Kms.getByName(Environment.getAppName(), "friday.search.appId"))
                .build();
        OpenAiChatOptions openAiChatOptions = OpenAiChatOptions.builder()
                .model("LongCat-Lite-8K-Chat")
                .temperature(0.0)
                .maxTokens(2000)
                .build();
        OneApiChatModel chatModel = OneApiChatModel.builder()
                .openAiApi(openAiApi)
                .defaultOptions(openAiChatOptions)
                .build();

        return ChatClient.builder(chatModel).build();
    }
}
