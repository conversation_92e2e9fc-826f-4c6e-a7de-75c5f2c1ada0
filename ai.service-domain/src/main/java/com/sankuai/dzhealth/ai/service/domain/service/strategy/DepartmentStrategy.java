package com.sankuai.dzhealth.ai.service.domain.service.strategy;

import com.alibaba.fastjson.JSON;
import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.dianping.rhino.threadpool.ThreadPool;
import com.google.common.collect.ImmutableMap;
import com.sankuai.dzhealth.ai.service.domain.chat.model.IntentionResult;
import com.sankuai.dzhealth.ai.service.domain.service.ChatSessionService;
import com.sankuai.dzhealth.ai.service.domain.utils.context.AiAnswerContext;
import com.sankuai.dzhealth.ai.service.infrastructure.observability.Span;
import com.sankuai.dzhealth.medical.client.biz.department.dto.DepartmentDTO;
import com.sankuai.dzhealth.medical.client.biz.department.dto.QueryAllDepartmentRequest;
import com.sankuai.dzhealth.medical.client.biz.department.service.DepartmentService;
import com.sankuai.dzhealth.medical.client.biz.department.standard.StandardDepartment;
import com.sankuai.dzhealth.medical.client.common.dto.Response;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * 科室信息策略实现
 */
@Component
@Slf4j
public class DepartmentStrategy implements DataSourceStrategy<DepartmentStrategy.DepartmentStrategyInfo> {

    @Autowired
    private DepartmentService departmentService;

    private static final ThreadPool taskPool = ChatSessionService.TASK_POOL;

    @Override
    public boolean shouldExecute(IntentionResult intentionResult) {
        return intentionResult.getDepartmentNeed() != null && intentionResult.getDepartmentNeed();
    }

    @Override
    public CompletableFuture<DepartmentStrategy.DepartmentStrategyInfo> execute(AiAnswerContext context, String rewriteText) {
        return CompletableFuture.supplyAsync(() ->
                queryAllDepartmentInfo(context, context.getShopId(), context.getPlatform()), taskPool.getExecutor());
    }

    /**
     * 查询所有科室信息
     */
    private DepartmentStrategyInfo queryAllDepartmentInfo(AiAnswerContext context, Long shopId, int platform) {
        Transaction transaction = Cat.newTransaction(getClass().getSimpleName(), "queryAllDepartment");
        transaction.setSuccessStatus();
        long startTime = System.currentTimeMillis();
        try {
            QueryAllDepartmentRequest request = new QueryAllDepartmentRequest();
            request.setShopId(shopId);
            request.setPlatform(platform);
            Response<List<DepartmentDTO>> response = departmentService.queryAllDepartment(request);
            DepartmentStrategyInfo res = new DepartmentStrategyInfo();
            if (response.respSuccess() && response.getData() != null && CollectionUtils.isNotEmpty(response.getData())) {
                List<DepartmentStrategyInfo.BaseDepartmentStrategyInfo> collect = response.getData().stream().map(e -> {
                    DepartmentStrategyInfo.BaseDepartmentStrategyInfo departmentInfo = new DepartmentStrategyInfo.BaseDepartmentStrategyInfo();
                    departmentInfo.setTitle(Optional.ofNullable(e.getTitle()).orElse(""));
                    departmentInfo.setBizId(Optional.ofNullable(e.getDepartmentId()).orElse(0L));
                    departmentInfo.setStdId(Optional.ofNullable(e.getStandardDepartment()).map(StandardDepartment::getStandardDepartmentId).orElse(0L));
                    return departmentInfo;
                }).collect(Collectors.toList());
                res.setDepartInfo(collect);
            }
            res.setSpan(Collections.singletonList(Span.builder()
                    .key(getClass().getSimpleName())
                    .value(JSON.toJSONString(ImmutableMap.of("request", request, "result", Optional.ofNullable(res.getDepartInfo()).orElse(Collections.emptyList()))))
                    .duration(System.currentTimeMillis() - startTime)
                    .build()));
            return res;
        } catch (Exception e) {
            log.error("queryAllDepartmentInfo error", e);
            transaction.setStatus(e);
            return null;
        } finally {
            transaction.complete();
        }
    }

    /**
     * 整理科室信息
     */
    private String collectDepartmentInfo(List<DepartmentDTO> departmentDTOS) {
        if (departmentDTOS == null || departmentDTOS.isEmpty()) {
            return "[]";
        }

        StringBuilder jsonBuilder = new StringBuilder();
        jsonBuilder.append("[");

        for (int i = 0; i < departmentDTOS.size(); i++) {
            DepartmentDTO dept = departmentDTOS.get(i);
            jsonBuilder.append(dept.getTitle() != null ? dept.getTitle() : "");
            if (i < departmentDTOS.size() - 1) {
                jsonBuilder.append(",");
            }
        }

        jsonBuilder.append("]");
        return jsonBuilder.toString();
    }

    @Data
    @EqualsAndHashCode(callSuper = true)
    public static class DepartmentStrategyInfo extends BaseStrategyInfo {

        private List<BaseDepartmentStrategyInfo> departInfo;

        @Data
        public static class BaseDepartmentStrategyInfo {

            private String title;

            private Long bizId;

            private Long stdId;
        }

        @Override
        public String toPrompt() {
            if (departInfo == null || CollectionUtils.isEmpty(departInfo)) {
                return "[]";
            }
            StringBuilder jsonBuilder = new StringBuilder();
            jsonBuilder.append("[");

            for (int i = 0; i < departInfo.size(); i++) {
                BaseDepartmentStrategyInfo dept = departInfo.get(i);
                jsonBuilder.append(dept.getTitle() != null ? dept.getTitle() : "");
                if (i < departInfo.size() - 1) {
                    jsonBuilder.append(",");
                }
            }

            jsonBuilder.append("]");
            return jsonBuilder.toString();
        }
    }
}
