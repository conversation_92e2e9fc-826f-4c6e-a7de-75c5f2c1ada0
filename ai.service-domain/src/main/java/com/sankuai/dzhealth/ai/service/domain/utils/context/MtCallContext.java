package com.sankuai.dzhealth.ai.service.domain.utils.context;


import com.alibaba.fastjson.JSON;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class MtCallContext {

    @FieldDoc(description = "任务Id")
    private Long taskId;

    @FieldDoc(description = "用户Id")
    private String userId;

    @FieldDoc(description = "会话Id")
    private String conversationId;

    @FieldDoc(description = "消息Id")
    private Long messageId;




    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }
}
