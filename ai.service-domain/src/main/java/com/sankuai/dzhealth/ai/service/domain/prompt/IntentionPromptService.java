package com.sankuai.dzhealth.ai.service.domain.prompt;

import com.dianping.haima.client.request.HaimaRequest;
import com.dianping.haima.entity.haima.HaimaConfig;
import com.sankuai.dzhealth.ai.service.infrastructure.acl.HaimaAcl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR> youtianyu
 * @version : 0.1
 * @since : 2025/3/27
 */
@Component
public class IntentionPromptService {
    @Autowired
    private HaimaAcl haimaAcl;

    public IntentionPromptConfig getProjectIntentionPrompt(String project) {
        HaimaRequest request = new HaimaRequest("dzhealth_ai_intention_prompt");
        List<HaimaConfig> haimaContents = haimaAcl.getHaimaConfigs(request);

        // 将haima数据转换为IntentionPrompt列表
        List<IntentionPromptConfig.IntentionPrompt> intentionPrompts = haimaContents.stream()
                .filter(haimaConfig -> Objects.equals(haimaConfig.getExtString("project"), project))
                .findFirst()
                .map(HaimaConfig::getContents)
                .orElse(Collections.emptyList())
                .stream()
                .map(content -> IntentionPromptConfig.IntentionPrompt.builder()
                        .key(content.getContentString("key"))
                        .name(content.getContentString("name"))
                        .classify(content.getContentString("classify"))
                        .rewrite(content.getContentString("rewrite"))
                        .prompt(content.getContentString("prompt"))
                        .functionCallJson(content.getContentString("functionCallJson"))
                        .build())
                .collect(Collectors.toList());

        // 构建并返回ProjectIntentionPrompt对象
        return IntentionPromptConfig.builder().project(project).list(intentionPrompts).build();
    }

}
