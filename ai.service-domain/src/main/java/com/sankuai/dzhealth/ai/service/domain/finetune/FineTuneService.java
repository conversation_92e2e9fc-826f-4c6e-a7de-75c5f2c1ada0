package com.sankuai.dzhealth.ai.service.domain.finetune;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.Cat;
import com.dianping.haima.entity.haima.HaimaContent;
import com.dianping.rhino.Rhino;
import com.dianping.rhino.threadpool.DefaultThreadPoolProperties;
import com.dianping.rhino.threadpool.ThreadPool;
import com.sankuai.dzhealth.ai.service.domain.prompt.IntentionPromptConfig;
import com.sankuai.dzhealth.ai.service.domain.prompt.IntentionPromptService;
import com.sankuai.dzhealth.ai.service.domain.utils.JsonUtils;
import com.sankuai.dzhealth.ai.service.domain.utils.context.AiAnswerContext;
import com.sankuai.dzhealth.ai.service.dto.SseAwareException;
import com.sankuai.dzhealth.ai.service.enums.StreamEventErrorTypeEnum;
import com.sankuai.dzhealth.ai.service.infrastructure.acl.HaimaAcl;
import com.sankuai.dzhealth.ai.service.infrastructure.observability.Span;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.messages.AbstractMessage;
import org.springframework.ai.chat.model.ChatResponse;
import org.springframework.ai.chat.model.Generation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;

import static java.util.stream.Collectors.toMap;

/**
 * @author: yangweicheng
 * @date: 2025/5/7 14:33
 * @version: 1.0
 */

@Slf4j
@Service
public class FineTuneService {

    @Autowired
    private ChatClient evaluationChatClient;

    @Autowired
    private HaimaAcl haimaAcl;

    @Autowired
    private IntentionPromptService intentionPromptService;

    @Autowired
    private FineTuneRepository fineTuneRepository;

    public static final ThreadPool FINE_TUNE_POOL = Rhino.newThreadPool("FINE_TUNE_POOL",
            DefaultThreadPoolProperties.Setter().withCoreSize(100).withMaxSize(200).withMaxQueueSize(1000));


    public void generateFineTuningData(AiAnswerContext context) {

        List<FineTuneData> fineTuneDataList = new ArrayList<>();

        CompletableFuture<List<FineTuneData>> future = CompletableFuture.supplyAsync(() -> {
            try {
                List<HaimaContent> haimaContents = haimaAcl.getContent("ai_hospital_instruction", null);
                if (CollectionUtils.isEmpty(haimaContents)) {
                    throw new SseAwareException(StreamEventErrorTypeEnum.SERVER_ERROR);
                }
                Map<String, String> promptMap = haimaContents.stream().collect(toMap(
                        e -> e.getContentString("promptKey"),
                        e -> e.getContentString("promptContent"),
                        (a, b) -> a
                ));


                IntentionPromptConfig intentionPrompt =
                        intentionPromptService.getProjectIntentionPrompt(context.getBizScene());

                String classifyTemplate = promptMap.getOrDefault("fine_tune_classify_rewrite", "");

                String system = classifyTemplate.replace("${intentionPrompt}", intentionPrompt.toIntentionPrompt());

                String classifyPrompt = context.getSpans().stream()
                        .filter(span -> "classifyPrompt".equals(span.getKey()))
                        .findFirst()
                        .map(Span::getValue)
                        .orElse("");
                int markerIndex = classifyPrompt.indexOf("<<UserPrompt>>");
                String user = markerIndex >= 0 ?
                        classifyPrompt.substring(markerIndex + "<<UserPrompt>>".length()) : classifyPrompt;

                Optional<JSONObject> response =
                        Optional.ofNullable(evaluationChatClient.prompt().system(system).user(user)
                                        .call().chatResponse())
                                .map(ChatResponse::getResult)
                                .map(Generation::getOutput)
                                .map(AbstractMessage::getText)
                                .map(JsonUtils::extractJsonContent)
                                .filter(JsonUtils::isValidJson)
                                .map(JSON::parseObject);

                if (!response.isPresent()) {
                    fineTuneDataList.add(FineTuneData.builder()
                            .sessionId(context.getSessionId())
                            .msgId(context.getMsgId())
                            .dataKey("empty")
                            .description("empty")
                            .success(false)
                            .build());
                } else {
                    JSONObject jsonObject = response.get();

                    try {
                        JSONArray metrics = jsonObject.getJSONArray("metrics");
                        for (int i = 0; i < metrics.size(); i++) {
                            JSONObject metric = metrics.getJSONObject(i);
                            boolean isValid = metric.containsKey("data_key")
                                    && metric.containsKey("description")
                                    && metric.containsKey("metadata")
                                    && metric.containsKey("feedback");

                            String dataKey = JsonUtils.getStringSafely(metric, "data_key", "unnamed_data_" + i);
                            String description = JsonUtils.getStringSafely(metric, "description", "");
                            String feedback = JsonUtils.getStringSafely(metric, "feedback", "");
                            String metaData = JsonUtils.getStringSafely(metric, "metadata", "");

                            fineTuneDataList.add(FineTuneData.builder()
                                    .dataKey(dataKey)
                                    .description(description)
                                    .success(isValid)
                                    .feedback(feedback)
                                    .metadata(metaData)
                                    .sessionId(context.getSessionId())
                                    .msgId(context.getMsgId())
                                    .build());
                        }
                    } catch (Exception e) {
                        fineTuneDataList.add(FineTuneData.builder()
                                .dataKey("error")
                                .description("Failed to parse metrics")
                                .feedback("Invalid metrics format: " + e.getMessage())
                                .success(false)
                                .sessionId(context.getSessionId())
                                .msgId(context.getMsgId())
                                .build());
                    }
                }

                return fineTuneDataList;
            } catch (Exception e) {
                log.error("Error in fine tune data generation", e);
                Cat.logError("Error in fine tune data generation", e);
                fineTuneDataList.add(FineTuneData.builder()
                        .dataKey("error")
                        .description("System error")
                        .feedback(e.getMessage())
                        .success(false)
                        .sessionId(context.getSessionId())
                        .msgId(context.getMsgId())
                        .build());
                return fineTuneDataList;
            }
        }, FINE_TUNE_POOL.getExecutor()).thenApply(result -> {
            fineTuneRepository.insertEvaluations(result);
            return result;
        });

        fineTuneRepository.insertAiAnswerContext(context);
    }
}
