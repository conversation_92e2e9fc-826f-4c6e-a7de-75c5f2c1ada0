package com.sankuai.dzhealth.ai.service.domain.card.handlers;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.util.Pair;
import com.sankuai.dzhealth.ai.service.domain.card.AbstractCardHandler;
import com.sankuai.dzhealth.ai.service.domain.utils.context.AiAnswerContext;
import com.sankuai.dzhealth.ai.service.dto.DepartmentCardDTO;
import com.sankuai.dzhealth.ai.service.dto.stream.StreamEventDTO;
import com.sankuai.dzhealth.ai.service.enums.StreamEventCardTypeEnum;
import com.sankuai.dzhealth.ai.service.infrastructure.observability.Span;
import com.sankuai.dzhealth.medical.client.biz.channelpagelist.dto.CardSummary;
import com.sankuai.dzhealth.medical.client.biz.department.dto.BatchQueryDepartmentRegistrationReq;
import com.sankuai.dzhealth.medical.client.biz.department.service.DepartmentService;
import com.sankuai.dzhealth.medical.client.common.dto.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.toList;

/**
 * 科室推荐卡片处理器 - 专门处理科室推荐信息
 */
@Slf4j
@Component
@Order(4)
public class DepartmentCardHandler extends AbstractCardHandler {

    @Autowired
    private DepartmentService departmentService;
    
    @Override
    public boolean supports(String fieldName) {
        return "departmentIds".equals(fieldName);
    }
    
    @Override
    public List<StreamEventDTO> handle(JSONObject jsonObject, 
                                    AiAnswerContext context, 
                                    SseEmitter sseEmitter, 
                                    AtomicInteger sseIndex,
                                    long startChatTime) {
        List<String> departmentIds = jsonObject.getJSONArray("departmentIds").toJavaList(String.class);
        String continueAsk = jsonObject.getString("continue_ask");
        if (StringUtils.isNotBlank(continueAsk)) {
            JSONObject continueAskJson = JSON.parseObject(continueAsk);
            String choiceTitle = continueAskJson.getString("choice_title");
            String singleSelect = continueAskJson.getString("single_select");
            if (StringUtils.isNotBlank(choiceTitle) || StringUtils.isNotBlank(singleSelect)) {
                return Collections.emptyList();
            }
        }
        List<StreamEventDTO> events = new ArrayList<>();
        context.addSpan(Span.builder()
                .key(getClass().getSimpleName() + "/departmentNames")
                .value(JSON.toJSONString(departmentIds))
                .build());
        log.info("extract_departmentIds={}", departmentIds);
        if (CollectionUtils.isNotEmpty(departmentIds)) {
            List<String> names = departmentIds.stream()
                    .map(item -> item.split("::")[0]).collect(toList());

            Map<String, String> id2Reason = departmentIds.stream()
                    .map(item -> item.split("::"))
                    .filter(parts -> parts.length >= 2)
                    .collect(Collectors.toMap(
                            parts -> parts[0], 
                            parts -> parts[1],
                            (a, b) -> a
                    ));

            List<Long> bizIds = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(names)) {
                Map<String, Pair<Long, Long>> departName2IdMap = context.getDepartName2IdMap();
                if (MapUtils.isNotEmpty(departName2IdMap)) {
                    bizIds = names.stream()
                            .filter(departName2IdMap::containsKey)
                            .map(e -> departName2IdMap.get(e).getKey())
                            .collect(toList());
                }
            }

            Map<String, Object> cardProps = new HashMap<>();
            try {
                if (CollectionUtils.isNotEmpty(bizIds)) {
                    context.addSpan(Span.builder()
                            .key(getClass().getSimpleName() + "/departmentIds")
                            .value(JSON.toJSONString(departmentIds))
                            .build());
                    BatchQueryDepartmentRegistrationReq req = new BatchQueryDepartmentRegistrationReq();
                    req.setPlatform(context.getPlatform());
                    req.setShopId(context.getShopId());
                    req.setDepartmentIds(bizIds);
                    context.addSpan(Span.builder()
                            .key(getClass().getSimpleName() + "/req")
                            .value(JSON.toJSONString(req))
                            .build());
                    Response<Map<Long, CardSummary>> departmentRegistrationInfoResponse = departmentService.batchQueryDepartmentRegistrationInfo(req);
                    log.info("departmentRegistrationInfoResponse={}, Req={}，traceId={}",JSON.toJSONString(departmentRegistrationInfoResponse), JSON.toJSONString(req), com.meituan.mtrace.Tracer.id());
                    if (departmentRegistrationInfoResponse.respSuccess() && MapUtils.isNotEmpty(departmentRegistrationInfoResponse.getData())) {
                        Map<Long, CardSummary> data = departmentRegistrationInfoResponse.getData();
                        List<DepartmentCardDTO> departRes = data.entrySet().stream()
                                .map(e -> {
                                    CardSummary value = e.getValue();
                                    DepartmentCardDTO cardDTO = new DepartmentCardDTO();
                                    cardDTO.setDepartmentId(e.getKey());
                                    cardDTO.setDepartmentName(value.getTitle());
                                    cardDTO.setRegisterStatus((value.getSummaryActionButton() != null && "立即挂号".equals(value.getSummaryActionButton().getText())) ? 1 : 0);
                                    cardDTO.setRegisterCanTime(value.getTag());
                                    if (value.getSummaryActionButton() != null) {
                                        cardDTO.setButtonText(value.getSummaryActionButton().getText());
                                        cardDTO.setDepartDetailUrl(value.getSummaryActionButton().getJumpUrl());
                                    }
                                    cardDTO.setRecommendReason(id2Reason.get(value.getTitle()));
                                    return cardDTO;
                                }).collect(toList());
                        Map<Long, Integer> orderMap = new HashMap<>();
                        for (int i = 0; i < bizIds.size(); i++) {
                            orderMap.put(bizIds.get(i), i);
                        }
                        departRes.sort(Comparator.comparingInt(a -> orderMap.get(a.getDepartmentId())));
                        //0410 最多四个科室挂号信息
                        if (departRes.size() >= 5) {
                            departRes = departRes.subList(0, 4);
                        }


                        context.addSpan(Span.builder()
                                .key(getClass().getSimpleName() + "/departRes")
                                .value(JSON.toJSONString(departRes))
                                .build());
                        if (!departRes.isEmpty()) {
                            departRes.get(0).setRecommendTag("最匹配");
                        }
                        if (CollectionUtils.isNotEmpty(departRes)) {
                            cardProps.put("list", departRes);
                            StreamEventDTO departmentEvent = sendCardEvent(
                                    StreamEventCardTypeEnum.DEPARTMENT_CARD,
                                    "departmentCard", 
                                    cardProps, 
                                    sseEmitter, 
                                    sseIndex.getAndIncrement()
                            );
                            addIfNotNull(events, departmentEvent);
                        }
                    }
                }
            } catch (Exception e) {
                log.error("queryDepartment error! bizIds={}", bizIds, e);
            }
        }
        
        return events;
    }

    @Override
    public int getOrder() {
        return 4;
    }
} 