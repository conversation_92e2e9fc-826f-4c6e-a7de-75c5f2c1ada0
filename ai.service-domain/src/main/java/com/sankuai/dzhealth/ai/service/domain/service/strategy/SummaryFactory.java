package com.sankuai.dzhealth.ai.service.domain.service.strategy;

import com.sankuai.dzhealth.ai.exception.BizException;
import com.sankuai.dzhealth.ai.service.domain.chat.model.ChatSummaryResult;
import com.sankuai.dzhealth.ai.service.domain.service.ChatSummaryService;
import com.sankuai.dzhealth.ai.service.request.ChatSummaryRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Author: zhongchangze
 * @Date: 2025/3/27 16:43
 * @Description:
 */
@Service
@Slf4j
public class SummaryFactory {

    @Autowired
    private List<ChatSummaryService> chatSummaryServiceList;

    public ChatSummaryService getChatSummaryService(Integer summarySceneType) {
        ChatSummaryService summaryService = chatSummaryServiceList.stream()
                .filter(chatSummaryService -> chatSummaryService.support(summarySceneType))
                .findFirst()
                .orElse(null);
        if (summaryService == null) {
            throw new BizException("未找到对应的总结策略");
        }
        return summaryService;
    }

    public ChatSummaryResult getSummaryResult(ChatSummaryRequest request) {
        return getChatSummaryService(request.getSceneType()).getChatSummary(request);
    }
}