package com.sankuai.dzhealth.ai.service.agent.domain.utils;

import com.dianping.haima.entity.haima.HaimaContent;
import com.sankuai.dzhealth.ai.service.agent.domain.process.task.TaskConfig;
import com.sankuai.dzhealth.ai.service.domain.utils.JsonUtils;
import com.sankuai.dzhealth.ai.service.infrastructure.acl.HaimaAcl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @author:chenwei
 * @time: 2025/7/15 11:27
 * @version: 0.0.1
 */

@Slf4j
@Service
public class HaimaAIUtils {

    @Autowired
    private HaimaAcl haimaAcl;

    public TaskConfig getAIConfig(String taskType) {
        List<HaimaContent> medicalTaskAiConfig = haimaAcl.getContent("medical_task_ai_config", null);
        TaskConfig res = medicalTaskAiConfig.stream().filter(config -> taskType.equals(config.getContentString("task"))).findFirst().map(config -> {
            TaskConfig cur = JsonUtils.parseObject(config.getContentString("config"), TaskConfig.class);
            if (cur == null) {
                cur = createDefaultTaskConfig();
            }
            if (StringUtils.isNotBlank(config.getContentString("systemPrompt"))) {
                cur.setSystemPrompt(config.getContentString("systemPrompt"));
            }
            return cur;
        }).orElse(createDefaultTaskConfig());

        return res;
    }

    private TaskConfig createDefaultTaskConfig() {
        TaskConfig taskConfig = new TaskConfig();
        taskConfig.setModel("deepseek-v3-friday");
        taskConfig.setStream(false);
        taskConfig.setSystemPrompt("");
        taskConfig.setKmsKey("friday.search.appId");
        taskConfig.setType("medicalMemoryUpdate");
        return taskConfig;
    }
}
