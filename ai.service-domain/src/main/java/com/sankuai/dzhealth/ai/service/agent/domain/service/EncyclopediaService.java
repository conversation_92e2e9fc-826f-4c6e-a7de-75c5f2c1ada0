package com.sankuai.dzhealth.ai.service.agent.domain.service;

import com.dianping.haima.entity.haima.HaimaContent;
import com.meituan.mdp.ai.friday.FridayChatModel;
import com.meituan.mdp.ai.friday.FridayChatOptions;
import com.meituan.mdp.ai.friday.api.FridayApi;
import com.meituan.service.inf.kms.client.Kms;
import com.meituan.service.inf.kms.utils.KmsResultNullException;
import com.sankuai.dzhealth.ai.service.agent.domain.entity.ChatSessionMessageEntity;
import com.sankuai.dzhealth.ai.service.agent.domain.memory.ChatHistoryMemory;
import com.sankuai.dzhealth.ai.service.agent.domain.memory.ChatHistoryMemoryAdvisor;
import com.sankuai.dzhealth.ai.service.agent.domain.repository.chatSession.ChatSessionMessageRepository;
import com.sankuai.dzhealth.ai.service.agent.domain.utils.SseUtils;
import com.sankuai.dzhealth.ai.service.api.RemoteResponse;
import com.sankuai.dzhealth.ai.service.domain.service.strategy.ShopRagEsStrategy;
import com.sankuai.dzhealth.ai.service.dto.DocumentDTO;
import com.sankuai.dzhealth.ai.service.enums.StreamEventDataTypeEnum;
import com.sankuai.dzhealth.ai.service.enums.StreamEventTypeEnum;
import com.sankuai.dzhealth.ai.service.infrastructure.acl.HaimaAcl;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.memory.ChatMemory;
import org.springframework.ai.chat.messages.MessageType;
import org.springframework.ai.chat.model.ChatResponse;
import org.springframework.ai.chat.model.Generation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.IOException;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> xiafangyuan
 * @since : 2025/7/15 20:07
 */
@Component
@Slf4j
public class EncyclopediaService {


    @Autowired
    private HaimaAcl haimaAcl;
    @Autowired
    private ShopRagEsStrategy shopRagEsStrategy;
    @Autowired
    private ChatHistoryMemory chatHistoryMemory;

    @Autowired
    private ChatSessionMessageRepository chatSessionMessageRepository;


    @Getter
    private volatile Set<String> encyclopediaWords = new HashSet<>();
    private volatile String systemPrompt;
    private volatile String userPrompt;

    /**
     * 查询百科
     *
     * @param sseEmitter SseEmitter 对象，用于发送 SSE 响应
     * @param sessionId  会话 ID，用于标识当前会话
     * @param query      用户查询的内容
     * @throws KmsResultNullException 如果 KMS 查询结果为空
     * @throws IOException            如果发送 SSE 内容时发生 IO 异常
     */
    public void queryEncyclopedia(SseEmitter sseEmitter, String sessionId, String query)
            throws KmsResultNullException, IOException {
        log.info("queryEncyclopedia_start,sessionId={},before_query={}", sessionId, query);
        List<ChatSessionMessageEntity> historyMsgList = chatSessionMessageRepository.findBySessionIdAndStatus(sessionId, 5, 0);
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(historyMsgList)) {
            ChatSessionMessageEntity messageEntity = historyMsgList.stream().filter(msg -> MessageType.USER.getValue().equals(msg.getRole())).findFirst().orElse(null);
            if (messageEntity != null) {
                query = messageEntity.getContent() + "+" + query;
            }
        }
        log.info("queryEncyclopedia_start,sessionId={},after_query={}", sessionId, query);
        FridayApi fridayApi = FridayApi.builder().completionsPath("/chat/completions")
                .baseUrl("https://aigc.sankuai.com/v1/openai/native")
                .apiKey(Kms.getByName("com.sankuai.dzhealth.ai.service", "friday.agent.appId")).build();

        FridayChatOptions fridayChatOptions = FridayChatOptions.builder().model("deepseek-v3-friday")
                .temperature(0.1).maxTokens(20000).build();

        FridayChatModel fridayChatModel = FridayChatModel.builder().fridayApi(fridayApi)
                .defaultOptions(fridayChatOptions).build();

        ChatClient fridayClient = ChatClient.builder(fridayChatModel)
                .defaultAdvisors(ChatHistoryMemoryAdvisor.builder(chatHistoryMemory).build()).build();


        // 查询rag
        RemoteResponse<List<DocumentDTO>> listRemoteResponse = shopRagEsStrategy.similaritySearch(query, 10, null,
                Arrays.asList("百科", "避雷针", "wordSelectionEncyclopedia"));
        String rag;
        if (listRemoteResponse.getSuccess() && !CollectionUtils.isEmpty(listRemoteResponse.getData())) {
            rag = listRemoteResponse.getData().stream().map(DocumentDTO::getText).reduce("",
                    (s1, s2) -> s1 + "\n\n" + s2);
        } else {
            rag = "";
        }
        log.info("queryEncyclopedia_rag={},query={},sessionId={}", rag, query, sessionId);

        // 流式调用 AI 模型
        String finalQuery = query;
        ChatClient.ChatClientRequestSpec requestSpec = fridayClient.prompt()
                // 指定当前上下文会话id，用于查询会话消息
                .advisors(advisorSpec -> advisorSpec.param(ChatMemory.CONVERSATION_ID, sessionId))
                .system(s -> s.text(systemPrompt).params(Map.of("rag", rag)))
                .user(u -> u.text(userPrompt).params(Map.of("query", finalQuery)));
        Iterable<ChatResponse> stream = requestSpec.stream().chatResponse().toIterable();

        StringBuilder stringBuilder = new StringBuilder(StringUtils.EMPTY);
        // 逐块发送响应
        for (ChatResponse response : stream) {
            response.getResults().stream().map(Generation::getOutput).forEach(s -> {
                try {
                    if (s.getText() != null) {
                        stringBuilder.append(s.getText());
                        SseUtils.sendSseContent(sseEmitter, StreamEventTypeEnum.MESSAGE.getType(),
                                StreamEventDataTypeEnum.MAIN_TEXT.getType(), s.getText());
                    }
                } catch (IOException e) {
                    throw new RuntimeException(e);
                }
            });

        }
        log.info("queryEncyclopedia_final_content={},sessionId={}", stringBuilder.toString(), sessionId);
    }

    /**
     * 标记大模型回答中的百科词汇
     *
     * 使用 {@code <u></u>}，相同的百科词汇仅标记第一个
     * @param answer 大模型回答结果
     * @return 返回标记结果
     */
    public String markEncyclopedia(String answer) {
        if (CollectionUtils.isEmpty(encyclopediaWords)) {
            return answer;
        }
        for (String word : encyclopediaWords) {
            answer = answer.replaceFirst(word, "[" + word + "]()");
        }
        return answer;
    }
    

    /**
     * 刷新本地百科词库，定时任务每五分钟执行一次，并且启动立即执行
     */
    @Scheduled(fixedRate = 5, timeUnit = TimeUnit.MINUTES)
    public void refreshEncyclopedia() {
        synchronized (EncyclopediaService.class){
            log.info("refreshEncyclopedia start");
            Set<String> newEncyclopediaWords = new HashSet<>();
            List<HaimaContent> aiHealthEncyclopediaWords = haimaAcl.getContent("ai_health_encyclopedia_words", null);
            for (HaimaContent aiHealthEncyclopediaWord : aiHealthEncyclopediaWords) {
                String words = aiHealthEncyclopediaWord.getContentString("words");
                newEncyclopediaWords.addAll(Arrays.stream(words.split("[,，]")).map(String::trim).toList());
                this.systemPrompt = aiHealthEncyclopediaWord.getContentString("systemPrompt");
                this.userPrompt = aiHealthEncyclopediaWord.getContentString("userPrompt");
            }
            this.encyclopediaWords = newEncyclopediaWords;// 原子性替换
            log.info("refreshEncyclopedia end");
        }

    }

}
