package com.sankuai.dzhealth.ai.service.domain.model.thinking;

import lombok.Builder;
import lombok.Data;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 思考会话模型
 */
@Data
@Builder
public class ThinkingSession {
    /**
     * 会话ID
     */
    private Long id;
    
    /**
     * 查询问题
     */
    private String query;
    
    /**
     * 会话状态
     */
    private String status;
    
    /**
     * 搜索结果
     */
    private Map<String, Object> searchResults;
    
    /**
     * 搜索配置
     */
    private Map<String, Object> searchConfig;
    
    /**
     * 计划的总步骤数
     */
    private Long totalPlannedSteps;
    
    /**
     * 实际步骤数
     */
    private Long actualSteps;
    
    /**
     * 开始时间
     */
    private Date startTime;
    
    /**
     * 结束时间
     */
    private Date endTime;
    
    /**
     * 思考步骤列表
     */
    private List<SequentialThought> thoughts;
} 