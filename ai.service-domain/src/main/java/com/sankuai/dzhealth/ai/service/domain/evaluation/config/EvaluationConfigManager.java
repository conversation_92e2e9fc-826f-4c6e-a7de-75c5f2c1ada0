package com.sankuai.dzhealth.ai.service.domain.evaluation.config;

import com.dianping.haima.entity.haima.HaimaContent;
import com.sankuai.dzhealth.ai.service.infrastructure.acl.HaimaAcl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: yangweicheng
 * @date: 2025/4/29 11:28
 * @version: 1.0
 */
@Component
@Slf4j
public class EvaluationConfigManager {

    @Autowired
    private HaimaAcl haimaAcl;

    /**
     * 获取所有评价配置
     *
     * @return 评价配置列表
     */
    public List<EvaluationConfig> getAllEvaluationConfigs() {
        List<HaimaContent> haimaContents = haimaAcl.getContent("dzhealth_ai_evaluation", null);
        if (CollectionUtils.isEmpty(haimaContents)) {
            log.error("未找到dzhealth_ai_evaluation配置");
            return Collections.emptyList();
        }

        return haimaContents.stream()
                .map(content -> new EvaluationConfig(
                        content.getContentString("evaluation"),
                        content.getContentString("system_prompt"),
                        content.getContentString("user_prompt")))
                .collect(Collectors.toList());
    }


    public List<EvaluationConfig> getAllEvaluationConfigs(String sceneKey) {
        List<HaimaContent> haimaContents = haimaAcl.getContent(sceneKey ,null);
        if (CollectionUtils.isEmpty(haimaContents)) {
            log.error("未找到{}配置", sceneKey);
            return Collections.emptyList();
        }

        return haimaContents.stream()
                .map(content -> new EvaluationConfig(
                        content.getContentString("evaluation"),
                        content.getContentString("system_prompt"),
                        content.getContentString("user_prompt")))
                .collect(Collectors.toList());
    }
}
