package com.sankuai.dzhealth.ai.service.agent.domain.strategy.impl;

import com.sankuai.dzhealth.ai.service.agent.domain.buffer.MessageBuffer;
import com.sankuai.dzhealth.ai.service.agent.domain.context.ChatClientContext;
import com.sankuai.dzhealth.ai.service.agent.domain.strategy.TaskTypeStrategy;
import org.springframework.stereotype.Component;

/**
 * 默认任务类型策略
 * 
 * <AUTHOR>
 * @time 2025/1/15
 */
@Component
public class DefaultTaskTypeStrategy implements TaskTypeStrategy {
    
    @Override
    public String getTaskType() {
        return "default";
    }
    
    @Override
    public void preFirstOutput(ChatClientContext context, MessageBuffer buffer, boolean hasFirst) {
        // 默认策略不做任何预处理
    }
    
    @Override
    public String processText(String text, ChatClientContext context) {
        // 默认策略直接返回原文本
        return text;
    }
    
    @Override
    public String onComplete(ChatClientContext context) {
        // 默认策略无剩余内容
        return null;
    }
}
