package com.sankuai.dzhealth.ai.service.domain.prompt;

import org.apache.commons.lang3.StringUtils;


public class XhsNoteCoverPrompt {
    public String PROMPT = """

请你帮忙设计小红书封面文案。具体如下：

## 文案需求概述

小红书笔记写者，创作了小红书笔记的正文和标题。需要你给该创作的小红书笔记设计一个其封面图片用的文案。

## 参考资料

### 创作好的小红书笔记的标题

{{{title}}}

### 创作好的小红书正文

{{{content}}}

### 该笔记被创作出来时参考的一些科普知识

{{{knowledge}}}

## 文案设计需求

参考上述的[参考资料](#参考资料), 设计封面图片用的下述文案。

1. 副标题
2. 要点摘要

## 输出要求

```jsonc
{
  "subTitle": "",
  "keyPoint": ""
}
```

字段“subTitle”，填充你创作的副标题

字段“keyPoint”，填充你创作的要点摘要

- 确保 JSON 输出格式正确，避免语法错误。
- 直接按输出格式输出 JSON 格式的字符串，不要输出 markdown 格式的 JSON

            """;

    private String title;
    private String content;
    private String knowledge;

    public static Builder builder() {
        return new Builder();
    }

    public static class Builder {
        private final XhsNoteCoverPrompt xhsNoteCoverPrompt;

        public Builder() {
            xhsNoteCoverPrompt = new XhsNoteCoverPrompt();
        }

        public Builder title(String title) {
            xhsNoteCoverPrompt.title = title;
            return this;
        }

        public Builder content(String content) {
            xhsNoteCoverPrompt.content = content;
            return this;
        }

        public Builder knowledge(String knowledge) {
            xhsNoteCoverPrompt.knowledge = knowledge;
            return this;
        }

        public String build() {
            return xhsNoteCoverPrompt.toStr();
        }
    }

    public String toStr() {
        return StringUtils.replaceEach(PROMPT,
                new String[]{"{{{title}}}", "{{{content}}}", "{{{knowledge}}}"},
                new String[]{title, content, knowledge});

    }
}
