package com.sankuai.dzhealth.ai.service.agent.domain.tools;

import com.alibaba.fastjson.JSON;
import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import com.dianping.rhino.Rhino;
import com.dianping.rhino.threadpool.DefaultThreadPoolProperties;
import com.dianping.rhino.threadpool.ThreadPool;
import com.dianping.ugc.review.remote.enums.ReviewPlatFormEnum;
import com.dianping.ugc.review.remote.mt.MTReviewQueryServiceV2;
import com.meituan.mdp.boot.starter.pigeon.annotation.MdpPigeonClient;
import com.sankuai.dzim.pilot.api.VectorSearchService;
import com.sankuai.dzim.pilot.api.data.ReviewDTO;
import com.sankuai.dzim.pilot.api.data.ReviewVectorSearchRequest;
import com.sankuai.dzim.pilot.api.data.VectorSearchResponse;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * POI评价查询服务
 *
 * @author: jiyizhou
 * @time: 2025/7/10 20:00
 * @version: 0.0.1
 */
@Service
@Slf4j
public class PoiReviewService {


    @MdpPigeonClient(url = "com.sankuai.dzim.pilot.api.VectorSearchService", timeout = 3000)
    private VectorSearchService vectorSearchService;

    @MdpPigeonClient(url = "UGCReviewService.MTReviewQueryServiceV2", timeout = 2000)
    private MTReviewQueryServiceV2 mtReviewQueryServiceV2;

    // 专用于评价查询的线程池
    private static final ThreadPool REVIEW_QUERY_POOL = Rhino.newThreadPool("REVIEW_QUERY_POOL",
            DefaultThreadPoolProperties.Setter().withCoreSize(50).withMaxSize(100).withMaxQueueSize(500));

    private static final String CAT_TYPE = PoiReviewService.class.getSimpleName();

    /**
     * 批量异步查询POI评价信息
     *
     * @param poiIds POI ID列表
     * @param size 返回评价数量
     * @param query 查询条件
     * @return Map<POI_ID, Map<评价ID, 评价内容>>
     */
    public Map<Long, Map<Long, String>> batchQueryReview(List<Long> poiIds, Integer size, String query) {
        Transaction transaction = Cat.newTransaction(CAT_TYPE, "batchQueryReviews");

        try {
            if (CollectionUtils.isEmpty(poiIds)) {
                log.info("POI ID列表为空");
                return new HashMap<>();
            }

            // 线程安全的结果容器
            Map<Long, Map<Long, String>> results = new ConcurrentHashMap<>();

            String config = Lion.getString(Environment.getAppName(), "recommend.vector.size");

            // 为每个poiId创建异步任务
            List<CompletableFuture<Void>> futures = poiIds.stream()
                    .map(poiId -> CompletableFuture.runAsync(() -> {
                        try {
                            ReviewVectorSearchRequest request = JSON.parseObject(config, ReviewVectorSearchRequest.class);
                            request.setQuery(query);
                            request.setTopK(size);
                            Map<Long, String> reviewString = queryReviews(poiId, request);
                            log.info("查询POI评价成功, poiId={}, 评价数量={}", poiId, reviewString.size());
                            results.put(poiId, reviewString);
                        } catch (Exception e) {
                            log.error("查询POI评价异常, poiId={}", poiId, e);
                            results.put(poiId, new HashMap<>());
                        }
                    }, REVIEW_QUERY_POOL.getExecutor()))
                    .collect(Collectors.toList());


            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
                    .get(2, TimeUnit.SECONDS); // 2秒超时

            log.info("批量查询POI评价完成, 请求数量={}, 成功数量={}", poiIds.size(), results.size());
            transaction.setSuccessStatus();
            return results;

        } catch (Exception e) {
            log.error("批量查询POI评价异常, poiIds={}", poiIds, e);
            transaction.setStatus(e);
            return new HashMap<>();
        } finally {
            transaction.complete();
        }
    }

    /**
     * 查询单个POI的评价并格式化
     *
     * @param poiId POI ID
     * @param request 查询请求
     * @return 评价ID到评价内容的映射
     */
    private Map<Long, String> queryReviews(Long poiId, ReviewVectorSearchRequest request) {
        Transaction transaction = Cat.newTransaction(CAT_TYPE, "queryReviews");

        try {
            log.info("查询POI评价, poiId={}", poiId);
            request.setMtShopId(poiId);
            VectorSearchResponse<ReviewDTO> response = vectorSearchService.searchReview(request);

            if (response == null || CollectionUtils.isEmpty(response.getDataList())) {
                log.info("本次POI评价查询无结果, poiId={}", poiId);
                return new HashMap<>();
            }

            List<ReviewDTO> reviewList = response.getDataList();

            // 按platform分别获取reviewid到unionReviewId的映射关系，并构造UnionReviewDTO
            List<UnionReviewDTO> unionReviewList = new ArrayList<>();
            for (Integer platform : Arrays.asList(ReviewPlatFormEnum.DP.value, ReviewPlatFormEnum.MT.value)) {
                List<ReviewDTO> platformReviews = reviewList.stream()
                        .filter(reviewDTO -> Objects.equals(reviewDTO.getPlatform(),
                                platform == ReviewPlatFormEnum.DP.value ? 0 : 1))
                        .collect(Collectors.toList());

                if (CollectionUtils.isNotEmpty(platformReviews)) {
                    Set<Long> feedbackIds = platformReviews.stream()
                            .map(ReviewDTO::getReviewId)
                            .collect(Collectors.toSet());

                    try {
                        Map<Long, Long> reviewMapping =
                                mtReviewQueryServiceV2.getReviewMapping(new ArrayList<>(feedbackIds), platform);

                        // 为当前platform的评价构造UnionReviewDTO
                        List<UnionReviewDTO> platformUnionReviews = platformReviews.stream()
                                .filter(reviewDTO -> reviewMapping.containsKey(reviewDTO.getReviewId()))
                                .map(reviewDTO -> {
                                    UnionReviewDTO unionReviewDTO = new UnionReviewDTO();
                                    // 复制ReviewDTO的所有字段
                                    unionReviewDTO.setContent(reviewDTO.getContent() == null ?
                                            null :
                                            reviewDTO.getContent().replace("\\\\n", " "));
                                    // 设置unionReviewId
                                    Long unionReviewId = reviewMapping.get(reviewDTO.getReviewId());
                                    unionReviewDTO.setUnionReviewId(unionReviewId);
                                    return unionReviewDTO;
                                })
                                .toList();

                        unionReviewList.addAll(platformUnionReviews);
                        log.info("获取platform={}的评价映射成功, 数量={}", platform,
                                reviewMapping != null ? reviewMapping.size() : 0);
                    } catch (Exception e) {
                        log.error("获取platform={}的评价映射异常", platform, e);
                    }
                }
            }

            // 构建返回结果
            Map<Long, String> reviewsMap = unionReviewList.stream()
                    .collect(Collectors.toMap(UnionReviewDTO::getUnionReviewId, UnionReviewDTO::getContent));

            log.info("本次POI评价查询成功, poiId={}, 评价数量={}",
                    poiId, reviewsMap.size());
            transaction.setSuccessStatus();
            return reviewsMap;

        } catch (Exception e) {
            log.error("查询POI评价异常, poiId={}", poiId, e);
            transaction.setStatus(e);
            return new HashMap<>();
        } finally {
            transaction.complete();
        }
    }

    @Data
    public static class UnionReviewDTO {
        private Long unionReviewId;

        private String content;
    }
}




