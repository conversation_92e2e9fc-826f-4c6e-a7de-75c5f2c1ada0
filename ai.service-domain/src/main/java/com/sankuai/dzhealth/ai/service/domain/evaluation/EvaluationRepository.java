package com.sankuai.dzhealth.ai.service.domain.evaluation;

import com.sankuai.dzhealth.ai.service.domain.evaluation.entity.RagEvaluationResponse;
import com.sankuai.dzhealth.ai.service.infrastructure.dal.entity.RagEvaluationResponseEntityWithBLOBs;
import com.sankuai.dzhealth.ai.service.infrastructure.dal.mapper.RagEvaluationResponseEntityMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

import static com.sankuai.dzhealth.ai.service.domain.utils.JsonUtils.toJsonString;

/**
 * @author: yangweicheng
 * @date: 2025/4/15 16:58
 * @version: 1.0
 */
@Repository
@Slf4j
public class EvaluationRepository {

    @Resource
    private RagEvaluationResponseEntityMapper ragEvaluationResponseEntityMapper;

    public void insertEvaluations(List<RagEvaluationResponse> result) {
        if (CollectionUtils.isEmpty(result)) {
            return;
        }
        for (RagEvaluationResponse evaluation : result) {
            RagEvaluationResponseEntityWithBLOBs entity = new RagEvaluationResponseEntityWithBLOBs();
            entity.setEvaluationKey(evaluation.getKey());
            entity.setDescription(evaluation.getDescription());
            entity.setSuccess(evaluation.isSuccess());
            entity.setPass(evaluation.isPass());
            entity.setScore(evaluation.getScore());
            entity.setFeedback(evaluation.getFeedback());
            entity.setMetadata(toJsonString(evaluation.getMetadata()));
            entity.setSessionId(evaluation.getSessionId());
            entity.setMsgId(evaluation.getMsgId());
            entity.setModelscene(evaluation.getModelScene());
            entity.setBizscene(evaluation.getBizScene());
            ragEvaluationResponseEntityMapper.insertSelective(entity);
        }
    }
}
