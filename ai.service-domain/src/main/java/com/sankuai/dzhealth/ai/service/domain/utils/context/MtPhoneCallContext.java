package com.sankuai.dzhealth.ai.service.domain.utils.context;


import com.alibaba.fastjson.JSON;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class MtPhoneCallContext {

    @FieldDoc(
            description = "会话id"
    )
    private Long sessionId;

    @FieldDoc(
            description = "商户id"
    )
    private Long shopId;


    @FieldDoc(
            description = "问题"
    )
    private String question;

    @FieldDoc(
            description = "用户Id"
    )
    private String userId;


    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }


}
