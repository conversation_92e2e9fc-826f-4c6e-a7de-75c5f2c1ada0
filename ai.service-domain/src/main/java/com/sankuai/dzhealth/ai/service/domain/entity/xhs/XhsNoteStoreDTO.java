package com.sankuai.dzhealth.ai.service.domain.entity.xhs;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.sankuai.beautycontent.store.storage.annotation.*;
import com.sankuai.beautycontent.store.storage.dto.StoreBaseDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * @author:chenwei
 * @time: 2025/6/18 15:44
 * @version: 0.0.1
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Store(storeType = 54)
public class XhsNoteStoreDTO extends StoreBaseDTO {

    @StoreExtAttribute(attributeKey = "link")
    @FieldDoc(description = "链接")
    private String link;

    @StoreExtAttribute(attributeKey = "head")
    @FieldDoc(description = "头图")
    private String head;

    @StoreExtAttribute(attributeKey = "title")
    @FieldDoc(description = "标题")
    private String title;

    @StoreExtAttribute(attributeKey = "content")
    @FieldDoc(description = "正文")
    private String content;

    @StoreReference(referenceKey = "topic")
    @FieldDoc(description = "话题")
    private List<String> topic;

    @StoreRank(rankKey = "1")
    @FieldDoc(description = "笔记曝光")
    private Long view;

    @StoreRank(rankKey = "2")
    @FieldDoc(description = "笔记互动增量")
    private Long interactIncrease;

    @StoreRank(rankKey = "3")
    @FieldDoc(description = "笔记互动全量")
    private Long interact;

    @StoreReference(referenceKey = "commentKey")
    @FieldDoc(description = "低优-评论区高频提及词")
    private List<String> commentKey;

    @StoreAttribute(attributeKey = "authorId")
    @FieldDoc(description = "发布者账号ID")
    private String authorId;

    @StoreAttribute(attributeKey = "authorName")
    @FieldDoc(description = "发布者昵称")
    private String authorName;

    @StoreAttribute(attributeKey = "type")
    @FieldDoc(description = "笔记类型,video:视频,图文:normal")
    private String type;

    @StoreReference(referenceKey = "project")
    @FieldDoc(description = "识别结果-主推项")
    private List<String> project;

    @StoreAttribute(attributeKey = "isMedicalBeauty")
    @FieldDoc(description = "识别结果-是否医美, 1是, 0否")
    private String isMedicalBeauty;

    @StoreAttribute(attributeKey = "companyName")
    @FieldDoc(description = "识别结果-竞品名称")
    private String companyName;

    @StoreAttribute(attributeKey = "batch")
    @FieldDoc(description = "上传日期")
    private String batch;

    @StoreExtAttribute(attributeKey = "raw")
    @FieldDoc(description = "原始数据")
    private String rawData;

    @StoreExtAttribute(attributeKey = "subTitle")
    @FieldDoc(description = "副标题")
    private String subTitle;

    @StoreExtAttribute(attributeKey = "keyPoint")
    @FieldDoc(description = "要点摘要")
    private String keyPoint;

}
