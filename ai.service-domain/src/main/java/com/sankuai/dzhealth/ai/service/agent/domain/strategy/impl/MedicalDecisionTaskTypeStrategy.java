package com.sankuai.dzhealth.ai.service.agent.domain.strategy.impl;

import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.sankuai.dzhealth.ai.service.agent.domain.buffer.MarkBuffer;
import com.sankuai.dzhealth.ai.service.agent.domain.buffer.MessageBuffer;
import com.sankuai.dzhealth.ai.service.agent.domain.buffer.MessageBufferEntity;
import com.sankuai.dzhealth.ai.service.agent.domain.context.ChatClientContext;
import com.sankuai.dzhealth.ai.service.agent.domain.process.card.data.ThinkProcessCardData;
import com.sankuai.dzhealth.ai.service.agent.domain.strategy.TaskTypeStrategy;
import com.sankuai.dzhealth.ai.service.enums.BufferItemTypeEnum;
import com.sankuai.dzhealth.ai.service.enums.StreamEventCardTypeEnum;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.UUID;

import static com.sankuai.dzhealth.ai.service.agent.domain.process.task.impl.MedicalProductTask.THINKING_COMPLETE_STATUS;

/**
 * 医疗决策任务类型策略
 * 该策略使用 MarkBuffer 进行文本装饰处理
 * 
 * <AUTHOR>
 * @time 2025/1/15
 */
@Component
public class MedicalDecisionTaskTypeStrategy implements TaskTypeStrategy {

    
    @Override
    public String getTaskType() {
        return "medicalDecision";
    }
    
    @Override
    public void preFirstOutput(ChatClientContext context, MessageBuffer buffer, boolean hasFirst) {
        if (hasFirst &&  buffer != null) {
            // 创建思考过程卡片
            ThinkProcessCardData thinkProcessCardData = new ThinkProcessCardData();
            thinkProcessCardData.setContent(context.getThinkingText());
            thinkProcessCardData.setFinish(true);
            thinkProcessCardData.setLoadingText(THINKING_COMPLETE_STATUS);

            buffer.writeBufferData(Collections.singletonList(MessageBufferEntity.builder()
                    .data(StreamEventCardTypeEnum.buildCardContent(
                            StreamEventCardTypeEnum.THINK_PROCESS_STREAM,
                            UUID.randomUUID().toString().replace("-", "")
                    ))
                    .type(BufferItemTypeEnum.THINK_PROCESS_STREAM.getType())
                    .extra(ImmutableMap.of("thinkProcessData", Lists.newArrayList(thinkProcessCardData)))
                    .build()), buffer);
        }
        
        // 确保 MarkBuffer 已初始化（对话级别共享）

    }
    
    @Override
    public String processText(String text, ChatClientContext context) {
        // 使用对话级别的 MarkBuffer 进行文本装饰
        MarkBuffer markBuffer = context.getMarkBuffer();
        return markBuffer.decoration(text);
    }
    
    @Override
    public String onComplete(ChatClientContext context) {
        // 使用对话级别的 MarkBuffer 获取剩余内容
        MarkBuffer markBuffer = context.getMarkBuffer();
        String result = markBuffer.complete();
        
        // 对话完成后清理 MarkBuffer（可选，根据业务需求决定）
        // markBufferManager.clearMarkBuffer();
        
        return result;
    }
}
