package com.sankuai.dzhealth.ai.service.agent.domain.process.task.impl;

import com.sankuai.dzhealth.ai.service.agent.domain.context.TaskContext;
import com.sankuai.dzhealth.ai.service.agent.domain.model.TaskProcessResult;
import com.sankuai.dzhealth.ai.service.agent.domain.process.task.GeneralTask;
import com.sankuai.dzhealth.ai.service.agent.domain.process.task.Task;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * @author:chenwei
 * @time: 2025/7/31 17:00
 * @version: 0.0.1
 */

@Slf4j
@Service
public class IrrelevantTask extends GeneralTask implements Task {
    @Override
    public boolean accept(String type) {
        return false;
    }

    @Override
    public TaskProcessResult process(TaskContext context) {
        String answer = getAnswer(context);
        return TaskProcessResult.builder().taskContext(context).answer(answer).build();
    }

    @Override
    public void after(TaskProcessResult result) {

    }
}
