package com.sankuai.dzhealth.ai.service.agent.domain.model.experiencereport;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 体验图片信息领域模型
 *
 * <AUTHOR>
 * @version 0.0.1
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ExperiencePicModel implements Serializable {

    /**
     * 图片ID
     */
    private Long picId;

    /**
     * 图片链接
     */
    private String url;

    /**
     * 图片体验标签
     */
    private String label;

    /**
     * 标签类别。0-体验前，1-体验后
     */
    private Integer type;

    /**
     * 笔记ID
     */
    private Long noteId;

    /**
     * 跳转链接
     */
    private String jumpUrl;

    /**
     * 商品ID
     */
    private Long productId;

    /**
     * 关联门店ID
     */
    private Long dpShopId;

    /**
     * 价格
     */
    private String price;

    /**
     * 体验报告标题
     */
    private String title;
}

