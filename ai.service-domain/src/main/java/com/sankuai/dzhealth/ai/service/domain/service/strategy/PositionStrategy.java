package com.sankuai.dzhealth.ai.service.domain.service.strategy;

import com.alibaba.fastjson.JSON;
import com.dianping.lion.Environment;
import com.dianping.rhino.threadpool.ThreadPool;
import com.sankuai.dzhealth.ai.service.domain.chat.model.IntentionResult;
import com.sankuai.dzhealth.ai.service.domain.service.ChatSessionService;
import com.sankuai.dzhealth.ai.service.domain.utils.context.AiAnswerContext;
import com.sankuai.dzhealth.ai.service.infrastructure.observability.Span;
import com.sankuai.map.open.platform.api.MapOpenApiService;
import com.sankuai.map.open.platform.api.division.District;
import com.sankuai.map.open.platform.api.division.DistrictRequest;
import com.sankuai.map.open.platform.api.division.DistrictResponse;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * @author:chenwei
 * @time: 2025/4/11 18:07
 * @version: 0.0.1
 */
@Service
@Slf4j
public class PositionStrategy implements DataSourceStrategy<PositionStrategy.PositionStrategyInfo>{



    @Autowired
    private MapOpenApiService.Iface districtService;

    private static final ThreadPool taskPool = ChatSessionService.TASK_POOL;

    /**
     * 是否应该执行该策略
     *
     * @param intentionResult 意图识别结果
     * @return 是否执行
     */
    @Override
    public boolean shouldExecute(IntentionResult intentionResult) {
        return intentionResult != null;
    }

    /**
     * 执行策略
     *
     * @param context     上下文
     * @param rewriteText 改写后的文本
     * @return 策略执行结果
     */
    @Override
    public CompletableFuture<PositionStrategyInfo> execute(AiAnswerContext context, String rewriteText) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                long startTime = System.currentTimeMillis();
                PositionStrategyInfo res = new PositionStrategyInfo();
                DistrictRequest request = new DistrictRequest();
                request.setKey(Environment.isTestEnv() ? "mb0fdc36dc44438b8d35375270668fdt" : "mfacf02b218842d4a97e667389f8365t");
                request.setLocation(String.format("%.6f,%.6f", context.getLng(), context.getLat()));
                request.setCoordinates("0");
                DistrictResponse districtResponse = districtService.district(request);
                log.info("districtService.district,req={},res={}", JSON.toJSONString(request), JSON.toJSONString(districtResponse));
                if (districtResponse != null && districtResponse.getStatus() == 200) {
                    List<District> districts = districtResponse.getDistricts();
                    if (CollectionUtils.isNotEmpty(districts)) {
                        String collect = districts.stream()
                                .map(District::getName)
                                .collect(Collectors.joining("-"));
                        res.setPosition(collect);
                        res.setSpan(Collections.singletonList(Span.builder()
                                .key(getClass().getSimpleName())
                                .value(collect)
                                .duration(System.currentTimeMillis() - startTime)
                                .build()));
                    }
                }
                return res;
            } catch (Exception e) {
                log.error("districtService_error", e);
                return null;
            }
        }, taskPool.getExecutor());

    }


    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @EqualsAndHashCode(callSuper = true)
    public static class PositionStrategyInfo extends BaseStrategyInfo {

        private String position;

        @Override
        public String toPrompt() {
            return position;
        }


    }
}
