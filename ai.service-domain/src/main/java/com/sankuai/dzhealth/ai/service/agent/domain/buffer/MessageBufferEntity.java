package com.sankuai.dzhealth.ai.service.agent.domain.buffer;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;


@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class MessageBufferEntity {

    @FieldDoc(description = "see BufferItemTypeEnum")
    private int type;

    private String data;

    private Map<String, Object> extra;


}
