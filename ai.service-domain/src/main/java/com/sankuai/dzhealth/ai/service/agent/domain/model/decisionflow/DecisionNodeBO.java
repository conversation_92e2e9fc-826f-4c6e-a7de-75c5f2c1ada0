package com.sankuai.dzhealth.ai.service.agent.domain.model.decisionflow;

import lombok.Builder;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 决策树中的步骤（节点）业务对象
 */
@Data
@Builder
public class DecisionNodeBO {

    /** 业务场景 */
    private String bizScene;

    /** 节点业务 ID（等同于 node_id 字段） */
    private String nodeId;

    /** 节点名称 */
    private String nodeName;

    /** 判别文案 */
    private String assessmentText;

    /** 示例图 URL */
    private String assessmentImg;

    /** 指导文案 */
    private String guidanceText;

    /** 状态 */
    private String status;

    /** 是否需要推供给：1-是 0-否，决定下一层的需求中有没有推荐供给模块 (默认 1) */
    private Boolean needSupply;

    /** 是否出医生：1-是 0-否 (默认 0) */
    private Boolean needDoctor;

    /** 子边列表 */
    @Builder.Default
    private List<DecisionEdgeBO> edges = new ArrayList<>();
} 