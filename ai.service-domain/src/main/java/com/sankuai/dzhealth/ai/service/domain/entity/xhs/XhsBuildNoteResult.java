package com.sankuai.dzhealth.ai.service.domain.entity.xhs;


import com.sankuai.dzhealth.ai.service.domain.utils.JsonUtils;
import com.sankuai.dzhealth.ai.service.infrastructure.utils.StrTools;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Objects;

@Data
@NoArgsConstructor
public class XhsBuildNoteResult {


    @Data
    @NoArgsConstructor
    public static class XhsBuildNoteItem {

        private XhsNoteRequest.TCSD tcsd;

        private String knowledge;

    }

    @Data
    @NoArgsConstructor
    public static class Note {
        private List<String> title;
        private String content;
        // 封面用文案
        private Cover cover;
        // 1 表示大模型生成的 笔记 不是json/有问题/为空等。0 表示正常。
        private int error = 0;

        public Note(int error) {
            this.error = error;
        }

        public static Note createNote(String content) {
            // 去除markdown语法
            content = StrTools.trimMKjson(content);
            Note t = JsonUtils.parseObject(content, Note.class);
            if (Objects.isNull(t)) {
                t = new Note(1);
                return t;
            }
            t.setError(0);
            return t;
        }

        public boolean isError() {
            return error == 1;
        }

        public String strTitle() {
            return String.join(", ", title);
        }

        public void buildCover(String result) {
            result = StrTools.trimMKjson(result);
            Cover cover = JsonUtils.parseObject(result, Cover.class);
            if (Objects.isNull(cover)) {
                this.cover = new Cover("", "");
                return;
            }
            this.cover = cover;
        }

        public String titleToJson() {
            Title t = new Title(title);
            return t.toJson();
        }
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Cover {
        private String subTitle;
        private String keyPoint;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Response {
        // 1 错误 0 正常
        private String status;

        public static String success() {
            return JsonUtils.toJsonString(new Response("0"));
        }
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Title {
        private List<String> values;

        public String toJson() {
            return JsonUtils.toJsonString(this);
        }
    }
}
