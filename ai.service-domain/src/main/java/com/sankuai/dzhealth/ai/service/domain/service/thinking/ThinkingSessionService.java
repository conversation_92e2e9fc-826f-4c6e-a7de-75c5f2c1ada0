package com.sankuai.dzhealth.ai.service.domain.service.thinking;

import com.sankuai.dzhealth.ai.service.domain.enums.thinking.ThinkingSessionStatusEnum;
import com.sankuai.dzhealth.ai.service.domain.model.thinking.ThinkingSession;

import java.util.Map;

/**
 * 思考会话服务接口
 */
public interface ThinkingSessionService {
    
    /**
     * 创建思考会话
     *
     * @param query 查询问题
     * @param searchConfig 搜索配置
     * @return 会话ID
     */
    Long createSession(String query, Map<String, Object> searchConfig);
    
    /**
     * 更新会话状态
     *
     * @param sessionId 会话ID
     * @param status 状态
     */
    void updateSessionStatus(Long sessionId, ThinkingSessionStatusEnum status);
    
    /**
     * 获取会话信息
     *
     * @param sessionId 会话ID
     * @return 思考会话
     */
    ThinkingSession getSession(Long sessionId);
    
    /**
     * 更新会话搜索结果
     *
     * @param sessionId 会话ID
     * @param searchResults 搜索结果
     */
    void updateSessionSearchResults(Long sessionId, Map<String, Object> searchResults);
    
    /**
     * 更新会话实际步骤数
     *
     * @param sessionId 会话ID
     * @param actualSteps 实际步骤数
     */
    void updateActualSteps(Long sessionId, Long actualSteps);
    
    /**
     * 结束会话
     *
     * @param sessionId 会话ID
     * @param status 结束状态
     */
    void finishSession(Long sessionId, ThinkingSessionStatusEnum status);
}