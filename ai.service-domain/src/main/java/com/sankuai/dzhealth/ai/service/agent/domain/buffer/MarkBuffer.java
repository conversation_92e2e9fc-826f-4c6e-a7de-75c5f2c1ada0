package com.sankuai.dzhealth.ai.service.agent.domain.buffer;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.regex.Pattern;
import java.util.regex.Matcher;

/**
 * 用于大模型流式输出时，识别出词库中的词条，并生成对应的Markdown链接
 */
public class MarkBuffer {
    /**
     * Trie树的根节点
     */
    private final TrieNode root;
    /**
     * 当前Trie树节点
     */
    private TrieNode current;
    /**
     * 全局缓冲区，用于存储当前正在处理的字符串
     */
    private final StringBuilder globalBuffer;
    /**
     * 用于存储已经处理过的词条
     */
    private final Set<String> markSet;
    /**
     * XML/HTML标签处理相关字段
     */
    private final StringBuilder tagBuffer;
    private boolean inTag = false;
    private final Pattern tagPattern = Pattern.compile("<([\\w-,;:]+)>(.*?)</\\1>");
    /**
     * 标签缓存限制相关字段
     */
    private int tagInputCounter = 0;
    private static final int MAX_TAG_INPUTS = 10;

    public MarkBuffer(Set<String> vocabulary) {
        this.root = new TrieNode();
        this.current = root;
        this.globalBuffer = new StringBuilder();
        this.markSet = new HashSet<>();
        this.tagBuffer = new StringBuilder();
        buildTrie(vocabulary);
    }

    /**
     * 构建Trie树
     *
     * @param vocabulary 词库集合
     */
    private void buildTrie(Set<String> vocabulary) {
        for (String word : vocabulary) {
            TrieNode node = root;
            for (char c : word.toCharArray()) {
                node.children.putIfAbsent(c, new TrieNode());
                node = node.children.get(c);
            }
            node.isEnd = true;
        }
    }

    /**
     * 标识百科词汇
     * <p>
     * </p>
     *
     * <pre>
     * decoration方法流程如下：
     * 1. 创建一个方法级别的StringBuilder临时缓存
     * 2. 将字符串拆分为单个字符进行遍历
     * 3. 检查是否在XML/HTML标签处理状态中
     *     1. 如果在标签状态中，将字符添加到标签缓冲区和临时缓存，检查是否形成完整标签
     *         1. 如果匹配到完整标签格式 "<([\w-,;:]+)>(.*?)</\1>"，退出标签状态
     *         2. 继续处理下一个字符
     * 4. 检查是否遇到标签开始符号 '<'
     *     1. 如果遇到，先输出全局缓冲区内容，然后进入标签处理状态
     * 5. 判断单个字符是否可以在前缀树中继续匹配（正常词汇匹配流程）
     *     1. 能够继续匹配
     *         1. 将字符加入全局缓存
     *         2. 更新前缀树指针
     *         3. 已经匹配到叶子节点（匹配到了百科词汇），
     *             1. 判断百科词汇是否已经处理过（只处理一次）
     *                 1. 未处理过，全局缓存按照 "[缓存内容]()" 装饰后加入到临时缓存，添加到已处理的百科词汇集合中
     *                 2. 已处理过，直接添加到临时缓存（也可以不添加，这里为了保证即时性）
     *             2. 清空全局缓存
     *             3. 前缀树重置到根结点
     *     2. 无法匹配
     *         1. 字符加入全局缓存
     *         2. 全局缓存加入到临时缓存
     *         3. 清空全局缓存
     * 6. 重复步骤3-5
     * 7. 调用方结束后调用 complete 方法获取剩余的缓存
     * </pre>
     *
     * <p>
     *
     * 注意：
     * </p>
     * 
     * <pre>
     * 1. 最后一定要调用 complete 方法获取剩余的缓存，否则输出内容不全
     * 2. 对于返回结果只需要判断null就行，不要使用trim和诸如isBlank的方法处理返回结果，需要保证前后空格和换行等格式
     * 3. XML/HTML标签内的内容不会进行词汇匹配，支持的标签格式为: <([\w-,;:]+)>(.*?)</\1>
     * 4. 标签缓存有上限限制（最大10次输入），超过限制后会强制退出标签状态，按普通文本处理
     * </pre>
     * 
     * @param input 待装饰的字符串
     * @return
     */
    public String decoration(String input) {
        StringBuilder tempBuffer = new StringBuilder();
        
        // 如果在标签状态中，增加输入计数
        if (inTag) {
            tagInputCounter++;
        }
        
        for (char c : input.toCharArray()) {
            // 检查是否在标签处理状态中
            if (inTag) {
                tagBuffer.append(c);
                tempBuffer.append(c);
                
                // 检查是否超过最大缓存限制
                if (tagInputCounter >= MAX_TAG_INPUTS) {
                    // 超过限制，强制退出标签状态，将缓存内容按普通文本处理
                    inTag = false;
                    tagInputCounter = 0;
                    tagBuffer.setLength(0);
                    // 重置前缀树状态，继续正常处理后续字符
                    current = root;
                    globalBuffer.setLength(0);
                    continue;
                }
                
                // 检查当前累积的标签内容是否符合完整标签格式
                String tagContent = tagBuffer.toString();
                Matcher matcher = tagPattern.matcher(tagContent);
                if (matcher.matches()) {
                    // 找到完整的标签，退出标签状态
                    inTag = false;
                    tagInputCounter = 0;
                    tagBuffer.setLength(0);
                }
                continue;
            }
            
            // 检查是否遇到标签开始符号
            if (c == '<') {
                // 先将全局缓冲区的内容输出
                if (globalBuffer.length() > 0) {
                    tempBuffer.append(globalBuffer);
                    globalBuffer.setLength(0);
                    current = root;
                }
                
                // 进入标签处理状态
                inTag = true;
                tagInputCounter = 1; // 初始化计数器，当前输入算作第1次
                tagBuffer.setLength(0);
                tagBuffer.append(c);
                tempBuffer.append(c);
                continue;
            }
            
            // 正常的词汇匹配流程
            if (current.children.containsKey(c)) {
                globalBuffer.append(c);
                current = current.children.get(c);
                if (current.isEnd) {
                    String encyclopedia = globalBuffer.toString();
                    if (!markSet.contains(encyclopedia)) {
                        tempBuffer.append("[").append(encyclopedia).append("]()");
                        markSet.add(encyclopedia);
                    } else {
                        tempBuffer.append(encyclopedia);
                    }
                    globalBuffer.setLength(0);
                    current = root;
                }
            } else {
                globalBuffer.append(c);
                tempBuffer.append(globalBuffer);
                globalBuffer.setLength(0);
                current = root;
            }
        }
        return !tempBuffer.isEmpty() ? tempBuffer.toString() : null;
    }

    /**
     * 用于返回剩余的缓存
     *
     * @return
     */
    public String complete() {
        StringBuilder result = new StringBuilder();
        
        // 如果还在标签状态中，返回标签缓冲区内容
        if (inTag && tagBuffer.length() > 0) {
            result.append(tagBuffer);
            tagBuffer.setLength(0);
            inTag = false;
            tagInputCounter = 0; // 重置计数器
        }
        
        // 返回全局缓冲区的内容
        if (globalBuffer.length() > 0) {
            result.append(globalBuffer);
            globalBuffer.setLength(0);
        }
        
        current = root;
        return result.length() > 0 ? result.toString() : "";
    }

    private static class TrieNode {
        Map<Character, TrieNode> children;
        boolean isEnd;

        TrieNode() {
            this.children = new HashMap<>();
            this.isEnd = false;
        }
    }
}
