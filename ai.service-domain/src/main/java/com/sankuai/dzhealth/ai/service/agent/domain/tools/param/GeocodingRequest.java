package com.sankuai.dzhealth.ai.service.agent.domain.tools.param;

import lombok.Data;
import org.springframework.ai.tool.annotation.ToolParam;

@Data
public class GeocodingRequest {

    @ToolParam(description = "The lat value, representing the north-south position on the Earth's surface. Should be a decimal number between -90 and 90.")
    private Double lat;

    @ToolParam(description = "The lng value, representing the east-west position on the Earth's surface. Should be a decimal number between -180 and 180.")
    private Double lng;
}
