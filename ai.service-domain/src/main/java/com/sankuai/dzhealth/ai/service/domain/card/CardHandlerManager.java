package com.sankuai.dzhealth.ai.service.domain.card;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.Cat;
import com.dianping.rhino.Rhino;
import com.dianping.rhino.threadpool.DefaultThreadPoolProperties;
import com.dianping.rhino.threadpool.ThreadPool;
import com.google.common.collect.Lists;
import com.sankuai.dzhealth.ai.service.domain.utils.context.AiAnswerContext;
import com.sankuai.dzhealth.ai.service.dto.stream.StreamEventDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.OrderComparator;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 卡片处理器管理器，负责协调各个卡片处理器
 */
@Slf4j
@Component
public class CardHandlerManager {
    
    private final List<CardHandler> handlers;
    
    // 创建专用于卡片处理的线程池
    private static final ThreadPool CARD_HANDLER_POOL = Rhino.newThreadPool("CARD_HANDLER_POOL",
            DefaultThreadPoolProperties.Setter()
                    .withCoreSize(10)
                    .withMaxSize(20)
                    .withMaxQueueSize(100));
    
    @Autowired
    public CardHandlerManager(List<CardHandler> handlers) {
        // 对处理器按照Order进行排序
        List<CardHandler> sortedHandlers = new ArrayList<>(handlers);
        OrderComparator.sort(sortedHandlers);
        
        this.handlers = sortedHandlers;
        
        // 日志输出处理器排序情况，方便排查问题
        if (log.isInfoEnabled()) {
            log.info("已加载的卡片处理器及其顺序:");
            for (CardHandler handler : this.handlers) {
                log.info("  {} 优先级: {}", handler.getClass().getSimpleName(), handler.getOrder());
            }
        }
    }
    
    /**
     * 处理JSON对象，调用对应的卡片处理器
     * 
     * @param jsonObject JSON对象
     * @param context 上下文
     * @param sseEmitter SSE发射器
     * @param sseIndex 索引计数器
     * @param startChatTime 开始时间
     * @return 生成的事件列表
     */
    public List<StreamEventDTO> processJsonObject(JSONObject jsonObject, 
                                               AiAnswerContext context, 
                                               SseEmitter sseEmitter, 
                                               AtomicInteger sseIndex,
                                               long startChatTime) {
        if (jsonObject == null) {
            return Collections.emptyList();
        }
        
        List<StreamEventDTO> results = new ArrayList<>();
        
        // 遍历JSON字段，对每个字段寻找对应的处理器
        for (String field : jsonObject.keySet()) {
            for (CardHandler handler : handlers) {
                if (handler.supports(field)) {
                    try {
                        long start = System.currentTimeMillis();
                        List<StreamEventDTO> events = handler.handle(jsonObject, context, sseEmitter, sseIndex, startChatTime);
                        Cat.newTransactionWithDuration(handler.getClass().getSimpleName(), field, System.currentTimeMillis() - start).complete();
                        if (events != null) {
                            results.addAll(events);
                        }
                    } catch (Exception e) {
                        log.error("Error processing field: {} with handler: {}", field, handler.getClass().getSimpleName(), e);
                    }
                    // 一个字段只需要一个处理器
                    break;
                }
            }
        }
        
        return results;
    }

    public List<StreamEventDTO> processJsonObjectByField(JSONObject tempJsonObject,
                                                                AiAnswerContext context,
                                                                SseEmitter sseEmitter,
                                                                AtomicInteger sseIndex,
                                                                long startChatTime,
                                                         Set<String> processedFields) {
        List<StreamEventDTO> results = new ArrayList<>();
        for (String field : tempJsonObject.keySet()) {
            if (processedFields.contains(field)) {
                continue;
            }
            if (context.isHasText() && "continue_ask".equals(field)
                    && StringUtils.isNotBlank(tempJsonObject.getString(field))) {
                JSONObject continueJson = JSON.parseObject(tempJsonObject.getString(field));
                if (StringUtils.isNotBlank(continueJson.getString("choice_title"))) {
                    processedFields.add(field);
                    continue;
                }
            }
            if (context.isHasText() && "phone".equals(field)
                    && StringUtils.isNotBlank(tempJsonObject.getString(field))
                    && "true".equals(tempJsonObject.getString(field))) {
                    processedFields.add(field);
                    continue;
            }

            //猜你想问暂不处理
            if ("related_question".equals(field)) {
                continue;
            }
            if (processedFields.contains("continue_ask")
                    && Lists.newArrayList("departmentIds", "phone").contains(field)
                    && !context.isHasText()) {
                    processedFields.add(field);
                    continue;
            }

            log.info("尝试处理字段: {}", field);

            for (CardHandler handler : handlers) {
                if (handler.supports(field)) {
                    try {
                        JSONObject singleFieldJson = new JSONObject();
                        singleFieldJson.put(field, tempJsonObject.get(field));

                        List<StreamEventDTO> events = handler.handle(singleFieldJson, context, sseEmitter, sseIndex, startChatTime);
                        // 添加处理结果
                        if (CollectionUtils.isNotEmpty(events)) {
                            results.addAll(events);
                            processedFields.add(field);
                        }
                    } catch (Exception e) {
                        log.error("增量处理JSON字段出错: {}", field, e);
                    }
                    break;
                }
            }
        }
        return results;
    }


    

    public List<StreamEventDTO> processJsonObjectByHandlerOrder(JSONObject jsonObject,
                                                            AiAnswerContext context,
                                                            SseEmitter sseEmitter,
                                                            AtomicInteger sseIndex,
                                                            long startChatTime) {
        if (jsonObject == null) {
            return Collections.emptyList();
        }

        List<StreamEventDTO> results = new ArrayList<>();
        
        // 按照处理器顺序遍历
        for (CardHandler handler : handlers) {
            // 遍历JSON字段，查找处理器支持的字段
            for (String field : jsonObject.keySet()) {
                if (handler.supports(field)) {
                    try {
                        long start = System.currentTimeMillis();
                        List<StreamEventDTO> events = handler.handle(jsonObject, context, sseEmitter, sseIndex, startChatTime);
                        Cat.newTransactionWithDuration(handler.getClass().getSimpleName(), field, System.currentTimeMillis() - start).complete();
                        if (CollectionUtils.isNotEmpty(events)) {
                            results.addAll(events);
                        }
                    } catch (Exception e) {
                        log.error("Error processing field: {} with handler: {}", field, handler.getClass().getSimpleName(), e);
                    }
                    // 一个字段只需要一个处理器
                    break;
                }
            }
        }

        return results;
    }
    
    /**
     * 基于Flux的处理方法：并行处理但保持顺序输出
     * 对于每个handler可能内部直接调用了sendCardEvent的情况，
     * 我们采用一种机制来确保事件按照handler的顺序被添加到reply列表
     * 每个handler使用独立的SseEmitterCollector实例以确保线程安全
     * 使用线程池控制并发度，避免资源耗尽
     * 
     * @param jsonObject JSON对象
     * @param context 上下文
     * @param sseEmitter SSE发射器
     * @param sseIndex 索引计数器
     * @param startChatTime 开始时间
     * @return 响应式的事件流
     */
    public Flux<StreamEventDTO> processJsonObjectReactive(JSONObject jsonObject,
                                                        AiAnswerContext context,
                                                        SseEmitter sseEmitter,
                                                        AtomicInteger sseIndex,
                                                        long startChatTime) {
        if (jsonObject == null) {
            return Flux.empty();
        }
        
        // 将处理器列表转换为 Flux，并使用flatMapSequential实现并行处理但按顺序输出
        return Flux.fromIterable(handlers)
            .flatMapSequential(handler -> {
                // 查找处理器支持的字段
                for (String field : jsonObject.keySet()) {
                    if (handler.supports(field)) {
                        // 使用CompletableFuture + 线程池 + Mono.fromFuture替代Mono.fromCallable
                        return Mono.fromFuture(CompletableFuture.supplyAsync(() -> {
                            try {
                                // 为每个处理任务创建独立的SseEmitterCollector，确保线程安全
                                SseEmitterCollector emitterCollector = new SseEmitterCollector(sseEmitter);
                                
                                long start = System.currentTimeMillis();
                                // 使用此处理器专属的收集器
                                List<StreamEventDTO> events = handler.handle(jsonObject, context, emitterCollector, sseIndex, startChatTime);
                                Cat.newTransactionWithDuration(handler.getClass().getSimpleName(), field, System.currentTimeMillis() - start).complete();
                                
                                // 处理完成后，从收集器获取事件
                                List<StreamEventDTO> collectedEvents = emitterCollector.getCollectedEvents();
                                
                                // 如果handler.handle返回了事件，也加入结果
                                if (events != null) {
                                    collectedEvents.addAll(events);
                                }
                                
                                return collectedEvents;
                            } catch (Exception e) {
                                log.error("Error processing field: {} with handler: {}", field, handler.getClass().getSimpleName(), e);
                                return Collections.<StreamEventDTO>emptyList();
                            }
                        }, CARD_HANDLER_POOL.getExecutor())).flatMapMany(Flux::fromIterable);
                    }
                }
                return Flux.empty();
            });
    }
    
    /**
     * SseEmitter的包装器，收集要发送的事件而不是直接发送
     */
    private static class SseEmitterCollector extends SseEmitter {
        private final SseEmitter delegate;
        private final List<StreamEventDTO> collectedEvents = new ArrayList<>();
        
        public SseEmitterCollector(SseEmitter delegate) {
            super(delegate.getTimeout());
            this.delegate = delegate;
        }
        
        @Override
        public void send(Object object) {
            // 不直接发送，而是收集
            if (object instanceof StreamEventDTO) {
                collectedEvents.add((StreamEventDTO) object);
            }
        }
        
        public List<StreamEventDTO> getCollectedEvents() {
            return new ArrayList<>(collectedEvents);
        }
        
        public void clearCollectedEvents() {
            collectedEvents.clear();
        }
        
        // 其他方法委托给原始的emitter
        @Override
        public void complete() {
            delegate.complete();
        }
        
        @Override
        public void completeWithError(Throwable ex) {
            delegate.completeWithError(ex);
        }
        
        // 其他需要委托的方法...
    }
    
    /**
     * 获取处理器列表
     * 返回不可修改的处理器列表，确保外部代码不会修改内部状态
     * 
     * @return 处理器列表
     */
    public List<CardHandler> getHandlers() {
        return handlers;
    }
} 