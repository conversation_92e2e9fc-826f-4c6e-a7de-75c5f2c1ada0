package com.sankuai.dzhealth.ai.service.agent.domain.process.card;

import com.sankuai.dzhealth.ai.service.agent.domain.context.MessageContext;
import com.sankuai.dzhealth.ai.service.dto.stream.StreamEventCardDataDTO;

/**
 * @author:chenwei
 * @time: 2025/7/9 11:06
 * @version: 0.0.1
 */
public interface CardBuilder {

    // card标识
    boolean accept(String cardType);


    // card属性填充
    void padding(StreamEventCardDataDTO streamEventCardDataDTO, MessageContext messageContext);

    /**
     * 解析属性到记忆中 大模型可以理解
     * @param streamEventCardDataDTO
     * @return
     */
    String parseCardProps(StreamEventCardDataDTO streamEventCardDataDTO);
}
