package com.sankuai.dzhealth.ai.service.domain.entity.xhs;

import com.sankuai.dzhealth.ai.service.domain.utils.JsonUtils;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Collection;
import java.util.Optional;

@Data
@NoArgsConstructor
public class ServeConfig {
    private boolean trace;

    public static Optional<ServeConfig> convertSelf(Object obj) {
        if (obj instanceof Collection) {
            return Optional.empty();
        }
        return Optional.ofNullable(JsonUtils.convertValue(obj, ServeConfig.class));
    }
}
