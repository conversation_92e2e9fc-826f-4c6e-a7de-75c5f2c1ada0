package com.sankuai.dzhealth.ai.service.agent.domain.buffer;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.meituan.mdp.boot.starter.util.Pair;
import com.sankuai.dzhealth.ai.service.agent.domain.buffer.label.CustomTruncateLabel;
import com.sankuai.dzhealth.ai.service.agent.domain.buffer.label.TruncateLabel;
import com.sankuai.dzhealth.ai.service.agent.domain.context.MessageContext;
import com.sankuai.dzhealth.ai.service.agent.domain.utils.BufferUtils;
import com.sankuai.dzhealth.ai.service.agent.domain.utils.ContentBuilderUtils;
import com.sankuai.dzhealth.ai.service.domain.utils.JsonUtils;
import com.sankuai.dzhealth.ai.service.dto.SseAwareException;
import com.sankuai.dzhealth.ai.service.dto.stream.StreamEventCardDataDTO;
import com.sankuai.dzhealth.ai.service.dto.stream.StreamEventDTO;
import com.sankuai.dzhealth.ai.service.dto.stream.StreamEventDataDTO;
import com.sankuai.dzhealth.ai.service.enums.BufferItemTypeEnum;
import com.sankuai.dzhealth.ai.service.enums.StreamEventCardTypeEnum;
import com.sankuai.dzhealth.ai.service.enums.StreamEventDataTypeEnum;
import com.sankuai.dzhealth.ai.service.enums.StreamEventErrorTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Queue;
import java.util.concurrent.Callable;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 缓冲区消费任务
 *
 * <AUTHOR>
 * @time 2025/7/6 15:40
 * @version 0.0.3
 */
@Slf4j
public class BufferConsumerTask implements Callable<Boolean> {

    private MessageBuffer messageBuffer;

    private SseEmitter sseEmitter;

    private MessageContext messageContext;

    private List<TruncateLabel> truncateLabelList;

    private TruncateLabel curTruncate;

    private boolean hasMainText = false;

    /**
     * 事件的索引
     */
    private final AtomicInteger eventIndex = new AtomicInteger(0);

    /**
     * 通过构造函数注入的BufferUtils实例
     */
    private final BufferUtils bufferUtils;

    /**
     * 通过构造函数注入的ContentBuilderUtils实例
     */
    private final ContentBuilderUtils contentBuilderUtils;

    private static final List<StreamEventCardTypeEnum> CARD_SPECIAL_LIST = Lists.newArrayList(StreamEventCardTypeEnum.PHOTO_TAKING,
            StreamEventCardTypeEnum.OPTION_CARD);

    public BufferConsumerTask(MessageBuffer messageBuffer, SseEmitter sseEmitter, 
                             List<TruncateLabel> truncateLabelList, MessageContext messageContext,
                             BufferUtils bufferUtils, ContentBuilderUtils contentBuilderUtils) {
        this.messageBuffer = messageBuffer;
        this.sseEmitter = sseEmitter;
        this.messageContext = messageContext;
        this.bufferUtils = bufferUtils;
        this.contentBuilderUtils = contentBuilderUtils;
        
        if (CollectionUtils.isEmpty(truncateLabelList)) {
            this.truncateLabelList = Lists.newArrayList(new CustomTruncateLabel());
        } else {
            this.truncateLabelList = truncateLabelList;
        }
    }

    @Override
    public Boolean call() {
        if (messageBuffer == null || sseEmitter == null) {
            log.error("Buffer or SseEmitter is null", new SseAwareException(StreamEventErrorTypeEnum.SSE_BUFFER_ERROR));
            return true;
        }
        List<MessageBufferEntity> entityList = Lists.newArrayList();
        List<MessageBufferEntity> itemsToProcess = new ArrayList<>();
        int currentIndex = 0;
        int startIndex = -1;
        try {
            while (true) {
                // 阻塞式地从队列中获取一个数据项, 会一直等待直到有数据
                MessageBufferEntity firstEntity = messageBuffer.getWritten().take();


                itemsToProcess.add(firstEntity);

                // 批量获取队列中所有剩余的数据项
                messageBuffer.getWritten().drainTo(itemsToProcess);

                boolean finishSignal = false;
                int limit = 40;
                while (currentIndex <= itemsToProcess.size() - 1) {
                    MessageBufferEntity entity = itemsToProcess.get(currentIndex);
                    if (BufferItemTypeEnum.FINISH_WRITE.getType() == entity.getType()) {
                        finishSignal = true;
                        break;
                    }

                    if (BufferItemTypeEnum.MAIN_TEXT.getType() == entity.getType() && !hasMainText) {
                        //思考结束 开始进入正文
                        hasMainText = true;
                    }

                    updateTruncate(entity.getData());

                    if (curTruncate != null && hasMainText && entity.getData().contains(curTruncate.getIdentifier())) {
                        startIndex = currentIndex;
                    }
                    boolean hasAdd = hasAddCurrentItem(startIndex, currentIndex, limit, entityList, entity);

                    process(entityList, entity, hasAdd);

                    currentIndex++;

                }


                // 如果在批次中找到了结束信号，则在处理完批内所有数据后，终止消费循环
                if (finishSignal) {
                    log.info("Buffer consumer task received finish signal, stopping.");
                    break;
                }
            }
        } catch (Exception e) {
            log.error("Buffer consumer task failed.", e);
            return false;
        }
        return true;
    }

    private void process(List<MessageBufferEntity> entityList, MessageBufferEntity entity, boolean hasAdd) {

        if (entity == null) {
            return;
        }
        if (CollectionUtils.isNotEmpty(entityList)) {
            processList(entityList, !hasAdd);
        }
        // 没有标签
        if (!hasAdd) {
            processSingle(entity);
        }


    }

    private void processList(List<MessageBufferEntity> entityList, boolean needAll) {

        // 处理全部 已经结束
        if (needAll) {
            processAll(entityList, curTruncate);
            curTruncate = null;
            return;
        }
        processPart(entityList);
    }

    private void processPart(List<MessageBufferEntity> entityList) {

        if (CollectionUtils.isEmpty(entityList)) {
            return;
        }

        List<MessageBufferEntity> mainTextEntityList = entityList.stream().filter(item -> item.getType() ==
                BufferItemTypeEnum.MAIN_TEXT.getType()).collect(Collectors.toList());
        List<MessageBufferEntity> otherEntityList = entityList.stream().filter(item -> item.getType() !=
                BufferItemTypeEnum.MAIN_TEXT.getType()).collect(Collectors.toList());

        StringBuilder sb = new StringBuilder();
        Map<String, Object> extra = Maps.newHashMap();
        for (MessageBufferEntity entity : mainTextEntityList) {
            extra.putAll(MapUtils.isEmpty(entity.getExtra()) ? Maps.newHashMap() : entity.getExtra());
            sb.append(entity.getData());
        }

        String data = sb.toString();

        List<Pair<String, String>> mainTextTags = curTruncate.parseLabels(data);

        //所有已闭合标签之后，判断是否还有未闭合的标签
        int tagIndex = 0;
        for (Pair<String, String> label : mainTextTags) {
            String labelContent = curTruncate.getContent(label);
            tagIndex = Math.max(data.indexOf(labelContent) + labelContent.length(), tagIndex);
        }
        String remainData = data.substring(tagIndex);

        // 剩余文本看下有没有其他截断策略
        TruncateLabel old = curTruncate;

        curTruncate = null;

        // 更新一下 标签截断
        updateTruncate(remainData);
        if (curTruncate != null) {

            entityList.clear();
            MessageBufferEntity mainTextEntity = new MessageBufferEntity();
            mainTextEntity.setType(BufferItemTypeEnum.MAIN_TEXT.getType());
            mainTextEntity.setData(remainData);
            mainTextEntity.setExtra(extra);
            entityList.add(mainTextEntity);
            entityList.addAll(otherEntityList);

            //发送前面已闭合的部分
            String closed = data.substring(0, tagIndex);
            if (StringUtils.isNotEmpty(closed)) {
                List<MessageBufferEntity> closedEntityList = Lists.newArrayList();
                MessageBufferEntity closedMainText = new MessageBufferEntity();
                closedMainText.setType(BufferItemTypeEnum.MAIN_TEXT.getType());
                closedMainText.setData(closed);
                closedMainText.setExtra(extra);
                closedEntityList.add(closedMainText);
                processAll(closedEntityList, old);
            }
        } else {
            //没有未闭合的，处理全部
            processAll(entityList, old);
        }

    }

    private void processAll(List<MessageBufferEntity> entityList, TruncateLabel truncateLabel) {

        // 正文包含标签
        List<MessageBufferEntity> mainTextEntities = entityList.stream()
                .filter(e -> BufferItemTypeEnum.MAIN_TEXT.getType() == e.getType()).collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(mainTextEntities)) {
            StreamEventDataDTO mainText = convertLabels(mainTextEntities, truncateLabel);
            StreamEventDTO mainEventDTO = contentBuilderUtils.buildEvent(mainText, messageContext);
            mainEventDTO.setIndex(eventIndex.getAndIncrement());
            sendSseMessage(mainEventDTO);
        }

        // 推荐问题
        List<MessageBufferEntity> questionEntities = entityList.stream()
                .filter(e -> BufferItemTypeEnum.RECOMMEND_QUESTION.getType() == e.getType()).collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(questionEntities)) {
            StreamEventDataDTO questionText = convertLabels(questionEntities, truncateLabel);
            StreamEventDTO questionEventDTO = contentBuilderUtils.buildEvent(questionText, messageContext);
            questionEventDTO.setIndex(eventIndex.getAndIncrement());
            sendSseMessage(questionEventDTO);
        }



        entityList.clear();

    }

    private void processSingle(MessageBufferEntity entity) {
        if (entity == null || StringUtils.isBlank(entity.getData())) {
            return;
        }
        StreamEventDataDTO streamEventDataDTO = new StreamEventDataDTO();
        streamEventDataDTO.setEvent(bufferUtils.convertStreamEventType(entity.getType()));
        streamEventDataDTO.setCardsData(bufferUtils.buildCardDTO(entity));
        streamEventDataDTO.setContent(getSingleContent(entity.getData(), streamEventDataDTO.getCardsData()));
        StreamEventDTO streamEventDTO = contentBuilderUtils.buildEvent(streamEventDataDTO, messageContext);
        streamEventDTO.setIndex(eventIndex.getAndIncrement());

        //补充sse发送
        sendSseMessage(streamEventDTO);

    }

    public StreamEventDataDTO convertLabels(List<MessageBufferEntity> entityList, TruncateLabel truncateLabel) {
        StringBuilder sb = new StringBuilder();
        Map<String, Object> extra = Maps.newHashMap();
        for (MessageBufferEntity entity : entityList) {
            extra.putAll(MapUtils.isEmpty(entity.getExtra()) ? Maps.newHashMap() : entity.getExtra());
            sb.append(entity.getData());
        }
        String data = sb.toString();
        List<Pair<String, String>> labels = truncateLabel == null ? Lists.newArrayList() : truncateLabel.parseLabels(data);
        List<StreamEventCardDataDTO> cardDataVOs = Lists.newArrayList();
        for (Pair<String, String> label : labels) {
            StreamEventCardDataDTO cardDataVO = new StreamEventCardDataDTO();
            cardDataVO.setType(cleanType(label.getKey()));
            cardDataVO.setKey(label.getValue());
            cardDataVO.setCardProps(extra);
            String tagContent = truncateLabel.getContent(label);

            String newTagContent = tagContent;
            if (truncateLabel.acceptTruncate("<")) {
                Pair<String, String> newLabel = new Pair<>(cleanType(label.getKey()), label.getValue());
                newTagContent = truncateLabel.getContent(newLabel);
            }
            data = formatLabels(label, tagContent, data, entityList, newTagContent);

            if (isNeedCard(label)) {
                cardDataVOs.add(cardDataVO);
            }
        }

        StreamEventDataDTO streamEventDataVO = new StreamEventDataDTO();
        streamEventDataVO.setContent(data);
        streamEventDataVO.setEvent(StreamEventDataTypeEnum.MAIN_TEXT.getType());
        streamEventDataVO.setCardsData(cardDataVOs);
        return streamEventDataVO;
    }

    private String formatLabels(Pair<String, String> label, String labelContent, String data, List<MessageBufferEntity> entityList, String newLabelContent) {
        String formatData = contentBuilderUtils.formatLabels(label, labelContent, data, entityList);

        if (StringUtils.isNotEmpty(formatData)) {
            return formatData;
        }
        StreamEventCardTypeEnum typeEnum = StreamEventCardTypeEnum.getByType(cleanType(label.getKey()));
        if (typeEnum == null) {
            return data;
        }
        boolean isOut = typeEnum.isOutBubble();
        return data.replace(labelContent, isOut ?
                (CARD_SPECIAL_LIST.contains(typeEnum) ? (":::{" + newLabelContent + "}:::\n") : (":::{" + newLabelContent + "}:::"))
                : ":::}" + newLabelContent + "{:::");
    }





    private boolean isNeedCard(Pair<String, String> label) {

        if (label.getKey().equals("**")) {
            return false;
        }
        if (StreamEventCardTypeEnum.getByType(label.getKey()) != null &&
                !StreamEventCardTypeEnum.getByType(label.getKey()).isNeedCard()) {
            return false;
        }
        return true;
    }

    private void sendSseMessage(StreamEventDTO streamEventDTO) {
        Queue<StreamEventDTO> converted = messageBuffer.getConverted();
        converted.add(streamEventDTO);

        try {
            if (messageBuffer.isSseClose()) {
                return;
            }
            sseEmitter.send(streamEventDTO);
        } catch (IOException e) {
            log.error("[ConsumerTask] sendSseMessage error", e);
            messageBuffer.setSseClose(true);
        }
    }


    private String getSingleContent(String data, List<StreamEventCardDataDTO> cardsData) {
        if (CollectionUtils.isEmpty(cardsData)) {
            return data;
        }

        for (StreamEventCardDataDTO cardDataVO : cardsData) {
            StreamEventCardTypeEnum cardType = StreamEventCardTypeEnum.getByType(cardDataVO.getType());
            if (cardType == null) {
                continue;
            }
            String tagContent = StreamEventCardTypeEnum.buildCardContent(cardType, cardDataVO.getKey());

            data = data.replace(tagContent, cardType.isOutBubble() ? ":::{" + tagContent + "}:::" : ":::}" + tagContent + "{:::");
        }
        return data;
    }

    private boolean hasAddCurrentItem(int startIndex, int curIndex, int limit, List<MessageBufferEntity> entityList, MessageBufferEntity currentEntity) {
        if (startIndex >= 0 && (curIndex - startIndex) < limit && curTruncate != null) {
            entityList.add(currentEntity);
            return true;
        }
        return false;
    }

    private void updateTruncate(String data) {
        if (curTruncate != null) {
            return;
        }

        if (!hasMainText) {
            return;
        }

        if (StringUtils.isEmpty(data)) {
            return;
        }

        for (TruncateLabel truncateLabel : truncateLabelList) {
            if (truncateLabel.acceptTruncate(data)) {
                log.info("[ConsumerTask] updateTruncate:{}", JsonUtils.toJsonString(truncateLabel));
                curTruncate = truncateLabel;
            }
        }
    }

    /**
     * 清理类型字符串中的特殊字符
     * 
     * @param type 原始类型字符串
     * @return 清理后的类型字符串
     */
    private String cleanType(String type) {
        if (type == null) {
            return "";
        }
        // 使用正则表达式一次性移除所有特殊字符，性能更好
        return type.replaceAll("[-:,_]", "");
    }

} 