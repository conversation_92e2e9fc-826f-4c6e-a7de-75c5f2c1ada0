package com.sankuai.dzhealth.ai.service.agent.domain.memory;

import com.sankuai.dzhealth.ai.service.domain.utils.JsonUtils;
import org.springframework.ai.chat.client.ChatClientMessageAggregator;
import org.springframework.ai.chat.client.ChatClientRequest;
import org.springframework.ai.chat.client.ChatClientResponse;
import org.springframework.ai.chat.client.advisor.api.*;
import org.springframework.ai.chat.memory.ChatMemory;
import org.springframework.ai.chat.messages.Message;
import org.springframework.ai.chat.messages.MessageType;
import org.springframework.ai.chat.messages.SystemMessage;
import org.springframework.util.Assert;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Scheduler;

import java.util.List;
import java.util.stream.IntStream;

/**
 * @author:chenwei
 * @time: 2025/8/1 09:36
 * @version: 0.0.1
 */
public class ChatHistoryMemoryAdvisor implements BaseChatMemoryAdvisor {

    private final ChatMemory chatMemory;

    private final String defaultConversationId;

    private final int order;

    private final Scheduler scheduler;



    private ChatHistoryMemoryAdvisor(ChatMemory chatMemory, String defaultConversationId, int order,
                                     Scheduler scheduler) {
        Assert.notNull(chatMemory, "chatMemory cannot be null");
        Assert.hasText(defaultConversationId, "defaultConversationId cannot be null or empty");
        Assert.notNull(scheduler, "scheduler cannot be null");
        this.chatMemory = chatMemory;
        this.defaultConversationId = defaultConversationId;
        this.order = order;
        this.scheduler = scheduler;
    }



    @Override
    public ChatClientRequest before(ChatClientRequest chatClientRequest, AdvisorChain advisorChain) {
        String conversationId = getConversationId(chatClientRequest.context(), this.defaultConversationId);

        // 1. Retrieve the chat memory for the current conversation.
        List<Message> memoryMessages = this.chatMemory.get(conversationId);


        StringBuilder sb = new StringBuilder();
        List<Message> instructions = chatClientRequest.prompt().getInstructions();
        // 使用Stream流找到第一个SYSTEM消息并处理
        IntStream.range(0, instructions.size())
                .filter(i -> instructions.get(i).getMessageType().equals(MessageType.SYSTEM))
                .findFirst()
                .ifPresent(index -> {
                    Message systemMessage = instructions.get(index);
                    String enhancedText = sb.append(systemMessage.getText())
                            .append("\n历史对话消息为:\n")
                            .append(JsonUtils.toJsonString(memoryMessages))
                            .toString();
                    instructions.set(index, new SystemMessage(enhancedText));
                });

        ChatClientRequest processedChatClientRequest = chatClientRequest.mutate()
                .prompt(chatClientRequest.prompt().mutate().messages(instructions).build())
                .build();


        return processedChatClientRequest;
    }

    @Override
    public ChatClientResponse after(ChatClientResponse chatClientResponse, AdvisorChain advisorChain) {

        return chatClientResponse;
    }

    @Override
    public int getOrder() {
        return this.order;
    }

    @Override
    public Scheduler getScheduler() {
        return this.scheduler;
    }


    @Override
    public Flux<ChatClientResponse> adviseStream(ChatClientRequest chatClientRequest,
                                                 StreamAdvisorChain streamAdvisorChain) {
        // Get the scheduler from BaseAdvisor
        Scheduler scheduler = this.getScheduler();

        // Process the request with the before method
        return Mono.just(chatClientRequest)
                .publishOn(scheduler)
                .map(request -> this.before(request, streamAdvisorChain))
                .flatMapMany(streamAdvisorChain::nextStream)
                .transform(flux -> new ChatClientMessageAggregator().aggregateChatClientResponse(flux,
                        response -> this.after(response, streamAdvisorChain)));
    }


    public static ChatHistoryMemoryAdvisor.Builder builder(ChatMemory chatMemory) {
        return new ChatHistoryMemoryAdvisor.Builder(chatMemory);
    }

    public static final class Builder {

        private String conversationId = ChatMemory.DEFAULT_CONVERSATION_ID;

        private int order = Advisor.DEFAULT_CHAT_MEMORY_PRECEDENCE_ORDER;

        private Scheduler scheduler = BaseAdvisor.DEFAULT_SCHEDULER;

        private ChatMemory chatMemory;

        private Builder(ChatMemory chatMemory) {
            this.chatMemory = chatMemory;
        }

        /**
         * Set the conversation id.
         * @param conversationId the conversation id
         * @return the builder
         */
        public ChatHistoryMemoryAdvisor.Builder conversationId(String conversationId) {
            this.conversationId = conversationId;
            return this;
        }

        /**
         * Set the order.
         * @param order the order
         * @return the builder
         */
        public ChatHistoryMemoryAdvisor.Builder order(int order) {
            this.order = order;
            return this;
        }

        public ChatHistoryMemoryAdvisor.Builder scheduler(Scheduler scheduler) {
            this.scheduler = scheduler;
            return this;
        }

        /**
         * Build the advisor.
         * @return the advisor
         */
        public ChatHistoryMemoryAdvisor build() {
            return new ChatHistoryMemoryAdvisor(this.chatMemory, this.conversationId, this.order, this.scheduler);
        }

    }
}
