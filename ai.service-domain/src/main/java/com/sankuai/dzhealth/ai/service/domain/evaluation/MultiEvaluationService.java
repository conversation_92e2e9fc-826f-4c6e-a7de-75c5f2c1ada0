package com.sankuai.dzhealth.ai.service.domain.evaluation;

import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.dianping.rhino.Rhino;
import com.dianping.rhino.threadpool.DefaultThreadPoolProperties;
import com.dianping.rhino.threadpool.ThreadPool;
import com.sankuai.dzhealth.ai.service.agent.domain.repository.chatSession.ChatSessionMessageRepository;
import com.sankuai.dzhealth.ai.service.agent.domain.service.evaluation.EvaluationDataService;
import com.sankuai.dzhealth.ai.service.agent.infrastructure.dal.entity.ChatSessionMessageDOWithBLOBs;
import com.sankuai.dzhealth.ai.service.agent.infrastructure.dal.entity.EvaluationSessionRecordsDO;
import com.sankuai.dzhealth.ai.service.agent.infrastructure.dal.entity.MessageEvaluationResultEntity;
import com.sankuai.dzhealth.ai.service.agent.infrastructure.dal.repository.evaluation.EvaluationSessionRecordsRepository;
import com.sankuai.dzhealth.ai.service.agent.infrastructure.model.MultiEvaluationRequest;
import com.sankuai.dzhealth.ai.service.agent.infrastructure.model.MultiEvaluationResponse;
import com.sankuai.dzhealth.ai.service.domain.evaluation.entity.EvaluationRequest;
import com.sankuai.dzhealth.ai.service.domain.utils.JsonUtils;
import com.sankuai.dzhealth.ai.service.dto.haima.EvaluationCaseInfo;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR> xiafangyuan
 * @since : 2025/8/13 11:06
 */
@Service
@Slf4j
public class MultiEvaluationService {

    @Autowired
    private EvaluationSessionRecordsRepository evaluationSessionRecordsRepository;

    @Autowired
    private ChatSessionMessageRepository chatSessionMessageRepository;

    @Autowired
    private MultiDialogueEvaluation multiDialogueEvaluation;

    @Autowired
    private EvaluationDataService evaluationDataService;

    // 评测专用线程池
    public static final ThreadPool MULTI_EVALUATION_SERVICE_POOL = Rhino.newThreadPool("MULTI_EVALUATION_SERVICE_POOL",
            DefaultThreadPoolProperties.Setter().withCoreSize(100).withMaxSize(200).withMaxQueueSize(1000));

    /**
     * 重新评估指定评估集的会话
     *
     * @param request 评估请求
     */
    public void reEvaluateByEvaluationId(EvaluationRequest request) {
        Transaction transaction = Cat.newTransaction(this.getClass().getSimpleName(), "reEvaluateByEvaluationId");
        try {
            log.info("开始重新评估，evaluationId: {}, sessionIds: {}, version: {}", request.getEvaluationId(),
                    request.getSessionIds(), request.getVersion());

            List<EvaluationSessionRecordsDO> evaluationSessionRecords = getEvaluationSessionRecords(request);
            if (CollectionUtils.isEmpty(evaluationSessionRecords)) {
                log.warn("evaluationId:{} 未找到对应的会话记录", request.getEvaluationId());
                return;
            }

            log.info("evaluationId:{} 需要评估的会话记录数量: {}", request.getEvaluationId(), evaluationSessionRecords.size());

            // 处理每个会话记录
            for (EvaluationSessionRecordsDO evaluationSessionRecord : evaluationSessionRecords) {
                processEvaluationSessionRecord(request, evaluationSessionRecord);
            }

            transaction.setSuccessStatus();

        } catch (Exception e) {
            log.error("重新评估失败，evaluationId: {}", request.getEvaluationId(), e);
            transaction.setStatus(e);
        } finally {
            transaction.complete();
        }
    }

    /**
     * 获取评估会话记录
     */
    private List<EvaluationSessionRecordsDO> getEvaluationSessionRecords(EvaluationRequest request) {
        List<EvaluationSessionRecordsDO> evaluationSessionRecords;
        if (CollectionUtils.isEmpty(request.getSessionIds())) {
            // 如果sessionIds为空，查询该evaluationId的所有会话记录
            evaluationSessionRecords = evaluationSessionRecordsRepository
                    .findByEvaluationId(String.valueOf(request.getEvaluationId()));
        } else {
            // 如果指定了sessionIds，根据evaluationId和sessionIds查询
            evaluationSessionRecords = evaluationSessionRecordsRepository.findByEvaluationIdAndSessionIds(
                    String.valueOf(request.getEvaluationId()), request.getSessionIds());
        }

        return evaluationSessionRecords;
    }

    /**
     * 处理单个评估会话记录
     */
    private void processEvaluationSessionRecord(EvaluationRequest request, EvaluationSessionRecordsDO evaluationSessionRecord) {
        String sessionId = evaluationSessionRecord.getSessionId();
        log.info("处理evaluationSessionRecord: evaluationId={}, sessionId={}",
                evaluationSessionRecord.getEvaluationId(), sessionId);

        try {
            List<ChatSessionMessageDOWithBLOBs> chatMessages = getChatMessagesBySessionId(sessionId);
            if (CollectionUtils.isEmpty(chatMessages)) {
                log.warn("evaluationId:{}, sessionId: {} 没有找到对话消息",
                        evaluationSessionRecord.getEvaluationId(), sessionId);
                return;
            }

            // 处理所有消息的单轮评测和多轮评测
            processSessionMessages(request, evaluationSessionRecord, chatMessages);

            log.info("sessionId: {} 评估完成", sessionId);

        } catch (Exception e) {
            log.error("评估evaluationId:{}, sessionId: {} 时发生异常",
                    evaluationSessionRecord.getEvaluationId(), sessionId, e);
        }
    }

    /**
     * 处理会话中的所有消息
     */
    private void processSessionMessages(EvaluationRequest request, EvaluationSessionRecordsDO evaluationSessionRecord,
                                      List<ChatSessionMessageDOWithBLOBs> chatMessages) {
        List<MessageEvaluationResultEntity> allMessageResults = new ArrayList<>();
        MultiEvaluationRequest lastMultiDialogueRequest = null;

        // 处理每个消息的单轮评测
        for (int i = 0; i < chatMessages.size(); i++) {
            ChatSessionMessageDOWithBLOBs chatMessage = chatMessages.get(i);
            boolean isLastMessage = (i == chatMessages.size() - 1);

            // 处理单轮评测
            List<MessageEvaluationResultEntity> messageResults = processSingleDialogueEvaluation(
                    request, evaluationSessionRecord, chatMessage);
            allMessageResults.addAll(messageResults);

            // 如果是最后一个message，保存多轮对话评测请求
            if (isLastMessage) {
                lastMultiDialogueRequest = extractMultiDialogueEvaluationRequest(chatMessage);
            }

            log.info("evaluationId:{} sessionId: {} messageId: {} 单轮评测完成，处理结果数量: {}",
                    evaluationSessionRecord.getEvaluationId(), evaluationSessionRecord.getSessionId(),
                    chatMessage.getMessageId(), messageResults.size());
        }

        // 执行多轮对话评测
        executeMultiDialogueEvaluation(request, evaluationSessionRecord, lastMultiDialogueRequest);
    }

    /**
     * 处理单个消息的单轮对话评测
     */
    private List<MessageEvaluationResultEntity> processSingleDialogueEvaluation(EvaluationRequest request,
                                                                               EvaluationSessionRecordsDO evaluationSessionRecord,
                                                                               ChatSessionMessageDOWithBLOBs chatMessage) {
        List<MultiEvaluationRequest> multiEvaluationRequests = extractMultiEvaluationRequests(chatMessage);
        List<CompletableFuture<MultiEvaluationResponse>> singleDialogueFutures = new ArrayList<>();

        // 创建异步任务
        for (MultiEvaluationRequest multiEvaluationRequest : multiEvaluationRequests) {
            CompletableFuture<MultiEvaluationResponse> future = createSingleDialogueEvaluationTask(
                    request, evaluationSessionRecord, multiEvaluationRequest);
            singleDialogueFutures.add(future);
        }

        // 等待所有任务完成并收集结果
        return collectSingleDialogueResults(evaluationSessionRecord, chatMessage, singleDialogueFutures);
    }

    /**
     * 创建单轮对话评测任务
     */
    private CompletableFuture<MultiEvaluationResponse> createSingleDialogueEvaluationTask(EvaluationRequest request,
                                                                                         EvaluationSessionRecordsDO evaluationSessionRecord,
                                                                                         MultiEvaluationRequest multiEvaluationRequest) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                // 设置evaluationId和version到请求中
                multiEvaluationRequest.setEvaluationId(request.getEvaluationId());
                multiEvaluationRequest.setEvaluationVer(request.getVersion());

                return multiDialogueEvaluation.executeSingleDialogueEvaluation(multiEvaluationRequest);
            } catch (Exception e) {
                log.error("单轮对话评测失败，evaluationId:{}, sessionId: {}, messageId: {}, bizScene: {}, modelScene:{}",
                        evaluationSessionRecord.getEvaluationId(), multiEvaluationRequest.getSessionId(),
                        multiEvaluationRequest.getMessageId(), multiEvaluationRequest.getBizScene(),
                        multiEvaluationRequest.getModelScene(), e);

                MultiEvaluationResponse errorResponse = new MultiEvaluationResponse();
                errorResponse.setSuccess(false);
                return errorResponse;
            }
        }, MULTI_EVALUATION_SERVICE_POOL.getExecutor());
    }

    /**
     * 收集单轮对话评测结果
     */
    private List<MessageEvaluationResultEntity> collectSingleDialogueResults(EvaluationSessionRecordsDO evaluationSessionRecord,
                                                                            ChatSessionMessageDOWithBLOBs chatMessage,
                                                                            List<CompletableFuture<MultiEvaluationResponse>> futures) {
        List<MessageEvaluationResultEntity> results = new ArrayList<>();

        try {
            CompletableFuture<Void> allFutures = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));
            allFutures.get(); // 等待所有单轮评测完成

            // 收集结果
            for (CompletableFuture<MultiEvaluationResponse> future : futures) {
                MultiEvaluationResponse response = future.get();
                if (response.getSuccess() && CollectionUtils.isNotEmpty(response.getMessageResults())) {
                    results.addAll(response.getMessageResults());
                }
            }

        } catch (Exception e) {
            log.error("等待评测任务完成时发生异常，evaluationId:{}, sessionId: {}, messageId: {}",
                    evaluationSessionRecord.getEvaluationId(), evaluationSessionRecord.getSessionId(),
                    chatMessage.getMessageId(), e);
        }

        return results;
    }

    /**
     * 执行多轮对话评测
     */
    private void executeMultiDialogueEvaluation(EvaluationRequest request, EvaluationSessionRecordsDO evaluationSessionRecord,
                                              MultiEvaluationRequest lastMultiDialogueRequest) {
        if (lastMultiDialogueRequest == null) {
            return;
        }

        try {
            log.info("开始执行多轮对话评测，evaluationId: {}, sessionId: {}, caseId: {}",
                    evaluationSessionRecord.getEvaluationId(), evaluationSessionRecord.getSessionId(),
                    evaluationSessionRecord.getCaseId());

            // 设置evaluationId和version到请求中
            lastMultiDialogueRequest.setEvaluationId(request.getEvaluationId());
            lastMultiDialogueRequest.setEvaluationVer(request.getVersion());

            // 通过caseId获取标准答案
            String standardAnswer = getStandardAnswerByCaseId(evaluationSessionRecord.getCaseId());
            if (standardAnswer != null) {
                lastMultiDialogueRequest.setReferenceAnswer(standardAnswer);
                log.info("成功获取标准答案，caseId: {}, standardAnswer长度: {}",
                        evaluationSessionRecord.getCaseId(), standardAnswer.length());
            } else {
                log.warn("未找到标准答案，caseId: {}", evaluationSessionRecord.getCaseId());
            }

            MultiEvaluationResponse multiResponse = multiDialogueEvaluation.executeMultiDialogueEvaluation(lastMultiDialogueRequest);
            if (multiResponse.getSuccess() && CollectionUtils.isNotEmpty(multiResponse.getSessionResults())) {
                log.info("evaluationId: {}, sessionId: {} 多轮评测完成，处理结果数量: {}",
                        evaluationSessionRecord.getEvaluationId(), evaluationSessionRecord.getSessionId(),
                        multiResponse.getSessionResults().size());
            }
        } catch (Exception e) {
            log.error("多轮对话评测失败，evaluationId: {}, sessionId: {}",
                    evaluationSessionRecord.getEvaluationId(), evaluationSessionRecord.getSessionId(), e);
        }
    }

    /**
     * 通过caseId获取标准答案
     */
    private String getStandardAnswerByCaseId(String caseId) {

        try {
            Map<String, EvaluationCaseInfo> evaluationDataMap =
                    evaluationDataService.getEvaluationCaseInfoMap();

            EvaluationCaseInfo evaluationData = evaluationDataMap.get(caseId);
            if (evaluationData != null) {
                return evaluationData.getAnswer();
            }

            log.warn("未找到caseId对应的评测数据，caseId: {}", caseId);
            return null;
        } catch (Exception e) {
            log.error("获取标准答案失败，caseId: {}", caseId, e);
            return null;
        }
    }

    /**
     * 根据sessionId查询所有对话消息
     */
    private List<ChatSessionMessageDOWithBLOBs> getChatMessagesBySessionId(String sessionId) {
        return chatSessionMessageRepository.findAllMessagesBySessionId(sessionId);
    }
    
    @SneakyThrows
    public <T> T extractField(String extra, String key, Class<T> clazz) {
        if (StringUtils.isBlank(extra)) {
            return null;
        }
        Map<String, Object> extraMap = JsonUtils.parseMap(extra);
        if (extraMap == null || !extraMap.containsKey(key)) {
            return null;
        }
        return JsonUtils.parseObject(JsonUtils.toJsonString(extraMap.get(key)), clazz);
    }

    /**
     * 从ChatSessionMessageDOWithBLOBs的extra字段中提取MultiEvaluationRequest列表
     */
    private List<MultiEvaluationRequest> extractMultiEvaluationRequests(ChatSessionMessageDOWithBLOBs chatMessage) {
        try {
            if (StringUtils.isBlank(chatMessage.getExtra())) {
                return Collections.emptyList();
            }

            // 解析extra字段
            Map<String, Object> extraMap = JsonUtils.parseMap(chatMessage.getExtra());
            if (extraMap == null || !extraMap.containsKey("multiEvaluationRequests")) {
                return Collections.emptyList();
            }

            // 获取multiEvaluationRequests
            Object multiEvaluationRequestsObj = extraMap.get("multiEvaluationRequests");
            if (multiEvaluationRequestsObj instanceof List<?> requestList) {
                List<MultiEvaluationRequest> result = new ArrayList<>();
                for (Object requestObj : requestList) {
                    try {
                        MultiEvaluationRequest request = JsonUtils.parseObject(JsonUtils.toJsonString(requestObj),
                                MultiEvaluationRequest.class);
                        if (request != null) {
                            result.add(request);
                        }
                    } catch (Exception e) {
                        log.warn("解析单个MultiEvaluationRequest失败，messageId: {}", chatMessage.getMessageId(), e);
                    }
                }
                return result;
            }

            return Collections.emptyList();
        } catch (Exception e) {
            log.error("解析MultiEvaluationRequest列表失败，messageId: {}", chatMessage.getMessageId(), e);
            return Collections.emptyList();
        }
    }

    /**
     * 从ChatSessionMessageDOWithBLOBs的extra字段中提取多轮对话评测请求
     */
    private MultiEvaluationRequest extractMultiDialogueEvaluationRequest(ChatSessionMessageDOWithBLOBs chatMessage) {
        try {
            if (StringUtils.isBlank(chatMessage.getExtra())) {
                return null;
            }

            // 解析extra字段
            Map<String, Object> extraMap = JsonUtils.parseMap(chatMessage.getExtra());
            if (extraMap == null || !extraMap.containsKey("multiEvaluationRequest")) {
                return null;
            }

            // 获取multiEvaluationRequest
            Object multiEvaluationRequestObj = extraMap.get("multiEvaluationRequest");
            return JsonUtils.parseObject(JsonUtils.toJsonString(multiEvaluationRequestObj), MultiEvaluationRequest.class);
        } catch (Exception e) {
            log.error("解析MultiEvaluationRequest失败，messageId: {}", chatMessage.getMessageId(), e);
            return null;
        }
    }

}
