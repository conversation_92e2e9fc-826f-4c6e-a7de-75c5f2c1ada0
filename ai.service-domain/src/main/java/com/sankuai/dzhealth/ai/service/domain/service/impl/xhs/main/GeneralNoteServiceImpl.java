package com.sankuai.dzhealth.ai.service.domain.service.impl.xhs.main;

import com.sankuai.dzhealth.ai.service.domain.service.XhsNoteAbstractService;
import com.sankuai.dzhealth.ai.service.domain.service.impl.xhs.JoyLifeNoteService;
import com.sankuai.dzhealth.ai.service.domain.service.impl.xhs.MedicalNoteService;
import com.sankuai.dzhealth.ai.service.enums.xhs.BizTypeEnum;
import com.sankuai.dzhealth.ai.service.request.xhs.XhsBuildNoteRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class GeneralNoteServiceImpl extends XhsNoteAbstractService<XhsBuildNoteRequest> {

    @Autowired
    private MedicalNoteService medicalNoteService;

    @Autowired
    private JoyLifeNoteService joyLifeNoteService;

    @Override
    protected String service(XhsBuildNoteRequest request) throws Exception {
        BizTypeEnum bizType = BizTypeEnum.getByBizCode(request.getBizCode());

        try {
            if (bizType == BizTypeEnum.MEDICAL) {
                medicalNoteService.processMedicalNote(request);
            } else if (bizType == BizTypeEnum.JOY_LIFE) {
                joyLifeNoteService.processJoyLifeNote(request);
            } else {
                throw new IllegalArgumentException("Unsupported BizCode: " + request.getBizCode());
            }
        } catch (Exception e) {
            log.error("任务失败:{}", e.getMessage(), e);
            return e.getMessage();
        }

        return "任务已提交异步处理";
    }
}
