package com.sankuai.dzhealth.ai.service.agent.domain.process.card;

import com.dianping.cat.Cat;
import com.sankuai.dzhealth.ai.service.agent.domain.context.MessageContext;
import com.sankuai.dzhealth.ai.service.agent.enums.ContextExtraKey;
import com.sankuai.dzhealth.ai.service.domain.utils.JsonUtils;
import com.sankuai.dzhealth.ai.service.dto.stream.StreamEventCardDataDTO;
import com.sankuai.dzhealth.ai.service.enums.StreamEventCardTypeEnum;
import com.sankuai.medicalcosmetology.mainpath.listingapi.dto.FillInfoDTO;
import com.sankuai.medicalcosmetology.mainpath.listingapi.dto.GoodsInfoDTO;
import com.sankuai.medicalcosmetology.mainpath.listingapi.dto.ShopInfoDTO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.io.Serializable;
import java.util.*;

/**
 * @author:chenwei
 * @time: 2025/7/9 15:52
 * @version: 0.0.1
 */
@Service
public class ShopCardBuilder implements CardBuilder {
    private static final Logger log = LoggerFactory.getLogger(ShopCardBuilder.class);

    @Override
    public boolean accept(String cardType) {
        return StreamEventCardTypeEnum.SHOP_PRODUCT_CARD.getType().equals(cardType);
    }

    @Override
    public void padding(StreamEventCardDataDTO streamEventCardDataDTO, MessageContext messageContext) {


        String key = streamEventCardDataDTO.getKey();
        Map<String, Serializable> extra = messageContext.getExtra();
        
        // 确保extra不为null
        if (extra == null) {
            extra = new HashMap<>();
            messageContext.setExtra(extra);
        }
        
        // 获取现有的supply key列表
        Map<String, Object> supplyKeyMap = getOrCreateSupplyKeyMap(extra);

        // 添加新的key
        supplyKeyMap.put(key, StreamEventCardTypeEnum.SHOP_PRODUCT_CARD.getType());

        log.info("shopKey={},map={}", key, JsonUtils.toJsonString(supplyKeyMap));
        // 更新到extra中
        extra.put(ContextExtraKey.SUPPLY_KEY_CARD_MAP.getKey(), JsonUtils.toJsonString(supplyKeyMap));
        log.info("shopKey={},map={}", key, JsonUtils.toJsonString(extra));
        Cat.logEvent("extraPutTime", String.valueOf(System.currentTimeMillis()));
    }

    @Override
    public String parseCardProps(StreamEventCardDataDTO streamEventCardDataDTO) {
        Map<String, Object> cardProps = streamEventCardDataDTO.getCardProps();
        StringBuilder result = new StringBuilder();

        FillInfoDTO detail = JsonUtils.parseObject(JsonUtils.toJsonString(cardProps.get("detail")), FillInfoDTO.class);

        if (detail != null && CollectionUtils.isNotEmpty(detail.getShopInfos())) {
            ShopInfoDTO shopInfoDTO = detail.getShopInfos().get(0);
            result.append("商户名称是:").append(shopInfoDTO.getShopName());
            if (CollectionUtils.isNotEmpty(detail.getGoodsInfoDTOs())) {
                GoodsInfoDTO goodsInfoDTO = detail.getGoodsInfoDTOs().get(0);
                result.append(",推荐商品是:").append(goodsInfoDTO.getGoodsName())
                        .append(",价格是:").append(goodsInfoDTO.getFinalPrice()).append("\n");
            }
        }

        if (Objects.nonNull(cardProps.get("reason"))) {
            result.append("该商户的推荐理由是:").append((String) cardProps.get("reason")).append("\n");
        }
        return result.toString();
    }

    /**
     * 获取或创建supply key列表
     */
    private Map<String, Object> getOrCreateSupplyKeyMap(Map<String, Serializable> extra) {
        String supplyListJson = (String) extra.get(ContextExtraKey.SUPPLY_KEY_CARD_MAP.getKey());
        if (StringUtils.isBlank(supplyListJson)) {
            return new HashMap<>();
        }

        try {
            return new HashMap<>(JsonUtils.parseMap(supplyListJson));
        } catch (Exception e) {
            log.error("解析supply key列表失败，使用新列表: {}", supplyListJson, e);
            return new HashMap<>();
        }
    }


}
