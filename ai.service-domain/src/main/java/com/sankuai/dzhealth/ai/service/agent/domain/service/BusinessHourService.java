package com.sankuai.dzhealth.ai.service.agent.domain.service;

import com.dianping.poi.bizhour.BizHourForecastService;
import com.dianping.poi.bizhour.dto.BizForecastDTO;
import com.dianping.poi.bizhour.enums.SourceEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;


@Slf4j
@Service
public class BusinessHourService {


    @Autowired
    private BizHourForecastService bizHourForecastService;

    public boolean isBusinessHourByPoint(Long shopId, Date dateString) {
        try {
            if (shopId == null || dateString == null) {
                throw new IllegalArgumentException("<isBusinessHour> 入参缺失");
            }

            // 将Date转换为yyyy-MM-dd HH:mm:ss格式的字符串
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            LocalDateTime dateTime = dateString.toInstant()
                    .atZone(ZoneId.systemDefault())
                    .toLocalDateTime();
            String formattedDateString = dateTime.format(formatter);

            BizForecastDTO bizForecast = bizHourForecastService.getBizForecast(shopId, SourceEnum.MEITUAN, formattedDateString);
            if (bizForecast == null || !bizForecast.isHasBiz() || !bizForecast.isOpenStatus()) {
                return false;
            }

            // 获取小时和分钟
            int hour = dateTime.getHour();
            int minute = dateTime.getMinute();
            // 计算对应的时间段索引（每30分钟一个时间段）
            int timeSlotIndex = hour * 2 + (minute >= 30 ? 1 : 0);
            char businessStatus = bizForecast.getToday().charAt(timeSlotIndex);
            return businessStatus == '1';
        }catch (Exception e){
            log.error("isBusinessHourByPoint error, shopId:{}, dateString:{}", shopId, dateString, e);
            return false;
        }
    }

    public boolean isBusinessHourByRange(Long shopId, Date startDateString, Date endDateString) {
        try {
            if (shopId == null || startDateString == null || endDateString == null) {
                throw new IllegalArgumentException("<isBusinessHour> 入参缺失");
            }
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

            // Date转LocalDateTime再转字符串
            LocalDateTime startDateTime = startDateString.toInstant()
                    .atZone(ZoneId.systemDefault())
                    .toLocalDateTime();
            LocalDateTime endDateTime = endDateString.toInstant()
                    .atZone(ZoneId.systemDefault())
                    .toLocalDateTime();

            String startDateStr = startDateTime.format(formatter);

            BizForecastDTO bizForecast = bizHourForecastService.getBizForecast(shopId, SourceEnum.MEITUAN, startDateStr);

            // 计算时间段索引，
            // 营业时间是48位字符串，将一天24小时以半小时为单位划分为48个，其中第一个字符表示凌晨0点到凌晨0点30分（不包含），0-休息1-营业
            // 下面两行代码的作用就是将传入参数的起止时间，转换为营业时间的区间，比如早上九点半，对应的就是9*2+1=19 区间（半小时为单位，因此9*2，30分属于下一个区间，因此+1）
            int startTimeSlotIndex = startDateTime.getHour() * 2 + (startDateTime.getMinute() >= 30 ? 1 : 0);
            int endTimeSlotIndex = endDateTime.getHour() * 2 + (endDateTime.getMinute() >= 30 ? 1 : 0);

            if (bizForecast == null || !bizForecast.isHasBiz() || !bizForecast.isOpenStatus()) {
                return false;
            }
            // 检查整个时间段是否都在营业时间内
            String todaySchedule = bizForecast.getToday();
            for (int i = startTimeSlotIndex; i <= endTimeSlotIndex; i++) {//
                if (todaySchedule.charAt(i) == '1') {// 只要传入的起止时间在任意营业时间就返回真
                    return true;
                }
            }

            return false;
        }catch (Exception e){
            log.error("isBusinessHourByRange error, shopId:{}, startDateString:{}, endDateString:{}", shopId, startDateString, endDateString, e);
            return false;
        }
    }



}