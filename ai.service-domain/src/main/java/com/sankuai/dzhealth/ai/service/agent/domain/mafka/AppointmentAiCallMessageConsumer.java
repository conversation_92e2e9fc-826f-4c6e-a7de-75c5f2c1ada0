package com.sankuai.dzhealth.ai.service.agent.domain.mafka;


import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.dianping.lion.client.util.JsonUtils;
import com.meituan.mafka.client.MafkaClient;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mafka.client.consumer.ConsumerConstants;
import com.meituan.mafka.client.consumer.IConsumerProcessor;
import com.sankuai.dzhealth.ai.service.agent.domain.service.AppointmentService;
import com.sankuai.dzhealth.ai.service.infrastructure.mafka.MafkaShutDownHelper;
import com.sankuai.dzim.pilot.api.data.aiphonecall.AIPhoneCallBackDTO;
import com.sankuai.dzim.pilot.api.enums.aiphonecall.AIPhoneCallSceneTypeEnum;
import com.sankuai.dzim.pilot.api.enums.aiphonecall.AIPhoneCallSourceEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;
import java.util.Properties;

@Slf4j
@Component
public class AppointmentAiCallMessageConsumer  implements InitializingBean {



    @Autowired
    private AppointmentService appointmentService;

    private static final String CAT_TYPE = AppointmentAiCallMessageConsumer.class.getSimpleName();

    @Override
    public void afterPropertiesSet() throws Exception {
        Properties properties = new Properties();
        properties.setProperty(ConsumerConstants.MafkaBGNamespace, "daozong");
        properties.setProperty(ConsumerConstants.MafkaClientAppkey, "com.sankuai.dzhealth.ai.service");
        properties.setProperty(ConsumerConstants.SubscribeGroup, "medical.ai.appointment.call.back");
        properties.setProperty(ConsumerConstants.MafkaDelayRetryCount, "3");
        IConsumerProcessor processor = MafkaClient.buildConsumerFactory(properties, "pilot.ai.phone.call.back.medical.ai");

        MafkaShutDownHelper.registerHook(processor);
        processor.recvMessageWithParallel(String.class, (message, context) -> {
            Transaction transaction = Cat.newTransaction(CAT_TYPE, "consume");
            String messageStr=String.valueOf(message.getBody());
            try{
                log.info("[AppointmentAiCallMessageConsumer] 处理延迟消息: message:{}", messageStr);
                if (StringUtils.isBlank(messageStr)) {
                    log.info("[AppointmentAiCallMessageConsumer] 处理延迟消息: messageStr is null");
                    return ConsumeStatus.CONSUME_SUCCESS;
                }

                AIPhoneCallBackDTO aiCallMessage = JsonUtils.fromJson(messageStr, AIPhoneCallBackDTO.class);
                if (Objects.isNull(aiCallMessage)) {
                    log.info("[AppointmentAiCallMessageConsumer] 处理延迟消息: AIPhoneCallBackDTO is null");
                    return ConsumeStatus.CONSUME_SUCCESS;
                }
                if(aiCallMessage.getSceneType()==AIPhoneCallSceneTypeEnum.RESERVATION.getType()&&aiCallMessage.getSource()== AIPhoneCallSourceEnum.MEDICAL_AI_CALL.getSource())
                {
                    log.info("[AppointmentAiCallMessageConsumer] 处理延迟消息: aiCallMessage:{}",messageStr);
                    appointmentService.listenToAIAppointmentSignal(aiCallMessage);
                }
                return ConsumeStatus.CONSUME_SUCCESS;

            }catch (Exception e){
                Cat.logEvent("AppointmentAiCallMessageConsumer", "handle.exception", "1", e.getMessage());
                log.error("AppointmentAiCallMessageConsumer handle exception for message:{}, e:", messageStr, e);
                transaction.setStatus(e);
                return ConsumeStatus.RECONSUME_LATER;
            }finally {
                transaction.complete();
            }
        });

    }


}
