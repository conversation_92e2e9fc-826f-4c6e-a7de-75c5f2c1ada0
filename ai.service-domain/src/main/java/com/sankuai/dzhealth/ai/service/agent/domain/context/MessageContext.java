package com.sankuai.dzhealth.ai.service.agent.domain.context;

import com.sankuai.dzhealth.ai.service.agent.domain.model.MemoryModel;
import com.sankuai.dzhealth.ai.service.agent.infrastructure.model.MultiEvaluationRequest;
import com.sankuai.dzhealth.ai.service.request.common.BasicParam;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * @author:chenwei
 * @time: 2025/7/7 10:13
 * @version: 0.0.1
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class MessageContext implements Serializable {

    private Long userId;

    private Integer platform;

    private String sessionId;

    private String msg;

    private String msgId;

    private String msgType;

    private String replyMsgId;

    private List<String> imgUrls;

    // 根据bizType 配置 AgentTaskConfig
    private String bizType;

    private AgentTaskConfig agentTaskConfig;

    // 基本环境参数
    private BasicParam basicParam;

    private String querySource;

    // 业务参数
    private Map<String, Serializable> extra;

    // 记忆模块
    private MemoryModel memoryModel;

    /**
     * 一次对话过程中，可能调用多次大模型用于不同目的，同时添加chatMemory作为请求大模型的上下文
     * <p></p>
     * 多次调用大模型就有多次评测需求
     */
    private List<MultiEvaluationRequest> multiEvaluationRequests;

    /**
     * 对当前对话为止的所有对话内容（即用户直接看到的内容）进行评测
     */
    private MultiEvaluationRequest multiEvaluationRequest;

    // 上下文摘要
    private String digest;

    private String thinkText;

    /**
     * 一次请求中调用搜索接口的次数
     */
    private AtomicInteger searchRequestCount = new AtomicInteger(0);

    /**
     * 一次请求中调用搜索接口成功的次数
     */
    private AtomicInteger searchRequestSuccessCount = new AtomicInteger(0);
}
