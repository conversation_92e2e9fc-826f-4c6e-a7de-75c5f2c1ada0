package com.sankuai.dzhealth.ai.service.domain.prompt;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.google.common.collect.ImmutableMap;
import lombok.Builder;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR> youtianyu
 * @version : 0.1
 * @since : 2025/3/27
 */
@Data
@Builder
public class IntentionPromptConfig {
    private String project;

    private String projectName;

    private List<IntentionPrompt> list;

    @Builder
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class IntentionPrompt {
        private String key;

        private String name;

        private String classify;

        private String rewrite;

        private String prompt;

        private String functionCallJson;
    }

    public String toIntentionPrompt() {
        return CollectionUtils.isEmpty(list) ?
                "[]" :
                JSON.toJSONString(CollectionUtils.isEmpty(list) ?
                        StringUtils.EMPTY :
                        list.stream()
                                .map(intentionPrompt -> ImmutableMap.of("意图标识", intentionPrompt.getKey(),
                                        "意图名称", intentionPrompt.getName(), "意图判别标准",
                                        intentionPrompt.getClassify(), "改写规则", intentionPrompt.getRewrite()))
                                .collect(Collectors.toList()));
    }

    public String toAnswerSystemPrompt(String key) {
        return CollectionUtils.isEmpty(list) ?
                StringUtils.EMPTY :
                list.stream().filter(intentionPrompt -> Objects.equals(key, intentionPrompt.getKey()))
                        .findFirst()
                        .map(intentionPrompt -> String.format("当前用户意图是：** %s **\n%s", intentionPrompt.getName(),
                                intentionPrompt.getPrompt()))
                        .orElse(StringUtils.EMPTY);
    }

    public String toAnswerUserPrompt(String key) {
        return CollectionUtils.isEmpty(list) ?
                StringUtils.EMPTY :
                list.stream().filter(intentionPrompt -> Objects.equals(key, intentionPrompt.getKey()))
                        .findFirst()
                        .map(IntentionPrompt::getName)
                        .orElse(StringUtils.EMPTY);
    }
}
