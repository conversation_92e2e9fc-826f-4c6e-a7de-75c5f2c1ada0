package com.sankuai.dzhealth.ai.service.agent.domain.utils;


import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.Map;


@Service
public class HttpUtils {
    private RestTemplate restTemplate = new RestTemplate();

    /**
     * 简单的GET请求示例
     *
     * @param url 请求URL
     * @return 响应字符串
     */
    public String simpleGet(String url) {
        return restTemplate.getForObject(url, String.class);
    }

    /**
     * 带请求头的GET请求示例
     *
     * @param url     请求URL
     * @param headers 请求头
     * @return 响应实体
     */
    public ResponseEntity<String> getWithHeaders(String url, Map<String, String> headers) {
        HttpHeaders httpHeaders = new HttpHeaders();
        if (headers != null) {
            headers.forEach(httpHeaders::add);
        }
        HttpEntity<String> entity = new HttpEntity<>(httpHeaders);
        return restTemplate.exchange(url, HttpMethod.GET, entity, String.class);
    }
}
