package com.sankuai.dzhealth.ai.service.domain.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

@Getter
public enum CardStatusEnum {


    //1=初始化, 2=外呼中, 3=成功, 4=失败
    INITIALIZATION(1,"任务初始化"),
    IN_PROGRESS(2,"任务外呼中"),
    SUCCESS(3, "任务成功"),
    FAILED(4, "任务已失败"),
    USER_CANCELED(5,"用户取消");




    private final int code;
    private final String desc;

    CardStatusEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String getDesc(Integer code) {
        return Arrays.stream(values()).filter(CardStatusEnum -> Objects.equals(CardStatusEnum.code,code)).findFirst().map(CardStatusEnum::getDesc).orElse(null);
    }

    public static Integer getCode(String desc){
        return Arrays.stream(values()).filter(CardStatusEnum -> Objects.equals(CardStatusEnum.desc, desc)).findFirst().map(CardStatusEnum::getCode).orElse(null);
    }
}
