package com.sankuai.dzhealth.ai.service.agent.domain.process.card.data;

import com.sankuai.dzhealth.ai.service.agent.domain.model.SupplyRecommendModel;
import com.sankuai.medicalcosmetology.display.dto.DoctorCardInfoAndCase;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @author:chenwei
 * @time: 2025/7/22 12:58
 * @version: 0.0.1
 */

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DoctorProductCardData {

    private DoctorCardInfoAndCase detail;

    private String reason;

    // 推荐理由前缀
    private String reasonPrefix;

    private List<SupplyRecommendModel> filterList;

    private CaseCardData report;

    private DoctorProductSimpleData product;

    private Boolean needMore;

    private List<ReferData> refer;

    private Long count;

    private Integer index;

    private Integer cityId;

    private Double lat;

    private Double lng;

    private Boolean isCurrentLocation;


    public Map<String, Object> toMap() {
        Map<String, Object> map = new HashMap<>();
        map.put("detail", detail);
        map.put("reason", reason);
        map.put("reasonPrefix", reasonPrefix);
        map.put("filterList", filterList);
        map.put("report", report);
        map.put("product", product);
        map.put("needMore", needMore);
        map.put("refer", refer);
        map.put("count", count);
        map.put("index", index);
        map.put("cityId", cityId);
        map.put("lat", lat);
        map.put("lng", lng);
        map.put("isCurrentLocation", isCurrentLocation);
        return map;
    }





}
