package com.sankuai.dzhealth.ai.service.domain.service.strategy;

import com.alibaba.fastjson.JSON;
import com.dianping.rhino.threadpool.ThreadPool;
import com.sankuai.dzhealth.ai.service.domain.chat.model.IntentionResult;
import com.sankuai.dzhealth.ai.service.domain.service.ChatSessionService;
import com.sankuai.dzhealth.ai.service.domain.tools.STDDepartmentService;
import com.sankuai.dzhealth.ai.service.domain.utils.context.AiAnswerContext;
import com.sankuai.dzhealth.ai.service.infrastructure.observability.Span;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * @author:chenwei
 * @time: 2025/4/2 15:56
 * @version: 0.0.1
 */
@Component
public class StdDepartmentStrategy implements DataSourceStrategy<StdDepartmentStrategy.StdDepartStrategyInfo>{


    @Autowired
    private STDDepartmentService stdDepartmentService;

    private static final ThreadPool taskPool = ChatSessionService.TASK_POOL;
    /**
     * 是否应该执行该策略
     *
     * @param intentionResult 意图识别结果
     * @return 是否执行
     */
    @Override
    public boolean shouldExecute(IntentionResult intentionResult) {
        return intentionResult != null && intentionResult.getNearByNeed();
    }

    /**
     * 执行策略
     *
     * @param context     上下文
     * @param rewriteText 改写后的文本
     * @return 策略执行结果
     */
    @Override
    public CompletableFuture<StdDepartStrategyInfo> execute(AiAnswerContext context, String rewriteText) {
        return CompletableFuture.supplyAsync(() -> {
            StdDepartStrategyInfo res = new StdDepartStrategyInfo();
            STDDepartmentService.Departments stdDepartments = stdDepartmentService.queryDepartments();

            if (stdDepartments != null && MapUtils.isNotEmpty(stdDepartments.getDepartments())) {
                res.setStdInfo(stdDepartments.getDepartments());
            }
            res.setSpan(Collections.singletonList(
                    Span.builder().key(getClass().getSimpleName()).value(JSON.toJSONString(stdDepartments)).build()));
            return res;

        }, taskPool.getExecutor());
    }

    @Data
    @EqualsAndHashCode(callSuper = true)
    public static class StdDepartStrategyInfo extends BaseStrategyInfo{

        private Map<String, Integer> stdInfo;

        @Override
        public String toPrompt() {
            if (MapUtils.isNotEmpty(stdInfo)) {
                return JSON.toJSONString(stdInfo.keySet());
            }
            return "[]";
        }
    }
}
