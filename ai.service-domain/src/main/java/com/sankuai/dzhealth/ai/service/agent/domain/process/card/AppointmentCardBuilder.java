package com.sankuai.dzhealth.ai.service.agent.domain.process.card;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.sankuai.dzhealth.ai.service.agent.domain.context.MessageContext;
import com.sankuai.dzhealth.ai.service.agent.domain.process.card.data.AppointmentCardData;
import com.sankuai.dzhealth.ai.service.agent.domain.service.AppointmentService;
import com.sankuai.dzhealth.ai.service.dto.stream.StreamEventCardDataDTO;
import com.sankuai.dzhealth.ai.service.enums.StreamEventCardTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.LinkedHashMap;
import java.util.Map;

@Slf4j
@Service
public class AppointmentCardBuilder implements CardBuilder{

    @Autowired
    private AppointmentService appointmentService;

    @Override
    public boolean accept(String cardType) {
        return StreamEventCardTypeEnum.APPOINTMENT_INFO_CARD.getType().equals(cardType);
    }

    @Override
    public void padding(StreamEventCardDataDTO streamEventCardDataDTO, MessageContext messageContext) {
        log.info("paddingAppointmentCard,streamEventCardDataDTO:{}",JSON.toJSONString(streamEventCardDataDTO));
        Map<String,Object> ans= streamEventCardDataDTO.getCardProps();
        ObjectMapper objectMapper = new ObjectMapper();
        String jsonKey;
        try {
            jsonKey=objectMapper.writeValueAsString(ans);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
        AppointmentCardData cardData = new AppointmentCardData();
        if (!ans.containsKey("status")) {
            cardData = appointmentService.pushAppointment(jsonKey, messageContext);
        } else {
            cardData = JSON.parseObject(jsonKey, AppointmentCardData.class);
        }
        if (cardData == null || !(checkInfoCard(cardData) || checkAppointmentCard(cardData))) {
            return;
        }
        Map<String, Object> cardProps = streamEventCardDataDTO.getCardProps();
        cardProps.clear();
        cardProps.putAll(cardData.toMap());


    }

    @Override
    public  String parseCardProps(StreamEventCardDataDTO streamEventCardDataDTO) {

        Map<String, Object> cardProps = streamEventCardDataDTO.getCardProps();
        StringBuilder result = new StringBuilder("当前预约信息：");

         // 定义字段映射
        Map<String, String> fieldLabels = new LinkedHashMap<>();
        fieldLabels.put("personDesc", "人数");
        fieldLabels.put("productName", "项目");
        fieldLabels.put("phone", "电话");
        fieldLabels.put("time", "时间");
        fieldLabels.put("position", "地址信息");
        fieldLabels.put("remark", "备注");
        // 遍历字段映射，构建结果字符串
        for (Map.Entry<String, String> entry : fieldLabels.entrySet()) {
            String fieldKey = entry.getKey();
            String fieldLabel = entry.getValue();
            Object fieldValue = cardProps.get(fieldKey);

            if (fieldValue != null &&StringUtils.isNotEmpty(fieldValue.toString())&& !fieldValue.toString().trim().isEmpty()) {
                result.append("\n").append(fieldLabel).append("：").append(fieldValue);
            }
        }

        return result.toString();
    }

    private boolean checkInfoCard(AppointmentCardData appointmentCardData) {
         if (appointmentCardData.getStatus() !=1 ) {
            return false;
         }
        return StringUtils.isNotBlank(appointmentCardData.getTitle()) && StringUtils.isNotBlank(appointmentCardData.getSubtitle())
                && StringUtils.isNotBlank(appointmentCardData.getTime()) && StringUtils.isNotBlank(appointmentCardData.getProductName())
                && StringUtils.isNotBlank(appointmentCardData.getPhone()) && StringUtils.isNotBlank(appointmentCardData.getPosition())
                && StringUtils.isNotBlank(appointmentCardData.getPersonDesc());

    }

    private boolean checkAppointmentCard(AppointmentCardData appointmentCardData) {
        if (appointmentCardData.getStatus() !=3) {
            return false;
        }
        return StringUtils.isNotBlank(appointmentCardData.getTitle()) && StringUtils.isNotBlank(appointmentCardData.getSubtitle())
                && StringUtils.isNotBlank(appointmentCardData.getMsg()) && appointmentCardData.getWaitTime()!=null &&appointmentCardData.getCurrentNum()!=null&&appointmentCardData.getTotalNum()!=null;
    }

}
