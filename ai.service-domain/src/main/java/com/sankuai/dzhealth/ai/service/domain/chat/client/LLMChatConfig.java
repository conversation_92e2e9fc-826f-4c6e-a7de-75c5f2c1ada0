package com.sankuai.dzhealth.ai.service.domain.chat.client;

import com.dianping.lion.Environment;
import com.meituan.service.inf.kms.client.Kms;
import com.meituan.service.inf.kms.utils.KmsResultNullException;
import com.sankuai.dzhealth.ai.service.domain.chat.client.advisor.CustomMessageChatMemoryAdvisor;
import com.sankuai.dzhealth.ai.service.domain.memory.DatabaseChatMemory;
import com.sankuai.dzhealth.ai.service.infrastructure.chatmodel.OneApiChatModel;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.openai.OpenAiChatOptions;
import org.springframework.ai.openai.api.OpenAiApi;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

/**
 * @author: duan<PERSON><PERSON>en
 * @date: 2025/3/20
 */
@Component
public class LLMChatConfig {

    @Autowired
    private DatabaseChatMemory databaseChatMemory;

    @Bean
    public ChatClient answerChatClient() throws KmsResultNullException {
        OpenAiApi openAiApi = new OpenAiApi.Builder()
                .completionsPath("/chat/completions")
                .baseUrl("https://aigc.sankuai.com/v1/openai/native")
                .apiKey(Kms.getByName(Environment.getAppName(), "friday.search.appId"))
                .build();
        OpenAiChatOptions openAiChatOptions = OpenAiChatOptions.builder()
                .model("deepseek-v3-friday")
                .temperature(0.0)
                .maxTokens(20000)
                .streamUsage(true)
                .build();
        OneApiChatModel chatModel = OneApiChatModel.builder()
                .openAiApi(openAiApi)
                .defaultOptions(openAiChatOptions)
                .build();

        return ChatClient.builder(chatModel)
                .defaultAdvisors(CustomMessageChatMemoryAdvisor.builder(databaseChatMemory)
                        .chatMemoryRetrieveSize(10) // 设置每次检索的历史消息数量
                        .build()).build();
    }

}
