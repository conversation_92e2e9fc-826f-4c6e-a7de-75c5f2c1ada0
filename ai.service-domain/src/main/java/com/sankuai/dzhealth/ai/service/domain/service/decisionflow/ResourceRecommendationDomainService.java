package com.sankuai.dzhealth.ai.service.domain.service.decisionflow;

import com.sankuai.dzhealth.ai.service.agent.domain.model.decisionflow.ResourceRecommendationBO;
import com.sankuai.dzhealth.ai.service.infrastructure.dal.entity.NodeResourceRelationDO;
import com.sankuai.dzhealth.ai.service.infrastructure.dal.entity.RecommendResourceDO;
import com.sankuai.dzhealth.ai.service.infrastructure.repository.decision.DecisionVectorStoreRepository;
import com.sankuai.dzhealth.ai.service.infrastructure.repository.decision.NodeResourceRelationRepository;
import com.sankuai.dzhealth.ai.service.infrastructure.repository.decision.RecommendResourceRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import com.sankuai.dzhealth.ai.service.infrastructure.dal.entity.DecisionNodeDO;
import com.sankuai.dzhealth.ai.service.infrastructure.repository.decision.DecisionNodeRepository;
import java.util.Collections;

/**
 * 资源推荐领域服务
 */
@Service
@RequiredArgsConstructor
public class ResourceRecommendationDomainService {

    private final NodeResourceRelationRepository relationRepository;
    private final RecommendResourceRepository resourceRepository;
    private final DecisionVectorStoreRepository vectorStoreRepository;
    private final DecisionNodeRepository nodeRepository;

    /**
     * 查询指定节点下的推荐资源
     */
    public List<ResourceRecommendationBO> listTop(String nodeId, int limit) {
        // 先查询节点获取 bizScene
        DecisionNodeDO node = nodeRepository.findByNodeId(nodeId);
        if (node == null) {
            return Collections.emptyList();
        }
        
        // 使用节点的 bizScene 查询资源
        List<NodeResourceRelationDO> relations = relationRepository.listOnlineByBizSceneAndNode(
            node.getBizScene(), nodeId, limit);
        return relations.stream()
                .map(rel -> {
                    RecommendResourceDO res = resourceRepository.findByResourceId(rel.getResourceId());
                    if (res == null) return null;
                    return convert(res);
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /**
     * 查询指定业务场景和节点下的推荐资源
     */
    public List<ResourceRecommendationBO> listTop(String bizScene, String nodeId, int limit) {
        List<NodeResourceRelationDO> relations = relationRepository.listOnlineByBizSceneAndNode(bizScene, nodeId, limit);
        return relations.stream()
                .map(rel -> {
                    RecommendResourceDO res = resourceRepository.findByResourceId(rel.getResourceId());
                    if (res == null) return null;
                    return convert(res);
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    private ResourceRecommendationBO convert(RecommendResourceDO entity) {
        return ResourceRecommendationBO.builder()
                .bizScene(entity.getBizScene())
                .resourceId(entity.getResourceId())
                .resourceType(entity.getResourceType())
                .resourceName(entity.getResourceName())
                .shortDesc(entity.getShortDesc())
                .attributes(entity.getAttributes())
                .tags(entity.getTags())
                .build();
    }

    /**
     * 根据用户关键字在 ES 中召回资源，再查询 DB 返回详情
     */
    public List<ResourceRecommendationBO> searchResources(String bizScene,String keyword,int topK){
        List<String> ids=vectorStoreRepository.searchResourceIds(bizScene,keyword,topK);
        return ids.stream()
                .map(resourceRepository::findByResourceId)
                .filter(Objects::nonNull)
                .map(this::convert)
                .collect(Collectors.toList());
    }

    /**
     * 创建或更新资源
     */
    public boolean createOrUpdateResource(String bizScene, ResourceRecommendationBO resource) {
        RecommendResourceDO existing = resourceRepository.findByResourceId(resource.getResourceId());

        if (existing == null) {
            // 创建新资源
            RecommendResourceDO entity = new RecommendResourceDO();
            entity.setBizScene(bizScene);
            entity.setResourceId(resource.getResourceId());
            entity.setResourceType(resource.getResourceType());
            entity.setResourceName(resource.getResourceName());
            entity.setShortDesc(resource.getShortDesc());
            entity.setAttributes(resource.getAttributes());
            entity.setTags(resource.getTags());
            entity.setStatus("ONLINE");

            int result = resourceRepository.insert(entity);
            if (result > 0) {
                indexResourceToES(bizScene, resource);
                return true;
            }
        } else {
            // 更新现有资源
            existing.setResourceName(resource.getResourceName());
            existing.setShortDesc(resource.getShortDesc());
            existing.setAttributes(resource.getAttributes());
            existing.setTags(resource.getTags());
            existing.setStatus("ONLINE");

            int result = resourceRepository.updateByResourceId(existing);
            if (result > 0) {
                indexResourceToES(bizScene, resource);
                return true;
            }
        }
        return false;
    }

    /**
     * 批量创建或更新资源
     */
    public boolean batchCreateOrUpdateResources(String bizScene, List<ResourceRecommendationBO> resources) {
        if (resources == null || resources.isEmpty()) {
            return true;
        }

        boolean allSuccess = true;
        for (ResourceRecommendationBO resource : resources) {
            if (!createOrUpdateResource(bizScene, resource)) {
                allSuccess = false;
            }
        }
        return allSuccess;
    }

    /**
     * 将资源索引到ES
     */
    private void indexResourceToES(String bizScene, ResourceRecommendationBO resource) {
        try {
            // 构建content：resource_name + short_desc + tags
            StringBuilder contentBuilder = new StringBuilder();
            if (resource.getResourceName() != null) {
                contentBuilder.append(resource.getResourceName()).append(" ");
            }
            if (resource.getShortDesc() != null) {
                contentBuilder.append(resource.getShortDesc()).append(" ");
            }
            if (resource.getTags() != null && !resource.getTags().isEmpty()) {
                contentBuilder.append(resource.getTags());
            }

            // 创建资源索引数据
            DecisionVectorStoreRepository.ResourceIndexData indexData =
                new DecisionVectorStoreRepository.ResourceIndexData(
                    bizScene,
                    resource.getResourceId(),
                    contentBuilder.toString().trim(),
                    resource.getResourceType(),
                    "ONLINE"
                );

            vectorStoreRepository.indexResources(List.of(indexData));
        } catch (Exception e) {
            // 记录日志但不影响主流程
            System.err.println("Failed to index resource to ES: " + resource.getResourceId() + ", error: " + e.getMessage());
        }
    }

    /**
     * 检查资源是否存在
     */
    public boolean resourceExists(String resourceId) {
        return resourceRepository.findByResourceId(resourceId) != null;
    }

    /**
     * 批量检查资源是否存在
     */
    public List<String> getMissingResourceIds(List<String> resourceIds) {
        return resourceIds.stream()
                .filter(id -> !resourceExists(id))
                .collect(Collectors.toList());
    }


} 
