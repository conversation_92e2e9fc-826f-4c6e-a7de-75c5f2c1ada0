package com.sankuai.dzhealth.ai.service.agent.domain.buffer;

import com.google.common.collect.Lists;
import com.sankuai.dzhealth.ai.service.domain.utils.context.RequestContext;
import com.sankuai.dzhealth.ai.service.domain.utils.context.RequestContextConstant;
import com.sankuai.dzhealth.ai.service.dto.stream.BufferMergedDTO;
import com.sankuai.dzhealth.ai.service.dto.stream.StreamEventCardDataDTO;
import com.sankuai.dzhealth.ai.service.dto.stream.StreamEventDTO;
import com.sankuai.dzhealth.ai.service.dto.stream.StreamEventDataDTO;
import com.sankuai.dzhealth.ai.service.enums.BufferItemTypeEnum;
import com.sankuai.dzhealth.ai.service.enums.StreamEventDataTypeEnum;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;

import java.util.*;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.Future;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.stream.Collectors;


@Slf4j
@Data
public class MessageBuffer {

    /**
     * 已写入缓冲池，使用阻塞队列实现
     */
    private final BlockingQueue<MessageBufferEntity> written = new LinkedBlockingQueue<>();

    /**
     * 写入数据转换后的
     */
    private Queue<StreamEventDTO> converted = new ConcurrentLinkedQueue<>();

    /**
     * 消费任务，用于判断任务是否结束
     */
    Future<Boolean> taskFuture;

    /**
     * sse链接是否关闭
     */
    private boolean sseClose = false;


    private volatile int messageType = 1;


    public boolean writeBufferMessageType(int messageType) {
        this.messageType = messageType;
        return true;
    }

    public boolean writeBufferData(List<MessageBufferEntity> bufferEntityListtyList, MessageBuffer buffer) {
        //从当前ThreadLocal 中获取buffer
        if (buffer == null) {
            buffer = RequestContext.getAttribute(RequestContextConstant.MESSAGE_BUFFER);
        }
        if (buffer == null) {
            return false;
        }
        if (CollectionUtils.isEmpty(bufferEntityListtyList)) {
            return true;
        }
        try {
            BlockingQueue<MessageBufferEntity> written = buffer.getWritten();

            for (MessageBufferEntity entity : bufferEntityListtyList) {
                if (entity == null || BufferItemTypeEnum.getByType(entity.getType()) == null) {
                    continue;
                }
                // 使用阻塞队列的 put 方法，此方法是线程安全的
                written.put(entity);
            }
        } catch (InterruptedException e) {
            log.warn("writeBufferData was interrupted", e);
            Thread.currentThread().interrupt();
            return false;
        }
        return true;
    }

    public BufferMergedDTO getBufferMerged() {
        if (taskFuture == null) {
            return BufferMergedDTO.builder().messageType(messageType).streamEventDTOS(Lists.newArrayList()).build();
        }
        //等待消费任务完成
        try {
            taskFuture.get();
        } catch (Exception e) {
            log.error("getBufferMerged was interrupted", e);
        }
        //合并转换后的数据
        if (CollectionUtils.isEmpty(converted)) {
            return BufferMergedDTO.builder().messageType(messageType).streamEventDTOS(Lists.newArrayList()).build();
        }
        //所有转换后的包分组
        Map<String, List<StreamEventDTO>> eventType2DTO = converted.stream().collect(Collectors.groupingBy(eventVO -> eventVO.getData().getEvent()));

        //状态包取最后一个
        List<StreamEventDTO> statusEventDTOs = eventType2DTO.get(StreamEventDataTypeEnum.LOADING_STATUS.getType());
        StreamEventDTO lastStatusDTO = CollectionUtils.isEmpty(statusEventDTOs) ? null :
                statusEventDTOs.stream().max(Comparator.comparingInt(StreamEventDTO::getIndex)).orElse(null);

        //思考包合并
        List<StreamEventDTO> reasonTextEventDTOs = Optional.ofNullable(eventType2DTO.get(StreamEventDataTypeEnum.THINK_PROCESS_STREAM.getType())).orElse(Lists.newArrayList());
        StreamEventDTO mergedReasonText = mergeThinkProcess(reasonTextEventDTOs);

        //正文包合并
        List<StreamEventDTO> mainTextEventDTOs = Optional.ofNullable(eventType2DTO.get(StreamEventDataTypeEnum.MAIN_TEXT.getType())).orElse(Lists.newArrayList());
        StreamEventDTO mergedMainText = mergeMainText(mainTextEventDTOs);

        // 执行合并操作
        List<StreamEventDTO> result = Lists.newArrayList();
        paddingResult(result, reasonTextEventDTOs, mergedReasonText, mainTextEventDTOs, mergedMainText, lastStatusDTO);
        return BufferMergedDTO.builder().messageType(messageType).streamEventDTOS(result).build();
    }

    private void paddingResult(List<StreamEventDTO> result, List<StreamEventDTO> reasonTextEventDTOs, StreamEventDTO mergedReasonText,
                               List<StreamEventDTO> mainTextEventDTOs, StreamEventDTO mergedMainText, StreamEventDTO lastStatusDTO) {
        if (lastStatusDTO != null) {
            result.add(lastStatusDTO);
        }
        if (CollectionUtils.isNotEmpty(reasonTextEventDTOs)) {
            result.add(mergedReasonText);
        }
        if (CollectionUtils.isNotEmpty(mainTextEventDTOs)) {
            result.add(mergedMainText);
        }
    }

    private StreamEventDTO mergeThinkProcess(List<StreamEventDTO> thinkProcessStreamEventDTOs) {
        StreamEventDTO mergedThinkProcess = new StreamEventDTO();
        StreamEventDataDTO mergedThinkProcessData = new StreamEventDataDTO();
        mergedThinkProcess.setType("message");
        mergedThinkProcess.setData(mergedThinkProcessData);
        mergedThinkProcessData.setEvent(StreamEventDataTypeEnum.THINK_PROCESS_STREAM.getType());
        mergedThinkProcessData.setContent(StringUtils.EMPTY);
        mergedThinkProcessData.setCardsData(Lists.newArrayList());
        if (CollectionUtils.isEmpty(thinkProcessStreamEventDTOs)) {
            return mergedThinkProcess;
        }
        StreamEventDTO thinkProcessDTO = thinkProcessStreamEventDTOs.get(thinkProcessStreamEventDTOs.size() - 1);
        if (thinkProcessDTO == null || thinkProcessDTO.getData() == null) {
            return mergedThinkProcess;
        }
        String content = mergedThinkProcessData.getContent();
        mergedThinkProcessData.setContent(StringUtils.isBlank(content) ? thinkProcessDTO.getData().getContent() :
                content + thinkProcessDTO.getData().getContent());
        List<StreamEventCardDataDTO> cardsDataList = mergedThinkProcessData.getCardsData();
        List<StreamEventCardDataDTO> newCardsData = Optional.ofNullable(thinkProcessDTO.getData().getCardsData()).orElse(Lists.newArrayList());
        cardsDataList.addAll(newCardsData);
        
        // 去除重复的type和key组合，保留最后一个
        mergedThinkProcessData.setCardsData(removeDuplicateCards(cardsDataList));
        return mergedThinkProcess;
    }

    private StreamEventDTO mergeMainText(List<StreamEventDTO> mainTextEventDTOs) {
        StreamEventDTO mergedMainText = new StreamEventDTO();
        StreamEventDataDTO mergedMainTextData = new StreamEventDataDTO();
        mergedMainText.setType("message");
        mergedMainText.setData(mergedMainTextData);
        mergedMainTextData.setEvent(StreamEventDataTypeEnum.MAIN_TEXT.getType());
        mergedMainTextData.setContent(StringUtils.EMPTY);
        mergedMainTextData.setCardsData(Lists.newArrayList());
        
        List<StreamEventCardDataDTO> allCardsData = Lists.newArrayList();
        for (StreamEventDTO mainTextEventDTO : mainTextEventDTOs) {
            if (mainTextEventDTO == null || mainTextEventDTO.getData() == null) {
                continue;
            }
            String content = mergedMainTextData.getContent();
            mergedMainTextData.setContent(StringUtils.isBlank(content) ? mainTextEventDTO.getData().getContent() :
                    content + mainTextEventDTO.getData().getContent());
            List<StreamEventCardDataDTO> cardsData = Optional.ofNullable(mainTextEventDTO.getData().getCardsData()).orElse(Lists.newArrayList());
            allCardsData.addAll(cardsData);
        }
        
        // 去除重复的type和key组合，保留最后一个
        List<StreamEventCardDataDTO> uniqueCardsData = removeDuplicateCards(allCardsData);
        mergedMainTextData.setCardsData(uniqueCardsData);
        log.info("originContent={}", mergedMainText.getData().getContent());
        
        // 清理content中的重复卡片标记和无效卡片标记
        String cleanedContent = cleanupContentCardMarkers(mergedMainTextData.getContent(), uniqueCardsData);
        mergedMainTextData.setContent(cleanedContent);
        
        return mergedMainText;
    }

    private List<StreamEventCardDataDTO> removeDuplicateCards(List<StreamEventCardDataDTO> cardsData) {
        if (CollectionUtils.isEmpty(cardsData)) {
            return Lists.newArrayList();
        }
        
        // 使用LinkedHashMap保持插入顺序，key为type+key的组合
        Map<String, StreamEventCardDataDTO> uniqueCards = new LinkedHashMap<>();
        
        for (StreamEventCardDataDTO card : cardsData) {
            if (card == null) {
                continue;
            }
            // 创建唯一标识：type + ":" + key
            String uniqueKey = (card.getType() != null ? card.getType() : "") + ":" + (card.getKey() != null ? card.getKey() : "");
            // 后面的元素会覆盖前面的元素，从而保留最后一个
            uniqueCards.put(uniqueKey, card);
        }
        
        return Lists.newArrayList(uniqueCards.values());
    }

    /**
     * 清理content中的卡片标记
     * 1. 去除重复的卡片标记，保留第一个
     * 2. 移除在cardsData中不存在的卡片标记
     */
    private String cleanupContentCardMarkers(String content, List<StreamEventCardDataDTO> cardsData) {
        if (StringUtils.isBlank(content) || CollectionUtils.isEmpty(cardsData)) {
            return content;
        }
        
        // 构建有效卡片的set，用于快速查找
        Set<String> validCardKeys = new HashSet<>();
        for (StreamEventCardDataDTO card : cardsData) {
            if (card != null && card.getType() != null && card.getKey() != null && MapUtils.isNotEmpty(card.getCardProps())) {
                validCardKeys.add(card.getType() + ":" + card.getKey());
            }
        }
        
        // 正则表达式匹配卡片标记格式：:::}<Type>Key</Type>{:::
        String cardPattern = ":::\\}<(\\w+)>([^<>]+?)</\\1>\\{:::";
        java.util.regex.Pattern pattern = java.util.regex.Pattern.compile(cardPattern);
        java.util.regex.Matcher matcher = pattern.matcher(content);
        
        // 记录已经处理过的卡片标记，用于去重
        Set<String> processedCardMarkers = new HashSet<>();
        StringBuilder result = new StringBuilder();
        int lastEnd = 0;
        
        while (matcher.find()) {
            String fullMatch = matcher.group(0);
            String type = matcher.group(1);
            String key = matcher.group(2);
            String cardKey = type + ":" + key;
            
            // 添加匹配前的内容
            result.append(content.substring(lastEnd, matcher.start()));
            
            // 检查是否是有效卡片且未重复处理
            if (validCardKeys.contains(cardKey) && !processedCardMarkers.contains(fullMatch)) {
                // 保留第一次出现的有效卡片标记
                result.append(fullMatch);
                processedCardMarkers.add(fullMatch);
            }
            // 如果是重复的或无效的卡片标记，则不添加到结果中（即删除）
            
            lastEnd = matcher.end();
        }
        
        // 添加剩余的内容
        result.append(content.substring(lastEnd));
        
        return result.toString();
    }

    public void finishBufferConsume(MessageBuffer buffer) {
        log.info("<time>finish={}", System.currentTimeMillis());
        List<MessageBufferEntity> finished = Lists.newArrayList();
        MessageBufferEntity finishedItem = new MessageBufferEntity();
        finishedItem.setType(BufferItemTypeEnum.FINISH_WRITE.getType());
        finished.add(finishedItem);
        writeBufferData(finished, buffer);
    }


}
