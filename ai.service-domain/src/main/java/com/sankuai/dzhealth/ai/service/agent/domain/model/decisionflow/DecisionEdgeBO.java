package com.sankuai.dzhealth.ai.service.agent.domain.model.decisionflow;

import lombok.Builder;
import lombok.Data;

/**
 * 决策树路径业务对象
 */
@Data
@Builder
public class DecisionEdgeBO {

    /** 业务场景 */
    private String bizScene;

    /** 父节点业务 ID */
    private String parentNodeId;

    /** 子节点业务 ID */
    private String childNodeId;

    /** 边业务 ID（edge_id） */
    private String edgeId;

    /** 边类型 AI / OPTION / DEFAULT 等 */
    private String edgeType;

    /** 边描述（匹配值或按钮文案） */
    private String edgeDesc;

    /** 优先级 */
    private Long sortOrder;

    /** 状态 */
    private String status;
} 