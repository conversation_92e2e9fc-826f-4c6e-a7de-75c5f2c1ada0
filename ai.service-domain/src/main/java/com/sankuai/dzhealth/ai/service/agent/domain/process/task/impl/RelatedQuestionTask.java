package com.sankuai.dzhealth.ai.service.agent.domain.process.task.impl;

import com.sankuai.dzhealth.ai.service.agent.domain.buffer.MessageBufferEntity;
import com.sankuai.dzhealth.ai.service.agent.domain.config.IntentTypeConfig;
import com.sankuai.dzhealth.ai.service.agent.domain.context.TaskContext;
import com.sankuai.dzhealth.ai.service.agent.domain.enums.TaskTypeEnum;
import com.sankuai.dzhealth.ai.service.agent.domain.model.TaskProcessResult;
import com.sankuai.dzhealth.ai.service.agent.domain.process.task.GeneralTask;
import com.sankuai.dzhealth.ai.service.agent.domain.process.task.Task;
import com.sankuai.dzhealth.ai.service.agent.domain.utils.BufferUtils;
import com.sankuai.dzhealth.ai.service.domain.utils.JsonUtils;
import com.sankuai.dzhealth.ai.service.domain.utils.context.RequestContext;
import com.sankuai.dzhealth.ai.service.domain.utils.context.RequestContextConstant;
import com.sankuai.dzhealth.ai.service.enums.BufferItemTypeEnum;
import com.sankuai.dzhealth.ai.service.enums.StreamEventCardTypeEnum;
import com.sankuai.dzhealth.ai.service.infrastructure.utils.UidUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @author:chenwei
 * @time: 2025/7/19 11:19
 * @version: 0.0.1
 */
@Component
@Slf4j
public class RelatedQuestionTask extends GeneralTask implements Task {


    @Autowired
    private UidUtils uidUtils;

    /**
     * 从字符串中提取JSON数组内容（只提取[]及其之间的内容）
     * @param text 包含JSON数组的文本
     * @return JSON数组字符串，如果没找到则返回"[]"
     */
    private String extractJsonArray(String text) {
        if (StringUtils.isBlank(text)) {
            return "[]";
        }
        
        // 使用正则表达式匹配第一个完整的JSON数组
        Pattern pattern = Pattern.compile("\\[.*?\\]", Pattern.DOTALL);
        Matcher matcher = pattern.matcher(text);
        
        if (matcher.find()) {
            return matcher.group();
        }
        
        return "[]";
    }

    @Override
    public boolean accept(String type) {
        return TaskTypeEnum.RELATED_QUESTION_TASK.getType().equals(type);
    }

    @Override
    public TaskProcessResult process(TaskContext context) {
        if (StringUtils.isBlank(context.getTaskConfig().getUserPrompt())) {
            context.getTaskConfig().setUserPrompt(context.getMessageContext().getMsg());
        }
        String answer = "[]";
        try {
            answer = getJsonAnswer(context);
        } catch (Exception e) {
            log.error("msg={}", context.getMessageContext().getMsg(), e);
        }
        buildMultiDialogueEvaluationRequest(context.getMessageContext(), context.getTaskConfig(), answer);
        return TaskProcessResult.builder()
                .taskContext(context)
                .answer(answer)
                .build();
    }

    @Override
    public void after(TaskProcessResult result) {
        String answer = result.getAnswer();
        String intentType = RequestContext.getAttribute(RequestContextConstant.INTENT_TYPE);
        if (IntentTypeConfig.RESERVE.equals(intentType)) {
            return;
        }
        try {
            if (StringUtils.isNotBlank(answer)) {
                // 提取JSON数组内容
                String jsonArrayContent = extractJsonArray(answer);
                List<String> relatedQuestions = JsonUtils.parseArray(jsonArrayContent, String.class);
                if (CollectionUtils.isNotEmpty(relatedQuestions)) {
                    String randomCardHashKey = uidUtils.getRandomCardHashKey();
                    String data = StreamEventCardTypeEnum.buildCardContent(StreamEventCardTypeEnum.RELATED_QUESTION_CARD, randomCardHashKey);
                    MessageBufferEntity entity = new MessageBufferEntity();
                    entity.setData(data);
                    entity.setType(BufferItemTypeEnum.MAIN_TEXT.getType());
                    Map<String , Object> map = new HashMap<>();
                    map.put("questions", relatedQuestions);
                    entity.setExtra(map);
                    BufferUtils.writeMainTextBuffer(entity, null);
                }
            }
        } catch (Exception e) {
            log.error("answer={}", answer, e);
        }

    }
}
