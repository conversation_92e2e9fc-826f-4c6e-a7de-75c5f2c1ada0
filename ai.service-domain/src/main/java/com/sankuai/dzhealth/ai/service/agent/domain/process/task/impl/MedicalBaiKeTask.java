package com.sankuai.dzhealth.ai.service.agent.domain.process.task.impl;

import com.sankuai.dzhealth.ai.service.agent.domain.context.TaskContext;
import com.sankuai.dzhealth.ai.service.agent.domain.enums.TaskTypeEnum;
import com.sankuai.dzhealth.ai.service.agent.domain.model.TaskProcessResult;
import com.sankuai.dzhealth.ai.service.agent.domain.process.task.GeneralTask;
import com.sankuai.dzhealth.ai.service.agent.domain.process.task.Task;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * @author:chenwei
 * @time: 2025/7/14 21:42
 * @version: 0.0.1
 */

@Slf4j
@Service
public class MedicalBaiKeTask extends GeneralTask implements Task {
    @Override
    public boolean accept(String type) {
        return TaskTypeEnum.MEDICAL_BAIKE_TASK.getType().equals(type);
    }

    @Override
    public TaskProcessResult process(TaskContext context) {

        // 如果未设置userPrompt 把问题塞进去
        if (StringUtils.isBlank(context.getTaskConfig().getUserPrompt())) {
            context.getTaskConfig().setUserPrompt(context.getMessageContext().getMsg());
        }
        String answer = getAnswer(context);
        buildMultiDialogueEvaluationRequest(context.getMessageContext(), context.getTaskConfig(), answer);
        return TaskProcessResult.builder()
                .taskContext(context)
                .answer(answer)
                .build();
    }

    @Override
    public void after(TaskProcessResult result) {

    }
}
