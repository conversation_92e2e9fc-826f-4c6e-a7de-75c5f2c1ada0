package com.sankuai.dzhealth.ai.service.agent.domain.buffer;

import com.dianping.rhino.Rhino;
import com.dianping.rhino.threadpool.DefaultThreadPoolProperties;
import com.dianping.rhino.threadpool.ThreadPool;
import com.sankuai.dzhealth.ai.service.agent.domain.buffer.label.CustomTruncateLabel;
import com.sankuai.dzhealth.ai.service.agent.domain.buffer.label.TruncateLabel;
import com.sankuai.dzhealth.ai.service.agent.domain.context.MessageContext;
import com.sankuai.dzhealth.ai.service.agent.domain.utils.BufferUtils;
import com.sankuai.dzhealth.ai.service.agent.domain.utils.ContentBuilderUtils;
import com.sankuai.dzhealth.ai.service.domain.utils.context.RequestContext;
import com.sankuai.dzhealth.ai.service.domain.utils.context.RequestContextConstant;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Future;

/**
 * 消息缓冲区服务
 * 
 * <AUTHOR>
 * @time 2025/7/6 15:10
 * @version 0.0.1
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MessageBufferService {

    public static final ThreadPool CONSUME_POOL = Rhino.newThreadPool("CONSUME_POOL",
            DefaultThreadPoolProperties.Setter().withCoreSize(100).withMaxSize(200).withMaxQueueSize(1000));

    /**
     * 缓冲区工具类
     */
    private final BufferUtils bufferUtils;

    /**
     * 内容构建工具类
     */
    private final ContentBuilderUtils contentBuilderUtils;

    /**
     * 创建缓冲区并启动消费任务
     * 
     * @return MessageBuffer 创建的消息缓冲区
     */
    public MessageBuffer createAndStartBufferConsumption() {
        log.info("Creating message buffer and starting consumption task");
        
        // 创建新的 MessageBuffer 实例
        MessageBuffer messageBuffer = new MessageBuffer();

        SseEmitter sseEmitter = RequestContext.getAttribute(RequestContextConstant.SSE_EMITTER);

        MessageContext context = RequestContext.getAttribute(RequestContextConstant.MESSAGE_CONTEXT);
        
        // 将 MessageBuffer 存入 RequestContext，使其在当前请求线程中可用
        RequestContext.setAttribute(RequestContextConstant.MESSAGE_BUFFER, messageBuffer);
        
        // 创建消费任务，并注入转换器和工具类
        BufferConsumerTask consumerTask = new BufferConsumerTask(messageBuffer, sseEmitter, 
                getTruncateMethod(context), context, bufferUtils, contentBuilderUtils);
        
        // 提交消费任务到线程池
        Future<Boolean> taskFuture = CONSUME_POOL.getExecutor().submit(consumerTask);
        
        // 将 Future 对象设置到 MessageBuffer 中，以便主流程可以等待任务完成
        messageBuffer.setTaskFuture(taskFuture);
        
        log.info("Message buffer created and consumption task started");
        return messageBuffer;
    }

    private List<TruncateLabel> getTruncateMethod(MessageContext context) {
        List<TruncateLabel> truncateLabels = new ArrayList<>();
        if (context == null || context.getAgentTaskConfig() == null || CollectionUtils.isEmpty(context.getAgentTaskConfig().getTruncates())) {
            truncateLabels.add(new CustomTruncateLabel());
            return truncateLabels;
        }
        context.getAgentTaskConfig().getTruncates().forEach(e -> {
            if ("custom".equals(e)) {
                truncateLabels.add(new CustomTruncateLabel());
            }
        });
        return truncateLabels;
    }

    /**
     * 停止缓冲区消费（发送结束信号）
     * 
     * @param messageBuffer 要停止的消息缓冲区
     */
    public void stopBufferConsumption(MessageBuffer messageBuffer) {
        if (messageBuffer == null) {
            log.warn("MessageBuffer is null, cannot stop consumption");
            return;
        }
        
        log.info("Stopping buffer consumption");
        messageBuffer.finishBufferConsume(messageBuffer);
    }

    /**
     * 获取当前请求上下文中的 MessageBuffer
     * 
     * @return MessageBuffer 当前的消息缓冲区，如果不存在则返回 null
     */
    public MessageBuffer getCurrentBuffer() {
        return RequestContext.getAttribute(RequestContextConstant.MESSAGE_BUFFER);
    }
}
