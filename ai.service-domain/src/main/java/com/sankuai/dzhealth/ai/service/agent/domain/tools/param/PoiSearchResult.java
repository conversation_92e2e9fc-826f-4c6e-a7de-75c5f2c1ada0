package com.sankuai.dzhealth.ai.service.agent.domain.tools.param;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.ai.tool.annotation.ToolParam;

/**
 * POI搜索结果
 * POI Search Result
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PoiSearchResult {

    @ToolParam(
        description = "城市编码，唯一标识城市的数字代码 / City code, unique numeric identifier for the city"
    )
    private String cityCode;

    @ToolParam(
        description = "经纬度信息，格式为'lng,lat'，如'116.397470,39.908823' / Geographic coordinates in 'lng,lat' format, e.g., '116.397470,39.908823'"
    )
    private String location;
}
