package com.sankuai.dzhealth.ai.service.agent.domain.process.task.impl;

import com.alibaba.fastjson.JSON;
import com.dianping.squirrel.client.StoreKey;
import com.dianping.squirrel.client.impl.redis.RedisStoreClient;
import com.sankuai.dzhealth.ai.service.agent.domain.buffer.MessageBuffer;
import com.sankuai.dzhealth.ai.service.agent.domain.buffer.MessageBufferEntity;
import com.sankuai.dzhealth.ai.service.agent.domain.context.TaskContext;
import com.sankuai.dzhealth.ai.service.agent.domain.enums.AppointmentReturnTypeEnum;
import com.sankuai.dzhealth.ai.service.agent.domain.enums.CatchAllTxtEnum;
import com.sankuai.dzhealth.ai.service.agent.domain.enums.TaskTypeEnum;
import com.sankuai.dzhealth.ai.service.agent.domain.model.AppointmentContext;
import com.sankuai.dzhealth.ai.service.agent.domain.process.card.data.AppointmentCardData;
import com.sankuai.dzhealth.ai.service.agent.domain.model.AppointmentReturn;
import com.sankuai.dzhealth.ai.service.agent.domain.model.TaskProcessResult;
import com.sankuai.dzhealth.ai.service.agent.domain.process.task.GeneralTask;
import com.sankuai.dzhealth.ai.service.agent.domain.process.task.Task;
import com.sankuai.dzhealth.ai.service.agent.domain.service.AppointmentService;
import com.sankuai.dzhealth.ai.service.agent.domain.tools.UserPhoneService;
import com.sankuai.dzhealth.ai.service.agent.domain.utils.BufferUtils;
import com.sankuai.dzhealth.ai.service.agent.dto.AppointmentRequestDTO;
import com.sankuai.dzhealth.ai.service.agent.enums.ContextExtraKey;
import com.sankuai.dzhealth.ai.service.agent.infrastructure.enums.ProductNameEnum;
import com.sankuai.dzhealth.ai.service.domain.utils.JsonUtils;
import com.sankuai.dzhealth.ai.service.domain.utils.context.RequestContext;
import com.sankuai.dzhealth.ai.service.domain.utils.context.RequestContextConstant;
import com.sankuai.dzhealth.ai.service.enums.BufferItemTypeEnum;
import com.sankuai.dzhealth.ai.service.enums.StreamEventCardTypeEnum;
import com.sankuai.dzhealth.ai.service.infrastructure.utils.UidUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import scala.App;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;


@Slf4j
@Service
public class MedicalAppointmentTask extends GeneralTask implements Task {

    @Autowired
    private AppointmentService appointmentService;

    @Autowired
    private UserPhoneService userPhoneService;

    @Autowired
    private UidUtils uidUtils;

    @Autowired
    @Qualifier("redisClient1")
    private RedisStoreClient redisStoreClient1;


    
    private static final int OPEN_HOUR = 10;
    private static final int CLOSE_HOUR = 21;


    @Override
    public boolean accept(String type) {
        return TaskTypeEnum.MEDICAL_APPOINTMENT_TASK.getType().equals(type);
    }

    @Override
    public TaskProcessResult process(TaskContext context) {
        log.info("MedicalAppointmentTask process,context:{}", JSON.toJSONString(context));
        // 如果未设置userPrompt 把问题塞进去
        if (StringUtils.isBlank(context.getTaskConfig().getUserPrompt())) {
            context.getTaskConfig().setUserPrompt(context.getMessageContext().getMsg());
        }
        MessageBuffer buffer = RequestContext.getAttribute(RequestContextConstant.MESSAGE_BUFFER);
        AppointmentReturn appointmentReturn=null;
        if (context.getMessageContext().getExtra().containsKey(ContextExtraKey.APPOINTMENT_BUTTON.getKey())
                && context.getMessageContext().getExtra().containsKey(ContextExtraKey.APPOINTMENT_MSG_ID.getKey())) {
            appointmentReturn = getAppointmentReturn(context);
            log.info("MedicalAppointmentTask process,appointmentReturn:{}", JSON.toJSONString(appointmentReturn));
            return processAppointmentReturn(appointmentReturn, context, buffer);
        }

        // 获取当前时间
        LocalDateTime now = LocalDateTime.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy年MM月dd日HH:mm");
        String currentTimeStr = now.format(formatter);
        String systemPrompt= context.getTaskConfig().getSystemPrompt();
        String userPhone=userPhoneService.getUserPhoneById(context.getMessageContext().getUserId());
        StoreKey storeKey = new StoreKey("appointment_short_memory", context.getMessageContext().getUserId(),context.getMessageContext().getSessionId() );
        String memory = redisStoreClient1.get(storeKey);
        if (StringUtils.isNotBlank(memory)) {
            systemPrompt = systemPrompt.replace("${memory}", memory);
        }
        systemPrompt=systemPrompt.replace("${currentPhone}",userPhone)
                .replace("${currentCityId}",String.valueOf(context.getMessageContext().getBasicParam().getUserCityId()))
                .replace("${currentTime}",currentTimeStr)
                .replace("${currentLat}",String.valueOf(context.getMessageContext().getBasicParam().getLat()))
                .replace("${currentLng}",String.valueOf(context.getMessageContext().getBasicParam().getLng()));
        context.getTaskConfig().setSystemPrompt(systemPrompt);
        log.info("MedicalAppointmentTask process,systemPrompt:{}", systemPrompt);
        String answer = getJsonAnswer(context);
        log.info("MedicalAppointmentTask process,answer:{}", answer);
        AppointmentContext appointmentContext = JSON.parseObject(answer, AppointmentContext.class);
        answer=checkAppointmentContext(appointmentContext,buffer,context.getMessageContext().getUserId(),context.getMessageContext().getSessionId());
        log.info("MedicalAppointmentTask process,answer:{}", answer);
        buildMultiDialogueEvaluationRequest(context.getMessageContext(), context.getTaskConfig(), answer);
        return TaskProcessResult.builder()
                .taskContext(context)
                .answer(answer)
                .build();
    }

    @Override
    public void after(TaskProcessResult result) {

    }

    public AppointmentReturn getAppointmentReturn(TaskContext context) {
        String msgId = context.getMessageContext().getExtra().get(ContextExtraKey.APPOINTMENT_MSG_ID.getKey()).toString().trim();
        String buttonValue = context.getMessageContext().getExtra().get(ContextExtraKey.APPOINTMENT_BUTTON.getKey()).toString().trim();
        String ReplyMsgId = context.getMessageContext().getReplyMsgId();
        if (StringUtils.isBlank(msgId) || StringUtils.isBlank(buttonValue) || StringUtils.isBlank(ReplyMsgId)) {
            return AppointmentReturn.builder()
                    .type(AppointmentReturnTypeEnum.TEXT)
                    .txt(CatchAllTxtEnum.EXCEPTION.getDesc())
                    .build();
        }
        AppointmentRequestDTO appointmentRequestDTO = new AppointmentRequestDTO();
        appointmentRequestDTO.setMsgId(msgId);
        appointmentRequestDTO.setUserId(context.getMessageContext().getUserId());
        appointmentRequestDTO.setAppVersion(context.getMessageContext().getBasicParam().getAppVersion());
        appointmentRequestDTO.setUuid(context.getMessageContext().getBasicParam().getUuid());
        appointmentRequestDTO.setReplyMsgId(ReplyMsgId);
        appointmentRequestDTO.setCityId(context.getMessageContext().getBasicParam().getUserCityId());
        return switch (buttonValue) {
            case "button_confirmAppointment" -> appointmentService.confirmAppointment(appointmentRequestDTO,context.getMessageContext().getBasicParam());
            case "button_cancelAppointment" -> appointmentService.cancelAppointment(appointmentRequestDTO);
            case "button_rescheduleAppointment" -> appointmentService.rescheduleAppointment(appointmentRequestDTO,context.getMessageContext().getBasicParam());
            case "button_cancelSuccessAppointment" ->
                    appointmentService.cancelSuccessAppointByButton(appointmentRequestDTO);
            case "button_reAppointment" -> appointmentService.reAppointment(appointmentRequestDTO);
            default -> {
                log.error("MedicalAppointmentTask getAppointmentReturn error, buttonValue:{}", buttonValue);
                yield AppointmentReturn.builder()
                        .type(AppointmentReturnTypeEnum.TEXT)
                        .txt(CatchAllTxtEnum.EXCEPTION.getDesc())
                        .build();
            }
        };
    }

    /**
     * 处理返回结果并构建任务处理结果
     *
     * @param appointmentReturn 预约返回结果
     * @param context 任务上下文
     * @param buffer 消息缓冲区
     * @return 任务处理结果
     */
    private TaskProcessResult processAppointmentReturn(AppointmentReturn appointmentReturn,
                                                       TaskContext context,
                                                       MessageBuffer buffer) {
        String content;
        MessageBufferEntity entity;

        if (appointmentReturn.getType() == AppointmentReturnTypeEnum.TEXT) {
            // 处理纯文本类型
            content = appointmentReturn.getTxt();
            entity = MessageBufferEntity.builder()
                    .data(content)
                    .type(BufferItemTypeEnum.MAIN_TEXT.getType())
                    .build();
        } else if (appointmentReturn.getType() == AppointmentReturnTypeEnum.CARD) {
            // 处理卡片类型
            AppointmentCardData card = appointmentReturn.getCard();
            String randomCardHashKey = uidUtils.getRandomCardHashKey();
            StreamEventCardTypeEnum cardTypeEnum = StreamEventCardTypeEnum.APPOINTMENT_INFO_CARD;
            content = StreamEventCardTypeEnum.buildCardContent(cardTypeEnum, randomCardHashKey);
            entity = MessageBufferEntity.builder()
                    .data(content)
                    .type(BufferItemTypeEnum.MAIN_TEXT.getType())
                    .extra(card.toMap())
                    .build();
        } else {
            throw new IllegalArgumentException("不支持的预约返回类型: " + appointmentReturn.getType());
        }

        BufferUtils.writeMainTextBuffer(entity, buffer);

        return TaskProcessResult.builder()
                .taskContext(context)
                .answer(content)
                .build();
    }

    private String checkAppointmentContext(AppointmentContext appointmentContext, MessageBuffer buffer,Long userId,String sessionId) {
        //1、多个预约条件不符合
        if (appointmentContext.hasMultipleEmptyFields()) {
            return buildErrorResponse(CatchAllTxtEnum.RE_APPOINTMENT,buffer);

        }
        Date startTime = appointmentContext.getStartTime();
        Date endTime = appointmentContext.getEndTime();

        // 2. 时间为空检查
        if (startTime == null || endTime == null) {
            return buildErrorResponse(CatchAllTxtEnum.TIME_NOT_EXIST,buffer);
        }

        long currentTimeMillis = System.currentTimeMillis();
        Date now = new Date(currentTimeMillis);

        // 3. 结束时间已过期检查
        if (endTime.before(now)) {
            return buildErrorResponse(CatchAllTxtEnum.TIME_IS_PASSED,   buffer);
        }

        // 4. 结束时间太近检查（30分钟内）
        Date minStartTime = new Date(currentTimeMillis + TimeUnit.MINUTES.toMillis(30));
        if (endTime.before(minStartTime)) {
            return buildErrorResponse(CatchAllTxtEnum.RE_APPOINTMENT_TIME_ERROR,buffer);
        }

        //调整开始时间为当前时间
        if(startTime.before(now)){
            startTime=now;
        }

        //5、是否跨天
        if(isCrossDay(startTime,endTime)){
            return buildErrorResponse(CatchAllTxtEnum.RE_APPOINTMENT_TIME_ERROR,buffer);
        }

        // 6. 开始时间太远检查（7天后）
        Date maxStartTime = new Date(currentTimeMillis + TimeUnit.DAYS.toMillis(7));
        if (startTime.after(maxStartTime)) {
            return buildErrorResponse(CatchAllTxtEnum.TIME_OUT_RANGE,buffer);
        }

        // 7. 营业时间检查
        if (!isValidAppointmentTime(startTime, endTime)) {
            return buildErrorResponse(CatchAllTxtEnum.TIME_IS_IN_CLOSED,buffer );
        }
        appointmentContext.setStartTime(adjustToBusinessHours(appointmentContext.getStartTime(), true));
        appointmentContext.setEndTime(adjustToBusinessHours(appointmentContext.getEndTime(), false));

        //8、提及项目但不是口腔
       if(StringUtils.isBlank(appointmentContext.getProductName())||(ProductNameEnum.fromDesc(appointmentContext.getProductName())==null)&&appointmentContext.getIsMentioned()){
              return  buildErrorResponse(CatchAllTxtEnum.PRODUCT_NOT_ALLOW,buffer);
       }


       //9、未提及项目
        if(StringUtils.isBlank(appointmentContext.getProductName())&&!appointmentContext.getIsMentioned()){
            return  buildErrorResponse(CatchAllTxtEnum.PRODUCT_NOT_EXIST,buffer);
        }

        //10、未提及地址、没有经纬度
        if(StringUtils.isBlank(appointmentContext.getPositionTxt())||!isValidCoordinate(appointmentContext.getLat(),appointmentContext.getLng())){
            return  buildErrorResponse(CatchAllTxtEnum.POSITION_NOT_EXIST,buffer);
        }
        //11、未提及地址但是找到经纬度
        if(!StringUtils.isBlank(appointmentContext.getPositionTxt())&&!isValidCoordinate(appointmentContext.getLat(),appointmentContext.getLng())){
            return  buildErrorResponse(CatchAllTxtEnum.POSITION_NOT_COLLECT,buffer);
        }
        //12、超出范围
        if(appointmentContext.getPositionTxt().equals("超出范围")){
            return  buildErrorResponse(CatchAllTxtEnum.POSITION_IS_OUTRANGE,buffer);
        }
        StoreKey storeKey = new StoreKey("appointment_short_memory",userId, sessionId);
        redisStoreClient1.set(storeKey, JsonUtils.toJsonString(appointmentContext));
        String randomCardHashKey = uidUtils.getRandomCardHashKey();
        StreamEventCardTypeEnum cardTypeEnum = StreamEventCardTypeEnum.APPOINTMENT_INFO_CARD;
        String content = StreamEventCardTypeEnum.buildCardContent(cardTypeEnum, randomCardHashKey);
        MessageBufferEntity entity = MessageBufferEntity.builder()
                .data(content)
                .type(BufferItemTypeEnum.MAIN_TEXT.getType())
                .extra(appointmentContext.toMap())
                .build();
        BufferUtils.writeMainTextBuffer(entity, buffer);
        return JSON.toJSONString(appointmentContext);
    }


    private String buildErrorResponse(CatchAllTxtEnum errorEnum, MessageBuffer buffer) {
        MessageBufferEntity entity = MessageBufferEntity.builder()
                .data(errorEnum.getDesc())
                .type(BufferItemTypeEnum.MAIN_TEXT.getType())
                .build();
        BufferUtils.writeMainTextBuffer(entity, buffer);
         return errorEnum.getDesc();
    }

    public static boolean isValidCoordinate(Double lat, Double lng) {
        // 1. 检查null
        if (lat == null || lng == null) {
            return false;
        }

        // 2. 检查是否都为0.0
        if (Double.compare(lat, 0.0) == 0 && Double.compare(lng, 0.0) == 0) {
            return false;
        }

        // 3. 检查范围（可选）
        if (lat < -90.0 || lat > 90.0 || lng < -180.0 || lng > 180.0) {
            return false;
        }

        return true;
    }



    public static boolean isCrossDay(Date startTime, Date endTime) {
        if (startTime == null || endTime == null) {
            return false;
        }

        LocalDate startDate = startTime.toInstant()
                .atZone(ZoneId.systemDefault())
                .toLocalDate();

        LocalDate endDate = endTime.toInstant()
                .atZone(ZoneId.systemDefault())
                .toLocalDate();

        return !startDate.equals(endDate);
    }





    /**
     * 判断预约时间是否符合营业时间要求
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return true-符合要求，false-不符合要求
     */
    public static boolean isValidAppointmentTime(Date startTime, Date endTime) {
        // 提取小时
        int startHour = getHour(startTime);
        int endHour = getHour(endTime);

        // 时间点的情况：开始时间等于结束时间
        if (startTime.equals(endTime)) {
            // 时间点必须在营业时间内
            return startHour >= OPEN_HOUR && startHour < CLOSE_HOUR;
        }

        // 时间段的情况：开始时间不等于结束时间
        // 只要有部分时间在营业时间内即可
        // 即：结束时间 > 营业开始时间 且 开始时间 < 营业结束时间
        return endHour > OPEN_HOUR && startHour < CLOSE_HOUR;
    }

    /**
     * 从Date中提取小时
     */
    private static int getHour(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        return calendar.get(Calendar.HOUR_OF_DAY);
    }

    private static Date adjustToBusinessHours(Date time, boolean isStartTime) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(time);
        int hour = cal.get(Calendar.HOUR_OF_DAY);
        int minute = cal.get(Calendar.MINUTE);

        if (isStartTime) {
            if (hour < OPEN_HOUR || (hour == OPEN_HOUR && minute < 0)) {
                cal.set(Calendar.HOUR_OF_DAY, OPEN_HOUR);
                cal.set(Calendar.MINUTE, 0);
            } else if (hour >= CLOSE_HOUR) {
                cal.add(Calendar.DAY_OF_MONTH, 1);
                cal.set(Calendar.HOUR_OF_DAY, OPEN_HOUR);
                cal.set(Calendar.MINUTE, 0);
            }
        } else {
            if (hour > CLOSE_HOUR || (hour == CLOSE_HOUR && minute > 0)) {
                cal.set(Calendar.HOUR_OF_DAY, CLOSE_HOUR);
                cal.set(Calendar.MINUTE, 0);
            } else if (hour < OPEN_HOUR) {
                cal.add(Calendar.DAY_OF_MONTH, -1);
                cal.set(Calendar.HOUR_OF_DAY, CLOSE_HOUR);
                cal.set(Calendar.MINUTE, 0);
            }
        }

        return cal.getTime();
    }


}
