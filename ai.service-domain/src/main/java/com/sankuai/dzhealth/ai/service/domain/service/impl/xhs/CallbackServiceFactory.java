package com.sankuai.dzhealth.ai.service.domain.service.impl.xhs;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy;
import com.sankuai.dzhealth.ai.service.api.xhs.XhsNoteCallbackService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.concurrent.ExecutionException;

/**
 * 回调服务工厂 - 管理XhsNoteCallbackService的创建和缓存
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
public class CallbackServiceFactory {

    private final Cache<String, XhsNoteCallbackService> clientCache = CacheBuilder.newBuilder()
            .maximumSize(20)
            .removalListener(removal -> {
                log.info("Removing XhsNoteCallbackService client from cache, appkey: {}, reason: {}",
                        removal.getKey(), removal.getCause());
            })
            .build();

    /**
     * 获取回调服务客户端
     * 
     * @param remoteAppkey 远程应用的appkey
     * @return XhsNoteCallbackService客户端
     */
    public XhsNoteCallbackService getCallbackService(String remoteAppkey) {
        try {
            return clientCache.get(remoteAppkey, () -> {
                try {
                    ThriftClientProxy thriftClientProxy = new ThriftClientProxy();
                    thriftClientProxy.setServiceInterface(XhsNoteCallbackService.class);
                    thriftClientProxy.setRemoteAppkey(remoteAppkey);
                    thriftClientProxy.setTimeout(1000);
                    thriftClientProxy.setNettyIO(true);
                    thriftClientProxy.setCallType("sync");
                    thriftClientProxy.afterPropertiesSet();
                    XhsNoteCallbackService client = (XhsNoteCallbackService) thriftClientProxy.getObject();
                    log.info("Successfully created XhsNoteCallbackService client for appkey: {}", remoteAppkey);
                    return client;
                } catch (Exception e) {
                    log.error("Failed to get XhsNoteCallbackService client for appkey: {}", remoteAppkey, e);
                    throw new RuntimeException("Failed to initialize XhsNoteCallbackService client for appkey: " + remoteAppkey, e);
                }
            });
        } catch (ExecutionException e) {
            log.error("Error getting XhsNoteCallbackService from cache for appkey: {}", remoteAppkey, e);
            throw new RuntimeException("Failed to get XhsNoteCallbackService for appkey: " + remoteAppkey, e.getCause());
        }
    }
} 
