package com.sankuai.dzhealth.ai.service.domain.service.thinking.impl;

import com.sankuai.dzhealth.ai.service.domain.enums.thinking.ThinkingSessionStatusEnum;
import com.sankuai.dzhealth.ai.service.domain.model.thinking.SequentialThought;
import com.sankuai.dzhealth.ai.service.domain.model.thinking.ThinkingSession;
import com.sankuai.dzhealth.ai.service.domain.service.thinking.ThinkingEngineService;
import com.sankuai.dzhealth.ai.service.domain.service.thinking.ThinkingSessionService;
import com.sankuai.dzhealth.ai.service.domain.service.thinking.ThinkingStepService;
import com.sankuai.dzhealth.ai.service.domain.thinking.tool.SequentialThinkingTool;
import com.sankuai.dzhealth.ai.service.domain.thinking.tool.WebSearchTool;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.messages.Message;
import org.springframework.ai.chat.messages.UserMessage;
import org.springframework.ai.chat.model.ChatResponse;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.ai.chat.prompt.SystemPromptTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 思考引擎服务实现类
 */
@Slf4j
@Service
public class ThinkingEngineServiceImpl implements ThinkingEngineService {

    @Autowired
    private ThinkingSessionService thinkingSessionService;
    @Autowired
    private ThinkingStepService thinkingStepService;
    @Autowired
    private SequentialThinkingTool sequentialThinkingTool;
    @Autowired
    private WebSearchTool webSearchTool;

    @Autowired
    @Qualifier("thinkingChatClient")
    private ChatClient chatClient;

    private static final String SYSTEM_PROMPT_TEMPLATE = """
你是一个专业的思考助手，将通过序列思考方法解决复杂问题。
你需要将问题分解为多个思考步骤，一步一步地进行思考，每一步都要深入分析并构建在前面步骤的基础上。
问题: {query}
            
你有两个工具可以使用：
1. sequentialthinking: 用于结构化记录你的思考过程
2. webSearch: 用于在网络上搜索最新信息，特别是当你需要具体数据、最新研究、相关事实时
            
【工具调用顺序要求】
- 在思考过程中，当你需要外部信息时，必须先调用webSearch工具获取信息，使用简洁明确的搜索查询。
- 仔细分析webSearch返回的结果，提取最相关的信息（如URL、标题、摘要）。
- 然后基于搜索结果，调用sequentialthinking工具记录你的思考。
- **在调用sequentialthinking工具时，务必将webSearch返回的相关URL以searchResults参数的形式结构化传递（如：searchResults=[“http://xxx.com/1”, “http://xxx.com/2”]），而不是仅在思考内容中自然语言引用。**
            
【思考步骤要求】
- 每个思考步骤都要用sequentialthinking工具结构化记录，包含：
  1. 当前的思考内容（thought）
  2. 思考序号（thoughtNumber）
  3. 预计总思考步骤数（totalThoughts）
  4. 是否需要下一个思考步骤（nextThoughtNeeded）
  5. 如果本步基于webSearch结果，必须传递searchResults参数，内容为本步引用的所有URL列表
- 你可以根据需要修改之前的思考（isRevision、revisesThought），或者从某个思考点分支出新的思考路径（branchFromThought、branchId）。
            
【最终输出要求】
- 最后一个思考步骤应该是对整个问题的总结和最终答案，必须引用所有用到的信息来源（searchResults）。
- 只有当nextThoughtNeeded为false时，才认为思考流程结束。
            
请严格按照“先搜索→再分析→再结构化记录思考”的顺序工作，确保每个需要外部信息的思考步骤都能在searchResults字段中结构化记录引用的URL。
""";

    @Override
    public Long createThinkingSession(String query, Map<String, Object> searchConfig) {
        return thinkingSessionService.createSession(query, searchConfig);
    }

    @Override
    public Map<String, Object> executeThinking(Long sessionId) {
        ThinkingSession session = thinkingSessionService.getSession(sessionId);
        if (session == null) {
            throw new IllegalArgumentException("会话不存在: " + sessionId);
        }
        // 更新会话状态为思考中
        thinkingSessionService.updateSessionStatus(sessionId, ThinkingSessionStatusEnum.THINKING);
        try {
            // 设置当前线程关联的会话ID，以便工具方法能够获取
            thinkingStepService.setCurrentSessionId(sessionId);
            // 构建系统提示
            Map<String, Object> params = new HashMap<>();
            params.put("query", session.getQuery());
            SystemPromptTemplate systemPromptTemplate = new SystemPromptTemplate(SYSTEM_PROMPT_TEMPLATE);
            Message systemMessage = systemPromptTemplate.createMessage(params);
            // 构建用户消息
            UserMessage userMessage = new UserMessage("请开始思考如何解决这个问题。");
            // 创建提示
            Prompt prompt = new Prompt(systemMessage, userMessage);
            // 执行思考过程
            ChatResponse response = chatClient.prompt(prompt)
                // 不需要在这里添加tools，应该通过配置类注册
                .call()
                .chatResponse();
            // 获取思考结果
            List<SequentialThought> thoughts = thinkingStepService.getThinkingSteps(sessionId);
            // 更新会话的实际步骤数
            if (!thoughts.isEmpty()) {
                thinkingSessionService.updateActualSteps(sessionId, (long) thoughts.size());
            }
            // 提取搜索结果并保存到会话
            Map<String, Object> searchResults = extractSearchResults(thoughts);
            if (!searchResults.isEmpty()) {
                thinkingSessionService.updateSessionSearchResults(sessionId, searchResults);
            }
            // 获取最终答案
            String finalAnswer = response.getResult().getOutput().getText();
            // 计算置信度评分（取最后一个思考步骤的置信度，如果没有则默认为0.8）
            BigDecimal confidenceScore = new BigDecimal("0.8");
            if (!thoughts.isEmpty()) {
                SequentialThought lastThought = thoughts.get(thoughts.size() - 1);
                if (lastThought.getConfidenceScore() != null) {
                    confidenceScore = lastThought.getConfidenceScore();
                }
            }
            // 提取来源URL
            List<String> sourceUrls = extractSourceUrls(thoughts);
            // 构建结果
            Map<String, Object> result = new HashMap<>();
            result.put("sessionId", sessionId);
            result.put("query", session.getQuery());
            result.put("thoughts", thoughts);
            result.put("finalAnswer", finalAnswer);
            result.put("confidenceScore", confidenceScore);
            result.put("sourceUrls", sourceUrls);
            result.put("verificationResult", createVerificationResult(thoughts));
            // 更新会话状态为已完成
            thinkingSessionService.updateSessionStatus(sessionId, ThinkingSessionStatusEnum.COMPLETED);
            return result;
        } catch (Exception e) {
            log.error("执行思考过程失败", e);
            thinkingSessionService.updateSessionStatus(sessionId, ThinkingSessionStatusEnum.FAILED);
            throw new RuntimeException("执行思考过程失败", e);
        } finally {
            // 清除当前线程关联的会话ID
            thinkingStepService.clearCurrentSessionId();
        }
    }

    @Override
    public Map<String, Object> getThinkingResult(Long sessionId) {
        ThinkingSession session = thinkingSessionService.getSession(sessionId);
        if (session == null) {
            throw new IllegalArgumentException("会话不存在: " + sessionId);
        }
        List<SequentialThought> thoughts = thinkingStepService.getThinkingSteps(sessionId);
        Map<String, Object> result = new HashMap<>();
        result.put("sessionId", sessionId);
        result.put("query", session.getQuery());
        result.put("status", session.getStatus());
        result.put("thoughts", thoughts);
        // 如果有最后一个思考步骤，并且不需要下一个思考步骤，则认为是最终答案
        if (!thoughts.isEmpty()) {
            SequentialThought lastThought = thoughts.get(thoughts.size() - 1);
            if (Boolean.FALSE.equals(lastThought.getNextThoughtNeeded())) {
                result.put("finalAnswer", lastThought.getThought());
            }
        }
        return result;
    }

    /**
     * 从思考步骤中提取来源URL
     *
     * @param thoughts 思考步骤列表
     * @return 来源URL列表
     */
    private List<String> extractSourceUrls(List<SequentialThought> thoughts) {
        List<String> sourceUrls = new ArrayList<>();
        for (SequentialThought thought : thoughts) {
            if (thought.getSearchResults() != null) {
                for (String result : thought.getSearchResults()) {
                    if (result.startsWith("http")) {
                        sourceUrls.add(result);
                    }
                }
            }
        }
        return sourceUrls;
    }
    /**
     * 创建验证结果
     *
     * @param thoughts 思考步骤列表
     * @return 验证结果
     */
    private Map<String, Object> createVerificationResult(List<SequentialThought> thoughts) {
        Map<String, Object> verificationResult = new HashMap<>();
        verificationResult.put("totalSteps", thoughts.size());
        verificationResult.put("hasRevisions", thoughts.stream().anyMatch(t -> Boolean.TRUE.equals(t.getIsRevision())));
        verificationResult.put("hasBranches", thoughts.stream().anyMatch(t -> t.getBranchFromThought() != null));
        // 计算平均置信度
        BigDecimal avgConfidence = thoughts.stream()
                .filter(t -> t.getConfidenceScore() != null)
                .map(SequentialThought::getConfidenceScore)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        if (!thoughts.isEmpty()) {
            long count = thoughts.stream()
                    .filter(t -> t.getConfidenceScore() != null)
                    .count();
            if (count > 0) {
                avgConfidence = avgConfidence.divide(new BigDecimal(count), 2, RoundingMode.HALF_UP);
            } else {
                avgConfidence = BigDecimal.ZERO;
            }
        }
        verificationResult.put("averageConfidence", avgConfidence);
        return verificationResult;
    }
    /**
     * 从思考步骤中提取搜索结果
     *
     * @param thoughts 思考步骤列表
     * @return 搜索结果Map
     */
    private Map<String, Object> extractSearchResults(List<SequentialThought> thoughts) {
        Map<String, Object> results = new HashMap<>();
        List<String> urls = new ArrayList<>();
        for (SequentialThought thought : thoughts) {
            if (thought.getSearchResults() != null && !thought.getSearchResults().isEmpty()) {
                urls.addAll(thought.getSearchResults());
            }
        }
        if (!urls.isEmpty()) {
            results.put("urls", urls);
        }
        return results;
    }
}