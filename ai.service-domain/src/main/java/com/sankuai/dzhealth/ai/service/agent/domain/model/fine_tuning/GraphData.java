package com.sankuai.dzhealth.ai.service.agent.domain.model.fine_tuning;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 图数据根模型
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class GraphData {

    /**
     * 节点列表
     */
    @JsonProperty("nodes")
    private List<GraphNode> nodes;

}

