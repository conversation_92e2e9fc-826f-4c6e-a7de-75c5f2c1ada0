package com.sankuai.dzhealth.ai.service.domain.service.decisionflow;

import com.sankuai.dzhealth.ai.service.enums.DecisionFlowElementStatusEnum;
import com.sankuai.dzhealth.ai.service.infrastructure.repository.decision.DecisionVectorStoreRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;

/**
 * 领域层向量检索服务
 * <p>
 * 封装业务层检索需求（是否灰度、topK 等），
 * 依赖基础设施层 DecisionVectorStoreRepository 做实际向量搜索。
 */
@Service
@RequiredArgsConstructor
public class DecisionVectorQueryService {

    private final DecisionVectorStoreRepository vectorRepo;

    /* ---------------------- 节点检索 ---------------------- */
    public List<String> searchNodeIdsOnline(String bizScene, String query, int topK) {
        return vectorRepo.searchNodeIds(bizScene, query, topK, Set.of(DecisionFlowElementStatusEnum.ONLINE));
    }

    public List<String> searchNodeIdsGray(String bizScene, String query, int topK) {
        return vectorRepo.searchNodeIds(bizScene, query, topK,
                Set.of(DecisionFlowElementStatusEnum.ONLINE, DecisionFlowElementStatusEnum.GRAY));
    }

    /* ---------------------- 资源检索 ---------------------- */
    public List<String> searchResourceIdsOnline(String bizScene, String query, int topK) {
        return vectorRepo.searchResourceIds(bizScene, query, topK, Set.of(DecisionFlowElementStatusEnum.ONLINE));
    }

    public List<String> searchResourceIdsGray(String bizScene, String query, int topK) {
        return vectorRepo.searchResourceIds(bizScene, query, topK,
                Set.of(DecisionFlowElementStatusEnum.ONLINE, DecisionFlowElementStatusEnum.GRAY));
    }
} 