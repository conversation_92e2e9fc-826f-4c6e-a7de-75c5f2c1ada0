package com.sankuai.dzhealth.ai.service.agent.domain.service;


import com.alibaba.fastjson.JSON;
import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.dianping.cat.util.MetricHelper;
import com.dianping.haima.entity.haima.HaimaContent;
import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import com.dianping.rhino.Rhino;
import com.dianping.rhino.threadpool.DefaultThreadPoolProperties;
import com.dianping.rhino.threadpool.ThreadPool;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.meituan.mdp.boot.starter.config.annotation.MdpConfig;
import com.meituan.mdp.boot.starter.thrift.annotation.MdpThriftClient;
import com.sankuai.dzhealth.ai.service.agent.domain.config.RecommendConfig;
import com.sankuai.dzhealth.ai.service.agent.domain.context.MessageContext;
import com.sankuai.dzhealth.ai.service.agent.domain.model.DoctorRecommendation;
import com.sankuai.dzhealth.ai.service.agent.domain.model.RecommendSizeData;
import com.sankuai.dzhealth.ai.service.agent.domain.model.ShopRecommendation;
import com.sankuai.dzhealth.ai.service.agent.domain.process.task.TaskConfig;
import com.sankuai.dzhealth.ai.service.agent.domain.tools.PoiReviewService;
import com.sankuai.dzhealth.ai.service.agent.infrastructure.proxy.DoctorTechShopAcl;
import com.sankuai.dzhealth.ai.service.agent.infrastructure.proxy.ReviewVectorSearchAcl;
import com.sankuai.dzhealth.ai.service.agent.infrastructure.proxy.doctorinfo.DoctorInfoAcl;
import com.sankuai.dzhealth.ai.service.agent.infrastructure.proxy.shop.ListingFacadeProxy;
import com.sankuai.dzhealth.ai.service.agent.request.*;
import com.sankuai.dzhealth.ai.service.domain.utils.JsonUtils;
import com.sankuai.dzhealth.ai.service.infrastructure.acl.HaimaAcl;
import com.sankuai.dzim.pilot.api.data.ReviewDTO;
import com.sankuai.dzim.pilot.api.data.ReviewVectorSearchRequest;
import com.sankuai.dzim.pilot.api.data.VectorSearchResponse;
import com.sankuai.medicalcosmetology.base.dto.DoctorTechShopRelationDTO;
import com.sankuai.medicalcosmetology.base.dto.RemoteResponse;
import com.sankuai.medicalcosmetology.display.api.home.DoctorHomeService;
import com.sankuai.medicalcosmetology.display.dto.DoctorProductCardDTO;
import com.sankuai.medicalcosmetology.display.dto.DoctorProductDTO;
import com.sankuai.medicalcosmetology.display.dto.TechnicianReviewDetailDTO;
import com.sankuai.medicalcosmetology.display.request.DoctorHomeRequest;
import com.sankuai.medicalcosmetology.mainpath.listingapi.dto.GoodsRecallDTO;
import com.sankuai.medicalcosmetology.mainpath.listingapi.dto.ShopGoodsRecallDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;


@Slf4j
@Service
public class RecommendService {

    public static final ThreadPool RECOMMEND_POOL = Rhino.newThreadPool("RECOMMEND_POOL",
            DefaultThreadPoolProperties.Setter().withCoreSize(50).withMaxSize(200).withMaxQueueSize(1000));
    public static final String SCENE_TYPE_DIALOGUE_PAGE = "DialoguePage";

    @MdpConfig("doctor.recommend.review.rag.request")
    private String reviewVectorSearchRequestConfig;


    @Autowired
    private DoctorInfoAcl doctorInfoAcl;

    @Autowired
    private DoctorTechShopAcl doctorTechShopAcl;

    @Autowired
    private ListingFacadeProxy listingFacadeProxy;

    @Autowired
    private HaimaAcl haimaAcl;

    @Autowired
    private RecommendConfig recommendConfig;


    @Autowired
    private PoiReviewService poiReviewService;

    @Autowired
    private ReviewVectorSearchAcl reviewVectorSearchAcl;

    @MdpThriftClient(remoteAppKey = "com.sankuai.medicalcosmetology.doctor.function", timeout = 2000)
    private DoctorHomeService doctorHomeService;

    public List<DoctorRecommendation> selectDoctorAndRecommend(List<Long> doctorIds, MessageContext messageContext, String nodeDesc) {
        Transaction transaction = Cat.newTransaction("RecommendService", "selectDoctorAndRecommend");
        try {

            log.info("query={}, [selectDoctorAndRecommend] doctorIds:{},nodeDesc={}", messageContext.getMsg(), JsonUtils.toJsonString(doctorIds), nodeDesc);
            if (CollectionUtils.isEmpty(doctorIds)) {
                throw new IllegalArgumentException("[recommendDoctor] doctorIds is empty");
            }
            if (doctorIds.size() > 20) {
                throw new IllegalArgumentException("[recommendDoctor] doctorIds size is too large");
            }
            DoctorInfoRequest doctorInfoRequest = new DoctorInfoRequest();
            doctorInfoRequest.setMergeDoctorIds(doctorIds);
            doctorInfoRequest.setUserId(messageContext.getUserId());
            doctorInfoRequest.setPlatform(2);
            String doctorInfo = doctorInfoAcl.queryDoctorInfo(doctorInfoRequest);
            Long doctorId = doctorIds.get(0);
            log.info("query={}, [selectDoctorAndRecommend] doctorInfo:{},nodeDesc={}", messageContext.getMsg(), JsonUtils.toJsonString(doctorInfo), nodeDesc);
            String sceneType = SCENE_TYPE_DIALOGUE_PAGE;
            MetricHelper.build().name("推荐供给数").tag("type", "doctor")
                    .tag("sceneType", sceneType).value(doctorIds.size());
            if (!StringUtils.isBlank(doctorInfo)) {

                TaskConfig selectDoctoTask = getHaiMaPrompt("select_doctor_prompt");
                String prompt = selectDoctoTask.getSystemPrompt().replace("${doctors_info}", doctorInfo).replace("${chat_record}", nodeDesc);
                selectDoctoTask.setUserPrompt(nodeDesc);
                selectDoctoTask.setSystemPrompt(prompt);
                log.info("query={}, [selectDoctorAndRecommend] selectPrompt:{}, nodeDesc={}", messageContext.getMsg(), JsonUtils.toJsonString(selectDoctoTask), nodeDesc);
                messageContext.getExtra().put(nodeDesc + "-doctorSelectPrompt", JsonUtils.toJsonString(selectDoctoTask));
                String doctorIdStr = recommendConfig.chatWithJSON(selectDoctoTask, sceneType);
                messageContext.getExtra().put(nodeDesc + "-doctorSelectPromptRes", doctorIdStr);
                log.info("query={}, [selectDoctorAndRecommend] selectPromptRes:{}, nodeDesc={}", messageContext.getMsg(), doctorIdStr, nodeDesc);
                if (StringUtils.isNotBlank(doctorIdStr)) {
                    doctorIdStr = cleanJsonString(doctorIdStr);
                }

                JsonNode jsonNode = JsonUtils.parseJsonNode(doctorIdStr);
                Long id = Optional.ofNullable(jsonNode.get("id"))
                        .filter(node -> !node.isNull() && (node.isLong() || node.canConvertToLong()))
                        .map(JsonNode::asLong)
                        .filter(value -> value > 0)
                        .orElse(null);

                if (id != null && doctorIds.contains(id)) {
                    doctorId = id;
                }
            }
            log.info("query={}, [selectDoctorAndRecommend] selectDoctorId:{}, nodeDesc={}", messageContext.getMsg(), doctorId, nodeDesc);
            return recommendDoctorInfo(Collections.singletonList(doctorId), messageContext, sceneType, nodeDesc);

        } catch (Exception e) {
            log.error("[recommendDoctor] recommendDoctorAndInfo error,doctorIds:{},messageContext:{}", doctorIds, JSON.toJSON(messageContext), e);
            if (CollectionUtils.isNotEmpty(doctorIds)) {
                return convertToDoctorRecommendations(Collections.singletonList(doctorIds.get(0)));
            }
            transaction.setStatus(e);
            return null;
        } finally {
            transaction.complete();
        }
    }

    public List<DoctorRecommendation> recommendDoctorInfo(List<Long> doctorIds, MessageContext messageContext, String sceneType, String reason) {
        Transaction transaction = Cat.newTransaction("RecommendService", "recommendDoctorInfo");
        try {
            log.info("query={},[recommendDoctorInfo]doctorIds={},reason={}", messageContext.getMsg(), JSON.toJSONString(doctorIds), reason);
            Map<String, RecommendSizeData> sizeDataMap = Lion.getMap(Environment.getAppName(), "recommend.size", RecommendSizeData.class);
            RecommendSizeData recommendSizeData = sizeDataMap.get(sceneType);
            int doctorProductSize = (recommendSizeData != null && recommendSizeData.getDoctorProductSize() != null)
                    ? recommendSizeData.getDoctorProductSize()
                    : 10;
            int doctorReviewSize = (recommendSizeData != null && recommendSizeData.getDoctorReviewSize() != null)
                    ? recommendSizeData.getDoctorReviewSize()
                    : 10;
            if (StringUtils.isBlank(messageContext.getMsg())) {
                messageContext.setMsg("推荐一下医生和下挂商品");
            }

            ConcurrentMap<Long, List<DoctorProductCardDTO>> doctorProduct = Maps.newConcurrentMap();
            ConcurrentMap<Long, List<TechnicianReviewDetailDTO>> doctorReview = Maps.newConcurrentMap();

            ConcurrentMap<Long, TechnicianReviewDetailDTO> reviewIdToReviewMap = Maps.newConcurrentMap();
            ConcurrentMap<String, DoctorProductCardDTO> productIdToProductMap = Maps.newConcurrentMap();

            // 预先构建DoctorHomeRequest对象，避免在循环中重复创建
            DoctorHomeRequest baseRequest = buildDoctorHomeRequest(messageContext);

            List<CompletableFuture<Void>> allTasks = new ArrayList<>();
            for (Long doctorId : doctorIds) {
                // 复用baseRequest，只修改doctorId
                DoctorHomeRequest doctorHomeRequest = cloneDoctorHomeRequest(baseRequest);
                doctorHomeRequest.setMergeDoctorId(doctorId);

                // 产品查询任务
                CompletableFuture<Void> productTask = CompletableFuture.runAsync(() -> {
                    try {
                        queryDoctorProducts(doctorId, doctorHomeRequest, doctorProduct, doctorProductSize);
                        // 直接在查询方法中填充映射，减少重复遍历
                        List<DoctorProductCardDTO> products = doctorProduct.get(doctorId);
                        if (CollectionUtils.isNotEmpty(products)) {
                            for (DoctorProductCardDTO product : products) {
                                if (product != null && StringUtils.isNotEmpty(product.getProductId())) {
                                    productIdToProductMap.put(product.getProductId(), product);
                                }
                            }
                        }
                    } catch (Exception e) {
                        log.error("查询医生产品异常,doctorId={}", doctorId, e);
                    }
                }, RECOMMEND_POOL.getExecutor());

                // 评价查询任务
                CompletableFuture<Void> reviewTask = CompletableFuture.runAsync(() -> {
                    try {
                        queryDoctorReviews(doctorId, doctorHomeRequest, doctorReview, doctorReviewSize, reason);
                        // 直接在查询方法中填充映射，减少重复遍历
                        List<TechnicianReviewDetailDTO> reviews = doctorReview.get(doctorId);
                        if (CollectionUtils.isNotEmpty(reviews)) {
                            for (TechnicianReviewDetailDTO review : reviews) {
                                if (review != null && review.getReviewId() != 0) {
                                    reviewIdToReviewMap.put(review.getReviewId(), review);
                                }
                            }
                        }
                    } catch (Exception e) {
                        log.error("查询医生评价异常, doctorId: {}", doctorId, e);
                    }
                }, RECOMMEND_POOL.getExecutor());

                allTasks.add(productTask);
                allTasks.add(reviewTask);
            }

            // 减少超时时间，但保留足够的处理时间
            CompletableFuture.allOf(allTasks.toArray(new CompletableFuture[0]))
                    .get(3, TimeUnit.SECONDS);


            List<String> doctorInfoForAi = new ArrayList<>(doctorIds.size());

            for (Long doctorId : doctorIds) {
                List<DoctorProductCardDTO> productCards = doctorProduct.get(doctorId);
                List<TechnicianReviewDetailDTO> reviewData = doctorReview.get(doctorId);
                doctorInfoForAi.add(convertDoctorToChineseJson(doctorId, productCards, reviewData));
            }

            // 构建提示词
            TaskConfig recommendDoctorTask = getHaiMaPrompt("recommend_doctor_prompt");
            String recommendDoctorPrompt = recommendDoctorTask.getSystemPrompt()
                    .replace("${doctor_relation}", String.join(",", doctorInfoForAi))
                    .replace("${chat_record}", reason)
                    .replace("${size}", String.valueOf(doctorIds.size()));
            recommendDoctorTask.setSystemPrompt(recommendDoctorPrompt);
            recommendDoctorTask.setUserPrompt(reason);

            log.info("query={}, [recommendDoctorInfo] recommendDoctorPrompt={}, reason={}", messageContext.getMsg(), JsonUtils.toJsonString(recommendDoctorTask), reason);
            // 调用大模型
            String recommendDoctorResult = recommendConfig.chatWithJSON(recommendDoctorTask, sceneType);
            log.info("query={}, [recommendDoctorInfo] recommendDoctorResult={}, reason={}", messageContext.getMsg(), recommendDoctorResult, reason);
            if (StringUtils.isNotBlank(recommendDoctorResult)) {
                recommendDoctorResult = cleanJsonString(recommendDoctorResult);
            }

            log.info("[recommendDoctorInfo] recommendDoctorResult={}", recommendDoctorResult);
            // 解析结果
            ObjectMapper mapper = new ObjectMapper();
            List<DoctorRecommendation> info = mapper.readValue(recommendDoctorResult, new TypeReference<List<DoctorRecommendation>>() {
            });
            log.info("query={}, [recommendDoctorInfo] recommendDoctorResultInfo={}, reason={}", messageContext.getMsg(), JsonUtils.toJsonString(info), reason);
            for (DoctorRecommendation doctorRecommendation : info) {
                if (CollectionUtils.isNotEmpty(doctorRecommendation.getReviewIdList())) {
                    List<String> reviewURL = new ArrayList<>(doctorRecommendation.getReviewIdList().size());
                    List<String> reviewContext = new ArrayList<>(doctorRecommendation.getReviewIdList().size());
                    List<String> reviewPicUrl = new ArrayList<>(doctorRecommendation.getReviewIdList().size());
                    for (Long reviewId : doctorRecommendation.getReviewIdList()) {
                        TechnicianReviewDetailDTO reviewDetailDTO = reviewIdToReviewMap.get(reviewId);
                        if (reviewDetailDTO != null) {
                            reviewContext.add(reviewDetailDTO.getBody());
                            reviewURL.add(reviewDetailDTO.getReviewUrl());
                            reviewPicUrl.add(reviewDetailDTO.getUserAvatarUrl());
                        }
                    }

                    doctorRecommendation.setReviewContext(reviewContext);
                    doctorRecommendation.setReviewURL(reviewURL);
                    doctorRecommendation.setReviewPicUrl(reviewPicUrl);
                }
                if (doctorRecommendation.getDetailId() != null) {
                    DoctorProductCardDTO doctorProductCardDTO = productIdToProductMap.get(doctorRecommendation.getDetailId());
                    if (doctorProductCardDTO != null) {
                        doctorRecommendation.setName(doctorProductCardDTO.getName());
                        doctorRecommendation.setPromoPrice(doctorProductCardDTO.getPromoPrice());
                        doctorRecommendation.setSaleNum(String.valueOf(doctorProductCardDTO.getSaleNum()));
                        doctorRecommendation.setDetailLink(doctorProductCardDTO.getDetailLink());
                    }
                }
            }
            log.info("query={}, [recommendDoctorInfo] finalInfo={}, reason={}", messageContext.getMsg(), JsonUtils.toJsonString(info), reason);
            if (CollectionUtils.isEmpty(info)) {
                throw new IllegalArgumentException("recommendDoctorInfo is empty");
            }
            return info;
        } catch (Exception e) {
            log.error("recommendDoctorInfo异常", e);
            if (CollectionUtils.isNotEmpty(doctorIds)) {
                return convertToDoctorRecommendations(doctorIds);
            }
            transaction.setStatus(e);
            return null;
        } finally {
            transaction.complete();
        }
    }

    /**
     * 将ID列表转换为DoctorRecommendation列表
     *
     * @param doctorIds 医生ID列表
     * @return DoctorRecommendation列表，如果输入为空则返回空列表
     */
    private List<DoctorRecommendation> convertToDoctorRecommendations(List<Long> doctorIds) {
        if (CollectionUtils.isEmpty(doctorIds)) {
            return new ArrayList<>();
        }

        return doctorIds.stream()
                .map(doctorId -> {
                    DoctorRecommendation recommendation = new DoctorRecommendation();
                    recommendation.setDoctorId(doctorId);
                    return recommendation;
                })
                .collect(Collectors.toList());
    }

    public List<ShopRecommendation> selectShopAndRecommend(List<ShopGoodsRecallDTO> shopGoodsRecallDTOs, MessageContext messageContext, String supplyDesc, String supplyName) {
        Transaction transaction = Cat.newTransaction("RecommendService", "selectShopAndRecommend");
        log.info("query={},[selectShopAndRecommend] shopQryList:{},supplyDesc:{}", messageContext.getMsg(), JSON.toJSONString(shopGoodsRecallDTOs), supplyDesc);
        if (CollectionUtils.isEmpty(shopGoodsRecallDTOs)) {
            throw new IllegalArgumentException("[recommendShop] shopIds is empty");
        }
        if (CollectionUtils.isNotEmpty(shopGoodsRecallDTOs) && shopGoodsRecallDTOs.size() > 20) {
            throw new IllegalArgumentException("[recommendShop] shopIds size is too large");
        }
        Long defaultShopId = shopGoodsRecallDTOs.get(0).getShopId();
        List<ShopQry> shopQryList = Lists.newArrayList();
        try {
            FillInfoRequest fillInfoRequest = getFillInfoRequest(shopGoodsRecallDTOs, messageContext);
            log.info("query={},supplyDesc={},[selectShopAndRecommend] fillInfoRequest:{}", messageContext.getMsg(), supplyDesc, JSON.toJSONString(fillInfoRequest));
            shopQryList = fillInfoRequest.getShopQryList();
            ShopQry shopQry = shopQryList.get(0);
            String result = listingFacadeProxy.fillInfo(fillInfoRequest);
            log.info("[selectShopAndRecommend]req={},result={}", JsonUtils.toJsonString(shopQryList), result);
            String sceneType = SCENE_TYPE_DIALOGUE_PAGE;
            MetricHelper.build().name("推荐供给数").tag("type", "shop")
                    .tag("sceneType", sceneType).value(shopGoodsRecallDTOs.size());
            if (StringUtils.isNotBlank(result) && !result.equals("{}")) {

                TaskConfig selectShopTask = getHaiMaPrompt("select_shop_prompt");
                String selectShopPrompt = selectShopTask.getSystemPrompt()
                        .replace("${shop_info}", result)
                        .replace("${shop_size}", String.valueOf(shopQryList.size()))
                        .replace("${chat_record}", supplyName + "," + supplyDesc);
                selectShopTask.setSystemPrompt(selectShopPrompt);
                selectShopTask.setUserPrompt(supplyName + "," + supplyDesc);
                messageContext.getExtra().put(supplyDesc + "-recommendShopPrompt", JsonUtils.toJsonString(selectShopTask));
                log.info("query={}, supplyDesc={}, shopPrompt={}", messageContext.getMsg(), supplyDesc, JsonUtils.toJsonString(selectShopTask));
                String selectShopQry = recommendConfig.chatWithJSON(selectShopTask, sceneType);
                log.info("query={}, supplyDesc={}, selectShopQry={}", messageContext.getMsg(), supplyDesc, selectShopQry);
                if (StringUtils.isNotBlank(selectShopQry)) {
                    selectShopQry = cleanJsonString(selectShopQry);
                }
                messageContext.getExtra().put(supplyDesc + "-recommendShopPromptRes", selectShopQry);
                JsonNode jsonNode = JsonUtils.parseJsonNode(selectShopQry);
                Long shopId = Optional.ofNullable(jsonNode.get("shopId"))
                        .filter(node -> !node.isNull() && StringUtils.isNotBlank(node.asText()))
                        .map(e -> NumberUtils.toLong(e.asText()))
                        .filter(value -> value > 0)
                        .orElse(null);

                Long productId = Optional.ofNullable(jsonNode.get("productId"))
                        .filter(node -> !node.isNull() && StringUtils.isNotBlank(node.asText()))
                        .map(e -> NumberUtils.toLong(e.asText()))
                        .filter(value -> value > 0)
                        .orElse(null);

                if (shopId == null) {
                    shopId = defaultShopId;
                }


                log.info("query={},shopId={},productId={}", messageContext.getMsg(), shopId, productId);
                List<ShopRecommendation> shopRecommendations = recommendShopInfo(Collections.singletonList(shopId), supplyDesc, SCENE_TYPE_DIALOGUE_PAGE, supplyName);
                if (CollectionUtils.isNotEmpty(shopRecommendations)) {
                    shopRecommendations.forEach(shopRecommendation -> {
                        shopRecommendation.setProductId(productId);
                    });
                }
                return shopRecommendations;
            }
            return convertToShopRecommendations(Collections.singletonList(defaultShopId));

        } catch (Exception e) {
            log.error("[recommendShop] recommendShopAndInfo error,shopQryList:{}", shopQryList != null ? shopQryList.toString() : "null", e);
            if (CollectionUtils.isNotEmpty(shopQryList)) {
                return convertToShopRecommendations(Collections.singletonList(shopQryList.get(0).getShopId()));
            }
            transaction.setStatus(e);
            return convertToShopRecommendations(Collections.singletonList(defaultShopId));
        } finally {
            transaction.complete();
        }
    }

    public List<ShopRecommendation> recommendShopInfo(List<Long> poiIds, String msg, String sceneType, String supplyName) {
        Transaction transaction = Cat.newTransaction("RecommendService", "recommendShopInfo");
        try {
            log.info("query={}, [recommendShopInfo] poiIds:{},sceneType:{}", msg, poiIds, sceneType);
            if (CollectionUtils.isEmpty(poiIds)) {
                return null;
            }
            if (StringUtils.isBlank(msg)) {
                msg = StringUtils.isBlank(supplyName) ? "推荐一下医美口腔项目" : supplyName;
            }
            
            // 如果只有一个门店，使用原有逻辑
            if (poiIds.size() == 1) {
                return recommendSingleShopInfo(poiIds, msg, sceneType, supplyName);
            }
            
            // 多个门店时使用并行调用逻辑
            return recommendMultipleShopsParallel(poiIds, msg, sceneType, supplyName);
            
        } catch (Exception e) {
            log.error("recommendShopInfo error", e);
            if (CollectionUtils.isNotEmpty(poiIds)) {
                return convertToShopRecommendations(poiIds);
            }
            transaction.setStatus(e);
            return null;
        } finally {
            transaction.complete();
        }
    }

    /**
     * 单个门店推荐逻辑（原有逻辑）
     */
    private List<ShopRecommendation> recommendSingleShopInfo(List<Long> poiIds, String msg, String sceneType, String supplyName) {
        Map<String, RecommendSizeData> sizeDataMap = Lion.getMap(Environment.getAppName(), "recommend.size", RecommendSizeData.class);
        RecommendSizeData recommendSizeData = sizeDataMap.get(sceneType);
        int shopReviewSize = (recommendSizeData != null && recommendSizeData.getShopReviewSize() != null)
                ? recommendSizeData.getShopReviewSize()
                : 10;
        Map<Long, Map<Long, String>> shopInfo = poiReviewService.batchQueryReview(poiIds, shopReviewSize, msg);
        log.info("query={}, [recommendSingleShopInfo] shopInfo={}", msg, JsonUtils.toJsonString(shopInfo));

        String shopInfoJson = JsonUtils.toJsonString(
                shopInfo.entrySet().stream()
                        .map(entry -> {
                            Map<String, Object> shopItem = new HashMap<>();
                            shopItem.put("门店id", entry.getKey());
                            shopItem.put("评价列表", entry.getValue());
                            return shopItem;
                        })
                        .toList()
        );
        
        TaskConfig recommendShopTask = getHaiMaPrompt("recommend_shop_prompt");
        String recommendShopPrompt = recommendShopTask.getSystemPrompt().replace("${shop_review}", shopInfoJson)
                .replace("${chat_record}", supplyName + "," + msg)
                .replace("${size}", "1");
        recommendShopTask.setSystemPrompt(recommendShopPrompt);
        recommendShopTask.setUserPrompt(supplyName + "," + msg);
        
        log.info("query={},[recommendSingleShopInfo]recommendShopTask={}", supplyName + "," + msg, JsonUtils.toJsonString(recommendShopTask));
        String recommendShopResult = "[]";
        try {
            recommendShopResult = recommendConfig.chatWithJSON(recommendShopTask);
        } catch (Exception e) {
            log.error("调用大模型推荐门店失败", e);
        }
        log.info("query={},[recommendSingleShopInfo]recommendShopTaskResult={}", msg, JsonUtils.toJsonString(recommendShopResult));
        
        if (StringUtils.isNotBlank(recommendShopResult)) {
            recommendShopResult = cleanJsonString(recommendShopResult);
        }
        
        List<ShopRecommendation> info = JsonUtils.parseObject(recommendShopResult, new TypeReference<List<ShopRecommendation>>() {
        });
        
        // 填充评价相关信息
        fillShopRecommendationDetails(info, shopInfo);
        
        log.info("query={}, [recommendSingleShopInfo] recommendShopTaskInfo={}", msg, JsonUtils.toJsonString(info));
        if (CollectionUtils.isEmpty(info)) {
            throw new IllegalArgumentException("recommendSingleShopInfo is empty");
        }
        return info;
    }

    /**
     * 多门店并行推荐逻辑
     */
    private List<ShopRecommendation> recommendMultipleShopsParallel(List<Long> poiIds, String msg, String sceneType, String supplyName) {
        Map<String, RecommendSizeData> sizeDataMap = Lion.getMap(Environment.getAppName(), "recommend.size", RecommendSizeData.class);
        RecommendSizeData recommendSizeData = sizeDataMap.get(sceneType);
        int shopReviewSize = (recommendSizeData != null && recommendSizeData.getShopReviewSize() != null)
                ? recommendSizeData.getShopReviewSize()
                : 10;
        
        // 批量查询所有门店的评价信息
        Map<Long, Map<Long, String>> allShopInfo = poiReviewService.batchQueryReview(poiIds, shopReviewSize, msg);
        log.info("query={},[recommendMultipleShopsParallel]allShopInfo={}", msg, JsonUtils.toJsonString(allShopInfo));

        // 为每个门店创建并行任务
        List<CompletableFuture<List<ShopRecommendation>>> futures = poiIds.stream()
                .map(poiId -> CompletableFuture.supplyAsync(() -> {
                    try {
                        return recommendSingleShopWithAI(poiId, allShopInfo.get(poiId), msg, supplyName);
                    } catch (Exception e) {
                        log.error("单个门店推荐失败,poiId={}", poiId, e);
                        // 返回基础的门店推荐信息作为降级
                        return convertToShopRecommendations(Collections.singletonList(poiId));
                    }
                }, RECOMMEND_POOL.getExecutor()))
                .collect(Collectors.toList());

        try {
            // 等待所有任务完成，设置合理的超时时间
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
                    .get(5, TimeUnit.SECONDS);

            // 合并结果
            List<ShopRecommendation> mergedResults = futures.stream()
                    .map(future -> {
                        try {
                            return future.get();
                        } catch (Exception e) {
                            log.error("获取门店推荐结果失败", e);
                            return new ArrayList<ShopRecommendation>();
                        }
                    })
                    .filter(CollectionUtils::isNotEmpty)
                    .flatMap(List::stream)
                    .collect(Collectors.toList());

            // 填充评价相关信息
            fillShopRecommendationDetails(mergedResults, allShopInfo);

            log.info("query={},[recommendMultipleShopsParallel]mergedResults={}", msg, JsonUtils.toJsonString(mergedResults));
            
            return mergedResults;

        } catch (Exception e) {
            log.error("并行推荐门店失败", e);
            // 降级：返回基础的门店推荐信息
            return convertToShopRecommendations(poiIds);
        }
    }

    /**
     * 为单个门店调用AI推荐
     */
    private List<ShopRecommendation> recommendSingleShopWithAI(Long poiId, Map<Long, String> shopReviews, String msg, String supplyName) {
        if (shopReviews == null || shopReviews.isEmpty()) {
            log.warn("门店{}的评价信息为空", poiId);
            return convertToShopRecommendations(Collections.singletonList(poiId));
        }

        // 构建单个门店的信息JSON
        Map<String, Object> shopItem = new HashMap<>();
        shopItem.put("门店id", poiId);
        shopItem.put("评价列表", shopReviews);
        String shopInfoJson = JsonUtils.toJsonString(Collections.singletonList(shopItem));

        // 构建AI任务配置
        TaskConfig recommendShopTask = getHaiMaPrompt("recommend_shop_prompt");
        String recommendShopPrompt = recommendShopTask.getSystemPrompt()
                .replace("${shop_review}", shopInfoJson)
                .replace("${chat_record}", supplyName + "," + msg)
                .replace("${size}", "1");
        recommendShopTask.setSystemPrompt(recommendShopPrompt);
        recommendShopTask.setUserPrompt(supplyName + "," + msg);

        log.info("poiId={},[recommendSingleShopWithAI]_recommendShopTask={}", poiId, JsonUtils.toJsonString(recommendShopTask));
        
        // 调用AI服务
        String recommendShopResult;
        try {
            recommendShopResult = recommendConfig.chatWithJSON(recommendShopTask);
        } catch (Exception e) {
            log.error("单个门店调用大模型失败, poiId={}", poiId, e);
            throw new RuntimeException("单个门店大模型调用失败", e);
        }
        log.info("poiId={}, [recommendSingleShopWithAI] recommendShopTaskResult={}", poiId, JsonUtils.toJsonString(recommendShopResult));

        if (StringUtils.isNotBlank(recommendShopResult)) {
            recommendShopResult = cleanJsonString(recommendShopResult);
        }

        try {
            List<ShopRecommendation> recommendations = JsonUtils.parseObject(recommendShopResult, new TypeReference<List<ShopRecommendation>>() {
            });
            
            if (CollectionUtils.isEmpty(recommendations)) {
                log.warn("AI返回的门店推荐结果为空, poiId={}", poiId);
                return convertToShopRecommendations(Collections.singletonList(poiId));
            }
            
            return recommendations;
        } catch (Exception e) {
            log.error("解析AI推荐结果失败, poiId={}, result={}", poiId, recommendShopResult, e);
            return convertToShopRecommendations(Collections.singletonList(poiId));
        }
    }

    /**
     * 填充门店推荐详情信息（评价链接、评价内容等）
     */
    private void fillShopRecommendationDetails(List<ShopRecommendation> recommendations, Map<Long, Map<Long, String>> allShopInfo) {
        if (CollectionUtils.isEmpty(recommendations) || allShopInfo == null) {
            return;
        }

        recommendations.forEach(shopRecommendation -> {
            List<Long> reviewIds = shopRecommendation.getReviewIds();
            if (CollectionUtils.isNotEmpty(reviewIds) && shopRecommendation.getShopId() != null) {
                Map<Long, String> shopReviews = allShopInfo.get(shopRecommendation.getShopId());
                if (shopReviews != null) {
                    List<String> reviewLinks = reviewIds.stream()
                            .map(reviewId -> buildMeituanReviewLink(String.valueOf(reviewId), shopRecommendation.getShopId()))
                            .collect(Collectors.toList());
                    shopRecommendation.setReviewUrl(reviewLinks);
                    
                    List<String> reviewContext = reviewIds.stream()
                            .map(shopReviews::get)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toList());
                    shopRecommendation.setReviewContext(reviewContext);
                }
            }
        });
    }

    /**
     * 将ID列表转换为ShopRecommendation列表
     *
     * @param poiIds 门店列表
     * @return ShopRecommendation列表，如果输入为空则返回空列表
     */
    private List<ShopRecommendation> convertToShopRecommendations(List<Long> poiIds) {
        if (CollectionUtils.isEmpty(poiIds)) {
            return new ArrayList<>();
        }

        return poiIds.stream()
                .map(poiId -> {
                    ShopRecommendation recommendation = new ShopRecommendation();
                    recommendation.setShopId(poiId);
                    return recommendation;
                })
                .collect(Collectors.toList());
    }


    // 构建美团评价链接的辅助方法
    private String buildMeituanReviewLink(String reviewId, Long shopId) {
        return String.format("imeituan://www.meituan.com/mrn?mrn_biz=gc&mrn_entry=ugc-review-list-dz&mrn_component=MTUGCReviewDetail&listVersion=2&sceneId=1000&reviewId=%s&shopId=%s",
                reviewId, shopId);
    }




    private static @NotNull FillInfoRequest getFillInfoRequest(List<ShopGoodsRecallDTO> shopGoodsRecallDTOs, MessageContext messageContext) {
        FillInfoRequest fillInfoRequest = new FillInfoRequest();

        // 初始化查询列表
        List<ShopQry> shopQryList = new ArrayList<>();
        List<GoodsQry> goodsQryList = new ArrayList<>();

        // 遍历每一个ShopGoodsRecallDTO，构造对应的ShopQry和GoodsQry
        for (ShopGoodsRecallDTO shopGoodsRecallDTO : shopGoodsRecallDTOs) {
            Long shopId = shopGoodsRecallDTO.getShopId();

            // 构造ShopQry
            ShopQry shopQry = new ShopQry();
            shopQry.setShopId(shopId);
            shopQryList.add(shopQry);

            // 构造GoodsQry（遍历该商户下的所有商品）
            if (CollectionUtils.isNotEmpty(shopGoodsRecallDTO.getShopGoodsRecallDTOs())) {
                for (GoodsRecallDTO goodsRecallDTO : shopGoodsRecallDTO.getShopGoodsRecallDTOs()) {
                    GoodsQry goodsQry = new GoodsQry();
                    goodsQry.setGoodsId(goodsRecallDTO.getGoodsId());
                    goodsQry.setGoodsType(goodsRecallDTO.getGoodsType());

                    // 在GoodsQry中绑定对应的shopId
                    GoodsShopQry goodsShopQry = new GoodsShopQry();
                    goodsShopQry.setShopId(shopId);
                    goodsQry.setShopQry(goodsShopQry);

                    goodsQryList.add(goodsQry);
                }
            }
        }

        fillInfoRequest.setShopQryList(shopQryList);
        fillInfoRequest.setGoodsQryList(goodsQryList);
        fillInfoRequest.setUserId(messageContext.getUserId());
        fillInfoRequest.setPlatform(messageContext.getPlatform());
        fillInfoRequest.setAppVersion(messageContext.getBasicParam().getAppVersion());
        fillInfoRequest.setCityId(messageContext.getBasicParam().getUserCityId());
        fillInfoRequest.setOs("ios".equals(messageContext.getBasicParam().getClientType()) ? "ios" : "Android");
        fillInfoRequest.setUuid(messageContext.getBasicParam().getUuid());
        fillInfoRequest.setLat(messageContext.getBasicParam().getLat());
        fillInfoRequest.setLng(messageContext.getBasicParam().getLng());
        fillInfoRequest.setUserId(messageContext.getUserId());
        fillInfoRequest.setTemplateKey("LargeModelRanking");
        return fillInfoRequest;
    }


    // 提取产品查询逻辑
    private void queryDoctorProducts(Long doctorId, DoctorHomeRequest request,
                                     ConcurrentMap<Long, List<DoctorProductCardDTO>> doctorProduct, Integer size) {
        RemoteResponse<DoctorProductDTO> doctorProductDTO = doctorHomeService.queryProductList(request);
        if (isValidResponse(doctorProductDTO) && doctorProductDTO.getData() != null) {
            List<DoctorProductCardDTO> productCards = doctorProductDTO.getData().getProductCards();
            log.info("queryDoctorProducts doctorId:{} productCards:{}", doctorId, JSON.toJSONString(doctorProduct));
            if (CollectionUtils.isNotEmpty(productCards)) {
                productCards = productCards.subList(0, Math.min(size, productCards.size()));
                doctorProduct.put(doctorId, productCards);
            }
        }
    }

    // 转换单个产品为中文JSON
    private String convertDoctorToChineseJson(Long doctorId, List<DoctorProductCardDTO> products, List<TechnicianReviewDetailDTO> reviewDatas) {
        try {
            Map<String, Object> doctorInfo = new HashMap<>();
            doctorInfo.put("医生id", doctorId);

            // 转换产品列表
            List<Map<String, Object>> chineseProducts = CollectionUtils.isNotEmpty(products)
                    ? products.stream()
                    .map(this::convertProductToChineseMap)
                    .collect(Collectors.toList())
                    : null;
            doctorInfo.put("商品列表", chineseProducts);

            // 转换评价列表
            List<Map<String, Object>> chineseReviews = CollectionUtils.isNotEmpty(reviewDatas)
                    ? reviewDatas.stream()
                    .map(this::convertReviewToChineseMap)
                    .collect(Collectors.toList())
                    : null;
            doctorInfo.put("评价列表", chineseReviews);


            ObjectMapper mapper = new ObjectMapper();
            return mapper.writeValueAsString(doctorInfo);

        } catch (Exception e) {
            log.error("转换医生信息为中文JSON失败", e);
            return "{}";
        }
    }

    // 转换单个产品为中文Map
    private Map<String, Object> convertProductToChineseMap(DoctorProductCardDTO product) {
        Map<String, Object> chineseProduct = new HashMap<>();
        chineseProduct.put("商品id", product.getProductId());
        chineseProduct.put("商品名称", product.getName());
        chineseProduct.put("促销价", product.getPromoPrice());
        chineseProduct.put("销量", product.getSaleNum());
        return chineseProduct;
    }

    // 转换单个评价为中文Map
    private Map<String, Object> convertReviewToChineseMap(TechnicianReviewDetailDTO review) {
        Map<String, Object> chineseReview = new HashMap<>();
        chineseReview.put("评价id", review.getReviewId());
        chineseReview.put("评价内容", review.getBody());
        return chineseReview;
    }

    private void queryDoctorReviews(Long doctorId, DoctorHomeRequest request,
                                    ConcurrentMap<Long, List<TechnicianReviewDetailDTO>> doctorReview, Integer size, String query) {
        try {
            List<DoctorTechShopRelationDTO> relations = doctorTechShopAcl.queryTechIdsByMergeDoctorId(Collections.singletonList(doctorId));
            if (CollectionUtils.isEmpty(relations)) {
                log.info("queryDoctorReviews no tech relations for doctorId={}", doctorId);
                return;
            }

            List<Long> technicianIds = relations.stream()
                    .map(DoctorTechShopRelationDTO::getTechnicianId)
                    .filter(Objects::nonNull)
                    .map(Integer::longValue)
                    .distinct()
                    .collect(Collectors.toList());

            if (CollectionUtils.isEmpty(technicianIds)) {
                log.info("queryDoctorReviews no technicianIds for doctorId={}", doctorId);
                return;
            }

            List<TechnicianReviewDetailDTO> allReviews = new ArrayList<>();
            int remaining = size != null && size > 0 ? size : 10;

            ReviewVectorSearchRequest searchRequest;
            try { searchRequest = JSON.parseObject(reviewVectorSearchRequestConfig, ReviewVectorSearchRequest.class); } catch (Exception e) { searchRequest = new ReviewVectorSearchRequest(); }
            searchRequest.setTopK(remaining);
            searchRequest.setQuery(query);
            searchRequest.setTechnicianIdList(technicianIds);

            VectorSearchResponse<ReviewDTO> response = reviewVectorSearchAcl.searchReview(searchRequest);

            if (response != null && CollectionUtils.isNotEmpty(response.getDataList())) {
                // 使用PoiReviewService的逻辑来处理reviewId到unionReviewId的转换
                Map<Long, ReviewDTO> processedReviews = poiReviewService.processReviewsForDoctor(response.getDataList());

                for (Map.Entry<Long, ReviewDTO> entry : processedReviews.entrySet()) {
                    if (remaining <= 0) break;

                    Long unionReviewId = entry.getKey();
                    ReviewDTO reviewDTO = entry.getValue();

                    TechnicianReviewDetailDTO detail = new TechnicianReviewDetailDTO();
                    detail.setReviewId(unionReviewId); // 使用unionReviewId

                    // 处理评价内容，替换换行符
                    String content = reviewDTO.getContent() == null ?
                            null : reviewDTO.getContent().replace("\\\\n", " ");
                    detail.setBody(content);

                    // 直接从ReviewDTO中获取shopId
                    Long shopId = Optional.ofNullable(reviewDTO.getMtShopId()).orElse(0L);
                    String reviewUrl = buildMeituanReviewLink(String.valueOf(unionReviewId), shopId);
                    detail.setReviewUrl(reviewUrl);

                    allReviews.add(detail);
                    remaining--;
                }
            }

            if (CollectionUtils.isNotEmpty(allReviews)) {
                doctorReview.put(doctorId, allReviews);
            }
        } catch (Exception e) {
            log.error("queryDoctorReviews by tech vector error, doctorId={}", doctorId, e);
        }
    }

    // 提取响应验证逻辑
    private boolean isValidResponse(RemoteResponse<?> response) {
        return response != null && response.getCode() == 200;
    }


    private String cleanJsonString(String jsonString) {
        if (StringUtils.isBlank(jsonString)) {
            return jsonString;
        }
        // 去掉开头的 ```json 或 ```
        jsonString = jsonString.replaceAll("^```(json)?\\s*", "");
        // 去掉结尾的 ```
        jsonString = jsonString.replaceAll("```\\s*$", "");

        jsonString = jsonString.replaceAll("\n", "");

        // 去掉首尾空白字符
        return jsonString.trim();
    }


    public DoctorHomeRequest buildDoctorHomeRequest(MessageContext messageContext) {
        DoctorHomeRequest doctorHomeRequest = new DoctorHomeRequest();
        //平台：1-点评，2-美团，3-外卖
        doctorHomeRequest.setPlatform(2);
        doctorHomeRequest.setInApp(true);
        doctorHomeRequest.setUserId(messageContext.getUserId());
        double lat = (messageContext.getBasicParam() != null && messageContext.getBasicParam().getLat() != null)
                ? messageContext.getBasicParam().getLat()
                : 0.0;
        double lng = (messageContext.getBasicParam() != null && messageContext.getBasicParam().getLng() != null)
                ? messageContext.getBasicParam().getLng()
                : 0.0;
        doctorHomeRequest.setLat(lat);
        doctorHomeRequest.setLng(lng);
        doctorHomeRequest.setCityId(messageContext.getBasicParam().getUserCityId());
        doctorHomeRequest.setAppVersion(messageContext.getBasicParam().getAppVersion());
        doctorHomeRequest.setDeviceId(messageContext.getBasicParam().getUuid());
        doctorHomeRequest.setMobileOS(messageContext.getBasicParam().getClientType());
        return doctorHomeRequest;
    }


    private TaskConfig getHaiMaPrompt(String task) {
        Map<String, String> field = Maps.newHashMap();
        field.put("task", task);
        List<HaimaContent> haimaContents = haimaAcl.getContent("medical_task_ai_config", field);

        if (CollectionUtils.isEmpty(haimaContents)) {
            return new TaskConfig();
        }

        HaimaContent content = haimaContents.get(0);
        TaskConfig config = JsonUtils.parseObject(content.getContentString("config"), TaskConfig.class);
        if (config == null) {
            config = new TaskConfig();
        }
        if (org.apache.commons.lang.StringUtils.isNotBlank(content.getContentString("systemPrompt"))) {
            config.setSystemPrompt(content.getContentString("systemPrompt"));
        }
        return config;
    }

    // 辅助方法：克隆DoctorHomeRequest对象，避免重复创建
    private DoctorHomeRequest cloneDoctorHomeRequest(DoctorHomeRequest original) {
        DoctorHomeRequest clone = new DoctorHomeRequest();
        clone.setPlatform(original.getPlatform());
        clone.setInApp(original.isInApp());
        clone.setUserId(original.getUserId());
        clone.setLat(original.getLat());
        clone.setLng(original.getLng());
        clone.setCityId(original.getCityId());
        clone.setAppVersion(original.getAppVersion());
        clone.setDeviceId(original.getDeviceId());
        clone.setMobileOS(original.getMobileOS());
        return clone;
    }

}



