package com.sankuai.dzhealth.ai.service.domain.evaluation.entity;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import org.springframework.ai.evaluation.EvaluationResponse;

import java.util.HashMap;
import java.util.Map;

/**
 * @author: yang<PERSON><PERSON>
 * @date: 2025/4/29 11:21
 * @version: 1.0
 */
@EqualsAndHashCode(callSuper = true)
@Getter
public class RagEvaluationResponse extends EvaluationResponse {
    private final String key;
    private final String description;
    private final boolean success;
    private final Long sessionId;
    private final Long msgId;
    private final String bizScene;
    private final String modelScene;

    // 添加构造函数
    public RagEvaluationResponse(boolean pass, float score, String feedback, Map<String, Object> metadata,
                               String key, String description, boolean success,
                               Long sessionId, Long msgId, String bizScene, String modelScene) {
        super(pass, score, feedback, metadata);
        this.key = key;
        this.description = description;
        this.success = success;
        this.sessionId = sessionId;
        this.msgId = msgId;
        this.bizScene = bizScene;
        this.modelScene = modelScene;
    }

    public static class RagEvaluationResponseBuilder {
        private String key;
        private String description;
        private boolean success;
        private boolean pass;
        private float score;
        private String feedback;
        private Map<String, Object> metadata = new HashMap<>();
        private Long sessionId;
        private Long msgId;
        private String bizScene;
        private String modelScene;

        public RagEvaluationResponseBuilder key(String key) {
            this.key = key;
            return this;
        }

        public RagEvaluationResponseBuilder description(String description) {
            this.description = description;
            return this;
        }

        public RagEvaluationResponseBuilder success(boolean success) {
            this.success = success;
            return this;
        }

        public RagEvaluationResponseBuilder pass(boolean pass) {
            this.pass = pass;
            return this;
        }

        public RagEvaluationResponseBuilder score(float score) {
            this.score = score;
            return this;
        }

        public RagEvaluationResponseBuilder feedback(String feedback) {
            this.feedback = feedback;
            return this;
        }

        public RagEvaluationResponseBuilder metadata(Map<String, Object> metadata) {
            this.metadata = metadata != null ? new HashMap<>(metadata) : new HashMap<>();
            return this;
        }

        public RagEvaluationResponseBuilder sessionId(Long sessionId) {
            this.sessionId = sessionId;
            return this;
        }

        public RagEvaluationResponseBuilder msgId(Long msgId) {
            this.msgId = msgId;
            return this;
        }

        public RagEvaluationResponseBuilder bizScene(String bizScene) {
            this.bizScene = bizScene;
            return this;
        }

        public RagEvaluationResponseBuilder modelScene(String modelScene) {
            this.modelScene = modelScene;
            return this;
        }

        public RagEvaluationResponse build() {
            return new RagEvaluationResponse(
                pass, score, feedback, metadata,
                key, description, success,
                sessionId, msgId, bizScene, modelScene
            );
        }
    }

    // 对外暴露的 builder() 方法
    public static RagEvaluationResponseBuilder builder() {
        return new RagEvaluationResponseBuilder();
    }
}
