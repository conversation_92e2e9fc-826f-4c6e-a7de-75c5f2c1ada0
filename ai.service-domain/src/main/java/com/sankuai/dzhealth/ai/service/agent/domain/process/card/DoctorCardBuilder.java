package com.sankuai.dzhealth.ai.service.agent.domain.process.card;

import com.sankuai.dzhealth.ai.service.agent.domain.context.MessageContext;
import com.sankuai.dzhealth.ai.service.agent.domain.process.card.data.DoctorProductSimpleData;
import com.sankuai.dzhealth.ai.service.agent.enums.ContextExtraKey;
import com.sankuai.dzhealth.ai.service.domain.utils.JsonUtils;
import com.sankuai.dzhealth.ai.service.dto.stream.StreamEventCardDataDTO;
import com.sankuai.dzhealth.ai.service.enums.StreamEventCardTypeEnum;
import com.sankuai.medicalcosmetology.display.dto.DoctorCardInfoAndCase;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

/**
 * @author:chenwei
 * @time: 2025/7/22 19:15
 * @version: 0.0.1
 */

@Service
@Slf4j
public class DoctorCardBuilder implements CardBuilder {
    @Override
    public boolean accept(String cardType) {
        return StreamEventCardTypeEnum.DOCTOR_CARD.getType().equals(cardType);
    }

    @Override
    public void padding(StreamEventCardDataDTO streamEventCardDataDTO, MessageContext messageContext) {
        String key = streamEventCardDataDTO.getKey();
        Map<String, Serializable> extra = messageContext.getExtra();

        // 确保extra不为null
        if (extra == null) {
            extra = new HashMap<>();
            messageContext.setExtra(extra);
        }

        // 获取现有的supply key列表
        Map<String, Object> supplyKeyMap = getOrCreateSupplyKeyMap(extra);

        // 添加新的key
        supplyKeyMap.put(key, StreamEventCardTypeEnum.DOCTOR_CARD.getType());

        // 更新到extra中
        extra.put(ContextExtraKey.SUPPLY_KEY_CARD_MAP.getKey(), JsonUtils.toJsonString(supplyKeyMap));
    }

    @Override
    public String parseCardProps(StreamEventCardDataDTO streamEventCardDataDTO) {
        Map<String, Object> cardProps = streamEventCardDataDTO.getCardProps();

        StringBuilder result = new StringBuilder();

        DoctorCardInfoAndCase doctorCardInfoAndCase = JsonUtils.parseObject(JsonUtils.toJsonString(cardProps.get("detail")), DoctorCardInfoAndCase.class);

        if (doctorCardInfoAndCase != null) {
            result.append("该医生的姓名是:").append(doctorCardInfoAndCase.getName())
                    .append(",职称是").append(doctorCardInfoAndCase.getClinicalTitle()).append("\n");
        }
        DoctorProductSimpleData product = JsonUtils.parseObject(JsonUtils.toJsonString(cardProps.get("product")), DoctorProductSimpleData.class);
        if (product != null) {
            result.append("推荐商品名称是:").append(product.getName()).append(",价格是:").append(product.getPromoPrice()).append("\n");
        }

        String reason = (String) cardProps.get("reason");
        result.append("该医生的推荐理由是:").append(reason).append("\n");
        return result.toString();
    }


    private Map<String, Object> getOrCreateSupplyKeyMap(Map<String, Serializable> extra) {
        String supplyListJson = (String) extra.get(ContextExtraKey.SUPPLY_KEY_CARD_MAP.getKey());
        if (StringUtils.isBlank(supplyListJson)) {
            return new HashMap<>();
        }

        try {
            return new HashMap<>(JsonUtils.parseMap(supplyListJson));
        } catch (Exception e) {
            log.error("解析supply key列表失败，使用新列表: {}", supplyListJson, e);
            return new HashMap<>();
        }
    }
}
