package com.sankuai.dzhealth.ai.service.domain.service.strategy;

import com.alibaba.fastjson.JSON;
import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.dianping.rhino.threadpool.ThreadPool;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.meituan.mdp.boot.starter.config.annotation.MdpConfig;
import com.sankuai.dzhealth.ai.service.api.ESVectorStoreService;
import com.sankuai.dzhealth.ai.service.api.RemoteResponse;
import com.sankuai.dzhealth.ai.service.domain.chat.model.IntentionResult;
import com.sankuai.dzhealth.ai.service.domain.service.ChatSessionService;
import com.sankuai.dzhealth.ai.service.domain.ability.llm.FilterableItem;
import com.sankuai.dzhealth.ai.service.domain.ability.llm.LLMResultFilterService;
import com.sankuai.dzhealth.ai.service.domain.utils.context.AiAnswerContext;
import com.sankuai.dzhealth.ai.service.dto.DocumentDTO;
import com.sankuai.dzhealth.ai.service.infrastructure.acl.FridayWebSearchAcl;
import com.sankuai.dzhealth.ai.service.infrastructure.model.FridaySearchResult;
import com.sankuai.dzhealth.ai.service.infrastructure.observability.Span;
import com.sankuai.dzhealth.ai.service.request.DocumentSearchRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.stream.IntStream;

import static java.util.stream.Collectors.toList;

import com.sankuai.dzhealth.ai.service.enums.MetadataKeyEnum;

/**
 * 搜索策略实现
 */
@Component
@Slf4j
public class SearchStrategy implements DataSourceStrategy<SearchStrategy.SearchStrategyInfo> {

    @Autowired
    private FridayWebSearchAcl fridayWebSearchAcl;

    @Autowired
    private ESVectorStoreService esVectorStoreService;

    // 专门用于搜索结果过滤的ChatClient
    @Autowired
    private ChatClient retrieveStrategyResEvaChatClient;

    @Autowired
    private LLMResultFilterService llmResultFilterService;

    @MdpConfig("public_hospital.search.sites:[\"www.dayi.org.cn\",\"www.zhihu.com\",\"www.bohe.cn\",\"www.fh21.com.cn\",\"www.familydoctor.com.cn\",\"www.haodf.com\",\"m.youlai.cn\",\"mp.weixin.qq.com\",\"baijiahao.baidu.com\"]")
    private String searchSites;

    @MdpConfig("public_hospital.search.topk")
    private Integer commonSearchTopK;
    @MdpConfig("public_hospital.search.deep_search_res_rag_topk:5")
    private Integer deepSearchResRagTopK;

    @MdpConfig("public_hospital.search.rag_timeout_ms:3000")
    private Long ragTimeoutMs = 3000L;

    /**
     * 是否启用搜索结果过滤功能
     * 配置项：public_hospital.search.enable_result_filtering
     * 默认值：true（搜索结果质量参差不齐，建议默认开启过滤）
     */
    @MdpConfig("public_hospital.search.enable_result_filtering:false")
    private Boolean enableSearchResultFiltering;

    private static final ThreadPool taskPool = ChatSessionService.TASK_POOL;


    @Override
    public boolean shouldExecute(IntentionResult intentionResult) {
        return intentionResult.getSearchNeed() != null && intentionResult.getSearchNeed();
    }

    @Override
    public CompletableFuture<SearchStrategy.SearchStrategyInfo> execute(AiAnswerContext context, String rewriteText) {
        // 获取医院简称和更新搜索站点
        List<String> initialSites = JSON.parseArray(searchSites, String.class);
        SearchQueryInfo queryInfo = buildSearchQueryInfo( rewriteText, initialSites);
        String formattedQuery = queryInfo.getQuery();
        final List<String> sites = queryInfo.getSites();

        // 先执行普通搜索
        CompletableFuture<SearchStrategyInfo> normalSearchFuture = CompletableFuture.supplyAsync(
            () -> querySearchInfo(formattedQuery, sites, commonSearchTopK),
            taskPool.getExecutor());

        // 同时异步执行RAG检索，设置超时
        CompletableFuture<SearchStrategyInfo> ragSearchFuture = executeRagSearch(formattedQuery)
            .completeOnTimeout(new SearchStrategyInfo(), ragTimeoutMs, TimeUnit.MILLISECONDS);

        // 组合两个Future，合并结果后判断相关性
        return normalSearchFuture.thenCombine(ragSearchFuture, (normalResult, ragResult) -> {
            // 合并搜索结果
            List<SearchStrategyInfo.BaseSearchStrategyInfo> mergedResults = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(normalResult.getSearchInfo())) {
                mergedResults.addAll(normalResult.getSearchInfo());
            }
            if (CollectionUtils.isNotEmpty(ragResult.getSearchInfo())) {
                mergedResults.addAll(ragResult.getSearchInfo());
            }

            // 如果合并后没有结果，直接返回空结果
            if (CollectionUtils.isEmpty(mergedResults)) {
                log.info("普通搜索和RAG检索均无结果");
                return new SearchStrategyInfo();
            }
            SearchStrategyInfo result = new SearchStrategyInfo();
            result.setSearchInfo(mergedResults);
            result.setIsValid(true);

            return result;
        }).exceptionally(e -> {
            log.error("搜索策略执行异常", e);
            // 发生异常时，尝试返回已完成的搜索结果
            try {
                if (normalSearchFuture.isDone()) {
                    return normalSearchFuture.join();
                }
            } catch (Exception ex) {
                log.error("获取普通搜索结果异常", ex);
            }
            return new SearchStrategyInfo();
        });
    }

    /**
     * 执行RAG检索
     */
    private CompletableFuture<SearchStrategyInfo> executeRagSearch(String formattedQuery) {
        return CompletableFuture.supplyAsync(() -> {
            Transaction transaction = Cat.newTransaction(getClass().getSimpleName(), "executeRagSearch");
            long startTime = System.currentTimeMillis();
            try {
                // 构建RAG检索请求
                Map<String, List<String>> metaMap = new HashMap<>();
                metaMap.put(MetadataKeyEnum.CHANNEL.getKey(), Lists.newArrayList("deep_search_result"));

                DocumentSearchRequest searchRequest = DocumentSearchRequest.builder()
                        .query(formattedQuery)
                        .topK(deepSearchResRagTopK)
                        .metaData(metaMap)
                        .build();

                RemoteResponse<List<DocumentDTO>> response = esVectorStoreService.similaritySearch(searchRequest);

                SearchStrategyInfo result = new SearchStrategyInfo();
                if (response.getSuccess() && CollectionUtils.isNotEmpty(response.getData())) {
                    List<SearchStrategyInfo.BaseSearchStrategyInfo> searchResults = response.getData().stream()
                            .map(doc -> {
                                SearchStrategyInfo.BaseSearchStrategyInfo info = new SearchStrategyInfo.BaseSearchStrategyInfo();
                                info.setSnippet(doc.getText());
                                info.setName(doc.getMetadata().getOrDefault(MetadataKeyEnum.TITLE.getKey(), "RAG检索结果"));
                                info.setDatePublished(doc.getMetadata().getOrDefault(MetadataKeyEnum.PUBLISH_TIME.getKey(), ""));

                                // 处理URL
                                String url = doc.getMetadata().getOrDefault(MetadataKeyEnum.URL.getKey(), "");
                                if (StringUtils.isNotBlank(url)) {
                                    info.setUrl(url);
                                }

                                // 处理多个URL
                                String urlsStr = doc.getMetadata().getOrDefault(MetadataKeyEnum.URLS.getKey(), "");
                                if (StringUtils.isNotBlank(urlsStr)) {
                                    try {
                                        List<String> urls = JSON.parseArray(urlsStr, String.class);
                                        if (CollectionUtils.isNotEmpty(urls)) {
                                            info.setUrls(urls);
                                        }
                                    } catch (Exception e) {
                                        log.error("解析URLs失败: {}", urlsStr, e);
                                    }
                                }

                                info.setFromDeepSearch(true);

                                return info;
                            })
                            .collect(toList());

                    result.setSearchInfo(searchResults);
                    result.setSpan(Collections.singletonList(Span.builder()
                            .key(getClass().getSimpleName() + ".RagSearch")
                            .value(JSON.toJSONString(ImmutableMap.of(
                                "request", searchRequest,
                                "response", response)))
                            .duration(System.currentTimeMillis() - startTime)
                            .build()));
                    log.info("RAG检索成功，查询: {}, 结果数量: {}, 耗时: {}ms",
                           formattedQuery, searchResults.size(), System.currentTimeMillis() - startTime);
                } else {
                    log.warn("RAG检索无结果或失败，查询: {}, 响应: {}", formattedQuery, response);
                }

                transaction.setSuccessStatus();
                return result;
            } catch (Exception e) {
                transaction.setStatus(e);
                log.error("执行RAG检索失败: {}", formattedQuery, e);
                return new SearchStrategyInfo();
            } finally {
                transaction.complete();
            }
        }, taskPool.getExecutor());
    }

    /**
     * 查询搜索信息
     */
    private SearchStrategyInfo querySearchInfo(String formattedQuery, List<String> sites,
                                               int topK) {
        Transaction transaction = Cat.newTransaction(getClass().getSimpleName(), "querySearchInfo");
        long startTime = System.currentTimeMillis();
        transaction.setSuccessStatus();
        try {
            List<FridaySearchResult> search = fridayWebSearchAcl.search(formattedQuery, sites, topK);
            SearchStrategyInfo searchStrategyInfo = new SearchStrategyInfo();
            if (CollectionUtils.isNotEmpty(search)) {
                List<SearchStrategyInfo.BaseSearchStrategyInfo> collectSearch = search.stream().filter(e -> StringUtils.isNotBlank(e.getSnippet()) || StringUtils.isNotBlank(e.getName())).map(e -> {
                    SearchStrategyInfo.BaseSearchStrategyInfo baseStrategyInfo =
                            new SearchStrategyInfo.BaseSearchStrategyInfo();
                    baseStrategyInfo.setUrl(e.getUrl());
                    baseStrategyInfo.setName(Optional.ofNullable(e.getName())
                            .filter(StringUtils::isNotBlank)
                            .orElse(e.getSnippet()));
                    baseStrategyInfo.setSnippet(e.getSnippet());
                    if (e.getDatePublished() != null) {
                        LocalDateTime dateTime = LocalDateTime.parse(e.getDatePublished());
                        baseStrategyInfo.setDatePublished(dateTime.toLocalDate().toString());
                    } else {
                        baseStrategyInfo.setDatePublished("");
                    }
                    return baseStrategyInfo;
                }).collect(toList());
                searchStrategyInfo.setSearchInfo(collectSearch);
            }
            searchStrategyInfo.setSpan(Collections.singletonList(Span.builder()
                    .key(getClass().getSimpleName())
                    .value(JSON.toJSONString(
                            ImmutableMap.of("rewriteText", formattedQuery, "sites", sites, "result", search)))
                    .duration(System.currentTimeMillis() - startTime)
                    .build()));
            return searchStrategyInfo;
        } catch (Exception e) {
            transaction.setStatus(e);
            return new SearchStrategyInfo();
        } finally {
            transaction.complete();
        }
    }

    /**
     * 构建搜索查询信息
     */
    private SearchQueryInfo buildSearchQueryInfo(String rewriteText, List<String> sites) {
        List<String> updatedSites = new ArrayList<>(sites);
        return new SearchQueryInfo(rewriteText, updatedSites);
    }

    /**
     * 判断意图是否需要医院名称
     * 基于海马配置中的功能需求分析
     */
    private boolean shouldIncludeHospitalName(String intentionKey) {
        // 需要医院上下文的意图：分诊、服务可及性确认、就诊指南
        return "triage".equals(intentionKey) ||
               "service".equals(intentionKey) ||
               "guide".equals(intentionKey);
    }

    /**
     * 搜索查询信息类
     */
    @Data
    private static class SearchQueryInfo {
        private final String query;
        private final List<String> sites;
        public SearchQueryInfo(String query, List<String> sites) {
            this.query = query;
            this.sites = sites;
        }
    }

    @Data
    @EqualsAndHashCode(callSuper = true)
    public static class SearchStrategyInfo extends BaseStrategyInfo {

        private List<BaseSearchStrategyInfo> searchInfo;

        /**
         * 搜索结果是否回答用户问题
         */
        private Boolean isValid;

        @Override
        public String toPrompt() {
            return collectSearchInfo(searchInfo);
        }

        @Data
        public static class BaseSearchStrategyInfo implements FilterableItem {

            @Deprecated
            private String url;
            private List<String> urls;

            private String snippet;

            private String name;

            private String datePublished;

            /**
             * 是否来自 RAG（deep_search_result）检索
             */
            private Boolean fromDeepSearch = false;

            public String getUrl() {
                if (url != null) {
                    return url;
                }
                return urls != null && !urls.isEmpty() ? urls.get(0) : null;
            }
            public void setUrl(String url) {
                this.url = url;
                if (this.urls == null) {
                    this.urls = new ArrayList<>();
                }
                if (url != null && !this.urls.contains(url)) {
                    this.urls.add(url);
                }
            }
            public List<String> getUrls() {
                return Objects.requireNonNullElseGet(urls, () -> url != null ? Collections.singletonList(url) : Collections.emptyList());
            }

            @Override
            public Map<String, String> getFieldsForEvaluation() {
                Map<String, String> fields = new LinkedHashMap<>();
                fields.put("标题", Optional.ofNullable(name).orElse(""));
                fields.put("内容", Optional.ofNullable(snippet).orElse(""));
                fields.put("发布时间", Optional.ofNullable(datePublished).orElse(""));
                return fields;
            }
        }
    }

    /**
     * 收集搜索信息
     */
    private static String collectSearchInfo(List<SearchStrategyInfo.BaseSearchStrategyInfo> results) {
        if (results == null || results.isEmpty()) {
            return "[]";
        }

        List<String> jsonResults = IntStream.range(0, results.size())
                .mapToObj(index -> {
                    SearchStrategyInfo.BaseSearchStrategyInfo result = results.get(index);
                    // 处理URL列表
                    String urlsJson = "[]";
                    List<String> urls = result.getUrls();
                    if (CollectionUtils.isNotEmpty(urls)) {
                        urlsJson = "[\"" + String.join("\",\"", urls) + "\"]";
                    }
                    return String.format(
                        "{\"序号\":\"%d\",\"标题\":\"%s\",\"内容\":\"%s\",\"发布时间\":\"%s\",\"类型\":\"%s\",\"URLs\":%s}",
                        index + 1, // 序号从1开始
                        Optional.ofNullable(result.getName()).orElse(""),
                        Optional.ofNullable(result.getSnippet()).orElse("").replace(" ", ""),
                        Optional.ofNullable(result.getDatePublished()).orElse(""),
                        "website",
                        urlsJson);
                })
                .collect(toList());

        return "[" + String.join(",", jsonResults) + "]";
    }

    @Override
    public SearchStrategyInfo filterResult(String rewriteText, SearchStrategyInfo strategyResult) {

        if (strategyResult == null || CollectionUtils.isEmpty(strategyResult.getSearchInfo())) {
            return strategyResult;
        }
        // 根据配置决定是否执行过滤
        if (!enableSearchResultFiltering) {
            log.debug("搜索结果过滤功能已禁用，返回原始结果");
            return strategyResult;
        }
        // 定义搜索结果专用的系统提示
        String systemMessageContent = "你是一个专业的搜索结果评估助手，专门负责评估网页搜索结果的相关性。" +
                "请评估每个搜索结果是否有助于回答用户的医疗健康问题。" +
                "评估标准：1)内容与问题高度相关 2)信息准确可靠 3)来源权威 4)内容完整有用。" +
                "返回格式：JSON数组，每个元素包含resultId（从1开始）和isValid（true/false）。" +
                "示例：[{\"resultId\":1,\"isValid\":true},{\"resultId\":2,\"isValid\":false}]";

        // 使用通用服务执行过滤
        List<SearchStrategyInfo.BaseSearchStrategyInfo> filteredSearchInfo = llmResultFilterService.filterResults(
                rewriteText,
                strategyResult.getSearchInfo(),
                systemMessageContent,
                retrieveStrategyResEvaChatClient
        );

        // 创建新的SearchStrategyInfo并返回
        SearchStrategyInfo result = new SearchStrategyInfo();
        result.setSearchInfo(filteredSearchInfo);
        result.setIsValid(!filteredSearchInfo.isEmpty());
        result.setSpan(strategyResult.getSpan());

        if (!filteredSearchInfo.isEmpty()) {
            if (filteredSearchInfo.stream().anyMatch(info -> Boolean.TRUE.equals(info.getFromDeepSearch()))) {
                Cat.logEvent("SearchResultFilter", "contains_deep_search");
            }else {
                Cat.logEvent("SearchResultFilter", "only_contains_web_search");
            }
        }
        return result;
    }
}