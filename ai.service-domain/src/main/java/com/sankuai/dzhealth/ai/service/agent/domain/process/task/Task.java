package com.sankuai.dzhealth.ai.service.agent.domain.process.task;

import com.sankuai.dzhealth.ai.service.agent.domain.context.TaskContext;
import com.sankuai.dzhealth.ai.service.agent.domain.model.TaskProcessResult;

/**
 * @author:chenwei
 * @time: 2025/7/9 10:47
 * @version: 0.0.1
 */
public interface Task {

    boolean accept(String type);


    TaskProcessResult process(TaskContext context);

    void after(TaskProcessResult result);






}
