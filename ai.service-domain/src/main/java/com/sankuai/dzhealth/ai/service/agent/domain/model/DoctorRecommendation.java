package com.sankuai.dzhealth.ai.service.agent.domain.model;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class DoctorRecommendation  {

    private Long doctorId;

    private String reason;

    private List<String> reviewURL;

    private List<String> reviewPicUrl;

    private List<Long> reviewIdList;

    private List<String> reviewContext;

    private String name;

    private String promoPrice;

    private String saleNum;

    private String detailId;

    private String detailLink;
}
