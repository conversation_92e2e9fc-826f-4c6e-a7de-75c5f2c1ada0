package com.sankuai.dzhealth.ai.service.domain.chat.client;


import com.dianping.lion.Environment;
import com.meituan.service.inf.kms.client.Kms;
import com.meituan.service.inf.kms.utils.KmsResultNullException;
import com.sankuai.dzhealth.ai.service.domain.chat.client.advisor.CustomMessageChatMemoryAdvisor;
import com.sankuai.dzhealth.ai.service.domain.memory.DatabaseChatMemory;
import com.sankuai.dzhealth.ai.service.infrastructure.chatmodel.OneApiChatModel;
import io.micrometer.observation.ObservationRegistry;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.openai.OpenAiChatOptions;
import org.springframework.ai.openai.api.OpenAiApi;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

@Component
public class RecommendQuestionChatConfig {

    @Bean("recommendQuestionChatClient")
    public ChatClient recommendQuestionChatClient() throws KmsResultNullException {
        OpenAiApi openAiApi = new OpenAiApi.Builder()
                .completionsPath("/chat/completions")
                .baseUrl("https://aigc.sankuai.com/v1/openai/native")
                .apiKey(Kms.getByName(Environment.getAppName(), "friday.search.appId"))
                .build();
        OpenAiChatOptions openAiChatOptions = OpenAiChatOptions.builder()
                .model("deepseek-v3-friday")
                .temperature(0.0)
                .maxTokens(20000)
                .streamUsage(true)
                .build();
        OneApiChatModel chatModel = OneApiChatModel.builder()
                .openAiApi(openAiApi)
                .defaultOptions(openAiChatOptions)
                .build();

        return ChatClient.builder(chatModel).build();
    }
}
