package com.sankuai.dzhealth.ai.service.agent.domain.process.card.data;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.HashMap;
import java.util.Map;

import static com.dianping.lion.client.util.StringUtils.isNotEmpty;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ProductCardData {





    private Long goodsId;

    /**
     * 推荐理由
     */
    private String reason;

    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 商品详情页跳转链接
     */
    private String goodsRedirectUrl;

    /**
     * 门店名称
     */
    private String shopName;

    /**
     * 商品头图url
     */
    private String goodsHeadUrl;

    /**
     * 划线价
     */
    private String goodsOriginalPrice;

    /**
     * 到手价
     */
    private String goodsFinalPrice;

    /**
     * 距离
     */
    private String shopDistance;

    /**
     * 商圈
     */
    private String shopBusinessCircle;

    /**
     * 核销量
     */
    private String goodsVerifiedSales;



    public Map<String, Object> toMap() {
        Map<String, Object> map = new HashMap<>();
        map.put("goodsId", goodsId);
        map.put("reason",this.reason);
        map.put("goodsName",this.goodsName);
        map.put("goodsRedirectUrl",this.goodsRedirectUrl);
        map.put("shopName",this.shopName);
        map.put("goodsHeadUrl",this.goodsHeadUrl);
        map.put("goodsOriginalPrice",this.goodsOriginalPrice);
        map.put("goodsFinalPrice",this.goodsFinalPrice);
        map.put("shopDistance",this.shopDistance);
        map.put("goodsVerifiedSales",this.goodsVerifiedSales);
        map.put("shopBusinessCircle",this.shopBusinessCircle);
        return map;
    }

    /**
     * 检查所有属性是否都有值
     * @return true表示所有属性都有值，false表示存在空值
     */
    public boolean hasAllValues() {
        return goodsId != null
                && isNotEmpty(this.reason)
                && isNotEmpty(this.goodsName)
                && isNotEmpty(this.goodsRedirectUrl)
                && isNotEmpty(this.shopName)
                && isNotEmpty(this.goodsHeadUrl)
                && isNotEmpty(this.goodsOriginalPrice)
                && isNotEmpty(this.goodsFinalPrice)
                && isNotEmpty(this.shopDistance)
                && isNotEmpty(this.goodsVerifiedSales);
    }





}
