package com.sankuai.dzhealth.ai.service.domain.service.impl.xhs;

import com.sankuai.dzhealth.ai.service.api.xhs.XhsNoteCallbackService;
import com.sankuai.dzhealth.ai.service.domain.entity.xhs.XhsNoteGenerateDTO;
import com.sankuai.dzhealth.ai.service.domain.utils.JsonUtils;
import com.sankuai.dzhealth.ai.service.dto.xhs.XhsNoteResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 小红书笔记生成器 - 提供通用的笔记生成功能
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
public class XhsNoteGenerator {

    @Resource
    private ChatClient xhsNoteLLMClient;

    @Autowired
    private XhsNoteStoreDomainService xhsNoteStoreDomainService;

    /**
     * 生成单篇小红书笔记
     * 
     * @param notePrompt 笔记生成的提示词
     * @param coverPrompt 封面生成的提示词
     * @param batch 批次ID
     * @param bizCode 业务代码
     * @param isMedical 是否为医美类笔记
     * @param callbackService 回调服务
     */
    public void generateSingleNote(String notePrompt, 
                                  String coverPrompt, 
                                  String batch,
                                  String bizCode, 
                                  boolean isMedical,
                                  XhsNoteCallbackService callbackService,
                                   String msg) {
        try {

            String content = xhsNoteLLMClient.prompt().user(notePrompt).call().content();
            String processedContent = processMdJson(content);
            List<String> titleList = JsonUtils.getStringList(processedContent, "title");
            String noteContent = JsonUtils.getString(processedContent, "content");


            String finalCoverPrompt = coverPrompt
                    .replace("{{{title}}}", JsonUtils.toJsonString(titleList))
                    .replace("{{{content}}}", noteContent);
            String coverContent = xhsNoteLLMClient.prompt().user(finalCoverPrompt).call().content();
            String processedCoverContent = processMdJson(coverContent);
            String subTitle = JsonUtils.getString(processedCoverContent, "subTitle");
            String keyPoint = JsonUtils.getString(processedCoverContent, "keyPoint");
            List<String> topicList = JsonUtils.getStringList(processedCoverContent, "topic");


            XhsNoteResponse noteResponse = new XhsNoteResponse();
            noteResponse.setTitleList(titleList);
            noteResponse.setContent(noteContent);
            noteResponse.setSubTitle(subTitle);
            noteResponse.setKeypoint(keyPoint);
            noteResponse.setTopicList(topicList);
            noteResponse.setBatch(batch);
            noteResponse.setBizCode(bizCode);
            noteResponse.setErrorMessage(msg);
            noteResponse.setStatus("SUCCESS");


            XhsNoteGenerateDTO xhsNoteGenerateDTO = buildStoreDTO(noteResponse, isMedical);
            xhsNoteStoreDomainService.saveGenerateNote(xhsNoteGenerateDTO);


            if (callbackService != null) {
                callbackService.onNoteGenerated(noteResponse);
            }

        } catch (Exception e) {
            log.error("Error generating note for batchId: {}, bizCode: {}", batch, bizCode, e);
            throw new RuntimeException("Failed to generate note", e);
        }
    }

    /**
     * 处理Markdown格式的JSON字符串
     */
    private String processMdJson(String mdJson) {
        if (mdJson == null || mdJson.trim().isEmpty()) {
            return "";
        }
        return mdJson.replace("```", "").replace("json", "").trim();
    }

    /**
     * 构建存储DTO对象
     */
    private XhsNoteGenerateDTO buildStoreDTO(XhsNoteResponse response, boolean isMedical) {
        XhsNoteGenerateDTO noteStoreDTO = new XhsNoteGenerateDTO();
        noteStoreDTO.setStatus(0);
        noteStoreDTO.setContent(response.getContent());
        noteStoreDTO.setTitle(JsonUtils.toJsonString(response.getTitleList()));
        noteStoreDTO.setBatch(response.getBatch());
        noteStoreDTO.setSubTitle(response.getSubTitle());
        noteStoreDTO.setKeyPoint(response.getKeypoint());
        noteStoreDTO.setTopic(response.getTopicList());
        noteStoreDTO.setIsMedicalBeauty(String.valueOf(isMedical));
        return noteStoreDTO;
    }
} 