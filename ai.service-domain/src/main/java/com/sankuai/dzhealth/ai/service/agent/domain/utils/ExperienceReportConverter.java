package com.sankuai.dzhealth.ai.service.agent.domain.utils;

import com.sankuai.beautycontent.experience.dto.AbDTO;
import com.sankuai.beautycontent.experience.dto.ExperiencePicInfo;
import com.sankuai.beautycontent.experience.dto.ExperienceReportVO;
import com.sankuai.beautycontent.experience.request.ExperienceReportRequest;
import com.sankuai.dzhealth.ai.service.agent.domain.model.experiencereport.*;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 体验报告模型转换工具类
 * <p>
 * 负责在基础设施层 DTO 和领域层 Model 之间进行转换
 *
 * <AUTHOR>
 * @version 0.0.1
 */
public class ExperienceReportConverter {

    private ExperienceReportConverter() {
        // 私有构造函数，防止实例化
    }

    /**
     * 将 domain 请求模型转换为 infrastructure 请求 DTO
     *
     * @param requestModel 领域请求模型
     * @return infrastructure 请求 DTO
     */
    public static ExperienceReportRequest toInfrastructureRequest(ExperienceReportRequestModel requestModel) {
        if (requestModel == null) {
            return null;
        }

        ExperienceReportRequest request = new ExperienceReportRequest();
        request.setPlatform(requestModel.getPlatform());
        request.setCityId(requestModel.getCityId());
        request.setShopId(requestModel.getShopId());
        request.setProductId(requestModel.getProductId());
        request.setOffset(requestModel.getOffset());
        request.setLimit(requestModel.getLimit());
        request.setDeviceId(requestModel.getDeviceId());
        request.setInApp(requestModel.getInApp());
        request.setType(requestModel.getType());
        request.setTechId(requestModel.getTechId());
        request.setTechIds(requestModel.getTechIds());
        request.setSearchWord(requestModel.getSearchWord());
        request.setUserId(requestModel.getUserId());
        request.setLat(requestModel.getLat());
        request.setLng(requestModel.getLng());
        request.setFilterTagIds(requestModel.getFilterTagIds());
        request.setAiRecommendTags(requestModel.getAiRecommendTags());
        request.setMergeDoctorId(requestModel.getMergeDoctorId());
        request.setMergeDoctorIds(requestModel.getMergeDoctorIds());
        request.setReportIds(requestModel.getReportIds());
        request.setPageNo(requestModel.getPageNo());

        return request;
    }

    /**
     * 将 ExperienceReportVO 转换为 ExperienceReportModel
     *
     * @param vo 体验报告 VO
     * @return 体验报告领域模型
     */
    public static ExperienceReportModel toExperienceReportModel(ExperienceReportVO vo) {
        if (vo == null) {
            return null;
        }

        return ExperienceReportModel.builder()
                .title(vo.getTitle())
                .reportNum(vo.getReportNum())
                .detailUrl(vo.getDetailUrl())
                .picList(toExperiencePicModelList(vo.getPicList()))
                .showModuleInfo(toAbModel(vo.getShowModuleInfo()))
                .build();
    }

    /**
     * 将 ExperiencePicInfo 列表转换为 ExperiencePicModel 列表
     *
     * @param picInfoList 图片信息列表
     * @return 图片信息领域模型列表
     */
    private static List<ExperiencePicModel> toExperiencePicModelList(List<ExperiencePicInfo> picInfoList) {
        if (CollectionUtils.isEmpty(picInfoList)) {
            return new ArrayList<>();
        }

        return picInfoList.stream()
                .map(ExperienceReportConverter::toExperiencePicModel)
                .collect(Collectors.toList());
    }

    /**
     * 将 ExperiencePicInfo 转换为 ExperiencePicModel
     *
     * @param picInfo 图片信息 DTO
     * @return 图片信息领域模型
     */
    private static ExperiencePicModel toExperiencePicModel(ExperiencePicInfo picInfo) {
        if (picInfo == null) {
            return null;
        }

        return ExperiencePicModel.builder()
                .picId(picInfo.getPicId())
                .url(picInfo.getUrl())
                .label(picInfo.getLabel())
                .type(picInfo.getType())
                .noteId(picInfo.getNoteId())
                .jumpUrl(picInfo.getJumpUrl())
                .productId(picInfo.getProductId())
                .dpShopId(picInfo.getDpShopId())
                .price(picInfo.getPrice())
                .title(picInfo.getTitle())
                .build();
    }

    /**
     * 将 AbDTO 转换为 AbModel
     *
     * @param abDTO AB实验 DTO
     * @return AB实验领域模型
     */
    private static AbModel toAbModel(AbDTO abDTO) {
        if (abDTO == null) {
            return null;
        }

        return AbModel.builder()
                .abId(abDTO.getAbId())
                .queryId(abDTO.getQueryId())
                .expStrategy(abDTO.getExpStrategy())
                .build();
    }

    /**
     * 将体验图片列表转换为AI案例列表
     * 按noteId分组，将同一案例的体验前后图片配对
     *
     * @param picList 体验图片列表
     * @return AI案例列表
     */
    public static List<AiCaseInfo> transformPicListToCaseList(List<ExperiencePicModel> picList) {
        if (CollectionUtils.isEmpty(picList)) {
            return new ArrayList<>();
        }

        // 按noteId分组
        Map<Long, List<ExperiencePicModel>> caseMap = picList.stream()
                .filter(pic -> pic.getNoteId() != null)
                .collect(Collectors.groupingBy(
                        ExperiencePicModel::getNoteId,
                        LinkedHashMap::new,
                        Collectors.toList()
                ));

        List<AiCaseInfo> caseList = new ArrayList<>();

        for (Map.Entry<Long, List<ExperiencePicModel>> entry : caseMap.entrySet()) {
            Long noteId = entry.getKey();
            List<ExperiencePicModel> pics = entry.getValue();

            // 找体验前的图片 (type=0)
            ExperiencePicModel beforePic = pics.stream()
                    .filter(pic -> Objects.equals(pic.getType(), 0))
                    .findFirst().orElse(null);

            // 找体验后的图片 (type=1)
            ExperiencePicModel afterPic = pics.stream()
                    .filter(pic -> Objects.equals(pic.getType(), 1))
                    .findFirst().orElse(null);

            // 如果前后图片都存在，才构建案例
            if (beforePic != null && afterPic != null) {
                AiCaseInfo caseInfo = AiCaseInfo.builder()
                        .caseId(noteId)
                        .beforeImage(AiCaseInfo.ImageInfo.builder()
                                .url(beforePic.getUrl())
                                .label("前")
                                .picId(beforePic.getPicId())
                                .build())
                        .afterImage(AiCaseInfo.ImageInfo.builder()
                                .url(afterPic.getUrl())
                                .label("后")
                                .picId(afterPic.getPicId())
                                .build())
                        .jumpUrl(beforePic.getJumpUrl()) // 使用任意一个的jumpUrl即可
                        .productId(beforePic.getProductId())
                        .dpShopId(beforePic.getDpShopId())
                        .price(beforePic.getPrice())
                        .title(beforePic.getTitle()) // 添加体验报告标题
                        .build();

                caseList.add(caseInfo);
            }
        }

        return caseList;
    }
}

