package com.sankuai.dzhealth.ai.service.domain.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONValidator;
import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.dianping.haima.entity.haima.HaimaContent;
import com.meituan.mdp.boot.starter.config.annotation.MdpConfig;
import com.sankuai.dzhealth.ai.service.domain.utils.context.AiAnswerContext;
import com.sankuai.dzhealth.ai.service.dto.HospitalCardDTO;
import com.sankuai.dzhealth.ai.service.dto.TagItemDTO;
import com.sankuai.dzhealth.ai.service.infrastructure.acl.HaimaAcl;
import com.sankuai.dzhealth.ai.service.infrastructure.observability.Span;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.Serializable;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> youtianyu
 * @version : 0.1
 * @since : 2025/4/1
 */
@Service
@Slf4j
public class HospitalRecommendService {

    @MdpConfig("hospital_recommend_max_input_hospital:50")
    private Integer maxInputHospital = 50;

    @Autowired
    private ChatClient recommendChatClient;

    @Autowired
    private HaimaAcl haimaAcl;

    public List<HospitalCardDTO> recommend(AiAnswerContext context,
                                           String query,
                                           List<HospitalCardDTO> hospitalCardDTOS) {
        Transaction transaction = Cat.newTransaction(getClass().getSimpleName(), "recommend");
        transaction.setSuccessStatus();
        try {
            if (CollectionUtils.isEmpty(hospitalCardDTOS)) {
                return Collections.emptyList();
            }
            if (hospitalCardDTOS.size() > maxInputHospital) {
                hospitalCardDTOS = hospitalCardDTOS.subList(0, maxInputHospital);
            }
            String promptTemplate = getPromptTemplate().orElseThrow(
                    () -> new RuntimeException("hospital_recommend promptTemplate is null"));

            Map<String, HospitalCardDTO> hospitalCardDTOMap = hospitalCardDTOS.stream()
                    .collect(Collectors.toMap(HospitalCardDTO::getHospitalName, a -> a, (a, b) -> a));

            String hospitalPrompt = toPrompt(hospitalCardDTOS);
            String prompt = promptTemplate.replace("${query}", query).replace("${hospitals}", hospitalPrompt);

            context.addSpan(Span.builder().key(getClass().getSimpleName() + "/prompt").value(prompt).build());

            String json = recommendChatClient.prompt().user(prompt).call().content();
            List<RecommendHospital> recommendHospitals = Collections.emptyList();
            try (JSONValidator validator = JSONValidator.from(json)) {
                if (Objects.equals(validator.getType(), JSONValidator.Type.Array)) {
                    recommendHospitals = JSON.parseArray(json, RecommendHospital.class);
                }
            }
            context.addSpan(Span.builder().key(getClass().getSimpleName() + "/result").value(json).build());

            return recommendHospitals.stream().map(hospital -> {
                HospitalCardDTO hospitalCardDTO = hospitalCardDTOMap.get(hospital.getHospitalName());
                if (hospitalCardDTO == null) {
                    return null;
                }
                Optional.ofNullable(hospitalCardDTO.getDepartmentCardDTOList())
                        .orElse(Collections.emptyList())
                        .stream()
                        .filter(departmentCardDTO -> Objects.equals(departmentCardDTO.getDepartmentName(),
                                hospital.getDepartmentName()))
                        .findFirst()
                        .ifPresent(departmentCardDTO -> {
                            hospitalCardDTO.setDepartmentName(departmentCardDTO.getDepartmentName());
                            hospitalCardDTO.setDepartDetailUrl(departmentCardDTO.getDepartDetailUrl());
                            hospitalCardDTO.setRegisterCanTime(departmentCardDTO.getRegisterCanTime());
                            hospitalCardDTO.setRegisterStatus(departmentCardDTO.getRegisterStatus());
                            hospitalCardDTO.setButtonText(departmentCardDTO.getButtonText());
                        });
                return hospitalCardDTO;
            }).filter(Objects::nonNull).collect(Collectors.toList());
        } catch (Exception e) {
            log.error("recommend, query={},hospitalCardDTOS={},e={}", query, JSON.toJSONString(hospitalCardDTOS), e, e);
            transaction.setStatus(e);
            return Collections.emptyList();
        } finally {
            transaction.complete();
        }
    }

    private Optional<String> getPromptTemplate() {
        List<HaimaContent> haimaContents = haimaAcl.getContent("ai_hospital_instruction", null);
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(haimaContents)) {
            Cat.logEvent("refreshPrompt", "empty");
            return Optional.empty();
        }
        return haimaContents.stream()
                .filter(content -> "hospital_recommend".equals(content.getContentString("promptKey")))
                .map(content -> content.getContentString("promptContent"))
                .findFirst();
    }

    @Data
    public static class RecommendHospital implements Serializable {
        private String hospitalName;

        private String departmentName;

    }

    public String toPrompt(List<HospitalCardDTO> hospitalCardDTOS) {
        if (CollectionUtils.isEmpty(hospitalCardDTOS)) {
            return "[]";
        }

        List<Map<String, Object>> promptList = hospitalCardDTOS.stream().map(hospital -> {
            Map<String, Object> hospitalMap = new HashMap<>();
            // 医院基本信息
            hospitalMap.put("医院名称", hospital.getHospitalName());
            hospitalMap.put("距离", hospital.getDistance());
            hospitalMap.put("地址", hospital.getAddress());
            hospitalMap.put("推荐理由", hospital.getRecommendReason());

            // 医院标签
            if (!CollectionUtils.isEmpty(hospital.getHospitalTags())) {
                List<String> tags = hospital.getHospitalTags()
                        .stream()
                        .map(TagItemDTO::getText)
                        .filter(StringUtils::isNotBlank)
                        .collect(Collectors.toList());
                hospitalMap.put("医院标签", tags);
            }

            // 科室信息
            if (!CollectionUtils.isEmpty(hospital.getDepartmentCardDTOList())) {
                List<Map<String, String>> departments = hospital.getDepartmentCardDTOList().stream().map(dept -> {
                    Map<String, String> deptMap = new HashMap<>();
                    deptMap.put("科室名称", dept.getDepartmentName());
                    deptMap.put("可挂号时间", dept.getRegisterCanTime());
                    deptMap.put("推荐理由", dept.getRecommendReason());
                    return deptMap;
                }).collect(Collectors.toList());
                hospitalMap.put("科室列表", departments);
            }

            return hospitalMap;
        }).collect(Collectors.toList());

        return JSON.toJSONString(promptList);
    }
}
