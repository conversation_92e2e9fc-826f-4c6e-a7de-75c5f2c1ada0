package com.sankuai.dzhealth.ai.service.domain.enums;

import java.util.Arrays;
import java.util.Objects;

/**
 * @Author: zhongchangze
 * @Date: 2025/3/26 15:29
 * @Description:
 */
public enum SummaryResultType {
    SUMMARY_FAILED(-1, "总结失败"),
    ANSWER_NOT_REPLY(0, "接听未回复"),
    ANSWER_REPLY_VALID(1, "接听并回复,信息有效"),
    ANSWER_REPLY_INFO_NOT_COMPLETE(2, "接听并回复,信息不全"),
    ANSWER_REPLY_INFO_CONTAIN_NEGATIVE(3, "接听并回复,信息包含负面情绪"),
    ANSWER_REPLY_HAVE_NO_IDEA(4, "接听并回复无法解答");

    public int getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }

    private final int type;

    private final String desc;

    SummaryResultType(int type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public static SummaryResultType getResultType(Integer type) {
        return Arrays.stream(values()).filter(SummaryResultType -> Objects.equals(SummaryResultType.type, type)).findFirst().orElse(null);
    }
}