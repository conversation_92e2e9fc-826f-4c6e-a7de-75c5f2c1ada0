package com.sankuai.dzhealth.ai.service.domain.memory;

import com.sankuai.dzhealth.ai.service.infrastructure.repository.conversation.ChatMessageRepository;
import com.sankuai.dzhealth.ai.service.infrastructure.dal.entity.ChatMessageEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.memory.ChatMemory;
import org.springframework.ai.chat.messages.AssistantMessage;
import org.springframework.ai.chat.messages.Message;
import org.springframework.ai.chat.messages.MessageType;
import org.springframework.ai.chat.messages.UserMessage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
public class DatabaseChatMemory implements ChatMemory {
    @Autowired
    private ChatMessageRepository messageRepository;

    @Override
    public void add(String conversationId, List<Message> messages) {
        try {
            if (messages == null || messages.isEmpty()) {
                log.warn("尝试保存空消息列表, conversationId: {}", conversationId);
                return;
            }

            List<ChatMessageEntity> entities = messages.stream()
                .map(message -> {
                    Map<String, Object> metadata = message.getMetadata();
                    // 直接使用前端传递的parentMessageId，不进行内容解析
                    return ChatMessageEntity.builder()
                        .messageId(metadata.getOrDefault("messageId", UUID.randomUUID().toString()).toString())
                        .conversationId(conversationId)
                        .role(message.getMessageType().name())
                        .content(message.getText())
                        .sequence((Long)metadata.getOrDefault("sequence", System.currentTimeMillis()))
                        .createdAt((Date)metadata.getOrDefault("createdAt", new Date()))
                        .contentType((Byte)metadata.getOrDefault("contentType", (byte)1))
                        .auditStatus((Byte)metadata.getOrDefault("auditStatus", (byte)1))
                        .senderId(metadata.getOrDefault("senderId", "system").toString())
                        .ttft(metadata.get("ttft") != null ? (Long)metadata.get("ttft") : null)
                        .e2eTime(metadata.get("e2eTime") != null ? (Long)metadata.get("e2eTime") : null)
                        .parentMessageId((String)metadata.get("parentMessageId"))
                        .extraData((String)metadata.get("extraData"))
                        .build();
                })
                .collect(Collectors.toList());

            log.info("正在保存 {} 条消息到数据库, conversationId: {}", entities.size(), conversationId);
            messageRepository.saveAll(entities);
            log.info("消息保存成功, conversationId: {}", conversationId);
        } catch (Exception e) {
            log.error("保存消息到数据库失败: {}", e.getMessage(), e);
        }
    }

    @Override
    public List<Message> get(String conversationId) {
        return get(conversationId, 10);
    }

    public List<Message> get(String conversationId, int lastN) {
        try {
            log.info("获取会话 {} 的最近 {} 条消息", conversationId, lastN);
            List<Message> messages = messageRepository.findLatestMessages(conversationId, lastN)
                .stream()
                .map(entity -> {
                    // 构建metadata
                    Map<String, Object> metadata = new HashMap<>();
                    metadata.put("messageId", entity.getMessageId());
                    metadata.put("senderId", entity.getSenderId());
                    metadata.put("ttft", entity.getTtft());
                    metadata.put("e2eTime", entity.getE2eTime());
                    metadata.put("parentMessageId", entity.getParentMessageId());
                    metadata.put("extraData", entity.getExtraData());
                    // 使用正确的构造函数
                    return MessageType.USER.name().equals(entity.getRole()) ?
                        new UserMessage.Builder().text(entity.getContent()).media(new ArrayList<>()).metadata(metadata).build() :
                        new AssistantMessage(entity.getContent(), metadata);
                })
                .collect(Collectors.toList());
            log.info("成功获取到 {} 条消息", messages.size());
            return messages;
        } catch (Exception e) {
            log.error("获取会话消息失败: {}", e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    @Override
    public void clear(String conversationId) {
        try {
            log.info("清除会话 {} 的所有消息", conversationId);
            messageRepository.deleteByConversationId(conversationId);
        } catch (Exception e) {
            log.error("清除会话消息失败: {}", e.getMessage(), e);
        }
    }
}
