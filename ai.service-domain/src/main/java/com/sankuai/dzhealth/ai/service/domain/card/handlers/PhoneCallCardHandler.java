package com.sankuai.dzhealth.ai.service.domain.card.handlers;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.sankuai.dzhealth.ai.service.domain.card.AbstractCardHandler;
import com.sankuai.dzhealth.ai.service.domain.service.MtPhoneCallService;
import com.sankuai.dzhealth.ai.service.domain.utils.JsonUtils;
import com.sankuai.dzhealth.ai.service.domain.utils.context.AiAnswerContext;
import com.sankuai.dzhealth.ai.service.domain.utils.context.MtPhoneCallContext;
import com.sankuai.dzhealth.ai.service.dto.CallCardDTO;
import com.sankuai.dzhealth.ai.service.dto.stream.StreamEventDTO;
import com.sankuai.dzhealth.ai.service.dto.stream.StreamEventDataDTO;
import com.sankuai.dzhealth.ai.service.enums.StreamEventCardTypeEnum;
import com.sankuai.dzhealth.ai.service.enums.StreamEventDataTypeEnum;
import com.sankuai.dzhealth.ai.service.enums.StreamEventTypeEnum;
import com.sankuai.dzhealth.ai.service.infrastructure.dal.entity.ChatMessageEntity;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.IOException;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 电话调用卡片处理器 - 第二优先级，因为电话调用也很重要
 */
@Slf4j
@Component
@Order(3)
public class PhoneCallCardHandler extends AbstractCardHandler {

    @Autowired
    private MtPhoneCallService mtPhoneCallService;
    
    @Override
    public boolean supports(String fieldName) {
        return "phone".equals(fieldName);
    }
    
    @Override
    public List<StreamEventDTO> handle(JSONObject jsonObject, 
                                    AiAnswerContext context, 
                                    SseEmitter sseEmitter, 
                                    AtomicInteger sseIndex,
                                    long startChatTime) {
        String phone = jsonObject.getString("phone");
        List<StreamEventDTO> reply = new ArrayList<>();
        String continueAsk = jsonObject.getString("continue_ask");
        if (StringUtils.isNotBlank(continueAsk)) {
            JSONObject continueAskJson = JSON.parseObject(continueAsk);
            String choiceTitle = continueAskJson.getString("choice_title");
            String singleSelect = continueAskJson.getString("single_select");
            if (StringUtils.isNotBlank(choiceTitle) || StringUtils.isNotBlank(singleSelect)) {
                return Collections.emptyList();
            }
        }
        
        if ("true".equals(phone) && CollectionUtils.isEmpty(reply)) {
            MtPhoneCallContext phoneCallContext = MtPhoneCallContext.builder()
                    .sessionId(context.getSessionId())
                    .shopId(context.getMtShopId())
                    .question(context.getContent())
                    .userId(String.valueOf(context.getUserId()))
                    .build();
            
            try {
                ChatMessageEntity messageEntity = mtPhoneCallService.generateCallTask(phoneCallContext);
                if (messageEntity != null) {
                    Long msgId = messageEntity.getId();
                    String msgContent = messageEntity.getContent();
                    
                    // 添加消息ID事件
                    StreamEventDTO eventIdDTO = new StreamEventDTO();
                    eventIdDTO.setType(StreamEventTypeEnum.PHONE.getType());
                    StreamEventDataDTO eventIdDataDTO = new StreamEventDataDTO();
                    eventIdDataDTO.setEvent(StreamEventDataTypeEnum.REPLY_MESSAGE_ID.getType());
                    eventIdDataDTO.setContent(String.valueOf(msgId));
                    eventIdDTO.setData(eventIdDataDTO);
                    reply.add(eventIdDTO);
                    
                    // 添加电话卡片事件
                    Map<String, Object> cardProps = new HashMap<>();
                    CallCardDTO callCardDTO = JSON.parseObject(msgContent, CallCardDTO.class);
                    cardProps.put("callCardDTO", callCardDTO);
                    
                    StreamEventDTO telephoneConsult = sendCardEvent(
                            StreamEventCardTypeEnum.TELEPHONE, 
                            "TelephoneConsult", 
                            cardProps, 
                            sseEmitter, 
                            sseIndex.getAndIncrement()
                    );
                    
                    addIfNotNull(reply, telephoneConsult);
                }
            } catch (Exception e) {
                log.error("phone_call={}", JSON.toJSONString(phoneCallContext), e);
                StreamEventDTO eventDTO = new StreamEventDTO();
                eventDTO.setType(StreamEventTypeEnum.MESSAGE.getType());
                eventDTO.setIndex(sseIndex.getAndIncrement());

                StreamEventDataDTO dataDTO = new StreamEventDataDTO();
                dataDTO.setEvent(StreamEventDataTypeEnum.MAIN_TEXT.getType());
                dataDTO.setContent("非常抱歉，当前问题暂时无法回答~");
                dataDTO.setCardsData(new ArrayList<>());
                eventDTO.setData(dataDTO);
                try {
                    sseEmitter.send(eventDTO);
                    reply.add(eventDTO);
                } catch (IOException ex) {
                    log.error("问医院兜底文案发送失败={}", JsonUtils.toJsonString(phoneCallContext), e);
                }

            }
        }
        
        return reply;
    }

    @Override
    public int getOrder() {
        return 3;
    }
} 