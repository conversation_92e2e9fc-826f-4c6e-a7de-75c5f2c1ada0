package com.sankuai.dzhealth.ai.service.agent.domain.utils;

import com.meituan.mdp.boot.starter.util.Pair;
import com.sankuai.dzhealth.ai.service.agent.domain.buffer.MessageBufferEntity;
import com.sankuai.dzhealth.ai.service.agent.domain.buffer.label.labelext.FormatLabel;
import com.sankuai.dzhealth.ai.service.agent.domain.context.FormatLabelContext;
import com.sankuai.dzhealth.ai.service.agent.domain.context.MessageContext;
import com.sankuai.dzhealth.ai.service.agent.domain.process.card.CardBuilder;
import com.sankuai.dzhealth.ai.service.dto.stream.StreamEventCardDataDTO;
import com.sankuai.dzhealth.ai.service.dto.stream.StreamEventDTO;
import com.sankuai.dzhealth.ai.service.dto.stream.StreamEventDataDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @author:chenwei
 * @time: 2025/7/8 12:21
 * @version: 0.0.1
 */

@Component
@Slf4j
public class ContentBuilderUtils {

    @Autowired
    private List<FormatLabel> formatLabelList;

    @Autowired
    private List<CardBuilder> cardBuilderList;

    public StreamEventDTO buildEvent(StreamEventDataDTO streamEventDataVO, MessageContext context) {

        if (streamEventDataVO == null) {
            return null;
        }

        paddingCard(streamEventDataVO, context);
        StreamEventDTO streamEventDTO = new StreamEventDTO();
        streamEventDTO.setType("message");
        streamEventDTO.setData(streamEventDataVO);

        return streamEventDTO;

    }

    public String formatLabels(Pair<String, String> label, String tagContent, String data, List<MessageBufferEntity> entityList) {
        if (label == null || StringUtils.isEmpty(label.getKey())) {
            return StringUtils.EMPTY;
        }

        FormatLabel formatLabel = formatLabelList.stream()
                .filter(ext -> ext.needFormat(label.getKey()))
                .findFirst()
                .orElse(null);

        if (formatLabel == null) {
            return StringUtils.EMPTY;
        }

        FormatLabelContext formatLabelContext = new FormatLabelContext();
//        formatLabelContext.setContext(context);
        formatLabelContext.setLabelContent(tagContent);
        formatLabelContext.setData(data);
        formatLabelContext.setLabel(label);

        return formatLabel.format(formatLabelContext);
    }

    private void paddingCard(StreamEventDataDTO streamEventDataDTO, MessageContext context) {

        if (CollectionUtils.isEmpty(streamEventDataDTO.getCardsData())) {
            return;
        }

        for (StreamEventCardDataDTO cardDataDTO : streamEventDataDTO.getCardsData()) {
            CardBuilder cardBuilder = getCardBuilder(cardDataDTO);
            if (cardBuilder == null) {
                continue;
            }
            cardBuilder.padding(cardDataDTO, context);
        }
    }

    private CardBuilder getCardBuilder(StreamEventCardDataDTO cardDataDTO) {
        if (cardDataDTO == null || StringUtils.isBlank(cardDataDTO.getType())) {
            return null;
        }
        return cardBuilderList.stream()
                .filter(ext -> ext.accept(cardDataDTO.getType()))
                .findFirst()
                .orElse(null);
    }
}
