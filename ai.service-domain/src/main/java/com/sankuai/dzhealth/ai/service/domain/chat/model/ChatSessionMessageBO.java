package com.sankuai.dzhealth.ai.service.domain.chat.model;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @author:chenwei
 * @time: 2025/3/25 13:21
 * @version: 0.0.1
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ChatSessionMessageBO implements Serializable {

    @FieldDoc(description = "消息ID")
    private Long msgId;

    @FieldDoc(description = "角色，如user、robot等")
    private String role;

    @FieldDoc(description = "点赞类型，0未操作，1赞，2踩")
    private Integer likeType;

    @FieldDoc(description = "消息内容")
    private String content;
}
