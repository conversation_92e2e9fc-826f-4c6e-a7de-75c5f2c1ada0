package com.sankuai.dzhealth.ai.service.agent.domain.tools.param;

import lombok.Data;
import org.springframework.ai.tool.annotation.ToolParam;

/**
 * @author:chenwei
 * @time: 2025/7/16 11:14
 * @version: 0.0.1
 */
@Data
public class PositionRequest {

    @ToolParam(description = "The latitude value, representing the north-south position on the Earth's surface. Should be a decimal number between -90 and 90.")
    private Double latitude;

    @ToolParam(description = "The longitude value, representing the east-west position on the Earth's surface. Should be a decimal number between -180 and 180.")
    private Double longitude;

}
