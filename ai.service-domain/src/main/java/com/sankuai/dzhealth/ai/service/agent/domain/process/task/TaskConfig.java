package com.sankuai.dzhealth.ai.service.agent.domain.process.task;

import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * @author:chenwei
 * @time: 2025/7/9 10:24
 * @version: 0.0.1
 */
@Data
public class TaskConfig implements Serializable {

    private String type;

    private String systemPrompt;

    private String userPrompt;

    private String kmsKey;

    private String model;

    private List<String> toolNames;

    private boolean stream = false;

    private Integer maxTokens;

    private boolean jsonFormat = false;

    private transient Map<String, Object> extraBodyConfig;



}
