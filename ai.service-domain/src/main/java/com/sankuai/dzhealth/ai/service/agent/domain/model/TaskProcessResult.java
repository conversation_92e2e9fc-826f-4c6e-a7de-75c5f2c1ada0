package com.sankuai.dzhealth.ai.service.agent.domain.model;

import com.sankuai.dzhealth.ai.service.agent.domain.context.TaskContext;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @author:chenwei
 * @time: 2025/7/11 10:42
 * @version: 0.0.1
 */

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TaskProcessResult implements Serializable {

    private TaskContext taskContext;

    // 回复文本
    private String answer;

    private TaskProcessResult mainProcessResult;
}
