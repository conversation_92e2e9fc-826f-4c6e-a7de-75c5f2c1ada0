package com.sankuai.dzhealth.ai.service.agent.domain.cellar;

import com.sankuai.dzhealth.ai.service.agent.domain.enums.CategoryEnum;
import com.sankuai.dzhealth.ai.service.domain.utils.JsonUtils;
import com.taobao.tair3.client.Result;
import com.taobao.tair3.client.TairClient;
import com.taobao.tair3.client.impl.MultiTairClient;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Repository;

import java.util.List;

@Slf4j
@Repository
@RequiredArgsConstructor
public class HistorySearchRecordRepository {

    @Autowired
    @Qualifier("cellar")
    private MultiTairClient tairClient;


    @Value("${mdp.cellar0.area}")
    private short area;


    private String buildKey(Long userId, CategoryEnum categoryEnum) {
        String firstCategoryId, secondCategoryId;
        switch (categoryEnum) {
            case MEDICAL_CONSULT: // 医美
                firstCategoryId = "2";
                secondCategoryId = "768";
                break;
            case MOUTH_CONSULT:// 口腔
                firstCategoryId = "450";
                secondCategoryId = "46";
                break;
            default:
                return "";
        }
        return String.format("%s-%s-mt-%d", firstCategoryId, secondCategoryId, userId);
    }


    public List<String> selectHistorySearchRecord(Long userId, CategoryEnum categoryEnum) {
        TairClient.TairOption opt = new TairClient.TairOption(1000, (short) 0, 3600);  // 参数分别为: 超时时间(单位为毫秒), 版本号（默认为0，不做写入版本校验）, 过期时间(单位为秒)
        String key = buildKey(userId, categoryEnum);
        if (key.isBlank()) {
            return null;
        }
        // 读取
        List<String> records;
        try {
            Result<String> result = tairClient.get(area, key, opt); // 读取数据
            if (Result.ResultCode.OK.equals(result.getCode())) {    // 判断是否成功
                String retValue = result.getResult();    // 获取返回数据
                records = JsonUtils.parseArray(retValue, String.class);
                return records;
            } else {
                log.error("tairClient get failed, result code: {}", result.getCode());
            }
        } catch (Exception e) {  // 调用错误
            log.error("tairClient get failed", e);
        }
        return null;
    }
}


