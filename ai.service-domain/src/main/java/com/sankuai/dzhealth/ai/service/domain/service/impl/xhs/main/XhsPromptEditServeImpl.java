package com.sankuai.dzhealth.ai.service.domain.service.impl.xhs.main;

import com.sankuai.dzhealth.ai.service.domain.entity.xhs.XhsNoteRequest;
import com.sankuai.dzhealth.ai.service.domain.entity.xhs.XhsPromptEditRequest;
import com.sankuai.dzhealth.ai.service.domain.service.XhsNoteAbstractService;
import com.sankuai.dzhealth.ai.service.domain.service.impl.xhs.event.BuildMaterialsEvent;
import com.sankuai.dzhealth.ai.service.domain.utils.JsonUtils;
import com.sankuai.dzhealth.ai.service.infrastructure.dal.entity.CorpusLibraryDOWithBLOBs;
import com.sankuai.dzhealth.ai.service.infrastructure.model.CorpusLibrary;
import com.sankuai.dzhealth.ai.service.infrastructure.repository.CorpusLibraryRepository;
import lombok.Data;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;


@Service
public class XhsPromptEditServeImpl extends XhsNoteAbstractService<XhsPromptEditRequest> {


    @Autowired
    private CorpusLibraryRepository corpusLibraryRepository;

    @Resource
    private BuildMaterialsEvent buildMaterialsEvent;

    @Data
    private static class VerifyRequest {
        private String batch;
        private String theme;
        private String category;
        private String scene;
        private String droves;
        private String hotNoteBatch;
        private Long id;

        public XhsNoteRequest.TCSD toTCSD() {
            XhsNoteRequest.TCSD tcsd = new XhsNoteRequest.TCSD();
            tcsd.setTheme(theme);
            tcsd.setCategory(category);
            tcsd.setScene(scene);
            tcsd.setDroves(droves);
            return tcsd;
        }

        public Long safeGetId() {
            return Optional.ofNullable(id).orElse(368763L);
        }
    }

    @Override
    protected String service(XhsPromptEditRequest request) throws Exception {
        String result;
        switch (request.toEnum()) {
            case GET_PROMPT -> result = select(request);
            case SAVE_PROMPT -> result = save(request);
            case VERIFY_PROMPT -> result = verify(request);
            default -> result = "";
        }
        return result;
    }

    private String select(XhsPromptEditRequest request) {


        CorpusLibrary res = corpusLibraryRepository.findById(Long.parseLong(request.getData()))
                .orElseThrow(() -> new RuntimeException("查询失败"));

        return JsonUtils.toJsonString(res);
    }

    private String save(XhsPromptEditRequest request) {
        CorpusLibraryDOWithBLOBs saveRequest = JsonUtils.parseObject(request.getData(), CorpusLibraryDOWithBLOBs.class);

        Long res = corpusLibraryRepository.save(saveRequest);
        if (res == null ) {
            throw new RuntimeException("保存失败");
        }
        return JsonUtils.toJsonString(res);
    }

    private String verify(XhsPromptEditRequest request) {
        VerifyRequest verifyRequest = JsonUtils.parseObject(request.getData(), VerifyRequest.class);
        XhsNoteRequest.TCSD tcsd = verifyRequest.toTCSD();

        // 获取热门笔记
        List<String> hotNotes = buildMaterialsEvent.getHotNotes(tcsd, verifyRequest.getHotNoteBatch());
        // 获取知识库
        String knowledge = buildMaterialsEvent.getKnowledge(tcsd);
        // 构建模版
        String template = buildMaterialsEvent.buildTemplate(hotNotes);
        // 构建笔记prompt
        return buildMaterialsEvent.buildNotePrompt(tcsd, hotNotes, template, knowledge, verifyRequest.safeGetId());
    }
}
