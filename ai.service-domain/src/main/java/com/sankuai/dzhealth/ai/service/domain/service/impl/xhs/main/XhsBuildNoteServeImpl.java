package com.sankuai.dzhealth.ai.service.domain.service.impl.xhs.main;

import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.sankuai.beautycontent.store.storage.dto.StoreCommandDTO;
import com.sankuai.beautycontent.store.storage.service.StoreCommandService;
import com.sankuai.dzhealth.ai.service.domain.entity.xhs.XhsBuildNoteResult;
import com.sankuai.dzhealth.ai.service.domain.entity.xhs.XhsNoteRequest;
import com.sankuai.dzhealth.ai.service.domain.service.XhsNoteAbstractService;
import com.sankuai.dzhealth.ai.service.domain.service.impl.xhs.event.AsyncCallFuncEvent;
import com.sankuai.dzhealth.ai.service.domain.service.impl.xhs.event.BuildMaterialsEvent;
import com.sankuai.dzhealth.ai.service.domain.service.impl.xhs.event.BuildStoreArgsEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
@Slf4j
public class XhsBuildNoteServeImpl extends XhsNoteAbstractService<XhsNoteRequest> {

    @Resource
    private BuildMaterialsEvent buildMaterialsEvent;

    @Resource
    private ChatClient xhsNoteLLMClient;

    @Resource
    private AsyncCallFuncEvent asyncCallFunc;

    @Autowired
    private StoreCommandService storeCommandService;

    @Override
    protected String service(XhsNoteRequest request) throws Exception {
        // 穷举组合
        List<XhsNoteRequest.TCSD> tcsds = request.buildCombine();

        // 批处理
        asyncCallFunc.asyncConsumer(tcsds.size(),
                i -> workflow(tcsds.get(i), request.getCount(), request.getBatch(), request.getHotNoteBatch(), request.safeGetId()));

        // 处理成功success，否则异常处理。
        return XhsBuildNoteResult.Response.success();
    }

    /**
     * 单个组合生成笔记的工作流
     * 1. 每个组合 拿取一份资料数据（热门笔记，知识库）/ 构建模板用prompt,生成一个模版 / 构建一个prompt（生成笔记用）
     * 2. for count 循环生成
     *   2.1 笔记
     *   2.2 构建封面文案用prompt，生成封面文案
     *   2.3 save
     * @param tcsd XhsNoteRequest.TCSD
     * @param count 生成笔记数量
     * @param batch 批次
     */
    private void workflow(XhsNoteRequest.TCSD tcsd, int count, String batch, String hotNoteBatch, Long id) {
        XhsBuildNoteResult.XhsBuildNoteItem workflowResult = new XhsBuildNoteResult.XhsBuildNoteItem();

        // 获取热门笔记
        List<String> hotNotes = buildMaterialsEvent.getHotNotes(tcsd, hotNoteBatch);
        // 获取知识库
        String knowledge = buildMaterialsEvent.getKnowledge(tcsd);
        // 构建模版
        String template = buildMaterialsEvent.buildTemplate(hotNotes);
        // 构建笔记prompt
        String prompt = buildMaterialsEvent.buildNotePrompt(tcsd, hotNotes, template, knowledge, id);

        // 装载一下必要数据
        workflowResult.setKnowledge(knowledge);
        workflowResult.setTcsd(tcsd);

        // 按 count 生成笔记
        asyncCallFunc.asyncRunnable(count, () -> buildNote(workflowResult, prompt, batch));
    }

    private void buildNote(XhsBuildNoteResult.XhsBuildNoteItem Item, String prompt, String batch) {
        // 调用大模型生成笔记
        XhsBuildNoteResult.Note note = null;
        try {
            String noteResponse = xhsNoteLLMClient.prompt().user(prompt).call().content();
            note = XhsBuildNoteResult.Note.createNote(noteResponse);

            if (note.isError()) {
                throw new RuntimeException("生成笔记失败");
            }
        } catch (Exception e) {
            throw new RuntimeException("生成笔记失败", e);
        }

        // 调用大模型生成封面文案
        String coverPrompt = buildMaterialsEvent.buildNoteCoverPrompt(note, Item.getKnowledge());
        String coverResponse = xhsNoteLLMClient.prompt().user(coverPrompt).call().content();
        note.buildCover(coverResponse);

        // save
        try {
            StoreCommandDTO store = BuildStoreArgsEvent.buildSaveStoreArgs(note, Item, batch, prompt);
            RemoteResponse<Long> res = storeCommandService.add(store);
            if (!res.isSuccess()) {
                throw new RuntimeException("保存笔记失败");
            }
        } catch (Exception e) {
            throw new RuntimeException("保存笔记失败", e);
        }

    }

}
