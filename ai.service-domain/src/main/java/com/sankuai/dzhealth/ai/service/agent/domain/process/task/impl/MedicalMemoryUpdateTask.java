package com.sankuai.dzhealth.ai.service.agent.domain.process.task.impl;

import com.sankuai.dzhealth.ai.service.agent.domain.context.MessageContext;
import com.sankuai.dzhealth.ai.service.agent.domain.context.TaskContext;
import com.sankuai.dzhealth.ai.service.agent.domain.enums.TaskTypeEnum;
import com.sankuai.dzhealth.ai.service.agent.domain.model.MemoryData;
import com.sankuai.dzhealth.ai.service.agent.domain.model.MemoryModel;
import com.sankuai.dzhealth.ai.service.agent.domain.model.TaskProcessResult;
import com.sankuai.dzhealth.ai.service.agent.domain.model.decisionflow.DecisionTreeModel;
import com.sankuai.dzhealth.ai.service.agent.domain.process.task.GeneralTask;
import com.sankuai.dzhealth.ai.service.agent.domain.process.task.Task;
import com.sankuai.dzhealth.ai.service.agent.domain.repository.chatSession.ChatSessionMessageRepository;
import com.sankuai.dzhealth.ai.service.agent.enums.ContextExtraKey;
import com.sankuai.dzhealth.ai.service.domain.utils.JsonUtils;
import com.sankuai.dzhealth.ai.service.domain.utils.context.RequestContext;
import com.sankuai.dzhealth.ai.service.domain.utils.context.RequestContextConstant;
import com.sankuai.dzhealth.ai.service.infrastructure.acl.HaimaAcl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * @author:chenwei
 * @time: 2025/7/14 17:01
 * @version: 0.0.1
 */

@Slf4j
@Service
public class MedicalMemoryUpdateTask extends GeneralTask implements Task {

    @Autowired
    private ChatSessionMessageRepository chatSessionMessageRepository;

    @Autowired
    private HaimaAcl haimaAcl;

    @Override
    public boolean accept(String type) {
        return TaskTypeEnum.MEDICAL_MEMORY_UPDATE_TASK.getType().equals(type);
    }

    @Override
    public TaskProcessResult process(TaskContext context) {
        MemoryModel memoryModel = context.getMessageContext().getMemoryModel();
        String memoryPrompt = (memoryModel == null ? "" : JsonUtils.toJsonString(memoryModel));

        String systemPrompt = context.getTaskConfig().getSystemPrompt();
        String replaceSystemPrompt = systemPrompt.replace("{{{memory}}}", memoryPrompt);
        context.getTaskConfig().setSystemPrompt(replaceSystemPrompt);
        context.getTaskConfig().setUserPrompt(context.getMessageContext().getMsg());

        String answer = StringUtils.EMPTY;
        try {
            answer = getJsonAnswer(context);
        } catch (Exception e) {
            log.error("msg={}", context.getMessageContext().getMsg(), e);
        }
        log.info("记忆结果为:{}", answer);
        return TaskProcessResult.builder()
                .taskContext(context)
                .answer(answer)
                .build();
    }

    @Override
    public void after(TaskProcessResult result) {

        MemoryModel memoryModel = new MemoryModel();
        //开始更新记忆
        try {
            if (StringUtils.isNotBlank(result.getAnswer())) {
                List<MemoryData> memoryDataList = JsonUtils.parseArray(result.getAnswer(), MemoryData.class);
                memoryModel.setMemoryList(memoryDataList);
            }
            MessageContext messageContext = result.getTaskContext().getMessageContext();
            String decisionData = RequestContext.getAttribute(RequestContextConstant.DECISION_DATA);
            if (StringUtils.isNotBlank(decisionData)) {
                List<DecisionTreeModel> decisionTreeModelList = JsonUtils.parseArray(decisionData, DecisionTreeModel.class);
                Map<String, Serializable> extra = messageContext.getExtra();
                if (MapUtils.isNotEmpty(extra) && extra.containsKey(ContextExtraKey.ASSESS_PICTURE_URLS.getKey())) {
                    String assessPicJson = (String) extra.get(ContextExtraKey.ASSESS_PICTURE_URLS.getKey());
                    Map<String, Object> picId2UrlMap = JsonUtils.parseMap(assessPicJson);

                    // 将决策树模型中的图片ID还原为URL
                    restoreImageUrlsInDecisionTreeModels(decisionTreeModelList, picId2UrlMap);
                }
                memoryModel.setDecisionTreeModelList(decisionTreeModelList);
            }
            // 执行所有完之后再更新messageContext

            messageContext.setMemoryModel(memoryModel);
            // 塞到当前线程里
            log.info("[memoryTask]messageContext:{}", JsonUtils.toJsonString(messageContext));
            RequestContext.setAttribute(RequestContextConstant.MESSAGE_CONTEXT, messageContext);
        } catch (Exception e) {
            log.error("memoryUpdateTask error", e);
        }


    }

    /**
     * 将决策树模型列表中的图片ID还原为URL
     * @param decisionTreeModelList 决策树模型列表
     * @param picId2UrlMap 图片ID到URL的映射关系
     */
    private void restoreImageUrlsInDecisionTreeModels(List<DecisionTreeModel> decisionTreeModelList, 
                                                     Map<String, Object> picId2UrlMap) {
        if (decisionTreeModelList == null || decisionTreeModelList.isEmpty() || 
            MapUtils.isEmpty(picId2UrlMap)) {
            return;
        }

        for (DecisionTreeModel model : decisionTreeModelList) {
            restoreImageUrlsInDecisionTreeModel(model, picId2UrlMap);
        }
        
        log.info("已还原决策树模型中的图片URL，模型数量: {}, 映射关系数量: {}", 
                decisionTreeModelList.size(), picId2UrlMap.size());
    }

    /**
     * 递归还原单个决策树模型及其子节点中的图片ID为URL
     * @param model 决策树模型
     * @param picId2UrlMap 图片ID到URL的映射关系
     */
    private void restoreImageUrlsInDecisionTreeModel(DecisionTreeModel model, 
                                                   Map<String, Object> picId2UrlMap) {
        if (model == null) {
            return;
        }

        // 还原当前节点的assessmentImg
        if (StringUtils.isNotBlank(model.getAssessmentImg())) {
            String imageId = model.getAssessmentImg();
            Object imageUrl = picId2UrlMap.get(imageId);
            if (imageUrl != null) {
                model.setAssessmentImg(String.valueOf(imageUrl));
            }
        }

        // 递归处理drillDownRequirements
        if (model.getDrillDownRequirements() != null && !model.getDrillDownRequirements().isEmpty()) {
            for (DecisionTreeModel childModel : model.getDrillDownRequirements()) {
                restoreImageUrlsInDecisionTreeModel(childModel, picId2UrlMap);
            }
        }
    }
}
