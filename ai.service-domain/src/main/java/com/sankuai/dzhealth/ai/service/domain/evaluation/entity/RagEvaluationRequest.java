package com.sankuai.dzhealth.ai.service.domain.evaluation.entity;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import org.springframework.ai.document.Document;
import org.springframework.ai.evaluation.EvaluationRequest;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @author: yangwei<PERSON>
 * @date: 2025/4/29 11:14
 * @version: 1.0
 */
@EqualsAndHashCode(callSuper = true)
@Getter
public class RagEvaluationRequest extends EvaluationRequest {

    private final String bizScene;
    private final String modelScene;
    private final Long sessionId;
    private final Long msgId;
    private final Map<String, Object> metadata;

    // 添加构造函数
    public RagEvaluationRequest(String userText, List<Document> dataList, String responseContent,
                                String bizScene, String modelScene,
                                Long sessionId, Long msgId, Map<String, Object> metadata) {
        super(userText, dataList, responseContent);
        this.bizScene = bizScene;
        this.modelScene = modelScene;
        this.sessionId = sessionId;
        this.msgId = msgId;
        this.metadata = metadata != null ? new HashMap<>(metadata) : new HashMap<>();
    }

    public static class RagEvaluationRequestBuilder {
        private String userText;
        private List<Document> dataList = new ArrayList<>();
        private String responseContent;
        private String evaluationType;
        private String bizScene;
        private String modelScene;
        private Long sessionId;
        private Long msgId;
        private Map<String, Object> metadata = new HashMap<>();

        public RagEvaluationRequestBuilder userText(String userText) {
            this.userText = userText;
            return this;
        }

        public RagEvaluationRequestBuilder dataList(List<Document> dataList) {
            this.dataList = dataList != null ? new ArrayList<>(dataList) : new ArrayList<>();
            return this;
        }

        public RagEvaluationRequestBuilder responseContent(String responseContent) {
            this.responseContent = responseContent;
            return this;
        }

        public RagEvaluationRequestBuilder evaluationType(String evaluationType) {
            this.evaluationType = evaluationType;
            return this;
        }

        public RagEvaluationRequestBuilder bizScene(String bizScene) {
            this.bizScene = bizScene;
            return this;
        }

        public RagEvaluationRequestBuilder modelScene(String modelScene) {
            this.modelScene = modelScene;
            return this;
        }

        public RagEvaluationRequestBuilder sessionId(Long sessionId) {
            this.sessionId = sessionId;
            return this;
        }

        public RagEvaluationRequestBuilder msgId(Long msgId) {
            this.msgId = msgId;
            return this;
        }

        public RagEvaluationRequestBuilder metadata(Map<String, Object> metadata) {
            this.metadata = metadata != null ? new HashMap<>(metadata) : new HashMap<>();
            return this;
        }

        public RagEvaluationRequest build() {
            return new RagEvaluationRequest(
                    userText, dataList, responseContent,
                    bizScene, modelScene,
                    sessionId, msgId, metadata
            );
        }
    }

    // 对外暴露的 builder() 方法
    public static RagEvaluationRequestBuilder builder() {
        return new RagEvaluationRequestBuilder();
    }
}