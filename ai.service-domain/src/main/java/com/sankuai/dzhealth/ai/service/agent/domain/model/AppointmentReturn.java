package com.sankuai.dzhealth.ai.service.agent.domain.model;


import com.sankuai.dzhealth.ai.service.agent.domain.enums.AppointmentReturnTypeEnum;
import com.sankuai.dzhealth.ai.service.agent.domain.process.card.data.AppointmentCardData;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AppointmentReturn {

    private AppointmentReturnTypeEnum type;

    private String txt;

    private AppointmentCardData card;

}
