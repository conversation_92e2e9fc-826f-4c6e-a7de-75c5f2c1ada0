package com.sankuai.dzhealth.ai.service.domain.utils.context;

import com.google.common.collect.Maps;
import com.sankuai.dzhealth.ai.service.agent.domain.buffer.MessageBuffer;
import com.sankuai.dzhealth.ai.service.agent.domain.buffer.MessageBufferEntity;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * @author:chenwei
 * @time: 2025/3/20 13:43
 * @version: 0.0.1
 */
@Data
public class RequestContext {
    private static final ThreadLocal<Map<Object, Object>> attributes = new ThreadLocal<>();

    public static void init() {
        attributes.set(Maps.newHashMap());
    }

    @SuppressWarnings("unchecked")
    public static <T> T getAttribute(Object key) {
        Map<Object, Object> map = attributes.get();
        if (map != null) {
            return (T) map.get(key);
        } else {
            return null;
        }
    }

    public static void setAttribute(Object key, Object value) {
        Map<Object, Object> map = attributes.get();
        if (map != null) {
            map.put(key, value);
        }
    }

    public static boolean isEmpty() {
        return attributes.get() == null;
    }

    public static void cleanup() {
        Map<Object, Object> map = attributes.get();

        if (map != null) {
            map.clear();
        }

        attributes.remove();
    }

    public static boolean writeBuffer(List<MessageBufferEntity> bufferEntityList, MessageBuffer messageBufferService) {
        if (messageBufferService == null) {
            messageBufferService = getAttribute(RequestContextConstant.MESSAGE_BUFFER);
        }
        if (messageBufferService == null) {
            return true;
        }
        return messageBufferService.writeBufferData(bufferEntityList, messageBufferService);
    }

    public static boolean writeBufferMessageType(int messageType) {
        MessageBuffer messageBufferService = getAttribute(RequestContextConstant.MESSAGE_BUFFER);
        if (messageBufferService == null) {
            return true;
        }
        return messageBufferService.writeBufferMessageType(messageType);
    }
}
