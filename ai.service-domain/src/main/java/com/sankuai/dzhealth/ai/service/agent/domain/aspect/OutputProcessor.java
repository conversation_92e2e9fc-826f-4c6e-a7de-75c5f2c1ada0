package com.sankuai.dzhealth.ai.service.agent.domain.aspect;

import com.sankuai.dzhealth.ai.service.agent.domain.buffer.MessageBuffer;
import com.sankuai.dzhealth.ai.service.agent.domain.buffer.MessageBufferEntity;
import com.sankuai.dzhealth.ai.service.agent.domain.context.ChatClientContext;
import com.sankuai.dzhealth.ai.service.agent.domain.strategy.TaskTypeStrategy;
import com.sankuai.dzhealth.ai.service.agent.domain.strategy.TaskTypeStrategyFactory;
import com.sankuai.dzhealth.ai.service.enums.BufferItemTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;

/**
 * 输出处理器
 * 统一处理不同任务类型的文本输出逻辑
 * 
 * <AUTHOR>
 * @time 2025/1/15
 */
@Slf4j
@Component
public class OutputProcessor {
    
    @Autowired
    private TaskTypeStrategyFactory strategyFactory;
    
    /**
     * 处理文本输出
     * 
     * @param text 原始文本
     * @param context 聊天上下文
     * @param buffer 消息缓冲区
     * @param sb 输出累积器
     * @return 是否已处理输出
     */
    public boolean processTextOutput(String text, ChatClientContext context, MessageBuffer buffer, StringBuilder sb) {
        if (StringUtils.isBlank(text)) {
            return false;
        }
        
        try {
            TaskTypeStrategy strategy = strategyFactory.getStrategy(context.getTaskType());
            String processedText = strategy.processText(text, context);
            
            if (processedText != null) {
                // 累积到输出结果
                sb.append(processedText);
                
                // 写入缓冲区
                if (buffer != null) {
                    log.info("<time>start={}", System.currentTimeMillis());
                    buffer.writeBufferData(Collections.singletonList(
                            MessageBufferEntity.builder()
                                    .data(processedText)
                                    .type(BufferItemTypeEnum.MAIN_TEXT.getType())
                                    .build()
                    ), buffer);
                }
                return true;
            }
            
            // 返回 null 表示暂时缓存，不输出
            return false;
            
        } catch (Exception e) {
            log.error("Error processing text output for taskType: {}", context.getTaskType(), e);
            // 出错时直接输出原文本
            sb.append(text);
            if (buffer != null) {
                buffer.writeBufferData(Collections.singletonList(
                        MessageBufferEntity.builder()
                                .data(text)
                                .type(BufferItemTypeEnum.MAIN_TEXT.getType())
                                .build()
                ), buffer);
            }
            return true;
        }
    }
    
    /**
     * 处理完成时的剩余内容
     * 
     * @param context 聊天上下文
     * @param buffer 消息缓冲区
     * @param sb 输出累积器
     */
    public void processCompletionOutput(ChatClientContext context, MessageBuffer buffer, StringBuilder sb) {
        try {
            TaskTypeStrategy strategy = strategyFactory.getStrategy(context.getTaskType());
            String remainingText = strategy.onComplete(context);
            
            if (StringUtils.isNotBlank(remainingText)) {
                sb.append(remainingText);
                if (buffer != null) {
                    buffer.writeBufferData(Collections.singletonList(
                            MessageBufferEntity.builder()
                                    .data(remainingText)
                                    .type(BufferItemTypeEnum.MAIN_TEXT.getType())
                                    .build()
                    ), buffer);
                }
            }
            
            log.info("[time]complete={},info={}", System.currentTimeMillis(), sb.toString());
            
        } catch (Exception e) {
            log.error("Error processing completion output for taskType: {}", context.getTaskType(), e);
        }
    }
}