package com.sankuai.dzhealth.ai.service.agent.domain.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

@Getter
public enum CatchAllTxtEnum {

    RE_APPOINTMENT(1,"请明确您的预约时间、预约地点和预约项目，我来帮你预约~你可以和我说“明天下午在附近做洗牙”"),
    RE_APPOINTMENT_TIME_ERROR(2,"不好意思，您预约的时间太紧张啦，这边建议间隔半小时以上预约~"),
    APPOINTMENT_NO_SHOP(3,"非常抱歉，未为你找到合适的商家，可以调整要求再试试~"),
    EXCEPTION(4,"系统开小差啦,请稍后重试~"),
    RESCHEDULE_APPOINTMENT(5,"好的，已为您重启本次帮约任务～”"),
    APPOINTMENT_CANCEL(6,"好的，已为您取消本次帮约任务~"),
    CANCEL_SUCCESS_APPOINT(7,"好的，已为您取消本次预约~"),
    TIME_NOT_EXIST(8,"抱歉，可以告诉我您要预约的具体时间吗？例如明天下午~"),
    TIME_OUT_RANGE(9,"抱歉，当前只支持七天内的预约，可以缩小时间范围再试试~"),
    TIME_IS_PASSED(10,"抱歉，不可预约过去时间哦～"),
    TIME_IS_IN_CLOSED(11,"不好意思，您预约的时间商家都打烊啦，这边建议您预约10:00-21:00的时间~"),
    PRODUCT_NOT_ALLOW(12,"抱歉，暂不支持该项目的预约服务~可以在口腔检查、种植牙、牙齿矫正、补牙、拔牙、美白、洗牙中预约~"),
    PRODUCT_NOT_EXIST(13,"请输入您需要预约的项目，目前支持口腔检查、种植牙、牙齿矫正、补牙、拔牙、美白、洗牙中预约～"),
    POSITION_NOT_EXIST(14,"抱歉，请告诉我您想要预约的具体地点,尽量具体哦"),
    POSITION_NOT_COLLECT (15,"抱歉，请告诉我您想要预约的具体地点，例如北京三里屯附近或北京朝阳区"),
    POSITION_IS_OUTRANGE(16,"抱歉，您提供的区域范围太大，请提供更具体的位置~"),
    TIME_IS_CROSS(17,"抱歉，当前只支持不支持跨天预约，这边建议您预约具体的一天哦~"),





    ;





    private final int code;
    private final String desc;

    CatchAllTxtEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String getDesc(Integer code) {
        return Arrays.stream(values()).filter(CatchAllTxtEnum -> Objects.equals(CatchAllTxtEnum.code,code)).findFirst().map(CatchAllTxtEnum::getDesc).orElse(null);
    }

    public static Integer getCode(String desc){
        return Arrays.stream(values()).filter(CatchAllTxtEnum -> Objects.equals(CatchAllTxtEnum.desc, desc)).findFirst().map(CatchAllTxtEnum::getCode).orElse(null);
    }
}
