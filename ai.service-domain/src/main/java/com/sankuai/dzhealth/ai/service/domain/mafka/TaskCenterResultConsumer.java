package com.sankuai.dzhealth.ai.service.domain.mafka;

import com.fasterxml.jackson.databind.JsonNode;
import com.meituan.mafka.client.MafkaClient;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mafka.client.consumer.ConsumerConstants;
import com.meituan.mafka.client.consumer.IConsumerProcessor;
import com.meituan.mdp.boot.starter.config.annotation.MdpConfig;
import com.sankuai.dzhealth.ai.service.domain.service.impl.xhs.CallbackServiceFactory;
import com.sankuai.dzhealth.ai.service.domain.service.impl.xhs.NoteGenerationHandler;
import com.sankuai.dzhealth.ai.service.domain.utils.JsonUtils;
import com.sankuai.dzhealth.ai.service.infrastructure.mafka.MafkaShutDownHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Properties;

/**
 * 任务中心结果Mafka消费者
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class TaskCenterResultConsumer implements InitializingBean {

    @Autowired
    private NoteGenerationHandler noteGenerationHandler;

    @Autowired
    private CallbackServiceFactory callbackServiceFactory;

    private static IConsumerProcessor consumer;

    @MdpConfig("failAppKey:com.sankuai.dzhealth.ai.service")
    private String defaultAppKey;

    @Override
    public void afterPropertiesSet() throws Exception {
        Properties properties = new Properties();
        // 设置业务所在BG的namespace
        properties.setProperty(ConsumerConstants.MafkaBGNamespace, "common");
        // 设置消费者appkey
        properties.setProperty(ConsumerConstants.MafkaClientAppkey, "com.sankuai.dzhealth.ai.service");
        // 设置订阅组group
        properties.setProperty(ConsumerConstants.SubscribeGroup, "xhs_collect_note_consumer");
        // 设置重试次数
        properties.setProperty(ConsumerConstants.MafkaDelayRetryCount, "1");

        // 创建topic对应的consumer对象
        consumer = MafkaClient.buildConsumerFactory(properties, "taskcenter_result_topic");
        // 注册关闭钩子
        MafkaShutDownHelper.registerHook(consumer);

        // 设置消费者处理器并启动订阅
        consumer.recvMessageWithParallel(String.class, (message, context) -> {
            String messageStr = String.valueOf(message.getBody());
            String batch = "";
            try {
                if (StringUtils.isNotBlank(messageStr)) {
                    String action = JsonUtils.getString(messageStr, "action");
                    int code = JsonUtils.getInt(messageStr, "code");


                    if ("xinhong".equals(action)) {
                        log.info("[TaskCenterResultConsumer] 接收到任务中心结果消息: message:{}", messageStr);
                        JsonNode byPath = JsonUtils.getByPath(messageStr, "result.batch");
                        batch = (byPath == null) ? "" : byPath.asText();

                        JsonNode answerNode = JsonUtils.getByPath(messageStr, "result.answer");
                        String answer = answerNode == null ? "" : answerNode.asText();
                        if (code == 0) {
                            log.info("[TaskCenterResultConsumer]: success, batchId:{}", batch);
                            if (StringUtils.isNotBlank(batch)) {
                                noteGenerationHandler.handle(batch, answer);
                            }
                        } else {
                            JsonNode errMsgNode = JsonUtils.getByPath(messageStr, "result.message");
                            log.info("[TaskCenterResultConsumer]: fail, batchId:{}", batch);
                            String errorMsg = (errMsgNode == null) ? "" : errMsgNode.asText();
                            if (StringUtils.isNotBlank(batch)) {
                                noteGenerationHandler.handle(batch, errorMsg);
                            }
                        }
                    }
                }
                return ConsumeStatus.CONSUME_SUCCESS;
            } catch (Exception e) {
                log.error("[TaskCenterResultConsumer] 处理消息异常，batchId: {}, message: {}", batch, messageStr, e);
                // 即使处理失败，也返回CONSUME_SUCCESS，防止消息积压
                return ConsumeStatus.CONSUME_SUCCESS;
            }
        });
    }
} 