package com.sankuai.dzhealth.ai.service.domain.utils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.ImmutableMap;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;
import java.util.Optional;

public class HistoryChatUtils {

    private static final Map<String, String> SOURCE_CHAT_PREFIX_MAP = ImmutableMap.of(
            "machine", "AI导诊台: ",
            "user", "医院: "
    );


    public static String getHistoryChat(String historyChatJson) {
        if (StringUtils.isBlank(historyChatJson)) {
            return null;
        }
        JSONObject jsonObject = (JSONObject) JSONObject.parse(historyChatJson);
        JSONArray historyList = Optional.ofNullable(jsonObject).map(json -> json.getJSONObject("dialogRawData"))
                .map(rawData -> rawData.getJSONArray("history")).orElse(null);
        if (CollectionUtils.isEmpty(historyList)) {
            return null;
        }

        StringBuilder chatContentBuilder = new StringBuilder();
        for (int i = 0; i < historyList.size(); i++) {
            JSONObject historyItem = historyList.getJSONObject(i);
            String source = historyItem.getString("source");
            if (!SOURCE_CHAT_PREFIX_MAP.containsKey(source)) {
                continue;
            }

            String message = historyItem.getString("message");
            String handleMessage = StringUtils.isBlank(message) ? StringUtils.EMPTY : message.trim();
            chatContentBuilder.append(SOURCE_CHAT_PREFIX_MAP.get(source)).append(handleMessage).append("\n");
        }
        return chatContentBuilder.toString();
    }
}
