package com.sankuai.dzhealth.ai.service.domain.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONValidator;
import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.dianping.haima.entity.haima.HaimaContent;
import com.sankuai.dzhealth.ai.service.dto.DepartmentCardDTO;
import com.sankuai.dzhealth.ai.service.infrastructure.acl.HaimaAcl;
import com.sankuai.dzhealth.ai.service.infrastructure.utils.DateTimeTools;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR> youtianyu
 * @version : 0.1
 * @since : 2025/4/1
 */
@Service
@Slf4j
public class DepartmentRecommendService {
    @Autowired
    private ChatClient recommendChatClient;

    @Autowired
    private HaimaAcl haimaAcl;

    @Autowired
    private DateTimeTools dateTimeTools;

    public List<DepartmentCardDTO> recommend(String query, List<DepartmentCardDTO> departmentCardDTOS) {
        Transaction transaction = Cat.newTransaction("DepartmentRecommendService", "recommendDepartment");
        transaction.setSuccessStatus();
        try {
            String promptTemplate = getPromptTemplate().orElseThrow(
                    () -> new RuntimeException("department_recommend promptTemplate is null"));
            String prompt = promptTemplate.replace("${query}", query)
                    .replace("${departments}", JSON.toJSONString(departmentCardDTOS)).replace("${time}", dateTimeTools.getNowTime());

            String json = recommendChatClient.prompt().user(prompt).call().content();

            try (JSONValidator validator = JSONValidator.from(json)) {
                if (Objects.equals(validator.getType(), JSONValidator.Type.Array)) {
                    return JSON.parseArray(json, DepartmentCardDTO.class);
                }
            }
            return departmentCardDTOS;
        } catch (Exception e) {
            log.error("recommendDepartment, query={},departmentCardDTOS={},e={}", query,
                    JSON.toJSONString(departmentCardDTOS), e, e);
            transaction.setStatus(e);
            return departmentCardDTOS;
        } finally {
            transaction.complete();
        }
    }

    private Optional<String> getPromptTemplate() {
        List<HaimaContent> haimaContents = haimaAcl.getContent("ai_hospital_instruction", null);
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(haimaContents)) {
            Cat.logEvent("refreshPrompt", "empty");
            return Optional.empty();
        }
        return haimaContents.stream()
                .filter(content -> "department_recommend".equals(content.getContentString("promptKey")))
                .map(content -> content.getContentString("promptContent"))
                .findFirst();
    }
}
