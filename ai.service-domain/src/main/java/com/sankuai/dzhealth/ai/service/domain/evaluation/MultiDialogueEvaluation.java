package com.sankuai.dzhealth.ai.service.domain.evaluation;

import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.dianping.cat.util.MetricHelper;
import com.dianping.haima.entity.haima.HaimaContent;
import com.dianping.rhino.Rhino;
import com.dianping.rhino.threadpool.DefaultThreadPoolProperties;
import com.dianping.rhino.threadpool.ThreadPool;
import com.meituan.hotel.dlm.lock.Lock;
import com.meituan.hotel.dlm.service.impl.DistributedLockManager;
import com.sankuai.dzhealth.ai.service.agent.infrastructure.dal.entity.MessageEvaluationResultEntity;
import com.sankuai.dzhealth.ai.service.agent.infrastructure.dal.entity.SessionEvaluationResultEntity;
import com.sankuai.dzhealth.ai.service.agent.infrastructure.dal.repository.evaluation.MessageEvaluationResultRepository;
import com.sankuai.dzhealth.ai.service.agent.infrastructure.dal.repository.evaluation.SessionEvaluationResultRepository;
import com.sankuai.dzhealth.ai.service.agent.infrastructure.model.EvaluationMetricsResult;
import com.sankuai.dzhealth.ai.service.agent.infrastructure.model.EvaluationResultsResponse;
import com.sankuai.dzhealth.ai.service.agent.infrastructure.model.MultiEvaluationRequest;
import com.sankuai.dzhealth.ai.service.agent.infrastructure.model.MultiEvaluationResponse;
import com.sankuai.dzhealth.ai.service.domain.enums.MultiEvaluationTypeEnum;
import com.sankuai.dzhealth.ai.service.domain.evaluation.config.MultiEvaluationConfig;
import com.sankuai.dzhealth.ai.service.domain.utils.JsonUtils;
import com.sankuai.dzhealth.ai.service.enums.MetadataKeyEnum;
import com.sankuai.dzhealth.ai.service.infrastructure.acl.HaimaAcl;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.messages.Message;
import org.springframework.ai.chat.messages.SystemMessage;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.ai.chat.prompt.PromptTemplate;
import org.springframework.ai.document.Document;
import org.springframework.ai.retry.NonTransientAiException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.web.client.HttpClientErrorException;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * 多轮对话评测
 * <p>
 * 单轮：本轮对话+历史对话记录进行评测。
 * <p>
 * 多轮：包含当前轮次的所有对话记录进行评测
 * <p>
 * 虽然单轮和多轮，对话次数是一样的，但是结构和行为是不同的。一个是 1+（n-1），一个是 n
 *
 * <AUTHOR>
 * @since 2025/1/15
 */
@Component
@Slf4j
@Setter
public class MultiDialogueEvaluation {

    public static final String SCENE_KEY = "multi_evaluation_ai_config";

    @Autowired
    private ChatClient evaluationChatClient;

    @Autowired
    private MessageEvaluationResultRepository messageEvaluationResultRepository;

    @Autowired
    private SessionEvaluationResultRepository sessionEvaluationResultRepository;

    @Autowired
    @Qualifier("multiDialogueEvaluationDistributedLock")
    private DistributedLockManager distributedLockManager;

    @Autowired
    private HaimaAcl haimaAcl;

    public static final ThreadPool Multi_EVALUATION_POOL = Rhino.newThreadPool("MULTI_EVALUATION_POOL",
            DefaultThreadPoolProperties.Setter().withCoreSize(100).withMaxSize(200).withMaxQueueSize(1000));

    // 多个单轮对话的异步评测
    public List<CompletableFuture<MultiEvaluationResponse>> executeMultiSingleDialogueEvaluationAsync(List<MultiEvaluationRequest> requests) {
        log.info("开始执行多个单轮对话异步评测，size: {}", requests.size());
        return requests.stream().map(request -> {
            return CompletableFuture.supplyAsync(() -> executeSingleDialogueEvaluation(request),
                    Multi_EVALUATION_POOL.getExecutor());
        }).collect(Collectors.toList());
    }

    // 多轮对话的异步评测
    public CompletableFuture<MultiEvaluationResponse> executeMultiDialogueEvaluationAsync(MultiEvaluationRequest request) {
        log.info("开始执行多轮对话异步评测，request: {}", request);
        return CompletableFuture.supplyAsync(() -> executeMultiDialogueEvaluation(request),
                Multi_EVALUATION_POOL.getExecutor());
    }

    /**
     * 执行单轮对话评测，当前轮次对话+ 历史对话记录进行评测。
     *
     * @param request 多轮对话评测请求
     * @return 多轮对话评测响应
     */
    public MultiEvaluationResponse executeSingleDialogueEvaluation(MultiEvaluationRequest request) {
        Transaction transaction = Cat.newTransaction(MultiDialogueEvaluation.class.getSimpleName(), "executeSingleDialogueEvaluation");
        String lockName = "AiHealth:executeSingleDialogueEvaluation:%s:%s".formatted(request.getSessionId(),
                request.getMessageId());
        // Lock reentrantLock = distributedLockManager.getReentrantLock(lockName);
        // reentrantLock.lock();
        try {
            log.info("开始执行单轮对话评测，sessionId: {}, messageId: {}, bizScene: {}, modelScene: {}", request.getSessionId(), request.getMessageId()
                    , request.getBizScene(), request.getModelScene());

            // 1. 执行单轮对话评测
            List<MessageEvaluationResultEntity> messageResults = executeSingleTurnEvaluation(request);

            // 3. 构建响应
            MultiEvaluationResponse response = MultiEvaluationResponse.builder()
                    .bizScene(request.getBizScene())
                    .sessionId(request.getSessionId())
                    .messageId(request.getMessageId())
                    .modelScene(request.getModelScene())
                    .messageResults(messageResults)
                    .success(true)
                    .msg("评测成功").build();

            log.info("单轮对话执行评测成功，sessionId: {}, messageId: {}, bizScene: {}, modelScene: {}", request.getSessionId(), request.getMessageId()
                    , request.getBizScene(), request.getModelScene());

            for (MessageEvaluationResultEntity messageResult : messageResults) {
                MetricHelper.build().name(request.getBizScene()).tag("model", request.getModelScene())
                        .tag("key", messageResult.getItem())
                        .value(messageResult.getScore().intValue());
            }

            transaction.setSuccessStatus();
            return response;

        } catch (Exception e) {
            log.error("单轮对话评测失败，sessionId: {}, messageId: {}, bizScene: {}, modelScene: {}", request.getSessionId(), request.getMessageId()
                    , request.getBizScene(), request.getModelScene(), e);
            transaction.setStatus(e);
            return MultiEvaluationResponse.builder().bizScene(request.getBizScene()).sessionId(request.getSessionId())
                    .messageId(request.getMessageId()).modelScene(request.getModelScene()).success(false)
                    .msg("评测失败: " + e.getMessage()).build();
        } finally {
            transaction.complete();
            // reentrantLock.unlock();
        }
    }

    /**
     * 执行多轮对话评测，包含当前轮次的所有对话记录进行评测
     *
     * @param request 多轮对话评测请求
     * @return 多轮对话评测响应
     */
    public MultiEvaluationResponse executeMultiDialogueEvaluation(MultiEvaluationRequest request) {
        Transaction transaction = Cat.newTransaction(MultiDialogueEvaluation.class.getSimpleName(), "executeMultiDialogueEvaluation");
        String lockName = "AiHealth:executeMultiDialogueEvaluation:%s:%s".formatted(request.getSessionId(),
                request.getMessageId());
        Lock reentrantLock = distributedLockManager.getReentrantLock(lockName);
        reentrantLock.lock();
        try {
            log.info("开始执行多轮对话评测，sessionId: {}, messageId: {}, bizScene: {}, modelScene: {}", request.getSessionId(), request.getMessageId()
                    , request.getBizScene(), request.getModelScene());

            // 2. 执行多轮会话评测
            List<SessionEvaluationResultEntity> sessionResults = executeMultiTurnEvaluation(request);

            // 3. 构建响应
            MultiEvaluationResponse response = MultiEvaluationResponse.builder()
                    .bizScene(request.getBizScene())
                    .sessionId(request.getSessionId())
                    .messageId(request.getMessageId())
                    .modelScene(request.getModelScene())
                    .sessionResults(sessionResults)
                    .success(true)
                    .msg("评测成功").build();

            log.info("多轮对话评测执行成功，sessionId: {}, messageId: {}, bizScene: {}, modelScene: {}", request.getSessionId(), request.getMessageId()
                    , request.getBizScene(), request.getModelScene());

            for (SessionEvaluationResultEntity sessionResult : sessionResults) {
                MetricHelper.build().name(request.getBizScene()).tag("model", request.getModelScene())
                        .tag("key", sessionResult.getItem())
                        .value(sessionResult.getScore().intValue());
            }

            transaction.setSuccessStatus();
            return response;

        } catch (Exception e) {
            log.error("多轮对话评测执行失败，sessionId: {}, messageId: {}, bizScene: {}, modelScene: {}", request.getSessionId(), request.getMessageId()
                    , request.getBizScene(), request.getModelScene(), e);
            transaction.setStatus(e);
            return MultiEvaluationResponse.builder().bizScene(request.getBizScene()).sessionId(request.getSessionId())
                    .messageId(request.getMessageId()).modelScene(request.getModelScene()).success(false)
                    .msg("评测失败: " + e.getMessage()).build();
        } finally {
            transaction.complete();
            reentrantLock.unlock();
        }
    }


    /**
     * 执行单轮对话评测
     *
     * @param request 评测请求
     * @return 单轮评测结果列表
     */
    private List<MessageEvaluationResultEntity> executeSingleTurnEvaluation(MultiEvaluationRequest request) {
        log.info("开始执行单轮对话评测，sessionId: {}, messageId: {}, bizScene: {}, modelScene: {}", request.getSessionId(), request.getMessageId()
                , request.getBizScene(), request.getModelScene());

        MultiEvaluationRequest.Message dialog = request.getDialog();
        // 这里的userPrompt实际上是被评测的大模型的 systemPrompt、rag、context、userPrompt和 assistantAnswer
        // String ragContent = getRagContent(request);
        Map<String, Object> parameters = Map.of(
                "system", dialog.getSystem() == null ? "" : dialog.getSystem(),// 被评测的大模型的系统提示
                // "rag", ragContent == null ? "" : ragContent,// 被评测的大模型的上下文
                "context", request.getContext(),
                "query", dialog.getUserQuery() == null ? "" : dialog.getUserQuery(),// 被评测的大模型用户提问内容
                "answer", dialog.getAssistantAnswer() == null ? "" : dialog.getAssistantAnswer()// 被评测的大模型回答内容
        );
        List<EvaluationMetricsResult.Metric> metrics = executeEvaluationWithParameters(request.getBizScene(), request.getModelScene(),
                parameters, MultiEvaluationTypeEnum.SINGLE.getType());

        // 增加搜索成功率指标
        if (request.getSearchRequestCount() != null && request.getSearchRequestCount() > 0
                && request.getSearchRequestSuccessCount() != null && request.getSearchRequestSuccessCount() > 0) {
            EvaluationMetricsResult.Metric searchMetric = getSearchMetric(request);
            metrics.add(searchMetric);
        }

        log.info("单轮对话评测结果，sessionId: {}, messageId: {}, bizScene: {}, modelScene: {}, metrics: {}",
                request.getSessionId(), request.getMessageId(), request.getBizScene(), request.getModelScene(), JsonUtils.toJsonString(metrics));
        List<MessageEvaluationResultEntity> messageResults = new ArrayList<>();
        for (EvaluationMetricsResult.Metric metric : metrics) {
            MessageEvaluationResultEntity result = MessageEvaluationResultEntity.builder()
                    .bizScene(request.getBizScene())
                    .sessionId(request.getSessionId())
                    .messageId(request.getMessageId())
                    .modelScene(request.getModelScene())
                    .item(metric.getItem())
                    .description(metric.getDescription())
                    .reason(metric.getReason())
                    .score(metric.getScore())
                    .type(0) // 单轮类型
                    .source(request.getSource()) // 用户来源
                    .addTime(new Date())
                    .updateTime(new Date())
                    .evaluationId(request.getEvaluationId())
                    .evaluationVer(request.getEvaluationVer())
                    .build();
            messageResults.add(result);
        }

        // 保存单轮评测结果
        if (CollectionUtils.isNotEmpty(messageResults)) {
            messageEvaluationResultRepository.batchInsert(messageResults);
            log.info("单轮评测结果保存成功，数量: {}", messageResults.size());
        } else {
            log.warn("单轮评测结果为空 ，sessionId: {}, messageId: {}, bizScene: {}, modelScene: {}", request.getSessionId(), request.getMessageId()
                    , request.getBizScene(), request.getModelScene());
        }

        return messageResults;
    }

    private EvaluationMetricsResult.Metric getSearchMetric(MultiEvaluationRequest request) {
        log.info("开始计算搜索成功率，sessionId: {}, messageId: {}, bizScene: {}, modelScene: {}, request: {}", request.getSessionId(), request.getMessageId()
                , request.getBizScene(), request.getModelScene(), request);
        EvaluationMetricsResult.Metric e = new EvaluationMetricsResult.Metric();
        e.setItem("searchSuccessRate");
        e.setDescription("搜索成功率");
        BigDecimal rate = BigDecimal.valueOf(request.getSearchRequestSuccessCount())
                .divide(BigDecimal.valueOf(request.getSearchRequestCount()), 2, RoundingMode.HALF_UP)
                .multiply(new BigDecimal("100"));
        e.setScore(rate);
        e.setReason("请求次数：%s，成功次数：%s".formatted(request.getSearchRequestCount(),
                request.getSearchRequestSuccessCount()));
        return e;
    }

    private String getRagContent(MultiEvaluationRequest request) {
        String ragContent = "";
        if (request.getDialog().getRagInfo() != null && !request.getDialog().getRagInfo().isEmpty()) {
            StringBuilder ragContentBuilder = new StringBuilder();
            for (Document doc : request.getDialog().getRagInfo()) {
                ragContentBuilder.append("\n## ").append(doc.getMetadata().get(MetadataKeyEnum.SOURCE.getKey())).append("\n").append(doc.getText());
            }
            ragContent = ragContentBuilder.toString();
        }
        return ragContent;
    }

    /**
     * 根据类型和参数执行评测
     *
     * @param bizScene   业务场景
     * @param modelScene 模型场景
     * @param parameters 模板参数
     * @return 评测结果列表
     */
    private List<EvaluationMetricsResult.Metric> executeEvaluationWithParameters(String bizScene, String modelScene,
                                                                                 Map<String, Object> parameters, String type) {

        log.info("开始执行评测，bizScene: {}, modelScene: {}, parameters: {}, type: {}", bizScene, modelScene, parameters, type);
        List<MultiEvaluationConfig> evaluationPrompts = getEvaluationPromptsFromHaima(SCENE_KEY, bizScene, modelScene, type);
        if (evaluationPrompts.isEmpty()) {
            log.warn("未找到评测配置直接返回, bizScene: {}, modelScene: {}, type: {}", bizScene, modelScene, type);
            return Collections.emptyList();
        }

        List<EvaluationMetricsResult.Metric> metrics = new ArrayList<>();

        for (MultiEvaluationConfig evaluationConfig : evaluationPrompts) {
            PromptTemplate promptTemplate = new PromptTemplate(evaluationConfig.getUserPrompt());
            Message userMessage = promptTemplate.createMessage(parameters);
            SystemMessage systemMessage = new SystemMessage(evaluationConfig.getSystemPrompt());

            List<EvaluationMetricsResult.Metric> result = callLLMForEvaluation(new Prompt(systemMessage, userMessage));
            if (!result.isEmpty()) {
                metrics.addAll(result);
            }
        }

        return metrics;
    }

    /**
     * 执行多轮会话评测
     *
     * @param request 评测请求
     * @return 多轮评测结果列表
     */
    private List<SessionEvaluationResultEntity> executeMultiTurnEvaluation(MultiEvaluationRequest request) {
        log.info("开始执行多轮对话评测，sessionId: {}, messageId: {}, bizScene: {}, modelScene: {}, standardAnswer: {}",
                request.getSessionId(), request.getMessageId(), request.getBizScene(), request.getModelScene(),
                request.getReferenceAnswer() != null ? "已设置" : "未设置");

        MultiEvaluationRequest.Message dialog = request.getDialog();
        // 这里的userPrompt实际上是被评测的大模型的 systemPrompt、rag、context、userPrompt和 assistantAnswer
        // String ragContent = getRagContent(request);
        Map<String, Object> parameters = Map.of(
                "system", "",// 被评测的大模型的系统提示
                "context", request.getContext() == null ? "" : request.getContext(),// 被评测的大模型的用户上下文信息
                "referenceAnswer", request.getReferenceAnswer() == null ? "" : request.getReferenceAnswer()// 标准答案
        );
        List<EvaluationMetricsResult.Metric> metrics = executeEvaluationWithParameters(request.getBizScene(),
                request.getModelScene(),
                parameters, MultiEvaluationTypeEnum.MULTI.getType());

        log.info("多轮对话评测结果，sessionId: {}, messageId: {}, bizScene: {}, modelScene: {}, metrics: {}",
                request.getSessionId(), request.getMessageId(), request.getBizScene(), request.getModelScene(), JsonUtils.toJsonString(metrics));


        List<SessionEvaluationResultEntity> sessionResults = new ArrayList<>();
        try {

            // 查询当前会话的所有评测结果
            List<SessionEvaluationResultEntity> existingSessionResults = sessionEvaluationResultRepository
                    .selectBySessionId(request.getSessionId());

            // 将现有结果按key分组
            Map<String, SessionEvaluationResultEntity> existingResultMap = existingSessionResults.stream()
                    .collect(Collectors.toMap(SessionEvaluationResultEntity::getItem, result -> result, (v1, v2) -> v1));

            // 处理当前消息的每个评测结果
            for (EvaluationMetricsResult.Metric metric : metrics) {
                String key = metric.getItem();
                BigDecimal currentScore = metric.getScore();

                SessionEvaluationResultEntity sessionResult = existingResultMap.get(key);

                if (sessionResult != null) {
                    // 更新现有的会话评测结果
                    Long messageCnt = sessionResult.getMessageCnt();
                    BigDecimal existingScore = sessionResult.getScore();

                    // 计算新的平均分数: (score*messageCnt+当前对话score)/(messageCnt+1)
                    BigDecimal newScore = existingScore.multiply(BigDecimal.valueOf(messageCnt))
                            .add(currentScore)
                            .divide(BigDecimal.valueOf(messageCnt + 1), 2, RoundingMode.HALF_UP);
                    Long newMessageCnt = messageCnt + 1;

                    sessionResult.setScore(newScore);
                    sessionResult.setMessageCnt(newMessageCnt);
                    sessionResult.setReason(metric.getReason());

                    // 更新数据库
                    sessionEvaluationResultRepository.updateBySessionIdAndKey(sessionResult);
                    sessionResults.add(sessionResult);

                    log.info("更新多轮对话评测结果，key: {}, 原分数: {}, 新分数: {}, 消息数: {}", key, existingScore, newScore, newMessageCnt);

                } else {
                    // 创建新的会话评测结果
                    SessionEvaluationResultEntity newSessionResult = SessionEvaluationResultEntity.builder()
                            .bizScene(request.getBizScene())
                            .sessionId(request.getSessionId())
                            .modelScene(request.getModelScene())
                            .item(key)
                            .description(metric.getDescription())
                            .score(currentScore)
                            .type(1) // 多轮类型
                            .messageCnt(1L)
                            .source(request.getSource())
                            .addTime(new Date())
                            .updateTime(new Date())
                            .reason(metric.getReason())
                            .evaluationId(request.getEvaluationId())
                            .evaluationVer(request.getEvaluationVer())
                            .build();

                    // 保存到数据库
                    sessionEvaluationResultRepository.insert(newSessionResult);
                    sessionResults.add(newSessionResult);
                    // session+key是唯一的，因此如果出现重复的评测项，插入的时候会报错
                    existingResultMap.put(key, newSessionResult);

                    log.info("创建新的多轮对话评测结果，key: {}, 分数: {}", key, currentScore);
                }
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        if (!sessionResults.isEmpty()) {
            log.info("多轮对话评测执行完成，处理结果数量: {}", sessionResults.size());
        } else {
            log.warn("多轮对话评测执行完成，处理结果为空, sessionId: {}, messageId: {}, bizScene: {}, modelScene: {}", request.getSessionId(), request.getMessageId()
                    , request.getBizScene(), request.getModelScene());
        }
        return sessionResults;
    }


    /**
     * 从海马平台获取评测prompt配置
     *
     * @param type 0单轮，1多轮，2单轮多轮
     * @return prompt列表
     */
    private List<MultiEvaluationConfig> getEvaluationPromptsFromHaima(String sceneKey, String bizScene,
                                                                      String modelScene, String type) {
        log.info("从海马平台获取评测prompt配置，sceneKey: {},bizScene: {}, modelScene: {}, type: {}", sceneKey, bizScene, modelScene, type);
        List<HaimaContent> evaluationConfigs = haimaAcl.getContent(sceneKey, null);
        List<MultiEvaluationConfig> filteredEvaluationConfigs = evaluationConfigs.stream()
                .map(content -> MultiEvaluationConfig.builder()
                        .bizScene(content.getContentString("bizScene"))
                        .modelScene(content.getContentString("modelScene"))
                        .systemPrompt(content.getContentString("systemPrompt"))
                        .userPrompt(content.getContentString("userPrompt"))
                        .type(content.getContentString("type"))
                        .build())
                .filter(c ->
//                        Objects.equals(bizScene, c.getBizScene())
//                                &&
                        Objects.equals(modelScene, c.getModelScene()))
                .toList();
        if (filteredEvaluationConfigs.isEmpty()) {
            log.error("未找到有效的评价配置，sceneKey: {},bizScene: {}, modelScene: {}, type: {}", sceneKey, bizScene, modelScene, type);
        }

        return filteredEvaluationConfigs;
    }

    /**
     * 调用大模型进行评测
     *
     * @param prompt 评测prompt
     * @return 评测结果
     */
    private List<EvaluationMetricsResult.Metric> callLLMForEvaluation(Prompt prompt) {
        if (log.isDebugEnabled()) {
            log.debug("调用大模型进行评测的逻辑，prompt: {}", prompt);
        }

        // 统计大模型调用时间
        long startTime = System.currentTimeMillis();
        EvaluationMetricsResult evaluationMetricsResult = null;
        try {
            evaluationMetricsResult = evaluationChatClient.prompt(prompt).call().entity(EvaluationMetricsResult.class);
        } catch (NonTransientAiException e) {
            boolean requestLimit = e.getMessage().contains("每分钟请求次数超过限制");
            if (requestLimit) {
                Cat.logError("每分钟请求次数超过限制", e);
                log.error("每分钟请求次数超过限制", e);
            }
        }
        catch (Exception e) {
            log.error("调用大模型进行评测失败，prompt: {}", prompt, e);
        }
        log.info("大模型调用耗时: {} 毫秒", System.currentTimeMillis() - startTime);
        if (evaluationMetricsResult != null && evaluationMetricsResult.getMetrics() != null && !evaluationMetricsResult.getMetrics().isEmpty()) {
            if (log.isDebugEnabled()) {
                evaluationMetricsResult.getMetrics().forEach(metric -> log.debug("评测结果：{}", metric));
            }
            return evaluationMetricsResult.getMetrics();
        }
        return Collections.emptyList();
    }

    /**
     * 根据 sessionId 和 messageId 查询评测结果
     *
     * @param sessionId 会话ID
     * @param messageId 消息ID
     * @return 评测结果封装为 EvaluationResultsResponse
     */
    public EvaluationResultsResponse getEvaluationResultsBySessionAndMessage(String sessionId, String messageId) {
        Transaction transaction = Cat.newTransaction(MultiDialogueEvaluation.class.getSimpleName(), "getEvaluationResultsBySessionAndMessage");
        try {
            log.info("开始查询评测结果，sessionId: {}, messageId: {}", sessionId, messageId);
            if (StringUtils.isBlank(sessionId)) {
                return new EvaluationResultsResponse();
            }
            // 查询单轮评测结果
            List<MessageEvaluationResultEntity> messageResults = messageEvaluationResultRepository
                    .selectBySessionIdAndMessageId(messageId);

            // 查询多轮评测结果
            List<SessionEvaluationResultEntity> sessionResults = sessionEvaluationResultRepository
                    .selectBySessionId(sessionId);

            // 构建响应
            EvaluationResultsResponse response = new EvaluationResultsResponse();
            response.setMessageResults(messageResults);
            response.setSessionResults(sessionResults);

            log.info("评测结果查询成功，sessionId: {}, messageId: {}", sessionId, messageId);
            transaction.setSuccessStatus();
            return response;

        } catch (Exception e) {
            log.error("评测结果查询失败，sessionId: {}, messageId: {}", sessionId, messageId, e);
            transaction.setStatus(e);
            return new EvaluationResultsResponse();
        } finally {
            transaction.complete();
        }
    }


    public List<SessionEvaluationResultEntity> getSessionEvaluationResults(String sessionId) {
        Transaction transaction = Cat.newTransaction(MultiDialogueEvaluation.class.getSimpleName(), "getEvaluationResultsBySessionAndMessage");
        try {
            log.info("开始查询评测结果, sessionId: {}", sessionId);
            // 查询多轮评测结果
            List<SessionEvaluationResultEntity> sessionResults = sessionEvaluationResultRepository
                    .selectBySessionId(sessionId);
            log.info("评测结果查询成功， sessionId: {}", sessionId);
            transaction.setSuccessStatus();
            return sessionResults;
        } catch (Exception e) {
            log.error("评测结果查询失败，sessionId: {}", sessionId, e);
            transaction.setStatus(e);
            return null;
        } finally {
            transaction.complete();
        }
    }

    public List<MessageEvaluationResultEntity> getMessageEvaluationResults(String messageId) {
        Transaction transaction = Cat.newTransaction(MultiDialogueEvaluation.class.getSimpleName(), "getEvaluationResultsBySessionAndMessage");
        try {
            log.info("开始查询评测结果, messageId: {}", messageId);
            // 查询单轮评测结果
            List<MessageEvaluationResultEntity> messageResults = messageEvaluationResultRepository
                    .selectBySessionIdAndMessageId(messageId);

            log.info("评测结果查询成功， messageId: {}", messageId);
            transaction.setSuccessStatus();
            return messageResults;
        } catch (Exception e) {
            log.error("评测结果查询失败, messageId: {}", messageId, e);
            transaction.setStatus(e);
            return null;
        } finally {
            transaction.complete();
        }
    }
}
