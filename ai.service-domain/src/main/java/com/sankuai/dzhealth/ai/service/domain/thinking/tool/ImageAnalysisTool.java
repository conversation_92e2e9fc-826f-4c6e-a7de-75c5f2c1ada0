package com.sankuai.dzhealth.ai.service.domain.thinking.tool;

import com.sankuai.dzhealth.ai.service.infrastructure.acl.ImageAnalysisAcl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.tool.annotation.Tool;
import org.springframework.ai.tool.annotation.ToolParam;
import org.springframework.stereotype.Component;

/**
 * 图片解析工具，供 LLM 在思考过程中调用
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ImageAnalysisTool {

    private final ImageAnalysisAcl imageAnalysisAcl;

    @Tool(description = "根据图片URL和上下文信息进行分析，返回包含图片链接和分析内容的 Markdown")
    public String analyzeImage(
            @ToolParam(description = "图片 URL", required = true) String imageUrl,
            @ToolParam(description = "图片的上下文信息，比如图片前后的文字内容，用于让分析结果更自然地融入整体内容") String context) {
        try {
            String analysisContent = imageAnalysisAcl.getFullAnalysisOfImage(imageUrl, context);
            if (analysisContent != null) {
                // 构建包含图片链接和分析内容的 Markdown
                StringBuilder result = new StringBuilder();
                result.append("![图片](").append(imageUrl).append(")\n\n");
                result.append(analysisContent);
                return result.toString();
            } else {
                // 即使分析失败，也保留图片链接
                return "![图片](" + imageUrl + ")\n\n未能解析该图片";
            }
        } catch (Exception e) {
            log.error("analyze image error", e);
            // 出错时也保留图片链接
            return "![图片](" + imageUrl + ")\n\n解析图片时发生错误: " + e.getMessage();
        }
    }


}
