package com.sankuai.dzhealth.ai.service.domain.service.impl.xhs;

import com.google.common.collect.Lists;
import com.sankuai.dzhealth.ai.service.api.ESVectorStoreService;
import com.sankuai.dzhealth.ai.service.api.RemoteResponse;
import com.sankuai.dzhealth.ai.service.domain.prompt.XhsTemplateNotePrompt;
import com.sankuai.dzhealth.ai.service.dto.DocumentDTO;
import com.sankuai.dzhealth.ai.service.request.DocumentSearchRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 小红书笔记数据丰富服务 - 负责获取热门笔记、知识库等数据来丰富笔记生成上下文
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
public class XhsNoteDataEnricher {

    @Autowired
    private ESVectorStoreService esVectorStoreService;

    @Resource
    private ChatClient xhsNoteLLMClient;

    /**
     * 获取热门笔记数据
     *
     * @param batch 批次ID
     * @return 热门笔记内容字符串
     */
    public String getHotNotes(String query, String batch) {
        try {
            Map<String, List<String>> metaData = new HashMap<>();
            metaData.put("batch", Collections.singletonList(batch));
            metaData.put("status", Collections.singletonList("0"));
            metaData.put("channel", Collections.singletonList("redBookNote"));
            
            DocumentSearchRequest request = DocumentSearchRequest.builder()
                    .query(query)
                    .metaData(metaData)
                    .topK(10)
                    .build();
            
            RemoteResponse<List<DocumentDTO>> listRemoteResponse = esVectorStoreService.similaritySearch(request);

            if (listRemoteResponse.getSuccess() && CollectionUtils.isNotEmpty(listRemoteResponse.getData())) {
                return listRemoteResponse.getData().stream()
                        .map(DocumentDTO::getText)
                        .collect(Collectors.joining("------\n\n"))
                        .trim();
            }
            
            log.info("No hot notes found for direction: {}, batch: {}", query, batch);
            return "";
        } catch (Exception e) {
            log.error("Error retrieving hot notes for direction: {}, batch: {}", query, batch, e);
            return "";
        }
    }

    /**
     * 获取知识库数据
     *
     * @return 知识库内容字符串
     */
    public String getKnowledge(String query) {
        try {

            Map<String, List<String>> metaData = new HashMap<>();
            metaData.put("channel", Lists.newArrayList("mart_general_beauty.medical_guide", "mart_general_beauty.medical_baike"));

            DocumentSearchRequest documentSearchRequest = DocumentSearchRequest.builder()
                    .query(query)
                    .topK(100)
                    .metaData(metaData)
                    .build();
            
            RemoteResponse<List<DocumentDTO>> listRemoteResponse = esVectorStoreService.similaritySearch(documentSearchRequest);
            
            if (listRemoteResponse.getSuccess() && CollectionUtils.isNotEmpty(listRemoteResponse.getData())) {
                String knowledge = listRemoteResponse.getData().stream()
                        .map(DocumentDTO::getText)
                        .collect(Collectors.joining("------\n\n"))
                        .trim();
                log.info("Successfully retrieved {} knowledge entries for direction: {}", listRemoteResponse.getData().size(), query);
                return knowledge;
            }
            
            log.warn("No knowledge found for direction: {}", query);
            return "";
        } catch (Exception e) {
            log.error("Error retrieving knowledge for direction: {}", query, e);
            return "";
        }
    }

    /**
     * 根据热门笔记生成笔记模板
     * 
     * @param hotNotes 热门笔记内容
     * @return 笔记模板字符串
     */
    public String getNoteTemplate(String hotNotes) {
        try {
            if (StringUtils.isBlank(hotNotes)) {
                log.warn("Hot notes is empty, cannot generate note template");
                return "";
            }
            
            String prompt = XhsTemplateNotePrompt.builder().hotNotes(hotNotes).build();
            String result = xhsNoteLLMClient.prompt().user(prompt).call().content();
            String processedResult = processMdJson(result);
            
            log.debug("Successfully generated note template from hot notes");
            return processedResult;
        } catch (Exception e) {
            log.error("Error generating note template from hot notes", e);
            return "";
        }
    }

    /**
     * 处理Markdown格式的JSON字符串
     */
    private String processMdJson(String mdJson) {
        if (StringUtils.isBlank(mdJson)) {
            return "";
        }
        return mdJson.replace("```", "").replace("json", "").trim();
    }
} 