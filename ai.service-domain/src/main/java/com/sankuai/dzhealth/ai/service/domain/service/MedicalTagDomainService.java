package com.sankuai.dzhealth.ai.service.domain.service;

import com.dianping.carnation.dto.TagItem;
import com.dianping.carnation.dto.TagTree;
import com.meituan.medicine.constant.response.IResponse;
import com.sankuai.dzhealth.ai.service.agent.infrastructure.proxy.product.TagServiceProxy;
import com.sankuai.dzhealth.ai.service.agent.domain.model.product.Product;
import com.sankuai.dzhealth.ai.service.agent.domain.model.product.ProductTag;
import com.sankuai.dzhealth.ai.service.dto.MedicalTagItem;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 医美标签相关业务逻辑处理
 * 负责处理医美项目标签的查询、映射和分级
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MedicalTagDomainService {

    private final ProductDomainService productDomainService;
    private final TagServiceProxy tagServiceProxy;

    /**
     * 重载方法：直接复用已查询到的商品信息，避免再次调用RPC
     */
    public Map<Integer, MedicalTagItem> queryProjectTagLevels(Product product) {
        if (product == null) {
            return Collections.emptyMap();
        }

        Long productId = null;
        if (product.getBasic() != null) {
            // 优先使用bizProductId
            if (product.getBasic().getBizProductId() != null) {
                productId = product.getBasic().getBizProductId();
            } else if (product.getBasic().getDpProductId() != null) {
                productId = product.getBasic().getDpProductId();
            } else if (product.getBasic().getMtProductId() != null) {
                productId = product.getBasic().getMtProductId();
            }
        }

        // 直接使用商品中的标签
        List<Long> skuTagIds = extractSkuTagIds(product);
        if (CollectionUtils.isEmpty(skuTagIds)) {
            log.warn("queryProjectTagLevels(product): no tags found for productId={}", productId);
            return Collections.emptyMap();
        }

        // 泛商品标签 → 医美项目标签映射
        Set<Integer> medicalTagIds = mapToMedicalTags(skuTagIds);
        if (CollectionUtils.isEmpty(medicalTagIds)) {
            log.warn("queryProjectTagLevels(product): no medical tags found for productId={}", productId);
            return Collections.emptyMap();
        }

        // 构建医美标签分级信息
        Map<Integer, MedicalTagItem> result = buildMedicalTagLevels(medicalTagIds, productId);

        log.info("queryProjectTagLevels(product) success: productId={}, found {} medical tags", productId, result.size());
        return result;
    }

    /**
     * 从商品模型中提取标签ID
     */
    private List<Long> extractSkuTagIds(Product product) {
        List<Long> skuTagIds = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(product.getTags())) {
            for (ProductTag tag : product.getTags()) {
                if (tag.getTagId() != null) {
                    skuTagIds.add(tag.getTagId());
                }
            }
        }
        return skuTagIds;
    }

    /**
     * 将泛商品标签映射为医美项目标签
     */
    private Set<Integer> mapToMedicalTags(List<Long> skuTagIds) {
        if (CollectionUtils.isEmpty(skuTagIds)) {
            return Collections.emptySet();
        }

        try {
            IResponse<Map<Long, List<Integer>>> resp = tagServiceProxy.queryTagMapping(skuTagIds);
            if (resp == null || !resp.isSuccess() || MapUtils.isEmpty(resp.getResult())) {
                log.warn("mapToMedicalTags: tag mapping failed, skuTagIds={}", skuTagIds);
                return Collections.emptySet();
            }

            Set<Integer> medicalTagIds = new HashSet<>();
            resp.getResult().values().forEach(ids -> {
                if (CollectionUtils.isNotEmpty(ids)) {
                    medicalTagIds.addAll(ids);
                }
            });
            return medicalTagIds;
        } catch (Exception e) {
            log.error("mapToMedicalTags exception, skuTagIds={}", skuTagIds, e);
            return Collections.emptySet();
        }
    }

    /**
     * 构建医美标签的分级信息
     */
    private Map<Integer, MedicalTagItem> buildMedicalTagLevels(Set<Integer> medicalTagIds, Long productId) {
        try {
            Map<Integer, MedicalTagItem> result = new HashMap<>();
            TagTree tagTree = tagServiceProxy.getTagTree();
            if (tagTree == null) {
                log.warn("buildMedicalTagLevels: tag tree is null for productId={}", productId);
                return Collections.emptyMap();
            }

            // 一级标签
            if (CollectionUtils.isNotEmpty(tagTree.getFirstTagList())) {
                for (TagItem item : tagTree.getFirstTagList()) {
                    if (medicalTagIds.contains(item.getId())) {
                        result.put(item.getId(), new MedicalTagItem(item.getId(), 1));
                    }
                }
            }

            // 二级标签
            if (MapUtils.isNotEmpty(tagTree.getSecondTagMap())) {
                for (List<TagItem> list : tagTree.getSecondTagMap().values()) {
                    if (CollectionUtils.isNotEmpty(list)) {
                        for (TagItem item : list) {
                            if (medicalTagIds.contains(item.getId())) {
                                result.put(item.getId(), new MedicalTagItem(item.getId(), 2));
                            }
                        }
                    }
                }
            }

            // 三级标签
            if (MapUtils.isNotEmpty(tagTree.getThirdTagMap())) {
                for (List<TagItem> list : tagTree.getThirdTagMap().values()) {
                    if (CollectionUtils.isNotEmpty(list)) {
                        for (TagItem item : list) {
                            if (medicalTagIds.contains(item.getId())) {
                                result.put(item.getId(), new MedicalTagItem(item.getId(), 3));
                            }
                        }
                    }
                }
            }

            return result;
        } catch (Exception e) {
            log.error("buildMedicalTagLevels exception, productId={}, medicalTagIds={}", productId, medicalTagIds, e);
            return Collections.emptyMap();
        }
    }
}

