package com.sankuai.dzhealth.ai.service.agent.domain.process.card;

import com.sankuai.dzhealth.ai.service.agent.domain.context.MessageContext;
import com.sankuai.dzhealth.ai.service.dto.stream.StreamEventCardDataDTO;
import com.sankuai.dzhealth.ai.service.enums.StreamEventCardTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * @author:chenwei
 * @time: 2025/8/5 09:40
 * @version: 0.0.1
 */

@Slf4j
@Service
public class PhotoTakingCardBuilder implements CardBuilder {
    @Override
    public boolean accept(String cardType) {
        return StreamEventCardTypeEnum.PHOTO_TAKING.getType().equals(cardType);
    }

    @Override
    public void padding(StreamEventCardDataDTO streamEventCardDataDTO, MessageContext messageContext) {
        log.info("<photoTakingCardBuilder>padding>, key={}", streamEventCardDataDTO.getKey());
        Map<String, Object> cardProps = new HashMap<>();
        cardProps.put("title", "一键拍照");
        streamEventCardDataDTO.setCardProps(cardProps);

    }

    @Override
    public String parseCardProps(StreamEventCardDataDTO streamEventCardDataDTO) {
        return "";
    }
}
