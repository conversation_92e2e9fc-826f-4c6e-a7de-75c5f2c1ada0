package com.sankuai.dzhealth.ai.service.domain.service;

import com.sankuai.dzhealth.ai.service.agent.domain.model.product.Product;
import com.sankuai.general.product.query.center.client.enums.IdTypeEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 商品类目相关业务逻辑处理
 * 负责处理不同商品体系（团单、泛商品）的类目信息提取
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ProductCategoryDomainService {

    private final ProductDomainService productDomainService;

    /**
     * 查询商品的三级类目ID，用于口腔场景构造精确的haima sceneKey
     *
     * @param productId 商品ID
     * @param idType    ID类型：1-点评团购，2-美团团购，3-泛商品
     * @return 三级类目ID，如果查询失败返回null
     */
    public String queryThirdCategoryId(Long productId, Integer idType) {
        try {
            // 根据idType确定商品体系类型
            IdTypeEnum idTypeEnum = determineIdTypeEnum(idType);

            // 使用商品领域服务查询商品信息
            Product product = productDomainService.findProduct(productId, idTypeEnum);
            if (product == null || product.getCategory() == null) {
                log.warn("queryThirdCategoryId: no product data found, productId={}, idType={}",
                        productId, idType);
                return null;
            }

            // 从商品类目信息中获取最精确的类目ID
            String categoryId = product.getCategory().getMostPreciseCategoryId();
            if (categoryId != null) {
                log.info("queryThirdCategoryId success: productId={}, idType={}, categoryId={}",
                        productId, idType, categoryId);
            } else {
                log.warn("queryThirdCategoryId: no category found, productId={}, idType={}",
                        productId, idType);
            }

            return categoryId;
        } catch (Exception e) {
            log.error("queryThirdCategoryId exception, productId={}, idType={}", productId, idType, e);
            return null;
        }
    }


    /**
     * 根据idType确定IdTypeEnum
     */
    private IdTypeEnum determineIdTypeEnum(Integer idType) {
        if (idType != null && idType == 1) {
            return IdTypeEnum.DP;
        } else if (idType != null && idType == 2) {
            return IdTypeEnum.MT;
        } else {
            return IdTypeEnum.BIZ_PRODUCT;
        }
    }
}

