package com.sankuai.dzhealth.ai.service.agent.domain.process.card.data;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

/**
 * @author:chenwei
 * @time: 2025/7/14 14:30
 * @version: 0.0.1
 */

@Data
@Builder
@AllArgsConstructor
public class ThinkProcessCardData {

    private String loadingText;

    @JsonProperty("isFinish")
    private boolean isFinish;

    private String content;


    public ThinkProcessCardData() {
        this.loadingText = "";
        this.isFinish = false;
        this.content = "";
    }
}
