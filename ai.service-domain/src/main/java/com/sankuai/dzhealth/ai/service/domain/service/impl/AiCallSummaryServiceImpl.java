package com.sankuai.dzhealth.ai.service.domain.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.dianping.haima.entity.haima.HaimaContent;
import com.dianping.lion.common.util.JsonUtils;
import com.meituan.mdp.boot.starter.thrift.annotation.MdpThriftServer;
import com.sankuai.dzhealth.ai.exception.BizException;
import com.sankuai.dzhealth.ai.service.domain.chat.model.ChatSummaryResult;
import com.sankuai.dzhealth.ai.service.domain.enums.SummaryResultType;
import com.sankuai.dzhealth.ai.service.domain.enums.SummarySceneEnum;
import com.sankuai.dzhealth.ai.service.domain.service.ChatSummaryService;
import com.sankuai.dzhealth.ai.service.infrastructure.acl.HaimaAcl;
import com.sankuai.dzhealth.ai.service.request.ChatSummaryRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static java.util.stream.Collectors.toMap;

/**
 * @Author: zhongchangze
 * @Date: 2025/3/24 11:49
 * @Description:
 */
@MdpThriftServer
@Slf4j
public class AiCallSummaryServiceImpl implements ChatSummaryService {

    private static final String AI_HOSPITAL_INSTRUCTION_HAI_KEY = "ai_hospital_instruction";

    private static final String TEL_CONTENT_SUMMARY = "tel_content_summary";

    private static final String SHOP_REPLY_PREFIX = "医院:";

    @Autowired
    private ChatClient.Builder summaryChatClientBuilder;
    @Autowired
    private HaimaAcl haimaAcl;

    @Override
    public ChatSummaryResult getChatSummary(ChatSummaryRequest chatSummaryRequest) {
        Transaction transaction = Cat.newTransaction("AiCallSummaryServiceImpl", "getChatSummary");
        log.info("ChatSummaryService.getChatSummary request:{}", JsonUtils.toJson(chatSummaryRequest));
        try {
            String dialogueContent = chatSummaryRequest.getDialogueContent();

            //判断有无商户回复
            if (!judgeShopHadReply(dialogueContent)) {
                return new ChatSummaryResult(SummaryResultType.ANSWER_NOT_REPLY.getType());
            }

            //获取海马的prompt
            String summaryPrompt = getHaiMaPrompt();

            summaryPrompt = summaryPrompt.replace("${dialogueContent}", dialogueContent)
                    .replace("${userIntention}", chatSummaryRequest.getUserIntention());
            ChatClient.CallResponseSpec call = summaryChatClientBuilder.build().prompt().user(summaryPrompt).call();

            ChatSummaryResult chatSummaryResult = buildSummaryResult(call);
            log.info("ChatSummaryService.getChatSummary response:{}", JsonUtils.toJson(chatSummaryResult));
            return chatSummaryResult;
        } catch (Exception e) {
            transaction.setStatus(e);
            log.error("AiCallSummaryServiceImpl.getChatSummary error, chatSummaryRequest:{}", JsonUtils.toJson(chatSummaryRequest), e);
            return new ChatSummaryResult(SummaryResultType.SUMMARY_FAILED.getType());
        } finally {
            transaction.complete();
        }

    }

    private ChatSummaryResult buildSummaryResult(ChatClient.CallResponseSpec call) {
        ChatSummaryResult chatSummaryResult = new ChatSummaryResult();

        String summaryStr = call.chatResponse().getResult().getOutput().getText();
        log.info("AiCallSummaryServiceImpl.buildSummaryResult summaryStr:{}", summaryStr);

        //summaryStr有可能会是markdown格式.  因此取第一个{和左后一个}中间的字符串
        String handledSummaryStr = getValidSummaryStr(summaryStr);

        JSONObject jsonObject = JSON.parseObject(handledSummaryStr);
        String conclusion = jsonObject.getString("conclusion");
        String relatedAnswers = jsonObject.getString("relatedAnswers");
        Integer conclusionType = jsonObject.getInteger("conclusionType");
        SummaryResultType summaryResultType = SummaryResultType.getResultType(conclusionType);
        if (Objects.isNull(summaryResultType)) {
            summaryResultType = SummaryResultType.SUMMARY_FAILED;
        }
        chatSummaryResult.setSummaryResult(summaryResultType.getType());
        chatSummaryResult.setConclusion(conclusion);
        chatSummaryResult.setRelatedAnswers(relatedAnswers);
        return chatSummaryResult;
    }

    private String getValidSummaryStr(String summaryStr) {
        if (StringUtils.isBlank(summaryStr)) {
            return StringUtils.EMPTY;
        }
        int leftIndex = summaryStr.indexOf('{');
        int rightIndex = summaryStr.lastIndexOf('}');
        if (leftIndex == -1 || rightIndex == -1) {
            throw new BizException("summaryStr格式错误");
        }
        return summaryStr.substring(leftIndex, rightIndex + 1);
    }

    private String getHaiMaPrompt() {

        List<HaimaContent> haimaContents = haimaAcl.getContent(AI_HOSPITAL_INSTRUCTION_HAI_KEY, null);
        if (CollectionUtils.isEmpty(haimaContents)) {
            return StringUtils.EMPTY;
        }
        Map<String, String> promptMap = haimaContents.stream().collect(toMap(
                e -> e.getContentString("promptKey"),
                e -> e.getContentString("promptContent"),
                (a, b) -> a
        ));
        return promptMap.getOrDefault(TEL_CONTENT_SUMMARY, StringUtils.EMPTY);
    }

    private boolean judgeShopHadReply(String message) {
        String[] split = message.split("\n");
        for (String text : split) {
            if (!text.startsWith(SHOP_REPLY_PREFIX)) {
                continue;
            }

            String regex = SHOP_REPLY_PREFIX + "\\s*(.*)";
            Pattern pattern = Pattern.compile(regex);
            Matcher matcher = pattern.matcher(text);

            // 检查是否匹配
            if (matcher.find()) {
                // 提取匹配的内容
                String result = matcher.group(1);
                if (StringUtils.isNotBlank(result)) {
                    return true;
                }
            }
        }
        return false;
    }

    @Override
    public boolean support(Integer sceneType) {
        return Objects.equals(SummarySceneEnum.MEDICAL_AI_CALL_SUMMARY.getScene(), sceneType);
    }
}