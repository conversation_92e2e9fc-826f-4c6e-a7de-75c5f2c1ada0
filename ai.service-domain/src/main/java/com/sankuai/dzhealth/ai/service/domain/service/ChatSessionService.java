package com.sankuai.dzhealth.ai.service.domain.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONValidator;
import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.dianping.cat.util.MetricHelper;
import com.dianping.cat.util.Pair;
import com.dianping.haima.entity.haima.HaimaContent;
import com.dianping.rhino.Rhino;
import com.dianping.rhino.threadpool.DefaultThreadPoolProperties;
import com.dianping.rhino.threadpool.ThreadPool;
import com.google.common.collect.Lists;
import com.meituan.mdp.boot.starter.config.annotation.MdpConfig;
import com.sankuai.dzhealth.ai.service.domain.card.CardHandlerManager;
import com.sankuai.dzhealth.ai.service.domain.chat.client.IntentionChatConfig;
import com.sankuai.dzhealth.ai.service.domain.chat.model.ChatSessionBO;
import com.sankuai.dzhealth.ai.service.domain.chat.model.ChatSessionMessageBO;
import com.sankuai.dzhealth.ai.service.domain.chat.model.IntentionResult;
import com.sankuai.dzhealth.ai.service.enums.MetadataKeyEnum;
import com.sankuai.dzhealth.ai.service.domain.prompt.IntentionPromptConfig;
import com.sankuai.dzhealth.ai.service.domain.prompt.IntentionPromptService;
import com.sankuai.dzhealth.ai.service.domain.service.strategy.*;
import com.sankuai.dzhealth.ai.service.domain.utils.context.AiAnswerContext;
import com.sankuai.dzhealth.ai.service.domain.utils.context.RequestContext;
import com.sankuai.dzhealth.ai.service.domain.utils.context.RequestContextConstant;
import com.sankuai.dzhealth.ai.service.dto.SseAwareException;
import com.sankuai.dzhealth.ai.service.dto.stream.StreamEventCardDataDTO;
import com.sankuai.dzhealth.ai.service.dto.stream.StreamEventDTO;
import com.sankuai.dzhealth.ai.service.dto.stream.StreamEventDataDTO;
import com.sankuai.dzhealth.ai.service.enums.*;
import com.sankuai.dzhealth.ai.service.infrastructure.acl.HaimaAcl;
import com.sankuai.dzhealth.ai.service.infrastructure.acl.ShopAcl;
import com.sankuai.dzhealth.ai.service.infrastructure.repository.conversation.ChatMessageRepository;
import com.sankuai.dzhealth.ai.service.infrastructure.dal.entity.ChatConversationEntity;
import com.sankuai.dzhealth.ai.service.infrastructure.dal.entity.ChatMessageEntity;
import com.sankuai.dzhealth.ai.service.infrastructure.dal.entity.ChatMessageFeedbackEntity;
import com.sankuai.dzhealth.ai.service.infrastructure.dal.entity.ChatPhoneCallTaskEntity;
import com.sankuai.dzhealth.ai.service.infrastructure.observability.Span;
import com.sankuai.dzhealth.ai.service.infrastructure.phoneCallTask.ChatPhoneCallTaskRepository;
import com.sankuai.dzhealth.ai.service.infrastructure.security.AuditRequest;
import com.sankuai.dzhealth.ai.service.infrastructure.security.AuditResult;
import com.sankuai.dzhealth.ai.service.infrastructure.security.PorscheAuditAcl;
import com.sankuai.dzhealth.ai.service.infrastructure.utils.DateTimeTools;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.messages.AbstractMessage;
import org.springframework.ai.chat.model.ChatResponse;
import org.springframework.ai.chat.model.Generation;
import org.springframework.ai.document.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.sankuai.dzhealth.ai.service.domain.utils.JsonUtils.toJsonString;
import static java.util.stream.Collectors.toMap;
import static org.springframework.ai.chat.memory.ChatMemory.CONVERSATION_ID;
/**
 * @author:chenwei
 * @time: 2025/3/19 16:09
 * @version: 0.0.1
 */
@Slf4j
@Service
public class ChatSessionService {

    private static final String DEFAULT_INTENTION = "general";

    @Autowired
    private IntentionChatConfig intentionChatConfig;

    @Autowired
    private ChatClient answerChatClient;

    @Autowired
    private HaimaAcl haimaAcl;

    @Autowired
    private ChatMessageRepository chatMessageRepository;

    @Autowired
    private ChatPhoneCallTaskRepository chatPhoneCallTaskRepository;

    @Autowired
    private IntentionPromptService intentionPromptService;

    @Autowired
    private PorscheAuditAcl porscheAuditAcl;

    @Autowired
    private DateTimeTools dateTimeTools;

    @Autowired
    private DataSourceStrategyContext strategyContext;

    // 添加卡片处理器管理器
    @Autowired
    private CardHandlerManager cardHandlerManager;

    // 新增 ShopAcl，用于获取医院简称
    @Autowired
    private ShopAcl shopAcl;

    public static final ThreadPool TASK_POOL = Rhino.newThreadPool("CHAT_TASK_POOL",
            DefaultThreadPoolProperties.Setter().withCoreSize(100).withMaxSize(200).withMaxQueueSize(1000));

    public static final ThreadPool CHAT_STREAM_POOL = Rhino.newThreadPool("CHAT_STREAM_POOL",
            DefaultThreadPoolProperties.Setter().withCoreSize(100).withMaxSize(200).withMaxQueueSize(1000));

    @MdpConfig("chat.audit.type:100220")
    private Integer type = 100220;

    public CompletableFuture<List<StreamEventDTO>> chatStream(AiAnswerContext context) {

        CompletableFuture<List<StreamEventDTO>> completionFuture = new CompletableFuture<>();
        SseEmitter sseEmitter = RequestContext.getAttribute(RequestContextConstant.SSE_EMITTER);
        if (sseEmitter == null) {
            completionFuture.complete(Collections.emptyList());
            return completionFuture;
        }
        long startSseTime = System.currentTimeMillis();

        auditUserContent(context);

        long startTime;

        context.addSpan(Span.builder()
                .key("start")
                .value(String.valueOf(context.getSessionId()))
                .build());

        List<HaimaContent> haimaContents = haimaAcl.getContent("ai_hospital_instruction", null);
        if (CollectionUtils.isEmpty(haimaContents)) {
            throw new SseAwareException(StreamEventErrorTypeEnum.SERVER_ERROR);
        }
        Cat.logEvent("refreshPrompt", "success");
        Map<String, String> promptMap = haimaContents.stream().collect(toMap(
                e -> e.getContentString("promptKey"),
                e -> e.getContentString("promptContent"),
                (a, b) -> a
        ));
        String classifyTemplate = promptMap.getOrDefault("classify_rewirte", "");
        String combinePrompt = promptMap.getOrDefault("combine_output", "");

        sendSseContent(sseEmitter, StreamEventTypeEnum.SESSION.getType(), StreamEventDataTypeEnum.SESSION_ID.getType(), String.valueOf(context.getSessionId()));
        sendSseContent(sseEmitter, StreamEventTypeEnum.MESSAGE.getType(), StreamEventDataTypeEnum.MESSAGE_ID.getType(), String.valueOf(context.getMsgId()));
        String nowTime = dateTimeTools.getNowTime();
        IntentionPromptConfig intentionPrompt =
                intentionPromptService.getProjectIntentionPrompt(context.getBizScene());

        context.addSpan(Span.builder().key("intentionList").value(toJsonString(intentionPrompt.getList())).build());

        String[] split = classifyTemplate.split("<<UserPrompt>>");
        String system = split[0].replace("${intentionPrompt}", intentionPrompt.toIntentionPrompt());
        String user = split[1].replace("${query}", context.getContent())
                .replace("${history}", Optional.ofNullable(context.getHistory()).orElse(""))
                .replace("${datetime}", nowTime);
        sendSseContent(sseEmitter, StreamEventTypeEnum.MESSAGE.getType(), StreamEventDataTypeEnum.LOADING_STATUS.getType(), "正在识别中...");

        context.addSpan(Span.builder().key("classifyPrompt").value(system + "<<UserPrompt>>" + user).build());

        startTime = System.currentTimeMillis();
        // 创建 advisorParams 用于记录消息
        Map<String, Object> intentionAdvisorParams = new HashMap<>();
        intentionAdvisorParams.put("senderId", String.valueOf(context.getUserId()));
        intentionAdvisorParams.put("contentType", (byte) 1);
        intentionAdvisorParams.put("auditStatus", (byte) 1);

        ChatClient currentIntentionChatClient;
        try {
            currentIntentionChatClient = intentionChatConfig.intentionChatClient();
        } catch (Exception e) {
            log.error("获取ChatClient实例失败", e);
            throw new SseAwareException(StreamEventErrorTypeEnum.SERVER_ERROR);
        }

        Optional<String> originIntentionResult =
                Optional.ofNullable(currentIntentionChatClient.prompt().system(system).user(user)
                                .advisors(advisor -> advisor
                                        .params(intentionAdvisorParams)  // 设置消息参数
                                        .param(CONVERSATION_ID, String.valueOf(context.getSessionId())) // 设置会话ID
//                                       .param(CHAT_MEMORY_RETRIEVE_SIZE_KEY, 10) // 设置历史消息获取数量
                                ).call().chatResponse())
                        .map(ChatResponse::getResult)
                        .map(Generation::getOutput)
                        .map(AbstractMessage::getText)
                        .map(ChatSessionService::extractJsonContent);

        IntentionResult intentionResult;
        if (originIntentionResult.isPresent() && validJson(originIntentionResult.get())) {
            intentionResult = JSON.parseObject(originIntentionResult.get(), IntentionResult.class);
        } else {
            intentionResult = new IntentionResult();
            intentionResult.setKey(DEFAULT_INTENTION);
            intentionResult.setQuery(originIntentionResult.orElse(context.getContent()));
        }
        setFunctionCallJson(intentionPrompt, intentionResult);

        // 设置意图key到context中，供后续策略使用
        context.setIntentionKey(intentionResult.getKey());

        context.addSpan(Span.builder()
                .key("intentionLLM")
                .value(JSON.toJSONString(intentionResult))
                .duration(System.currentTimeMillis() - startTime)
                .build());
        Cat.newTransactionWithDuration(getClass().getSimpleName(), "intentionLLM",
                System.currentTimeMillis() - startTime).complete();
        Cat.newTransactionWithDuration(getClass().getSimpleName(), "SSE-intentionLLM",
                System.currentTimeMillis() - startSseTime).complete();
        MetricHelper.build().name("耗时打点").tag("scene", "intention").duration(System.currentTimeMillis() - startSseTime);


        String intention = Optional.ofNullable(intentionResult.getKey()).orElse(DEFAULT_INTENTION);
        // 根据意图和医院信息构建rewriteText
        String rewriteText = Optional.ofNullable(intentionResult.getQuery()).orElse(context.getContent());
        if (shouldIncludeHospitalName(intention)) {
            String hospitalName = getHospitalName(context);
            if (StringUtils.isNotBlank(hospitalName)) {
                rewriteText = hospitalName + " " + rewriteText;
            }
        }
        // 新增：记录rewriteText，供后续评价等流程使用
        context.addSpan(Span.builder().key("rewriteText").value(rewriteText).build());
        sendSseContent(sseEmitter, StreamEventTypeEnum.MESSAGE.getType(), StreamEventDataTypeEnum.LOADING_STATUS.getType(), "正在查询资料...");

        // 使用策略模式执行各种数据源查询，内部自动完成过滤
        startTime = System.currentTimeMillis();
        // 直接获取执行和过滤后的结果
        Map<String, BaseStrategyInfo> finalResults = strategyContext.executeStrategies(
            intentionResult, context, rewriteText);

        // 从结果中提取各个策略的信息
        SearchStrategy.SearchStrategyInfo searchInfo = (SearchStrategy.SearchStrategyInfo) finalResults.get("SearchStrategy");
        HospitalStrategy.HospitalStrategyInfo hospitalInfo = (HospitalStrategy.HospitalStrategyInfo) finalResults.get("HospitalStrategy");
        DepartmentStrategy.DepartmentStrategyInfo departmentInfo = (DepartmentStrategy.DepartmentStrategyInfo) finalResults.get("DepartmentStrategy");
        RagEsStrategy.RagStrategyInfo ragEsInfo = (RagEsStrategy.RagStrategyInfo) finalResults.get("RagEsStrategy");
        TimeStrategy.TimeStrategyInfo timeNow = (TimeStrategy.TimeStrategyInfo) finalResults.get("TimeStrategy");
        StdDepartmentStrategy.StdDepartStrategyInfo stdInfo = (StdDepartmentStrategy.StdDepartStrategyInfo) finalResults.get("StdDepartmentStrategy");
        PositionStrategy.PositionStrategyInfo positionInfo = (PositionStrategy.PositionStrategyInfo) finalResults.get("PositionStrategy");
        HistoryMessageStrategy.HistoryMessageInfo historyMessageInfo = (HistoryMessageStrategy.HistoryMessageInfo) finalResults.get("HistoryMessageStrategy");
        ShopRagEsStrategy.ShopRagStrategyInfo shopRagInfo = (ShopRagEsStrategy.ShopRagStrategyInfo) finalResults.get("ShopRagEsStrategy");

        if (stdInfo != null && MapUtils.isNotEmpty(stdInfo.getStdInfo())) {
            context.setStdDepartName2IdMap(stdInfo.getStdInfo());
        }
        if (searchInfo != null && CollectionUtils.isNotEmpty(searchInfo.getSearchInfo())) {
            context.setSearchInfo(searchInfo.getSearchInfo());
        }
        if (ragEsInfo != null && CollectionUtils.isNotEmpty(ragEsInfo.getRagInfo())) {
            context.setRagInfo(ragEsInfo.getRagInfo());
        }
        if (shopRagInfo != null && CollectionUtils.isNotEmpty(shopRagInfo.getRagInfo())) {
            context.setShopRagInfo(shopRagInfo.getRagInfo());
        }
        if (departmentInfo != null && CollectionUtils.isNotEmpty(departmentInfo.getDepartInfo())) {
            Map<String, Pair<Long, Long>> departMap = new HashMap<>();
            departmentInfo.getDepartInfo().forEach(e -> {
                if (StringUtils.isNotBlank(e.getTitle())) {
                    Long bizId = e.getBizId() == null ? 0L : e.getBizId();
                    Long stdId = Optional.ofNullable(e.getStdId()).orElse(0L);
                    Pair<Long, Long> idPair = Pair.from(bizId, stdId);
                    departMap.put(e.getTitle(), idPair);
                }
            });
            context.setDepartName2IdMap(departMap);
        }

        finalResults.values()
                .stream()
                .filter(Objects::nonNull)
                .map(BaseStrategyInfo::getSpan)
                .filter(Objects::nonNull)
                .forEach(context::addSpans);

        context.addSpan(Span.builder()
                .key("RagAll")
                .duration(System.currentTimeMillis() - startTime)
                .build());

        Cat.newTransactionWithDuration(getClass().getSimpleName(), "SSE-RagAll",
                System.currentTimeMillis() - startSseTime).complete();
        MetricHelper.build().name("耗时打点").tag("scene", "get_context_info").duration(System.currentTimeMillis() - startSseTime);

        String[] combinePromptSplit = combinePrompt.split("<<UserPrompt>>");
        String systemAnswerPrompt = combinePromptSplit[0].replace("${intentionPrompt}",
                Optional.ofNullable(intentionPrompt.toAnswerSystemPrompt(intention)).orElse(""));

        String userAnswerPrompt = combinePromptSplit[1].replace("${query}", rewriteText)
                .replace("${intention}", intentionPrompt.toAnswerUserPrompt(intention));

        context.addSpan(Span.builder()
                .key("answerPrompt")
                .value(systemAnswerPrompt + "<<UserPrompt>>" + userAnswerPrompt)
                .build());

        List<Document> documents = Stream.of(
                        createDocument(hospitalInfo, "当前医院"),
                        createDocument(departmentInfo, "本院科室"),
                        createDocument(ragEsInfo, "知识库文章"),
                        createDocument(searchInfo, "搜索引擎文章"),
                        createDocument(stdInfo, "标准科室"),
                        createDocument(historyMessageInfo, "历史对话信息"),
                        createDocument(context.getHistory(), "历史对话摘要"),
                        createDocument(positionInfo, "位置信息"),
                        createDocument(shopRagInfo, "医院知识大全"),
                        createDocument(timeNow, "当前时间")
                ).filter(Objects::nonNull)
                .filter(doc -> StringUtils.isNotBlank(doc.getText()))
                .collect(Collectors.toList());

        userAnswerPrompt = appendDocumentsToPrompt(userAnswerPrompt, documents);

        String documentsJson = JSON.toJSONString(documents);


        context.addSpan(Span.builder()
                .key("ragInfo")
                .value(documentsJson)
                .build());

        // 添加用于跟踪的变量
        List<StreamEventDTO> reply = new ArrayList<>();
        final StringBuilder fullContent = new StringBuilder();
        final StringBuilder visibleContent = new StringBuilder();
        final boolean[] isHiddenContent = {false};
        // 使用零宽空格作为分隔符
        final String hiddenSeparator = "\u200B";

        final String hiddenSeparatorV2 = "{";

        // 新增：用于跟踪已处理的JSON字段
        final Set<String> processedJsonFields = new HashSet<>();

        // 新增：用于保存部分JSON内容
        final StringBuilder partialJsonContent = new StringBuilder();

        // 保存最近收到的内容，用于检测跨片段的分隔符
        final StringBuilder recentContentBuffer = new StringBuilder(50);

        // 添加用于段落处理的标志位
        final boolean[] isFirstParagraph = {false};


        // 添加延迟确认缓冲区和标志位
        final StringBuilder suspectBuffer = new StringBuilder(20);  // 存储可能是分隔符开头的内容
        final boolean[] inSuspectMode = {false};  // 是否处于"怀疑模式"
        final String[] possibleSeparators = {"```", "`", "``"};  // 可能的分隔符开头

        Map<String, Object> advisorParams = new HashMap<>();
        advisorParams.put("senderId", String.valueOf(context.getUserId()));
        advisorParams.put("contentType", (byte) 1);
        advisorParams.put("auditStatus", (byte) 1);

        // 获取基本的聊天响应流
        startTime = System.currentTimeMillis();
        sendSseContent(sseEmitter, StreamEventTypeEnum.MESSAGE.getType(), StreamEventDataTypeEnum.LOADING_STATUS.getType(), "正在生成答案...");

        Flux<ChatResponse> chatResponseFlux = answerChatClient.prompt().system(systemAnswerPrompt).user(userAnswerPrompt)
                .advisors(advisor -> advisor
                        .params(advisorParams)  // 设置消息参数
                        .param(CONVERSATION_ID, String.valueOf(context.getSessionId())) // 设置会话ID
//                        .param(CHAT_MEMORY_RETRIEVE_SIZE_KEY, 10) // 设置历史消息获取数量
                ).stream().chatResponse();
        Cat.newTransactionWithDuration(getClass().getSimpleName(), "buildChatResponseFlux", System.currentTimeMillis() - startTime).complete();
        long startChatTime = System.currentTimeMillis();

        // 创建计数器用于生成序号
        AtomicInteger auditIndex = new AtomicInteger(0);
        AtomicInteger sseIndex = new AtomicInteger(0);

        // 订阅流并发送事件
        chatResponseFlux.flatMapSequential(chatResponse -> {
            String content = Optional.ofNullable(chatResponse)
                    .map(ChatResponse::getResult)
                    .map(Generation::getOutput)
                    .map(AbstractMessage::getText)
                    .orElse(org.apache.commons.lang3.StringUtils.EMPTY);
            if (StringUtils.isBlank(content)) {
                return Mono.empty();
            }
            return Mono.fromFuture(CompletableFuture.supplyAsync(() -> {
                long bizId = context.getMsgId() * 10000 + auditIndex.getAndIncrement();
                AuditResult contentAuditResult = porscheAuditAcl.audit(AuditRequest.builder()
                        .bizId(bizId)
                        .dataSource(9)
                        .transId(String.valueOf(bizId))
                        .userId(Long.valueOf(context.getUserId().replace("m-", "").replace("d-", "")))
                        .userIP("127.0.0.1")
                        .userSource(context.getPlatform().equals(1) ? 2 : 1)
                        .assistantId(context.getBizScene())
                        .textBody(Collections.singletonList(AuditRequest.TextBody.builder()
                                .name("AIGCText")
                                .value(content)
                                .desc("AIGCText")
                                .build()))
                        .type(type)
                        .build());
                if (!Objects.equals(contentAuditResult.getAdvice(), AuditResult.Advice.PASSED.getCode())) {
                    context.addSpan(Span.builder()
                            .key("auditLLM")
                            .value(contentAuditResult.getMsg())
                            .build());
                    throw new SseAwareException(StreamEventErrorTypeEnum.AUDIT_ERROR);
                }
                return content;
            }, CHAT_STREAM_POOL.getExecutor()));
        }).subscribe(content -> {
                    try {
                        // 从ChatResponse提取内容
                        if (StringUtils.isBlank(content)) {
                            return;
                        }
                        //首字符打点
                        if (fullContent.isEmpty()) {
                            Cat.newTransactionWithDuration(getClass().getSimpleName(), "TTFT", System.currentTimeMillis() - startChatTime).complete();
                            Cat.newTransactionWithDuration(getClass().getSimpleName(), "SSE-TTFT", System.currentTimeMillis() - startSseTime).complete();
                            MetricHelper.build().name("耗时打点").tag("scene", "TTFT").duration(System.currentTimeMillis() - startSseTime);
                            context.addSpan(
                                    Span.builder().key("TTFT").duration(System.currentTimeMillis() - startChatTime).build());
                        }


                        String processedContent = content;
                        if (processedContent.contains("\n")) {
                            isFirstParagraph[0] = true;
                        } else if (!isFirstParagraph[0] && processedContent.contains("，")) {
                            Cat.logEvent("replace", ",");
                            isFirstParagraph[0] = true;
                            processedContent = processedContent.replace("，", "\n\n");
                        }

                        // 添加到完整内容
                        fullContent.append(processedContent);

                        // 如果已经进入隐藏内容模式，不再向客户端发送
                        if (isHiddenContent[0]) {

                            context.setHasText(CollectionUtils.isNotEmpty(reply));
                            partialJsonContent.append(processedContent);
                            String currentPartialJson = partialJsonContent.toString();

                            if (currentPartialJson.length() > 10) {
                                List<StreamEventDTO> partialResults = processPartialJsonFields(
                                        currentPartialJson, context, sseEmitter, sseIndex, processedJsonFields, startChatTime);
                                if (CollectionUtils.isNotEmpty(partialResults)) {
                                    reply.addAll(partialResults);
                                }
                            }
                            return;
                        }

                        // 更新检测缓冲区
                        recentContentBuffer.append(processedContent);
                        // 保持缓冲区在合理大小
                        if (recentContentBuffer.length() > 50) {
                            recentContentBuffer.delete(0, recentContentBuffer.length() - 50);
                        }

                        // 新的分隔符识别和处理逻辑
                        if (inSuspectMode[0]) {
                            // 正在收集可能的分隔符
                            suspectBuffer.append(processedContent);
                            String currentBuffer = suspectBuffer.toString();

                            // 检查是否匹配 ```json 模式或 `json 模式
                            if (currentBuffer.contains("```json") ||
                                    (currentBuffer.startsWith("```") && currentBuffer.toLowerCase().contains("json")) ||
                                    currentBuffer.contains("`json") ||
                                    (currentBuffer.startsWith("`") && currentBuffer.toLowerCase().contains("json"))) {
                                // 确认是分隔符，进入隐藏模式
                                Cat.logEvent("json", currentBuffer);
                                isHiddenContent[0] = true;
                                //没有消息只放卡片的话
                                if (CollectionUtils.isNotEmpty(reply)) {
                                    context.addSpan(Span.builder().key("startExtraInfo").duration(System.currentTimeMillis() - startChatTime).build());
                                    StreamEventDTO like = sendCardEvent(StreamEventCardTypeEnum.FEEDBACK_TAIL_CARD,
                                            "like", null, sseEmitter, sseIndex.getAndIncrement());
                                    reply.add(like);
                                }
                                sseEmitter.send(SseEmitter.event().data(StreamEventDTO.builder().index(sseIndex.getAndIncrement()).type(StreamEventTypeEnum.MESSAGE.getType())
                                        .data(StreamEventDataDTO.builder().event(StreamEventDataTypeEnum.LOADING_STATUS.getType()).content("努力加载更多信息中").build()).build()));
                                Cat.newTransactionWithDuration(getClass().getSimpleName(), "SSE-Hidden", System.currentTimeMillis() - startSseTime).complete();
                                MetricHelper.build().name("耗时打点").tag("scene", "start_hidden").duration(System.currentTimeMillis() - startSseTime);
                                context.addSpan(
                                        Span.builder().key("notDisplayDuration").duration(System.currentTimeMillis() - startChatTime).build());

                                // 添加内容到partialJsonContent
                                int jsonStart = currentBuffer.toLowerCase().indexOf("json");
                                if (jsonStart >= 0) {
                                    // 查找json后面可能出现的第一个"{"
                                    int bracketIndex = currentBuffer.indexOf("{", jsonStart);
                                    if (bracketIndex >= 0) {
                                        partialJsonContent.append(currentBuffer.substring(bracketIndex));
                                        log.info("隐藏模式初始内容：{}", currentBuffer.substring(bracketIndex));
                                    }
                                }


                                // 重置状态
                                suspectBuffer.setLength(0);
                                inSuspectMode[0] = false;
                                return;
                            } else if (currentBuffer.length() > 15) {
                                // 缓冲区长度超过15个字符，判断不是分隔符
                                boolean stillSuspect = false;
                                for (String separator : possibleSeparators) {
                                    if (currentBuffer.startsWith(separator)) {
                                        stillSuspect = true;
                                        break;
                                    }
                                }

                                if (!stillSuspect) {
                                    // 不是分隔符，将内容发送给客户端
                                    log.info("确认不是分隔符，发送内容: {}", currentBuffer);
                                    visibleContent.append(currentBuffer);
                                    StreamEventDTO eventDTO = new StreamEventDTO();
                                    eventDTO.setType(StreamEventTypeEnum.MESSAGE.getType());
                                    eventDTO.setIndex(sseIndex.getAndIncrement());

                                    StreamEventDataDTO dataDTO = new StreamEventDataDTO();
                                    dataDTO.setEvent(StreamEventDataTypeEnum.MAIN_TEXT.getType());
                                    dataDTO.setContent(currentBuffer);
                                    dataDTO.setCardsData(new ArrayList<>());

                                    eventDTO.setData(dataDTO);
                                    reply.add(eventDTO);
                                    sseEmitter.send(eventDTO);

                                    // 重置状态
                                    suspectBuffer.setLength(0);
                                    inSuspectMode[0] = false;
                                }
                            }
                        } else {
                            // 检查当前内容是否可能是分隔符的开头或包含分隔符
                            boolean hasSeparator = false;
                            int separatorStartIndex = -1;
                            String matchedSeparator = null;
                            String bufferContent = recentContentBuffer.toString();

                            // 先检查当前片段是否包含完整分隔符
                            for (String separator : possibleSeparators) {
                                int index = processedContent.indexOf(separator);
                                if (index >= 0 && (separatorStartIndex == -1 || index < separatorStartIndex)) {
                                    separatorStartIndex = index;
                                    matchedSeparator = separator;
                                    hasSeparator = true;
                                }
                            }

                            // 如果当前片段没有完整分隔符，检查跨片段情况
                            if (!hasSeparator) {
                                String recentContent = recentContentBuffer.toString();
                                for (String separator : possibleSeparators) {
                                    if (separator.length() > 1) {
                                        // 多字符分隔符的跨片段检测
                                        for (int i = 1; i < separator.length(); i++) {
                                            if (recentContent.endsWith(separator.substring(0, i)) &&
                                                    processedContent.startsWith(separator.substring(i))) {
                                                hasSeparator = true;
                                                // 跨片段的情况，分隔符起始位置是当前片段的开始
                                                separatorStartIndex = 0;
                                                break;
                                            }
                                        }
                                    } else {
                                        // 单字符分隔符的检测 (例如 `)
                                        if (recentContent.endsWith(separator)) {
                                            // 检查如果最近内容以单引号结尾，新内容可能是json
                                            if (processedContent.toLowerCase().startsWith("json") ||
                                                    processedContent.toLowerCase().contains("json")) {
                                                hasSeparator = true;
                                                // 将json的开始位置标记为分隔符起始位置
                                                int jsonIndex = processedContent.toLowerCase().indexOf("json");
                                                if (jsonIndex >= 0) {
                                                    separatorStartIndex = jsonIndex;
                                                } else {
                                                    separatorStartIndex = 0;
                                                }
                                                break;
                                            }
                                        }
                                    }

                                    if (hasSeparator) {
                                        break;
                                    }
                                }
                            }

                            // 处理含有分隔符的情况
                            if (hasSeparator) {
                                // 检查是否还有可见部分需要发送
                                if (separatorStartIndex > 0) {
                                    // 发送分隔符前的可见部分
                                    String visiblePart = processedContent.substring(0, separatorStartIndex);
                                    visibleContent.append(visiblePart);

                                    StreamEventDTO eventDTO = new StreamEventDTO();
                                    eventDTO.setType(StreamEventTypeEnum.MESSAGE.getType());
                                    eventDTO.setIndex(sseIndex.getAndIncrement());

                                    StreamEventDataDTO dataDTO = new StreamEventDataDTO();
                                    dataDTO.setEvent(StreamEventDataTypeEnum.MAIN_TEXT.getType());
                                    dataDTO.setContent(visiblePart);
                                    dataDTO.setCardsData(new ArrayList<>());

                                    eventDTO.setData(dataDTO);
                                    reply.add(eventDTO);
                                    sseEmitter.send(eventDTO);

                                    log.info("发送分隔符前的可见部分: {}", visiblePart);
                                }

                                // 进入怀疑模式，只将分隔符部分存入缓冲区
                                if (matchedSeparator != null && processedContent.contains(matchedSeparator + "json")) {
                                    // 如果当前片段已经包含完整的分隔符+json，直接进入隐藏模式
                                    isHiddenContent[0] = true;
                                    //没有消息只放卡片的话
                                    if (CollectionUtils.isNotEmpty(reply)) {
                                        context.addSpan(Span.builder().key("startExtraInfo").duration(System.currentTimeMillis() - startChatTime).build());
                                        StreamEventDTO like = sendCardEvent(StreamEventCardTypeEnum.FEEDBACK_TAIL_CARD,
                                                "like", null, sseEmitter, sseIndex.getAndIncrement());
                                        reply.add(like);
                                    }
                                    Cat.newTransactionWithDuration(getClass().getSimpleName(), "SSE-Hidden", System.currentTimeMillis() - startSseTime).complete();
                                    MetricHelper.build().name("耗时打点").tag("scene", "start_hidden").duration(System.currentTimeMillis() - startSseTime);
                                    sseEmitter.send(SseEmitter.event().data(StreamEventDTO.builder().index(sseIndex.getAndIncrement()).type(StreamEventTypeEnum.MESSAGE.getType())
                                            .data(StreamEventDataDTO.builder().event(StreamEventDataTypeEnum.LOADING_STATUS.getType()).content("努力加载更多信息中").build()).build()));
                                    context.addSpan(
                                            Span.builder().key("notDisplayDuration").duration(System.currentTimeMillis() - startChatTime).build());

                                    // 添加内容到partialJsonContent
                                    int jsonIndex = processedContent.toLowerCase().indexOf("json");
                                    if (jsonIndex >= 0) {
                                        // 查找json后面可能出现的第一个"{"
                                        int bracketIndex = processedContent.indexOf("{", jsonIndex);
                                        if (bracketIndex >= 0) {
                                            partialJsonContent.append(processedContent.substring(bracketIndex));
                                        }
                                    }
                                } else {
                                    // 否则把分隔符部分放入怀疑缓冲区
                                    suspectBuffer.append(processedContent.substring(separatorStartIndex));
                                    inSuspectMode[0] = true;
                                }
                            }
                            // 其次检查是否包含直接的分隔符 { 或 \u200B
                            else if (bufferContent.contains(hiddenSeparator) || bufferContent.contains(hiddenSeparatorV2)) {
                                // 找到分隔符位置
                                int separatorIndex = !bufferContent.contains(hiddenSeparator) ?
                                        bufferContent.indexOf(hiddenSeparatorV2) : bufferContent.indexOf(hiddenSeparator);

                                // 计算当前片段中分隔符之前的内容长度
                                int visibleContentLength = separatorIndex - (bufferContent.length() - processedContent.length());

                                // 只有当分隔符位于当前内容中时才需要发送部分内容
                                if (visibleContentLength > 0) {
                                    String visiblePart = processedContent.substring(0, visibleContentLength);
                                    visibleContent.append(visiblePart);
                                    // 发送可见部分
                                    StreamEventDTO eventDTO = new StreamEventDTO();
                                    eventDTO.setType(StreamEventTypeEnum.MESSAGE.getType());
                                    eventDTO.setIndex(sseIndex.getAndIncrement());

                                    StreamEventDataDTO dataDTO = new StreamEventDataDTO();
                                    dataDTO.setEvent(StreamEventDataTypeEnum.MAIN_TEXT.getType());
                                    dataDTO.setContent(visiblePart);
                                    dataDTO.setCardsData(new ArrayList<>());

                                    eventDTO.setData(dataDTO);
                                    reply.add(eventDTO);
                                    sseEmitter.send(eventDTO);
                                }

                                // 设置标志位，后续内容不再发送
                                isHiddenContent[0] = true;
                                //没有消息只放卡片的话
                                if (CollectionUtils.isNotEmpty(reply)) {
                                    context.addSpan(Span.builder().key("startExtraInfo").duration(System.currentTimeMillis() - startChatTime).build());
                                    StreamEventDTO like = sendCardEvent(StreamEventCardTypeEnum.FEEDBACK_TAIL_CARD,
                                            "like", null, sseEmitter, sseIndex.getAndIncrement());
                                    reply.add(like);
                                }
                                Cat.newTransactionWithDuration(getClass().getSimpleName(), "SSE-Hidden", System.currentTimeMillis() - startSseTime).complete();
                                MetricHelper.build().name("耗时打点").tag("scene", "start_hidden").duration(System.currentTimeMillis() - startSseTime);
                                sseEmitter.send(SseEmitter.event().data(StreamEventDTO.builder().index(sseIndex.getAndIncrement()).type(StreamEventTypeEnum.MESSAGE.getType())
                                        .data(StreamEventDataDTO.builder().event(StreamEventDataTypeEnum.LOADING_STATUS.getType()).content("努力加载更多信息中").build()).build()));
                                context.addSpan(
                                        Span.builder().key("notDisplayDuration").duration(System.currentTimeMillis() - startChatTime).build());

                                // 添加内容到partialJsonContent
                                if (bufferContent.contains(hiddenSeparatorV2)) {
                                    int separatorPos = bufferContent.indexOf(hiddenSeparatorV2);
                                    // 如果分隔符在当前处理内容中
                                    if (separatorPos >= bufferContent.length() - processedContent.length()) {
                                        int relativePos = separatorPos - (bufferContent.length() - processedContent.length());
                                        partialJsonContent.append(processedContent.substring(relativePos));
                                    }
                                }
                            } else {
                                // 正常内容，直接发送
                                visibleContent.append(processedContent);
                                StreamEventDTO eventDTO = new StreamEventDTO();
                                eventDTO.setType(StreamEventTypeEnum.MESSAGE.getType());
                                eventDTO.setIndex(sseIndex.getAndIncrement());

                                StreamEventDataDTO dataDTO = new StreamEventDataDTO();
                                dataDTO.setEvent(StreamEventDataTypeEnum.MAIN_TEXT.getType());
                                dataDTO.setContent(processedContent);
                                dataDTO.setCardsData(new ArrayList<>());

                                eventDTO.setData(dataDTO);
                                reply.add(eventDTO);
                                sseEmitter.send(eventDTO);
                            }
                        }
                    } catch (Exception e) {
                        log.error("Error sending SSE event", e);
                        completionFuture.completeExceptionally(e);
                    }
                },
                error -> {
                    log.error("Error in chat response stream", error);
                    completionFuture.completeExceptionally(error);
                },
                () -> {
                    try {
                        Cat.newTransactionWithDuration(getClass().getSimpleName(), "E2E", System.currentTimeMillis() - startChatTime).complete();
                        Cat.newTransactionWithDuration(getClass().getSimpleName(), "SSE-E2E", System.currentTimeMillis() - startSseTime).complete();
                        MetricHelper.build().name("耗时打点").tag("scene", "E2E").duration(System.currentTimeMillis() - startSseTime);
                        // 流结束后处理缓冲区中剩余的内容 --delete
                        // 流结束后处理隐藏内容
                        String completeContent = fullContent.toString();
                        context.setAssistantVisibleContent(visibleContent.toString());
                        context.setAssistantContent(completeContent);
                        String hiddenContent = "";
                        context.addSpan(Span.builder()
                                .key("answer")
                                .value(completeContent)
                                .duration(System.currentTimeMillis() - startChatTime)
                                .build());

                        // 提取隐藏内容
                        if (completeContent.contains(hiddenSeparator) || completeContent.contains(hiddenSeparatorV2)) {
                            hiddenContent = completeContent.contains(hiddenSeparator)
                                    ? completeContent.substring(completeContent.indexOf(hiddenSeparator) + hiddenSeparator.length())
                                    : completeContent.substring(completeContent.indexOf(hiddenSeparatorV2));
                            // 处理隐藏内容
                            log.info("消息id:{},处理隐藏内容: {}", context.getMsgId(), hiddenContent);
                            context.addSpan(Span.builder()
                                    .key("hiddenContent")
                                    .value(hiddenContent)
                                    .build());
                            int start = hiddenContent.indexOf("{");
                            int end = hiddenContent.lastIndexOf("}") + 1;
                            if (start >= 0 && end > start) {
                                String jsonStr = hiddenContent.substring(start, end);
                                try {
                                    if (validJson(jsonStr)) {
                                        JSONObject jsonObject = JSON.parseObject(jsonStr);
                                        JSONObject remainingFields = new JSONObject();
                                        for (String field : jsonObject.keySet()) {
                                            if (!processedJsonFields.contains(field)) {
                                                remainingFields.put(field, jsonObject.get(field));
                                            }
                                        }

                                        // 如果还有未处理的字段，才进行处理
                                        if (!remainingFields.isEmpty()) {
                                            List<StreamEventDTO> cardEvents = cardHandlerManager.processJsonObjectByHandlerOrder(
                                                    remainingFields, context, sseEmitter, sseIndex, startChatTime);
                                            // 将卡片事件添加到回复列表
                                            if (CollectionUtils.isNotEmpty(cardEvents)) {
                                                reply.addAll(cardEvents);
                                            }
                                        }
                                        Cat.newTransactionWithDuration(getClass().getSimpleName(), "SSE-finish", System.currentTimeMillis() - startSseTime).complete();
                                        MetricHelper.build().name("耗时打点").tag("scene", "finish").duration(System.currentTimeMillis() - startSseTime);
                                    } else {
                                        log.info("无效的 JSON 格式: {}", jsonStr);
                                    }
                                } catch (Exception e) {
                                    log.error("处理 JSON 时出错: {}", jsonStr, e);
                                }
                            }
                        }
                        // 完成Future
                        completionFuture.complete(reply);
                    } catch (Exception e) {
                        log.error("Error sending final SSE event", e);
                        completionFuture.completeExceptionally(e);
                    }
                }
        );

        return completionFuture;
    }

    private void setFunctionCallJson(IntentionPromptConfig intentionPrompt, IntentionResult result) {
        if (result == null || StringUtils.isBlank(result.getKey())) {
            return;
        }

        intentionPrompt.getList().stream()
                .filter(e -> e.getKey().equals(result.getKey()))
                .findFirst()
                .map(IntentionPromptConfig.IntentionPrompt::getFunctionCallJson)
                .filter(StringUtils::isNotBlank)
                .map(json -> {
                    try {
                        return JSON.parseObject(json);
                    } catch (Exception e) {
                        log.error("Failed to parse function call json: {}", json, e);
                        return new JSONObject();
                    }
                })
                .ifPresent(jsonObject -> {
                    result.setSearchNeed(jsonObject.getBoolean("searchNeed") != null && jsonObject.getBoolean("searchNeed"));
                    result.setDepartmentNeed(jsonObject.getBoolean("departmentNeed") != null && jsonObject.getBoolean("departmentNeed"));
                    result.setHospitalNeed(jsonObject.getBoolean("hospitalNeed") != null && jsonObject.getBoolean("hospitalNeed"));
                    result.setRagNeed(jsonObject.getBoolean("ragNeed") != null && jsonObject.getBoolean("ragNeed"));
                    result.setTimeNeed(jsonObject.getBoolean("timeNeed") != null && jsonObject.getBoolean("timeNeed"));
                    result.setNearByNeed(jsonObject.getBoolean("nearByNeed") != null && jsonObject.getBoolean("nearByNeed"));
                    result.setShopRagNeed(jsonObject.getBoolean("shopRagNeed") != null && jsonObject.getBoolean("shopRagNeed"));
                });
    }

    private void auditUserContent(AiAnswerContext context) {
        AuditRequest auditRequest = AuditRequest.builder()
                .bizId(context.getMsgId())
                .userId(Long.valueOf(context.getUserId().replace("m-", "").replace("d-", "")))
                .userIP("127.0.0.1")
                .userSource(context.getPlatform().equals(1) ? 2 : 1)
                .assistantId(context.getBizScene())
                .dataSource(2)
                .transId(String.valueOf(context.getMsgId()))
                .type(type)
                .textBody(Collections.singletonList(AuditRequest.TextBody.builder()
                        .name("AIGCText")
                        .value(context.getContent())
                        .desc("AIGCText")
                        .build()))
                .build();
        context.addSpan(Span.builder().key("auditRequest").value(JSON.toJSONString(auditRequest)).build());
        AuditResult userAuditResult = porscheAuditAcl.audit(auditRequest);
        if (userAuditResult.getSpan() != null) {
            context.addSpan(userAuditResult.getSpan());
        }
        if (!Objects.equals(userAuditResult.getAdvice(), AuditResult.Advice.PASSED.getCode())) {
            context.addSpan(Span.builder().key("auditUser").value(userAuditResult.getMsg()).build());
            throw new SseAwareException(StreamEventErrorTypeEnum.AUDIT_ERROR);
        }
    }

    private static boolean validJson(String s) {
        if (StringUtils.isBlank(s)) {
            return false;
        }
        try (JSONValidator validator = JSONValidator.from(s)) {
            return Objects.equals(validator.getType(), JSONValidator.Type.Object);
        } catch (Exception e) {
            log.error("JSON validation failed for string: {}", s, e);
            return false;
        }
    }

    private static String extractJsonContent(String input) {
        if (StringUtils.isBlank(input)) {
            return "";
        }

        int start = input.indexOf("{");
        if (start == -1) {
            return "";
        }

        int end = input.lastIndexOf("}");
        if (end == -1 || end < start) {
            return "";
        }

        return input.substring(start, end + 1);
    }


    /**
     * 发送卡片事件的通用方法
     *
     * @param cardTypeEnum 卡片类型，StreamEventCardTypeEnum
     * @param cardKey      卡片键名
     * @param cardProps    卡片属性
     * @param sseEmitter   SSE发射器
     * @param index        索引引用
     * @return 保存的消息ID
     */
    private StreamEventDTO sendCardEvent(StreamEventCardTypeEnum cardTypeEnum, String cardKey, Map<String, Object> cardProps, SseEmitter sseEmitter, int index) {
        try {
            StreamEventDTO eventDTO = new StreamEventDTO();
            eventDTO.setType(StreamEventTypeEnum.MESSAGE.getType());
            eventDTO.setIndex(index);
            StreamEventDataDTO dataDTO = new StreamEventDataDTO();
            dataDTO.setEvent(StreamEventDataTypeEnum.MAIN_TEXT.getType());
            boolean isOutBubble = cardTypeEnum.isOutBubble();

            dataDTO.setContent(isOutBubble ? ":::{" + StreamEventCardTypeEnum.buildCardContent(cardTypeEnum, cardKey) + "}:::"
                    : ":::}" + StreamEventCardTypeEnum.buildCardContent(cardTypeEnum, cardKey) + "{:::");
            StreamEventCardDataDTO cardDataDTO = new StreamEventCardDataDTO();
            cardDataDTO.setType(cardTypeEnum.getType());
            cardDataDTO.setKey(cardKey);
            cardDataDTO.setCardProps(cardProps);
            dataDTO.setCardsData(Lists.newArrayList(cardDataDTO));
            eventDTO.setData(dataDTO);
            log.info("sendCardEvent:{}", JSON.toJSONString(eventDTO));
            sseEmitter.send(eventDTO);

            return eventDTO;
        } catch (Exception e) {
            log.error("Error sending card event, cardType={}, cardKey={}", cardTypeEnum.getType(), cardKey, e);
            return null;
        }
    }


    public Long saveMessage(AiAnswerContext context) {
        ChatMessageEntity messageEntity = ChatMessageEntity.builder()
                .conversationId(String.valueOf(context.getSessionId()))
                .messageId(UUID.randomUUID().toString())
                .auditStatus(((byte) 1))
                .content(context.getContent())
                .contentType((byte) 1)
                .senderId(String.valueOf(context.getUserId()))
                .role(context.getRole())
                .build();

        chatMessageRepository.saveMsgByPrimaryId(messageEntity);
        if (messageEntity.getId() != null) {
            return messageEntity.getId();
        }
        return 0L;
    }

    public Long saveOrUpdateMsg(ChatMessageEntity messageEntity) {
        chatMessageRepository.saveMsgByPrimaryId(messageEntity);
        if (messageEntity.getId() != null) {
            return messageEntity.getId();
        }
        return 0L;
    }

    public List<ChatSessionMessageBO> queryMsgBySessionId(Long sessionId, String userId, int pageNo, int pageSize) {
        int offset = (pageNo - 1) * pageSize;
        List<ChatMessageEntity> messagesByConversationId = chatMessageRepository.findMessagesByConversationId(String.valueOf(sessionId), String.valueOf(userId), offset, pageSize);
        List<String> messageIds = messagesByConversationId.stream().map(ChatMessageEntity::getId).map(String::valueOf).toList();
        if (CollectionUtils.isEmpty(messageIds)) {
            return Lists.newArrayList();
        }
        List<ChatMessageFeedbackEntity> chatMessageFeedbackEntities = chatMessageRepository.queryFeedback(messageIds, String.valueOf(userId));
        // 构建消息ID到反馈实体的映射关系
        Map<String, ChatMessageFeedbackEntity> feedbackMap;
        if (CollectionUtils.isNotEmpty(chatMessageFeedbackEntities)) {
            feedbackMap = chatMessageFeedbackEntities.stream()
                    .collect(toMap(
                            ChatMessageFeedbackEntity::getMessageId,
                            feedback -> feedback,
                            (a, b) -> a
                    ));
        } else {
            feedbackMap = new HashMap<>();
        }
        // 将消息实体转换为BO对象
        return messagesByConversationId.stream()
                .map(message -> {
                    ChatMessageFeedbackEntity feedback = feedbackMap.get(String.valueOf(message.getId()));
                    Integer likeType = 0; // 默认未操作
                    if (feedback != null && feedback.getFeedbackType() != null) {
                        likeType = Integer.valueOf(feedback.getFeedbackType());
                    }
                    return ChatSessionMessageBO.builder()
                            .msgId(message.getId())
                            .role(message.getRole())
                            .content(message.getContent())
                            .likeType(likeType)
                            .build();
                }).toList();
    }

    /**
     * 直接根据消息ID查询消息
     *
     * @param msgId  消息ID
     * @param userId 用户ID
     * @return 消息BO对象，如果未找到则返回null
     */
    public ChatSessionMessageBO queryMsgById(Long msgId, String userId) {
        List<ChatMessageEntity> messages = chatMessageRepository.findMessageByMsgId(msgId, String.valueOf(userId));
        if (CollectionUtils.isEmpty(messages)) {
            return null;
        }

        // 获取第一条消息（应该只有一条）
        ChatMessageEntity message = messages.get(0);

        // 查询该消息的反馈信息
        List<ChatMessageFeedbackEntity> feedbacks = chatMessageRepository.queryFeedback(
                Collections.singletonList(String.valueOf(msgId)), String.valueOf(userId));

        // 默认未操作
        Integer likeType = 0;

        // 如果有反馈记录，获取反馈类型
        if (CollectionUtils.isNotEmpty(feedbacks)) {
            likeType = Integer.valueOf(feedbacks.get(0).getFeedbackType());
        }

        // 构建并返回消息BO对象
        return ChatSessionMessageBO.builder()
                .msgId(message.getId())
                .role(message.getRole())
                .content(message.getContent())
                .likeType(likeType)
                .build();
    }

    public List<ChatSessionBO> querySession(String userId, int pageNo, int pageSize, Long shopId) {
        int offset = (pageNo - 1) * pageSize;
        List<ChatConversationEntity> conversations =
                chatMessageRepository.findConversations(String.valueOf(userId), offset, pageSize, String.valueOf(shopId), BusinessTypeEnum.SHOP.getType());
        if (CollectionUtils.isEmpty(conversations)) {
            return Lists.newArrayList();
        }
        return conversations.stream()
                .map(this::convertSessionBO).toList();
    }

    public List<ChatSessionBO> querySessionWithEmpty(String userId, int pageNo, int pageSize, Long shopId) {
        int offset = (pageNo - 1) * pageSize;
        List<ChatConversationEntity> conversations =
                chatMessageRepository.findConversationsWithEmptyTitle(String.valueOf(userId), offset, pageSize, String.valueOf(shopId), BusinessTypeEnum.SHOP.getType());
        if (CollectionUtils.isEmpty(conversations)) {
            return Lists.newArrayList();
        }
        return conversations.stream()
                .map(this::convertSessionBO).toList();
    }

    private ChatSessionBO convertSessionBO(ChatConversationEntity entity) {
        if (entity == null) {
            return null;
        }
        // 获取创建时间
        Date updatedAt = entity.getUpdatedAt();
        // 创建日期时间格式 (yyyy-MM-dd)
        String dateTime = "";
        // 创建准确时间格式 (HH:mm)
        String explicitTime = "";
        if (updatedAt != null) {
            // 创建SimpleDateFormat来格式化日期
            SimpleDateFormat dateFormat = new java.text.SimpleDateFormat("yyyy-MM-dd");
            SimpleDateFormat timeFormat = new java.text.SimpleDateFormat("HH:mm");
            dateTime = dateFormat.format(updatedAt);
            explicitTime = timeFormat.format(updatedAt);
        }
        return ChatSessionBO.builder()
                .sessionId(entity.getId())
                .title(entity.getTitle())
                .dateTime(dateTime)
                .explicitTime(explicitTime)
                .build();
    }

    public Long saveFeedback(Long msgId, String userId) {
        ChatMessageFeedbackEntity chatMessageFeedbackEntity = chatMessageRepository.saveFeedback(msgId, userId);
        return chatMessageFeedbackEntity.getId();
    }

    public boolean updateFeedback(Long msgId, String userId, int operateType) {
        return chatMessageRepository.updateFeedback(String.valueOf(msgId), String.valueOf(userId), (byte) operateType);
    }

    public boolean updateSession(Long sessionId, byte status, String title) {
        return chatMessageRepository.updateSession(sessionId, status, title);
    }

    public boolean updateSessionByUserId(String userId, byte status) {
        return chatMessageRepository.updateSessionByUserId(String.valueOf(userId), status);
    }

    public ChatConversationEntity createSession(AiAnswerContext context) {
        return chatMessageRepository.createConversation(String.valueOf(context.getUserId()), context.getBizScene(), context.getContent(), String.valueOf(context.getMtShopId()));
    }

    /**
     * 创建会话（带业务关联信息）
     *
     * @param context      上下文
     * @param businessType 业务类型，如SHOP/PRODUCT/ORDER/ACTIVITY
     * @param businessId   关联业务ID
     * @return 创建的会话实体
     */
    public ChatConversationEntity createSession(AiAnswerContext context, String businessType, String businessId) {
        return chatMessageRepository.createConversation(
                String.valueOf(context.getUserId()),
                context.getBizScene(),
                context.getContent(),
                businessType,
                businessId);
    }

    /**
     * 更新会话业务关联信息
     *
     * @param sessionId    会话ID
     * @param businessType 业务类型
     * @param businessId   业务ID
     * @return 是否更新成功
     */
    public boolean updateSessionBusinessInfo(Long sessionId, String businessType, String businessId) {
        return chatMessageRepository.updateSessionBusinessInfo(sessionId, businessType, businessId);
    }

    /**
     * 根据业务类型和业务ID查询会话
     *
     * @param businessType 业务类型
     * @param businessId   业务ID
     * @return 关联的会话列表
     */
    public List<ChatConversationEntity> findConversationsByBusiness(String businessType, String businessId) {
        return chatMessageRepository.findConversationsByBusiness(businessType, businessId);
    }

    public ChatConversationEntity checkSession(Long sessionId, String userId) {
        List<ChatConversationEntity> conversationById = chatMessageRepository.findConversationById(sessionId, userId);
        if (CollectionUtils.isNotEmpty(conversationById)) {
            return conversationById.get(0);
        }
        return null;
    }

    public Boolean hasPhoneTaskDoing(Long sessionId, List<Integer> statusList) {
        List<ChatPhoneCallTaskEntity> chatPhoneCallTaskEntities = chatPhoneCallTaskRepository.queryTaskBySessionId(sessionId, statusList);
        return CollectionUtils.isNotEmpty(chatPhoneCallTaskEntities);
    }

    private boolean sendSseContent(SseEmitter sseEmitter, String type, String event, String content) {
        Transaction transaction = Cat.newTransaction(getClass().getSimpleName(), type + "/" + event);
        try {
            sseEmitter.send(SseEmitter.event().data(StreamEventDTO.builder().type(type)
                    .data(StreamEventDataDTO.builder().event(event).content(content).build()).build()));
            transaction.setSuccessStatus();
            return true;
        } catch (Exception e) {
            log.error("sendSseContent error, type={}, event={}, content={}", type, event, content, e);
            transaction.setStatus(e);
        } finally {
            transaction.complete();
        }
        return false;
    }

    private List<StreamEventDTO> processPartialJsonFields(
            String partialJson,
            AiAnswerContext context,
            SseEmitter sseEmitter,
            AtomicInteger sseIndex,
            Set<String> processedFields,
            long startChatTime) {

        if (StringUtils.isBlank(partialJson)) {
            return Collections.emptyList();
        }

        List<StreamEventDTO> results = new ArrayList<>();

        try {
            int start = partialJson.indexOf("{");
            if (start == -1) {
                return results;
            }
            String jsonContent = partialJson.substring(start);

            String validJsonStr = ensureValidJson(jsonContent);
            if (!validJson(validJsonStr)) {
                return results;
            }

            JSONObject tempJsonObject = JSON.parseObject(validJsonStr);
            if (tempJsonObject == null || tempJsonObject.isEmpty()) {
                return results;
            }

            List<StreamEventDTO> streamEventDTOS =
                    cardHandlerManager.processJsonObjectByField(tempJsonObject, context,
                            sseEmitter, sseIndex, startChatTime, processedFields);
            if (CollectionUtils.isNotEmpty(streamEventDTOS)) {
                results.addAll(streamEventDTOS);
            }
        } catch (Exception e) {
            log.error("增量处理部分JSON出错: {}", partialJson, e);
        }

        return results;
    }

    /**
     * 确保JSON字符串格式有效
     * 处理不完整的JSON字符串，如 {"key":"value"
     *
     * @param jsonStr 原始JSON字符串
     * @return 有效的JSON字符串
     */
    private String ensureValidJson(String jsonStr) {
        // 处理边界情况
        if (StringUtils.isBlank(jsonStr)) {
            return "{}";
        }

        // 提取和清理JSON
        int start = jsonStr.indexOf("{");
        if (start == -1) {
            return "{}";
        }

        String json = jsonStr.substring(start);
        json = json.contains("```") ? json.replaceAll("```json", "").replaceAll("```", "") : json;

        // 快速格式检查
        if (!json.contains(":") || !hasBalancedQuotes(json)) {
            return "";
        }

        try {
            return findValidJson(json);
        } catch (Exception e) {
            log.error("JSON处理异常: {}", json, e);
            return "{}";
        }
    }

    /**
     * 查找有效的JSON结构或截断点
     */
    private String findValidJson(String json) {
        int level = 0;
        int lastValidComma = -1;
        boolean inString = false;

        for (int i = 0; i < json.length(); i++) {
            char c = json.charAt(i);

            // 处理字符串和转义
            if (c == '\\') {
                i++; // 跳过转义字符
                continue;
            }
            if (c == '"' && (i == 0 || json.charAt(i - 1) != '\\')) {
                inString = !inString;
                continue;
            }
            if (inString) {
                continue;
            }

            // 处理JSON结构
            switch (c) {
                case '{':
                case '[':
                    level++;
                    break;
                case '}':
                case ']':
                    level--;
                    if (level == 0) {
                        return json.substring(0, i + 1); // 找到完整JSON
                    }
                    break;
                case ',':
                    if (level == 1) {
                        lastValidComma = i; // 记录有效逗号位置
                    }
                    break;
                default:
                    break;
            }
        }

        // 根据处理结果返回
        if (level == 0) {
            return json; // JSON已完整
        }
        if (lastValidComma > 0) {
            return json.substring(0, lastValidComma) + "}"; // 截断到最后有效逗号
        }
        return "{}"; // 无法修复
    }

    /**
     * 检查字符串中的引号是否配对
     */
    private boolean hasBalancedQuotes(String text) {
        boolean escaped = false;
        int count = 0;

        for (int i = 0; i < text.length(); i++) {
            char c = text.charAt(i);
            if (escaped) {
                escaped = false;
            } else if (c == '\\') {
                escaped = true;
            } else if (c == '"') {
                count++;
            }
        }

        return count % 2 == 0;
    }

    private Document createDocument(Object info, String source) {
        if (info instanceof BaseStrategyInfo && StringUtils.isNotBlank(((BaseStrategyInfo) info).toPrompt())) {
            return new Document(((BaseStrategyInfo) info).toPrompt(), Map.of(MetadataKeyEnum.SOURCE.getKey(), source));
        } else if (info instanceof String && StringUtils.isNotBlank((String) info)) {
            return new Document((String) info, Map.of(MetadataKeyEnum.SOURCE.getKey(), source));
        }
        return null;
    }

    private String appendDocumentsToPrompt(String prompt, List<Document> documents) {
        StringBuilder promptBuilder = new StringBuilder(prompt);
        for (Document doc : documents) {
            promptBuilder.append("\n# ")
                    .append(doc.getMetadata().get(MetadataKeyEnum.SOURCE.getKey()))
                    .append("\n")
                    .append(doc.getText());
        }
        return promptBuilder.toString();
    }

    // ===== 新增辅助方法 =====
    /**
     * 判断意图是否需要加入医院名称
     */
    private boolean shouldIncludeHospitalName(String intentionKey) {
        return "triage".equals(intentionKey) ||
                "service".equals(intentionKey) ||
                "guide".equals(intentionKey);
    }

    /**
     * 获取医院简称，优先海马，其次ShopAcl
     */
    private String getHospitalName(AiAnswerContext context) {
        try {
            // 尝试从海马配置获取
            Map<String, String> fields = new HashMap<>();
            fields.put("mtShopId", String.valueOf(context.getMtShopId()));
            List<HaimaContent> hospitalBriefs = haimaAcl.getContent("public_hospital_brief", fields);
            if (CollectionUtils.isNotEmpty(hospitalBriefs)) {
                HaimaContent content = hospitalBriefs.get(0);
                String simpleName = content.getContentString("simpleName");
                if (StringUtils.isNotBlank(simpleName)) {
                    return simpleName;
                }
            }
            // 兜底使用ShopAcl
            if (shopAcl != null && context.getMtShopId() != null) {
                String hospitalName = shopAcl.getHospitalName(context.getMtShopId());
                if (StringUtils.isNotBlank(hospitalName)) {
                    return hospitalName;
                }
            }
        } catch (Exception e) {
            log.error("获取医院名称失败, shopId={}", context.getMtShopId(), e);
        }
        return null;
    }
}
