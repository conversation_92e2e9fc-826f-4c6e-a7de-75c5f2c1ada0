package com.sankuai.dzhealth.ai.service.domain.model.thinking;

import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 深度搜索结果模型
 */
@Data
@Builder
public class DeepSearchResult {
    /**
     * 记录ID
     */
    private Long id;
    
    /**
     * 业务来源标识
     */
    private String businessSource;
    
    /**
     * 用户问题
     */
    private String question;
    
    /**
     * 深度搜索生成的答案
     */
    private String answer;
    
    /**
     * 信息来源URL列表
     */
    private List<String> sourceUrls;
    
    /**
     * 置信度评分
     */
    private BigDecimal confidenceScore;
    
    /**
     * 创建时间
     */
    private LocalDateTime addTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
} 