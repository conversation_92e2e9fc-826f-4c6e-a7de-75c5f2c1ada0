package com.sankuai.dzhealth.ai.service.agent.domain.model.experiencereport;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 体验报告响应领域模型
 *
 * <AUTHOR>
 * @version 0.0.1
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ExperienceReportResponseModel implements Serializable {

    /**
     * 响应码
     */
    private Integer code;

    /**
     * 响应消息
     */
    private String message;

    /**
     * 是否成功
     */
    private Boolean success;

    /**
     * 体验报告数据
     */
    private ExperienceReportModel data;
}

