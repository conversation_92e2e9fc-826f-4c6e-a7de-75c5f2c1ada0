package com.sankuai.dzhealth.ai.service.agent.domain.tools.param;

import lombok.Data;
import org.springframework.ai.tool.annotation.ToolParam;

/**
 * 地理编码描述请求参数
 * Geocoding Description Request Parameters
 */
@Data
public class GeocodingDescRequest {

    @ToolParam(
        description = "城市ID（支持中文名称或数字编码）如上海市或10 / City ID (supports Chinese name or numeric code) e.g., 上海市 or 10",
        required = true
    )
    private String cityId;

    @ToolParam(
        description = "地址信息，详细的地址描述 / Address information, detailed address description",
        required = true
    )
    private String address;

}
