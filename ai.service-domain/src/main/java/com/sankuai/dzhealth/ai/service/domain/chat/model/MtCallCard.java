package com.sankuai.dzhealth.ai.service.domain.chat.model;

import com.alibaba.fastjson.JSON;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class MtCallCard {
    @FieldDoc(description = "状态")
    private Integer status;

    @FieldDoc(description = "顶部标题")
    private String topTitle;

    @FieldDoc(description = "顶部图标")
    private String topIcon;

    @FieldDoc(description = "底部标题")
    private String bottomTitle;

    @FieldDoc(description = "副标题")
    private String subTitle;

    @FieldDoc(description = "精炼文本")
    private String refinedTxt;

    @FieldDoc(description = "文本内容")
    private String txt;


    @FieldDoc(description = "按钮文本")
    private String buttonTxt;

    @FieldDoc(description = "历史文本")
    private String historyTxt;



    @FieldDoc(description ="外呼次数")
    private int callTime;

    @FieldDoc(description ="开始时间")
    private String startTime;

    @FieldDoc(description ="任务Id")
    private Long taskId;

    @FieldDoc(description ="mediaTxt")
    private String mediaTxt;


    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }
}
