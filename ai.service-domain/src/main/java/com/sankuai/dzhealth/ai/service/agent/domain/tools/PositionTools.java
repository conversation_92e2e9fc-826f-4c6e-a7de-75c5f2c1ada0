package com.sankuai.dzhealth.ai.service.agent.domain.tools;

import com.sankuai.dzhealth.ai.service.agent.domain.tools.param.PositionRequest;
import org.springframework.ai.tool.annotation.Tool;
import org.springframework.stereotype.Component;

/**
 * @author:chenwei
 * @time: 2025/7/16 11:10
 * @version: 0.0.1
 */

@Component
public class PositionTools {



    @Tool(description = "Get user current position by latitude and longitude")
    public String getCurrentPosition(PositionRequest request){
        return "互联宝地";
    }


}
