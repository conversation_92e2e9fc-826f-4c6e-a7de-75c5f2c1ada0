package com.sankuai.dzhealth.ai.service.agent.domain.model;


import com.alibaba.fastjson.annotation.JSONField;
import com.dianping.lion.client.util.StringUtils;

import com.sankuai.dzhealth.ai.service.agent.infrastructure.enums.ProductNameEnum;
import com.sankuai.dzhealth.ai.service.agent.infrastructure.model.AppointmentInfoBO;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@Data
public class AppointmentContext implements Serializable {

    private String productName;

    @JSONField(format = "yyyy年MM月dd日HH:mm")
    private Date startTime;

    @JSONField(format = "yyyy年MM月dd日HH:mm")
    private Date endTime;

    private String positionTxt;

    private Double lng;

    private Double lat;

    private String phone;

    private String personDesc;

    private String filterItems;

    private String remark;

    private String cityId;

    private Boolean isMentioned;

    private String searchword;

    private Double distance;

    private String price;


    public Map<String, Object> toMap() {
        Map<String, Object> map = new HashMap<>();
        map.put("productName", this.productName);
        map.put("startTime", this.startTime);
        map.put("endTime", this.endTime);
        map.put("positionTxt", this.positionTxt);
        map.put("lng", this.lng);
        map.put("lat", this.lat);
        map.put("phone", this.phone);
        map.put("personDesc", this.personDesc);
        map.put("filterItems", this.filterItems);
        map.put("remark", this.remark);
        map.put("cityId", this.cityId);
        map.put("isMentioned", this.isMentioned);
        map.put("searchword", this.searchword);
        map.put("distance", this.distance);
        map.put("price", this.price);

        return map;
    }



    /**
     * 转换为AppointmentInfoBO
     */
    public AppointmentInfoBO toAppointmentInfoBO() {
        return AppointmentInfoBO.builder()
                .productName(ProductNameEnum.fromDesc(this.productName))
                .appointmentStartTime(this.startTime)
                .appointmentEndTime(this.endTime)
                .positionTxt(this.positionTxt)
                .lng(this.lng)
                .lat(this.lat)
                .phone(this.phone)
                .personDesc(this.personDesc)
                .filterItems(isValidValue(this.filterItems) ? this.filterItems : null)
                .remark(isValidValue(this.remark) ? this.remark : null)
                .cityId(!StringUtils.isBlank(this.cityId) ? Integer.parseInt(this.cityId) : null)
                .isMentioned(this.isMentioned)
                .searchword(this.searchword)
                .distance(this.distance)
                .price(this.price)
                .build();
    }

    private boolean isValidValue(String value) {
        return StringUtils.hasText(value) && !"无".equals(value);
    }

    /**
     * 检查是否有多个字段为空
     * @return true表示有多个字段为空，false表示只有0个或1个字段为空
     */
    public boolean hasMultipleEmptyFields() {
        int emptyCount = 0;

        if (StringUtils.isBlank(this.productName)) emptyCount++;
        if (this.startTime == null||this.endTime == null) emptyCount++;
        if (StringUtils.isBlank(this.positionTxt)||this.lng == null||this.lat == null||this.cityId==null) emptyCount++;
        if (StringUtils.isBlank(this.phone)) emptyCount++;
        return emptyCount > 1;
    }




}
