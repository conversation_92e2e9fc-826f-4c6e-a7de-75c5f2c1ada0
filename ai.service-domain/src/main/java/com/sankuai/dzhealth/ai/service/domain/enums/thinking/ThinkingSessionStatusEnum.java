package com.sankuai.dzhealth.ai.service.domain.enums.thinking;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 思考会话状态枚举
 */
@Getter
@AllArgsConstructor
public enum ThinkingSessionStatusEnum {
    
    /**
     * 初始化
     */
    INIT("INIT", "初始化"),
    
    /**
     * 思考中
     */
    THINKING("THINKING", "思考中"),
    
    /**
     * 已完成
     */
    COMPLETED("COMPLETED", "已完成"),
    
    /**
     * 失败
     */
    FAILED("FAILED", "失败");
    
    /**
     * 状态码
     */
    private final String code;
    
    /**
     * 状态描述
     */
    private final String desc;
}