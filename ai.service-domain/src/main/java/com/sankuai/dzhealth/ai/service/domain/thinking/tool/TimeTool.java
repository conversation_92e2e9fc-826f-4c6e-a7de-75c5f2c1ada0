package com.sankuai.dzhealth.ai.service.domain.thinking.tool;

import com.sankuai.dzhealth.ai.service.infrastructure.utils.DateTimeTools;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.tool.annotation.Tool;
import org.springframework.ai.tool.annotation.ToolParam;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 时间工具
 * 用于在AI思考过程中获取当前时间信息
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class TimeTool {
    
    private final DateTimeTools dateTimeTools;

    /**
     * 获取当前时间工具方法
     * 通过Spring AI的Tool注解注册为工具方法
     *
     * @param format 时间格式，可选参数。支持的格式：
     *               - "full" 完整格式：yyyy-MM-dd HH:mm:ss 星期X
     *               - "date" 日期格式：yyyy-MM-dd
     *               - "time" 时间格式：HH:mm:ss
     *               - "datetime" 日期时间格式：yyyy-MM-dd HH:mm:ss
     *               - "weekday" 星期格式：星期X
     *               - "timestamp" 时间戳格式：毫秒时间戳
     * @return 格式化后的时间字符串
     */
    @Tool(description = "获取当前的日期和时间信息。" +
            "适用于需要知道当前时间、日期、星期几等时间相关信息的场景。" +
            "特别适用于回答'今天是几号'、'现在几点'、'今天星期几'、'今天医院上班吗'等时间敏感的问题。")
    public String getCurrentTime(
            @ToolParam(description = "时间格式类型，可选。支持：full(完整格式)、date(日期)、time(时间)、datetime(日期时间)、weekday(星期)、timestamp(时间戳)。默认为full", required = false) 
            String format
    ) {
        log.info("获取当前时间，格式: {}", format);
        
        try {
            LocalDateTime now = LocalDateTime.now();
            
            // 默认格式或full格式
            if (format == null || "full".equalsIgnoreCase(format)) {
                return dateTimeTools.getNowTime();
            }
            
            // 根据不同格式返回相应的时间信息
            switch (format.toLowerCase()) {
                case "date":
                    return now.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
                case "time":
                    return now.format(DateTimeFormatter.ofPattern("HH:mm:ss"));
                case "datetime":
                    return now.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                case "weekday":
                    return getWeekdayName(now.getDayOfWeek().getValue());
                case "timestamp":
                    return String.valueOf(System.currentTimeMillis());
                default:
                    // 未知格式，返回完整格式
                    return dateTimeTools.getNowTime();
            }
            
        } catch (Exception e) {
            log.error("获取当前时间失败: {}", e.getMessage(), e);
            return "获取当前时间时发生错误: " + e.getMessage();
        }
    }
    
    /**
     * 获取详细的时间信息
     * 
     * @return 包含多种时间格式的详细信息
     */
    @Tool(description = "获取详细的时间信息，包括完整日期时间、星期几、时间戳等。" +
            "适用于需要全面了解当前时间信息的场景。")
    public String getDetailedTimeInfo() {
        log.info("获取详细时间信息");
        
        try {
            LocalDateTime now = LocalDateTime.now();
            long timestamp = System.currentTimeMillis();
            
            StringBuilder timeInfo = new StringBuilder();
            timeInfo.append("当前时间详细信息：\n");
            timeInfo.append("完整时间: ").append(dateTimeTools.getNowTime()).append("\n");
            timeInfo.append("日期: ").append(now.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"))).append("\n");
            timeInfo.append("时间: ").append(now.format(DateTimeFormatter.ofPattern("HH:mm:ss"))).append("\n");
            timeInfo.append("星期: ").append(getWeekdayName(now.getDayOfWeek().getValue())).append("\n");
            timeInfo.append("时间戳: ").append(timestamp).append("\n");
            timeInfo.append("年份: ").append(now.getYear()).append("\n");
            timeInfo.append("月份: ").append(now.getMonthValue()).append("\n");
            timeInfo.append("日: ").append(now.getDayOfMonth()).append("\n");
            timeInfo.append("小时: ").append(now.getHour()).append("\n");
            timeInfo.append("分钟: ").append(now.getMinute()).append("\n");
            timeInfo.append("秒: ").append(now.getSecond());
            
            return timeInfo.toString();
            
        } catch (Exception e) {
            log.error("获取详细时间信息失败: {}", e.getMessage(), e);
            return "获取详细时间信息时发生错误: " + e.getMessage();
        }
    }
    
    /**
     * 根据星期数值获取中文星期名称
     * 
     * @param dayOfWeek 星期数值（1-7，1为星期一）
     * @return 中文星期名称
     */
    private String getWeekdayName(int dayOfWeek) {
        return switch (dayOfWeek) {
            case 1 -> "星期一";
            case 2 -> "星期二";
            case 3 -> "星期三";
            case 4 -> "星期四";
            case 5 -> "星期五";
            case 6 -> "星期六";
            case 7 -> "星期日";
            default -> "未知";
        };
    }
} 