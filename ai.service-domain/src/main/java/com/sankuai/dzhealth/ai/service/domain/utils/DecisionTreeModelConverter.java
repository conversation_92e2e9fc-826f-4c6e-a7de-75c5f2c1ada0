package com.sankuai.dzhealth.ai.service.domain.utils;

import com.sankuai.dzhealth.ai.service.agent.domain.model.decisionflow.DecisionTreeModel;
import com.sankuai.dzhealth.ai.service.agent.domain.model.SupplyRecommendModel;
import com.fasterxml.jackson.databind.JsonNode;
import com.sankuai.dzhealth.ai.service.dto.decision.DecisionFlowDTO;
import com.sankuai.dzhealth.ai.service.dto.decision.DecisionNodeDTO;
import com.sankuai.dzhealth.ai.service.dto.decision.NodeResourceRelationDTO;
import com.sankuai.dzhealth.ai.service.dto.decision.ResourceRecommendationDTO;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 公共转换工具：将 DecisionFlowDTO 转换为 DecisionTreeModel 列表
 */
public final class DecisionTreeModelConverter {
    private DecisionTreeModelConverter() {}

    public static java.util.List<DecisionTreeModel> convertToDecisionTreeModels(DecisionFlowDTO decisionFlowDTO, int depth) {
        if (decisionFlowDTO == null || decisionFlowDTO.getNodes() == null || decisionFlowDTO.getNodes().isEmpty()) {
            return new ArrayList<>();
        }

        Map<String, DecisionNodeDTO> nodeMap = decisionFlowDTO.getNodes().stream()
                .collect(Collectors.toMap(DecisionNodeDTO::getNodeId, node -> node));

        Map<String, List<String>> parentToChildrenMap = new HashMap<>();
        if (decisionFlowDTO.getEdges() != null) {
            for (var edge : decisionFlowDTO.getEdges()) {
                parentToChildrenMap.computeIfAbsent(edge.getParentId(), k -> new ArrayList<>())
                        .add(edge.getChildId());
            }
        }

        Set<String> aiParentSet = new HashSet<>();
        if (decisionFlowDTO.getEdges() != null) {
            for (var edge : decisionFlowDTO.getEdges()) {
                if (edge.getEdgeType() != null && "AI".equalsIgnoreCase(edge.getEdgeType())) {
                    aiParentSet.add(edge.getParentId());
                }
            }
        }

        Map<String, ResourceRecommendationDTO> resourceMap = new HashMap<>();
        if (decisionFlowDTO.getResources() != null) {
            for (var resource : decisionFlowDTO.getResources()) {
                resourceMap.put(resource.getResourceId(), resource);
            }
        }

        Map<String, List<NodeResourceRelationDTO>> nodeResourceMap = new HashMap<>();
        if (decisionFlowDTO.getRelations() != null) {
            for (var relation : decisionFlowDTO.getRelations()) {
                nodeResourceMap.computeIfAbsent(relation.getNodeId(), k -> new ArrayList<>()).add(relation);
            }
        }

        Set<String> allChildIds = new HashSet<>();
        if (decisionFlowDTO.getEdges() != null) {
            for (var edge : decisionFlowDTO.getEdges()) {
                allChildIds.add(edge.getChildId());
            }
        }

        List<String> rootNodeIds = decisionFlowDTO.getNodes().stream()
                .map(DecisionNodeDTO::getNodeId)
                .filter(nodeId -> !allChildIds.contains(nodeId))
                .collect(Collectors.toList());

        return rootNodeIds.stream()
                .map(rootId -> buildDecisionTreeModel(rootId, nodeMap, parentToChildrenMap, resourceMap, nodeResourceMap, aiParentSet, 1, depth))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    private static DecisionTreeModel buildDecisionTreeModel(String nodeId,
                                                            Map<String, DecisionNodeDTO> nodeMap,
                                                            Map<String, List<String>> parentToChildrenMap,
                                                            Map<String, ResourceRecommendationDTO> resourceMap,
                                                            Map<String, List<NodeResourceRelationDTO>> nodeResourceMap,
                                                            Set<String> aiParentSet,
                                                            int currentDepth,
                                                            int maxDepth) {
        DecisionNodeDTO node = nodeMap.get(nodeId);
        if (node == null) {
            return null;
        }

        List<DecisionTreeModel> children = new ArrayList<>();
        List<String> childIds = parentToChildrenMap.get(nodeId);
        if (childIds != null && !childIds.isEmpty()) {
            children = childIds.stream()
                    .map(childId -> buildDecisionTreeModel(childId, nodeMap, parentToChildrenMap, resourceMap, nodeResourceMap, aiParentSet, currentDepth + 1, maxDepth))
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
        }

        SupplyRecommendModel supplyRecommendModel = buildSupplyRecommendModel(nodeId, resourceMap, nodeResourceMap);

        return DecisionTreeModel.builder()
                .nodeName(node.getNodeName())
                .assessmentText(node.getAssessmentText())
                .assessmentImg(node.getAssessmentImg())
                .guidanceText(node.getGuidanceText())
                .needSupply(node.getNeedSupply() != null ? node.getNeedSupply() : false)
                .needFaceDiagnose(aiParentSet != null && aiParentSet.contains(nodeId))
                .needDoctor(node.getNeedDoctor() != null ? node.getNeedDoctor() : false)
                .supplyRecommendModel(supplyRecommendModel)
                .drillDownRequirements(children)
                .build();
    }

    private static SupplyRecommendModel buildSupplyRecommendModel(String nodeId,
                                                                 Map<String, ResourceRecommendationDTO> resourceMap,
                                                                 Map<String, List<NodeResourceRelationDTO>> nodeResourceMap) {
        List<NodeResourceRelationDTO> relations = nodeResourceMap.get(nodeId);
        if (relations == null || relations.isEmpty()) {
            return null;
        }
        relations.sort(Comparator.comparing(NodeResourceRelationDTO::getSortOrder, Comparator.nullsLast(Comparator.naturalOrder())));
        NodeResourceRelationDTO relation = relations.get(0);
        ResourceRecommendationDTO resource = resourceMap.get(relation.getResourceId());
        if (resource == null) {
            return null;
        }

        // 从 attributes 中提取筛选项与标签
        String medicalCaseTag = "";
        String shopTagFilter = "";
        String priceRangeFilter = "";
        try {
            String attributes = resource.getAttributes();
            JsonNode attrs = JsonUtils.parseJsonNode(attributes);
            if (attrs != null) {
                // caseTags: 数组 -> 用逗号拼接
                JsonNode caseTagsNode = attrs.get("caseTags");
                if (caseTagsNode != null && caseTagsNode.isArray()) {
                    List<String> tags = new ArrayList<>();
                    caseTagsNode.forEach(n -> tags.add(n.asText("")));
                    medicalCaseTag = String.join(",", tags);
                }
                // 商户筛选项：recallLogic 字段（后端现有数据规范）
                JsonNode recallLogicNode = attrs.get("recallLogic");
                if (recallLogicNode != null && !recallLogicNode.isNull()) {
                    shopTagFilter = recallLogicNode.asText("");
                }

                // 价格区间：仅从 priceRange 获取
                if (attrs.has("priceRange") && !attrs.get("priceRange").isNull()) {
                    //目前塞空字符串，后面看情况
//                    priceRangeFilter = attrs.get("priceRange").asText("");
                }
            }
        } catch (Exception ignore) { }

        return SupplyRecommendModel.builder()
                .supplyId(resource.getResourceId())
                .supplyName(resource.getResourceName())
                .supplyRecommendReason(relation.getRationale())
                .medicalCaseTag(medicalCaseTag)
                .shopTagFilter(shopTagFilter)
                .priceRangeFilter(priceRangeFilter)
                .build();
    }
}


