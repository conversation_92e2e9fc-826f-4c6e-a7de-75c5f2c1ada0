package com.sankuai.dzhealth.ai.service.agent.domain.utils;

import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @author:chenwei
 * @time: 2025/8/11 17:42
 * @version: 0.0.1
 */
public class UrlUtils {

    /**
     * 从URL字符串中获取指定参数的值
     * 
     * @param urlString URL字符串
     * @param paramName 参数名
     * @return 参数值，如果没有找到则返回空字符串
     */
    public static String getParamValue(String urlString, String paramName) {
        if (urlString == null || paramName == null) {
            return null;
        }

        // 构建正则表达式：参数名=值(&|$)
        String regex = paramName + "=([^&]+)";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(urlString);

        if (matcher.find()) {
            return matcher.group(1);
        }

        return StringUtils.EMPTY;
    }

    /**
     * 从URL字符串中获取多个参数名中第一个有值的参数值
     * 
     * @param urlString URL字符串
     * @param paramNames 参数名列表，按优先级顺序传入
     * @return 第一个找到的参数值，如果都没有找到则返回空字符串
     */
    public static String getParamValue(String urlString, List<String> paramNames) {
        if (urlString == null || paramNames == null || paramNames.isEmpty()) {
            return null;
        }

        for (String paramName : paramNames) {
            if (paramName == null) {
                continue;
            }
            
            String value = getParamValue(urlString, paramName);
            if (StringUtils.isNotEmpty(value)) {
                return value;
            }
        }

        return StringUtils.EMPTY;
    }
}
