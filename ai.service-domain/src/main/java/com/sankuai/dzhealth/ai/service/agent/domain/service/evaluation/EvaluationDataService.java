package com.sankuai.dzhealth.ai.service.agent.domain.service.evaluation;

import com.dianping.haima.entity.haima.HaimaContent;
import com.sankuai.dzhealth.ai.service.domain.utils.JsonUtils;
import com.sankuai.dzhealth.ai.service.dto.haima.EvaluationCaseInfo;
import com.sankuai.dzhealth.ai.service.infrastructure.acl.HaimaAcl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
public class EvaluationDataService {
    @Autowired
    private HaimaAcl haimaAcl;

    private static final String BEAUTY_BENCH = "beauty_bench";

    public List<EvaluationCaseInfo> getEvaluationCaseInfo() {
        List<HaimaContent> medicalTaskAiConfig = haimaAcl.getContent(BEAUTY_BENCH, null);
        return medicalTaskAiConfig
                .stream()
                .map(e -> {
                    EvaluationCaseInfo evaluationData = JsonUtils.parseObject(e.getExtJson(), EvaluationCaseInfo.class);
                    if (evaluationData == null) {
                        return null;
                    }
                    String initialQuestion = evaluationData.getInitialQuestion();
                    initialQuestion = initialQuestion.trim();
                    evaluationData.setInitialQuestions(initialQuestion.split("\\R"));
                    return evaluationData;
                }).
                filter(Objects::nonNull).
                toList();
    }

    public Map<String, EvaluationCaseInfo> getEvaluationCaseInfoMap() {
        return getEvaluationCaseInfo().stream().collect(Collectors.toMap(
                evaluationData -> String.valueOf(evaluationData.getNum()),
                evaluationData -> evaluationData,
                (existing, replacement) -> existing // 如果有重复的key，保留第一个)
        ));
    }
}
