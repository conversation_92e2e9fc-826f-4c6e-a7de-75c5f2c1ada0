package com.sankuai.dzhealth.ai.service.domain.card;

import com.alibaba.fastjson.JSONObject;
import com.sankuai.dzhealth.ai.service.domain.utils.context.AiAnswerContext;
import com.sankuai.dzhealth.ai.service.dto.stream.StreamEventDTO;
import org.springframework.core.Ordered;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 卡片处理器接口，定义处理JSON字段的方法
 */
public interface CardHandler extends Ordered {
    
    /**
     * 检查是否支持处理指定的字段
     * @param fieldName JSON字段名
     * @return 是否支持处理
     */
    boolean supports(String fieldName);
    
    /**
     * 处理JSON数据并生成卡片事件
     * @param jsonObject 完整的JSON对象
     * @param context 上下文信息
     * @param sseEmitter SSE发射器
     * @param sseIndex 消息索引计数器
     * @param startChatTime 开始时间，用于性能追踪
     * @return 处理生成的事件列表
     */
    List<StreamEventDTO> handle(JSONObject jsonObject, 
                              AiAnswerContext context, 
                              SseEmitter sseEmitter, 
                              AtomicInteger sseIndex, 
                              long startChatTime);
    
    /**
     * 获取处理器的执行顺序，数字越小优先级越高
     * 默认实现返回最低优先级
     * @return 执行顺序
     */
    @Override
    default int getOrder() {
        return Ordered.LOWEST_PRECEDENCE;
    }
} 