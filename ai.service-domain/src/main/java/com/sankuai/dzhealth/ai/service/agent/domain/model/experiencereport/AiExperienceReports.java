package com.sankuai.dzhealth.ai.service.agent.domain.model.experiencereport;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * AI助手案例列表信息领域模型
 * 包含案例列表、总数、微详情链接等完整展示信息
 *
 * <AUTHOR>
 * @version 0.0.1
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AiExperienceReports implements Serializable {

    private String title;

    /**
     * 案例总数量 (如：23)
     */
    private Integer totalCount;

    /**
     * 微详情页链接 ("查看全部"的跳转链接)
     */
    private String detailUrl;

    /**
     * 案例列表 (用于展示的前N个案例)
     */
    private List<AiCaseInfo> caseList;
}

