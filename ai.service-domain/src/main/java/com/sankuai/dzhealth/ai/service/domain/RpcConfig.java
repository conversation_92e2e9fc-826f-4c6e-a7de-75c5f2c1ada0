package com.sankuai.dzhealth.ai.service.domain;

import com.dianping.poi.bizhour.BizHourForecastService;
import com.meituan.mdp.boot.starter.pigeon.annotation.MdpPigeonClient;
import com.meituan.mdp.boot.starter.thrift.annotation.MdpThriftClient;
import com.meituan.taskcenter.service.TaskService;
import com.sankuai.beautycontent.store.storage.service.StoreCommandService;
import com.sankuai.beautycontent.store.storage.service.StoreQueryService;
import com.sankuai.call.sdk.service.IAiCallService;
import com.sankuai.clr.content.process.thrift.api.ShopBookInfoProcessService;
import com.sankuai.dzhealth.medical.client.biz.channelpagelist.service.ChannelPageListService;
import com.sankuai.dzhealth.medical.client.biz.department.service.DepartmentService;
import com.sankuai.dzhealth.medical.client.biz.hospital.service.HospitalService;
import com.sankuai.dzim.pilot.api.AIPhoneCallService;
import com.sankuai.leads.process.gateway.thrift.api.LeadsWriteController;
import com.sankuai.leads.process.thrift.resv.user.api.ResvUserOrderService;
import com.sankuai.map.open.platform.api.MapOpenApiService;
import com.sankuai.medicalcosmetology.mainpath.listingapi.service.ListingFacade;
import com.sankuai.sinai.data.api.service.MtPoiService;
import com.sankuai.wpt.user.retrieve.thrift.message.RpcUserRetrieveService;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;

/**
 * @author:chenwei
 * @time: 2025/3/20 10:22
 * @version: 0.0.1
 */
@Configuration
@ComponentScan({"com.sankuai.beautycontent.store.storage"})
public class RpcConfig {

    @MdpPigeonClient(url = "com.sankuai.dzhealth.medical.client.biz.department.service.DepartmentService", timeout = 5000)
    private DepartmentService departmentService;

    @MdpPigeonClient(url = "com.sankuai.dzhealth.medical.client.biz.hospital.service.HospitalService", timeout = 5000)
    private HospitalService hospitalService;

    @MdpPigeonClient(url = "AiCallService", timeout = 3000)
    private IAiCallService aiCallService;

    @MdpPigeonClient(url = "com.sankuai.dzhealth.medical.client.biz.channelpagelist.service.ChannelPageListService", timeout = 5000)
    private ChannelPageListService channelPageListService;

    @MdpThriftClient(remoteAppKey = "com.sankuai.apigw.map.facadecenter", timeout = 3000)
    private MapOpenApiService.Iface districtService;

    @MdpPigeonClient(url = "com.sankuai.beautycontent.store.storage.service.StoreCommandService", timeout = 2000)
    private StoreCommandService storeCommandService;

    @MdpPigeonClient(url = "com.sankuai.beautycontent.store.storage.service.StoreQueryService", timeout = 2000)
    private StoreQueryService storeQueryService;

    @MdpThriftClient(remoteAppKey = "com.sankuai.horus.solution.taskcenter", timeout = 5000)
    private TaskService.Iface taskCenterService;

    @MdpThriftClient(remoteAppKey = "com.sankuai.wpt.user.retrieve", testTimeout = 5000, timeout = 1000)
    private RpcUserRetrieveService.Iface rpcUserRetrieveService;

    @MdpPigeonClient(url = "com.sankuai.dzim.pilot.api.AIPhoneCallService", timeout = 2000)
    private AIPhoneCallService aiPhoneCallService;

    @MdpThriftClient(remoteAppKey = "com.sankuai.leads.content.process", timeout = 3000)
    private ShopBookInfoProcessService shopBookInfoProcessService;

    @MdpThriftClient(remoteAppKey = "com.sankuai.leads.process", timeout = 1500, testTimeout = 5000)
    private ResvUserOrderService resvUserOrderService;

    @MdpThriftClient(remoteAppKey = "com.sankuai.leads.process", timeout = 1000, testTimeout = 5000)
    private LeadsWriteController.Iface leadsWriteController;

    @MdpThriftClient(remoteAppKey = "com.sankuai.medicalcosmetology.mainpath.function", timeout = 5000, testTimeout = 5000)
    private ListingFacade listingFacade;

    @MdpThriftClient(remoteAppKey = "com.sankuai.sinai.data.query", timeout = 1000, testTimeout = 2000, async = true)
    private MtPoiService mtPoiServiceFuture;

    @MdpPigeonClient(
            url = "http://service.dianping.com/com.dianping.poi.bizhour.bizHourForecastService_1.0.0",
            timeout = 5000
    )
    private BizHourForecastService bizHourForecastService;


}
