package com.sankuai.dzhealth.ai.service.domain.enums;

import java.util.Arrays;

public enum XhsPromptEnum {
    GET_PROMPT(1, "获取 prompt"),
    SAVE_PROMPT(2, "保存 prompt"),
    VERIFY_PROMPT(3, "校验 prompt"),
    ERROR(-1, "未知");

    private final int taskType;

    XhsPromptEnum(int taskType, String ignore) {
        this.taskType = taskType;
    }

    public int getStatus() {
        return taskType;
    }

    public static XhsPromptEnum from(int taskType) {

        return Arrays.stream(XhsPromptEnum.values())
                .filter(xhsPromptEnum -> xhsPromptEnum.getStatus() == taskType)
                .findFirst()
                .orElse(XhsPromptEnum.ERROR);
    }
}
