package com.sankuai.dzhealth.ai.service.agent.domain.enums;

import com.sankuai.dzhealth.ai.service.agent.infrastructure.enums.ProductNameEnum;
import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

@Getter
public enum AppointmentProjectEnum {


    ORAL_EXAMINATION(66,ProductNameEnum.ORAL_EXAMINATION),
    DENTAL_IMPLANT(25,ProductNameEnum.DENTAL_IMPLANT),
    ORTHODONTICS(22, ProductNameEnum.ORTHODONTICS),
    FILLING(23, ProductNameEnum.FILLING),
    EXTRACTION(24, ProductNameEnum.EXTRACTION),
    WHITENING(67, ProductNameEnum.WHITENING),
    CLEANING(21, ProductNameEnum.CLEANING);



    private final long code;
    private final ProductNameEnum product;

    AppointmentProjectEnum(long code, ProductNameEnum product) {
        this.code = code;
        this.product = product;
    }

    public static ProductNameEnum getProduct(Integer code) {
        return Arrays.stream(values()).filter(AppointmentProjectEnum -> Objects.equals(AppointmentProjectEnum.code,code)).findFirst().map(AppointmentProjectEnum::getProduct).orElse(null);
    }

    public static Long getCode(ProductNameEnum product){
        return Arrays.stream(values()).filter(AppointmentProjectEnum -> Objects.equals(AppointmentProjectEnum.product, product)).findFirst().map(AppointmentProjectEnum::getCode).orElse(null);
    }
}
