package com.sankuai.dzhealth.ai.service.agent.domain.utils;

import com.sankuai.dzhealth.ai.service.dto.stream.StreamEventDTO;
import com.sankuai.dzhealth.ai.service.dto.stream.StreamEventDataDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * @author:chenwei
 * @time: 2025/7/6 10:47
 * @version: 0.0.1
 */
@Slf4j
public class SseUtils {

    public static void setSseHeader(HttpServletResponse httpServletResponse) {
        httpServletResponse.setHeader("X-Accel-Buffering", "no");
        httpServletResponse.setHeader("Cache-Control", "no-cache");
        httpServletResponse.setHeader("Connection", "keep-alive");
    }

    public static boolean sendSseContent(SseEmitter sseEmitter, String type, String event, String content) throws IOException {
        try {
            sseEmitter.send(SseEmitter.event().data(StreamEventDTO.builder().type(type)
                    .data(StreamEventDataDTO.builder().event(event).content(content).build()).build()));
            return true;
        } catch (Exception e) {
            log.error("sse 发送失败, msg={}", e.getMessage(), e);
        }
        return false;
    }
}
