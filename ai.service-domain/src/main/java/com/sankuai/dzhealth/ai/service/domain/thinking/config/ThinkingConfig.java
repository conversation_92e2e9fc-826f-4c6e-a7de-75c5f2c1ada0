package com.sankuai.dzhealth.ai.service.domain.thinking.config;

import com.dianping.lion.Environment;
import com.meituan.service.inf.kms.client.Kms;
import com.meituan.service.inf.kms.utils.KmsResultNullException;
import com.sankuai.dzhealth.ai.service.domain.thinking.tool.SequentialThinkingTool;
import com.sankuai.dzhealth.ai.service.domain.thinking.tool.TimeTool;
import com.sankuai.dzhealth.ai.service.domain.thinking.tool.WebSearchTool;
import lombok.RequiredArgsConstructor;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.model.ChatModel;
import org.springframework.ai.openai.OpenAiChatModel;
import org.springframework.ai.openai.OpenAiChatOptions;
import org.springframework.ai.openai.api.OpenAiApi;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 思考引擎配置类
 */
@Configuration
@RequiredArgsConstructor
public class ThinkingConfig {

    private final SequentialThinkingTool sequentialThinkingTool;
    private final WebSearchTool webSearchTool;
    private final TimeTool timeTool;

    /**
     * 配置ChatClient
     *
     * @return ChatClient实例
     */
    @Bean("thinkingChatClient")  // 显式指定 Bean 名称
    public ChatClient thinkingChatClient() throws KmsResultNullException {

        OpenAiApi openAiApi = new OpenAiApi.Builder()
                .completionsPath("/chat/completions")
                .baseUrl("https://aigc.sankuai.com/v1/openai/native")
                .apiKey(Kms.getByName(Environment.getAppName(), "friday.search.appId"))
                .build();
        OpenAiChatOptions openAiChatOptions = OpenAiChatOptions.builder()
                .model("deepseek-v3-friday")
                .temperature(0.0)
                .maxTokens(1000)
                .build();
        OpenAiChatModel chatModel = OpenAiChatModel.builder()
                .openAiApi(openAiApi)
                .defaultOptions(openAiChatOptions)
                .build();
        return ChatClient.builder(chatModel)
                .defaultTools(sequentialThinkingTool, webSearchTool, timeTool)  // 注册三个工具：思考、搜索、时间
                .build();
    }

}