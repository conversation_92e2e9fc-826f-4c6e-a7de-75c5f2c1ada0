package com.sankuai.dzhealth.ai.service.agent.domain.process.task.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.Cat;
import com.dianping.cat.util.MetricHelper;
import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import com.dianping.rhino.Rhino;
import com.dianping.rhino.threadpool.DefaultThreadPoolProperties;
import com.dianping.rhino.threadpool.ThreadPool;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.meituan.mdp.boot.starter.config.annotation.MdpConfig;
import com.meituan.mdp.boot.starter.util.Pair;
import com.sankuai.beautycontent.experience.enums.FilterType;
import com.sankuai.dzhealth.ai.service.agent.domain.buffer.MessageBuffer;
import com.sankuai.dzhealth.ai.service.agent.domain.buffer.MessageBufferEntity;
import com.sankuai.dzhealth.ai.service.agent.domain.context.MessageContext;
import com.sankuai.dzhealth.ai.service.agent.domain.context.TaskContext;
import com.sankuai.dzhealth.ai.service.agent.domain.enums.TaskTypeEnum;
import com.sankuai.dzhealth.ai.service.agent.domain.model.*;
import com.sankuai.dzhealth.ai.service.agent.domain.model.decisionflow.DecisionTreeModel;
import com.sankuai.dzhealth.ai.service.agent.domain.model.experiencereport.AiExperienceReports;
import com.sankuai.dzhealth.ai.service.agent.domain.model.experiencereport.ExperienceReportRequestModel;
import com.sankuai.dzhealth.ai.service.agent.domain.process.card.data.*;
import com.sankuai.dzhealth.ai.service.agent.domain.process.task.GeneralTask;
import com.sankuai.dzhealth.ai.service.agent.domain.process.task.Task;
import com.sankuai.dzhealth.ai.service.agent.domain.process.task.TaskConfig;
import com.sankuai.dzhealth.ai.service.agent.domain.service.MedicalDecisionRagService;
import com.sankuai.dzhealth.ai.service.agent.domain.service.RecommendService;
import com.sankuai.dzhealth.ai.service.agent.domain.service.experiencereport.ExperienceReportDomainService;
import com.sankuai.dzhealth.ai.service.agent.domain.utils.BufferUtils;
import com.sankuai.dzhealth.ai.service.agent.domain.utils.HaimaAIUtils;
import com.sankuai.dzhealth.ai.service.agent.domain.utils.UrlUtils;
import com.sankuai.dzhealth.ai.service.agent.enums.ContextExtraKey;
import com.sankuai.dzhealth.ai.service.domain.utils.JsonUtils;
import com.sankuai.dzhealth.ai.service.domain.utils.context.RequestContext;
import com.sankuai.dzhealth.ai.service.domain.utils.context.RequestContextConstant;
import com.sankuai.dzhealth.ai.service.enums.BizSceneEnum;
import com.sankuai.dzhealth.ai.service.enums.BufferItemTypeEnum;
import com.sankuai.dzhealth.ai.service.enums.StreamEventCardTypeEnum;
import com.sankuai.dzhealth.ai.service.request.common.BasicParam;
import com.sankuai.medicalcosmetology.display.api.DoctorInfoQueryService;
import com.sankuai.medicalcosmetology.display.dto.DoctorCardInfoAndCase;
import com.sankuai.medicalcosmetology.display.dto.DoctorCardInfoAndCaseBatchQueryResponse;
import com.sankuai.medicalcosmetology.display.enums.DoctorRecommendFilterEnum;
import com.sankuai.medicalcosmetology.display.request.DoctorRecommendListRecallRequest;
import com.sankuai.medicalcosmetology.mainpath.listingapi.common.ResponseDTO;
import com.sankuai.medicalcosmetology.mainpath.listingapi.dto.FillInfoDTO;
import com.sankuai.medicalcosmetology.mainpath.listingapi.dto.GoodsRecallDTO;
import com.sankuai.medicalcosmetology.mainpath.listingapi.dto.ReCallSortIdsDTO;
import com.sankuai.medicalcosmetology.mainpath.listingapi.dto.ShopGoodsRecallDTO;
import com.sankuai.medicalcosmetology.mainpath.listingapi.request.FillInfoQry;
import com.sankuai.medicalcosmetology.mainpath.listingapi.request.GoodsQry;
import com.sankuai.medicalcosmetology.mainpath.listingapi.request.GoodsShopQry;
import com.sankuai.medicalcosmetology.mainpath.listingapi.request.ReCallSortIdsQry;
import com.sankuai.medicalcosmetology.mainpath.listingapi.service.ListingFacade;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

import static com.sankuai.dzhealth.ai.service.agent.domain.process.task.impl.RecommendListTask.RECOMMEND_REASON;

/**
 * 医疗产品推荐任务处理器
 * 负责处理医疗产品推荐流程，包括思考过程展示、决策分析和推荐查询
 *
 * <AUTHOR>
 * @version 0.0.1
 * @since 2025/7/14 21:46
 */
@Service
@Slf4j
public class MedicalProductTask extends GeneralTask implements Task {
    private static final String THINKING_STATUS = "思考中";

    private static final String THINKING_SEARCH_STATUS = "搜索中";

    private static final String THINKING_RESULT_GENERATE_STATUS = "结果生成中";
    public static final String THINKING_COMPLETE_STATUS = "思考已完成";

    private static final String SETTLE_DATA = "正在整理数据中";
    private static final String DECISION_MODEL_CONFIG_KEY = "medicalDecision";

    @MdpConfig("recommend.page.size:10")
    private int recommendSize;
    private static final int RECOMMEND_PAGE_NUM = 0;

    public static final String DEFAULT_HEAD_PIC = "https://p0.meituan.net/ingee/63226470246fb7c718268c282e0458783882.png";

    @Autowired
    private HaimaAIUtils haimaAIUtils;

    @Autowired
    private ListingFacade listingFacade;

    @Autowired
    private DoctorInfoQueryService doctorInfoQueryService;

    @Autowired
    private MedicalDecisionRagService medicalDecisionRagService;


    @Autowired
    private RecommendService recommendService;

    @Autowired
    private ExperienceReportDomainService experienceReportDomainService;

    private static final Gson gson = new GsonBuilder()
            .excludeFieldsWithoutExposeAnnotation()
            .create();

    // 线程池配置
    public static final ThreadPool PRODUCT_POOL = Rhino.newThreadPool("productPool",
            DefaultThreadPoolProperties.Setter().withCoreSize(20).withMaxSize(100).withMaxQueueSize(500));

    public static final ThreadPool SEARCH_POOL = Rhino.newThreadPool("searchPool",
            DefaultThreadPoolProperties.Setter().withCoreSize(30).withMaxSize(100).withMaxQueueSize(500));

    @Override
    public boolean accept(String type) {
        return TaskTypeEnum.MEDICAL_PRODUCT_TASK.getType().equals(type);
    }

    @Override
    public TaskProcessResult process(TaskContext context) {
        // 1. 初始化处理
        initializeTaskContext(context);
        String userQuery = context.getTaskConfig().getUserPrompt();
        MessageBuffer buffer = RequestContext.getAttribute(RequestContextConstant.MESSAGE_BUFFER);
        String bizType = RequestContext.getAttribute(RequestContextConstant.BIZ_TYPE);


        ThinkProcessCardData thinkProcessCardData = createThinkProcessCardData();

        BufferUtils.writeThinkingProcessStreamBuffer(
                MessageBufferEntity.builder()
                        .data(StreamEventCardTypeEnum.buildCardContent(
                                StreamEventCardTypeEnum.THINK_PROCESS_STREAM,
                                UUID.randomUUID().toString().replace("-", "")
                        ))
                        .type(BufferItemTypeEnum.THINK_PROCESS_STREAM.getType())
                        .extra(ImmutableMap.of("thinkProcessData", Lists.newArrayList(thinkProcessCardData)))
                        .build(),
                buffer
        );


        // cpv 属性
        CompletableFuture<String> productCpvFuture = CompletableFuture.supplyAsync(() -> medicalDecisionRagService.search(context.getMessageContext().getMsg(), 5, Lists.newArrayList("medicalCpv")), PRODUCT_POOL.getExecutor());

//        CompletableFuture<String> baikeFuture = CompletableFuture.supplyAsync(() -> medicalDecisionRagService.search(context.getMessageContext().getMsg(), 5, Lists.newArrayList("百科", "避雷针")), PRODUCT_POOL.getExecutor());

        // 判别图片id转换
        MemoryModel memoryModel = context.getMessageContext().getMemoryModel();
        List<DecisionTreeModel> memoryModelDecisionTreeModelList = new ArrayList<>();
        if (memoryModel != null && memoryModel.getDecisionTreeModelList() != null) {
            memoryModelDecisionTreeModelList.addAll(memoryModel.getDecisionTreeModelList());
        }
        String decisionRagStr = medicalDecisionRagService.queryDecisionRagInfo(userQuery, 2, context.getMessageContext().getBizType());
        List<DecisionTreeModel> ragDecisionTreeRagModels = JsonUtils.parseArray(decisionRagStr, DecisionTreeModel.class);

        context.getMessageContext().getExtra().put("originRagInfo", JsonUtils.toJsonString(ragDecisionTreeRagModels));
        // 处理图片URL到ID的映射
        processImageUrlMapping(memoryModelDecisionTreeModelList, ragDecisionTreeRagModels, context);

        // memoryModel 更新 图片为id
        if (memoryModel != null && memoryModel.getDecisionTreeModelList() != null) {
            memoryModel.setDecisionTreeModelList(memoryModelDecisionTreeModelList);
            context.getMessageContext().setMemoryModel(memoryModel);
        }

        String productCpv = productCpvFuture.join();
//        String baike = baikeFuture.join();

        long startTime = System.currentTimeMillis();
        Cat.newTransactionWithDuration(getClass().getSimpleName(), "startTime", 0);

        // 2. 执行思考流程，获取决策文本和完整内容的Future
        ThinkingFlowResult thinkingFlowResult = executeThinkingProcess(context, buffer,
                gson.toJson(ragDecisionTreeRagModels),
                gson.toJson(memoryModelDecisionTreeModelList), productCpv);
        // 3. 等待决策文本完成
        String decisionText = thinkingFlowResult.getDecisionFuture().join();
        Cat.newTransactionWithDuration(getClass().getSimpleName(), "think", System.currentTimeMillis() - startTime);

        context.getMessageContext().setThinkText(decisionText);

        String fullContent = thinkingFlowResult.getFullContentFuture().join();
        // medicalProduct 思考过程
        buildMultiDialogueEvaluationRequest(context.getMessageContext(), context.getTaskConfig(), fullContent);
        String decisionFilter = extractJsonFromContent(fullContent);

        context.getMessageContext().getExtra().put("decisionJson", decisionFilter);
        log.info("query={},decisionJson={}", context.getMessageContext().getMsg(), decisionFilter);
        List<DecisionTreeModel> decisionFilterTreeModels = JSON.parseArray(decisionFilter, DecisionTreeModel.class);

        context.getMessageContext().getExtra().put("beforeProcessDecisionTree", JsonUtils.toJsonString(decisionFilterTreeModels));
        processDecisionTree(decisionFilterTreeModels, bizType, context);
        context.getMessageContext().getExtra().put("afterProcessDecisionTree", JsonUtils.toJsonString(decisionFilterTreeModels));

        thinkProcessCardData.setLoadingText(SETTLE_DATA);
        thinkProcessCardData.setContent(decisionText);
        BufferUtils.writeThinkingProcessStreamBuffer(
                MessageBufferEntity.builder()
                        .data(StreamEventCardTypeEnum.buildCardContent(
                                StreamEventCardTypeEnum.THINK_PROCESS_STREAM,
                                UUID.randomUUID().toString().replace("-", "")
                        ))
                        .type(BufferItemTypeEnum.THINK_PROCESS_STREAM.getType())
                        .extra(ImmutableMap.of("thinkProcessData", Lists.newArrayList(thinkProcessCardData)))
                        .build(),
                buffer
        );

        Cat.newTransactionWithDuration(getClass().getSimpleName(), "fullContent", System.currentTimeMillis() - startTime);
        // 4. 异步获取推荐数据（内部等待fullContent完成）
        CompletableFuture<List<Object>> recommendFuture = processRecommendationsAsync(decisionFilterTreeModels, context, ragDecisionTreeRagModels, bizType);

        Cat.newTransactionWithDuration(getClass().getSimpleName(), "shopProduct", System.currentTimeMillis() - startTime);
        Cat.logEvent("shopProduct", String.valueOf(System.currentTimeMillis() - startTime));
        // 5. 生成最终答案（基于decisionText）
        String finalAnswer = generateFinalAnswer(context, userQuery, decisionText,
                JSON.toJSONString(ragDecisionTreeRagModels), JSON.toJSONString(memoryModelDecisionTreeModelList), JSON.toJSONString(decisionFilterTreeModels));

        Cat.newTransactionWithDuration(getClass().getSimpleName(), "finalAnswer", System.currentTimeMillis() - startTime);
        Cat.logEvent("finalAnswer", String.valueOf(System.currentTimeMillis() - startTime));
        Cat.logEvent("finalAnswerTime", String.valueOf(System.currentTimeMillis()));
        // 6. 保存决策数据（内部等待fullContent完成）
        saveDecisionData(fullContent);
        // 7. 等待推荐结果和数据保存完成（可选，根据业务需要决定是否等待）
        try {
            // 构建供给ID到卡片类型的映射
            Map<String, Object> supplyIdCardMap = buildSupplyIdCardMap(decisionFilterTreeModels);

            log.info("query={},supplyIdCardMap={}",  context.getMessageContext().getMsg(), JsonUtils.toJsonString(supplyIdCardMap));
            List<Object> recommendSupply = recommendFuture.join();
            Map<String, Object> supplyKeyIdMap = getSupplyKeyIdMapFromContext(context);

            // 合并映射数据
            if (MapUtils.isNotEmpty(supplyIdCardMap) && (MapUtils.isNotEmpty(supplyKeyIdMap) || CollectionUtils.isNotEmpty(recommendSupply))) {
                supplyKeyIdMap.putAll(supplyIdCardMap);
            }
            Map<String, Object> supplyId2TitleMap = JsonUtils.parseMap((String) context.getMessageContext().getExtra().get(ContextExtraKey.SUPPLY_ID_TITLE.getKey()));

            log.info("query={},recommendSupply={}, supplyKeyIds={}, supplyId2TitleMap={}", context.getMessageContext().getMsg(), JsonUtils.toJsonString(recommendSupply),
                    JsonUtils.toJsonString(supplyKeyIdMap), JsonUtils.toJsonString(supplyId2TitleMap));
            Cat.logEvent("processTime", String.valueOf(System.currentTimeMillis()));
            process(recommendSupply, supplyKeyIdMap, supplyId2TitleMap);
            Cat.newTransactionWithDuration(getClass().getSimpleName(), "finalProduct", System.currentTimeMillis() - startTime);
            Cat.logEvent("finalProduct", String.valueOf(System.currentTimeMillis() - startTime));
        } catch (Exception e) {
            log.error("异步任务执行异常，但不影响主流程", e);
        }

        // medicalDecision 导购
        buildMultiDialogueEvaluationRequest(context.getMessageContext(), context.getTaskConfig(), finalAnswer);
        return TaskProcessResult.builder()
                .taskContext(context)
                .answer(finalAnswer)
                .build();
    }

    private void processDecisionTree(List<DecisionTreeModel> decisionTreeModels, String bizType, TaskContext context) {
        Map<String, MedicalCaseConfigModel> medicalCaseConfigModelMap = Lion.getMap(Environment.getAppName(), "decision.medical.case.config", MedicalCaseConfigModel.class);
        if (CollectionUtils.isNotEmpty(decisionTreeModels)) {
            decisionTreeModels.forEach(decisionTreeModel -> {
                if (medicalCaseConfigModelMap.containsKey(decisionTreeModel.getNodeName())) {
                    decisionTreeModel.setNeedSupply(medicalCaseConfigModelMap.get(decisionTreeModel.getNodeName()).getNeedSupply());
                    SupplyRecommendModel supplyRecommendModel = new SupplyRecommendModel();
                    supplyRecommendModel.setMedicalCaseTag(medicalCaseConfigModelMap.get(decisionTreeModel.getNodeName()).getMedicalCaseTag());
                    decisionTreeModel.setSupplyRecommendModel(supplyRecommendModel);
                }

                List<DecisionTreeModel> drillDownRequirements = decisionTreeModel.getDrillDownRequirements();
                if (CollectionUtils.isNotEmpty(drillDownRequirements)) {
                    // 使用List存储需要移除的requirement，避免在迭代时修改集合
                    List<DecisionTreeModel> requirementsToRemove = new ArrayList<>();
                    List<CompletableFuture<Void>> futures = new ArrayList<>();

                    if (decisionTreeModel.getNeedSupply() != null && decisionTreeModel.getNeedSupply()) {
                        drillDownRequirements.stream()
                                .filter(requirement -> requirement.getSupplyRecommendModel() != null)
                                .forEach(requirement -> {
                                    SupplyRecommendModel supplyModel = requirement.getSupplyRecommendModel();
                                    if (BizSceneEnum.MOUTH_CONSULT.getBizScene().equals(bizType)) {
                                        supplyModel.setSupplyType(SupplyRecommendModel.SupplyType.SHOP.getCode());
                                    }
                                    Pair<Double, Double> positionPair = parsePosition(supplyModel, getCurrentBasicParam(context));

                                    if ("doctor".equals(supplyModel.getSupplyType())) {
                                        CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                                            try {
                                                DoctorRecommendListRecallRequest doctorRequest = new DoctorRecommendListRecallRequest();
                                                buildDoctorRequest(doctorRequest, supplyModel, getCurrentMessageContext(context), positionPair);

                                                DoctorCardInfoAndCaseBatchQueryResponse doctorResponse =
                                                        doctorInfoQueryService.queryDoctorRecommendList(doctorRequest);

                                                log.info("前置检查医生数据,req={},res={}",JsonUtils.toJsonString(doctorRequest), JsonUtils.toJsonString(doctorResponse));
                                                // 检查响应数据是否为空
                                                if (doctorResponse == null || !doctorResponse.getSuccess() ||
                                                        CollectionUtils.isEmpty(doctorResponse.getData())) {
                                                    log.info("医生推荐响应数据为空，将移除requirement={},req={},res={}", JsonUtils.toJsonString(requirement),
                                                            JsonUtils.toJsonString(doctorRequest), JsonUtils.toJsonString(doctorResponse));
                                                    synchronized (requirementsToRemove) {
                                                        requirementsToRemove.add(requirement);
                                                    }
                                                }
                                            } catch (Exception e) {
                                                log.error("医生推荐查询异常，将移除requirement: {}", JsonUtils.toJsonString(requirement), e);
                                                synchronized (requirementsToRemove) {
                                                    requirementsToRemove.add(requirement);
                                                }
                                            }
                                        }, PRODUCT_POOL.getExecutor());
                                        futures.add(future);

                                    } else {
                                        CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                                            try {
                                                ReCallSortIdsQry reCallSortIdsQry = new ReCallSortIdsQry();
                                                buildReCallRequest(reCallSortIdsQry, supplyModel, getCurrentMessageContext(context), positionPair);
                                                ResponseDTO<ReCallSortIdsDTO> shopResponse = listingFacade.reCallSortIds(reCallSortIdsQry);

                                                log.info("前置检查商户数据,req={},res={}",JsonUtils.toJsonString(reCallSortIdsQry), JsonUtils.toJsonString(shopResponse));
                                                // 检查响应数据是否为空
                                                if (shopResponse == null || !shopResponse.isSuccess() || shopResponse.getData() == null ||
                                                        CollectionUtils.isEmpty(shopResponse.getData().getShopGoodsRecallDTOs())) {
                                                    log.info("商户推荐响应数据为空，将移除requirement={},req={},res={}", JsonUtils.toJsonString(requirement),
                                                            JsonUtils.toJsonString(reCallSortIdsQry), JsonUtils.toJsonString(shopResponse));
                                                    synchronized (requirementsToRemove) {
                                                        requirementsToRemove.add(requirement);
                                                    }
                                                }
                                            } catch (Exception e) {
                                                log.error("商户推荐查询异常，将移除requirement: {}", JsonUtils.toJsonString(requirement), e);
                                                synchronized (requirementsToRemove) {
                                                    requirementsToRemove.add(requirement);
                                                }
                                            }
                                        }, PRODUCT_POOL.getExecutor());
                                        futures.add(future);
                                    }
                                });

                        // 等待所有异步任务完成
                        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
                    }


                    // 移除需要删除的requirement
                    if (!requirementsToRemove.isEmpty()) {
                        drillDownRequirements.removeAll(requirementsToRemove);
                        log.info("已移除 {} 个无效的requirement", requirementsToRemove.size());
                    }
                }
            });

        }
    }


    private void process(List<Object> recommendSupply, Map<String, Object> supplyIdCardMap, Map<String, Object> supplyId2TitleMap) {

        List<SupplyRecommendModel> filterList = new ArrayList<>();
        if (MapUtils.isNotEmpty(supplyId2TitleMap) && MapUtils.isNotEmpty(supplyIdCardMap)) {
            log.info("supplyId2TitleMap={}", JsonUtils.toJsonString(supplyId2TitleMap));
            for (Object object : recommendSupply) {
                if (object instanceof DoctorProductCardData doctorProductCardData) {
                    SupplyRecommendModel supplyRecommendModel = doctorProductCardData.getFilterList().get(0);
                    String reason = String.valueOf(supplyId2TitleMap.get(supplyRecommendModel.getSupplyId()));
                    if (reason.contains(":")) {
                        reason = reason.substring(reason.indexOf(":") + 1).trim();
                    }
                    supplyRecommendModel.setSupplyRecommendReason(reason);
                    if (StringUtils.isBlank(reason)) {
                        doctorProductCardData.setNeedMore(false);
                    }
                    String cardType = String.valueOf(supplyIdCardMap.get(supplyRecommendModel.getSupplyId()));
                    if (StreamEventCardTypeEnum.DOCTOR_CARD.getType().equals(cardType) && doctorProductCardData.getDetail() != null && doctorProductCardData.getNeedMore()) {
                        filterList.add(supplyRecommendModel);
                    }
                } else if (object instanceof ShopProductCardData shopProductCardData) {
                    SupplyRecommendModel supplyRecommendModel = shopProductCardData.getFilterList().get(0);
                    String reason = String.valueOf(supplyId2TitleMap.get(supplyRecommendModel.getSupplyId()));
                    if (reason.contains(":")) {
                        reason = reason.substring(reason.indexOf(":") + 1).trim();
                    }
                    supplyRecommendModel.setSupplyRecommendReason(reason);
                    if (StringUtils.isBlank(reason)) {
                        shopProductCardData.setNeedMore(false);
                    }
                    String cardType = String.valueOf(supplyIdCardMap.get(supplyRecommendModel.getSupplyId()));
                    if (StreamEventCardTypeEnum.SHOP_PRODUCT_CARD.getType().equals(cardType) && shopProductCardData.getDetail() != null && shopProductCardData.getNeedMore()) {
                        filterList.add(supplyRecommendModel);
                    }
                }
            }
        }


        // 构建supplyId到推荐数据的映射，提高查找效率
        Map<String, Object> supplyIdToRecommendDataMap = buildSupplyIdToRecommendDataMap(recommendSupply);

        // 遍历supplyIdCardMap中的每个key和对应的type
        for (Map.Entry<String, Object> entry : supplyIdCardMap.entrySet()) {
            String supplyId = entry.getKey();
            String cardType = String.valueOf(entry.getValue());

            // 从映射中快速获取匹配的数据
            Object matchedRecommendData = supplyIdToRecommendDataMap.get(supplyId);
            Integer foundIndex = findSupplyIndexInFilterList(supplyId, filterList);
            if (StreamEventCardTypeEnum.DOCTOR_CARD.getType().equals(cardType)) {
                // 发送医生卡片
                if (matchedRecommendData instanceof DoctorProductCardData doctorProductCardData) {

                    log.info("before<process>doctorProductCardData={}, supplyModel={}", JsonUtils.toJsonString(doctorProductCardData), JsonUtils.toJsonString(doctorProductCardData.getFilterList()));
                    // 有匹配的医生数据
                    doctorProductCardData.setFilterList(filterList);
                    if (CollectionUtils.isEmpty(filterList)) {
                        doctorProductCardData.setNeedMore(false);
                    }

                    // 查找filterList中与当前supply相同supplyId的元素索引
                    if (foundIndex != null) {
                        doctorProductCardData.setIndex(foundIndex);
                    }

                    log.info("after<process>doctorProductCardData={}, supplyModel={}", JsonUtils.toJsonString(doctorProductCardData), JsonUtils.toJsonString(doctorProductCardData.getFilterList()));

                    BufferUtils.writeMainTextBuffer(MessageBufferEntity.builder()
                            .data(StreamEventCardTypeEnum.buildCardContent(StreamEventCardTypeEnum.DOCTOR_CARD, supplyId))
                            .type(BufferItemTypeEnum.MAIN_TEXT.getType())
                            .extra(doctorProductCardData.toMap().get("detail") == null ? null : doctorProductCardData.toMap())
                            .build(), null);
                } else {
                    // 没有匹配的数据，发送null
                    log.info("<process>No matching doctor data found for supplyId={}", supplyId);
                    BufferUtils.writeMainTextBuffer(MessageBufferEntity.builder()
                            .data(StreamEventCardTypeEnum.buildCardContent(StreamEventCardTypeEnum.DOCTOR_CARD, supplyId))
                            .type(BufferItemTypeEnum.MAIN_TEXT.getType())
                            .extra(null)
                            .build(), null);
                }
            } else if (StreamEventCardTypeEnum.SHOP_PRODUCT_CARD.getType().equals(cardType)) {
                // 发送商户卡片
                if (matchedRecommendData instanceof ShopProductCardData shopProductCardData) {
                    // 有匹配的商户数据
                    log.info("before<process>shopProductCardData={}, supplyModel={}", JsonUtils.toJsonString(shopProductCardData), JsonUtils.toJsonString(shopProductCardData.getFilterList()));
                    shopProductCardData.setFilterList(filterList);
                    if (CollectionUtils.isEmpty(filterList)) {
                        shopProductCardData.setNeedMore(false);
                    }
                    if (foundIndex != null) {
                        shopProductCardData.setIndex(foundIndex);
                    }
                    log.info("after<process>shopProductCardData={}, supplyModel={}", JsonUtils.toJsonString(shopProductCardData), JsonUtils.toJsonString(shopProductCardData.getFilterList()));
                    BufferUtils.writeMainTextBuffer(MessageBufferEntity.builder()
                            .data(StreamEventCardTypeEnum.buildCardContent(StreamEventCardTypeEnum.SHOP_PRODUCT_CARD, supplyId))
                            .type(BufferItemTypeEnum.MAIN_TEXT.getType())
                            .extra(shopProductCardData.toMap().get("detail") == null ? null : shopProductCardData.toMap())
                            .build(), null);
                } else {
                    // 没有匹配的数据，发送null
                    log.info("<process>No matching shop data found for supplyId={}", supplyId);
                    BufferUtils.writeMainTextBuffer(MessageBufferEntity.builder()
                            .data(StreamEventCardTypeEnum.buildCardContent(StreamEventCardTypeEnum.SHOP_PRODUCT_CARD, supplyId))
                            .type(BufferItemTypeEnum.MAIN_TEXT.getType())
                            .extra(null)
                            .build(), null);
                }
            }
        }

    }

    /**
     * 构建supplyId到推荐数据的映射，提高查找效率
     *
     * @param recommendSupply 推荐数据列表
     * @return supplyId到推荐数据的映射Map
     */
    private Map<String, Object> buildSupplyIdToRecommendDataMap(List<Object> recommendSupply) {
        Map<String, Object> supplyIdToDataMap = new HashMap<>();

        if (CollectionUtils.isEmpty(recommendSupply)) {
            return supplyIdToDataMap;
        }

        for (Object recommendData : recommendSupply) {
            String supplyId = null;

            if (recommendData instanceof DoctorProductCardData doctorProductCardData) {
                if (CollectionUtils.isNotEmpty(doctorProductCardData.getFilterList())) {
                    SupplyRecommendModel supplyModel = doctorProductCardData.getFilterList().get(0);
                    supplyId = supplyModel.getSupplyId();
                }
            } else if (recommendData instanceof ShopProductCardData shopProductCardData) {
                if (CollectionUtils.isNotEmpty(shopProductCardData.getFilterList())) {
                    SupplyRecommendModel supplyModel = shopProductCardData.getFilterList().get(0);
                    supplyId = supplyModel.getSupplyId();
                }
            }

            if (StringUtils.isNotBlank(supplyId)) {
                supplyIdToDataMap.put(supplyId, recommendData);
            }
        }

        return supplyIdToDataMap;
    }

    /**
     * 初始化任务上下文
     */
    private void initializeTaskContext(TaskContext context) {
        if (StringUtils.isBlank(context.getTaskConfig().getUserPrompt())) {
            context.getTaskConfig().setUserPrompt(context.getMessageContext().getMsg());
        }
    }

    /**
     * 执行思考流程处理
     */
    public ThinkingFlowResult executeThinkingProcess(TaskContext context, MessageBuffer buffer,
                                                     String decisionRagStr, String historyDecisionStr, String productCpv) {
        // 创建思考过程卡片数据
        ThinkProcessCardData thinkProcessCardData = createThinkProcessCardData();

        String systemPrompt = context.getTaskConfig().getSystemPrompt();
        String replace = systemPrompt.replace("{{{decisions}}}", decisionRagStr)
                .replace("{{{historyDecisions}}}", historyDecisionStr)
                .replace("{{{productCpv}}}", productCpv)
                .replace("{{{userPersona}}}", Optional.ofNullable(context.getMessageContext().getMemoryModel())
                        .map(e -> JsonUtils.toJsonString(e.getMemoryList())).orElse(""));

        log.info("query={}, thinkPrompt={}", context.getMessageContext().getMsg(), replace);
        context.getTaskConfig().setSystemPrompt(replace);

        thinkProcessCardData.setLoadingText(THINKING_SEARCH_STATUS);
        writeThinkingProcessBuffer(thinkProcessCardData, buffer);

        // 订阅思考流程
        Flux<String> thinkAnswerFlux = getFluxThink(context);
        return subscribeToThinkingStream(context.getTaskConfig().getType(), thinkAnswerFlux, thinkProcessCardData, buffer);
    }

    /**
     * 创建思考过程卡片数据
     */
    private ThinkProcessCardData createThinkProcessCardData() {
        return ThinkProcessCardData.builder()
                .loadingText(THINKING_STATUS)
                .content("")
                .isFinish(false)
                .build();
    }

    /**
     * 订阅思考流程
     *
     * @return
     */
    private ThinkingFlowResult subscribeToThinkingStream(String type, Flux<String> thinkAnswerFlux,
                                                         ThinkProcessCardData thinkProcessCardData,
                                                         MessageBuffer buffer) {
        // 初始化思考状态
        AtomicBoolean intercepted = new AtomicBoolean(false);
        StringBuilder mockThinkAnswer = new StringBuilder();
        StringBuilder fullThinkAnswer = new StringBuilder();
        CompletableFuture<String> decisionFuture = new CompletableFuture<>();
        CompletableFuture<String> fullContentFuture = new CompletableFuture<>();

        long startTime = System.currentTimeMillis();
        AtomicBoolean isFirst = new AtomicBoolean(false);
        thinkAnswerFlux.subscribe(content -> {
            // ttft埋点
            if (StringUtils.isNotBlank(content) && isFirst.compareAndSet(false, true)) {
                MetricHelper.build().name("大模型耗时打点")
                        .tag("taskType", type)
                        .tag("metricType", "ttft")
                        .value(System.currentTimeMillis() - startTime);
            }
            handleThinkingContent(content, mockThinkAnswer, fullThinkAnswer, decisionFuture, intercepted,
                    thinkProcessCardData, buffer);
        }, error -> handleThinkingError(error, fullThinkAnswer, fullContentFuture, decisionFuture), () -> {
            handleThinkingComplete(mockThinkAnswer, fullThinkAnswer, decisionFuture, fullContentFuture);
            // 耗时埋点
            MetricHelper.build().name("大模型耗时打点")
                    .tag("taskType", type)
                    .tag("metricType", "costTime")
                    .value(System.currentTimeMillis() - startTime);
        });
        return new ThinkingFlowResult(decisionFuture, fullContentFuture);
    }

    /**
     * 处理思考内容
     */
    private void handleThinkingContent(String content,
                                       StringBuilder mockThinkAnswer,
                                       StringBuilder fullThinkAnswer,
                                       CompletableFuture<String> decisionFuture,
                                       AtomicBoolean intercepted,
                                       ThinkProcessCardData thinkProcessCardData,
                                       MessageBuffer buffer) {

        fullThinkAnswer.append(content);

        if (!intercepted.get()) {
            if (shouldInterceptContent(content)) {
                handleContentInterception(content, mockThinkAnswer, decisionFuture,
                        intercepted, thinkProcessCardData, buffer);
            } else {
                appendNormalContent(content, mockThinkAnswer, thinkProcessCardData, buffer);
            }
        }
    }

    /**
     * 判断是否应该拦截内容
     */
    private boolean shouldInterceptContent(String content) {
        return content.contains("``") || content.contains("json") || content.contains("n[") || content.contains("[{") || (content.contains("[") && !content.contains("[]"));
    }

    /**
     * 处理内容拦截
     */
    private void handleContentInterception(String content,
                                           StringBuilder mockThinkAnswer,
                                           CompletableFuture<String> decisionFuture,
                                           AtomicBoolean intercepted,
                                           ThinkProcessCardData thinkProcessCardData,
                                           MessageBuffer buffer) {

        intercepted.set(true);
        content = content.replace("\n", "");

        int interceptIndex = findInterceptIndex(content);
        String needSendContent = content.substring(0, interceptIndex);
        String finalThinkAnswer = mockThinkAnswer.append(needSendContent).toString();

        decisionFuture.complete(finalThinkAnswer);
    }

    /**
     * 查找拦截索引位置
     */
    private int findInterceptIndex(String content) {
        int backtickIndex = content.indexOf("`");
        int bracketIndex = Math.max(content.indexOf("[{"), content.indexOf("["));

        if (backtickIndex != -1 && bracketIndex != -1) {
            return Math.min(backtickIndex, bracketIndex);
        } else if (backtickIndex != -1) {
            return backtickIndex;
        } else {
            return bracketIndex;
        }
    }

    /**
     * 添加正常内容
     */
    private void appendNormalContent(String content,
                                     StringBuilder mockThinkAnswer,
                                     ThinkProcessCardData thinkProcessCardData,
                                     MessageBuffer buffer) {

        mockThinkAnswer.append(content);
        thinkProcessCardData.setContent(mockThinkAnswer.toString());
        thinkProcessCardData.setLoadingText(THINKING_RESULT_GENERATE_STATUS);
        if (StringUtils.isNotBlank(content) && buffer != null) {
            writeThinkingProcessBuffer(thinkProcessCardData, buffer);
        }
    }

    /**
     * 写入思考过程缓冲区
     */
    private void writeThinkingProcessBuffer(ThinkProcessCardData thinkProcessCardData, MessageBuffer buffer) {
        BufferUtils.writeThinkingProcessStreamBuffer(
                MessageBufferEntity.builder()
                        .data(StreamEventCardTypeEnum.buildCardContent(
                                StreamEventCardTypeEnum.THINK_PROCESS_STREAM,
                                UUID.randomUUID().toString().replace("-", "")
                        ))
                        .type(BufferItemTypeEnum.THINK_PROCESS_STREAM.getType())
                        .extra(ImmutableMap.of("thinkProcessData", Lists.newArrayList(thinkProcessCardData)))
                        .build(),
                buffer
        );
    }

    /**
     * 处理思考错误
     */
    private void handleThinkingError(Throwable error,
                                     StringBuilder fullThinkAnswer,
                                     CompletableFuture<String> fullContentFuture,
                                     CompletableFuture<String> decisionFuture) {

        log.error("思考流程异常", error);
        fullContentFuture.complete(fullThinkAnswer.toString());
        decisionFuture.completeExceptionally(error);
    }

    /**
     * 处理思考完成
     */
    private void handleThinkingComplete(StringBuilder mockThinkAnswer,
                                        StringBuilder fullThinkAnswer,
                                        CompletableFuture<String> decisionFuture,
                                        CompletableFuture<String> fullContentFuture) {

        log.info("思考流程完成，完整内容: {}", fullThinkAnswer.toString());

        if (!decisionFuture.isDone()) {
            decisionFuture.complete(mockThinkAnswer.toString());
        }
        fullContentFuture.complete(fullThinkAnswer.toString());
    }

    /**
     * 异步处理推荐数据
     */
    private CompletableFuture<List<Object>> processRecommendationsAsync(List<DecisionTreeModel> decisionTreeModels, TaskContext context, List<DecisionTreeModel> ragDecisionTreeRagModels, String bizType) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                // 等待完整内容完成
                List<Object> res = new CopyOnWriteArrayList<>();
                processRecommendations(decisionTreeModels, context, res, ragDecisionTreeRagModels, bizType);
                return res;
            } catch (Exception e) {
                log.error("处理推荐数据异常", e);
                return Collections.emptyList();
            }
        }, SEARCH_POOL.getExecutor());
    }

    /**
     * 处理推荐数据
     */
    private void processRecommendations(List<DecisionTreeModel> decisionTreeModelList, TaskContext context, List<Object> res, List<DecisionTreeModel> ragDecisionTreeRagModels, String bizType) {
//        String extraJson = extractJsonFromContent(fullContent);
//        log.info("query={},decisionJson: {}", context.getMessageContext().getMsg(), extraJson);
//        List<DecisionTreeModel> decisionTreeModelList = parseDecisionTreeModels(extraJson);

        log.info("query={},[decisionJson]tree={}", context.getMessageContext().getMsg(), JsonUtils.toJsonString(decisionTreeModelList));
        context.getMessageContext().getExtra().put("recommendDecisionTree", JsonUtils.toJsonString(decisionTreeModelList));
        context.getMessageContext().getExtra().put("ragInfo", JsonUtils.toJsonString(ragDecisionTreeRagModels));
        if (CollectionUtils.isEmpty(decisionTreeModelList) || decisionTreeModelList.size() != 1) {
            return;
        }

        DecisionTreeModel treeModel = decisionTreeModelList.get(0);
        if (treeModel == null || !Boolean.TRUE.equals(treeModel.getNeedSupply())) {
            return;
        }
        Map<String, String> supplyName2Id = new HashMap<>();
        if (CollectionUtils.isNotEmpty(ragDecisionTreeRagModels) && CollectionUtils.isNotEmpty(ragDecisionTreeRagModels.get(0).getDrillDownRequirements())) {
            try {
                supplyName2Id = ragDecisionTreeRagModels.get(0).getDrillDownRequirements().stream()
                        .filter(e -> e.getSupplyRecommendModel() != null)
                        .collect(Collectors.toMap(
                                e -> Optional.ofNullable(e.getSupplyRecommendModel().getSupplyName()).orElse(""),
                                e -> Optional.ofNullable(e.getSupplyRecommendModel().getSupplyId()).orElse(""),
                                (a, b) -> a
                        ));
            } catch (Exception e) {
                log.error("query={},ragDecisionTreeRagModels={}", context.getMessageContext().getMsg(), JsonUtils.toJsonString(ragDecisionTreeRagModels), e);
            }
        }
        log.info("query={},ragDecisionTreeRagModels={}", context.getMessageContext().getMsg(), JsonUtils.toJsonString(ragDecisionTreeRagModels));


        processDrillDownRequirements(treeModel, context, res, supplyName2Id, bizType);
    }

    /**
     * 解析决策树模型列表
     */
    private List<DecisionTreeModel> parseDecisionTreeModels(String extraJson) {
        try {
            return JSON.parseArray(extraJson, DecisionTreeModel.class);
        } catch (Exception e) {
            log.error("解析决策树模型失败: {}", extraJson, e);
            return Lists.newArrayList();
        }
    }

    /**
     * 处理下钻需求
     */
    private void processDrillDownRequirements(DecisionTreeModel treeModel, TaskContext context, List<Object> res, Map<String, String> supplyName2Id, String bizType) {
        if (CollectionUtils.isEmpty(treeModel.getDrillDownRequirements())) {
            return;
        }

        List<CompletableFuture<Void>> futures = new ArrayList<>();

        treeModel.getDrillDownRequirements().stream()
                .filter(requirement -> requirement.getSupplyRecommendModel() != null)
                .forEach(requirement -> {
                    SupplyRecommendModel supplyModel = requirement.getSupplyRecommendModel();
                    if (BizSceneEnum.MOUTH_CONSULT.getBizScene().equals(bizType)) {
                        supplyModel.setSupplyType(SupplyRecommendModel.SupplyType.SHOP.getCode());
                    }
                    if (supplyName2Id.containsKey(supplyModel.getSupplyName()) && (StringUtils.isBlank(supplyModel.getSupplyId()) ||
                            !supplyModel.getSupplyId().startsWith("A"))) {
                        supplyModel.setSupplyId(supplyName2Id.get(supplyModel.getSupplyName()));
                    }
                    Pair<Double, Double> positionPair = parsePosition(supplyModel, getCurrentBasicParam(context));

                    if ("doctor".equals(supplyModel.getSupplyType())) {
                        CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                            DoctorProductCardData doctorProductCardData = new DoctorProductCardData();
                            queryDoctorRecommendations(supplyModel, positionPair, context, doctorProductCardData, requirement.getNodeName());
                            res.add(doctorProductCardData);
                        }, PRODUCT_POOL.getExecutor());
                        futures.add(future);

                    } else {
                        CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                            ShopProductCardData shopProductCardData = new ShopProductCardData();
                            queryShopRecommendations(supplyModel, positionPair, context, shopProductCardData, requirement.getNodeName());
                            res.add(shopProductCardData);
                        }, PRODUCT_POOL.getExecutor());
                        futures.add(future);
                    }
                });

        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
    }

    /**
     * 获取当前基础参数
     */
    private BasicParam getCurrentBasicParam(TaskContext context) {
        // 从当前上下文获取基础参数的逻辑
        // 这里需要根据实际情况实现
        return context.getMessageContext().getBasicParam();
    }

    private void processDoctorPosition(BasicParam basicParam, SupplyRecommendModel supplyModel, DoctorProductCardData doctorProductCardData) {
        Integer userCityId = basicParam.getUserCityId();
        doctorProductCardData.setCityId(NumberUtils.toInt(supplyModel.getCityId()));
        doctorProductCardData.setIsCurrentLocation(StringUtils.isBlank(supplyModel.getCityId()) || userCityId.equals(NumberUtils.toInt(supplyModel.getCityId())));
        doctorProductCardData.setLat(basicParam.getLat());
        doctorProductCardData.setLng(basicParam.getLng());
        if (StringUtils.isNotBlank(supplyModel.getLngLat())) {
            String[] split = supplyModel.getLngLat().split(",");
            if (split.length == 2) {
                doctorProductCardData.setLng(NumberUtils.toDouble(split[0]));
                doctorProductCardData.setLat(NumberUtils.toDouble(split[1]));
            }
        }
    }

    private void processShopPosition(BasicParam basicParam, SupplyRecommendModel supplyModel, ShopProductCardData shopProductCardData) {
        Integer userCityId = basicParam.getUserCityId();
        shopProductCardData.setCityId(NumberUtils.toInt(supplyModel.getCityId()));
        shopProductCardData.setIsCurrentLocation(StringUtils.isBlank(supplyModel.getCityId()) || userCityId.equals(NumberUtils.toInt(supplyModel.getCityId())));
        shopProductCardData.setLat(basicParam.getLat());
        shopProductCardData.setLng(basicParam.getLng());
        if (StringUtils.isNotBlank(supplyModel.getLngLat())) {
            String[] split = supplyModel.getLngLat().split(",");
            if (split.length == 2) {
                shopProductCardData.setLng(NumberUtils.toDouble(split[0]));
                shopProductCardData.setLat(NumberUtils.toDouble(split[1]));
            }
        }
    }


    /**
     * 查询医生推荐
     */
    private void queryDoctorRecommendations(SupplyRecommendModel supplyModel, Pair<Double, Double> positionPair, TaskContext context, DoctorProductCardData doctorProductCardData, String nodeName) {
        try {
            MessageContext messageContext = context.getMessageContext();
            log.info("query={},<queryDoctorRecommendations>: {}", messageContext.getMsg(), JsonUtils.toJsonString(supplyModel));
            DoctorRecommendListRecallRequest doctorRequest = new DoctorRecommendListRecallRequest();
            buildDoctorRequest(doctorRequest, supplyModel, getCurrentMessageContext(context), positionPair);

            DoctorCardInfoAndCaseBatchQueryResponse recommendListResponse =
                    doctorInfoQueryService.queryDoctorRecommendList(doctorRequest);

            processDoctorPosition(messageContext.getBasicParam(), supplyModel, doctorProductCardData);
            context.getMessageContext().getExtra().put(nodeName + "-searchDoctorReq", JsonUtils.toJsonString(doctorRequest));
            context.getMessageContext().getExtra().put(nodeName + "-searchDoctorRes", JsonUtils.toJsonString(recommendListResponse));
            updateSearchRequestCount(context);

            log.info("query={},<MedicalProductTask>queryDoctorRecommendations_DoctorCardInfoAndCaseBatchQueryResponse: {}, req={}",
                    messageContext.getMsg(),
                    JsonUtils.toJsonString(recommendListResponse), JsonUtils.toJsonString(doctorRequest));

            if (recommendListResponse.getSuccess() && CollectionUtils.isNotEmpty(recommendListResponse.getData())) {
                updateSearchRequestSuccessCount(context);
                List<Long> mergeDoctorIds = recommendListResponse.getData().stream().filter(Objects::nonNull).map(DoctorCardInfoAndCase::getMergeDoctorId).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(mergeDoctorIds) && mergeDoctorIds.size() >= 2) {
                    doctorProductCardData.setNeedMore(true);
                }
                List<DoctorRecommendation> doctorRecommendations = new ArrayList<>();
                String nodeDesc = StringUtils.isNotBlank(supplyModel.getSupplySearchWord()) ? supplyModel.getSupplySearchWord() :
                        Optional.ofNullable(supplyModel.getSupplyName()).orElse("");
                try {
                    doctorRecommendations = recommendService.selectDoctorAndRecommend(mergeDoctorIds, messageContext, nodeDesc);
                } catch (Exception e) {
                    log.error("recommendService.selectDoctorAndRecommend, req={},nodeDesc={}", JsonUtils.toJsonString(mergeDoctorIds), nodeDesc, e);
                }
                messageContext.getExtra().put(nodeName + "-recommendDoctorReq", JsonUtils.toJsonString(mergeDoctorIds) + "-" + nodeDesc);
                messageContext.getExtra().put(nodeName + "-recommendDoctorRes", JsonUtils.toJsonString(doctorRecommendations));

                log.info("query={},<MedicalProductTask>queryDoctorRecommendations_doctorRecommendations={},nodeDesc={}", messageContext.getMsg(),
                        JsonUtils.toJsonString(doctorRecommendations), nodeDesc);

                if (CollectionUtils.isNotEmpty(doctorRecommendations)) {
                    DoctorRecommendation doctorRecommendation = doctorRecommendations.get(0);
                    doctorProductCardData.setReason(doctorRecommendation.getReason());

                    supplyModel.setTopId(String.valueOf(doctorRecommendation.getDoctorId()));
                    DoctorProductSimpleData simpleData = new DoctorProductSimpleData();
                    simpleData.setName(doctorRecommendation.getName());
                    String finalPrice = formatPrice(doctorRecommendation.getPromoPrice());
                    simpleData.setPromoPrice(finalPrice);
                    simpleData.setDetailLink(doctorRecommendation.getDetailLink());
                    simpleData.setSaleNum("已售" + doctorRecommendation.getSaleNum());
                    simpleData.setProductId(NumberUtils.toLong(UrlUtils.getParamValue(simpleData.getDetailLink(), "productid")));
                    doctorProductCardData.setProduct(simpleData);
                    doctorProductCardData.setReasonPrefix(RECOMMEND_REASON);

                    if (CollectionUtils.isNotEmpty(doctorRecommendation.getReviewURL()) && CollectionUtils.isNotEmpty(doctorRecommendation.getReviewContext())) {
                        List<String> reviewUrls = doctorRecommendation.getReviewURL();
                        List<String> reviewContents = doctorRecommendation.getReviewContext();
                        doctorProductCardData.setCount((long) reviewUrls.size());


                        List<ReferData> refer = new ArrayList<>();
                        for (int i = 0; i < reviewUrls.size(); i++) {
                            ReferData referData = new ReferData();
                            referData.setUrl(reviewUrls.get(i));
                            referData.setContent(reviewContents.get(i));
                            referData.setSource("review");
                            referData.setHeadPic(DEFAULT_HEAD_PIC);
                            refer.add(referData);
                        }
                        doctorProductCardData.setRefer(refer);
                    }

                    DoctorCardInfoAndCase doctorCardInfoAndCase = recommendListResponse.getData().stream().filter(e -> Objects.equals(e.getMergeDoctorId(), doctorRecommendation.getDoctorId())).findFirst().orElse(null);
                    if (doctorCardInfoAndCase == null) {
                        throw new IllegalArgumentException("医生获取失败");
                    }
                    {
                        processDoctorCase(doctorCardInfoAndCase, doctorProductCardData);
                        supplyModel.setDoctorCardInfoAndCase(doctorCardInfoAndCase);
                        supplyModel.getDoctorCardInfoAndCase().setStatisticalDataList(null);
                        supplyModel.getDoctorCardInfoAndCase().setDoctorCaseList(null);
                    }
                    doctorProductCardData.setDetail(doctorCardInfoAndCase);

                    //todo 案例待补充
                    if (StringUtils.isNotBlank(supplyModel.getMedicalCaseTag())) {
                        List<Long> tagIds = Arrays.stream(supplyModel.getMedicalCaseTag().split(","))
                                .map(String::trim)
                                .filter(s -> !s.isEmpty() && NumberUtils.isDigits(s))
                                .map(Long::parseLong)
                                .distinct()
                                .collect(Collectors.toList());
                        ExperienceReportRequestModel requestModel = ExperienceReportRequestModel
                                .builder()
                                .mergeDoctorId(doctorRecommendation.getDoctorId())
                                .aiRecommendTags(tagIds)
                                .platform(messageContext.getPlatform())
                                .type(FilterType.AI_CONSULTANT.getCode())
                                .offset(0)
                                .limit(5)
                                .build();
                        try {
                            AiExperienceReports aiExperienceReportListInfo = experienceReportDomainService.getAiExperienceReportListInfo(requestModel);
                            messageContext.getExtra().put(nodeName + "-recommendCaseReq", JsonUtils.toJsonString(requestModel));
                            messageContext.getExtra().put(nodeName + "-recommendCaseRes", JsonUtils.toJsonString(aiExperienceReportListInfo));

                            log.info("query={},report,req={},res={},", messageContext.getMsg(), JsonUtils.toJsonString(requestModel), JsonUtils.toJsonString(aiExperienceReportListInfo));
                            CaseCardData caseCardData = new CaseCardData();

                            if (aiExperienceReportListInfo != null) {
                                caseCardData.setCaseInfoList(aiExperienceReportListInfo.getCaseList());
                                caseCardData.setCount(Optional.ofNullable(aiExperienceReportListInfo.getTotalCount()).map(Long::valueOf).orElse(0L));
                                caseCardData.setTitle(aiExperienceReportListInfo.getTitle());
                                caseCardData.setJumpUrl(aiExperienceReportListInfo.getDetailUrl());
                                doctorProductCardData.setReport(caseCardData);
                            }


                        } catch (Exception e) {
                            log.error("requestModel={}", JsonUtils.toJsonString(requestModel), e);
                        }
                    }
                    messageContext.getExtra().put(nodeName + "-recommendDoctorCard", JsonUtils.toJsonString(doctorProductCardData));

                    log.info("query={},<MedicalProductTask>queryDoctorRecommendations_doctorProductCardData: {}", messageContext.getMsg(),
                            JsonUtils.toJsonString(doctorProductCardData));


                }
            }
            doctorProductCardData.setFilterList(Lists.newArrayList(supplyModel));


        } catch (Exception e) {
            log.error("查询医生推荐失败", e);
            throw new IllegalArgumentException("医生获取失败");
        }
    }

    private void processDoctorCase(DoctorCardInfoAndCase doctorCardInfoAndCase, DoctorProductCardData doctorProductCardData) {
        if (doctorCardInfoAndCase != null && doctorCardInfoAndCase.getShopInfo() != null &&
                !doctorProductCardData.getIsCurrentLocation()) {
            doctorCardInfoAndCase.getShopInfo().setDistance(StringUtils.EMPTY);
        }
    }

    // 更新搜索调用次数
    private void updateSearchRequestSuccessCount(TaskContext context) {
        context.getMessageContext().getSearchRequestSuccessCount().incrementAndGet();
    }

    // 更新搜索成功次数
    private void updateSearchRequestCount(TaskContext context) {
        context.getMessageContext().getSearchRequestCount().incrementAndGet();
    }

    private String getAnchorUrl(String jumpUrl, String productUrl, String keyword) {
        StringBuilder builder = new StringBuilder(jumpUrl);
        JSONObject jsonObject = new JSONObject();
        String productId = UrlUtils.getParamValue(productUrl, "productid");
        if (StringUtils.isNotBlank(productId)) {
            jsonObject.put("spu", productId);
        } else {
            productId = UrlUtils.getParamValue(productUrl, "did");
            if (StringUtils.isNotBlank(productId)) {
                jsonObject.put("did", productId);
            }
        }

        if (StringUtils.isNotBlank(jumpUrl) && StringUtils.isNotBlank(productId)) {
            builder.append("&").append("summarypids=").append((URLEncoder.encode(jsonObject.toString(), StandardCharsets.UTF_8)));
            builder.append("&").append("anchorgoodflag=").append(1);
            builder.append("&").append("anchorgoodid=").append(productId);
            builder.append("&").append("anchor=").append("deal_shelf");
            builder.append("&").append("keyword=").append(URLEncoder.encode(keyword, StandardCharsets.UTF_8));
            return builder.toString();
        }
        return builder.toString();

    }


    /**
     * 查询商户推荐
     */
    private void queryShopRecommendations(SupplyRecommendModel supplyModel, Pair<Double, Double> positionPair, TaskContext context, ShopProductCardData shopProductCardData, String nodeName) {
        try {
            log.info("query={},queryShopRecommendations:{}", context.getMessageContext().getMsg(), JsonUtils.toJsonString(supplyModel));
            ReCallSortIdsQry reCallSortIdsQry = new ReCallSortIdsQry();
            buildReCallRequest(reCallSortIdsQry, supplyModel, getCurrentMessageContext(context), positionPair);

            shopProductCardData.setFilterList(Lists.newArrayList(supplyModel));

            processShopPosition(getCurrentBasicParam(context), supplyModel, shopProductCardData);

            ResponseDTO<ReCallSortIdsDTO> response = listingFacade.reCallSortIds(reCallSortIdsQry);
            context.getMessageContext().getExtra().put(nodeName + "-searchReq", JsonUtils.toJsonString(reCallSortIdsQry));
            context.getMessageContext().getExtra().put(nodeName + "-searchRes", JsonUtils.toJsonString(response));
            updateSearchRequestCount(context);
            log.info("query={},[MedicalProductTask]queryShopRecommendations_Response={}, req={}", context.getMessageContext().getMsg(), JsonUtils.toJsonString(response), JsonUtils.toJsonString(reCallSortIdsQry));
            if (response.isSuccess() && response.getData() != null && CollectionUtils.isNotEmpty(response.getData().getShopGoodsRecallDTOs())) {
                updateSearchRequestSuccessCount(context);
                List<ShopGoodsRecallDTO> shopGoodsRecallDTOs = response.getData().getShopGoodsRecallDTOs();
                shopProductCardData.setNeedMore((long) shopGoodsRecallDTOs.size() >= 2);

                String recommendDesc = StringUtils.isNotBlank(supplyModel.getSupplySearchWord()) ?
                        supplyModel.getSupplySearchWord() :
                        supplyModel.getSupplyName();
                context.getMessageContext().getExtra().put(nodeName + "-recommendShopOriginReq", JsonUtils.toJsonString(shopGoodsRecallDTOs));
                List<ShopRecommendation> shopRecommendations = new ArrayList<>();
                try {
                    shopRecommendations = recommendService.selectShopAndRecommend(shopGoodsRecallDTOs, context.getMessageContext(), recommendDesc, supplyModel.getSupplyName());
                } catch (Exception e) {
                    log.error("recommendService.selectShopAndRecommend error, req={}, desc={}", JsonUtils.toJsonString(shopGoodsRecallDTOs), recommendDesc, e);
                }
                context.getMessageContext().getExtra().put(nodeName + "-recommendShopAfterReq", JsonUtils.toJsonString(shopGoodsRecallDTOs));
                context.getMessageContext().getExtra().put(nodeName + "-recommendShopRes", JsonUtils.toJsonString(shopRecommendations));
                log.info("query={},[MedicalProductTask]queryShopRecommendations_shopRecommendations={},desc={},req={}", context.getMessageContext().getMsg(),
                        JsonUtils.toJsonString(shopRecommendations), recommendDesc, JsonUtils.toJsonString(shopGoodsRecallDTOs));
                if (CollectionUtils.isNotEmpty(shopRecommendations)) {
                    ShopRecommendation recommendation = shopRecommendations.get(0);
                    supplyModel.setTopId(String.valueOf(recommendation.getShopId()));
                    supplyModel.setGoodsId(String.valueOf(recommendation.getProductId()));
                    if (StringUtils.isNotBlank(recommendation.getReason())) {
                        shopProductCardData.setReason(recommendation.getReason());
                        shopProductCardData.setReasonPrefix(RECOMMEND_REASON);
                    }
                    if (CollectionUtils.isNotEmpty(recommendation.getReviewIds()) && CollectionUtils.isNotEmpty(recommendation.getReviewContext())) {
                        List<ReferData> refer = new ArrayList<>();
                        List<String> reviewUrls = recommendation.getReviewUrl();
                        List<String> reviewContents = recommendation.getReviewContext();
                        for (int i = 0; i < reviewUrls.size(); i++) {
                            ReferData referData = new ReferData();
                            referData.setUrl(reviewUrls.get(i));
                            referData.setContent(reviewContents.get(i));
                            referData.setSource("review");
                            referData.setHeadPic(DEFAULT_HEAD_PIC);
                            refer.add(referData);
                        }
                        shopProductCardData.setRefer(refer);
                        shopProductCardData.setCount((long) reviewUrls.size());
                    }
                    ShopGoodsRecallDTO shopGoodsRecallDTO = shopGoodsRecallDTOs.stream().filter(e -> e.getShopId().equals(recommendation.getShopId())).findFirst().orElse(null);
                    if (shopGoodsRecallDTO == null) {
                        shopGoodsRecallDTO = shopGoodsRecallDTOs.get(0);
                    }
                    FillInfoQry fillInfoQry = new FillInfoQry();
                    buildFillInfoQuery(fillInfoQry, context.getMessageContext(), positionPair, shopGoodsRecallDTO, recommendation.getProductId(), supplyModel);
                    ResponseDTO<FillInfoDTO> fillInfoDTOResponseDTO = listingFacade.fillInfo(fillInfoQry);
                    context.getMessageContext().getExtra().put(nodeName + "-recommendShopFillReq", JsonUtils.toJsonString(fillInfoQry));
                    context.getMessageContext().getExtra().put(nodeName + "-recommendShopFillRes", JsonUtils.toJsonString(fillInfoDTOResponseDTO));
                    log.info("query={}, fillInfoDTOResponseDTO={}, req={}", context.getMessageContext().getMsg(), JsonUtils.toJsonString(fillInfoDTOResponseDTO), JsonUtils.toJsonString(fillInfoQry));
                    if (CollectionUtils.isNotEmpty(shopGoodsRecallDTO.getShopGoodsRecallDTOs())) {
                        supplyModel.setGoodsType(shopGoodsRecallDTO.getShopGoodsRecallDTOs().get(0).getGoodsType());
                        supplyModel.setGoodsId((recommendation.getProductId() == null || recommendation.getProductId() <= 0) ?
                                String.valueOf(shopGoodsRecallDTO.getShopGoodsRecallDTOs().get(0).getGoodsId()) :
                                String.valueOf(recommendation.getProductId()));
                    }

                    if (fillInfoDTOResponseDTO.isSuccess() && fillInfoDTOResponseDTO.getData() != null) {
                        FillInfoDTO fillInfoDTO = fillInfoDTOResponseDTO.getData();
                        if (CollectionUtils.isNotEmpty(fillInfoDTO.getGoodsInfoDTOs())) {
                            final String[] productUrl = new String[1];
                            fillInfoDTO.getGoodsInfoDTOs().forEach(goodsInfoDTO -> {
                                String finalPrice = formatPrice(goodsInfoDTO.getFinalPrice());
                                productUrl[0] = goodsInfoDTO.getGoodsRedirectUrl();
                                goodsInfoDTO.setFinalPrice(finalPrice);
                            });
                            fillInfoDTO.getShopInfos().forEach(shopInfoDTO -> {
                                shopInfoDTO.setShopJumpUrl(getAnchorUrl(shopInfoDTO.getShopJumpUrl(), productUrl[0], supplyModel.getSupplySearchWord()));
                                // 处理距离字符串，如果≥30km则置空，否则添加"米"后缀
                                String processedDistance = processDistance(shopInfoDTO.getShopDistance());
                                shopInfoDTO.setShopDistance(processedDistance);
                            });
                        }

                        shopProductCardData.setDetail(fillInfoDTO);
                    }
                    //todo 体验报告
                    if (StringUtils.isNotBlank(supplyModel.getMedicalCaseTag())) {
                        List<Long> tagIds = Arrays.stream(supplyModel.getMedicalCaseTag().split(","))
                                .map(String::trim)
                                .filter(s -> !s.isEmpty() && NumberUtils.isDigits(s))
                                .map(Long::parseLong)
                                .distinct()
                                .collect(Collectors.toList());
                        ExperienceReportRequestModel requestModel = ExperienceReportRequestModel
                                .builder()
                                .shopId(recommendation.getShopId())
                                .aiRecommendTags(tagIds)
                                .platform(context.getMessageContext().getPlatform())
                                .type(FilterType.AI_CONSULTANT.getCode())
                                .offset(0)
                                .limit(5)
                                .build();
                        try {
                            AiExperienceReports aiExperienceReportListInfo = experienceReportDomainService.getAiExperienceReportListInfo(requestModel);
                            context.getMessageContext().getExtra().put(nodeName + "-recommendCaseReq", JsonUtils.toJsonString(requestModel));
                            context.getMessageContext().getExtra().put(nodeName + "-recommendCaseRes", JsonUtils.toJsonString(aiExperienceReportListInfo));
                            log.info("shopReport,req={},res={},", JsonUtils.toJsonString(requestModel), JsonUtils.toJsonString(aiExperienceReportListInfo));
                            CaseCardData caseCardData = new CaseCardData();

                            if (aiExperienceReportListInfo != null) {
                                caseCardData.setCaseInfoList(aiExperienceReportListInfo.getCaseList());
                                caseCardData.setCount(Optional.ofNullable(aiExperienceReportListInfo.getTotalCount()).map(Long::valueOf).orElse(0L));
                                caseCardData.setTitle(aiExperienceReportListInfo.getTitle());
                                caseCardData.setJumpUrl(aiExperienceReportListInfo.getDetailUrl());
                                shopProductCardData.setReport(caseCardData);
                            }


                        } catch (Exception e) {
                            log.error("query={}, requestModel={}", context.getMessageContext().getMsg(), JsonUtils.toJsonString(requestModel), e);
                        }
                    }
                    context.getMessageContext().getExtra().put(nodeName + "-cardData", JsonUtils.toJsonString(shopProductCardData));

                    log.info("query={},[MedicalProductTask]queryShopRecommendations_shopProductCardData{}", context.getMessageContext().getMsg(),
                            JsonUtils.toJsonString(shopProductCardData));
                }


            }
            shopProductCardData.setFilterList(Lists.newArrayList(supplyModel));


        } catch (Exception e) {
            log.error("查询商户推荐失败", e);
            throw e;
        }
    }

    /**
     * 获取当前消息上下文
     */
    private MessageContext getCurrentMessageContext(TaskContext taskContext) {
        // 从当前上下文获取消息上下文的逻辑
        // 这里需要根据实际情况实现
        return taskContext.getMessageContext();
    }

    /**
     * 生成最终答案
     */
    private String generateFinalAnswer(TaskContext context, String userQuery, String decisionText, String decisionRagStr, String historyDecisionStr, String decisionFilter) {
        TaskConfig medicalDecisionConfig = getTaskConfig();
        context.setTaskConfig(medicalDecisionConfig);

        String finalPrompt = buildFinalPrompt(medicalDecisionConfig, decisionRagStr,
                context.getMessageContext(), decisionText, historyDecisionStr, decisionFilter);

        if (MapUtils.isNotEmpty(context.getMessageContext().getExtra())) {
            String qaAnswer = (String) context.getMessageContext().getExtra().get(ContextExtraKey.PRODUCT_QA.getKey());
            finalPrompt = finalPrompt + "\n\n已有问答知识:\n\n" + qaAnswer;
        }

        context.getTaskConfig().setSystemPrompt(finalPrompt);


        context.getTaskConfig().setUserPrompt(userQuery);

        log.info("query={},[medicalDecision]prompt={}", context.getMessageContext().getMsg(), finalPrompt);

        return getAnswer(context);
    }

    /**
     * 获取任务配置
     */
    private TaskConfig getTaskConfig() {
        TaskConfig config = haimaAIUtils.getAIConfig(DECISION_MODEL_CONFIG_KEY);
        if (config == null) {
            throw new IllegalArgumentException("策略模型配置为空");
        }
        return config;
    }

    /**
     * 构建最终提示词
     */
    private String buildFinalPrompt(TaskConfig config, String decisionRagStr,
                                    MessageContext messageContext, String decisionText, String historyDecisionStr, String decisionFilter) {

        String systemPrompt = config.getSystemPrompt();
        String skinReportId = (String) messageContext.getExtra().get(ContextExtraKey.SKIN_REPORT_ID.getKey());
        return systemPrompt.replace("{{{filter}}}", decisionFilter)
                .replace("{{{originDecisions}}}", decisionRagStr)
                .replace("{{{historyDecisions}}}", historyDecisionStr)
                .replace("{{{userPersona}}}",
                        Optional.ofNullable(messageContext.getMemoryModel())
                                .map(e -> JsonUtils.toJsonString(e.getMemoryList()))
                                .orElse(""))
                .replace("{{{curDecisionDesc}}}", decisionText)
                .replace("{{{skinReport}}}", StringUtils.isBlank(skinReportId) ? "false" : "true")
                .replace("{{{bizType}}}", BizSceneEnum.MOUTH_CONSULT.getBizScene().equals(messageContext.getBizType()) ? "口腔" : "医美");
    }

    /**
     * 保存决策数据
     */
    private void saveDecisionData(String fullContent) {
        String decisionJson = extractJsonFromContent(fullContent);
        log.info("<decisionJson>{}", decisionJson);
        RequestContext.setAttribute(RequestContextConstant.DECISION_DATA, decisionJson);
    }


    /**
     * 解析位置信息
     */
    private Pair<Double, Double> parsePosition(SupplyRecommendModel supplyRecommendModel, BasicParam basicParam) {
        // 1. 从supplyRecommendModel中获取经纬度
        Pair<Double, Double> positionFromModel = parsePositionFromModel(supplyRecommendModel);
        if (positionFromModel != null) {
            return positionFromModel;
        }

        // 2. 从basicParam中获取经纬度
        Pair<Double, Double> positionFromParam = parsePositionFromParam(basicParam);
        if (positionFromParam != null) {
            return positionFromParam;
        }

        log.warn("无法从任何来源获取有效的经纬度信息");
        return null;
    }

    /**
     * 从供应推荐模型中解析位置
     */
    private Pair<Double, Double> parsePositionFromModel(SupplyRecommendModel supplyRecommendModel) {
        if (supplyRecommendModel == null || StringUtils.isBlank(supplyRecommendModel.getLngLat())) {
            return null;
        }

        try {
            String[] lngLatArray = supplyRecommendModel.getLngLat().split(",");
            if (lngLatArray.length == 2) {
                double lng = Double.parseDouble(lngLatArray[0]);
                double lat = Double.parseDouble(lngLatArray[1]);
                log.info("从supplyRecommendModel获取经纬度成功:lng={},lat={}", lng, lat);
                return new Pair<>(lng, lat);
            }
        } catch (Exception e) {
            log.error("解析supplyRecommendModel经纬度失败: {}", supplyRecommendModel.getLngLat(), e);
        }

        return null;
    }

    /**
     * 从基础参数中解析位置
     */
    private Pair<Double, Double> parsePositionFromParam(BasicParam basicParam) {
        if (basicParam == null) {
            return null;
        }

        Double lng = basicParam.getLng();
        Double lat = basicParam.getLat();

        if (lng != null && lat != null) {
            log.info("从basicParam获取经纬度成功: lng={}, lat={}", lng, lat);
            return new Pair<>(lng, lat);
        }

        return null;
    }

    private void buildFillInfoQuery(FillInfoQry fillInfoQry, MessageContext messageContext,
                                    Pair<Double, Double> positionPair, ShopGoodsRecallDTO shopGoodsRecallDTO, Long productId, SupplyRecommendModel supplyRecommendModel) {
        String templateKey = Lion.getString(Environment.getAppName(),
                "health.recommend.list.template.key", "ListingShow");
        com.sankuai.medicalcosmetology.mainpath.listingapi.request.ShopQry shopQry = new com.sankuai.medicalcosmetology.mainpath.listingapi.request.ShopQry();
        shopQry.setShopId(shopGoodsRecallDTO.getShopId());
        fillInfoQry.setShopQryList(Lists.newArrayList(shopQry));

        GoodsRecallDTO goodsRecallDTO = shopGoodsRecallDTO.getShopGoodsRecallDTOs().stream().filter(e ->
                Objects.equals(e.getGoodsId(), productId)).findFirst().orElse(null);

        if (goodsRecallDTO == null) {
            goodsRecallDTO = shopGoodsRecallDTO.getShopGoodsRecallDTOs().get(0);
        }

        GoodsQry qry = new GoodsQry();
        qry.setGoodsId(goodsRecallDTO.getGoodsId());
        qry.setGoodsType(goodsRecallDTO.getGoodsType());
        GoodsShopQry goodsShopQry = new GoodsShopQry();
        goodsShopQry.setShopId(shopGoodsRecallDTO.getShopId());
        qry.setShopQry(goodsShopQry);

        fillInfoQry.setGoodsQryList(Lists.newArrayList(qry));


        BasicParam basicParam = messageContext.getBasicParam();
        fillInfoQry.setTemplateKey(templateKey);
        fillInfoQry.setUserId(basicParam.getUserId());
        fillInfoQry.setPlatform(messageContext.getPlatform());
        fillInfoQry.setCityId(basicParam.getCityId());
        if (NumberUtils.toInt(supplyRecommendModel.getCityId()) > 0) {
            fillInfoQry.setCityId(NumberUtils.toInt(supplyRecommendModel.getCityId()));
        }
        fillInfoQry.setAppVersion(basicParam.getAppVersion());
        fillInfoQry.setOs("ios".equals(basicParam.getClientType()) ? "ios" : "Android");
        fillInfoQry.setUuid(basicParam.getUuid());
        fillInfoQry.setLat(basicParam.getLat());
        fillInfoQry.setLng(basicParam.getLng());
    }

    /**
     * 构建商户查询请求
     */
    private void buildReCallRequest(ReCallSortIdsQry reCallSortIdsQry,
                                    SupplyRecommendModel supplyRecommendModel,
                                    MessageContext messageContext,
                                    Pair<Double, Double> positionPair) {


        String templateKey = Lion.getString(Environment.getAppName(),
                "health.recommend.list.template.key", "111");

        BasicParam basicParam = messageContext.getBasicParam();

        reCallSortIdsQry.setUserId(basicParam.getUserId());
        reCallSortIdsQry.setPlatform(messageContext.getPlatform());
        reCallSortIdsQry.setCityId(basicParam.getCityId());
        if (basicParam.getCityId() == null || basicParam.getCityId() <= 0) {
            reCallSortIdsQry.setCityId(basicParam.getUserCityId());
        }
        if (StringUtils.isNotBlank(supplyRecommendModel.getCityId()) && NumberUtils.toInt(supplyRecommendModel.getCityId()) > 0) {
            reCallSortIdsQry.setCityId(NumberUtils.toInt(supplyRecommendModel.getCityId(), 10));
        }
        String queryStr = StringUtils.isNotBlank(supplyRecommendModel.getSupplySearchWord()) ?
                supplyRecommendModel.getSupplySearchWord() :
                supplyRecommendModel.getSupplyName();
        reCallSortIdsQry.setQueryStr(queryStr);
        reCallSortIdsQry.setTemplateKey(templateKey);
        reCallSortIdsQry.setUuid(basicParam.getUuid());
        reCallSortIdsQry.setSortType(
                SupplyRecommendModel.ShopSortType.fromCode(supplyRecommendModel.getSortFilter()).getCode());
        reCallSortIdsQry.setLat(positionPair == null ? null : positionPair.getValue());
        reCallSortIdsQry.setLng(positionPair == null ? null : positionPair.getKey());
        // 如果shopTagFilter不包含中文字符且只包含数字、逗号、分号，则设置labels
        String shopTagFilter = supplyRecommendModel.getShopTagFilter();
        if (shopTagFilter != null && isValidTagFilter(shopTagFilter)) {
            reCallSortIdsQry.setLabels(shopTagFilter);
        }
        reCallSortIdsQry.setPoiBackCateId(supplyRecommendModel.getSupplyCate());
        reCallSortIdsQry.setPrices(supplyRecommendModel.getPriceRangeFilter());
        reCallSortIdsQry.setPageNum(RECOMMEND_PAGE_NUM);
        reCallSortIdsQry.setPageSize(recommendSize);
        if (supplyRecommendModel.getDistance() != null && supplyRecommendModel.getDistance() > 0) {
            reCallSortIdsQry.setDistance(String.valueOf(supplyRecommendModel.getDistance()));
        }
    }

    /**
     * 构建医生查询请求
     */
    private void buildDoctorRequest(DoctorRecommendListRecallRequest doctorRequest,
                                    SupplyRecommendModel supplyRecommendModel,
                                    MessageContext context,
                                    Pair<Double, Double> positionPair) {

        BasicParam basicParam = context.getBasicParam();

        doctorRequest.setCityId(basicParam.getCityId());
        if (StringUtils.isNotBlank(supplyRecommendModel.getCityId()) && NumberUtils.toInt(supplyRecommendModel.getCityId()) > 0) {
            doctorRequest.setCityId(NumberUtils.toInt(supplyRecommendModel.getCityId(), 10));
        }
        doctorRequest.setOffset(RECOMMEND_PAGE_NUM);
        doctorRequest.setLimit(recommendSize);
        doctorRequest.setPlatform(context.getPlatform());
        doctorRequest.setClientType(basicParam.getClientType());
        doctorRequest.setUserId(basicParam.getUserId());
        doctorRequest.setUuid(basicParam.getUuid());
        doctorRequest.setVersion(basicParam.getAppVersion());
        doctorRequest.setLat(basicParam.getLat());
        doctorRequest.setLng(basicParam.getLng());
        doctorRequest.setQuery(supplyRecommendModel.getSupplySearchWord());
        doctorRequest.setSortType(
                SupplyRecommendModel.DoctorSortType.fromDesc(supplyRecommendModel.getSortFilter()).getIntCode());
        doctorRequest.setFilters(Lists.newArrayList(
                supplyRecommendModel.getDoctorFilter() == null ?
                        DoctorRecommendFilterEnum.DEFAULT.getCode() :
                        DoctorRecommendFilterEnum.getByCode(supplyRecommendModel.getDoctorFilter()).getCode()));
    }

    /**
     * 从内容中提取JSON
     */
    private String extractJsonFromContent(String content) {
        int firstBraceIndex = Math.max(content.indexOf("["), content.indexOf("[{"));
        if (firstBraceIndex != -1) {
            return content.substring(firstBraceIndex).replace("json", "").replace("`", "");
        }
        return "[]";
    }

    @Override
    public void after(TaskProcessResult result) {
        // 后置处理逻辑
    }

    /**
     * 处理图片URL到ID的映射和供给ID映射
     * 提取决策树模型中的assessmentImg图片和supplyRecommendModel中的supplyId，为它们分配唯一ID，并保存到context中
     */
    private void processImageUrlMapping(List<DecisionTreeModel> memoryModelList,
                                        List<DecisionTreeModel> ragModelList,
                                        TaskContext context) {

        Map<String, String> pictureUrlMap = new HashMap<>();
        Map<String, String> supplyIdMap = new HashMap<>();
        int imageIdCounter = 1;
        int supplyIdCounter = 100; // 从100开始

        // 处理memory模型列表
//        if (CollectionUtils.isNotEmpty(memoryModelList)) {
//            for (DecisionTreeModel model : memoryModelList) {
//                imageIdCounter = processDecisionTreeModelImages(model, pictureUrlMap, imageIdCounter);
//            }
//        }

        // 处理RAG模型列表  
        if (CollectionUtils.isNotEmpty(ragModelList)) {
            for (DecisionTreeModel model : ragModelList) {
                imageIdCounter = processDecisionTreeModelImages(model, pictureUrlMap, imageIdCounter);
                supplyIdCounter = processDecisionTreeModelSupplyIds(model, supplyIdMap, supplyIdCounter);
            }
        }

        // 保存图片映射关系到context
        if (!pictureUrlMap.isEmpty()) {
            context.getMessageContext().getExtra().put(
                    ContextExtraKey.ASSESS_PICTURE_URLS.getKey(), JsonUtils.toJsonString(pictureUrlMap));
        }

        // 保存供给ID映射关系到context
        if (!supplyIdMap.isEmpty()) {
            context.getMessageContext().getExtra().put(
                    ContextExtraKey.ASSESS_SUPPLY_IDS.getKey(), JsonUtils.toJsonString(supplyIdMap));
        }
    }

    /**
     * 递归处理决策树模型中的图片
     *
     * @param model          决策树模型
     * @param pictureUrlMap  图片URL到ID的映射
     * @param imageIdCounter 图片ID计数器
     * @return 更新后的计数器值
     */
    private int processDecisionTreeModelImages(DecisionTreeModel model,
                                               Map<String, String> pictureUrlMap,
                                               int imageIdCounter) {
        if (model == null) {
            return imageIdCounter;
        }

        // 处理当前节点的assessmentImg
        if (StringUtils.isNotBlank(model.getAssessmentImg())) {
            String imageUrl = model.getAssessmentImg();

            String imageId = "pic" + String.valueOf(imageIdCounter++);
            pictureUrlMap.put(imageId, imageUrl);
            model.setAssessmentImg(imageId);


        }

        // 递归处理drillDownRequirements
        if (CollectionUtils.isNotEmpty(model.getDrillDownRequirements())) {
            for (DecisionTreeModel childModel : model.getDrillDownRequirements()) {
                imageIdCounter = processDecisionTreeModelImages(childModel, pictureUrlMap, imageIdCounter);
            }
        }

        return imageIdCounter;
    }

    /**
     * 递归处理决策树模型中的供给ID
     *
     * @param model           决策树模型
     * @param supplyIdMap     供给ID映射
     * @param supplyIdCounter 供给ID计数器
     * @return 更新后的计数器值
     */
    private int processDecisionTreeModelSupplyIds(DecisionTreeModel model,
                                                  Map<String, String> supplyIdMap,
                                                  int supplyIdCounter) {
        if (model == null) {
            return supplyIdCounter;
        }

        // 处理当前节点的supplyRecommendModel
        if (model.getSupplyRecommendModel() != null && StringUtils.isNotBlank(model.getSupplyRecommendModel().getSupplyId())) {
            String originalSupplyId = model.getSupplyRecommendModel().getSupplyId();

            if (StringUtils.isNotBlank(originalSupplyId)) {
                String newSupplyId = "A" + String.valueOf(supplyIdCounter++);
                supplyIdMap.put(newSupplyId, originalSupplyId);
                model.getSupplyRecommendModel().setSupplyId(newSupplyId);
            }
        }
        // 递归处理drillDownRequirements
        if (CollectionUtils.isNotEmpty(model.getDrillDownRequirements())) {
            for (DecisionTreeModel childModel : model.getDrillDownRequirements()) {
                supplyIdCounter = processDecisionTreeModelSupplyIds(childModel, supplyIdMap, supplyIdCounter);
            }
        }

        return supplyIdCounter;
    }


    /**
     * 思考流程结果内部类
     */
    public static class ThinkingFlowResult {
        private final CompletableFuture<String> decisionFuture;
        private final CompletableFuture<String> fullContentFuture;

        public ThinkingFlowResult(CompletableFuture<String> decisionFuture, CompletableFuture<String> fullContentFuture) {
            this.decisionFuture = decisionFuture;
            this.fullContentFuture = fullContentFuture;
        }

        public CompletableFuture<String> getDecisionFuture() {
            return decisionFuture;
        }

        public CompletableFuture<String> getFullContentFuture() {
            return fullContentFuture;
        }
    }

    private boolean isValidTagFilter(String str) {
        if (StringUtils.isBlank(str)) {
            return true;
        }
        return str.matches("[0-9,;^]+");
    }

    /**
     * 构建供给ID到卡片类型的映射
     *
     * @param decisionFilterTreeModels 决策过滤树模型列表
     * @return 供给ID到卡片类型的映射
     */
    private Map<String, Object> buildSupplyIdCardMap(List<DecisionTreeModel> decisionFilterTreeModels) {
        Map<String, Object> supplyIdCardMap = new HashMap<>();

        if (CollectionUtils.isEmpty(decisionFilterTreeModels)) {
            return supplyIdCardMap;
        }

        DecisionTreeModel firstModel = decisionFilterTreeModels.get(0);
        if (firstModel == null || CollectionUtils.isEmpty(firstModel.getDrillDownRequirements())) {
            return supplyIdCardMap;
        }

        firstModel.getDrillDownRequirements()
                .stream()
                .filter(requirement -> requirement.getSupplyRecommendModel() != null)
                .map(requirement -> requirement.getSupplyRecommendModel())
                .filter(supply -> StringUtils.isNotBlank(supply.getSupplyId()))
                .forEach(supply -> {
                    String cardType = determineCardType(supply.getSupplyType());
                    supplyIdCardMap.put(supply.getSupplyId(), cardType);
                });

        return supplyIdCardMap;
    }

    /**
     * 根据供给类型确定卡片类型
     *
     * @param supplyType 供给类型
     * @return 对应的卡片类型
     */
    private String determineCardType(String supplyType) {
        return SupplyRecommendModel.SupplyType.DOCTOR.getCode().equals(supplyType)
                ? StreamEventCardTypeEnum.DOCTOR_CARD.getType()
                : StreamEventCardTypeEnum.SHOP_PRODUCT_CARD.getType();
    }

    /**
     * 从上下文中获取供给键ID映射
     *
     * @param context 任务上下文
     * @return 供给键ID映射，如果解析失败则返回空Map
     */
    private Map<String, Object> getSupplyKeyIdMapFromContext(TaskContext context) {
        try {
            String supplyKeyCardMapJson = (String) context.getMessageContext()
                    .getExtra()
                    .get(ContextExtraKey.SUPPLY_KEY_CARD_MAP.getKey());
            return new HashMap<>(JsonUtils.parseMap(supplyKeyCardMapJson));
        } catch (Exception e) {
            log.warn("Failed to parse supply key card map from context", e);
            return new HashMap<>();
        }
    }

    /**
     * 在filterList中查找与指定supplyId相同的元素索引
     *
     * @param targetSupplyId 目标供给ID
     * @param filterList     筛选列表
     * @return 匹配元素的索引，如果未找到则返回null
     */
    private Integer findSupplyIndexInFilterList(String targetSupplyId, List<SupplyRecommendModel> filterList) {
        if (StringUtils.isBlank(targetSupplyId) || CollectionUtils.isEmpty(filterList)) {
            return null;
        }

        // 查找匹配的元素索引
        for (int i = 0; i < filterList.size(); i++) {
            SupplyRecommendModel supply = filterList.get(i);
            if (supply != null && targetSupplyId.equals(supply.getSupplyId())) {
                return i;
            }
        }
        return null;
    }

    /**
     * 格式化价格，处理小数位显示
     * 规则：
     * - 如果小数点后为.00，则只保留整数部分
     * - 如果小数点后不为.00，则只保留一位小数（截取第一位小数）
     *
     * @param price 原始价格字符串
     * @return 格式化后的价格字符串
     */
    private String formatPrice(String price) {
        if (StringUtils.isBlank(price) || !price.contains(".")) {
            return price;
        }

        try {
            String[] parts = price.split("\\.");
            if (parts.length != 2) {
                return price;
            }

            String integerPart = parts[0];
            String decimalPart = parts[1];

            // 如果小数部分为00，则只返回整数部分
            if (decimalPart.equals("00") || decimalPart.equals("0")) {
                return integerPart;
            } else {
                // 只保留第一位小数
                char firstDecimal = decimalPart.charAt(0);
                return integerPart + "." + firstDecimal;
            }
        } catch (Exception e) {
            log.error("价格格式异常，返回原始值: {}", price, e);
            return price;
        }
    }

    /**
     * 处理距离字符串，如果距离大于等于30km则置空
     * 支持处理如下格式的距离字符串：
     * - "≥99km" -> 如果数字≥30则返回null，否则返回原字符串
     * - "5km" -> 如果数字≥30则返回null，否则返回原字符串
     * - "1000m" -> 如果数字≥30000m(30km)则返回null，否则返回原字符串
     *
     * @param distance 原始距离字符串
     * @return 处理后的距离字符串，如果≥30km则返回null
     */
    private String processDistance(String distance) {
        if (StringUtils.isBlank(distance)) {
            return distance;
        }

        try {
            // 移除所有空格
            String cleanDistance = distance.trim().replaceAll("\\s+", "");
            
            // 匹配数字部分，支持≥符号
            String numberStr = cleanDistance.replaceAll("[^0-9.]", "");
            if (StringUtils.isBlank(numberStr)) {
                return distance; // 无法解析数字，返回原字符串
            }

            double distanceValue = Double.parseDouble(numberStr);
            
            // 判断单位并转换为km
            if (cleanDistance.toLowerCase().contains("km")) {
                // 已经是km单位
                if (distanceValue >= 50) {
                    return StringUtils.EMPTY;
                }
            } else if (cleanDistance.toLowerCase().contains("m")) {
                // 米单位转换为km
                double distanceInKm = distanceValue / 1000.0;
                if (distanceInKm >= 50) {
                    return StringUtils.EMPTY;
                }
            } else {
                // 默认当作km处理
                if (distanceValue >= 50) {
                    return StringUtils.EMPTY;
                }
            }
            
            return distance;
            
        } catch (Exception e) {
            log.warn("距离字符串解析异常，返回原始值: {}", distance, e);
            return distance;
        }
    }
}
