package com.sankuai.dzhealth.ai.service.domain.card.handlers;

import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.Cat;
import com.sankuai.dzhealth.ai.service.domain.card.AbstractCardHandler;
import com.sankuai.dzhealth.ai.service.domain.service.ChatSessionService;
import com.sankuai.dzhealth.ai.service.domain.utils.context.AiAnswerContext;
import com.sankuai.dzhealth.ai.service.dto.stream.StreamEventDTO;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 摘要卡片处理器
 */
@Component
@Order(1)
public class DigestCardHandler extends AbstractCardHandler {

    @Autowired
    private ChatSessionService chatSessionService;
    
    @Override
    public boolean supports(String fieldName) {
        return "digest".equals(fieldName);
    }
    
    @Override
    public List<StreamEventDTO> handle(JSONObject jsonObject, 
                                    AiAnswerContext context, 
                                    SseEmitter sseEmitter, 
                                    AtomicInteger sseIndex,
                                    long startChatTime) {
        String digest = jsonObject.getString("digest");
        if (StringUtils.isNotBlank(digest)) {
            Cat.logEvent("update_session", "start");
            boolean res = chatSessionService.updateSession(context.getSessionId(), (byte) 1, digest);
            Cat.logEvent("update_session", String.valueOf(res));
        }
        // 摘要处理不生成卡片事件
        return emptyEvents();
    }

    @Override
    public int getOrder() {
        return 1;
    }
} 