package com.sankuai.dzhealth.ai.service.agent.domain.service.evaluation;

import com.dianping.lion.client.util.CollectionUtils;
import com.dianping.zebra.group.router.ZebraForceMasterHelper;
import com.sankuai.dzhealth.ai.service.agent.domain.entity.ChatSessionMessageEntity;
import com.sankuai.dzhealth.ai.service.agent.domain.repository.chatSession.ChatSessionMessageRepository;
import com.sankuai.dzhealth.ai.service.agent.domain.repository.chatSession.ChatSessionRepository;
import com.sankuai.dzhealth.ai.service.agent.domain.utils.ExcelUtils;
import com.sankuai.dzhealth.ai.service.agent.infrastructure.dal.entity.*;
import com.sankuai.dzhealth.ai.service.agent.infrastructure.dal.example.MessageEvaluationResultEntityExample;
import com.sankuai.dzhealth.ai.service.agent.infrastructure.dal.mapper.MessageEvaluationResultEntityMapper;
import com.sankuai.dzhealth.ai.service.agent.infrastructure.dal.repository.evaluation.EvaluationRecordsRepository;
import com.sankuai.dzhealth.ai.service.agent.infrastructure.dal.repository.evaluation.EvaluationSessionRecordsRepository;
import com.sankuai.dzhealth.ai.service.agent.infrastructure.dal.repository.evaluation.SessionEvaluationResultRepository;
import com.sankuai.dzhealth.ai.service.domain.evaluation.entity.MessageCopyRequest;
import com.sankuai.dzhealth.ai.service.domain.utils.JsonUtils;
import com.sankuai.dzhealth.ai.service.dto.evaluation.MessageListDTO;
import com.sankuai.dzhealth.ai.service.dto.haima.EvaluationCaseInfo;
import com.sankuai.dzhealth.ai.service.dto.haima.EvaluationMetric;
import com.sankuai.dzhealth.ai.service.dto.stream.StreamEventDTO;
import com.sankuai.dzhealth.ai.service.enums.StreamEventCardTypeEnum;
import com.sankuai.dzhealth.ai.service.infrastructure.utils.UidUtils;
import joptsimple.internal.Strings;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static com.sankuai.dzhealth.ai.service.agent.domain.service.evaluation.EvaluationDialogueService.EvaluationType.*;
import static com.sankuai.dzhealth.ai.service.infrastructure.utils.UidUtils.CHAT_MESSAGE_LEAF_KEY;
import static com.sankuai.dzhealth.ai.service.infrastructure.utils.UidUtils.CHAT_SESSION_LEAF_KEY;


@Slf4j
@Service
public class EvaluationDialogueService {

    @Autowired
    private ChatSessionMessageRepository chatSessionMessageRepository;

    @Autowired
    private EvaluationDataService evaluationDataService;

    @Autowired
    private ExcelUtils excelUtils;

    @Autowired
    private UidUtils uidUtils;

    @Autowired
    private EvaluationRecordsRepository evaluationRecordsRepository;


    @Autowired
    private EvaluationSessionRecordsRepository evaluationSessionRecordsRepository;

    @Autowired
    private EvaluationMetricsService evaluationMetricsService;

    @Autowired
    private MessageEvaluationResultEntityMapper messageEvaluationResultEntityMapper;

    @Autowired
    private SessionEvaluationResultRepository sessionEvaluationResultRepository;
    @Autowired
    private ChatSessionRepository chatSessionRepository;

    public String dialogueReplay(MessageCopyRequest request) {
        List<String> sessionIDs = new ArrayList<>();
        if (!Strings.isNullOrEmpty(request.getEvaluationId())) {
            List<EvaluationSessionRecordsDO> sessionEvaluationResultEntities = evaluationSessionRecordsRepository.findByEvaluationIdOrderByCreateTimeDesc(request.getEvaluationId(),1000,0);
            sessionIDs = sessionEvaluationResultEntities.stream().map(EvaluationSessionRecordsDO::getSessionId).toList();
            if (CollectionUtils.isNotEmpty(request.getSessionIds())) {
                sessionIDs = sessionIDs.stream().filter(request.getSessionIds()::contains).toList();
            }
        } else {
            sessionIDs = request.getSessionIds();
        }
        if (CollectionUtils.isEmpty(sessionIDs)) {
            return "";
        }
        List<ChatSessionDOWithBLOBs> chatSessionDOWithBLOBs = chatSessionRepository.findSessionsBySessionIDs(sessionIDs, Long.parseLong(request.getSourceUserID()));
        List<ChatSessionMessageDOWithBLOBs> chatSessionMessageDOWithBLOBs = chatSessionMessageRepository.findMessagesBySessionIds(sessionIDs, Long.parseLong(request.getSourceUserID()));

        // 替换新的session_id 和 message_id  和 user_id
        Map<String, String> sessionIdMap = new HashMap<>();
        Map<String, String> messageIdMap = new HashMap<>();
        chatSessionDOWithBLOBs.forEach(e -> {
            e.setUserId(Long.parseLong(request.getTargetUserID()));
            e.setSessionId(sessionIdMap.computeIfAbsent(e.getSessionId(), k -> {
                return String.valueOf(uidUtils.getNextId(CHAT_SESSION_LEAF_KEY));
            }));
        });
        chatSessionMessageDOWithBLOBs.forEach(e -> {
            e.setUserId(Long.parseLong(request.getTargetUserID()));
            e.setSessionId(sessionIdMap.computeIfAbsent(e.getSessionId(), k -> {
                return String.valueOf(uidUtils.getNextId(CHAT_SESSION_LEAF_KEY));
            }));
            e.setMessageId(messageIdMap.computeIfAbsent(e.getMessageId(), k -> {
                return String.valueOf(uidUtils.getNextId(CHAT_MESSAGE_LEAF_KEY));
            }));
        });
        int sessionUpdateCount = chatSessionRepository.batchInsertChatSessionDOWithBLOBs(chatSessionDOWithBLOBs);
        int messageUpdateCount = chatSessionMessageRepository.batchInsertChatSessionMessageDOWithBLOBs(chatSessionMessageDOWithBLOBs);
        return String.format("完成对话迁移sessionUpdateCount:%s,messageUpdateCount:%s", sessionUpdateCount, messageUpdateCount);
    }

    @Getter
    @AllArgsConstructor
    public enum EvaluationType {
        MEDICAL_AGENT("medicalAgent", "意图识别"),
        RELATED_QUESTION("relatedQuestion", "猜你想问"),
        MEDICAL_DECISION("medicalDecision", "导购"),
        MEDICAL_BAIKE("medicalBaike", "科普"),
        MEDICAL_APPOINTMENT("medicalAppointment", "医美预约任务"),
        End2End("end2end", "端到端");

        private final String code;
        private final String name;
    }


    static Map<EvaluationType, List<String>> sceneMetricMapping = new LinkedHashMap<>();

    static {
        sceneMetricMapping.put(MEDICAL_AGENT, List.of("intent_correctness"));
        sceneMetricMapping.put(RELATED_QUESTION, List.of("guess_question"));
        sceneMetricMapping.put(MEDICAL_DECISION, Arrays.asList("response_structure", "classification_rationality", "scientific_explanation", "content_risk", "recommendation_ability"));
        sceneMetricMapping.put(MEDICAL_BAIKE, Arrays.asList("response_structure", "classification_rationality", "scientific_explanation", "content_risk"));
        sceneMetricMapping.put(MEDICAL_APPOINTMENT, List.of("appointment_completion"));
        sceneMetricMapping.put(End2End, List.of("dialogue_flow", "task_completion"));
    }

    /*
medicalAgent: 通用
   intent_correctness	意图识别准确度
relatedQuestion:
   guess_question	猜你想问合理性
medicalDecision: // 导购
   response_structure	回复内容的结构
   classification_rationality	分类合理
   scientific_explanation	科普内容质量
   content_risk	内容安全风险
   recommendation_ability	推荐能力
medicalBaike:
   response_structure	回复内容的结构
   classification_rationality	分类的合理性
   scientific_explanation	科学讲解内容质量
   content_risk	内容安全风险
medicalAppointment
   appointment_completion	预约完成度
*/
    public ResponseEntity<byte[]> evaluationResult(String evaluationID, Integer version) {
        List<EvaluationMetric> evaluationMetricInfoList = evaluationMetricsService.getEvaluationMetricInfo();
        Map<String, EvaluationMetric> evaluationMetricInfoMap = evaluationMetricInfoList.stream().
                collect(Collectors.toMap(EvaluationMetric::getKey, e -> e, (existing, replacement) -> existing));

        ArrayList<String> headers = new ArrayList<>();
        headers.add("session_id");
        headers.add("message_id");
        headers.add("message");
        ArrayList<String> extraHeaders = new ArrayList<>();
        sceneMetricMapping.forEach((k, v) -> {
            v.forEach(item -> {
                EvaluationMetric evaluationMetric = evaluationMetricInfoMap.get(item);
                if (evaluationMetric == null) {
                    return;
                }
                headers.add(k.getName() + "-" + evaluationMetric.getName());
                extraHeaders.add(k.getName() + "-" + evaluationMetric.getName() + "-reason");
            });
        });
        headers.add("单轮对话统计得分");
        headers.add("session统计得分");
        headers.addAll(extraHeaders);

        ArrayList<Map<String, String>> rows = new ArrayList<>();
        // 查询评测下session列表
        List<EvaluationSessionRecordsDO> sessionList = evaluationSessionRecordsRepository.findByEvaluationIdOrderByCreateTimeDesc(evaluationID, 1000, 0);
        if (sessionList == null) {
            return null;
        }

        AtomicInteger colorNum = new AtomicInteger();
        sessionList.forEach(sessionData -> {
            String sessionId = sessionData.getSessionId();
            String caseId = sessionData.getCaseId();
            colorNum.getAndIncrement();

            // 查询session下message评测结果
            MessageEvaluationResultEntityExample example = new MessageEvaluationResultEntityExample();
            example.createCriteria()
                    .andSessionIdEqualTo(sessionId);
            example.setOrderByClause("add_time asc");
            List<MessageEvaluationResultEntity> messageEvaluationResultEntities = messageEvaluationResultEntityMapper.selectByExample(example);
            if (messageEvaluationResultEntities == null) {
                return;
            }
            //  message_id  model_scene  metric_code  value
            Map<String, Map<String, Map<String, MessageEvaluationResultEntity>>> messageDataMap = new LinkedHashMap<>();
            messageEvaluationResultEntities.forEach(e -> {
                messageDataMap.computeIfAbsent(e.getMessageId(), k -> new HashMap<>());
                messageDataMap.get(e.getMessageId()).computeIfAbsent(e.getModelScene(), k -> new HashMap<>());
                messageDataMap.get(e.getMessageId()).get(e.getModelScene()).computeIfAbsent(e.getItem(), k -> e);
            });

            Map<String, ChatSessionMessageDOWithBLOBs> messageMap = new HashMap<>();
            List<ChatSessionMessageDOWithBLOBs> allMessagesBySessionId = chatSessionMessageRepository.findAllMessagesBySessionId(sessionId);
            if (allMessagesBySessionId != null) {
                messageMap = allMessagesBySessionId.stream().collect(Collectors.toMap(ChatSessionMessageDO::getMessageId, e -> e, (existing, replacement) -> existing));
            }

            AtomicInteger score0MessageNum = new AtomicInteger();
            AtomicInteger score1MessageNum = new AtomicInteger();
            AtomicInteger score2MessageNum = new AtomicInteger();
            Map<String, ChatSessionMessageDOWithBLOBs> finalMessageMap = messageMap;
            messageDataMap.forEach((k, v) -> {
                HashMap<String, String> row = new HashMap<>();
                row.put("session_id", caseId + "-" + sessionId);
                row.put("message_id", k);
                row.put("color", colorNum.get() % 2 == 0 ? "grey" : "white");

                row.put("message", finalMessageMap.getOrDefault(k, new ChatSessionMessageDOWithBLOBs()).getContent());

                EvaluationType intent = null;
                if (v.containsKey(MEDICAL_DECISION.getCode())) {
                    intent = MEDICAL_DECISION;
                } else if (v.containsKey(MEDICAL_BAIKE.getCode())) {
                    intent = MEDICAL_BAIKE;
                } else if (v.containsKey(MEDICAL_APPOINTMENT.getCode())) {
                    intent = MEDICAL_APPOINTMENT;
                }

                Map<EvaluationType, List<String>> rowNeedSceneMetricMapping = new LinkedHashMap<>();
                rowNeedSceneMetricMapping.put(MEDICAL_AGENT, sceneMetricMapping.get(MEDICAL_AGENT));
                rowNeedSceneMetricMapping.put(RELATED_QUESTION, sceneMetricMapping.get(RELATED_QUESTION));
                if (intent != null) {
                    rowNeedSceneMetricMapping.put(intent, sceneMetricMapping.get(intent));
                }


                AtomicInteger intentCorrectness = new AtomicInteger();
                AtomicInteger score0num = new AtomicInteger();
                AtomicInteger score1num = new AtomicInteger();
                AtomicInteger score2num = new AtomicInteger();
                AtomicInteger unknowNum = new AtomicInteger();
                AtomicInteger total = new AtomicInteger();

                rowNeedSceneMetricMapping.forEach((t, metric) -> {
                    metric.forEach(item -> {
                        total.incrementAndGet();
                        EvaluationMetric evaluationMetric = evaluationMetricInfoMap.get(item);
                        if (evaluationMetric == null) {
                            log.error("metric info is null {}", item);
                            return;
                        }

                        String cellValue = "UNKNOWN";
                        String reason = "-";
                        if (v.get(t.getCode()) != null && v.get(t.getCode()).get(item) != null) {
                            MessageEvaluationResultEntity messageEvaluationResultEntity = v.get(t.getCode()).get(item);
                            int i = messageEvaluationResultEntity.getScore().intValue();
                            reason = messageEvaluationResultEntity.getReason();
                            cellValue = String.valueOf(i);
                            if ("intent_correctness".equals(item)) {
                                intentCorrectness.set(i);
                            }
                            switch (i) {
                                case 0:
                                    score0num.incrementAndGet();
                                    break;
                                case 1:
                                    score1num.incrementAndGet();
                                    break;
                                case 2:
                                    score2num.incrementAndGet();
                                    break;
                            }
                        } else {
                            unknowNum.incrementAndGet();
                        }
                        row.put(t.getName() + "-" + evaluationMetric.getName(), cellValue);
                        row.put(t.getName() + "-" + evaluationMetric.getName() + "-reason", reason);
                    });
                });

                int messageScore = 0;
                if (score2num.get() == total.get()) {
                    messageScore = 2;
                    score2MessageNum.incrementAndGet();
                } else if (score1num.get() < total.get() / 2 && score0num.get() == 0) {
                    messageScore = 1;
                    score1MessageNum.incrementAndGet();
                } else {
                    score0MessageNum.incrementAndGet();
                }
                row.put("单轮对话统计得分", String.valueOf(messageScore));
                rows.add(row);
            });
            int sessionScore = 0;
            if (score2MessageNum.get() == messageDataMap.size()) {
                sessionScore = 2;
            } else if (score1MessageNum.get() < messageDataMap.size() / 2 && score0MessageNum.get() == 0) {
                sessionScore = 1;
            }
            if (rows.size() > 1) {
                rows.get(rows.size() - 1).put("session统计得分", String.valueOf(sessionScore));
            }

            // session视角 评测结果

            List<SessionEvaluationResultEntity> sessionEvaluationResults = sessionEvaluationResultRepository.selectBySessionIdAndVersion(sessionId, version);
            Map<String, SessionEvaluationResultEntity> collect = sessionEvaluationResults.stream().collect(Collectors.toMap(e -> e.getItem(), e -> e));
            if (collect.containsKey("dialogue_flow")) {
                rows.get(rows.size() - 1).put(evaluationMetricInfoMap.get("dialogue_flow").getName(), collect.get("dialogue_flow").getScore().toBigInteger().toString());
                rows.get(rows.size() - 1).put(evaluationMetricInfoMap.get("dialogue_flow").getName() + "-reason", collect.get("dialogue_flow").getReason());
            }
            if (collect.containsKey("task_completion")) {
                rows.get(rows.size() - 1).put(evaluationMetricInfoMap.get("task_completion").getName(), collect.get("task_completion").getScore().toBigInteger().toString());
                rows.get(rows.size() - 1).put(evaluationMetricInfoMap.get("task_completion").getName() + "-reason", collect.get("task_completion").getReason());
            }
        });

        ExcelUtils.SheetData sheetData = ExcelUtils.SheetData.builder().headers(headers).dataList(rows).build();
        return excelUtils.generateMultiSheetExcel(evaluationID, Collections.singletonList(sheetData));
    }


    public ResponseEntity<byte[]> downloadDialogueRecordCSV(@RequestParam(value = "evaluation_id") String evaluationId) {

        EvaluationRecordsDO evaluationRecordsDO = evaluationRecordsRepository.selectByExample(evaluationId).stream().findFirst().orElse(null);
        if (evaluationRecordsDO == null) {
            return new ResponseEntity<>(HttpStatus.NOT_FOUND);
        }

        List<EvaluationSessionRecordsDO> byEvaluationId = evaluationSessionRecordsRepository.findByEvaluationId(evaluationId);
        HashMap<String, String> case2Session = new HashMap<>();
        byEvaluationId.forEach(e -> case2Session.put(e.getCaseId(), e.getSessionId()));

        List<EvaluationCaseInfo> evaluationData = evaluationDataService.getEvaluationCaseInfo().stream().filter(e -> case2Session.containsKey(String.valueOf(e.getNum()))).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(evaluationData)) {
            return new ResponseEntity<>(null, null, HttpStatus.OK);
        }

        ArrayList<String> headers = new ArrayList<>();
        headers.add("测试集编号");
        headers.add("session_id");
        headers.add("message_id");
//        headers.add("对话内容");
        headers.add("角色");
        headers.add("原始记录answer");
        ArrayList<Map<String, String>> dataList = new ArrayList<>();

        evaluationData.forEach(e ->

        {
            try {
                String sessionID = case2Session.get(String.valueOf(e.getNum()));
                if (sessionID == null) {
                    log.warn("Session ID not found for case: {}", e.getNum());
                    return;
                }

                List<ChatSessionMessageEntity> chatSessionMessageEntities = getChatSessionMessageEntities(sessionID);
                List<MessageListDTO> messageListDTOS = extractMessageV2(chatSessionMessageEntities);

                for (int i = 0; i + 1 < messageListDTOS.size() && i < chatSessionMessageEntities.size() - 1; i = i + 2) {
                    // User message
                    HashMap<String, String> data = new HashMap<>();
                    data.put("测试集编号", String.valueOf(e.getNum()));
                    data.put("session_id", String.valueOf(e.getNum()) + "3210" + sessionID);
                    data.put("message_id", messageListDTOS.get(i).getMessageId());
                    data.put("角色", "user");
                    data.put("原始记录answer", "message_id:" + messageListDTOS.get(i).getMessageId() + ":" + chatSessionMessageEntities.get(i).getContent());
//                    data.put("对话内容", "%s\n------------------\n%s".formatted(
//                            messageListDTOS.get(i).getMessageContent(),
//                            i + 1 < messageListDTOS.size() ? messageListDTOS.get(i + 1).getMessageContent() : ""
//                    ));
                    dataList.add(data);

                    // Assistant message
                    if (i + 1 < messageListDTOS.size()) {
                        HashMap<String, String> data1 = new HashMap<>();
                        data1.put("测试集编号", String.valueOf(e.getNum()));
                        data1.put("session_id", String.valueOf(e.getNum()) + "3210" + sessionID);
                        data1.put("message_id", messageListDTOS.get(i).getMessageId());
                        data1.put("角色", "assistant");
                        data1.put("原始记录answer", i + 1 < chatSessionMessageEntities.size() ? chatSessionMessageEntities.get(i + 1).getContent() : "");
//                        data1.put("对话内容", "%s\n------------------\n%s".formatted(
//                                messageListDTOS.get(i).getMessageContent(),
//                                messageListDTOS.get(i + 1).getMessageContent()
//                        ));
                        dataList.add(data1);
                    }
                }
            } catch (Exception ex) {
                log.error("Error processing evaluation data for case: {}", e.getNum(), ex);
            }
        });

        return excelUtils.generateCSV(evaluationRecordsDO.getEvaluationName(), headers, dataList);
    }

    private List<ChatSessionMessageEntity> getChatSessionMessageEntities(String sessionID) {
        List<ChatSessionMessageEntity> bySessionIdAndStatus = chatSessionMessageRepository.findBySessionIdAndStatus(sessionID, 100, 0);
        ZebraForceMasterHelper.clearLocalContext();
        bySessionIdAndStatus.sort((o1, o2) -> {
            // 相同创建时间user在前 assistant在后
            if (o1.getCreateTime().equals(o2.getCreateTime())) {
                int val1 = "user".equals(o1.getRole()) ? 0 : 1;
                int val2 = "user".equals(o2.getRole()) ? 0 : 1;
                return val1 - val2;
            }
            return o1.getCreateTime().compareTo(o2.getCreateTime());
        });
        return bySessionIdAndStatus;
    }


    @NotNull
    private List<MessageListDTO> extractMessageV2(List<ChatSessionMessageEntity> bySessionIdAndStatus) {
        ZebraForceMasterHelper.forceMasterInLocalContext();
        // 历史会话查询
        List<MessageListDTO> chatRecords = bySessionIdAndStatus.stream().map(e -> {
            String role = e.getRole();
            String content = "";
            if (Objects.equals(role, "user")) {
                content = "<<我>>:" + e.getContent();
            } else {
                List<StreamEventDTO> streamEventDTOS = JsonUtils.parseArray(e.getContent(), StreamEventDTO.class);
                List<String> mainText = streamEventDTOS.
                        stream().
                        filter(dto -> dto.getData() != null && dto.getData().getEvent().equals("mainText")).
                        map(dto -> {
                            List<String> cardList = dto.getData().getCardsData().stream().map(
                                    cardData -> {
                                        StreamEventCardTypeEnum cardType = StreamEventCardTypeEnum.getByType(cardData.getType());
                                        if (cardType == StreamEventCardTypeEnum.RICH_TEXT_TITLE_CARD) {
                                            return null;
                                        }
                                        if (cardType == null) {
                                            return JsonUtils.toJsonString(cardData.getCardProps());
                                        }
                                        return cardType.getDesc() + "<%s>%s<%s/>".formatted(cardType, cardData.getKey(), cardType) + "详细信息:\n " + "```\n" + JsonUtils.toPrettyJsonString(cardData.getCardProps()) + "\n```";
                                    }
                            ).filter(Objects::nonNull).toList();
                            return dto.getData().getContent() + "\n --- \n<<以下是卡片详细信息>>\n" + Strings.join(cardList, "\n");
                        }).
                        toList();
                content = "<<医生>>:" + Strings.join(mainText, "\n");
            }
            return MessageListDTO.
                    builder().
                    sessionId(e.getSessionId()).
                    messageId(e.getMessageId()).
                    messageContent(content).
                    createTime(DateFormatUtils.format(e.getCreateTime(), "yyyy-MM-dd HH:mm:ss")).
                    build();
        }).toList();
        return chatRecords;
    }
}
