package com.sankuai.dzhealth.ai.service.agent.domain.process.card.data;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.HashMap;
import java.util.Map;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AppointmentCardData {

    /**
     * 状态
     */
    private Integer status;

    /**
     * 标题
     */
    private String title;

    /**
     * 副标题
     */
    private String subtitle;

    /**
     * 时间
     */
    private String time;

    /**
     * 位置
     */
    private String position;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 人员描述
     */
    private String personDesc;

    /**
     * 电话
     */
    private String phone;

    /**
     * 等待时间
     */
    private String waitTime;

    /**
     * 当前号码
     */
    private Integer currentNum;

    /**
     * 总号码数
     */
    private Integer totalNum;

    /**
     * 消息
     */
    private String msg;

    /**
     * 成功商店名称
     */
    private String successShop;

    /**
     * 成功商店ID
     */
    private Long successShopId;


    /**
     * 备注
     */
    private String remark;

    /**
     * 预约单跳链
     */
    private String leadUrl;

    /**
     * 消息ID
     */
    private String msgId;

    /**
     * 是否点击
     */
    private boolean clicked;

    /**
     * 导航跳链
     */
    private String NavigateUrl;

    /**
     *商家跳链
     *
     */
    private String shopUrl;

    /**
     * 搜索关键字
     */
    private String searchword;

    /**
     * 距离，默认5000，单位米
     */
    private Double distance;

    /**
     * 价格：格式为 R100.0:200.0
     */
    private String price;


    public Map<String, Object> toMap() {
        Map<String, Object> map = new HashMap<>();
        map.put("status", this.status);
        map.put("title", this.title);
        map.put("subtitle", this.subtitle);
        map.put("time", this.time);
        map.put("position", this.position);
        map.put("productName", this.productName);
        map.put("personDesc", this.personDesc);
        map.put("phone", this.phone);
        map.put("waitTime", this.waitTime);
        map.put("currentNum", this.currentNum);
        map.put("totalNum", this.totalNum);
        map.put("msg", this.msg);
        map.put("successShop", this.successShop);
        map.put("successShopId", this.successShopId);
        map.put("remark", this.remark);
        map.put("leadUrl", this.leadUrl);
        map.put("msgId", this.msgId);
        map.put("clicked", this.clicked);
        map.put("NavigateUrl", this.NavigateUrl);
        map.put("shopUrl", this.shopUrl);
        map.put("searchword", this.searchword);
        map.put("distance", this.distance);
        map.put("price", this.price);
        return map;
    }



}
