package com.sankuai.dzhealth.ai.service.domain.service;

import com.facebook.swift.service.ThriftService;
import com.sankuai.dzhealth.ai.service.domain.chat.model.ChatSummaryResult;
import com.sankuai.dzhealth.ai.service.request.ChatSummaryRequest;

/**
 * @Author: zhongchangze
 * @Date: 2025/3/24 11:47
 * @Description:
 */
@ThriftService
public interface ChatSummaryService {

    ChatSummaryResult getChatSummary(ChatSummaryRequest chatSummaryRequest);

    boolean support(Integer sceneType);
}
