package com.sankuai.dzhealth.ai.service.agent.domain.process.card;

import com.sankuai.dzhealth.ai.service.agent.domain.context.MessageContext;
import com.sankuai.dzhealth.ai.service.agent.enums.ContextExtraKey;
import com.sankuai.dzhealth.ai.service.domain.utils.JsonUtils;
import com.sankuai.dzhealth.ai.service.dto.stream.StreamEventCardDataDTO;
import com.sankuai.dzhealth.ai.service.enums.StreamEventCardTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @author:chenwei
 * @time: 2025/7/19 17:50
 * @version: 0.0.1
 */

@Service
@Slf4j
public class OptionCardBuilder implements CardBuilder {
    
    // 匹配选项的正则表达式：匹配方括号内的内容，然后按冒号分割
    private static final Pattern OPTION_PATTERN = Pattern.compile("\\[([^\\]]*)\\]");
    
    @Override
    public boolean accept(String cardType) {
        return StreamEventCardTypeEnum.OPTION_CARD.getType().equals(cardType);
    }

    @Override
    public void padding(StreamEventCardDataDTO streamEventCardDataDTO, MessageContext messageContext) {
        String key = streamEventCardDataDTO.getKey();
        
        if (StringUtils.isBlank(key)) {
            return;
        }
        Map<String, Object> picId2UrlMap = new HashMap<>();
        Map<String, Serializable> extra = messageContext.getExtra();
        if (MapUtils.isNotEmpty(extra) && extra.containsKey(ContextExtraKey.ASSESS_PICTURE_URLS.getKey())) {
            String assessPicJson = (String) extra.get(ContextExtraKey.ASSESS_PICTURE_URLS.getKey());
            picId2UrlMap = JsonUtils.parseMap(assessPicJson);
        }
        
        // 解析字符串并构建 cardProps
        Map<String, Object> cardProps = parseKeyToCardProps(key, picId2UrlMap);
        if (!cardProps.isEmpty()) {
            streamEventCardDataDTO.setCardProps(cardProps);
        }
    }

    @Override
    public String parseCardProps(StreamEventCardDataDTO streamEventCardDataDTO) {
        Map<String, Object> cardProps = streamEventCardDataDTO.getCardProps();

        StringBuilder result = new StringBuilder();
        String questionText = (String) cardProps.get("questionText");
        result.append("选项问题是：").append(questionText).append("\n");
        try {
            List<OptionDTO> options = JsonUtils.parseArray(JsonUtils.toJsonString(cardProps.get("options")), OptionDTO.class);

            for (int i = 0; i < options.size(); i++) {
                OptionDTO option = options.get(i);
                result.append("选项").append(i + 1).append("的选项是:").append(option.getText()).append(",选项描述是:")
                        .append(option.getDesc()).append("\n");
            }
        } catch (Exception e) {
            log.error("<optionCard parse>", e);
        }
        return result.toString();
    }


    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class OptionDTO implements Serializable {

        private String text;

        private String desc;

        private String imageUrl;
    }

    /**
     * 解析 key 字符串并转换为 cardProps
     * 格式：问题[选项1:选项1描述:图片id][选项2:desc:图片id][选项3:desc:图片id]底部文案
     * 
     * @param key 要解析的字符串
     * @return 解析后的 cardProps Map
     */
    private Map<String, Object> parseKeyToCardProps(String key, Map<String, Object> picId2UrlMap) {
        try {
            Map<String, Object> cardProps = new HashMap<>();
            
            // 查找所有选项
            Matcher matcher = OPTION_PATTERN.matcher(key);
            List<Map<String, Object>> options = new ArrayList<>();
            
            while (matcher.find()) {
                String bracketContent = matcher.group(1).trim();
                
                // 按冒号分割方括号内的内容
                String[] parts = bracketContent.split(":", -1); //
                
                String text = parts.length > 0 ? parts[0].trim() : "";
                String desc = parts.length > 1 ? parts[1].trim() : "";
                String imageId = parts.length > 2 ? parts[2].trim() : "";
                
                // 如果desc为空并且imageId不为pic开头，则把imageId内容与desc互换
                if (StringUtils.isBlank(desc) && StringUtils.isNotBlank(imageId) && !imageId.startsWith("pic")) {
                    String temp = desc;
                    desc = imageId;
                    imageId = temp;
                }
                
                Map<String, Object> option = new HashMap<>();
                if (StringUtils.isNotBlank(text)) {
                    option.put("text", text);
                    option.put("desc", desc);
                    // 如果图片ID不为空，则设置图片URL（这里可能需要根据实际情况构建完整的URL）
                    if (StringUtils.isNotBlank(imageId) && imageId.startsWith("pic")) {
                        // 这里可能需要根据图片ID构建完整的URL，暂时直接使用ID
                        option.put("imageUrl", buildImageUrl(imageId, picId2UrlMap));
                    } else {
                        option.put("imageUrl", "");
                    }
                }

                
                options.add(option);
            }
            if (CollectionUtils.isEmpty(options) || options.size() < 2) {
                return cardProps;
            }
            
            // 提取问题文本（选项之前的部分）
            String questionText = extractQuestionText(key);
            cardProps.put("questionText", questionText);
            
            // 提取底部文案（选项之后的部分）
            String extraButtonText = extractExtraButtonText(key);
            cardProps.put("extraButtonText", extraButtonText);
            
            // 设置筛选类型，根据是否有图片URL来判断
            // 只有当所有选项都有有效的图片URL时，才设置为图片类型，否则为文本类型
            boolean allHasImages = options.stream()
                    .allMatch(option -> (StringUtils.isNotBlank((String) option.get("imageUrl")) && ((String) option.get("imageUrl")).startsWith("http")));
            cardProps.put("filterType", allHasImages ? "image" : "text");
            
            // 添加选项列表
            cardProps.put("options", options);
            
            return cardProps;
            
        } catch (Exception e) {
            // 记录错误日志，但不抛出异常
            log.error("解析选项卡片数据异常" + e.getMessage(), e);
            return new HashMap<>();
        }
    }
    
    /**
     * 提取问题文本（第一个 [ 之前的内容）
     */
    private String extractQuestionText(String key) {
        int firstBracket = key.indexOf('[');
        if (firstBracket > 0) {
            return key.substring(0, firstBracket).trim();
        }
        return "";
    }
    
    /**
     * 提取底部文案（最后一个 ] 之后的内容）
     */
    private String extractExtraButtonText(String key) {
        int lastBracket = key.lastIndexOf(']');
        if (lastBracket != -1 && lastBracket < key.length() - 1) {
            return key.substring(lastBracket + 1).trim();
        }
        return "";
    }
    
    /**
     * 根据图片ID构建图片URL
     * 这里可能需要根据实际的图片服务来构建完整的URL
     */
    private String buildImageUrl(String imageId, Map<String, Object> picId2UrlMap) {
        if (StringUtils.isBlank(imageId)) {
            return "";
        }

        return String.valueOf(picId2UrlMap.get(imageId));
    }
}
