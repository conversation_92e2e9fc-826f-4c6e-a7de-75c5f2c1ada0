package com.sankuai.dzhealth.ai.service.domain.service;

import com.sankuai.dzhealth.ai.service.agent.infrastructure.proxy.product.ProductQueryProxy;
import com.sankuai.dzhealth.ai.service.agent.domain.model.product.Product;
import com.sankuai.dzhealth.ai.service.agent.domain.model.product.ProductBasic;
import com.sankuai.dzhealth.ai.service.agent.domain.model.product.ProductCategory;
import com.sankuai.dzhealth.ai.service.agent.domain.model.product.ProductTag;
import com.sankuai.general.product.query.center.client.dto.AttrDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO;
import com.sankuai.general.product.query.center.client.enums.IdTypeEnum;
import com.sankuai.general.product.query.center.client.response.QueryDealGroupListResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 商品领域服务
 * 负责商品相关的领域逻辑处理，包含防腐层转换
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ProductDomainService {

    private final ProductQueryProxy productQueryProxy;

    /**
     * 根据商品ID和类型查询商品信息
     *
     * @param productId 商品ID
     * @param idType    ID类型枚举
     * @return 商品领域模型，查询失败返回null
     */
    public Product findProduct(Long productId, IdTypeEnum idType) {
        try {
            QueryDealGroupListResponse response;
            if (idType == IdTypeEnum.BIZ_PRODUCT) {
                response = productQueryProxy.queryBizProductInfo(productId);
            } else {
                response = productQueryProxy.queryDealGroupInfo(productId, idType);
            }

            return convertToProduct(response, idType);
        } catch (Exception e) {
            log.error("findProduct exception, productId={}, idType={}", productId, idType, e);
            return null;
        }
    }

    /**
     * 查询团购商品信息
     * 适用于点评/美团团购商品
     */
    public Product findDealGroupProduct(Long productId, IdTypeEnum idType) {
        if (idType != IdTypeEnum.DP && idType != IdTypeEnum.MT) {
            log.warn("findDealGroupProduct: invalid idType for deal group, productId={}, idType={}",
                    productId, idType);
            return null;
        }
        return findProduct(productId, idType);
    }

    /**
     * 查询泛商品信息
     * 适用于泛商品业务体系
     */
    public Product findBizProduct(Long productId) {
        return findProduct(productId, IdTypeEnum.BIZ_PRODUCT);
    }

    // ========== 防腐层转换逻辑 ==========

    /**
     * 将查询中心响应转换为商品领域模型
     */
    private Product convertToProduct(QueryDealGroupListResponse response, IdTypeEnum idType) {
        if (response == null || response.getCode() != 0 ||
            CollectionUtils.isEmpty(response.getData().getList())) {
            log.warn("convertToProduct: invalid response, code={}",
                    response != null ? response.getCode() : null);
            return null;
        }

        // 取第一个商品数据（通常批量查询只查一个商品）
        DealGroupDTO dealGroup = response.getData().getList().get(0);

        return Product.builder()
                .basic(convertBasic(dealGroup))
                .category(convertCategory(dealGroup, idType))
                .tags(convertTags(dealGroup))
                .attributes(convertAttributes(dealGroup))
                .isBizProduct(idType == IdTypeEnum.BIZ_PRODUCT)
                .build();
    }

    /**
     * 转换商品基本信息
     */
    private ProductBasic convertBasic(DealGroupDTO dealGroup) {
        ProductBasic.ProductBasicBuilder builder = ProductBasic.builder()
                .dpProductId(dealGroup.getDpDealGroupId())
                .mtProductId(dealGroup.getMtDealGroupId())
                .bizProductId(dealGroup.getBizProductId());

        if (dealGroup.getBasic() != null) {
            builder.title(dealGroup.getBasic().getTitle())
                   .status(dealGroup.getBasic().getStatus())
                   .beginSaleDate(dealGroup.getBasic().getBeginSaleDate())
                   .endSaleDate(dealGroup.getBasic().getEndSaleDate())
                   .tradeType(dealGroup.getBasic().getTradeType())
                   .brandName(dealGroup.getBasic().getBrandName())
                   .addTime(dealGroup.getBasic().getAddTime())
                   .updateTime(dealGroup.getBasic().getUpdateTime());
        }

        return builder.build();
    }

    /**
     * 转换商品类目信息
     */
    private ProductCategory convertCategory(DealGroupDTO dealGroup, IdTypeEnum idType) {
        ProductCategory.ProductCategoryBuilder builder = ProductCategory.builder();

        if (dealGroup.getCategory() != null) {
            builder.categoryId(dealGroup.getCategory().getCategoryId())
                   .platformCategoryId(dealGroup.getCategory().getPlatformCategoryId())
                   .serviceType(dealGroup.getCategory().getServiceType())
                   .serviceTypeId(dealGroup.getCategory().getServiceTypeId());
        }

        // 从扩展属性中提取类目信息（主要用于泛商品）
        if (CollectionUtils.isNotEmpty(dealGroup.getAttrs())) {
            for (AttrDTO attr : dealGroup.getAttrs()) {
                if (CollectionUtils.isNotEmpty(attr.getValue())) {
                    String value = attr.getValue().get(0);
                    switch (attr.getName()) {
                        case "category3":
                            builder.thirdCategoryId(value);
                            break;
                        case "category4":
                            builder.fourthCategoryId(value);
                            break;
                        case "category5":
                            builder.fifthCategoryId(value);
                            break;
                    }
                }
            }
        }

        return builder.build();
    }

    /**
     * 转换商品标签信息
     */
    private List<ProductTag> convertTags(DealGroupDTO dealGroup) {
        if (CollectionUtils.isEmpty(dealGroup.getTags())) {
            return Collections.emptyList();
        }

        List<ProductTag> tags = new ArrayList<>();
        for (DealGroupTagDTO tagDTO : dealGroup.getTags()) {
            ProductTag tag = ProductTag.builder()
                    .tagId(tagDTO.getId())
                    .tagName(tagDTO.getTagName())
                    .build();
            tags.add(tag);
        }
        return tags;
    }

    /**
     * 转换扩展属性为键值对
     */
    private Map<String, String> convertAttributes(DealGroupDTO dealGroup) {
        if (CollectionUtils.isEmpty(dealGroup.getAttrs())) {
            return Collections.emptyMap();
        }

        Map<String, String> attributes = new HashMap<>();
        for (AttrDTO attr : dealGroup.getAttrs()) {
            if (CollectionUtils.isNotEmpty(attr.getValue())) {
                // 取第一个值作为属性值
                attributes.put(attr.getName(), attr.getValue().get(0));
            }
        }
        return attributes;
    }
}

