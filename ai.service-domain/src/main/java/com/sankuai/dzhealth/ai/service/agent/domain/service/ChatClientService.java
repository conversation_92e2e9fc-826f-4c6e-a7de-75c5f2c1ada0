package com.sankuai.dzhealth.ai.service.agent.domain.service;

import com.dianping.cat.Cat;
import com.dianping.cat.util.MetricHelper;
import com.dianping.rhino.Rhino;
import com.dianping.rhino.threadpool.DefaultThreadPoolProperties;
import com.dianping.rhino.threadpool.ThreadPool;
import com.google.common.collect.Lists;
import com.meituan.mdp.ai.friday.ExtraBodyConfig;
import com.meituan.mdp.ai.friday.FridayChatModel;
import com.meituan.mdp.ai.friday.FridayChatOptions;
import com.meituan.mdp.ai.friday.api.FridayApi;
import com.meituan.mdp.ai.friday.api.ResponseFormat;
import com.meituan.mdp.boot.starter.config.annotation.MdpConfig;
import com.meituan.mdp.boot.starter.util.Pair;
import com.meituan.service.inf.kms.client.Kms;
import com.meituan.service.inf.kms.utils.KmsResultNullException;
import com.sankuai.dzhealth.ai.service.agent.domain.aspect.OutputProcessor;
import com.sankuai.dzhealth.ai.service.agent.domain.aspect.PreOutputProcessor;
import com.sankuai.dzhealth.ai.service.agent.domain.buffer.MarkBuffer;
import com.sankuai.dzhealth.ai.service.agent.domain.buffer.MessageBuffer;
import com.sankuai.dzhealth.ai.service.agent.domain.buffer.MessageBufferEntity;
import com.sankuai.dzhealth.ai.service.agent.domain.context.ChatClientContext;
import com.sankuai.dzhealth.ai.service.agent.domain.context.MessageContext;
import com.sankuai.dzhealth.ai.service.agent.domain.memory.ChatHistoryMemory;
import com.sankuai.dzhealth.ai.service.agent.domain.memory.ChatHistoryMemoryAdvisor;
import com.sankuai.dzhealth.ai.service.agent.domain.tools.GeocodingTool;
import com.sankuai.dzhealth.ai.service.agent.domain.tools.MedicalBeautyQueryTool;
import com.sankuai.dzhealth.ai.service.agent.domain.tools.MedicalBeautyWebSearchTool;
import com.sankuai.dzhealth.ai.service.agent.domain.utils.BeanContextUtils;
import com.sankuai.dzhealth.ai.service.domain.utils.context.RequestContext;
import com.sankuai.dzhealth.ai.service.domain.utils.context.RequestContextConstant;
import com.sankuai.dzhealth.ai.service.dto.SseAwareException;
import com.sankuai.dzhealth.ai.service.enums.BufferItemTypeEnum;
import com.sankuai.dzhealth.ai.service.infrastructure.security.PorscheAuditAcl;
import io.micrometer.observation.ObservationRegistry;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.memory.ChatMemory;
import org.springframework.ai.chat.messages.AbstractMessage;
import org.springframework.ai.chat.model.ChatResponse;
import org.springframework.ai.chat.model.Generation;
import org.springframework.ai.deepseek.DeepSeekAssistantMessage;
import org.springframework.ai.support.ToolCallbacks;
import org.springframework.ai.tool.ToolCallback;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;



/**
 * @author:chenwei
 * @time: 2025/7/11 14:12
 * @version: 0.0.1
 */

@Slf4j
@Service
public class ChatClientService {


    @Autowired
    private ChatHistoryMemory chatHistoryMemory;

    @Autowired
    private PorscheAuditAcl porscheAuditAcl;

    @Autowired
    private ObservationRegistry observationRegistry;


    @Autowired
    private GeocodingTool geocodingTool;

    @Autowired
    private MedicalBeautyQueryTool medicalBeautyQueryTool;

    @Autowired
    private MedicalBeautyWebSearchTool medicalBeautyWebSearchTool;

    @MdpConfig("chat.audit.type:100220")
    private Integer type = 100220;

    @MdpConfig("chat.special.task")
    private String[] specialTypeList;

    public static final ThreadPool AUDIT_POOL = Rhino.newThreadPool("auditPool",
            DefaultThreadPoolProperties.Setter().withCoreSize(100).withMaxSize(200).withMaxQueueSize(500));
    
    @Autowired
    private EncyclopediaService encyclopediaService;

    @Autowired
    private PreOutputProcessor preOutputProcessor;

    @Autowired
    private OutputProcessor outputProcessor;


    // 思考模型需要单独创建
    public CompletableFuture<String> fridayChatStream(ChatClientContext context) throws KmsResultNullException, SseAwareException {
        Cat.logEvent("ChatClientService", "fridayChatStream." + context.getTaskType());
        FridayApi fridayApi = FridayApi.builder()
                .completionsPath("/chat/completions")
                .baseUrl("https://aigc.sankuai.com/v1/openai/native")
                .apiKey(Kms.getByName("com.sankuai.dzhealth.ai.service", context.getKmsKey()))
                .build();

        ToolCallback[] toolCallbacks = getToolCallbacks(context);

        FridayChatOptions fridayChatOptions = FridayChatOptions.builder()
                .model(StringUtils.isBlank(context.getModel()) ? "deepseek-v3-friday" : context.getModel())
                .temperature(0.1)
                .extraBodyConfig(ExtraBodyConfig.builder().properties(context.getExtraBodyConfig()).build())
                .maxTokens(Optional.ofNullable(context.getMaxTokens()).orElse(20000))
                .build();

        FridayChatModel fridayChatModel = FridayChatModel.builder()
                .fridayApi(fridayApi)
                .observationRegistry(observationRegistry)
                .defaultOptions(fridayChatOptions)
                .build();

        ChatClient fridayClient = ChatClient.builder(fridayChatModel, observationRegistry, null)
                .defaultAdvisors(ChatHistoryMemoryAdvisor.builder(chatHistoryMemory).build())
                .build();


        final String[] answer = new String[1];

        context.setMarkBuffer(new MarkBuffer(encyclopediaService.getEncyclopediaWords()));
        AtomicBoolean hasFirst = new AtomicBoolean(true);
        CompletableFuture<String> future = new CompletableFuture<>();
        // 统计大模型ttft和时间消耗
        long startTime = System.currentTimeMillis();
        AtomicBoolean receiveFirstToken = new AtomicBoolean(false);
        StringBuilder sb = new StringBuilder();
        AtomicInteger auditIndex = new AtomicInteger(0);
        // 在主线程中获取MessageBuffer引用
        MessageBuffer buffer = RequestContext.getAttribute(RequestContextConstant.MESSAGE_BUFFER);
        fridayClient.prompt()
                .system(context.getSystemPrompt())
                .user(context.getUserPrompt())
                .advisors(advisorSpec ->
                        advisorSpec.param(ChatMemory.CONVERSATION_ID, context.getMessageContext().getSessionId()))
                .toolCallbacks(toolCallbacks)
                .stream().chatResponse().flatMapSequential(chatResponse -> {
                            String content = Optional.ofNullable(chatResponse)
                                    .map(ChatResponse::getResult)
                                    .map(Generation::getOutput)
                                    .map(AbstractMessage::getText)
                                    .orElse(org.apache.commons.lang3.StringUtils.EMPTY);
                            if (StringUtils.isBlank(content)) {
                                return Mono.empty();
                            }
                            return Mono.fromFuture(CompletableFuture.supplyAsync(() ->
                                    audit(false, content, context.getMessageContext(), auditIndex), AUDIT_POOL.getExecutor()));
                        }
                ).subscribe(pair -> {
                            if (pair.getKey()) { // thinking
                                if (StringUtils.isNotBlank(pair.getValue())) {
                                    // 记录输出第一个token消耗的时间
                                    if (receiveFirstToken.compareAndSet(false, true)) {
                                        MetricHelper.build().name("大模型耗时打点")
                                        .tag("taskType", context.getTaskType())
                                        .tag("metricType", "ttft")
                                        .value(System.currentTimeMillis() - startTime);
                                    }
                                    // 直接使用buffer引用调用writeBufferData方法
                                    if (buffer != null) {
                                        buffer.writeBufferData(Collections.singletonList(
                                            MessageBufferEntity.builder()
                                                .data(pair.getValue())
                                                .type(BufferItemTypeEnum.THINK_PROCESS_STREAM.getType())
                                                .build()
                                        ), buffer);
                                    }
                                }
                            } else { // main text
                                if (StringUtils.isNotBlank(pair.getValue())) {
                                    // 记录输出第一个token消耗的时间
                                    if (receiveFirstToken.compareAndSet(false, true)) {
                                        MetricHelper.build().name("大模型耗时打点")
                                                .tag("taskType", context.getTaskType())
                                                .tag("metricType", "ttft")
                                                .value(System.currentTimeMillis() - startTime);
                                    }
                                    // 处理首字符输出前的逻辑
                                    preOutputProcessor.processPreFirstOutput(context, buffer, hasFirst);

                                    // 处理文本输出
                                    outputProcessor.processTextOutput(pair.getValue(), context, buffer, sb);
                                }

                            }
                        },
                        error -> {
                            log.error(error.getMessage(), error);
                        },
                        () -> {
                            // 处理完成时的剩余内容
                            outputProcessor.processCompletionOutput(context, buffer, sb);
                            answer[0] = sb.toString();
                            future.complete(answer[0]);
                            // 记录总消耗时间
                            MetricHelper.build().name("大模型耗时打点")
                                    .tag("taskType", context.getTaskType())
                                    .tag("metricType", "costTime")
                                    .value(System.currentTimeMillis() - startTime);
                        });
        return future;
    }



    public CompletableFuture<String> fridayThinkChatStream(ChatClientContext context) throws KmsResultNullException, SseAwareException {

        FridayApi fridayApi = FridayApi.builder()
                .completionsPath("/chat/completions")
                .baseUrl("https://aigc.sankuai.com/v1/openai/native")
                .apiKey(Kms.getByName("com.sankuai.dzhealth.ai.service", context.getKmsKey()))
                .build();

        ToolCallback[] toolCallbacks = getToolCallbacks(context);

        FridayChatOptions fridayChatOptions = FridayChatOptions.builder()
                .model(StringUtils.isBlank(context.getModel()) ? "deepseek-v3-friday" : context.getModel())
                .temperature(0.1)
                .extraBodyConfig(ExtraBodyConfig.builder().properties(context.getExtraBodyConfig()).build())
                .maxTokens(Optional.ofNullable(context.getMaxTokens()).orElse(20000))
                .build();

        FridayChatModel fridayChatModel = FridayChatModel.builder()
                .fridayApi(fridayApi)
                .defaultOptions(fridayChatOptions)
                .build();

        ChatClient fridayClient = ChatClient.builder(fridayChatModel)
                .defaultAdvisors(ChatHistoryMemoryAdvisor.builder(chatHistoryMemory).build())
                .build();


        final String[] answer = new String[1];
        AtomicBoolean hasFirst = new AtomicBoolean(true);
        CompletableFuture<String> future = new CompletableFuture<>();
        StringBuilder sb = new StringBuilder();
        AtomicInteger auditIndex = new AtomicInteger(0);
        // 在主线程中获取MessageBuffer引用
        MessageBuffer buffer = RequestContext.getAttribute(RequestContextConstant.MESSAGE_BUFFER);
        fridayClient.prompt()
                .system(context.getSystemPrompt())
                .user(context.getUserPrompt())
                .advisors(advisorSpec ->
                        advisorSpec.param(ChatMemory.CONVERSATION_ID, context.getMessageContext().getSessionId()))
                .toolCallbacks(toolCallbacks)
                .stream().chatResponse().flatMapSequential(chatResponse -> {
                    if (chatResponse != null && CollectionUtils.isNotEmpty(chatResponse.getResults())) {
                        for (Generation generation : chatResponse.getResults()) {
                            DeepSeekAssistantMessage deepSeekAssistantMessage = (DeepSeekAssistantMessage) generation.getOutput();
                            String reasoning = Optional.ofNullable(deepSeekAssistantMessage.getReasoningContent()).orElse("");
                            if (StringUtils.isNotBlank(reasoning)) {
                                return Mono.fromFuture(CompletableFuture.supplyAsync(() ->
                                        audit(true ,reasoning, context.getMessageContext(), auditIndex), AUDIT_POOL.getExecutor()));

                            }
                            String content = Optional.ofNullable(deepSeekAssistantMessage.getText()).orElse("");
                            if (StringUtils.isNotBlank(content)) {
                                return Mono.fromFuture(CompletableFuture.supplyAsync(() ->
                                        audit(false ,content, context.getMessageContext(), auditIndex), AUDIT_POOL.getExecutor()));
                            }
                        }
                    }
                    return Mono.empty();
                }).subscribe(pair -> {
                            if (pair.getKey()) { // thinking
                                if (StringUtils.isNotBlank(pair.getValue())) {
                                    // 直接使用buffer引用调用writeBufferData方法
                                    if (buffer != null) {
                                        buffer.writeBufferData(Collections.singletonList(
                                                MessageBufferEntity.builder()
                                                        .data(pair.getValue())
                                                        .type(BufferItemTypeEnum.THINK_PROCESS_STREAM.getType())
                                                        .build()
                                        ), buffer);
                                    }
                                }
                            } else { // main text
                                if (StringUtils.isNotBlank(pair.getValue())) {
                                    // 处理首字符输出前的逻辑
                                    preOutputProcessor.processPreFirstOutput(context, buffer, hasFirst);

                                    // 处理文本输出
                                    outputProcessor.processTextOutput(pair.getValue(), context, buffer, sb);
                                }

                            }
                        },
                        error -> {
                            log.error(error.getMessage(), error);
                        },
                        () -> {
                            // 处理完成时的剩余内容
                            outputProcessor.processCompletionOutput(context, buffer, sb);
                            answer[0] = sb.toString();
                            log.info("<time>friday_complete={},info={}", System.currentTimeMillis(), answer[0]);
                            future.complete(answer[0]);
                        });
        return future;
    }

    //
    public Flux<String> fridayChatStreamFlux(ChatClientContext context) throws KmsResultNullException, SseAwareException {
        Cat.logEvent("ChatClientService", "fridayChatStreamFlux." + context.getTaskType());
        FridayApi fridayApi = FridayApi.builder()
                .completionsPath("/chat/completions")
                .baseUrl("https://aigc.sankuai.com/v1/openai/native")
                .apiKey(Kms.getByName("com.sankuai.dzhealth.ai.service", context.getKmsKey()))
                .build();

        ToolCallback[] toolCallbacks = getToolCallbacks(context);

        FridayChatOptions fridayChatOptions = FridayChatOptions.builder()
                .model(StringUtils.isBlank(context.getModel()) ? "deepseek-v3-friday" : context.getModel())
                .temperature(0.1)
                .extraBodyConfig(ExtraBodyConfig.builder().properties(context.getExtraBodyConfig()).build())
                .maxTokens(Optional.ofNullable(context.getMaxTokens()).orElse(20000))
                .build();

        FridayChatModel fridayChatModel = FridayChatModel.builder()
                .fridayApi(fridayApi)
                .defaultOptions(fridayChatOptions)
                .build();

        ChatClient chatClient = ChatClient.builder(fridayChatModel)
                .defaultAdvisors(ChatHistoryMemoryAdvisor.builder(chatHistoryMemory).build())
                .build();

        AtomicInteger auditIndex = new AtomicInteger(0);

        return chatClient.prompt()
                .system(context.getSystemPrompt())
                .user(context.getUserPrompt())
                .advisors(advisorSpec ->
                        advisorSpec.param(
                                ChatMemory.CONVERSATION_ID, context.getMessageContext().getSessionId()))
                .toolCallbacks(toolCallbacks)
                .stream().chatResponse().flatMapSequential(chatResponse -> {
                            String content = Optional.ofNullable(chatResponse)
                                    .map(ChatResponse::getResult)
                                    .map(Generation::getOutput)
                                    .map(AbstractMessage::getText)
                                    .orElse(StringUtils.EMPTY);
                            if (StringUtils.isBlank(content)) {
                                return Mono.empty();
                            }
                            return Mono.fromFuture(CompletableFuture.supplyAsync(() ->
                                    audit(false, content, context.getMessageContext(), auditIndex), AUDIT_POOL.getExecutor()));

                        }
                ).map(Pair::getValue);

    }

    public String fridayChatCall(ChatClientContext context) throws KmsResultNullException, SseAwareException {
        Cat.logEvent("ChatClientService", "fridayChatCall." + context.getTaskType());
        FridayApi fridayApi = FridayApi.builder()
                .completionsPath("/chat/completions")
                .baseUrl("https://aigc.sankuai.com/v1/openai/native")
                .apiKey(Kms.getByName("com.sankuai.dzhealth.ai.service", context.getKmsKey()))
                .build();

        ToolCallback[] toolCallbacks = getToolCallbacks(context);

        FridayChatOptions fridayChatOptions = FridayChatOptions.builder()
                .model(StringUtils.isBlank(context.getModel()) ? "deepseek-v3-friday" : context.getModel())
                .temperature(0.1)
                .extraBodyConfig(ExtraBodyConfig.builder().properties(context.getExtraBodyConfig()).build())
                .maxTokens(Optional.ofNullable(context.getMaxTokens()).orElse(20000))
                .build();

        FridayChatModel fridayChatModel = FridayChatModel.builder()
                .fridayApi(fridayApi)
                .defaultOptions(fridayChatOptions)
                .build();

        ChatClient chatClient = ChatClient.builder(fridayChatModel)
                .defaultAdvisors(ChatHistoryMemoryAdvisor.builder(chatHistoryMemory).build())
                .build();

        // 在主线程中获取MessageBuffer引用
        MessageBuffer buffer = RequestContext.getAttribute(RequestContextConstant.MESSAGE_BUFFER);
        long start = System.currentTimeMillis();
        String content = chatClient.prompt()
                .system(context.getSystemPrompt())
                .user(context.getUserPrompt())
                .advisors(advisorSpec ->
                        advisorSpec.param(
                                        ChatMemory.CONVERSATION_ID, context.getMessageContext().getSessionId()))
                .toolCallbacks(toolCallbacks)
                .call().content();
        MetricHelper.build().name("大模型耗时打点")
                .tag("metricType", "costTime")
                .tag("taskType", context.getTaskType())
                .value(System.currentTimeMillis() - start);
        Pair<Boolean, String> auditRes = audit(false, content, context.getMessageContext(), new AtomicInteger(0));
        if (StringUtils.isNotBlank(content) && buffer != null) {
            buffer.writeBufferData(Collections.singletonList(
                    MessageBufferEntity.builder()
                            .data(content)
                            .type(BufferItemTypeEnum.MAIN_TEXT.getType())
                            .build()
            ), buffer);
        }
        return content;

    }

    private ToolCallback[] getToolCallbacks(ChatClientContext context) {
        List<String> toolNames = Lists.newArrayList("dateTimeToolTest");
        List<Object> toolCallObject = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(context.getToolNames())) {
            toolNames = context.getToolNames();
        }
        toolCallObject =  BeanContextUtils.getBeans(toolNames);
        return ToolCallbacks.from(toolCallObject.toArray());
    };


    //json格式输出
    public String fridayChatWithJson(ChatClientContext context) throws KmsResultNullException, SseAwareException {
        Cat.logEvent("ChatClientService", "fridayChatWithJson." + context.getTaskType());
        FridayApi fridayApi = FridayApi.builder()
                .completionsPath("/chat/completions")
                .baseUrl("https://aigc.sankuai.com/v1/openai/native")
                .apiKey(Kms.getByName("com.sankuai.dzhealth.ai.service", context.getKmsKey()))
                .build();

        ToolCallback[] toolCallbacks = getToolCallbacks(context);

        FridayChatOptions fridayChatOptions = FridayChatOptions.builder()
                .model(StringUtils.isBlank(context.getModel()) ? "deepseek-v3-friday" : context.getModel())
                .temperature(0.1)
                .extraBodyConfig(ExtraBodyConfig.builder().properties(context.getExtraBodyConfig()).build())
                .maxTokens(Optional.ofNullable(context.getMaxTokens()).orElse(20000))
                .responseFormat(ResponseFormat.builder().type(ResponseFormat.Type.JSON_OBJECT).build())
                .build();

        FridayChatModel fridayChatModel = FridayChatModel.builder()
                .fridayApi(fridayApi)
                .defaultOptions(fridayChatOptions)
                .build();

        ChatClient chatClient = ChatClient.builder(fridayChatModel)
                .defaultAdvisors(ChatHistoryMemoryAdvisor.builder(chatHistoryMemory).build())
                .build();


        long start = System.currentTimeMillis();
        String content = chatClient.prompt()
                .system(context.getSystemPrompt())
                .user(context.getUserPrompt())
                .advisors(advisorSpec ->
                        advisorSpec.param(
                                ChatMemory.CONVERSATION_ID, context.getMessageContext().getSessionId()))
                .toolCallbacks(toolCallbacks)
                .call().content();
        MetricHelper.build().name("大模型耗时打点")
                .tag("metricType", "costTime")
                .tag("taskType", context.getTaskType())
                .value(System.currentTimeMillis() - start);
        AtomicInteger auditIndex = new AtomicInteger(0);
        Pair<Boolean, String> auditRes = audit(false, content, context.getMessageContext(), auditIndex);
        return auditRes.getValue();


    }




    private Pair<Boolean, String> audit(boolean isThinking ,String content, MessageContext messageContext, AtomicInteger auditIndex) {
        long bizId = NumberUtils.toLong(messageContext.getMsgId()) + auditIndex.getAndIncrement();
//        AuditResult contentAuditResult = porscheAuditAcl.audit(AuditRequest.builder()
//                .bizId(bizId)
//                .dataSource(9)
//                .transId(String.valueOf(bizId))
//                .userId(messageContext.getUserId())
//                .userIP(messageContext.getBasicParam().getIp())
//                .userSource(messageContext.getPlatform())
//                .assistantId(messageContext.getBizType())
//                .textBody(Collections.singletonList(AuditRequest.TextBody.builder()
//                        .name("AIGCText")
//                        .value(content)
//                        .desc("AIGCText")
//                        .build()))
//                .type(type)
//                .build());
//        if (!Objects.equals(contentAuditResult.getAdvice(), AuditResult.Advice.PASSED.getCode())) {
//            throw new SseAwareException(StreamEventErrorTypeEnum.AUDIT_ERROR);
//        }
        return new Pair<>(isThinking, content);
    }



}
