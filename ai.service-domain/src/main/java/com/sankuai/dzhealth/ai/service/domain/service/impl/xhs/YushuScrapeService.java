package com.sankuai.dzhealth.ai.service.domain.service.impl.xhs;

import com.meituan.mdp.boot.starter.config.annotation.MdpConfig;
import com.meituan.taskcenter.service.TaskPublishRequest;
import com.meituan.taskcenter.service.TaskService;
import com.sankuai.dzhealth.ai.service.domain.entity.xhs.TaskCenterData;
import com.sankuai.dzhealth.ai.service.domain.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @author:chenwei
 * @time: 2025/7/7 17:46
 * @version: 0.0.1
 */
@Slf4j
@Service
public class YushuScrapeService {

    @MdpConfig("scrap.xh.account.cookie:sensorsdata2015jssdkcross=%7B%22distinct_id%22%3A%22nr_pfzv4t083%22%2C%22first_id%22%3A%22197aaeeae8f1b1-045a63df218051-********-2007040-197aaeeae91193%22%2C%22props%22%3A%7B%22%24latest_traffic_source_type%22%3A%22%E8%87%AA%E7%84%B6%E6%90%9C%E7%B4%A2%E6%B5%81%E9%87%8F%22%2C%22%24latest_search_keyword%22%3A%22%E6%9C%AA%E5%8F%96%E5%88%B0%E5%80%BC%22%2C%22%24latest_referrer%22%3A%22https%3A%2F%2Fwww.google.com%2F%22%7D%2C%22identities%22%3A%22eyIkaWRlbnRpdHlfY29va2llX2lkIjoiMTk3YWFlZWFlOGYxYjEtMDQ1YTYzZGYyMTgwNTEtMTg1MjU2MzYtMjAwNzA0MC0xOTdhYWVlYWU5MTE5MyIsIiRpZGVudGl0eV9sb2dpbl9pZCI6Im5yX3BmenY0dDA4MyJ9%22%2C%22history_login_id%22%3A%7B%22name%22%3A%22%24identity_login_id%22%2C%22value%22%3A%22nr_pfzv4t083%22%7D%7D; acw_tc=0a472f9117509925444157599e0075a960e29ea07673b264027c62c871fcc2; auth_n=SAe4WU9waVqWOKFB7YgGe2Em7amVBu+Q//STh+4cCm5AcOeR0u4cPR3tsG/N3HPl; token=A4FCDE4F315846E4A9E9F09CE3F78172; tfstk=g2EqN5iafcV5Amcxouiwab4OOAmxAc5B7lGsIR2ihjcDflaz7WwKCte_GzlaabFZWthssfP_Ir65AMwYHcng76sCAyvdRAF2iqmGIbDInTMDhCpEHcnGO_aE3sxoXSbT7be0ETksCFYgsx0kZbH6ofVii4AoLvnij50iqgDIpAxmIAmlUAhoscmgs8XrCbciaphii3kKoOLL9HXGppuxt-cyjHvKKqXThFt20uMquR2maxk44xuqtV2Z9erUK-qKQlBH7fPToSM8_M5rxyFzb2mNb3nbLzVqzr5w44Uupl0zy68LpR2LS4rwitN3ZPPiezCw7XzghyESPdYooyFb-4r5g3h3r8yszzB9lfaKI53Lf6KxiR2LvyiF0BH4rJoP4uOtEXx4XrRM7qD-UX6PUQ2I02NIoTQeWF3uyYlChtT9WqcIUX6yCFLtr-DrOtnf.")
    private String cookie;
    @MdpConfig("yushu.prompt:点击标题，点击内容，点击标签，点击合作品牌，点击提及品类，点击种草品牌，点击点赞数，自定义点赞数100，点击确定，输入笔记标题处输入“%s”，点击搜索，依次在前30个图标处悬停")
    private String yushuPrompt;


    @Autowired
    private TaskService.Iface taskCenterService;


    public void scrape(String searchWord, String batch, List<TaskCenterData.CookieData> cookies, String prompt) throws TException {

        TaskPublishRequest taskPublishRequest = new TaskPublishRequest();
        taskPublishRequest.setAppName("xinhong");
        taskPublishRequest.setAction("xinhong");

        TaskCenterData taskCenterData = TaskCenterData.builder()
                .cookies(cookies)
                .url("https://xh.newrank.cn/notes/notesSearch")
                .prompt(String.format(StringUtils.isBlank(prompt) ? yushuPrompt : prompt, searchWord))
                .extra(TaskCenterData.ExtraData.builder()
                        .saveNote(true)
                        .batch(batch)
                        .build())
                .build();
        taskPublishRequest.setData(JsonUtils.toJsonString(taskCenterData));

        log.info("[TaskCenterResultConsumer] request={}", JsonUtils.toJsonString(taskPublishRequest));
        taskCenterService.taskPublish(taskPublishRequest);

    }
}
