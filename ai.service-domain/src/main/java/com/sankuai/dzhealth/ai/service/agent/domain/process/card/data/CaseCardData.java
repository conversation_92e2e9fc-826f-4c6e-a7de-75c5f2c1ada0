package com.sankuai.dzhealth.ai.service.agent.domain.process.card.data;

import com.sankuai.dzhealth.ai.service.agent.domain.model.experiencereport.AiCaseInfo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @author:chenwei
 * @time: 2025/7/23 10:11
 * @version: 0.0.1
 */

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CaseCardData {

    private String title;

    private String jumpUrl;

    private Long count;

    private List<AiCaseInfo> caseInfoList;


    public Map<String, Object> toMap() {
        Map<String, Object> map = new HashMap<>();
        map.put("caseInfoList", caseInfoList);
        map.put("jumpUrl", jumpUrl);
        map.put("needMore", count != null && count > 2);
        return map;
    }

}
