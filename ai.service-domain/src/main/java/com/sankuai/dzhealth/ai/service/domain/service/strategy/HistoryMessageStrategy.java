package com.sankuai.dzhealth.ai.service.domain.service.strategy;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import com.dianping.rhino.threadpool.ThreadPool;
import com.sankuai.dzhealth.ai.service.domain.chat.model.ChatSessionMessageBO;
import com.sankuai.dzhealth.ai.service.domain.chat.model.IntentionResult;
import com.sankuai.dzhealth.ai.service.domain.service.ChatSessionService;
import com.sankuai.dzhealth.ai.service.domain.utils.context.AiAnswerContext;
import com.sankuai.dzhealth.ai.service.infrastructure.observability.Span;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.Serializable;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.toList;

/**
 * @author:chenwei
 * @time: 2025/4/15 10:50
 * @version: 0.0.1
 */
@Slf4j
@Service
public class HistoryMessageStrategy implements DataSourceStrategy<HistoryMessageStrategy.HistoryMessageInfo>{

    @Autowired
    private ChatSessionService chatSessionService;

    private static final ThreadPool taskPool = ChatSessionService.TASK_POOL;


    /**
     * 是否应该执行该策略
     *
     * @param intentionResult 意图识别结果
     * @return 是否执行
     */
    @Override
    public boolean shouldExecute(IntentionResult intentionResult) {
        return true;
    }

    /**
     * 执行策略
     *
     * @param context     上下文
     * @param rewriteText 改写后的文本
     * @return 策略执行结果
     */
    @Override
    public CompletableFuture<HistoryMessageStrategy.HistoryMessageInfo> execute(AiAnswerContext context, String rewriteText) {
        return CompletableFuture.supplyAsync(() -> listHistoryMessage(context), taskPool.getExecutor());
    }

    private HistoryMessageInfo listHistoryMessage(AiAnswerContext context) {
        try {
            long startTime = System.currentTimeMillis();
            HistoryMessageInfo res = new HistoryMessageInfo();
            Integer count = Lion.getInt(Environment.getAppName(), "history.message.count", 10);
            List<ChatSessionMessageBO> chatSessionMessageBOS = chatSessionService.queryMsgBySessionId(context.getSessionId(), context.getUserId(), 1, count);
            List<HistoryMessageInfo.BaseMessageInfo> collect = chatSessionMessageBOS.size() > 1
                    ? chatSessionMessageBOS.stream()
                    .map(e -> {
                        HistoryMessageInfo.BaseMessageInfo baseMessageInfo = new HistoryMessageInfo.BaseMessageInfo();
                        baseMessageInfo.setRole(e.getRole());
                        String extractContent = "robot".equalsIgnoreCase(e.getRole()) ?
                                extractContent(e.getContent()) : e.getContent();
                        baseMessageInfo.setContent(extractContent);
                        return baseMessageInfo;
                    }).collect(Collectors.collectingAndThen(
                            Collectors.toList(),
                            list -> {
                                Collections.reverse(list);
                                return list;
                            }))
                    : Collections.emptyList();
            res.setHistoryInfoList(collect);
            res.setSpan(Collections.singletonList(Span.builder()
                    .key(getClass().getSimpleName())
                    .value(JSON.toJSONString(collect))
                    .duration(System.currentTimeMillis() - startTime)
                    .build()));
            return res;
        } catch (Exception e) {
            log.error("listHistoryMessage error", e);
            return null;
        }
    }

    private String extractContent(String jsonStr) {
        if (jsonStr == null || jsonStr.isEmpty()) {
            return "";
        }
        try {
            StringBuilder result = new StringBuilder();
            JSONObject json = JSON.parseObject(jsonStr);
            JSONObject data = json.getJSONObject("data");

            String content = data.getString("content");
            if (content != null) {
                int separatorIndex = content.indexOf(":::");
                if (separatorIndex != -1) {
                    result.append(content.substring(0, separatorIndex).trim());
                }
            }
            JSONArray cardsData = data.getJSONArray("cardsData");
            if (cardsData != null) {
                for (int i = 0; i < cardsData.size(); i++) {
                    JSONObject card = cardsData.getJSONObject(i);
                    if ("continueAsk".equals(card.getString("key"))) {
                        JSONObject cardProps = card.getJSONObject("cardProps");
                        String text = cardProps.getString("text");
                        if (text != null) {
                            result.append(text);
                        }
                        break;
                    }
                }
            }

            return result.toString();
        } catch (Exception e) {
            log.error("Failed to extract content from JSON: {}", jsonStr, e);
            return jsonStr;
        }
    }


    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @EqualsAndHashCode(callSuper = true)
    public static class HistoryMessageInfo extends BaseStrategyInfo {

        private List<BaseMessageInfo> historyInfoList;


        @Override
        public String toPrompt() {
            if (CollectionUtils.isEmpty(historyInfoList)) {
                return "[]";
            }
            List<String> jsonResults = historyInfoList.stream()
                    .map(e -> String.format("{\"内容\":\"%s\",\"角色\":\"%s\"}",
                            Optional.ofNullable(e.getContent()).orElse(""),
                            Optional.ofNullable(e.getRole()).map(role -> {
                                if ("robot".equals(role)) {
                                    return "system";
                                }
                                return "user";
                            }).orElse("")))
                    .collect(toList());

            return "[" + String.join(",", jsonResults) + "]";
        }

        @Data
        public static class BaseMessageInfo implements Serializable {

            private String role;

            private String content;
        }
    }
}
