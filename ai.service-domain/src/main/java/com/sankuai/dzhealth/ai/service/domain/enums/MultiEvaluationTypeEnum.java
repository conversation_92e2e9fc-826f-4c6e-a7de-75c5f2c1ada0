package com.sankuai.dzhealth.ai.service.domain.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @since : 2025/7/29 20:54
 */
@AllArgsConstructor
@Getter

public enum MultiEvaluationTypeEnum {


    SINGLE("0", "单轮对话"),

    MULTI("1", "多轮对话"),
    SINGLE_MULTI("2", "单轮多轮对话");

    public final String type;
    public final String msg;


}
