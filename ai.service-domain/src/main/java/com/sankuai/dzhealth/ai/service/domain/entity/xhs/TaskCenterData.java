package com.sankuai.dzhealth.ai.service.domain.entity.xhs;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * @author:chenwei
 * @time: 2025/6/20 17:32
 * @version: 0.0.1
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TaskCenterData implements Serializable {
    private String url;
    private String prompt;
    private List<CookieData> cookies;
    private ExtraData extra;

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ExtraData implements Serializable {
        private String batch;
        @JsonProperty("isSaveNote")
        private boolean saveNote = true;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class CookieData implements Serializable {

        @JsonProperty("name")
        private String name;

        @JsonProperty("value")
        private String value;

        @JsonProperty("domain")
        private String domain;

        @JsonProperty("path")
        private String path;

        @JsonProperty("expires")
        private Double expires;

        @JsonProperty("httpOnly")
        private Boolean httpOnly = false;

        @JsonProperty("secure")
        private Boolean secure = false;

        @JsonProperty("sameSite")
        private String sameSite;
    }
}
