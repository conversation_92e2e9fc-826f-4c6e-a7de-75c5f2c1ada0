package com.sankuai.dzhealth.ai.service.domain.service.strategy;

import com.sankuai.dzhealth.ai.service.infrastructure.observability.Span;
import lombok.Data;

import java.util.List;

/**
 * @author:chenwei
 * @time: 2025/4/1 22:41
 * @version: 0.0.1
 */
@Data
public abstract class BaseStrategyInfo {

    List<Span> span;

    public abstract String toPrompt();

    public void setSpan(List<Span> span) {
        // 若传入列表为 null，则保持 null；否则包装为可修改的 ArrayList，避免出现不可修改集合导致的 UnsupportedOperationException
        this.span = span == null ? null : new java.util.ArrayList<>(span);
    }

}
