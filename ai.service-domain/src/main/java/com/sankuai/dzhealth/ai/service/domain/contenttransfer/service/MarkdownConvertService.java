package com.sankuai.dzhealth.ai.service.domain.contenttransfer.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

/**
 * Markdown转换领域服务，负责内容转换的核心业务逻辑
 *
 * <AUTHOR> based on 到店架构标准化规范
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MarkdownConvertService {

    private static final String CONVERSION_PROMPT = "你是一个专业的Markdown转换工具。将用户提供的内容转换为Markdown格式。\n\n" +
            "核心要求：\n" +
            "- 直接输出纯净的Markdown内容，不添加任何说明、引导语或解释文字\n" +
            "- 保持标题层次结构清晰合理\n" +
            "图片处理：\n" +
            "- 发现图片URL时使用analyzeImage工具，传递图片前后的相关文字作为context参数，生成的图片描述结果要自然地替换图片位置，语调和风格与周围文字保持一致\n" +
            "请开始转换以下内容：";

    @Qualifier("markdownConvertChatClient")
    private final ChatClient markdownConvertChatClient;

    /**
     * 将原始内容转换为Markdown格式
     * 模型会自动识别图片并调用tool进行分析
     *
     * @param originContent 原始内容
     * @return 转换后的Markdown内容
     * @throws Exception 转换过程中的异常
     */
    public String convertToMarkdown(String originContent) throws Exception {
        validateInput(originContent);

        // 使用LLM转换为Markdown，模型会自动调用ImageAnalysisTool处理图片
        return convertContentWithLLM(originContent);
    }

    /**
     * 验证输入参数
     */
    private void validateInput(String originContent) {
        if (StringUtils.isBlank(originContent)) {
            throw new IllegalArgumentException("原始内容不能为空");
        }
    }

    /**
     * 使用LLM转换内容为Markdown
     */
    private String convertContentWithLLM(String originContent){

        // 使用SpringAI的ChatClient进行调用
        String response = markdownConvertChatClient.prompt()
                .system(CONVERSION_PROMPT)
                .user(originContent)
                .call()
                .content();

        // 清理markdown代码块标记（如果存在）
        return cleanMarkdownWrapper(response);
    }

    /**
     * 基本清理：仅移除代码块标记（通过优化后的Prompt，LLM应该一次性产生干净结果）
     */
    private String cleanMarkdownWrapper(String content) {
        if (StringUtils.isBlank(content)) {
            return content;
        }

        // 基本清理：移除可能的代码块标记
        String result = content.replace("```markdown", "").replace("```", "");

        // 清理多余的换行符
        result = result.replaceAll("^\\s*\\n+", "").replaceAll("\\n+\\s*$", "");

        return result.trim();
    }


}