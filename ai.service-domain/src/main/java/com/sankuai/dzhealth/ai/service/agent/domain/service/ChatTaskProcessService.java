package com.sankuai.dzhealth.ai.service.agent.domain.service;

import com.dianping.haima.entity.haima.HaimaContent;
import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import com.dianping.rhino.Rhino;
import com.dianping.rhino.threadpool.DefaultThreadPoolProperties;
import com.dianping.rhino.threadpool.ThreadPool;
import com.sankuai.dzhealth.ai.service.agent.domain.buffer.MessageBuffer;
import com.sankuai.dzhealth.ai.service.agent.domain.buffer.MessageBufferService;
import com.sankuai.dzhealth.ai.service.agent.domain.context.AgentTaskConfig;
import com.sankuai.dzhealth.ai.service.agent.domain.context.MessageContext;
import com.sankuai.dzhealth.ai.service.agent.domain.context.TaskContext;
import com.sankuai.dzhealth.ai.service.agent.domain.memory.ChatHistoryMemory;
import com.sankuai.dzhealth.ai.service.agent.domain.model.TaskProcessResult;
import com.sankuai.dzhealth.ai.service.agent.domain.process.task.TaskConfig;
import com.sankuai.dzhealth.ai.service.agent.domain.process.task.TaskProcessor;
import com.sankuai.dzhealth.ai.service.domain.utils.JsonUtils;
import com.sankuai.dzhealth.ai.service.domain.utils.context.RequestContext;
import com.sankuai.dzhealth.ai.service.domain.utils.context.RequestContextConstant;
import com.sankuai.dzhealth.ai.service.dto.SseAwareException;
import com.sankuai.dzhealth.ai.service.dto.stream.BufferMergedDTO;
import com.sankuai.dzhealth.ai.service.infrastructure.acl.HaimaAcl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @author:chenwei
 * @time: 2025/7/10 17:27
 * @version: 0.0.1
 */

@Slf4j
@Service
public class ChatTaskProcessService {

    @Autowired
    private MessageBufferService messageBufferService;


    @Autowired
    private TaskProcessor taskProcessor;
    
    @Autowired
    private HaimaAcl haimaAcl;
    
    @Autowired
    private ChatHistoryMemory chatHistoryMemory;


    public static final ThreadPool SSE_STREAM_POOL = Rhino.newThreadPool("SSE_STREAM_POOL",
            DefaultThreadPoolProperties.Setter().withCoreSize(100).withMaxSize(200).withMaxQueueSize(1000));



    public BufferMergedDTO process(MessageContext context) {

        MessageBuffer buffer = null;

        try {

            paddingContext(context);

            RequestContext.setAttribute(RequestContextConstant.MESSAGE_CONTEXT, context);
            RequestContext.setAttribute(RequestContextConstant.BIZ_TYPE, context.getBizType());

            buffer = messageBufferService.createAndStartBufferConsumption();

            run(context);

            buffer.finishBufferConsume(buffer);

            return buffer.getBufferMerged();

        } catch (Exception e) {
            log.error("<ChatTaskProcessService.process>", e);
            throw new RuntimeException(e);
        } finally {
            if (buffer != null) {
                buffer.finishBufferConsume(buffer);
            }
        }
    }



    // 完善配置信息
    private void paddingContext(MessageContext context) {
        Map<String, AgentTaskConfig> config = Lion.getMap(Environment.getAppName(), "agent.task.config", AgentTaskConfig.class);
        if (MapUtils.isEmpty(config) || !config.containsKey(context.getBizType())) {
            throw new RuntimeException("<AgentConfig is empty!>");
        }
        AgentTaskConfig agentTaskConfig = config.get(context.getBizType());

        // 海马覆盖lion配置，lion主要配置mainTask以及subTask以及label
        if (StringUtils.isNotBlank(agentTaskConfig.getHaimaKey())) {
            List<HaimaContent> medicalTaskAiConfig = haimaAcl.getContent("medical_task_ai_config", null);
            Map<String, TaskConfig> configMap = medicalTaskAiConfig.stream().collect(
                    Collectors.toMap(e -> e.getContentString("task"),
                            e -> {
                                TaskConfig cur = JsonUtils.parseObject(e.getContentString("config"), TaskConfig.class);
                                if (cur == null) {
                                    return createDefaultTaskConfig();
                                }
                                if (StringUtils.isNotBlank(e.getContentString("systemPrompt"))) {
                                    cur.setSystemPrompt(e.getContentString("systemPrompt"));
                                }
                                return cur;
                                }, (a, b) -> a));

            String mainTaskType = agentTaskConfig.getMainTask().getType();
            if (configMap.containsKey(mainTaskType)) {
                agentTaskConfig.setMainTask(configMap.get(mainTaskType));
            }

            // 替换子任务配置
            List<TaskConfig> updatedSubTasks = agentTaskConfig.getSubTask().stream()
                    .map(subTask -> {
                        if (configMap.containsKey(subTask.getType())) {
                            return configMap.get(subTask.getType());
                        }
                        return subTask;
                    })
                    .collect(Collectors.toList());
            agentTaskConfig.setSubTask(updatedSubTasks);
        }
        context.setAgentTaskConfig(agentTaskConfig);
        context.setMultiEvaluationRequests(new ArrayList<>());
    }

    private TaskConfig createDefaultTaskConfig() {
        TaskConfig taskConfig = new TaskConfig();
        taskConfig.setModel("deepseek-v3-friday");
        taskConfig.setStream(false);
        taskConfig.setSystemPrompt("");
        taskConfig.setKmsKey("friday.search.appId");
        taskConfig.setType("medicalMemoryUpdate");
        return taskConfig;
    }

    private void run(MessageContext context) {

        TaskContext mainTaskContext = taskProcessor.getMainTaskContext(context);

        List<TaskContext> subTaskContextList = taskProcessor.getSubTaskContext(context);
        try {
            List<CompletableFuture<TaskProcessResult>> subFutures = subTaskContextList.stream()
                    .map(subTask ->
                            CompletableFuture.supplyAsync(() -> taskProcessor.process(subTask),
                                    SSE_STREAM_POOL.getExecutor()))
                    .toList();


            TaskProcessResult mainResult = taskProcessor.process(mainTaskContext);
            RequestContext.setAttribute(RequestContextConstant.MAIN_TASK_RESULT, mainResult);
            CompletableFuture<Void> allSubTasks = CompletableFuture.allOf(
                    subFutures.toArray(new CompletableFuture[0]));

            try {
                allSubTasks.get(5, TimeUnit.SECONDS);
                // 获取子任务结果
                subFutures.stream()
                        .map(CompletableFuture::join)
                        .forEach(subResult -> {
                            taskProcessor.afterProcess(subResult, mainResult);
                        });
            } catch (Exception e) {
                log.error("子任务执行超时，超过2秒未完成", e);
            }
        } catch (SseAwareException e) {
            log.error("<process>run sse error", e);
            throw e;
        } catch (Exception e) {
            log.error("run异常", e);
            throw new RuntimeException(e);
        }



    }
}
