package com.sankuai.dzhealth.ai.service.agent.domain.strategy;

import com.sankuai.dzhealth.ai.service.agent.domain.strategy.impl.DefaultTaskTypeStrategy;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * TaskType 策略工厂
 * 负责管理和获取不同任务类型的策略实现
 * 
 * <AUTHOR>
 * @time 2025/1/15
 */
@Component
public class TaskTypeStrategyFactory {
    
    @Autowired
    private List<TaskTypeStrategy> strategies;
    
    @Autowired
    private DefaultTaskTypeStrategy defaultTaskTypeStrategy;
    
    private final Map<String, TaskTypeStrategy> strategyMap = new HashMap<>();
    
    @PostConstruct
    public void init() {
        for (TaskTypeStrategy strategy : strategies) {
            strategyMap.put(strategy.getTaskType(), strategy);
        }
    }
    
    /**
     * 根据任务类型获取对应的策略
     * 
     * @param taskType 任务类型
     * @return 策略实现
     */
    public TaskTypeStrategy getStrategy(String taskType) {
        // 首先检查是否有精确匹配的策略
        TaskTypeStrategy strategy = strategyMap.get(taskType);
        if (strategy != null) {
            return strategy;
        }
        // 返回默认策略
        return defaultTaskTypeStrategy;
    }
}
