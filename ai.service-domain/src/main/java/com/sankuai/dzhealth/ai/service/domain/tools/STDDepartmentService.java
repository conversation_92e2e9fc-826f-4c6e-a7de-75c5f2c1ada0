package com.sankuai.dzhealth.ai.service.domain.tools;

import com.fasterxml.jackson.core.type.TypeReference;
import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.meituan.mdp.boot.starter.mdpcache.core.Cache;
import com.meituan.mdp.boot.starter.mdpcache.core.builder.CaffeineCacheBuilder;
import com.meituan.mdp.boot.starter.pigeon.annotation.MdpPigeonClient;
import com.sankuai.beautycontent.beautytagapi.dto.tagtree.TagTreeNodeDTO;
import com.sankuai.beautycontent.beautytagapi.infotag.dto.tag.ContentBaseTagMetaDTO;
import com.sankuai.beautycontent.beautytagapi.infotag.facade.ContentTagQueryFacade;
import com.sankuai.beautycontent.beautytagapi.infotag.request.ContentBaseTagRequest;
import com.sankuai.beautycontent.beautytagapi.service.TagTreeFacade;
import lombok.Builder;
import lombok.Data;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR> youtianyu
 * @version : 0.1
 * @since : 2025/4/1
 */
@Service
public class STDDepartmentService {

    @MdpPigeonClient(url = "com.sankuai.beautycontent.beautytagapi.infotag.facade.ContentTagQueryFacade",
                     timeout = 1000)
    private ContentTagQueryFacade contentTagQueryFacade;

    @MdpPigeonClient(url = "com.sankuai.beautycontent.beautytagapi.service.TagTreeFacade",
            testTimeout = 3000, timeout = 1500)
    private TagTreeFacade tagTreeFacade;

    private final Cache cache =
            new CaffeineCacheBuilder().expireAfterWrite(1, TimeUnit.MINUTES).loader(key -> loadCache()).build();

    public Departments queryDepartments() {
        return Departments.builder().departments(cache.get("department", new TypeReference<>() {
        })).build();
    }

    private Map<String, Integer> loadCache() {
        ContentBaseTagRequest request = new ContentBaseTagRequest();
        request.setBizType(44);
        request.setStatus(1);
        RemoteResponse<List<ContentBaseTagMetaDTO>> baseTags = contentTagQueryFacade.queryBaseTags(request);
        TagTreeNodeDTO tagTreeNodeDTO = tagTreeFacade.loadTagTree(89);
        List<TagTreeNodeDTO> childNodeList = tagTreeNodeDTO.getChildNodeList();
        List<Long> firstNodeIds = childNodeList.stream().map(e -> e.getBaseTagDTO().getId()).collect(Collectors.toList());
        return baseTags.getData()
                .stream()
                .filter(e -> !firstNodeIds.contains(Long.valueOf(e.getId())))
                .collect(Collectors.toMap(ContentBaseTagMetaDTO::getName, ContentBaseTagMetaDTO::getId, (a, b) -> a));
    }

    @Builder
    @Data
    public static class Departments {
        Map<String, Integer> departments;
    }
}
