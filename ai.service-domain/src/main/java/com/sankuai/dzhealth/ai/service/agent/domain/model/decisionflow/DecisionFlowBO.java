package com.sankuai.dzhealth.ai.service.agent.domain.model.decisionflow;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 决策网络业务对象（纯领域层，不暴露给外部）
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DecisionFlowBO {
    private String bizScene;
    private List<DecisionNodeBO> nodes;
    private List<DecisionEdgeBO> edges;

    /**
     * 资源列表
     */
    private List<ResourceRecommendationBO> resources;

    /**
     * 节点资源关联关系
     */
    private List<NodeResourceRelationBO> relations;
} 