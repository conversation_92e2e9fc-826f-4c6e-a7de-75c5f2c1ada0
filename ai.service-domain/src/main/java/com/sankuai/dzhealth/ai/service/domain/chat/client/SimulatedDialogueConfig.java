package com.sankuai.dzhealth.ai.service.domain.chat.client;

import com.dianping.lion.Environment;
import com.meituan.service.inf.kms.client.Kms;
import com.meituan.service.inf.kms.utils.KmsResultNullException;
import com.sankuai.dzhealth.ai.service.infrastructure.chatmodel.OneApiChatModel;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.openai.OpenAiChatOptions;
import org.springframework.ai.openai.api.OpenAiApi;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

@Component
public class SimulatedDialogueConfig {

    @Bean("simulatedDialogueChatClient")
    public ChatClient simulatedDialogueChatClient() throws KmsResultNullException {
        OpenAiApi openAiApi = new OpenAiApi.Builder()
                .completionsPath("/chat/completions")
                .baseUrl("https://aigc.sankuai.com/v1/openai/native")
                .apiKey(Kms.getByName(Environment.getAppName(), "friday.agent.appId"))
                .build();
        OpenAiChatOptions openAiChatOptions = OpenAiChatOptions.builder()
//                .model("deepseek-v3-friday")
                .model("deepseek-r1-huawei")
                .temperature(0.0)
                .maxTokens(20000)
                .streamUsage(true)
                .build();
        OneApiChatModel chatModel = OneApiChatModel.builder()
                .openAiApi(openAiApi)
                .defaultOptions(openAiChatOptions)
                .build();

        return ChatClient.builder(chatModel).build();
    }
}
