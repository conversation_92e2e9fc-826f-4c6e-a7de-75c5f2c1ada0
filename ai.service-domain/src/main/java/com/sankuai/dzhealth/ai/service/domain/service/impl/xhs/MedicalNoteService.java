package com.sankuai.dzhealth.ai.service.domain.service.impl.xhs;

import com.alibaba.fastjson.JSON;
import com.dianping.haima.entity.haima.HaimaContent;
import com.dianping.rhino.Rhino;
import com.dianping.rhino.threadpool.DefaultThreadPoolProperties;
import com.dianping.rhino.threadpool.ThreadPool;
import com.dianping.squirrel.client.StoreKey;
import com.dianping.squirrel.client.impl.redis.RedisStoreClient;
import com.meituan.mdp.boot.starter.config.annotation.MdpConfig;
import com.meituan.mdp.boot.starter.util.Pair;
import com.meituan.taskcenter.service.TaskPublishRequest;
import com.meituan.taskcenter.service.TaskService;
import com.sankuai.dzhealth.ai.service.api.xhs.XhsNoteCallbackService;
import com.sankuai.dzhealth.ai.service.domain.entity.xhs.Direction;
import com.sankuai.dzhealth.ai.service.domain.entity.xhs.TaskCenterData;
import com.sankuai.dzhealth.ai.service.domain.utils.JsonUtils;
import com.sankuai.dzhealth.ai.service.infrastructure.acl.HaimaAcl;
import com.sankuai.dzhealth.ai.service.request.xhs.XhsBuildNoteRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class MedicalNoteService {

    @Autowired
    private TaskService.Iface taskCenterService;


    @MdpConfig("xh.account.cookie:sensorsdata2015jssdkcross=%7B%22distinct_id%22%3A%22nr_pfzv4t083%22%2C%22first_id%22%3A%22197aaeeae8f1b1-045a63df218051-********-2007040-197aaeeae91193%22%2C%22props%22%3A%7B%22%24latest_traffic_source_type%22%3A%22%E8%87%AA%E7%84%B6%E6%90%9C%E7%B4%A2%E6%B5%81%E9%87%8F%22%2C%22%24latest_search_keyword%22%3A%22%E6%9C%AA%E5%8F%96%E5%88%B0%E5%80%BC%22%2C%22%24latest_referrer%22%3A%22https%3A%2F%2Fwww.google.com%2F%22%7D%2C%22identities%22%3A%22eyIkaWRlbnRpdHlfY29va2llX2lkIjoiMTk3YWFlZWFlOGYxYjEtMDQ1YTYzZGYyMTgwNTEtMTg1MjU2MzYtMjAwNzA0MC0xOTdhYWVlYWU5MTE5MyIsIiRpZGVudGl0eV9sb2dpbl9pZCI6Im5yX3BmenY0dDA4MyJ9%22%2C%22history_login_id%22%3A%7B%22name%22%3A%22%24identity_login_id%22%2C%22value%22%3A%22nr_pfzv4t083%22%7D%7D; acw_tc=0a472f9117509925444157599e0075a960e29ea07673b264027c62c871fcc2; auth_n=SAe4WU9waVqWOKFB7YgGe2Em7amVBu+Q//STh+4cCm5AcOeR0u4cPR3tsG/N3HPl; token=A4FCDE4F315846E4A9E9F09CE3F78172; tfstk=g2EqN5iafcV5Amcxouiwab4OOAmxAc5B7lGsIR2ihjcDflaz7WwKCte_GzlaabFZWthssfP_Ir65AMwYHcng76sCAyvdRAF2iqmGIbDInTMDhCpEHcnGO_aE3sxoXSbT7be0ETksCFYgsx0kZbH6ofVii4AoLvnij50iqgDIpAxmIAmlUAhoscmgs8XrCbciaphii3kKoOLL9HXGppuxt-cyjHvKKqXThFt20uMquR2maxk44xuqtV2Z9erUK-qKQlBH7fPToSM8_M5rxyFzb2mNb3nbLzVqzr5w44Uupl0zy68LpR2LS4rwitN3ZPPiezCw7XzghyESPdYooyFb-4r5g3h3r8yszzB9lfaKI53Lf6KxiR2LvyiF0BH4rJoP4uOtEXx4XrRM7qD-UX6PUQ2I02NIoTQeWF3uyYlChtT9WqcIUX6yCFLtr-DrOtnf.")
    private String cookie;
    @MdpConfig("yushu.analyse.prompt:点击标题，点击内容，点击标签，点击合作品牌，点击提及品类，点击种草品牌，点击点赞数，自定义点赞数100，点击确定，输入笔记标题处输入“%s”，点击搜索，依次在前30个图标处悬停")
    private String yushuAnalysePrompt;


    // 用于缓存请求上下文的Cache，以batchId为key
    @Autowired
    @Qualifier("redisClient0")
    private RedisStoreClient redisStoreClient0;


    @Autowired
    private YushuScrapeService yushuScrapeService;


    @Autowired
    private HaimaAcl haimaAcl;

    @Autowired
    private XhsNoteDataEnricher dataEnricher;

    @Autowired
    private MedicalNotePromptBuilder promptBuilder;

    @Autowired
    private XhsNoteGenerator noteGenerator;

    @Autowired
    private CallbackServiceFactory callbackServiceFactory;

    public static final ThreadPool MEDICAL_TASK_POOL = Rhino.newThreadPool("medicalNote",
            DefaultThreadPoolProperties.Setter().withCoreSize(100).withMaxSize(200).withMaxQueueSize(1000));

    public static final ThreadPool DELAY_TASK_POOL = Rhino.newThreadPool("delayTask",
            DefaultThreadPoolProperties.Setter().withCoreSize(100).withMaxSize(200).withMaxQueueSize(1000));


    /**
     * 处理医疗笔记生成请求
     *
     * @param request 笔记生成请求
     */
    public void processMedicalNote(XhsBuildNoteRequest request) {
        List<String> products = JsonUtils.parseArray(request.getParams().get("product"), String.class);
        if (CollectionUtils.isEmpty(products)) {
            return;
        }
        List<TaskCenterData.CookieData> cookies = JsonUtils.parseArray(request.getCookie(), TaskCenterData.CookieData.class);
        if (CollectionUtils.isEmpty(cookies)) {
            throw new IllegalArgumentException("<cookie is error>");
        }

        for (String product : products) {
            String batchId = request.getBatch() + "-" + request.getBizCode() + "-" + product;
            try {
                StoreKey storeKey = new StoreKey("xhs_request_cache", batchId);
                redisStoreClient0.set(storeKey, JsonUtils.toJsonString(request));

                yushuScrapeService.scrape(product, batchId, cookies, request.getPrompt());

                // 获取超时时间，默认为60秒
                long timeoutSeconds = request.getMaxTimeout() != null ? request.getMaxTimeout() : 600L;
                
                // 异步延迟调用processNote，不阻塞当前线程，使用自定义线程池
                CompletableFuture.delayedExecutor(timeoutSeconds, TimeUnit.SECONDS, DELAY_TASK_POOL.getExecutor())
                        .execute(() -> {
                            try {
                                log.info("开始处理超时后的笔记生成任务，batchId: {}, 延迟时间: {}秒", batchId, timeoutSeconds);
                                processNote(request, batchId, "兜底笔记");
                            } catch (Exception ex) {
                                log.error("延迟处理笔记生成任务失败，batchId: {}", batchId, ex);
                            }
                        });

            } catch (Exception e) {
                if (StringUtils.isNotEmpty(batchId)) {
                    log.error("yushuScrapeService failed for batchId: {}, cleared context cache", batchId);
                }
            }
        }

    }

    public void processAnalyseSafely(String batchId, String product) {
        try {
            TaskPublishRequest taskPublishRequest = new TaskPublishRequest();
            taskPublishRequest.setAppName("xinhong");
            taskPublishRequest.setAction("xinhong");


            TaskCenterData taskCenterData = TaskCenterData.builder()
                    .cookies(JSON.parseArray(cookie, TaskCenterData.CookieData.class))
                    .url("https://xh.newrank.cn/notes/notesSearch")
                    .prompt(String.format(yushuAnalysePrompt, product))
                    .extra(TaskCenterData.ExtraData.builder().saveNote(true).batch(batchId).build())
                    .build();
            taskPublishRequest.setData(JsonUtils.toJsonString(taskCenterData));

            log.info("[processAnalyse]request={}", JsonUtils.toJsonString(taskPublishRequest));
            taskCenterService.taskPublish(taskPublishRequest);
        } catch (Exception e) {
            log.error("[processAnalyse]batchId: {}, product={}", batchId, product, e);
        }
    }

    /**
     * 获取缓存的请求上下文
     *
     * @param batchId 批次ID
     * @return 原始请求对象，如果不存在则返回null
     */
    public XhsBuildNoteRequest getRequestContext(String batchId) {
        StoreKey storeKey = new StoreKey("xhs_request_cache", batchId);
        String cacheRequest =  redisStoreClient0.get(storeKey);
        return JsonUtils.parseObject(cacheRequest, XhsBuildNoteRequest.class);
    }

    /**
     * 清理请求上下文缓存
     *
     * @param batchId 批次ID
     */
    public void clearRequestContext(String batchId) {
        if (StringUtils.isNotBlank(batchId) && getRequestContext(batchId) != null) {
            StoreKey storeKey = new StoreKey("xhs_request_cache", batchId);
            redisStoreClient0.delete(storeKey);
            log.info("Cleared request context cache for batchId: {}", batchId);
        }
    }

    public void processNote(XhsBuildNoteRequest originalRequest, String batchId, String msg) {
        try {
            if (getRequestContext(batchId) == null) {
                return;
            }
            List<HaimaContent> basePromptConfigs = haimaAcl.getContent("xhs_note_base_prompt", null);
            Pair<String, String> promptConfig = getPromptConfig("medical", "medical_cover", basePromptConfigs);
            String basePrompt = promptConfig.getKey();
            String baseCoverPrompt = promptConfig.getValue();

            Map<String, String> params = originalRequest.getParams();
            int count = NumberUtils.toInt(params.get("count"), 5);

            String product = extractSuffixFromBatchId(batchId);

            List<Direction> allDirections = generateDirectionCombinations(params, product);


            log.info("note_handler,req={}", JsonUtils.toJsonString(originalRequest));
            XhsNoteCallbackService callbackService = callbackServiceFactory.getCallbackService(originalRequest.getCallbackAppkey());


            for (Direction direction : allDirections) {

                String hotNotes = dataEnricher.getHotNotes(direction.toString(), batchId);
                String knowledge = dataEnricher.getKnowledge(direction.toString());
                String noteTemplate = dataEnricher.getNoteTemplate(hotNotes);


                String notePrompt = promptBuilder.buildNotePrompt(direction, knowledge, hotNotes, noteTemplate, basePrompt);


                for (int j = 0; j < count; j++) {
                    CompletableFuture.runAsync(() -> {
                        noteGenerator.generateSingleNote(
                                notePrompt,
                                baseCoverPrompt,
                                originalRequest.getBatch(),
                                originalRequest.getBizCode(),
                                true,
                                callbackService,
                                msg
                        );
                    }, MEDICAL_TASK_POOL.getExecutor());
                }
            }
            log.info("Successfully submitted all medical note generation tasks for batchId: {}", batchId);
        } catch (Exception e) {
            log.error("[MedicalNoteGenerationHandler] 处理消息异常，batchId: {}", batchId, e);
        } finally {
            clearRequestContext(batchId);
        }
    }


    private List<Direction> generateDirectionCombinations(Map<String, String> params, String product) {
        List<String> projectList = JsonUtils.parseArray(params.get("project"), String.class);
        List<String> sceneList = JsonUtils.parseArray(params.get("scene"), String.class);
        List<String> personList = JsonUtils.parseArray(params.get("person"), String.class);

        List<Direction> combinations = new ArrayList<>();
        if (CollectionUtils.isEmpty(projectList) || CollectionUtils.isEmpty(sceneList)
                || CollectionUtils.isEmpty(personList)) {
            log.warn("One of the direction lists is empty, cannot generate combinations. Params: {}", params);
            return combinations;
        }

        for (String project : projectList) {
            for (String scene : sceneList) {
                for (String person : personList) {
                    Direction direction = Direction.builder()
                            .project(project)
                            .product(product)
                            .scene(scene)
                            .person(person)
                            .build();
                    combinations.add(direction);
                }
            }

        }
        return combinations;
    }



    private Pair<String, String> getPromptConfig(String firstBizCode, String secondBizCode, List<HaimaContent> basePromptConfigs) {
        String basePrompt = "";
        String baseCoverPrompt = "";
        if (CollectionUtils.isNotEmpty(basePromptConfigs)) {
            Optional<HaimaContent> joyConfig = basePromptConfigs.stream()
                    .filter(e -> firstBizCode.equals(e.getContentString("bizCode")))
                    .findFirst();
            if (joyConfig.isPresent()) {
                basePrompt = joyConfig.get().getContentString("promptTemplate");
            }

            Optional<HaimaContent> coverConfig = basePromptConfigs.stream()
                    .filter(e -> secondBizCode.equals(e.getContentString("bizCode")))
                    .findFirst();
            if (coverConfig.isPresent()) {
                baseCoverPrompt = coverConfig.get().getContentString("promptTemplate");
            }
        }
        return new Pair<>(basePrompt, baseCoverPrompt);
    }
    private String extractSuffixFromBatchId(String batchId) {
        if (batchId == null || batchId.isEmpty()) {
            return "";
        }

        String[] parts = batchId.split("-");
        return parts.length > 0 ? parts[parts.length - 1] : "";
    }
}
