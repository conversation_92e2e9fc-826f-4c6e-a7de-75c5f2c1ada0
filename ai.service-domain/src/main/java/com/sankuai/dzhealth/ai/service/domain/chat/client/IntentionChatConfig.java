package com.sankuai.dzhealth.ai.service.domain.chat.client;

import com.alibaba.fastjson.JSON;
import com.dianping.lion.Environment;
import com.meituan.mdp.boot.starter.config.annotation.MdpConfig;
import com.meituan.mdp.boot.starter.config.annotation.MdpConfigListener;
import com.meituan.mdp.boot.starter.config.vo.ConfigEvent;
import com.meituan.service.inf.kms.client.Kms;
import com.meituan.service.inf.kms.utils.KmsResultNullException;
import com.sankuai.dzhealth.ai.service.domain.chat.client.advisor.CustomMessageChatMemoryAdvisor;
import com.sankuai.dzhealth.ai.service.domain.memory.DatabaseChatMemory;
import com.sankuai.dzhealth.ai.service.infrastructure.chatmodel.OneApiChatModel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.openai.OpenAiChatOptions;
import org.springframework.ai.openai.api.OpenAiApi;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;

/**
 * @author: duanxiaowen
 * @date: 2025/3/20
 */
@Component
@Slf4j
public class IntentionChatConfig {


    // Bean名称常量
    public static final String INTENTION_CHAT_CLIENT = "public_hospital_intentionChatClient";

    @Autowired
    private DatabaseChatMemory databaseChatMemory;

    @MdpConfig("friday.LLM.modelselection")
    private ConcurrentHashMap<String, String> modelSelections;

    private final ConcurrentHashMap<String, ChatClient> cachedChatClients = new ConcurrentHashMap<>();

    @Bean(INTENTION_CHAT_CLIENT)
    public ChatClient intentionChatClient() throws KmsResultNullException {
        return getCachedChatClient(INTENTION_CHAT_CLIENT);
    }

    /**
     * 获取缓存的ChatClient实例
     */
    private ChatClient getCachedChatClient(String beanName) throws KmsResultNullException {
        return cachedChatClients.computeIfAbsent(beanName, key -> {
            try {
                return createChatClient(key);
            } catch (KmsResultNullException e) {
                throw new RuntimeException("Failed to create ChatClient for key: " + key, e);
            }
        });
    }

    /**
     * 监听配置变更并重新创建受影响的ChatClient
     */
    @MdpConfigListener("friday.LLM.modelselection")
    private void onModelSelectionChanged(ConfigEvent configEvent) {
        log.info("检测到配置变更: friday.LLM.modelselection, 旧值: {}, 新值: {}",
                    configEvent.getOldValue(), configEvent.getNewValue());

        // 检查哪些bean的配置发生了变化
        HashMap<String, String> changedKeys = getChangedKeys(configEvent);

        if (changedKeys.isEmpty()) {
            log.info("没有相关的配置key发生变化，跳过ChatClient重建");
            return;
        }

        // 重新创建受影响的ChatClient
        for (String configKey : changedKeys.keySet()) {
            String beanName = configKey; // 直接使用configKey作为beanName
            if (cachedChatClients.containsKey(beanName)) {
                try {
                    ChatClient newClient = createChatClient(beanName);
                    // 使用原子操作替换ChatClient
                    ChatClient oldClient = cachedChatClients.put(beanName, newClient);
                    log.info("ChatClient [{}] 已成功重建，使用新的模型配置: {}，旧实例已替换",
                              beanName, changedKeys.get(configKey));
                } catch (Exception e) {
                    log.error("重建ChatClient [{}] 失败: {}", beanName, e.getMessage(), e);
                    // 重建失败时不影响其他ChatClient的重建
                }
            }
        }
    }

    /**
     * 获取配置变更中发生变化的key
     */
    private HashMap<String, String> getChangedKeys(ConfigEvent configEvent) {
        HashMap<String, String> changedKeys = new HashMap<>();

        try {
            // 解析旧配置和新配置
            HashMap<String, String> oldConfig = parseConfigValue(configEvent.getOldValue());
            HashMap<String, String> newConfig = parseConfigValue(configEvent.getNewValue());

            // 检查所有可能的配置key
            if (newConfig != null) {
                for (String key : newConfig.keySet()) {
                    // 只检查当前类中已缓存的beanName
                    if (cachedChatClients.containsKey(key)) {
                        String oldValue = oldConfig != null ? oldConfig.get(key) : null;
                        String newValue = newConfig.get(key);

                        if (!java.util.Objects.equals(oldValue, newValue)) {
                            changedKeys.put(key, newValue);
                            log.info("配置key [{}] 发生变化: {} -> {}", key, oldValue, newValue);
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.warn("解析配置变更时出错", e);
        }

        return changedKeys;
    }



    /**
     * 解析配置值
     */
    @SuppressWarnings("unchecked")
    private HashMap<String, String> parseConfigValue(String configValue) {
        if (configValue == null || configValue.trim().isEmpty()) {
            return null;
        }

        try {
            // 使用fastjson解析JSON字符串
            return JSON.parseObject(configValue, HashMap.class);
        } catch (Exception e) {
            log.error("解析JSON配置失败: {}", configValue, e);
            return new HashMap<>();
        }
    }

    /**
     * 创建ChatClient的具体实现
     */
    private ChatClient createChatClient(String beanName) throws KmsResultNullException {
        OpenAiApi openAiApi = new OpenAiApi.Builder()
                .completionsPath("/chat/completions")
                .baseUrl("https://aigc.sankuai.com/v1/openai/native")
                .apiKey(Kms.getByName(Environment.getAppName(), "friday.search.appId"))
                .build();

        // 直接使用beanName作为配置key
        String modelName = Optional.ofNullable(modelSelections)
                .map(selections -> selections.get(beanName))
                .orElse("LongCat-Large-32K-Chat-0626"); // 使用默认值作为兜底

        log.info("使用配置key: {}, 模型: {}", beanName, modelName);

        OpenAiChatOptions openAiChatOptions = OpenAiChatOptions.builder()
                .model(modelName)
                .temperature(0.0)
                .maxTokens(1000)
                .build();

        OneApiChatModel chatModel = OneApiChatModel.builder()
                .openAiApi(openAiApi)
                .defaultOptions(openAiChatOptions)
                .build();

        return ChatClient.builder(chatModel)
                .defaultAdvisors(CustomMessageChatMemoryAdvisor.builder(databaseChatMemory)
                        .chatMemoryRetrieveSize(10) // 设置每次检索的历史消息数量
                        .build()).build();
    }

}
