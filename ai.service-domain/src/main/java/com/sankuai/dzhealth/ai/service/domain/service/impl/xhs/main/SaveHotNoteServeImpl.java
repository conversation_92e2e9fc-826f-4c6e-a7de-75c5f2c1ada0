package com.sankuai.dzhealth.ai.service.domain.service.impl.xhs.main;

import com.google.common.collect.Lists;
import com.sankuai.beautycontent.store.storage.service.StoreCommandService;
import com.sankuai.dzhealth.ai.service.api.ESVectorStoreService;
import com.sankuai.dzhealth.ai.service.api.RemoteResponse;
import com.sankuai.dzhealth.ai.service.domain.entity.xhs.SaveHotNoteRequest;
import com.sankuai.dzhealth.ai.service.domain.service.XhsNoteAbstractService;
import com.sankuai.dzhealth.ai.service.domain.service.impl.xhs.event.BuildStoreArgsEvent;
import com.sankuai.dzhealth.ai.service.domain.utils.JsonUtils;
import com.sankuai.dzhealth.ai.service.dto.DocumentDTO;
import com.sankuai.dzhealth.ai.service.infrastructure.dal.entity.CorpusLibraryDOWithBLOBs;
import com.sankuai.dzhealth.ai.service.infrastructure.repository.CorpusLibraryRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

@Slf4j
@Service
public class SaveHotNoteServeImpl extends XhsNoteAbstractService<List<SaveHotNoteRequest>> {

    @Autowired
    private ESVectorStoreService esVectorStoreService;

    @Resource
    private CorpusLibraryRepository corpusLibraryRepository;

    @Autowired
    private StoreCommandService storeCommandService;

    @Override
    protected String service(List<SaveHotNoteRequest> request) throws Exception {
        List<Integer> s = Lists.newArrayList();
        for (SaveHotNoteRequest saveHotNoteRequest : request) {
            s.add(saveTask(saveHotNoteRequest));
        }
        return s.stream().allMatch(i -> i == 0) ? "0" : "-1";
    }

    private int saveTask(SaveHotNoteRequest request) {
        try {
            // 1. 存 store 54
            Long storeId = saveStore(request);
            // 2. 存 corpusLibrary
            Long libId = saveCorpusLibrary(null, request.joinTitleAndContent(), storeId, request);
            // 3. 存 es
            saveES(request, libId, storeId);
            return 0;
        } catch (Exception e) {
            log.error("saveTask failed", e);
            return -1;
        }

    }

    private Long saveStore(SaveHotNoteRequest request) {
        try {
            com.meituan.beauty.fundamental.light.remote.RemoteResponse<Long> res = storeCommandService.add(BuildStoreArgsEvent.buildSaveHotNoteStoreArgs(request));
            if (res.isSuccess() && res.getData() != null) {
                return res.getData();
            }
        } catch (Exception e) {
            log.error("saveStore failed", e);
            throw new RuntimeException(e);
        }
        throw new RuntimeException("saveStore failed");
    }

    private Long saveCorpusLibrary(Long id, String titleAndContent, Long storeId, SaveHotNoteRequest request) {
        CorpusLibraryDOWithBLOBs corpusLibrary = new CorpusLibraryDOWithBLOBs();
        // content
        corpusLibrary.setCorpusContent(titleAndContent);
        // type
        corpusLibrary.setCorpusType(1);
        // id
        corpusLibrary.setId(id);
        // storeId 54
        corpusLibrary.setResourceId(storeId);
        // channel
        corpusLibrary.setResourceChannel("xhs.hotNote");
        // head
        corpusLibrary.setResourceUri(request.getValue("cover"));

        try {
            return corpusLibraryRepository.save(corpusLibrary);
        } catch (Exception e) {
            log.error("saveCorpusLibrary failed", e);
            log.error("saveCorpusLibrary failed, request: {}", JsonUtils.toJsonString(request));
            throw new RuntimeException(e);
        }
    }

    private void saveES(SaveHotNoteRequest request, Long libId, Long storeId) {

        DocumentDTO documentDTO = new DocumentDTO();
        // corpusLibrary id
        documentDTO.setId(String.valueOf(libId));
        // text
        documentDTO.setText(request.joinTitleAndContent());

        /*
        metadata
         */
        Map<String, String> metadata = request.tryGetMetadata();

        metadata.put("hotNoteStatus", "1");
        metadata.put("channel", "xhs.hotNote");
        metadata.put("noteId", request.getNoteId());
        metadata.put("resourceId", String.valueOf(storeId));
        metadata.put("authorname", request.SafeGetAuthorname());
        metadata.put("authorid", request.SafeGetAuthorId());
        metadata.put("content", request.getContent());
        metadata.put("title", request.getTitle());
        metadata.put("head", request.getValue("cover"));

        documentDTO.setMetadata(metadata);

        try {
            // 用library id 存到es 向量库中
            RemoteResponse<Boolean> res = esVectorStoreService.buildIndex(Lists.newArrayList(documentDTO));
            if (!res.getSuccess() || !res.getData()) {
                throw new RuntimeException("esVectorStoreService.buildIndex failed " + JsonUtils.toJsonString(request));
            }
        }  catch (Exception e) {
            log.error("saveES failed", e);
            log.error("saveES failed, request: {}", JsonUtils.toJsonString(request));
            throw new RuntimeException(e);
        }
    }

}
