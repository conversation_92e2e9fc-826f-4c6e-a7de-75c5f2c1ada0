package com.sankuai.dzhealth.ai.service.agent.domain.service;

import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import com.sankuai.dzhealth.ai.service.domain.utils.DecisionTreeModelConverter;
import com.sankuai.dzhealth.ai.service.agent.domain.model.decisionflow.DecisionTreeModel;
import com.sankuai.dzhealth.ai.service.api.DecisionFlowService;
import com.sankuai.dzhealth.ai.service.api.ESVectorStoreService;
import com.sankuai.dzhealth.ai.service.api.RemoteResponse;
import com.sankuai.dzhealth.ai.service.domain.utils.JsonUtils;
import com.sankuai.dzhealth.ai.service.dto.DocumentDTO;
import com.sankuai.dzhealth.ai.service.dto.decision.DecisionFlowDTO;
 
import com.sankuai.dzhealth.ai.service.enums.BizSceneEnum;
import com.sankuai.dzhealth.ai.service.enums.MetadataKeyEnum;
import com.sankuai.dzhealth.ai.service.request.DecisionTreeSearchRequest;
import com.sankuai.dzhealth.ai.service.request.DocumentSearchRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
 

/**
 * @author:chenwei
 * @time: 2025/7/15 15:15
 * @version: 0.0.1
 */

@Slf4j
@Service
public class MedicalDecisionRagService {

    @Autowired
    private DecisionFlowService decisionFlowService;

    @Autowired
    private ESVectorStoreService esVectorStoreService;



    public String queryDecisionRagInfo(String query, int depth, String bizType) {

        if (depth <= 0) {
            depth = 3;
        }
        String ragBizScene = Lion.getString(Environment.getAppName(), "rag.biz.scene", "医美决策树3.0");
        String mouthRagBizScene = Lion.getString(Environment.getAppName(), "mouth.rag.biz.scene", "口腔决策树");
        DecisionTreeSearchRequest request = new DecisionTreeSearchRequest();
        request.setQuery(StringUtils.isBlank(query) ? "" : query);
        request.setTopK(3);
        request.setBizScene(BizSceneEnum.MEDICAL_CONSULT.getBizScene().equals(bizType) ? ragBizScene : mouthRagBizScene);
        request.setPreviewGray(false);
        request.setMaxDepth(2);
        List<DecisionTreeModel> ragInfo = null;
        try {
            RemoteResponse<List<DecisionFlowDTO>> nodeSearchResponse = decisionFlowService.search(request);

            ragInfo = new ArrayList<>();
            log.info("query={}, node={}", query, JsonUtils.toJsonString(nodeSearchResponse));
            if (nodeSearchResponse.getSuccess() && CollectionUtils.isNotEmpty(nodeSearchResponse.getData())) {
                int nodeCount = Math.min(3, nodeSearchResponse.getData().size());
                for (int i = 0; i < nodeCount; i++) {
                    DecisionFlowDTO node = nodeSearchResponse.getData().get(i);
                    List<DecisionTreeModel> cur = convertToDecisionTreeModelJson(node, depth);
                    ragInfo.addAll(cur);
                }
                log.info("query={}, ragInfo={}", query, JsonUtils.toJsonString(ragInfo));
            }
            String configSupply = Lion.getString(Environment.getAppName(), "config.supply", "");
            if (StringUtils.isNotBlank(configSupply)) {
                return configSupply;
            }
            return JsonUtils.toJsonString(ragInfo);
        } catch (Exception e) {
            log.error("req={}", JsonUtils.toJsonString(request), e);
            return StringUtils.EMPTY;
        }



    }

    public String search(String query, int topK, List<String> channels) {
        Transaction transaction = Cat.newTransaction("ESVectorStoreService", "similaritySearch");
        try {
            DocumentSearchRequest request = new DocumentSearchRequest();
            request.setQuery(query);
            request.setTopK(10);
            Map<String, List<String>> params = new HashMap<>();
            params.put(MetadataKeyEnum.CHANNEL.getKey(), channels);
            request.setMetaData(params);
            RemoteResponse<List<DocumentDTO>> listRemoteResponse = esVectorStoreService.similaritySearch(request);


            if (listRemoteResponse.getSuccess() && CollectionUtils.isNotEmpty(listRemoteResponse.getData())) {
                StringBuilder result = new StringBuilder();
                listRemoteResponse.getData().forEach(document -> {
                    result.append(document.getText()).append("\n");
                });
                return result.toString();
            }
            return StringUtils.EMPTY;
        } catch (Exception e) {
            log.error("similaritySearch,rewriteText={},topK={},channels={},error={}",
                    query, topK, channels, e.getMessage(), e);
            transaction.setStatus(e);
            return StringUtils.EMPTY;
        } finally {
            transaction.complete();
        }
    }


    /**
     * 使用DecisionTreeModel优化的转换方法
     * 将DecisionFlowDTO转换为DecisionTreeModel列表，然后序列化为JSON
     * @param decisionFlowDTO 决策流DTO
     * @return JSON字符串
     */
    public static List<DecisionTreeModel> convertToDecisionTreeModelJson(DecisionFlowDTO decisionFlowDTO, int depth) {
        return DecisionTreeModelConverter.convertToDecisionTreeModels(decisionFlowDTO, depth);
    }


}
