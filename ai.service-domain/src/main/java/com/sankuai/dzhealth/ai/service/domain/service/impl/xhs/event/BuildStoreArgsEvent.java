package com.sankuai.dzhealth.ai.service.domain.service.impl.xhs.event;

import com.google.common.collect.Lists;
import com.sankuai.beautycontent.store.storage.dto.*;
import com.sankuai.dzhealth.ai.service.domain.entity.xhs.SaveHotNoteRequest;
import com.sankuai.dzhealth.ai.service.domain.entity.xhs.XhsBuildNoteResult;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

import java.util.List;
import java.util.ArrayList;
import java.util.stream.Collectors;

public class BuildStoreArgsEvent {


    public static StoreCommandDTO buildSaveStoreArgs(XhsBuildNoteResult.Note note, XhsBuildNoteResult.XhsBuildNoteItem item, String batch, String prompt) {

        /*
          base
         */
        Worker worker = new Worker();
        worker.store.setEditorType(5);
        worker.store.setEditorId("wb_songjuyi");

        /*
          storeDto
         */
        worker.storeDto.setStatus(0);
        worker.storeDto.setStoreType(56);

        /*
          attribute
         */
        // 批次
        worker.attribute("batch", batch);
        // 品类
        worker.attribute("category", item.getTcsd().getCategory());
        // 人群
        worker.attribute("droves", item.getTcsd().getDroves());
        // 场景
        worker.attribute("scene", item.getTcsd().getScene());
        // 方向
        worker.attribute("theme", item.getTcsd().getTheme());
        // 1.0 和 2.0 区分用
        worker.attribute("xhsversion", "2.0");

        /*
           storeExtAttributeDTOList
         */
        // 标题 s
        worker.extAttribute("title", note.titleToJson());
        // 正文
        worker.extAttribute("content", note.getContent());
        // 封面副标题
        worker.extAttribute("subTitle", note.getCover().getSubTitle());
        // 封面要点摘要
        worker.extAttribute("keyPoint", note.getCover().getKeyPoint());
        // prompt
        worker.extAttribute("prompt", prompt);

        return worker.build();
    }

    public static StoreCommandDTO buildSaveHotNoteStoreArgs(SaveHotNoteRequest input) {
        /*
          base
         */
        Worker worker = new Worker();
        worker.store.setEditorType(5);
        worker.store.setEditorId("wb_songjuyi");

        /*
          storeDto
         */
        worker.storeDto.setStatus(0);
        worker.storeDto.setStoreType(54);
        worker.storeDto.setRelationId(input.getNoteId());

        /*
          attribute
         */
        // 热门笔记的爬取批次
        worker.attribute("batch", input.SafeGetBatch());
        // 发布者昵称
        worker.attribute("authorName", input.SafeGetAuthorname());
        // 账号id
        worker.attribute("authorId", input.SafeGetAuthorId());
        // channel
        worker.attribute("channel", "xhs.hotNote");
        // hotNoteStatus
        worker.attribute("hotNoteStatus", "1");
        // 标题
        worker.extAttribute("title", input.getTitle());
        // 正文
        worker.extAttribute("content", input.getContent());
        // 头图url
        worker.extAttribute("head", input.getValue("cover"));
        worker.rank("2", input.getValue("interactiveCount"));

        // 提取话题名称列表
        String topicsJson = input.getValue("topics");
        List<String> topicNames = extractTopicNames(topicsJson);
        worker.references("topic", topicNames);

        worker.rank("3", input.getValue("interactiveCount"));
        return worker.build();
    }

    /**
     * 从话题JSON字符串中提取name列表
     * @param topicsJson 话题JSON字符串
     * @return name列表
     */
    public static List<String> extractTopicNames(String topicsJson) {
        if (StringUtils.isBlank(topicsJson)) {
            return new ArrayList<>();
        }

        try {
            JSONArray topics = JSON.parseArray(topicsJson);
            return topics.stream()
                    .map(obj -> ((JSONObject) obj).getString("name"))
                    .filter(StringUtils::isNotBlank)
                    .collect(Collectors.toList());
        } catch (Exception ignored) {
            return new ArrayList<>();
        }
    }

    static class Worker {
        private final StoreCommandDTO store;
        private final StoreDetailDTO storeDetail;
        private final List<StoreAttributeDTO> storeAttributes;
        private final List<StoreExtAttributeDTO> storeExtAttributes;
        private final List<StoreRankDTO> storeRanks;
        private final List<StoreReferenceDTO> references;
        public final StoreDTO storeDto;

        public Worker() {
            store = new StoreCommandDTO();
            storeDetail = new StoreDetailDTO();
            storeAttributes = Lists.newArrayList();
            storeExtAttributes = Lists.newArrayList();
            storeDto = new StoreDTO();
            storeRanks = Lists.newArrayList();
            references = Lists.newArrayList();
        }

        public StoreCommandDTO build() {
            storeDetail.setStoreDTO(storeDto);
            storeDetail.setStoreExtAttributeDTOList(storeExtAttributes);
            storeDetail.setStoreAttributeDTOList(storeAttributes);
            storeDetail.setStoreRankDTOList(storeRanks);
            storeDetail.setStoreReferenceDTOList(references);
            store.setStoreDetailDTO(storeDetail);
            return store;
        }

        private void attribute(String key, String value) {
            if (StringUtils.isBlank(value)) return;
            StoreAttributeDTO attribute = new StoreAttributeDTO();
            attribute.setAttributeKey(key);
            attribute.setAttributeValue(value);
            attribute.setSceneKey("ge");
            storeAttributes.add(attribute);
        }
        private void rank(String key, String value) {
            if (!NumberUtils.isDigits(value)) return;
            StoreRankDTO rankDTO = new StoreRankDTO();
            rankDTO.setRankKey(key);
            rankDTO.setRankValue(NumberUtils.toLong(value));
            rankDTO.setSceneKey("ge");
            storeRanks.add(rankDTO);
        }
        private void references(String key, List<String> value) {
            if (CollectionUtils.isEmpty(value)) return;
            StoreReferenceDTO rankDTO = new StoreReferenceDTO();
            rankDTO.setReferenceKey(key);
            rankDTO.setReferenceIdList(value);
            rankDTO.setSceneKey("ge");
            references.add(rankDTO);
        }
        private void extAttribute(String key, String value) {
            if (StringUtils.isBlank(value)) return;
            StoreExtAttributeDTO attribute = new StoreExtAttributeDTO();
            attribute.setAttributeKey(key);
            attribute.setAttributeValue(value);
            attribute.setSceneKey("ge");
            storeExtAttributes.add(attribute);
        }
    }
}
