package com.sankuai.dzhealth.ai.service.domain.ability.llm;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.dianping.cat.Cat;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.messages.SystemMessage;
import org.springframework.ai.chat.messages.UserMessage;
import org.springframework.ai.chat.model.ChatResponse;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * A generic service to filter lists of items using an LLM.
 */
@Service
@Slf4j
public class LLMResultFilterService {

    /**
     * Filters a list of items based on their relevance to a user query using an LLM.
     *
     * @param userQuery         The user's query.
     * @param itemsToFilter     The list of items to filter.
     * @param systemMessageContent The content for the system message, guiding the LLM's evaluation.
     * @param chatClient        The ChatClient to use for the LLM call.
     * @param <T>               The type of the items, must implement {@link FilterableItem}.
     * @return A filtered list of items.
     */
    public <T extends FilterableItem> List<T> filterResults(
            String userQuery,
            List<T> itemsToFilter,
            String systemMessageContent,
            ChatClient chatClient) {

        if (CollectionUtils.isEmpty(itemsToFilter)) {
            return new ArrayList<>();
        }

        try {
            // 1. Build the prompt for the LLM
            String userMessageContent = buildUserMessageContent(userQuery, itemsToFilter);
            SystemMessage systemMessage = new SystemMessage(systemMessageContent);
            UserMessage userMessage = new UserMessage(userMessageContent);
            Prompt prompt = new Prompt(Arrays.asList(systemMessage, userMessage));

            // 2. Call the LLM
            ChatResponse response = chatClient.prompt(prompt).call().chatResponse();
            String answer = response.getResult().getOutput().getText();

            // 3. Parse the response and filter the results
            return parseAndFilter(answer, itemsToFilter);

        } catch (Exception e) {
            Cat.logError("Failed to filter results with LLM", e);
            log.error("Error filtering results with LLM. Returning original list.", e);
            // On failure, return the original unfiltered list
            return itemsToFilter;
        }
    }

    private <T extends FilterableItem> String buildUserMessageContent(String userQuery, List<T> items) {
        StringBuilder contentBuilder = new StringBuilder();
        contentBuilder.append("用户问题: ").append(userQuery).append("\n\n");

        for (int i = 0; i < items.size(); i++) {
            T item = items.get(i);
            contentBuilder.append("结果 ").append(i + 1).append(":\n");
            for (Map.Entry<String, String> entry : item.getFieldsForEvaluation().entrySet()) {
                contentBuilder.append(entry.getKey()).append(": ").append(entry.getValue()).append("\n");
            }
            contentBuilder.append("\n");
        }
        return contentBuilder.toString();
    }

    private <T extends FilterableItem> List<T> parseAndFilter(String llmAnswer, List<T> originalItems) {
        try {
            String jsonStr = extractJsonFromText(llmAnswer);
            if (StringUtils.isBlank(jsonStr)) {
                log.error("Could not extract valid JSON from LLM response: {}", llmAnswer);
                return originalItems;
            }

            List<Map<String, Object>> evaluationResults = JSON.parseObject(jsonStr, new TypeReference<List<Map<String, Object>>>() {});
            
            List<Integer> validIndices = evaluationResults.stream()
                    .filter(eval -> (Boolean) eval.getOrDefault("isValid", false))
                    .map(eval -> ((Integer) eval.get("resultId")) - 1) // Convert to 0-based index
                    .collect(Collectors.toList());

            List<T> filteredItems = new ArrayList<>();
            for (Integer index : validIndices) {
                if (index >= 0 && index < originalItems.size()) {
                    filteredItems.add(originalItems.get(index));
                }
            }
            
            log.info("Finished filtering results. Original count: {}, Filtered count: {}", originalItems.size(), filteredItems.size());
            return filteredItems;

        } catch (Exception e) {
            log.error("Error parsing LLM evaluation response: {}", llmAnswer, e);
            return originalItems; // Return original list on parsing error
        }
    }

    private String extractJsonFromText(String text) {
        int start = text.indexOf('[');
        int end = text.lastIndexOf(']');
        if (start != -1 && end != -1 && end > start) {
            return text.substring(start, end + 1);
        }
        return "";
    }
} 