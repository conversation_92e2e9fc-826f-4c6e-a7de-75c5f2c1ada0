package com.sankuai.dzhealth.ai.service.domain.thinking.util;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 搜索结果提取工具类
 * 用于从思考内容中提取URL引用
 */
public class SearchResultExtractor {
    
    private static final Logger logger = LoggerFactory.getLogger(SearchResultExtractor.class);
    
    /**
     * 从思考内容中提取URL引用
     *
     * @param thoughtContent 思考内容
     * @return URL列表
     */
    public static List<String> extractUrlsFromThought(String thoughtContent) {
        List<String> urls = new ArrayList<>();
        if (StringUtils.isBlank(thoughtContent)) {
            return urls;
        }
        
        try {
            // URL模式匹配
            Pattern urlPattern = Pattern.compile(
                "https?://[-a-zA-Z0-9+&@#/%?=~_|!:,.;]*[-a-zA-Z0-9+&@#/%=~_|]");
            Matcher matcher = urlPattern.matcher(thoughtContent);
            
            while (matcher.find()) {
                String url = matcher.group();
                if (!urls.contains(url)) { // 避免重复
                    urls.add(url);
                    logger.debug("从思考内容中提取URL: {}", url);
                }
            }
        } catch (Exception e) {
            logger.error("提取URL时发生错误", e);
        }
        
        return urls;
    }
} 