package com.sankuai.dzhealth.ai.service.domain.service.strategy;

import com.alibaba.fastjson.JSON;
import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.dianping.rhino.threadpool.ThreadPool;
import com.google.common.collect.ImmutableMap;
import com.sankuai.dzhealth.ai.service.domain.chat.model.IntentionResult;
import com.sankuai.dzhealth.ai.service.domain.service.ChatSessionService;
import com.sankuai.dzhealth.ai.service.domain.utils.context.AiAnswerContext;
import com.sankuai.dzhealth.ai.service.infrastructure.observability.Span;
import com.sankuai.dzhealth.medical.client.biz.hospital.model.HospitalAggInfo;
import com.sankuai.dzhealth.medical.client.biz.hospital.model.HospitalCollectInfo;
import com.sankuai.dzhealth.medical.client.biz.hospital.service.HospitalService;
import com.sankuai.dzhealth.medical.client.biz.shop.dto.HospitalInfoRequest;
import com.sankuai.dzhealth.medical.client.common.dto.Response;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.concurrent.CompletableFuture;

/**
 * 医院信息策略实现
 */
@Component
@Slf4j
public class HospitalStrategy implements DataSourceStrategy<HospitalStrategy.HospitalStrategyInfo> {

    @Autowired
    private HospitalService hospitalService;

    private static final ThreadPool taskPool = ChatSessionService.TASK_POOL;


    @Override
    public boolean shouldExecute(IntentionResult intentionResult) {
        return intentionResult.getHospitalNeed() != null && intentionResult.getHospitalNeed();
    }

    @Override
    public CompletableFuture<HospitalStrategyInfo> execute(AiAnswerContext context, String rewriteText) {
        return CompletableFuture.supplyAsync(() ->
                queryHospitalInfo(context.getShopId(), context.getPlatform()), taskPool.getExecutor());
    }

    /**
     * 查询医院信息
     */
    private HospitalStrategyInfo queryHospitalInfo(Long shopId, int platform) {
        Transaction transaction = Cat.newTransaction(getClass().getSimpleName(), "queryHospitalInfo");
        transaction.setSuccessStatus();
        long startTime = System.currentTimeMillis();
        try {
            HospitalInfoRequest request = new HospitalInfoRequest();
            request.setShopId(shopId);
            request.setPlatformType(platform);
            HospitalStrategyInfo hospitalInfo = new HospitalStrategyInfo();
            Response<HospitalAggInfo> shopInfoResponse = hospitalService.queryHospitalAggregateInfo(request);
            if (shopInfoResponse.respSuccess() && shopInfoResponse.getData() != null) {
                HospitalAggInfo data = shopInfoResponse.getData();
                hospitalInfo.setShopInfo(data);
            }
            hospitalInfo.setSpan(Collections.singletonList(Span.builder()
                    .key(getClass().getSimpleName())
                    .value(JSON.toJSONString(ImmutableMap.of("request", request, "result", shopInfoResponse)))
                    .duration(System.currentTimeMillis() - startTime)
                    .build()));
            return hospitalInfo;
        } catch (Exception e) {
            transaction.setStatus(e);
            log.error("queryHospitalInfo error", e);
            return null;
        } finally {
            transaction.complete();
        }
    }

    @Data
    @EqualsAndHashCode(callSuper = true)
    public static class HospitalStrategyInfo extends BaseStrategyInfo {

        private HospitalAggInfo shopInfo;

        @Override
        public String toPrompt() {
            if (shopInfo == null) {
                return "";
            }

            StringBuilder prompt = new StringBuilder();
            // 医院名称
            prompt.append("医院名称:").append(shopInfo.getShopName()).append("\n");

            //logo
            if (StringUtils.isNotBlank(shopInfo.getHeadPic())) {
                prompt.append("医院logo:").append(shopInfo.getHeadPic()).append("\n");
            }
            // 医院电话
            if (shopInfo.getShopPhoneInfoList() != null && !shopInfo.getShopPhoneInfoList().isEmpty()) {
                prompt.append("联系电话:");
                shopInfo.getShopPhoneInfoList().forEach(phone ->
                        prompt.append(phone.getAreaCode()).append("-").append(phone.getEntity()).append(" "));
                prompt.append("\n");
            }

            // 从collectInfo获取更多信息
            HospitalCollectInfo collectInfo = shopInfo.getCollectInfo();
            if (collectInfo != null) {
                // 医院简介
                if (collectInfo.getBriefDesc() != null) {
                    prompt.append("医院简介:").append(collectInfo.getBriefDesc()).append("\n");
                }

                // 排名信息
                if (collectInfo.getNationalRank() != null) {
                    prompt.append(collectInfo.getNationalRankDesc()).append(collectInfo.getNationalRank()).append("名\n");
                }
                if (collectInfo.getRegionRank() != null) {
                    prompt.append(collectInfo.getRegionRankDesc()).append(collectInfo.getRegionRank()).append("名\n");
                }

                // 地址信息
                prompt.append("地址信息:")
                        .append(collectInfo.getProvince()).append(" ")
                        .append(collectInfo.getCity()).append(" ")
                        .append(collectInfo.getRegion()).append(" ")
                        .append(collectInfo.getAddress()).append("\n");

                // 门诊时间
                if (collectInfo.getOpenTime() != null) {
                    prompt.append("门诊时间:").append(collectInfo.getOpenTime()).append("\n");
                }

                // 医院概况标签
                if (collectInfo.getTags() != null && !collectInfo.getTags().isEmpty()) {
                    prompt.append("医院特色:");
                    collectInfo.getTags().forEach(tag -> prompt.append(tag).append(","));
                    prompt.append("\n");
                }
                //医保标签
                if (collectInfo.getMedicalInsuranceTag() != null && !collectInfo.getMedicalInsuranceTag().isEmpty()) {
                    prompt.append("医保标签:").append(collectInfo.getMedicalInsuranceTag()).append("\n");
                }

                // 门诊服务
                if (collectInfo.getOutpatientServices() != null && !collectInfo.getOutpatientServices().isEmpty()) {
                    prompt.append("门诊服务:");
                    collectInfo.getOutpatientServices().forEach(service -> prompt.append(service).append(","));
                    prompt.append("\n");
                }

                // 复旦排名等级
                if (collectInfo.getFdRankLevel() != null) {
                    prompt.append("复旦医院排名等级:").append(collectInfo.getFdRankLevel());
                }
            }

            return prompt.toString();
        }
    }
} 