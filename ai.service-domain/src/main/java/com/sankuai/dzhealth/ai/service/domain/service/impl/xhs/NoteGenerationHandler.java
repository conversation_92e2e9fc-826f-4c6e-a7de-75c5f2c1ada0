package com.sankuai.dzhealth.ai.service.domain.service.impl.xhs;

import com.sankuai.dzhealth.ai.service.enums.xhs.BizTypeEnum;
import com.sankuai.dzhealth.ai.service.request.xhs.XhsBuildNoteRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 医疗笔记生成处理器 - 处理由Mafka消息触发的医疗笔记生成流程
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class NoteGenerationHandler {

    @Autowired
    private MedicalNoteService medicalNoteService;

    @Autowired
    private JoyLifeNoteService joyLifeNoteService;

    /**
     * 处理任务中心的结果消息
     *
     * @param batchId 批次ID
     */
    public void handle(String batchId, String errMsg) {
        // 1. 获取原始请求上下文
        XhsBuildNoteRequest originalRequest = medicalNoteService.getRequestContext(batchId);
        if (originalRequest == null) {
            log.info("[NoteGenerationHandler] 未找到batchId对应的上下文: {}", batchId);
            return;
        }
        if (BizTypeEnum.MEDICAL.getBizCode().equals(originalRequest.getBizCode())) {
            medicalNoteService.processNote(originalRequest, batchId, errMsg);
        } else if (BizTypeEnum.JOY_LIFE.getBizCode().equals(originalRequest.getBizCode())) {
            joyLifeNoteService.processNote(originalRequest, batchId, errMsg);
        }
    }
}
