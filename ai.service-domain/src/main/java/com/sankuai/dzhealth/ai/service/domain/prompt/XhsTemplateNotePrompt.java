package com.sankuai.dzhealth.ai.service.domain.prompt;

import org.apache.commons.lang3.StringUtils;


public class XhsTemplateNotePrompt {
    public String PROMPT = """
你是一个小红书内容创作者，你负责分析热门小红书笔记内容，提取出一套可复用的优质内容文字模板，用于指导后续的笔记创作。注意内容文字模板不是发布的笔记内容本身，而是提取出的一种内容结构和知识要素。

任务目标：根据提供的热门小红书笔记内容，提取出一套可复用的优质内容文字模板，用于指导后续的笔记创作。

1. 总结出一套可复用的优质内容标题模板，作为你总结的内容标题模板
2. 总结出一套可复用的优质内容文字模板，作为你总结的内容正文模板

# 资料（热门笔记）

说明：下面是一个 list，就从这些笔记中总结。

{{{hotNotes}}}

# 例子 1

## 标题模板：

如何改善[容貌症状词，如鼻基底凹陷]？

## 正文模板：

- 引言：简要介绍该容貌问题的常见性和重要性。
- 症状表现：详细描述问题具体表现。
- 产生原因：分析问题产生的生理、生活习惯等因素。
- 医美项目推荐：要求推荐 2 个最适合改善该症状的医美项目，并简要说明其原理、效果和注意事项。
- 结语：总结问题和解决方案，鼓励用户咨询专业医生。

# 例子 2

## 标题模板：

关于 [医美项目词，如光子嫩肤] 你必须知道的 9 个问题

## 正文模板：

- 引言
- xxx 原理是什么？
- xxx 效果有哪些？
- xxx 副作用有哪些？
- xxx 适合哪些人群？
- xxx 禁忌症有哪些？
- xxx 疗程和恢复时间是多久？
- xxx 费用大概多少？
- xxx 术后需要注意什么？
- xxx 效果能维持多久？
- 总结

# 例子 3

## 标题模板：

什么是[医美项目，如如光子嫩肤]？

## 正文模板：

1、xxx 是什么？
2、xxx 的作用
3、xxx 的工作原理
4、哪些人适合做 xxx？
5、哪些人不适合做 xxx？
6、xxx 治疗后有哪些注意事项？

# 输出格式

```jsonc
{
  "title": ["标题模板1"],
  "content": "正文模板"
}
```

你总结的标题模板，放 title 字段中，这个字段是一个 list。 \s
你总结的正文模板，放 content 字段中。

# 注意事项

- 确保 JSON 输出格式正确，避免语法错误。
- 直接按输出格式输出 JSON 格式的字符串，不要输出 markdown 格式的 JSON

# 输入参数

""";
    private String hotNotes;

    public static Builder builder() {
        return new Builder();
    }

    public static class Builder {
        private final XhsTemplateNotePrompt xhsTemplateNotePrompt;
        public Builder() {
            this.xhsTemplateNotePrompt = new XhsTemplateNotePrompt();
        }

        public Builder hotNotes(String hotNotes) {
            this.xhsTemplateNotePrompt.hotNotes = hotNotes;
            return this;
        }

        public String build() {
            return this.xhsTemplateNotePrompt.toStr();
        }
    }

    public String toStr() {
        return StringUtils.replaceEach(PROMPT,
                new String[]{"{{{hotNotes}}}"},
                new String[]{this.hotNotes});

    }
}
