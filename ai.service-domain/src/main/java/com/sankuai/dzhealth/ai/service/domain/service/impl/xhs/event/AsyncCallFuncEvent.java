package com.sankuai.dzhealth.ai.service.domain.service.impl.xhs.event;

import com.dianping.rhino.Rhino;
import com.dianping.rhino.threadpool.DefaultThreadPoolProperties;
import com.dianping.rhino.threadpool.ThreadPool;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.function.Consumer;
import java.util.function.Function;

@Slf4j
@Component
public class AsyncCallFuncEvent implements InitializingBean {
    private ThreadPool threadPool;

    @Override
    public void afterPropertiesSet() throws Exception {
        threadPool = Rhino.newThreadPool("XhsAsyncThreadPool",
                DefaultThreadPoolProperties.Setter().withCoreSize(100).withMaxSize(200).withMaxQueueSize(1000));
    }
    public void asyncRunnable(int count, Runnable func) {
        call_task_Consumer(count, i -> func.run());
    }

    public void asyncConsumer(int count, Consumer<Integer> func) {
        call_task_Consumer(count, func);
    }

    private <T> void call_task(int count, Function<Integer, T> func) {
        if (count <= 0) return;

        List<CompletableFuture<T>> futures = Lists.newCopyOnWriteArrayList();

        for (int i = 0; i < count; i++) {
            int finalI = i;

            CompletableFuture<T> future = CompletableFuture.supplyAsync(
                    () -> func.apply(finalI)
                    , threadPool.getExecutor());
            futures.add(future);
        }
        futures.forEach(future -> {
            try {
                future.get(40, TimeUnit.SECONDS);
            } catch (InterruptedException | ExecutionException | TimeoutException e) {
                throw new RuntimeException(e);
            }
        });
    }

    private void call_task_Consumer(int count, Consumer<Integer> func) {
        if (count <= 0) return;

        List<CompletableFuture<Void>> futures = Lists.newCopyOnWriteArrayList();
        for (int i = 0; i < count; i++) {
            int finalI = i;

            CompletableFuture<Void> future = CompletableFuture.runAsync(
                    () -> func.accept(finalI)
                    , threadPool.getExecutor());
            futures.add(future);
        }

        futures.forEach(future -> {
            try {
                future.get(5, TimeUnit.SECONDS);
            } catch (TimeoutException e) {
                log.info("timeout: {}", Thread.currentThread().getName());
            } catch (InterruptedException | ExecutionException e) {
                throw new RuntimeException(e);
            }
        });
    }
}
