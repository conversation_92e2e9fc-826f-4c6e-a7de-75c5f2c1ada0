package com.sankuai.dzhealth.ai.service.agent.domain.utils;

import com.google.common.collect.Lists;
import com.meituan.mdp.boot.starter.util.Pair;
import com.sankuai.dzhealth.ai.service.agent.domain.buffer.MessageBuffer;
import com.sankuai.dzhealth.ai.service.agent.domain.buffer.MessageBufferEntity;
import com.sankuai.dzhealth.ai.service.agent.domain.buffer.label.CustomTruncateLabel;
import com.sankuai.dzhealth.ai.service.domain.utils.context.RequestContext;
import com.sankuai.dzhealth.ai.service.dto.stream.StreamEventCardDataDTO;
import com.sankuai.dzhealth.ai.service.enums.BufferItemTypeEnum;
import com.sankuai.dzhealth.ai.service.enums.StreamEventDataTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 缓冲区数据处理器
 * 
 * <AUTHOR>
 * @time 2025/7/6 15:35
 * @version 0.0.1
 */
@Slf4j
@Service
public class BufferUtils {
    public String convertStreamEventType(int bufferItemType) {
        BufferItemTypeEnum typeEnum = BufferItemTypeEnum.getByType(bufferItemType);
        if (typeEnum == null) {
            return StreamEventDataTypeEnum.MAIN_TEXT.getType();
        }
        return switch (typeEnum) {
            case LOADING_STATUS -> StreamEventDataTypeEnum.LOADING_STATUS.getType();
            case THINK_PROCESS_STREAM -> StreamEventDataTypeEnum.THINK_PROCESS_STREAM.getType();
            case MAIN_TEXT -> StreamEventDataTypeEnum.MAIN_TEXT.getType();
            case RECOMMEND_QUESTION ->
                // 推荐问题暂时转换为主文本类型
                    StreamEventDataTypeEnum.MAIN_TEXT.getType();
            default -> StreamEventDataTypeEnum.MAIN_TEXT.getType();
        };
    }

    public List<StreamEventCardDataDTO> buildCardDTO(MessageBufferEntity entity) {
        if (entity.getExtra() == null) {
            return null;
        }

        CustomTruncateLabel customLabelTruncate = new CustomTruncateLabel();
        List<Pair<String, String>> pairs = customLabelTruncate.parseLabels(entity.getData());
        if (CollectionUtils.isEmpty(pairs)) {
            return null;
        }
        return pairs.stream().map(pair -> {
            StreamEventCardDataDTO cardDataDTO = new StreamEventCardDataDTO();
            cardDataDTO.setType(pair.getKey());
            cardDataDTO.setKey(pair.getValue());
            cardDataDTO.setCardProps(entity.getExtra());
            return cardDataDTO;
        }).collect(Collectors.toList());
    }

    public static boolean writeMainTextBuffer(MessageBufferEntity entity, MessageBuffer buffer) {
        if (entity == null || StringUtils.isEmpty(entity.getData())) {
            return false;
        }
        entity.setType(BufferItemTypeEnum.MAIN_TEXT.getType());
        return RequestContext.writeBuffer(Lists.newArrayList(entity), buffer);
    }

    public static boolean writeThinkingProcessStreamBuffer(MessageBufferEntity entity, MessageBuffer buffer) {
        if (entity == null || StringUtils.isEmpty(entity.getData())) {
            return false;
        }
        entity.setType(BufferItemTypeEnum.THINK_PROCESS_STREAM.getType());
        return RequestContext.writeBuffer(Lists.newArrayList(entity), buffer);
    }


    public static boolean writeLoadingStatusBuffer(MessageBufferEntity entity, MessageBuffer buffer) {
        if (entity == null || StringUtils.isEmpty(entity.getData())) {
            return false;
        }
        entity.setType(BufferItemTypeEnum.LOADING_STATUS.getType());
        return RequestContext.writeBuffer(Lists.newArrayList(entity), buffer);
    }
} 