package com.sankuai.dzhealth.ai.service.domain.utils;

import com.sankuai.dzhealth.ai.service.enums.BizSceneEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;

/**
 * 医美AI相关URL构造工具类
 * <AUTHOR> Assistant
 */
@Slf4j
public class AestheticMedicineAiUrlUtils {

    // MRN相关常量
    private static final String MRN_BIZ = "gcbu";
    private static final String MRN_ENTRY = "aesthetic-ai-app";
    private static final String MRN_COMPONENT = "AestheticMedicineAI";

    // App Schema常量（只保留app内的schema，因为都是站内入口）
    private static final String DP_APP_MRN_SCHEMA = "dianping://mrn?mrn_biz=%s&mrn_entry=%s&mrn_component=%s";
    private static final String MT_APP_MRN_SCHEMA = "imeituan://www.meituan.com/mrn?mrn_biz=%s&mrn_entry=%s&mrn_component=%s";

    /**
     * 构造医美AI Agent URL（统一方法）
     * 
     * 使用场景：
     * 1. 构建入口URL：question=null, mainQuestion=null
     * 2. 构建QA URL：question!=null, mainQuestion!=null
     * 
     * @param productId 产品ID
     * @param productTitle 商品标题
     * @param platform 平台 1=点评，2=美团
     * @param idType ID类型
     * @param categoryId 品类ID
     * @param question 问题（可选，为null时构建入口URL）
     * @param mainQuestion 是否为主问答（可选，仅在有question时有效）
     * @return 构造的URL
     */
    public static String buildAgentUrl(String productId, String productTitle,
                                       int platform, int idType, int categoryId,
                                       String question, Boolean mainQuestion) {
        String baseUrl = getBaseUrl(platform);
        String bizType = (categoryId == 506) ? BizSceneEnum.MOUTH_CONSULT.getBizScene() : BizSceneEnum.MEDICAL_CONSULT.getBizScene();
        
        String encodedTitle = !StringUtils.isBlank(productTitle) 
                ? URLEncoder.encode(productTitle, StandardCharsets.UTF_8) : "";
        
        if (!StringUtils.isBlank(question)) {
            // 构建带问题的QA URL
            String encodedQuestion = URLEncoder.encode(question, StandardCharsets.UTF_8);
            return String.format("%s&idType=%d&productId=%s&categoryId=%d&platform=%d&question=%s&mainQuestion=%s&source=1&bizType=%s%s",
                    baseUrl, idType, productId, categoryId, platform, encodedQuestion, mainQuestion, bizType,
                    encodedTitle.isEmpty() ? "" : "&productTitle=" + encodedTitle);
        } else {
            // 构建入口URL
            return String.format("%s&idType=%d&productId=%s&categoryId=%d&platform=%d&source=1&bizType=%s%s",
                    baseUrl, idType, productId, categoryId, platform, bizType,
                    encodedTitle.isEmpty() ? "" : "&productTitle=" + encodedTitle);
        }
    }




    /**
     * 根据平台获取基础URL（都是app内schema）
     * @param platform 平台 1=点评，2=美团
     * @return 基础URL
     */
    private static String getBaseUrl(int platform) {
        if (platform == 1) {
            // 点评
            return String.format(DP_APP_MRN_SCHEMA, MRN_BIZ, MRN_ENTRY, MRN_COMPONENT);
        } else {
            // 美团
            return String.format(MT_APP_MRN_SCHEMA, MRN_BIZ, MRN_ENTRY, MRN_COMPONENT);
        }
    }
}

