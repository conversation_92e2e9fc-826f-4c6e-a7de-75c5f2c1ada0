package com.sankuai.dzhealth.ai.service.domain.service.decisionflow;

import com.dianping.rhino.Rhino;
import com.dianping.rhino.threadpool.DefaultThreadPoolProperties;
import com.dianping.rhino.threadpool.ThreadPool;
import com.sankuai.dzhealth.ai.service.agent.domain.model.decisionflow.*;
import com.sankuai.dzhealth.ai.service.dto.decision.DecisionEdgeDTO;
import com.sankuai.dzhealth.ai.service.dto.decision.DecisionNodeDTO;
import com.sankuai.dzhealth.ai.service.dto.decision.NodeResourceRelationDTO;
import com.sankuai.dzhealth.ai.service.dto.decision.ResourceRecommendationDTO;
import com.sankuai.dzhealth.ai.service.enums.DecisionFlowElementStatusEnum;
import com.sankuai.dzhealth.ai.service.enums.DicisionFlowStatusEnum;
import com.sankuai.dzhealth.ai.service.enums.RelationStatusEnum;
import com.sankuai.dzhealth.ai.service.infrastructure.dal.entity.DecisionNodeDO;
import com.sankuai.dzhealth.ai.service.infrastructure.dal.entity.DecisionNodeEdgeDO;
import com.sankuai.dzhealth.ai.service.infrastructure.dal.entity.NodeResourceRelationDO;
import com.sankuai.dzhealth.ai.service.infrastructure.dal.entity.RecommendResourceDO;
import com.sankuai.dzhealth.ai.service.infrastructure.repository.decision.*;
import com.sankuai.dzhealth.ai.service.infrastructure.repository.decision.DecisionVectorStoreRepository.NodeIndexData;
import com.sankuai.dzhealth.ai.service.infrastructure.utils.UidUtils;
import com.sankuai.dzhealth.ai.service.request.DecisionFlowImportRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 决策树领域服务
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DecisionFlowDomainService {

    private final DecisionNodeRepository nodeRepository;
    private final DecisionEdgeRepository edgeRepository;
    private final NodeResourceRelationRepository relationRepository;
    private final RecommendResourceRepository resourceRepository;
    private final UidUtils uidUtils;
    private final DecisionVectorStoreRepository vectorStoreRepository;

    private static final ThreadPool ES_INDEX_TASK_POOL = Rhino.newThreadPool("DecisionFlowESIndexPool",
            DefaultThreadPoolProperties.Setter().withCoreSize(10).withMaxSize(20).withMaxQueueSize(500));


    /**
     * 构建完整决策树
     */
    public DecisionNodeBO buildTree(String bizScene) {
        // 查询所有节点/边
        List<DecisionNodeDO> nodeEntities = nodeRepository.listByBizScene(bizScene);
        List<DecisionNodeEdgeDO> edgeEntities = edgeRepository.listByBizScene(bizScene);

        // 转成 Map
        Map<String, DecisionNodeBO> nodeMap = nodeEntities.stream()
                .map(this::convertNode)
                .collect(Collectors.toMap(DecisionNodeBO::getNodeId, n -> n));

        // 组装边
        for (DecisionNodeEdgeDO edge : edgeEntities) {
            DecisionEdgeBO edgeBO = convertEdge(edge);
            DecisionNodeBO parent = nodeMap.get(edge.getParentId());
            if (parent != null) {
                parent.getEdges().add(edgeBO);
            }
        }

        // ROOT 作为虚根
        return nodeMap.getOrDefault("ROOT", nodeMap.values().stream().findFirst().orElse(null));
    }

    /**
     * 基于 edgeValue 过滤并返回下一节点候选列表（按 sortOrder 升序）。
     *
     * @param currentNodeId 当前节点业务 id
     * @param edgeValue     匹配值（AI 预测结果 / 用户选项等），可为空
     * @return 候选节点列表（0-N 条）
     */
    public List<DecisionNodeBO> decideNext(String currentNodeId, String edgeValue) {
        List<DecisionNodeEdgeDO> edges = edgeRepository.listByParentNode(currentNodeId);
        if (edges.isEmpty()) {
            return Collections.emptyList();
        }

        // 1. 精确匹配 edge_desc
        List<DecisionNodeEdgeDO> matched = (edgeValue == null)
                ? Collections.emptyList()
                : edges.stream()
                        .filter(e -> Objects.equals(edgeValue, e.getEdgeDesc()))
                        .collect(Collectors.toList());

        // 2. DEFAULT 边
        if (matched.isEmpty()) {
            matched = edges.stream()
                    .filter(e -> "DEFAULT".equalsIgnoreCase(e.getEdgeType()))
                    .collect(Collectors.toList());
        }

        // 3. 排序最小边
        if (matched.isEmpty()) {
            Long minOrder = edges.stream()
                    .map(DecisionNodeEdgeDO::getSortOrder)
                    .min(Long::compareTo)
                    .orElse(0L);
            matched = edges.stream()
                    .filter(e -> Objects.equals(minOrder, e.getSortOrder()))
                    .collect(Collectors.toList());
        }

        return matched.stream()
                .map(e -> nodeRepository.findByNodeId(e.getChildId()))
                .filter(Objects::nonNull)
                .map(this::convertNode)
                .collect(Collectors.toList());
    }

    private DecisionNodeBO convertNode(DecisionNodeDO entity) {
        if (entity == null) return null;
        return DecisionNodeBO.builder()
                .bizScene(entity.getBizScene())
                .nodeId(entity.getNodeId())
                .nodeName(entity.getNodeName())
                .assessmentText(entity.getAssessmentText())
                .assessmentImg(entity.getAssessmentImg())
                .guidanceText(entity.getGuidanceText())
                .status(entity.getStatus())
                .needSupply(entity.getNeedSupply())
                .needDoctor(entity.getNeedDoctor())
                .build();
    }

    private DecisionEdgeBO convertEdge(DecisionNodeEdgeDO edge) {
        return DecisionEdgeBO.builder()
                .bizScene(edge.getBizScene())
                .parentNodeId(edge.getParentId())
                .childNodeId(edge.getChildId())
                .edgeId(edge.getEdgeId())
                .edgeType(edge.getEdgeType())
                .edgeDesc(edge.getEdgeDesc())
                .sortOrder(edge.getSortOrder())
                .status(edge.getStatus())
                .build();
    }

    /** 把资源持久化对象转换为领域对象 */
    private ResourceRecommendationBO convertResource(RecommendResourceDO entity) {
        if (entity == null) return null;
        return ResourceRecommendationBO.builder()
                .bizScene(entity.getBizScene())
                .resourceId(entity.getResourceId())
                .resourceType(entity.getResourceType())
                .resourceName(entity.getResourceName())
                .shortDesc(entity.getShortDesc())
                .attributes(entity.getAttributes())
                .tags(entity.getTags())
                .build();
    }

    /** 把节点-资源关联持久化对象转换为领域对象 */
    private NodeResourceRelationBO convertRelation(NodeResourceRelationDO rel) {
        if (rel == null) return null;
        return NodeResourceRelationBO.builder()
                .bizScene(rel.getBizScene())
                .nodeId(rel.getNodeId())
                .resourceId(rel.getResourceId())
                .resourceType(rel.getResourceType())
                .sortOrder(rel.getSortOrder())
                .status(rel.getStatus())
                .rationale(rel.getRationale())
                .build();
    }

    /**
     * 返回指定业务场景所有节点实体，供上层组装为 DecisionFlowDTO
     */
    public List<DecisionNodeDO> listNodes(String bizScene) {
        return nodeRepository.listByBizScene(bizScene);
    }

    public List<DecisionNodeDO> listNodes(String bizScene, DecisionFlowElementStatusEnum status) {
        return nodeRepository.listByBizSceneAndStatus(bizScene, status.code());
    }

    /**
     * 返回指定业务场景所有路径实体
     */
    public List<DecisionNodeEdgeDO> listEdges(String bizScene) {
        return edgeRepository.listByBizScene(bizScene);
    }

    public List<DecisionNodeEdgeDO> listEdges(String bizScene, DecisionFlowElementStatusEnum status) {
        return edgeRepository.listByBizSceneAndStatus(bizScene, status.code());
    }

    /**
     * 获取完整决策网络 BO（节点 + 路径）
     */
    public DecisionFlowBO getFlow(String bizScene) {
        List<DecisionNodeDO> onlineNodes = listNodes(bizScene, DecisionFlowElementStatusEnum.ONLINE);
        Map<String, DecisionNodeDO> nodeMap = onlineNodes.stream()
                .collect(Collectors.toMap(DecisionNodeDO::getNodeId, n -> n));

        List<DecisionNodeEdgeDO> onlineEdges = listEdges(bizScene, DecisionFlowElementStatusEnum.ONLINE).stream()
                .filter(e -> nodeMap.containsKey(e.getParentId()) && nodeMap.containsKey(e.getChildId()))
                .collect(Collectors.toList());

        List<DecisionNodeBO> nodeBOs = onlineNodes.stream()
                .map(this::convertNode)
                .collect(Collectors.toList());

        List<DecisionEdgeBO> edgeBOs = onlineEdges.stream()
                .map(this::convertEdge)
                .collect(Collectors.toList());

        return DecisionFlowBO.builder()
                .bizScene(bizScene)
                .nodes(nodeBOs)
                .edges(edgeBOs)
                .build();
    }

    /**
     * 获取完整决策网络 BO（支持灰度预览）
     */
    public DecisionFlowBO getFlow(String bizScene, boolean previewGray){
        if(!previewGray){
            return getFlow(bizScene);
        }
        List<DecisionNodeDO> nodes = nodeRepository.listByBizScene(bizScene); // ONLINE + GRAY
        // 处理同一 nodeId 既有 ONLINE 又有 GRAY 的情况，优先保留 GRAY 版本
        Map<String, DecisionNodeDO> nodeMap = nodes.stream()
                .collect(Collectors.toMap(
                        DecisionNodeDO::getNodeId,
                        n -> n,
                        (existing, replacement) -> {
                            // 若两个版本冲突，优先选择 GRAY，其次保留已有
                            if (DecisionFlowElementStatusEnum.GRAY.code().equals(replacement.getStatus())) {
                                return replacement;
                            }
                            return existing;
                        }
                ));

        List<DecisionNodeEdgeDO> edges = edgeRepository.listByBizScene(bizScene).stream()
                .filter(e -> nodeMap.containsKey(e.getParentId()) && nodeMap.containsKey(e.getChildId()))
                .collect(Collectors.toList());

        List<DecisionNodeBO> nodeBOs = nodes.stream()
                .map(this::convertNode)
                .collect(Collectors.toList());

        List<DecisionEdgeBO> edgeBOs = edges.stream()
                .map(this::convertEdge)
                .collect(Collectors.toList());

        return DecisionFlowBO.builder()
                .bizScene(bizScene)
                .nodes(nodeBOs)
                .edges(edgeBOs)
                .build();
    }

    /**
     * 批量导入决策网络（增量更新模式）
     */
    @Transactional
    public boolean importFlow(DecisionFlowImportRequest dto){
        if(dto==null || dto.getBizScene()==null || dto.getBizScene().trim().isEmpty()){
            throw new IllegalArgumentException("bizScene is required");
        }
        String scene = dto.getBizScene();
        
        // ⏱️ 性能监控
        long startTime = System.currentTimeMillis();
        log.info("⏱️ ImportFlow STARTED for bizScene: {}", scene);

        // ========== 处理节点增量更新 ==========
        // 用于记录“前端索引(数组下标) / 临时 id  ->  后端生成的新 id”的映射
        Map<Integer, String> indexIdMap = new HashMap<>();

        // 移除了 ES 索引相关的代码，只保留数据库操作
        List<DecisionNodeDO> nodesToInsert = new ArrayList<>();
        List<DecisionNodeDO> nodesToUpdate = new ArrayList<>();

        if(dto.getNodes() != null && !dto.getNodes().isEmpty()) {

            // 先遍历一遍节点列表，给缺失 nodeId 的节点生成新 id，并收集全部 nodeId 方便后续一次查询
            List<DecisionNodeDTO> nodeDtoList = dto.getNodes();
            for (int idx = 0; idx < nodeDtoList.size(); idx++) {
                DecisionNodeDTO nodeDTO = nodeDtoList.get(idx);

                // 判定：nodeId 为空、全空白，或前缀为 "tmp_" / "new_" 都视为“需要后端生成”
                if (nodeDTO.getNodeId() == null || nodeDTO.getNodeId().trim().isEmpty()
                        || nodeDTO.getNodeId().startsWith("tmp_") || nodeDTO.getNodeId().startsWith("new_")) {
                    String generatedId = String.valueOf(uidUtils.nextDecisionFlowSnowflakeId());
                    indexIdMap.put(idx, generatedId);
                    nodeDTO.setNodeId(generatedId);
                }
            }

            // 批量查询已存在的节点（包含 ONLINE + GRAY），避免循环中多次 DB I/O
            long nodeQueryStart = System.currentTimeMillis();
            List<String> allNodeIds = nodeDtoList.stream().map(DecisionNodeDTO::getNodeId).toList();
            List<DecisionNodeDO> existingNodesAll = nodeRepository.listByBizScene(scene);
            log.info("⏱️ Node query: {}ms (found {} nodes)", System.currentTimeMillis() - nodeQueryStart, existingNodesAll.size());
            Map<String, DecisionNodeDO> existingGrayMap = existingNodesAll.stream()
                    .filter(n -> DecisionFlowElementStatusEnum.GRAY.code().equals(n.getStatus()))
                    .collect(Collectors.toMap(DecisionNodeDO::getNodeId, n -> n, (a,b)->a));

            // 再次遍历进行插入 / 更新分类
            for (DecisionNodeDTO nodeDTO : nodeDtoList) {
                DecisionNodeDO existingGray = existingGrayMap.get(nodeDTO.getNodeId());
                if (existingGray != null) {
                    // 已存在 GRAY 版本 → 更新
                    existingGray.setBizScene(scene);  // 确保 bizScene 被正确设置
                    existingGray.setNodeName(nodeDTO.getNodeName());
                    existingGray.setAssessmentText(nodeDTO.getAssessmentText());
                    existingGray.setAssessmentImg(nodeDTO.getAssessmentImg());
                    existingGray.setGuidanceText(nodeDTO.getGuidanceText());
                    existingGray.setNeedSupply(nodeDTO.getNeedSupply());
                    existingGray.setNeedDoctor(nodeDTO.getNeedDoctor());
                    nodesToUpdate.add(existingGray);
                } else {
                    // 不存在 GRAY → 插入新 GRAY
                    DecisionNodeDO entity = new DecisionNodeDO();
                    entity.setBizScene(scene);
                    entity.setNodeId(nodeDTO.getNodeId());
                    entity.setNodeName(nodeDTO.getNodeName());
                    entity.setAssessmentText(nodeDTO.getAssessmentText());
                    entity.setAssessmentImg(nodeDTO.getAssessmentImg());
                    entity.setGuidanceText(nodeDTO.getGuidanceText());
                    entity.setNeedSupply(nodeDTO.getNeedSupply());
                    entity.setNeedDoctor(nodeDTO.getNeedDoctor());
                    entity.setStatus(DecisionFlowElementStatusEnum.GRAY.code());
                    nodesToInsert.add(entity);
                }

                // 移除了节点 ES 索引数据的处理，减少不必要的操作
            }
         
            // 批量执行数据库操作
            long nodeDbStart = System.currentTimeMillis();
            if (!nodesToInsert.isEmpty()) {
                nodeRepository.batchInsert(nodesToInsert);
                log.info("Batch inserted {} new nodes", nodesToInsert.size());
            }
            
            if (!nodesToUpdate.isEmpty()) {
                nodeRepository.batchUpdateByNodeId(nodesToUpdate);
                log.info("Batch updated {} existing nodes", nodesToUpdate.size());
            }
            log.info("⏱️ Node DB operations: {}ms", System.currentTimeMillis() - nodeDbStart);
        }

        // ========== 处理边增量更新 ==========
        List<DecisionNodeEdgeDO> edgesToInsert = new ArrayList<>();
        List<DecisionNodeEdgeDO> edgesToUpdate = new ArrayList<>();
        
        if(dto.getEdges() != null && !dto.getEdges().isEmpty()) {
            // 边的 parentId / childId 已经是显式业务 ID，直接使用

            // 批量查询已存在的边
            List<String> edgeIds = dto.getEdges().stream()
                .map(DecisionEdgeDTO::getEdgeId)
                .filter(id -> id != null && !id.trim().isEmpty())
                .collect(Collectors.toList());
            
            if(edgeIds.isEmpty()) {
                throw new IllegalArgumentException("All edge edgeIds are required for incremental update");
            }
            
            // 分类查询已存在边：区分 ONLINE 与 GRAY
            long edgeQueryStart = System.currentTimeMillis();
            List<DecisionNodeEdgeDO> existingEdgesAll = edgeRepository.listByBizScene(scene);
            log.info("⏱️ Edge query: {}ms (found {} edges)", System.currentTimeMillis() - edgeQueryStart, existingEdgesAll.size());
            Map<String, DecisionNodeEdgeDO> existingGrayEdgeMap = existingEdgesAll.stream()
                    .filter(e -> DecisionFlowElementStatusEnum.GRAY.code().equals(e.getStatus()))
                    .collect(Collectors.toMap(DecisionNodeEdgeDO::getEdgeId, e -> e, (a,b)->a));
            Map<String, DecisionNodeEdgeDO> existingOnlineEdgeMap = existingEdgesAll.stream()
                    .filter(e -> DecisionFlowElementStatusEnum.ONLINE.code().equals(e.getStatus()))
                    .collect(Collectors.toMap(DecisionNodeEdgeDO::getEdgeId, e -> e, (a,b)->a));
            
            for(DecisionEdgeDTO edgeDTO : dto.getEdges()) {
                if(edgeDTO.getEdgeId() == null || edgeDTO.getEdgeId().trim().isEmpty()) {
                    continue; // 已在上面检查过
                }
                
                DecisionNodeEdgeDO existingGray = existingGrayEdgeMap.get(edgeDTO.getEdgeId());
                if (existingGray != null) {
                    // 更新 GRAY 版本
                    existingGray.setBizScene(scene);  // 确保 bizScene 被正确设置
                    existingGray.setParentId(edgeDTO.getParentId());
                    existingGray.setChildId(edgeDTO.getChildId());
                    existingGray.setEdgeType(edgeDTO.getEdgeType());
                    existingGray.setEdgeDesc(edgeDTO.getEdgeDesc());
                    existingGray.setSortOrder(edgeDTO.getSortOrder());
                    edgesToUpdate.add(existingGray);
                } else {
                    // 不存在，准备插入
                    DecisionNodeEdgeDO entity = new DecisionNodeEdgeDO();
                    entity.setBizScene(scene);
                    entity.setEdgeId(edgeDTO.getEdgeId());
                    entity.setParentId(edgeDTO.getParentId());
                    entity.setChildId(edgeDTO.getChildId());
                    entity.setEdgeType(edgeDTO.getEdgeType());
                    entity.setEdgeDesc(edgeDTO.getEdgeDesc());
                    entity.setSortOrder(edgeDTO.getSortOrder());
                    entity.setStatus(DecisionFlowElementStatusEnum.GRAY.code());
                    
                    edgesToInsert.add(entity);
                }
            }
            
            // 批量执行数据库操作
            if (!edgesToInsert.isEmpty()) {
                edgeRepository.batchInsert(edgesToInsert);
                log.info("Batch inserted {} new edges", edgesToInsert.size());
            }
            
            if (!edgesToUpdate.isEmpty()) {
                edgeRepository.batchUpdateByEdgeId(edgesToUpdate);
                log.info("Batch updated {} existing edges", edgesToUpdate.size());
            }
        }

        // ========== 处理节点删除列表 ==========
        if(dto.getDeletedNodeIds() != null && !dto.getDeletedNodeIds().isEmpty()) {
            List<String> validDeletedNodeIds = dto.getDeletedNodeIds().stream()
                .filter(nodeId -> nodeId != null && !nodeId.trim().isEmpty())
                .collect(Collectors.toList());
            
            if (!validDeletedNodeIds.isEmpty()) {
                // 批量更新状态为GRAY_DELETE
                int deletedCount = nodeRepository.batchUpdateStatusByNodeIds(
                    validDeletedNodeIds, DecisionFlowElementStatusEnum.GRAY_DELETE.code());
                log.info("Batch marked {} nodes for deletion with GRAY_DELETE status", deletedCount);
            }
        }

        // ========== 处理边删除列表 ==========
        if(dto.getDeletedEdgeIds() != null && !dto.getDeletedEdgeIds().isEmpty()) {
            List<String> validDeletedEdgeIds = dto.getDeletedEdgeIds().stream()
                .filter(edgeId -> edgeId != null && !edgeId.trim().isEmpty())
                .collect(Collectors.toList());
            
            if (!validDeletedEdgeIds.isEmpty()) {
                // 批量更新状态为GRAY_DELETE
                int deletedCount = edgeRepository.batchUpdateStatusByEdgeIds(
                    validDeletedEdgeIds, DecisionFlowElementStatusEnum.GRAY_DELETE.code());
                log.info("Batch marked {} edges for deletion with GRAY_DELETE status", deletedCount);
            }
        }

        // ========== 处理资源和关联关系（保持原有逻辑） ==========
        // 使用字符串作为 key 以同时兼容「数组下标」和「前端引用 ID（如 res_001）」两种形式
        Map<String, String> resourceIndexIdMap = new HashMap<>();
        if(dto.getResources() != null && !dto.getResources().isEmpty()) {
            long resourceStart = System.currentTimeMillis();
            List<ResourceRecommendationDTO> resourceList = dto.getResources();

            // === 一次性查询已有资源（ONLINE + GRAY） ===
            List<RecommendResourceDO> existingOnline = resourceRepository.listByBizSceneAndStatus(scene, DecisionFlowElementStatusEnum.ONLINE.code());
            List<RecommendResourceDO> existingGray   = resourceRepository.listByBizSceneAndStatus(scene, DecisionFlowElementStatusEnum.GRAY.code());
            Map<String, RecommendResourceDO> existingMap = new HashMap<>();
            Stream.concat(existingOnline.stream(), existingGray.stream())
                    .forEach(r -> existingMap.put(r.getResourceName()+"##"+r.getResourceType(), r));

            List<RecommendResourceDO> toInsert = new ArrayList<>();
            List<RecommendResourceDO> toUpdate = new ArrayList<>();

            for (ResourceRecommendationDTO resource : resourceList) {
                String key = resource.getResourceName()+"##"+resource.getResourceType();
                RecommendResourceDO exist = existingMap.get(key);
                String finalResourceId;
                if (exist != null) {
                    finalResourceId = exist.getResourceId();
                    // 如果是 GRAY 版本需要更新
                    if (DecisionFlowElementStatusEnum.GRAY.code().equals(exist.getStatus())) {
                        exist.setShortDesc(resource.getShortDesc());
                        exist.setAttributes(resource.getAttributes());
                        exist.setTags(resource.getTags());
                        toUpdate.add(exist);
                    }
                    // ONLINE 版本复用即可，无需更新
                } else {
                    finalResourceId = String.valueOf(uidUtils.nextDecisionFlowSnowflakeId());
                    RecommendResourceDO entity = new RecommendResourceDO();
                    entity.setBizScene(scene);
                    entity.setResourceId(finalResourceId);
                    entity.setResourceType(resource.getResourceType());
                    entity.setResourceName(resource.getResourceName());
                    entity.setShortDesc(resource.getShortDesc());
                    entity.setAttributes(resource.getAttributes());
                    entity.setTags(resource.getTags());
                    entity.setStatus(DecisionFlowElementStatusEnum.GRAY.code());
                    toInsert.add(entity);
                }

                if (resource.getResourceId() != null && !resource.getResourceId().trim().isEmpty()) {
                    resourceIndexIdMap.put(resource.getResourceId(), finalResourceId);
                }
            }

            if(!toInsert.isEmpty()){
                resourceRepository.batchInsert(toInsert);
                log.info("Batch inserted {} new resources", toInsert.size());
            }
            if(!toUpdate.isEmpty()){
                resourceRepository.batchUpdateByResourceIds(toUpdate);
                log.info("Batch updated {} resources", toUpdate.size());
            }

            log.info("⏱️ Resource processing: {}ms ({} resources)", System.currentTimeMillis() - resourceStart, resourceList.size());
        }

        // 处理关联关系 - 批量插入
        if(dto.getRelations() != null && !dto.getRelations().isEmpty()) {
            List<NodeResourceRelationDO> relationEntities = dto.getRelations().stream()
                .map(relationDTO -> {
                    NodeResourceRelationDO entity = new NodeResourceRelationDO();
                    entity.setBizScene(scene);
                    // 解析 relation 中引用的新节点索引
                    entity.setNodeId(resolveNodeRef(relationDTO.getNodeId()));
                    entity.setResourceId(resolveResourceRef(relationDTO.getResourceId(), resourceIndexIdMap));
                    entity.setResourceType(relationDTO.getResourceType());
                    entity.setSortOrder(relationDTO.getSortOrder());
                    entity.setRationale(relationDTO.getRationale());
                    entity.setStatus(RelationStatusEnum.GRAY.code());
                    return entity;
                })
                .collect(Collectors.toList());

            // 批量插入关联关系，减少数据库往返
            int inserted = relationRepository.batchInsert(relationEntities);
            log.info("Batch inserted {} relations", inserted);
        }

        // 处理删除的关联关系和资源 - 批量删除
        if(dto.getDeletedRelationIds() != null && !dto.getDeletedRelationIds().isEmpty()) {
            List<Long> validRelationIds = dto.getDeletedRelationIds().stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

            if (!validRelationIds.isEmpty()) {
                relationRepository.batchDeleteByIds(validRelationIds);
                log.info("Batch marked {} relations for deletion", validRelationIds.size());
            }
        }

        if(dto.getDeletedResourceIds() != null && !dto.getDeletedResourceIds().isEmpty()) {
            List<String> validResourceIds = dto.getDeletedResourceIds().stream()
                .filter(resourceId -> resourceId != null && !resourceId.trim().isEmpty())
                .collect(Collectors.toList());

            if (!validResourceIds.isEmpty()) {
                resourceRepository.batchDeleteByResourceIds(validResourceIds);
                log.info("Batch marked {} resources for deletion", validResourceIds.size());
            }
        }

        // 删除单独的 ES 写入逻辑，统一放到 publishGray 里处理，避免重复索引

        // ---------- 异步触发灰度发布，使 GRAY / GRAY_DELETE 与 ES 同步 ----------
        long publishGrayStart = System.currentTimeMillis();
        // 使用CompletableFuture异步执行ES索引，避免阻塞主流程
        CompletableFuture.runAsync(() -> publishGray(scene), ES_INDEX_TASK_POOL.getExecutor())
            .whenComplete((result, throwable) -> {
                if (throwable != null) {
                    log.error("⚠️ PublishGray async failed for bizScene: {}", scene, throwable);
                } else {
                    log.info("⏱️ PublishGray async completed: {}ms for bizScene: {}",
                        System.currentTimeMillis() - publishGrayStart, scene);
                }
            });
        log.info("⏱️ PublishGray async submitted: {}ms", System.currentTimeMillis() - publishGrayStart);

        // ⏱️ 总体耗时监控
        long totalTime = System.currentTimeMillis() - startTime;
        log.info("⏱️ ImportFlow COMPLETED: TOTAL {}ms for bizScene: {}", totalTime, scene);
        log.info("📊 Summary - nodes: {} (inserted: {}, updated: {}), edges: {} (inserted: {}, updated: {}), deletedNodes: {}, deletedEdges: {}", 
            dto.getNodes() != null ? dto.getNodes().size() : 0,
            nodesToInsert.size(),
            nodesToUpdate.size(),
            dto.getEdges() != null ? dto.getEdges().size() : 0,
            edgesToInsert.size(),
            edgesToUpdate.size(),
            dto.getDeletedNodeIds() != null ? dto.getDeletedNodeIds().size() : 0,
            dto.getDeletedEdgeIds() != null ? dto.getDeletedEdgeIds().size() : 0
        );
        
        // ⚠️ 性能警告
        if (totalTime > 10000) {
            log.warn("⚠️ PERFORMANCE WARNING: ImportFlow took {}ms (>10s) for bizScene: {}", totalTime, scene);
        }
        
        return true;
    }

    /**
     * 灰度发布：处理GRAY和GRAY_DELETE状态的数据，让灰度流量能看到变更
     */
    @Transactional
    public long publishGray(String bizScene) {
        long batchId = System.currentTimeMillis();

        // 1. 处理GRAY_DELETE状态的节点 - 从ES中删除这些文档
        List<DecisionNodeDO> grayDeleteNodes = nodeRepository.listByBizSceneAndStatus(bizScene, DecisionFlowElementStatusEnum.GRAY_DELETE.code());
        if (!grayDeleteNodes.isEmpty()) {
            List<String> deleteNodeIds = grayDeleteNodes.stream()
                    .map(DecisionNodeDO::getNodeId)
                    .toList();
            long esDeleteStart = System.currentTimeMillis();
            try {
                vectorStoreRepository.deleteDocs(deleteNodeIds);
                log.info("⏱️ ES delete docs: {}ms (removed {} GRAY_DELETE nodes)", 
                    System.currentTimeMillis() - esDeleteStart, deleteNodeIds.size());
            } catch (Exception e) {
                log.warn("Failed to remove GRAY_DELETE nodes from ES", e);
            }
        }

        // 2. 处理GRAY状态的节点 - 更新ES索引状态，让灰度检索能找到
        List<DecisionNodeDO> grayNodes = nodeRepository.listByBizSceneAndStatus(bizScene, DecisionFlowElementStatusEnum.GRAY.code());
        if (!grayNodes.isEmpty()) {
            long esIndexStart = System.currentTimeMillis();
            try {
                List<NodeIndexData> grayIndexData = grayNodes.stream()
                    .map(node -> {
                        StringBuilder sb = new StringBuilder();
                        if (node.getNodeName() != null) sb.append(node.getNodeName());
                        return new NodeIndexData(bizScene, node.getNodeId(), sb.toString().trim(), DecisionFlowElementStatusEnum.GRAY.code());
                    })
                    .toList();

                                // ES 单次最多 4 条，改为并行批量
                int batchSize = 4;
                List<CompletableFuture<Void>> futures = new ArrayList<>();
                for (int i = 0; i < grayIndexData.size(); i += batchSize) {
                    int end = Math.min(i + batchSize, grayIndexData.size());
                    List<NodeIndexData> sub = grayIndexData.subList(i, end);
                    futures.add(CompletableFuture.runAsync(() -> {
                        try {
                            vectorStoreRepository.indexNodes(sub);
                        } catch (Exception ex) {
                            log.warn("indexNodes batch failed", ex);
                        }
                    }, ES_INDEX_TASK_POOL.getExecutor()));
                }
                CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
                log.info("⏱️ ES index GRAY nodes: {}ms (indexed {} nodes)", 
                    System.currentTimeMillis() - esIndexStart, grayNodes.size());
            } catch (Exception e) {
                log.warn("Failed to update GRAY nodes in ES", e);
            }
        }

        log.info("Gray published for bizScene: {}, batchId: {}", bizScene, batchId);
        return batchId;
    }

    /**
     * 回滚灰度：删除所有 GRAY / GRAY_DELETE 行，线上立即恢复 ONLINE 版本。
     */
    @Transactional
    public void rollbackGray(String bizScene) {
        // 批量删除节点 GRAY 和 GRAY_DELETE
        List<DecisionNodeDO> grayNodes = nodeRepository.listByBizSceneAndStatus(bizScene, DecisionFlowElementStatusEnum.GRAY.code());
        List<DecisionNodeDO> delNodes = nodeRepository.listByBizSceneAndStatus(bizScene, DecisionFlowElementStatusEnum.GRAY_DELETE.code());

        // 批量删除节点（通过状态批量更新实现软删除）
        if (!grayNodes.isEmpty() || !delNodes.isEmpty()) {
            List<String> allNodeIds = Stream.concat(grayNodes.stream(), delNodes.stream())
                    .map(DecisionNodeDO::getNodeId)
                    .collect(Collectors.toList());
            nodeRepository.batchUpdateStatusByNodeIds(allNodeIds, DecisionFlowElementStatusEnum.ONLINE_DELETE.code());
            log.info("Batch deleted {} nodes in rollbackGray", allNodeIds.size());
        }

        // 批量删除边 GRAY 和 GRAY_DELETE
        List<DecisionNodeEdgeDO> grayEdges = edgeRepository.listByBizSceneAndStatus(bizScene, DecisionFlowElementStatusEnum.GRAY.code());
        List<DecisionNodeEdgeDO> delEdges = edgeRepository.listByBizSceneAndStatus(bizScene, DecisionFlowElementStatusEnum.GRAY_DELETE.code());

        if (!grayEdges.isEmpty() || !delEdges.isEmpty()) {
            List<String> allEdgeIds = Stream.concat(grayEdges.stream(), delEdges.stream())
                    .map(DecisionNodeEdgeDO::getEdgeId)
                    .collect(Collectors.toList());
            edgeRepository.batchUpdateStatusByEdgeIds(allEdgeIds, DecisionFlowElementStatusEnum.ONLINE_DELETE.code());
            log.info("Batch deleted {} edges in rollbackGray", allEdgeIds.size());
        }

        // 同步删除 ES 索引（仅节点）
        List<String> delIds = Stream.concat(grayNodes.stream(), delNodes.stream())
                                   .map(DecisionNodeDO::getNodeId)
                                   .toList();
        if (!delIds.isEmpty()) {
            try { vectorStoreRepository.deleteDocs(delIds); } catch (Exception ex) { log.warn("delete gray docs failed", ex); }
        }
    }

    /**
     * 正式上线：事务内删除旧 ONLINE，灰度转 ONLINE，并硬删 *_DELETE
     */
    @Transactional
    public void grayToOnline(String bizScene) {
        // 1. 批量删除与灰度重叠的 ONLINE 旧数据
        List<DecisionNodeDO> grayNodes = nodeRepository.listByBizSceneAndStatus(bizScene, DecisionFlowElementStatusEnum.GRAY.code());
        List<DecisionNodeEdgeDO> grayEdges = edgeRepository.listByBizSceneAndStatus(bizScene, DecisionFlowElementStatusEnum.GRAY.code());

        if (!grayNodes.isEmpty()) {
            // 先找出与灰度节点 nodeId 重叠的 ONLINE 版本，避免误把 GRAY 版本标记为删除
            Set<String> grayNodeIds = grayNodes.stream()
                    .map(DecisionNodeDO::getNodeId)
                    .collect(Collectors.toSet());

            List<String> overlapOnlineNodeIds = nodeRepository
                    .listByBizSceneAndStatus(bizScene, DecisionFlowElementStatusEnum.ONLINE.code())
                    .stream()
                    .filter(n -> grayNodeIds.contains(n.getNodeId()))
                    .map(DecisionNodeDO::getNodeId)
                    .collect(Collectors.toList());

            if (!overlapOnlineNodeIds.isEmpty()) {
                // 为避免唯一键 (bizScene,nodeId,status) 冲突：
                // 先清理已有 ONLINE_DELETE 记录，再将 ONLINE → ONLINE_DELETE（限定来源状态）
                try {
                    nodeRepository.hardDeleteByNodeIdsAndStatus(bizScene, overlapOnlineNodeIds, DecisionFlowElementStatusEnum.ONLINE_DELETE.code());
                } catch (Exception ex) {
                    log.warn("hardDelete existing ONLINE_DELETE before marking ONLINE → ONLINE_DELETE failed", ex);
                }
                nodeRepository.batchUpdateStatusByNodeIds(
                        bizScene,
                        overlapOnlineNodeIds,
                        DecisionFlowElementStatusEnum.ONLINE.code(),
                        DecisionFlowElementStatusEnum.ONLINE_DELETE.code()
                );
                log.info("Batch deleted {} overlapping ONLINE nodes", overlapOnlineNodeIds.size());
            }
        }

        if (!grayEdges.isEmpty()) {
            // 仅删除与灰度边 edgeId 重叠的 ONLINE 版本，避免误删 GRAY 版本
            Set<String> grayEdgeIds = grayEdges.stream()
                    .map(DecisionNodeEdgeDO::getEdgeId)
                    .collect(Collectors.toSet());

            List<String> overlapOnlineEdgeIds = edgeRepository
                    .listByBizSceneAndStatus(bizScene, DecisionFlowElementStatusEnum.ONLINE.code())
                    .stream()
                    .filter(e -> grayEdgeIds.contains(e.getEdgeId()))
                    .map(DecisionNodeEdgeDO::getEdgeId)
                    .collect(Collectors.toList());

            if (!overlapOnlineEdgeIds.isEmpty()) {
                edgeRepository.batchUpdateStatusByEdgeIds(overlapOnlineEdgeIds, DecisionFlowElementStatusEnum.ONLINE_DELETE.code());
                log.info("Batch deleted {} overlapping ONLINE edges", overlapOnlineEdgeIds.size());
            }
        }

        // 2. 将 GRAY 版本逐条更新为 ONLINE（按主键避免误改其他状态）
        if (!grayNodes.isEmpty()) {
            List<String> grayNodeIds = grayNodes.stream().map(DecisionNodeDO::getNodeId).toList();
            // 为避免唯一键 (bizScene, nodeId, status) 冲突，先删除可能存在的 ONLINE_DELETE 旧记录
            try {
                nodeRepository.hardDeleteByNodeIdsAndStatus(bizScene, grayNodeIds, DecisionFlowElementStatusEnum.ONLINE_DELETE.code());
            } catch (Exception ex) {
                log.warn("hardDelete ONLINE_DELETE nodes before ONLINE update failed", ex);
            }
            // 仅将 GRAY 转为 ONLINE，限定来源状态，避免误更新其他状态
            nodeRepository.batchUpdateStatusByNodeIds(bizScene, grayNodeIds, DecisionFlowElementStatusEnum.GRAY.code(), DecisionFlowElementStatusEnum.ONLINE.code());
            log.info("Batch updated {} nodes (GRAY → ONLINE)", grayNodeIds.size());
        }

        if (!grayEdges.isEmpty()) {
            List<String> grayEdgeIds = grayEdges.stream().map(DecisionNodeEdgeDO::getEdgeId).toList();
            edgeRepository.batchUpdateStatusByEdgeIds(grayEdgeIds, DecisionFlowElementStatusEnum.ONLINE.code());
            log.info("Batch updated {} edges (GRAY → ONLINE)", grayEdgeIds.size());
        }

        // ---------- 将 GRAY_DELETE 转为 ONLINE_DELETE ----------
        List<DecisionNodeDO> grayDeleteNodes = nodeRepository.listByBizSceneAndStatus(bizScene, DecisionFlowElementStatusEnum.GRAY_DELETE.code());
        if (!grayDeleteNodes.isEmpty()) {
            List<String> ids = grayDeleteNodes.stream().map(DecisionNodeDO::getNodeId).toList();
            // 同理，转换前先清理已有 ONLINE_DELETE 记录
            try {
                nodeRepository.hardDeleteByNodeIdsAndStatus(bizScene, ids, DecisionFlowElementStatusEnum.ONLINE_DELETE.code());
            } catch (Exception ex) {
                log.warn("hardDelete ONLINE_DELETE nodes before convert GRAY_DELETE → ONLINE_DELETE failed", ex);
            }
            // 仅将 GRAY_DELETE 转为 ONLINE_DELETE
            nodeRepository.batchUpdateStatusByNodeIds(bizScene, ids, DecisionFlowElementStatusEnum.GRAY_DELETE.code(), DecisionFlowElementStatusEnum.ONLINE_DELETE.code());
            log.info("Converted {} nodes GRAY_DELETE → ONLINE_DELETE", ids.size());
        }

        List<DecisionNodeEdgeDO> grayDeleteEdges = edgeRepository.listByBizSceneAndStatus(bizScene, DecisionFlowElementStatusEnum.GRAY_DELETE.code());
        if (!grayDeleteEdges.isEmpty()) {
            List<String> ids = grayDeleteEdges.stream().map(DecisionNodeEdgeDO::getEdgeId).toList();
            edgeRepository.batchUpdateStatusByEdgeIds(ids, DecisionFlowElementStatusEnum.ONLINE_DELETE.code());
            log.info("Converted {} edges GRAY_DELETE → ONLINE_DELETE", ids.size());
        }

        List<RecommendResourceDO> grayDeleteResources = resourceRepository.listByStatus(DecisionFlowElementStatusEnum.GRAY_DELETE.code());
        if (!grayDeleteResources.isEmpty()) {
            resourceRepository.batchUpdateStatusByResourceIds(
                    grayDeleteResources.stream().map(RecommendResourceDO::getResourceId).toList(),
                    DecisionFlowElementStatusEnum.ONLINE_DELETE.code());
            log.info("Converted {} resources GRAY_DELETE → ONLINE_DELETE", grayDeleteResources.size());
        }

        List<NodeResourceRelationDO> grayDeleteRelations = relationRepository.listByStatus(bizScene, RelationStatusEnum.GRAY_DELETE.code());
        if (!grayDeleteRelations.isEmpty()) {
            relationRepository.batchUpdateStatusByIds(
                    grayDeleteRelations.stream().map(NodeResourceRelationDO::getId).toList(),
                    RelationStatusEnum.ONLINE_DELETE.code());
            log.info("Converted {} relations GRAY_DELETE → ONLINE_DELETE", grayDeleteRelations.size());
        }

        // 3. 批量硬删除 *_DELETE 状态的数据
        List<DecisionNodeDO> delNodes = nodeRepository.listByBizSceneAndStatus(bizScene, DecisionFlowElementStatusEnum.ONLINE_DELETE.code());
        List<DecisionNodeEdgeDO> delEdges = edgeRepository.listByBizSceneAndStatus(bizScene, DecisionFlowElementStatusEnum.ONLINE_DELETE.code());

        // (已移除 HARD_DELETE 逻辑) 若需要统计 ONLINE_DELETE 数量，可在日志中单独打印 delNodes.size()/delEdges.size()

        // 同步 ES：1) 删除已硬删节点 2) 更新原 GRAY -> ONLINE
        List<String> deleteIds = delNodes.stream().map(DecisionNodeDO::getNodeId).toList();
        if(!deleteIds.isEmpty()){
            try{ vectorStoreRepository.deleteDocs(deleteIds);}catch(Exception ex){log.warn("delete docs when online failed",ex);}        }

        // 重新索引新 ONLINE 节点（灰度转正）- 并行分批处理
        List<NodeIndexData> reindex = grayNodes.stream()
                .map(n -> {
                    StringBuilder sb = new StringBuilder();
                    if (n.getNodeName() != null) sb.append(n.getNodeName());
                    return new NodeIndexData(bizScene, n.getNodeId(), sb.toString().trim(), DecisionFlowElementStatusEnum.ONLINE.code());
                })
                .toList();
        if(!reindex.isEmpty()){
            int batchSize = 4;
            List<CompletableFuture<Void>> futures = new ArrayList<>();
            for (int i = 0; i < reindex.size(); i += batchSize) {
                int endIndex = Math.min(i + batchSize, reindex.size());
                List<NodeIndexData> batch = reindex.subList(i, endIndex);
                final int batchNum = (i / batchSize) + 1;
                final int totalBatches = (reindex.size() + batchSize - 1) / batchSize;
                futures.add(CompletableFuture.runAsync(() -> {
                    try {
                        vectorStoreRepository.indexNodes(batch);
                        log.info("Successfully indexed ONLINE node batch {}/{}, size: {}", batchNum, totalBatches, batch.size());
                    } catch (Exception ex) {
                        log.warn("indexNodes batch {}/{} failed", batchNum, totalBatches, ex);
                    }
                }, ES_INDEX_TASK_POOL.getExecutor()));
            }
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
        }

        // ---------- 批量处理资源 Gray -> Online & 删除 ----------
        List<RecommendResourceDO> grayResources = resourceRepository.listByStatus(DecisionFlowElementStatusEnum.GRAY.code());
        if (!grayResources.isEmpty()) {
            resourceRepository.batchUpdateStatusByResourceIds(
                grayResources.stream().map(RecommendResourceDO::getResourceId).collect(Collectors.toList()),
                DecisionFlowElementStatusEnum.ONLINE.code()
            );
            log.info("Batch updated {} resources from GRAY to ONLINE", grayResources.size());
        }

        List<RecommendResourceDO> delResources = resourceRepository.listByStatus(DecisionFlowElementStatusEnum.ONLINE_DELETE.code());
        if (!delResources.isEmpty()) {
            resourceRepository.batchDeleteByResourceIds(
                delResources.stream().map(RecommendResourceDO::getResourceId).collect(Collectors.toList())
            );
            log.info("Batch deleted {} resources", delResources.size());
        }

        // ---------- 批量处理关系 Gray -> Online & 删除 ----------
        List<NodeResourceRelationDO> grayRelations = relationRepository.listByStatus(bizScene, RelationStatusEnum.GRAY.code());
        if (!grayRelations.isEmpty()) {
            relationRepository.batchUpdateStatusByIds(
                grayRelations.stream().map(NodeResourceRelationDO::getId).collect(Collectors.toList()),
                RelationStatusEnum.ONLINE.code()
            );
            log.info("Batch updated {} relations from GRAY to ONLINE", grayRelations.size());
        }

        List<NodeResourceRelationDO> delRelations = relationRepository.listByStatus(bizScene, RelationStatusEnum.ONLINE_DELETE.code());
        if (!delRelations.isEmpty()) {
            relationRepository.batchDeleteByIds(
                delRelations.stream().map(NodeResourceRelationDO::getId).collect(Collectors.toList())
            );
            log.info("Batch deleted {} relations", delRelations.size());
        }
    }

    /**
     * 将前端传来的节点引用（可能是列表下标，也可能是临时 nodeId）转换为真实新生成的 nodeId。
     */
    // 当前设计：前端总是传显式业务 ID，无需再做下标映射
    private String resolveNodeRef(String ref) {
        return ref;
    }

    /**
     * 将前端传来的资源引用（列表下标）转换为真实新生成的 resourceId。
     */
    private String resolveResourceRef(String ref, Map<String, String> resourceIndexIdMap) {
        // 使用 resourceIndexIdMap 进行映射转换
        return resourceIndexIdMap.getOrDefault(ref, ref);
    }

    /* ---------- Node CRUD ---------- */
    public void addNode(DecisionNodeDTO dto){
        if(nodeRepository.findByNodeId(dto.getNodeId())!=null){
            throw new IllegalArgumentException("nodeId exists");
        }
        DecisionNodeDO e=new DecisionNodeDO();
        e.setBizScene(dto.getBizScene());
        e.setNodeId(dto.getNodeId());
        e.setNodeName(dto.getNodeName());
        e.setAssessmentText(dto.getAssessmentText());
        e.setAssessmentImg(dto.getAssessmentImg());
        e.setGuidanceText(dto.getGuidanceText());
        e.setStatus(dto.getStatus());
        nodeRepository.insert(e);
        try {
            StringBuilder sb=new StringBuilder();
            if(dto.getNodeName()!=null) sb.append(dto.getNodeName());
            vectorStoreRepository.indexNodes(List.of(new NodeIndexData(dto.getBizScene(), dto.getNodeId(), sb.toString().trim(), dto.getStatus())));
        }catch(Exception ex){log.warn("index addNode failed",ex);}  
    }

    public void updateNode(DecisionNodeDTO dto){
        DecisionNodeDO e=new DecisionNodeDO();
        e.setNodeId(dto.getNodeId());
        e.setNodeName(dto.getNodeName());
        e.setAssessmentText(dto.getAssessmentText());
        e.setAssessmentImg(dto.getAssessmentImg());
        e.setGuidanceText(dto.getGuidanceText());
        e.setStatus(dto.getStatus());
        nodeRepository.updateByNodeId(e);
        try{
            StringBuilder sb=new StringBuilder();
            if(dto.getNodeName()!=null) sb.append(dto.getNodeName());
            vectorStoreRepository.indexNodes(List.of(new NodeIndexData("", dto.getNodeId(), sb.toString().trim(), dto.getStatus())));
        }catch(Exception ex){log.warn("index updateNode failed",ex);}  
    }

    public void deleteNode(String nodeId){
        // delete relations and edges first
        edgeRepository.deleteByParentNode(nodeId);
        relationRepository.deleteByNodeId(nodeId);
        nodeRepository.deleteByNodeId(nodeId);
        try{vectorStoreRepository.deleteDocs(List.of(nodeId));}catch(Exception ex){log.warn("delete node doc failed",ex);}  
    }

    public void patchNodeStatus(String nodeId,String status){
        DecisionNodeDO e=new DecisionNodeDO();
        e.setNodeId(nodeId);
        e.setStatus(status);
        nodeRepository.updateByNodeId(e);
        try{
            if("ONLINE".equalsIgnoreCase(status)){
                DecisionNodeDO entity=nodeRepository.findByNodeId(nodeId);
                if(entity!=null){
                    StringBuilder sb=new StringBuilder();
                    if(entity.getNodeName()!=null) sb.append(entity.getNodeName());
                    vectorStoreRepository.indexNodes(List.of(new NodeIndexData(entity.getBizScene(), nodeId, sb.toString().trim(), status)));}
            }else{
                vectorStoreRepository.deleteDocs(List.of(nodeId));
            }
        }catch(Exception ex){log.warn("sync patchNodeStatus to ES failed",ex);}  
    }

    /* ---------- Edge CRUD ---------- */
    public void addEdge(DecisionEdgeDTO dto){
        if(edgeRepository.findByEdgeId(dto.getEdgeId())!=null){
            throw new IllegalArgumentException("edgeId exists");
        }
        if(nodeRepository.findByNodeId(dto.getParentId())==null||nodeRepository.findByNodeId(dto.getChildId())==null){
            throw new IllegalArgumentException("parent/child node not exist");
        }
        DecisionNodeEdgeDO edge=new DecisionNodeEdgeDO();
        edge.setBizScene(dto.getBizScene());
        edge.setEdgeId(dto.getEdgeId());
        edge.setParentId(dto.getParentId());
        edge.setChildId(dto.getChildId());
        edge.setEdgeType(dto.getEdgeType());
        edge.setEdgeDesc(dto.getEdgeDesc());
        edge.setSortOrder(dto.getSortOrder());
        edge.setStatus(DecisionFlowElementStatusEnum.GRAY.code());
        edgeRepository.insert(edge);
    }

    public void updateEdge(DecisionEdgeDTO dto){
        DecisionNodeEdgeDO edge=new DecisionNodeEdgeDO();
        edge.setEdgeId(dto.getEdgeId());
        edge.setEdgeType(dto.getEdgeType());
        edge.setEdgeDesc(dto.getEdgeDesc());
        edge.setSortOrder(dto.getSortOrder());
        edgeRepository.updateByEdgeId(edge);
    }

    public void deleteEdge(String edgeId){
        edgeRepository.deleteByEdgeId(edgeId);
    }

    /* ---------- Relation CRUD ---------- */
    public void addRelation(NodeResourceRelationDTO dto){
        if(nodeRepository.findByNodeId(dto.getNodeId())==null){
            throw new IllegalArgumentException("node not exist");
        }
        NodeResourceRelationDO rel=new NodeResourceRelationDO();
        rel.setBizScene(dto.getBizScene());
        rel.setNodeId(dto.getNodeId());
        rel.setResourceId(dto.getResourceId());
        rel.setResourceType(dto.getResourceType());
        rel.setSortOrder(dto.getSortOrder());
        rel.setRationale(dto.getRationale());
        rel.setStatus(RelationStatusEnum.GRAY.code());
        relationRepository.insert(rel);
    }

    public void deleteRelation(Long id){
        relationRepository.deleteById(id);
    }

    /* ---------- Search ---------- */
    public List<DecisionNodeBO> searchNodes(String bizScene, String query, int topK) {
        // 为了避免 topK 较小时候候选不足（如 topK = 1 无结果）的问题，
        // 这里拉取更大的候选集合，然后再按照 topK 截断。
        int fetchK = topK < 3 ? 10 : topK * 3;   // 至少 10 条，或者 3 倍 topK

        List<String> nodeIds = vectorStoreRepository.searchNodeIds(bizScene, query, fetchK);

        List<DecisionNodeBO> candidates = nodeIds.stream()
                .map(nodeRepository::findByNodeId)
                .filter(Objects::nonNull)
                .map(this::convertNode)
                .collect(Collectors.toList());

        // 截断到调用方期望的 topK 数量
        if (candidates.size() > topK) {
            return candidates.subList(0, topK);
        }
        return candidates;
    }

    /**
     * 搜索节点，支持合并 GRAY 数据
     */
    public List<DecisionNodeBO> searchNodes(String bizScene, String query, int topK, boolean previewGray) {
        if (!previewGray) {
            return searchNodes(bizScene, query, topK);
        }

        int fetchK = topK < 3 ? 10 : topK * 3;

        // ONLINE + GRAY
        List<String> nodeIds = vectorStoreRepository.searchNodeIdsGray(bizScene, query, fetchK);

        List<DecisionNodeBO> candidates = nodeIds.stream()
                .map(nodeRepository::findByNodeId)
                .filter(Objects::nonNull)
                .map(this::convertNode)
                .collect(Collectors.toList());

        if (candidates.size() > topK) {
            return candidates.subList(0, topK);
        }
        return candidates;
    }

    /**
     * 获取数据库中所有不同的业务场景
     */
    public List<String> listAllBizScenes() {
        return nodeRepository.listAllBizScenes();
    }

    /* ---------- 图结构专用查询方法 ---------- */

    /**
     * 获取决策树概览信息
     */
    public DecisionFlowSummaryBO getFlowSummary(String bizScene) {
        List<DecisionNodeDO> allNodes = nodeRepository.listByBizScene(bizScene);
        List<DecisionNodeEdgeDO> allEdges = edgeRepository.listByBizScene(bizScene);

        // 计算根节点（没有入边的节点）
        Set<String> hasParent = allEdges.stream().map(DecisionNodeEdgeDO::getChildId).collect(Collectors.toSet());
        List<DecisionNodeDO> rootNodeEntities = allNodes.stream()
                .filter(node -> !hasParent.contains(node.getNodeId()))
                .collect(Collectors.toList());

        List<DecisionNodeBO> rootNodes = rootNodeEntities.stream()
                .map(this::convertNode)
                .collect(Collectors.toList());

        // 计算叶子节点数量（没有出边的节点）
        Set<String> hasChild = allEdges.stream().map(DecisionNodeEdgeDO::getParentId).collect(Collectors.toSet());
        long leafNodeCount = allNodes.stream()
                .filter(node -> !hasChild.contains(node.getNodeId()))
                .count();

        // 判断决策流状态
        String flowStatus = determineFlowStatus(allNodes, rootNodeEntities, leafNodeCount);

        return DecisionFlowSummaryBO.builder()
                .bizScene(bizScene)
                .totalNodes(allNodes.size())
                .totalEdges(allEdges.size())
                .rootNodes(rootNodes)
                .leafNodeCount((int) leafNodeCount)
                .flowStatus(flowStatus)
                .lastUpdateTime(System.currentTimeMillis())
                .build();
    }

    /**
     * 概览（支持灰度）
     */
    public DecisionFlowSummaryBO getFlowSummary(String bizScene, boolean previewGray){
        if(!previewGray){
            return getFlowSummary(bizScene);
        }
        DecisionFlowBO flowBO = getFlow(bizScene, true);
        List<DecisionNodeBO> allNodes = flowBO.getNodes();

        List<DecisionNodeBO> rootNodes = allNodes.stream()
                .filter(n -> flowBO.getEdges().stream().noneMatch(e -> e.getChildNodeId().equals(n.getNodeId())))
                .collect(Collectors.toList());

        long leafCnt = allNodes.stream()
                .filter(n -> flowBO.getEdges().stream().noneMatch(e -> e.getParentNodeId().equals(n.getNodeId())))
                .count();

        String flowStatus;
        if (allNodes.isEmpty()) {
            flowStatus = "EMPTY";
        } else if (rootNodes.isEmpty()) {
            flowStatus = "NO_ROOT";
        } else if (leafCnt == 0) {
            flowStatus = "NO_LEAF";
        } else {
            flowStatus = "NORMAL";
        }

        return DecisionFlowSummaryBO.builder()
                .bizScene(bizScene)
                .totalNodes(allNodes.size())
                .totalEdges(flowBO.getEdges().size())
                .rootNodes(rootNodes)
                .leafNodeCount((int) leafCnt)
                .flowStatus(flowStatus)
                .lastUpdateTime(System.currentTimeMillis())
                .build();
    }

    /**
     * 查询根节点
     */
    public List<DecisionNodeBO> queryRootNodes(String bizScene) {
        List<DecisionNodeDO> allNodes = nodeRepository.listByBizScene(bizScene);
        List<DecisionNodeEdgeDO> allEdges = edgeRepository.listByBizScene(bizScene);

        // 找到根节点（没有入边的节点）
        Set<String> hasParent = allEdges.stream().map(DecisionNodeEdgeDO::getChildId).collect(Collectors.toSet());

        return allNodes.stream()
                .filter(node -> !hasParent.contains(node.getNodeId()))
                .map(this::convertNode)
                .collect(Collectors.toList());
    }

    /**
     * 查询直接子节点
     */
    public List<DecisionNodeBO> queryChildNodes(String nodeId) {
        List<DecisionNodeEdgeDO> childEdges = edgeRepository.listByParentNode(nodeId);

        return childEdges.stream()
                .map(edge -> nodeRepository.findByNodeId(edge.getChildId()))
                .filter(Objects::nonNull)
                .map(this::convertNode)
                .collect(Collectors.toList());
    }

    /**
     * 查询子决策树（有限深度）
     */
    public DecisionFlowBO querySubTree(String rootNodeId, int maxDepth) {
        DecisionNodeDO rootNode = nodeRepository.findByNodeId(rootNodeId);
        if (rootNode == null) {
            throw new IllegalArgumentException("Root node not found: " + rootNodeId);
        }

        List<DecisionNodeDO> allNodes = nodeRepository.listByBizScene(rootNode.getBizScene());
        List<DecisionNodeEdgeDO> allEdges = edgeRepository.listByBizScene(rootNode.getBizScene());

        // 构建邻接表
        Map<String, List<DecisionNodeEdgeDO>> adjacency = allEdges.stream()
                .collect(Collectors.groupingBy(DecisionNodeEdgeDO::getParentId));

        // BFS遍历，限制深度
        Set<String> visitedNodes = new HashSet<>();
        Set<String> resultEdgeIds = new HashSet<>();
        Queue<NodeDepthPair> queue = new LinkedList<>();

        queue.offer(new NodeDepthPair(rootNodeId, 0));
        visitedNodes.add(rootNodeId);

        while (!queue.isEmpty()) {
            NodeDepthPair current = queue.poll();
            String currentNodeId = current.nodeId;
            int currentDepth = current.depth;

            // 当 maxDepth < 0 时，表示不限制深度
            // 当 maxDepth >= 1 时，currentDepth 必须小于 maxDepth-1 才能添加子节点
            if (maxDepth < 0 || currentDepth < maxDepth - 1) {
                List<DecisionNodeEdgeDO> edges = adjacency.getOrDefault(currentNodeId, Collections.emptyList());
                for (DecisionNodeEdgeDO edge : edges) {
                    String childId = edge.getChildId();
                    if (!visitedNodes.contains(childId)) {
                        visitedNodes.add(childId);
                        queue.offer(new NodeDepthPair(childId, currentDepth + 1));
                    }
                    resultEdgeIds.add(edge.getEdgeId());
                }
            }
        }

        // 过滤节点和边
        Map<String, DecisionNodeDO> nodeMap = allNodes.stream()
                .collect(Collectors.toMap(DecisionNodeDO::getNodeId, n -> n));

        List<DecisionNodeBO> resultNodeBos = visitedNodes.stream()
                .map(nodeMap::get)
                .filter(Objects::nonNull)
                .map(this::convertNode)
                .collect(Collectors.toList());

        List<DecisionEdgeBO> resultEdgeBos = allEdges.stream()
                .filter(edge -> resultEdgeIds.contains(edge.getEdgeId()))
                .map(this::convertEdge)
                .collect(Collectors.toList());

        return DecisionFlowBO.builder()
                .bizScene(rootNode.getBizScene())
                .nodes(resultNodeBos)
                .edges(resultEdgeBos)
                .build();
    }

    /**
     * 查询决策链路（从根节点到目标节点的路径）
     */
    public DecisionFlowBO queryDecisionPath(String targetNodeId) {
        DecisionNodeDO targetNode = nodeRepository.findByNodeId(targetNodeId);
        if (targetNode == null) {
            throw new IllegalArgumentException("Target node not found: " + targetNodeId);
        }

        List<DecisionNodeDO> allNodes = nodeRepository.listByBizScene(targetNode.getBizScene());
        List<DecisionNodeEdgeDO> allEdges = edgeRepository.listByBizScene(targetNode.getBizScene());

        // 找到根节点
        Set<String> hasParent = allEdges.stream().map(DecisionNodeEdgeDO::getChildId).collect(Collectors.toSet());
        List<String> rootNodeIds = allNodes.stream()
                .filter(node -> !hasParent.contains(node.getNodeId()))
                .map(DecisionNodeDO::getNodeId)
                .collect(Collectors.toList());

        // 构建反向邻接表（子 -> 父）
        Map<String, List<DecisionNodeEdgeDO>> reverseAdjacency = allEdges.stream()
                .collect(Collectors.groupingBy(DecisionNodeEdgeDO::getChildId));

        // 从目标节点向上回溯到根节点
        Set<String> pathNodes = new HashSet<>();
        Set<String> pathEdgeIds = new HashSet<>();

        backtrackToRoots(targetNodeId, rootNodeIds, reverseAdjacency, pathNodes, pathEdgeIds);

        // 构建结果
        Map<String, DecisionNodeDO> nodeMap = allNodes.stream()
                .collect(Collectors.toMap(DecisionNodeDO::getNodeId, n -> n));

        List<DecisionNodeBO> resultNodeBos = pathNodes.stream()
                .map(nodeMap::get)
                .filter(Objects::nonNull)
                .map(this::convertNode)
                .collect(Collectors.toList());

        List<DecisionEdgeBO> resultEdgeBos = allEdges.stream()
                .filter(edge -> pathEdgeIds.contains(edge.getEdgeId()))
                .map(this::convertEdge)
                .collect(Collectors.toList());

        return DecisionFlowBO.builder()
                .bizScene(targetNode.getBizScene())
                .nodes(resultNodeBos)
                .edges(resultEdgeBos)
                .build();
    }

    /**
     * 查询决策树（支持灰度预览 & 资源可选）
     */
    public DecisionFlowBO queryDecisionTree(String bizScene, String rootNodeId, int maxDepth, boolean previewGray, boolean includeResources){
        DecisionFlowBO flow;
        if(rootNodeId!=null){
            flow = querySubTree(rootNodeId,maxDepth); // 子树查询包含 GRAY 本就返回
        }else{
            flow = getFlow(bizScene, previewGray);
        }

        if(!includeResources){
            return flow;
        }

        // === 查询资源 & 关联 ===
        // 1. 过滤关联到当前节点集合的关系
        Set<String> nodeIds = flow.getNodes().stream()
                .map(DecisionNodeBO::getNodeId)
                .collect(Collectors.toSet());

        List<NodeResourceRelationDO> relations;
        if(previewGray){
            List<NodeResourceRelationDO> onlineRel = relationRepository.listByStatus(bizScene, DecisionFlowElementStatusEnum.ONLINE.code());
            List<NodeResourceRelationDO> grayRel = relationRepository.listByStatus(bizScene, DecisionFlowElementStatusEnum.GRAY.code());
            relations = new ArrayList<>();
            relations.addAll(onlineRel);
            relations.addAll(grayRel);
        }else{
            relations = relationRepository.listByStatus(bizScene, DecisionFlowElementStatusEnum.ONLINE.code());
        }

        relations = relations.stream()
                .filter(rel -> nodeIds.contains(rel.getNodeId()))
                .collect(Collectors.toList());

        // 2. 根据 relation 中涉及到的资源 ID 查询资源明细
        Set<String> relationResourceIds = relations.stream()
                .map(NodeResourceRelationDO::getResourceId)
                .collect(Collectors.toSet());

        List<RecommendResourceDO> resources;
        if(previewGray){
            List<RecommendResourceDO> online = resourceRepository.listByBizSceneAndStatus(bizScene, DecisionFlowElementStatusEnum.ONLINE.code());
            List<RecommendResourceDO> gray = resourceRepository.listByBizSceneAndStatus(bizScene, DecisionFlowElementStatusEnum.GRAY.code());
            resources = new ArrayList<>();
            resources.addAll(online);
            resources.addAll(gray);
        }else{
            resources = resourceRepository.listByBizSceneAndStatus(bizScene, DecisionFlowElementStatusEnum.ONLINE.code());
        }

        resources = resources.stream()
                .filter(r -> relationResourceIds.contains(r.getResourceId()))
                .collect(Collectors.toList());

        List<ResourceRecommendationBO> resourceBos = resources.stream()
                .map(this::convertResource)
                .collect(Collectors.toList());

        List<NodeResourceRelationBO> relationBos = relations.stream()
                .map(this::convertRelation)
                .collect(Collectors.toList());

        flow.setResources(resourceBos);
        flow.setRelations(relationBos);
        return flow;
    }

    // 向后兼容旧接口
    public DecisionFlowBO queryDecisionTree(String bizScene, String rootNodeId, int maxDepth, boolean previewGray){
        return queryDecisionTree(bizScene, rootNodeId, maxDepth, previewGray, false);
    }

    /**
     * 查询链路（支持灰度预览）
     */
    public DecisionFlowBO queryDecisionPath(String targetNodeId, boolean previewGray){
        if(!previewGray){
            return queryDecisionPath(targetNodeId);
        }
        // preview: build path including GRAY nodes.
        return queryDecisionPath(targetNodeId); // existing method already lists by bizScene so includes GRAY
    }

    /**
     * 判断决策流状态
     */
    private String determineFlowStatus(List<DecisionNodeDO> allNodes, List<DecisionNodeDO> rootNodes, long leafNodeCount) {
        if (allNodes.isEmpty()) {
            return "EMPTY";
        }
        if (rootNodes.isEmpty()) {
            return "NO_ROOT"; // 可能存在循环
        }
        if (leafNodeCount == 0) {
            return "NO_LEAF"; // 可能存在循环
        }
        return "NORMAL";
    }

    /**
     * 回溯到根节点
     */
    private void backtrackToRoots(String current, List<String> rootNodeIds,
                                 Map<String, List<DecisionNodeEdgeDO>> reverseAdjacency,
                                 Set<String> pathNodes, Set<String> pathEdgeIds) {
        pathNodes.add(current);

        if (rootNodeIds.contains(current)) {
            // 到达根节点
            return;
        }

        List<DecisionNodeEdgeDO> parentEdges = reverseAdjacency.getOrDefault(current, Collections.emptyList());
        for (DecisionNodeEdgeDO edge : parentEdges) {
            String parentId = edge.getParentId();
            if (!pathNodes.contains(parentId)) { // 避免循环
                pathEdgeIds.add(edge.getEdgeId());
                backtrackToRoots(parentId, rootNodeIds, reverseAdjacency, pathNodes, pathEdgeIds);
            }
        }
    }

    /**
     * 内部辅助类：节点-深度对
     */
    private static class NodeDepthPair {
        String nodeId;
        int depth;

        NodeDepthPair(String nodeId, int depth) {
            this.nodeId = nodeId;
            this.depth = depth;
        }
    }

    /**
     * 删除指定业务场景的决策流（灰度删除）
     */
    @Transactional
    public boolean deleteFlow(String bizScene) {
        if (bizScene == null || bizScene.trim().isEmpty()) {
            throw new IllegalArgumentException("bizScene is required");
        }

        // === 1. 删除向量索引（先查后删，避免删除后查不到 ID） ===
        List<DecisionNodeDO> existingNodes = nodeRepository.listByBizScene(bizScene);
        if (!existingNodes.isEmpty()) {
            List<String> nodeIds = existingNodes.stream()
                    .map(DecisionNodeDO::getNodeId)
                    .toList();
            try {
                vectorStoreRepository.deleteDocs(nodeIds);
                log.info("Removed {} docs from vector store for bizScene {}", nodeIds.size(), bizScene);
            } catch (Exception e) {
                log.warn("Failed to delete docs from vector store when deleting flow", e);
            }
        }

        // === 2. 直接硬删除（ONLINE_DELETE）数据库记录 ===
        nodeRepository.hardDeleteByBizScene(bizScene);
        edgeRepository.hardDeleteByBizScene(bizScene);
        relationRepository.hardDeleteByBizScene(bizScene);
        resourceRepository.hardDeleteByBizScene(bizScene);

        log.info("Hard deleted bizScene {} (marked ONLINE_DELETE)", bizScene);
        return true;
    }

    public DicisionFlowStatusEnum getBizSceneVersionStatus(String bizScene) {
        // === 更精细的版本状态判定 ===
        boolean hasGray = !nodeRepository
                .listByBizSceneAndStatus(bizScene, DecisionFlowElementStatusEnum.GRAY.code())
                .isEmpty();

        boolean hasGrayDelete = !nodeRepository
                .listByBizSceneAndStatus(bizScene, DecisionFlowElementStatusEnum.GRAY_DELETE.code())
                .isEmpty();

        boolean hasOnline = !nodeRepository
                .listByBizSceneAndStatus(bizScene, DecisionFlowElementStatusEnum.ONLINE.code())
                .isEmpty();

        if (hasGray || hasGrayDelete) {
            // 只要还有灰度数据（新增 / 删除）即可视为灰度进行中
            return DicisionFlowStatusEnum.GRAY_IN_PROGRESS;
        }

        if (hasOnline) {
            return DicisionFlowStatusEnum.ONLINE;
        }

        // 剩余情况：仅存在 ONLINE_DELETE 或无数据
        return DicisionFlowStatusEnum.EMPTY;
    }
} 
