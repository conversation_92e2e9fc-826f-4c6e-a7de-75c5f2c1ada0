package com.sankuai.dzhealth.ai.service.domain.entity.xhs;

import com.fasterxml.jackson.databind.JsonNode;
import com.sankuai.dzhealth.ai.service.domain.utils.JsonUtils;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

@Data
public class SaveHotNoteRequest {
    private String noteId;
    private String content;
    private String title;
    private Map<String, String> metadata;

    public boolean isEmpty() {
        return (StringUtils.isBlank(this.content) && StringUtils.isBlank(this.title));
    }

    public Map<String, String> tryGetMetadata() {
        this.metadata = Optional.ofNullable(metadata).orElse(new HashMap<>());
        return this.metadata;
    }

    public String joinTitleAndContent() {
        return String.join("\n", title, content);
    }

    public String SafeGetBatch() {
        return getValue("hotNoteBatchStr");
    }

    public String SafeGetAuthorname() {
        if (metadata == null) {
            return null;
        }

        JsonNode res = JsonUtils.getByPath(metadata.getOrDefault("user", null), "nickname");
        return res == null ? null : res.asText();
    }

    public String SafeGetAuthorId() {
        if (metadata == null) {
            return null;
        }
        JsonNode res = JsonUtils.getByPath(metadata.getOrDefault("user", null), "redId");
        return res == null ? null : res.asText();
    }

    public String getValue(String key) {
        if (metadata == null) {
            return null;
        }
        return metadata.getOrDefault(key, null);
    }
}
