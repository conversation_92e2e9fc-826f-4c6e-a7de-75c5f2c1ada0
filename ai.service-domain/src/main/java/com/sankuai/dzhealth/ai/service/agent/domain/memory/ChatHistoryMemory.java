package com.sankuai.dzhealth.ai.service.agent.domain.memory;

import com.sankuai.dzhealth.ai.service.agent.domain.entity.ChatSessionMessageEntity;
import com.sankuai.dzhealth.ai.service.agent.domain.process.card.CardBuilder;
import com.sankuai.dzhealth.ai.service.agent.domain.repository.chatSession.ChatSessionMessageRepository;
import com.sankuai.dzhealth.ai.service.domain.evaluation.entity.EvaluationMessage;
import com.sankuai.dzhealth.ai.service.domain.utils.JsonUtils;
import com.sankuai.dzhealth.ai.service.dto.stream.StreamEventCardDataDTO;
import com.sankuai.dzhealth.ai.service.dto.stream.StreamEventDTO;
import com.sankuai.dzhealth.ai.service.dto.stream.StreamEventDataDTO;
import com.sankuai.dzhealth.ai.service.enums.StreamEventCardTypeEnum;
import com.sankuai.dzhealth.ai.service.enums.StreamEventDataTypeEnum;
import com.sankuai.dzhealth.ai.service.enums.StreamEventTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.ai.chat.memory.ChatMemory;
import org.springframework.ai.chat.messages.AssistantMessage;
import org.springframework.ai.chat.messages.Message;
import org.springframework.ai.chat.messages.MessageType;
import org.springframework.ai.chat.messages.UserMessage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @author:chenwei
 * @time: 2025/7/11 16:20
 * @version: 0.0.1
 */

@Slf4j
@Component
public class ChatHistoryMemory implements ChatMemory {

    @Autowired
    private ChatSessionMessageRepository chatSessionMessageRepository;


    @Autowired
    private List<CardBuilder> cardBuilders;


    @Override
    public void add(String conversationId, List<Message> messages) {

    }

    @Override
    public List<Message> get(String conversationId) {

        //只获取历史最新十条有效对话数据
        List<ChatSessionMessageEntity> bySessionIdAndStatusEntityList = chatSessionMessageRepository.findBySessionIdAndStatus(conversationId, 7, 0);
        List<Message> collect = bySessionIdAndStatusEntityList.stream().map(entity -> {
                    return MessageType.USER.getValue().equals(entity.getRole()) ?
                            new UserMessage(entity.getContent()) :
                            new AssistantMessage(parseAssistantMessage(entity.getContent()));
                }
        ).collect(Collectors.toList());
        Collections.reverse(collect);
        log.info("chatHistory={}", JsonUtils.toJsonString(collect));
        return collect;
    }

    private String parseAssistantMessage(String message) {
        if (StringUtils.isBlank(message)) {
            return StringUtils.EMPTY;
        }
        List<StreamEventDTO> streamEventDTOS = JsonUtils.parseArray(message, StreamEventDTO.class);

        StreamEventDTO streamEventDTO = streamEventDTOS.stream().filter(e -> StreamEventTypeEnum.MESSAGE.getType().equals(e.getType())).filter(e -> {
            StreamEventDataDTO data = e.getData();
            return StreamEventDataTypeEnum.MAIN_TEXT.getType().equals(data.getEvent());
        }).findFirst().orElse(null);
        if (streamEventDTO == null) {
            return StringUtils.EMPTY;
        }

        StreamEventDataDTO data = streamEventDTO.getData();
        String content = data.getContent();
        List<StreamEventCardDataDTO> cardsData = data.getCardsData();
        if (CollectionUtils.isEmpty(cardsData)) {
            return content;
        }
        StringBuilder result = new StringBuilder();


        for (StreamEventCardDataDTO cardData : cardsData) {
            if (cardData == null) {
                continue;
            }

            String tag = StreamEventCardTypeEnum.buildCardContent(cardData.getType(), cardData.getKey());

            if (StreamEventCardTypeEnum.excludeChatMemoryCardType(cardData.getType())) {
                content = content.replace(tag, StringUtils.EMPTY);
                continue;
            }
            for (CardBuilder builder : cardBuilders) {
                if (builder.accept(cardData.getType()) && MapUtils.isNotEmpty(cardData.getCardProps())) {
                    String parseContent = builder.parseCardProps(cardData);
                    content = content.replace(tag, parseContent);
                }
            }
        }
        String replaceContent = content.replace(":::{", "").replace(":::}", "")
                .replace("}:::", "").replace("{:::", "");
        result.append(replaceContent);
        return result.toString();


    }

    @Override
    public void clear(String conversationId) {

    }

    // 获取所有记忆数据
    public List<Message> getAllMemory(String conversationId) {

        List<ChatSessionMessageEntity> bySessionIdAndStatusEntityList = chatSessionMessageRepository
                .findBySessionIdAndStatus(conversationId);
        List<Message> collect = bySessionIdAndStatusEntityList.stream()
                .map(entity -> MessageType.USER.getValue().equals(entity.getRole())
                        ? new UserMessage(entity.getContent())
                        : new AssistantMessage(parseAssistantMessage(entity.getContent())))
                .collect(Collectors.toList());

        return collect;
    }
}
