package com.sankuai.dzhealth.ai.service.agent.domain.model.experiencereport;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 体验报告领域模型
 *
 * <AUTHOR>
 * @version 0.0.1
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ExperienceReportModel implements Serializable {

    /**
     * 模块标题
     */
    private String title;

    /**
     * 体验报告个数
     */
    private Integer reportNum;

    /**
     * 详情页链接
     */
    private String detailUrl;

    /**
     * 图片信息列表
     */
    private List<ExperiencePicModel> picList;

    /**
     * 模块是否展示AB信息
     */
    private AbModel showModuleInfo;
}

