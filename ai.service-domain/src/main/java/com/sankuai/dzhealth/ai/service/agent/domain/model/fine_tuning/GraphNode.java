package com.sankuai.dzhealth.ai.service.agent.domain.model.fine_tuning;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 图节点模型
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class GraphNode {

    /**
     * 节点ID
     */
    @JsonProperty("id")
    private String id;
    /**
     * 节点类型：decision（决策节点）或resource（资源节点）
     */
    @JsonProperty("node_type")
    private String nodeType;

    /**
     * 父节点列表
     */
    @JsonProperty("parents")
    private List<String> parents;

    /**
     * 子节点列表
     */
    @JsonProperty("children")
    private List<String> children;

    @JsonIgnore
    private GraphNode parentsNode;

    @JsonIgnore
    private List<GraphNode> childrenNode;
}

