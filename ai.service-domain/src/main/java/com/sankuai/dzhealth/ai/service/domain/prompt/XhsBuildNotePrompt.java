package com.sankuai.dzhealth.ai.service.domain.prompt;


import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.sankuai.dzhealth.ai.service.infrastructure.model.CorpusLibrary;
import com.sankuai.dzhealth.ai.service.infrastructure.repository.CorpusLibraryRepository;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.IntStream;


@Component
public class XhsBuildNotePrompt {

    @Autowired
    private CorpusLibraryRepository corpusLibraryRepository;

    public static class PromptData {
        private String noteType;
        private String product;
        private String scene;
        private String targetAudience;
        private String hotNotes;
        private String template;
        private String knowledge;
        private String experienceAndSkills;
        private String conclusion;
        private String requisiteTags;
        private String optionalTags;
        private String precautions;

        public PromptData noteType(List<String> noteType) {
            StringBuilder sb = new StringBuilder();
            noteType.stream().filter(StringUtils::isNotBlank)
                    .forEach(n -> Optional.ofNullable(noteTypeAndDescription.get(n))
                            .ifPresent(v -> sb.append("  - ").append(n).append(": ").append(v).append("\n")));
            this.noteType = sb.toString();
            return this;
        }
        public PromptData product(List<String> products) {
            this.product = products.stream()
                    .filter(StringUtils::isNotBlank)
                    .map(p -> "  - " + p + "\n").collect(Collectors.joining());
            return this;
        }
        public PromptData scene(List<String> scenes) {
            this.scene = scenes.stream()
                    .filter(StringUtils::isNotBlank)
                    .map(p -> "  - " + p + "\n").collect(Collectors.joining());
            return this;
        }
        public PromptData targetAudience(List<String> targetAudiences) {
            this.targetAudience = targetAudiences.stream()
                    .filter(StringUtils::isNotBlank)
                    .map(p -> "  - " + p + "\n").collect(Collectors.joining());
            return this;
        }
        public PromptData hotNotes(List<String> hotNotes) {

            this.hotNotes = hotNotes.stream().filter(StringUtils::isNotBlank).map(str -> "\n```\n" + str + "\n``` \n").collect(Collectors.joining("> ---\n"));
            return this;
        }
        public PromptData template(String template) {
            this.template = template;
            return this;
        }
        public PromptData knowledge(String knowledge) {
            this.knowledge = knowledge;
            return this;
        }

        // experienceAndSkills 心得技巧建议
        public void buildExperienceAndSkills() {
            this.experienceAndSkills = """
    1. 痛点引入技巧
        - 通过具体场景引发共鸣（如“拍照时拼命提拉嘴角，法令纹依然抢戏”）。
        - 用对比手法强化需求（如“遮瑕膏填不平沟壑，高光反显纹路”）。
    
    2. 结构化逻辑
        - 分类型解析：先分类（如凹陷型/下垂型），再对应解决方案，增强专业性。
        - 项目说明：用“原理+适配人群+关键点”框架，清晰易懂。
    
    3. 避坑指南设计
        - 反向提醒用户常见误区（如“拒绝盲目填充”“提拉优先于填充”）。
        - 强调动态评估（如“注射时需大笑观察”），体现细节专业度。
    
    4. 结尾情绪价值
        - 升华主题，弱化年龄焦虑（如“抗衰目标是恰到好处的自信”）。
        - 用“自然”“协调”“从容”等词传递正向理念。
    """;
        }

        // conclusion 美团宣发结束语
        public void buildConclusion() {
            List<String> list = Lists.newArrayList(
                    """
        快上美团🔍【医美xxxxx】囤上再说
        ✨美团大平台，活动正规有保障
        ✨严选机构，仪器可验真
        ✨价格透明不踩坑，随时买随时退
        ⚠️部分项目价格因地区不同略有差异
        """,
                    """
        去美团🔍【医美xxxxx】进入活动页面，领777元补贴礼包🎁
        
        🥇美团医美严选，大平台有保障
        🥇大白亲操，仪器扫码验真伪
        🥇价格透明，到店不加价
        🥇不用随时退，过期退
        """,
                    """
        💰抢购攻略：
        👉去美团app首页搜🔍【医美xxxxx】
        ✅领xx元无门槛券
        ✅享xx项目xx元
        ⚠️项目价格因地区不同略有差异
        """,
                    """
            🎁打开美团APP主页搜索口令【医美xxxxx】，解锁专属变美福利！
            💥510元变美券包直接到账，还有机会抽取惊喜小奖励！
            💥百补专区更有777元券包限时领取，骨折价叠加项目优惠券，价格美丽到尖叫！
            🌟【美团医美拒绝医美刺客】
            透明价格，无隐形消费，让你放心薅羊毛！
            专业机构、资质全公开，品质服务有保障！
            """,
                    """
            在做医美前熟悉周期和注意事项也很有必要！
            上美团有官方科普，带你科学变美
            👉美团搜【医美xxxxx】领限时特惠
            👉海量变美项目，限时低价等你来！
            """,
                    """
            💡 变美计划 · 全流程安排👇
            1️⃣ 打开美团APP，搜索【医美xxxx】
            2️⃣ 领取 ¥xxx 无门槛医美券
            3️⃣ 安排xx项目，限时补贴后总价只要 xxx元左右！
            🌟 为什么都选美团医美？
            💛 项目正规严选，操作安心又靠谱
            💛 价格透明补贴多，xx项目合计还不到一条裙子的价
            💛 优惠券可退可改，假期前后灵活安排不慌张～
            """,
                    """
            找靠谱机构和医生很重要，如果不知道选什么机构的姐妹，可以像我一样在美团​搜
            🔍【医美xxxx】
            
            大平台安全有保障，机构资质透明，仪器药品可溯源。
            价格透明，不满意了随时退！最主要的是美团现在还有百亿补贴，直接比美容院便宜一半！有需要的姐妹直接冲！
            ​
            """,
                    """
            像我基本上是去美团搜🔍【医美xxxx】，在大平台上团购做医美比较有保障，相对来说价格也比较能接受一些~
            而且最近美团有百亿补贴，超多医美项目都有优惠！姐妹们有需要可以冲！
            """,
                    """
            像我基本上都是直接上美团🔍【医美xxxx】，大平台可信又可靠，直接是在本地就可做🌟
            
            不想做了随时退，都是正规🏥，现在还有百亿补贴，做项目更省钱，宝子们可以冲！
            """,
                    """
            姐妹们如果想做但害怕踩雷，不知道哪里有专业医生机构，可以试试美团医美，那里面有多家正规机构、多种项目，支持验真，价格透明可以直接比价，拒绝隐藏消费，我现在囤项目只都在上面屯！
            👉偷偷给大家分享活动口令，现在搜美团首页🔍【医美xxxx】可以领取💰百亿补贴专享券!大家可以屯一些薅羊毛项目，优惠券随时退，项目也不过期，随时安排，选个懂你体质的专家，效果能翻倍！
            """,
                    """
            💸薅羊毛党抄作业时间
            
            现在在美团app搜【医美xxxx】，可以领取💰百亿补贴专享券，趁着现在活动力度大，可以囤一些光子嫩肤卡，利用午休或放假时间去do，均价算下来比奶茶还便宜！而且都是正规🏥认证可靠，设备材料支持验真，价格优惠透明，很多热门项目任意比对和选择，过期也可随时退，实打实让姐妹们安心变美！
            
            如果对机构不放心的，可以来美团医美，现在搜美团首页🔍【医美xxxx】可以领取💰百亿补贴专享券!
            """,
                    """
            如果对机构不放心的，可以来美团医美，现在搜美团首页🔍【医美xxxx】可以领取💰百亿补贴专享券!
            💡为什么选择美团医美？
            ✅ 价格透明：补贴明明白白，变美不踩坑
            ✅ 正规机构：美团严选认证，设备材料支持验真，安心变美
            ✅ 灵活退改：优惠券随时退，项目不过期，随时安排，变美无压力！
            """,
                    """
            趁着现在活动力度大，现在搜美团首页🔍【医美xxxx】可以领取💰百亿补贴专享券!
            可以囤一些光子嫩肤卡、玻尿酸，利用午休或放假时间去do，均价算下来比奶茶还便宜！而且都是正规🏥认证可靠，设备材料支持验真，价格优惠透明，很多热门项目任意比对和选择，过期也可随时退，实打实让姐妹们安心变美！跑路风险退退退！姐妹们记住！省钱变美的尽头不是冒险，而是用验真功能锁死正规低价！
            """,
                    """
            ❤️宝子们！变美路上急不得，做好功课再上车！
            但是现在很多机构虚假宣传，想省心的姐妹可以在美团app上🔍【医美xxxx】，可以领取💰百亿补贴专享券，大平台安全有保障，机构资质透明，设备材料支持验真，价格优惠透明，拥有超多医美项目可选择，过期也可随时退，让姐妹们安心变美！
            """
            );
            IntStream.range(0, list.size()).boxed().forEach(i -> list.set(i, (i + 1) + ". \n" + list.get(i)));
            this.conclusion = String.join("\n\n", list);
        }

        // requisiteTags 必须添加的标签
        public void buildRequisiteTags() {
            List<String> list = Lists.newArrayList("美团医美", "美团医美百亿补贴", "美团医美专业变美", "好看不止一点", "美团医美618变美季");
            this.requisiteTags = "【" + String.join(",", list) + "】";
        }

        // optionalTags 可选标签
        public void buildOptionalTags() {
            List<String> list = Lists.newArrayList("普通人的变美思路", "不要低估一个女生变美的决心", "万能护肤公式", "狠人护肤心得", "省钱攻略");
            this.optionalTags = "【" + String.join(",", list) + "】";
        }

        // precautions 注意事项
        public void buildPrecautions() {
            this.precautions = """
    1. 符号使用规范
        - 避免使用「」、【】、“”、|、→、《》、+、（）等符号。
        - 减少表情符号，在总结性小标题中使用1️⃣ 2️⃣ 3️⃣ 这类emoji替代数字。
    
    2. 禁用词汇/风险控制相关
        - 涉及利用受益者/患者等名义或形象做推荐证明（如文章内容说：我之前大干皮，经常做水光，做完皮肤嫩嫩的），自身经历相关描述需改为第三人称，不可用“我”等第一人称表述。
        - 医美平台推广，不可涉及具体医院名称露出；\s
        - 医美广告中不得出现医生，不能以专家名义进行推广；\s
        - 医美广告中不得含有安全、亲测、医用、祛、长效、无痛、清除色斑源头、永久、效果持久、瞬间击退、一次性、立即见效、立竿见影、干净彻底、效果维持1-2年、维持时间等承诺保证用语。\s
        - 不可使用不合规用语，如 XX 针，精雕、冻龄术、水光针、少女针、美白针、熊猫针等。 -比如水光针表述为水光，熊猫针表述为嗨体熊猫。
        - 不能说功效类文字，功效类文字要变为事实性描述，比如变白表述为可以让你们皮肤变嫩、维持时间长、见效快。
        - 【肉肉】涉及平台限制推广的业务范围，视频要毙掉口播，字幕肉肉换成rr。
        - 不能出现：推荐、最、首次、第一、解决、全能、面膜、痤疮、镇静等绝对性用词和医美禁用词，可用拼音代替，如tui荐、zui、第yi、解jue、mian膜
    
    3. 文案结构要求
        - 文章篇幅在400-600字之内，标题最多不能超过20字；
        - 语句连贯成段，避免零散短句（如“异常反应：持续红肿＞72小时”需改写为完整句子）。
        - 减少双引号的使用，非必要不加引号。
        - 必须带美团 利益/引流 引导话术。
        - 文末必带风控话术，文末需透传：价格因地区而异，宝子们要以实际页面为准~
    
    4. 内容风格
        - 提供情绪价值，结尾需有总结性鼓励。
    """;

        }

        public String build(XhsBuildNotePrompt comp, Long id) {
            return comp.repace(this, id);
        }
    }

    private static final Map<String, String> noteTypeAndDescription = new ImmutableMap.Builder<String, String>()
            .put("医美项目推荐", "医美项目推荐内容方向，应围绕用户在不同季节（如春秋季过敏、夏季出油、冬季干燥等）、节日（母亲节、情人节、五一、十一、春节等）、痛点（鼻基底凹陷、法令纹、泪沟、黑眼圈等）场景（加班熬夜等）下的真实需求，结合皮肤/美学问题，借助高性价比套餐和创新组合，提供科学、实用、有吸引力的项目盘点推荐、组合使用方案等，提升用户决策效率和转化率。")
            .put("低价爆品推荐", "针对羊毛党、学生党或医美小白人群，以极具吸引力低价项目为主打，突出“低门槛体验”、“超高性价比”等卖点，内容中制造快速囤货、购买焦虑和紧迫感，引导用户下单，商品推荐和宣导方向为9.9元脱毛、19.9元小气泡、69元光子嫩肤等等其他低价引流品类。")
            .put("医美项目科普", "讲解各医美项目（如激光美容、注射填充、射频紧肤等）的功效、作用、原理、成分、适合人群等科普类问题，例如一篇带你读懂光子嫩肤、什么是光子嫩肤？关于光子嫩肤你必须知道的几个问题、光子嫩肤的几大误区、光子嫩肤的原理和功效看这篇就够了，列举项目如：光子嫩肤（黄金超光子、黑金超光子）、热玛吉、水光针、黄金微针、玻尿酸/嗨体、刷酸、脱毛（冰点脱毛、激光脱毛）。")
            .put("项目组合使用方案", "医美项目组合使用，是指将两种及以上不同功能或作用机制的医美治疗项目，按照科学、安全、个性化原则进行搭配和联合应用。通过项目之间的协同效应，实现更优的美肤、美形或抗衰效果，满足求美者多层次、多方面的皮肤及形态改善需求。医美项目组合使用方式的介绍和科普，如{xxxx}怎么搭配？、{xxx+xxx}cp组合、刷酸+黄金微针搭配使用方式等。")
            .put("科普避雷", "以专业人士或医美老手身份，科普医美知识、误区，避坑某项目/机构/医生，输出干货性内容。")
            .put("变美经验分享", "多为讲述个人变美经历，分享变美 Tips 或项目心得。")
            .put("术后护理注意事项", "针对不同医美项目，给出术后清洁、护肤、饮食、作息等方面的具体建议，比如双眼皮手术后如何正确清洁伤口、如何选择合适的护肤品。介绍术后护理和建议，涵盖伤口处理、饮食调节、运动限制、可能存在的并发症及规避方式等方面，帮助用户恢复得更快更好。例如：热玛吉术后注意事项/打完热玛吉如何护理、光子嫩肤术后注意事项等。")
            .put("品类品牌推荐评测", "医美某个具体品类的品牌评测及推荐、对比，如玻尿酸品牌推荐、水光品牌如何选、同品类不同品牌的价格、优缺点对比等。")
            .put("医美项目对比", "对两个或多个不同医美项目进行优缺点分析、适应人群、效果分析等，帮助用户做出更好选择。例如：玻尿酸VS胶原蛋白的区别，热玛吉VS超声刀以及光子嫩肤、水光针、黄金微针、热玛吉、超声炮等项目介绍对比。")
            .put("打法分享", "展示医生操作实拍或治疗方法分布，针对某个问题的具体打法。痘肌，毛孔大有粉刺痘坑、脸部轮廓不清晰等。")
            .put("明星美学类", "通过明星/网红图片或视频素材，作为噱头，讲解某个审美或打法。")
            .build();

    public PromptData builder() {
        PromptData prompt = new PromptData();
        // 构建心得技巧建议
        prompt.buildExperienceAndSkills();
        // 美团宣发结束语
        prompt.buildConclusion();
        // 必须添加的标签
        prompt.buildRequisiteTags();
        // 可选标签
        prompt.buildOptionalTags();
        // 注意事项
        prompt.buildPrecautions();
        return prompt;
    }


    public String repace( PromptData promptData, Long id) {

        CorpusLibrary res = corpusLibraryRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("查询失败"));

        String basePrompt = res.getCorpusContent();

        if (StringUtils.isBlank(basePrompt)) {
            throw new RuntimeException("Prompt构建失败");
        }

        return StringUtils.replaceEach(basePrompt,
                new String[]{"{{{noteType}}}", "{{{product}}}", "{{{scene}}}",
                        "{{{targetAudience}}}", "{{{hotNotes}}}", "{{{template}}}",
                        "{{{knowledge}}}", "{{{experienceAndSkills}}}", "{{{conclusion}}}",
                        "{{{requisiteTags}}}", "{{{optionalTags}}}", "{{{precautions}}}"},
                new String[]{promptData.noteType, promptData.product, promptData.scene,
                        promptData.targetAudience, promptData.hotNotes, promptData.template,
                        promptData.knowledge, promptData.experienceAndSkills, promptData.conclusion,
                        promptData.requisiteTags, promptData.optionalTags, promptData.precautions}
        );

    }








}
