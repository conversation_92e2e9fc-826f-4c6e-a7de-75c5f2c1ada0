package com.sankuai.dzhealth.ai.service.domain.evaluation.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MessageCopyRequest {
    @JsonProperty("evaluation_id")
    private String evaluationId;
    @JsonProperty("session_ids")
    private List<String> sessionIds;
    @JsonProperty("source_user_id")
    @NotNull(message = "评估ID不能为空")
    private String sourceUserID;
    @JsonProperty("target_user_id")
    @NotNull(message = "评估ID不能为空")
    private String targetUserID;
}
