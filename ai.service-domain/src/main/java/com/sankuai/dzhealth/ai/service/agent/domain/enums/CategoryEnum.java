package com.sankuai.dzhealth.ai.service.agent.domain.enums;

public enum CategoryEnum {
    MEDICAL_CONSULT("850", "medical_consult"),
    MOUTH_CONSULT("506", "mouth_consult");
    public String code;
    public String bizType;

    CategoryEnum(String code, String bizType) {
        this.code = code;
        this.bizType = bizType;
    }

    public static CategoryEnum getByCode(String code) {
        for (CategoryEnum status : values()) {
            if (status.code.equals(code)) {
                return status;
            }
        }
        return MEDICAL_CONSULT;
    }
}