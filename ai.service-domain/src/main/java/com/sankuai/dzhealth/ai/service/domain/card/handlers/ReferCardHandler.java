package com.sankuai.dzhealth.ai.service.domain.card.handlers;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.sankuai.dzhealth.ai.service.domain.card.AbstractCardHandler;
import com.sankuai.dzhealth.ai.service.domain.utils.context.AiAnswerContext;
import com.sankuai.dzhealth.ai.service.dto.ReferItemDTO;
import com.sankuai.dzhealth.ai.service.dto.stream.StreamEventDTO;
import com.sankuai.dzhealth.ai.service.enums.StreamEventCardTypeEnum;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 引用卡片处理器 - 较低优先级
 */
@Component
@Order(7)
public class ReferCardHandler extends AbstractCardHandler {

    @Override
    public boolean supports(String fieldName) {
        return "refer".equals(fieldName);
    }

    @Override
    public List<StreamEventDTO> handle(JSONObject jsonObject,
                                    AiAnswerContext context,
                                    SseEmitter sseEmitter,
                                    AtomicInteger sseIndex,
                                    long startChatTime) {
        String referUrls = jsonObject.getString("refer");
        if (StringUtils.isNotBlank(referUrls)) {
            List<ReferItemDTO> referItems = JSON.parseArray(referUrls, ReferItemDTO.class);
            StreamEventDTO referItemsEvent = null;
            if (CollectionUtils.isNotEmpty(referItems)) {

                List<ReferItemDTO> list = referItems.stream()
                        .filter(referItemDTO ->
                                !Lists.newArrayList("knowledgeDisease", "knowledgeSymptom").contains(referItemDTO.getSource()))
                        .peek(referItemDTO -> {
                            Optional.ofNullable(context.getSearchInfo())
                                    .orElse(Collections.emptyList())
                                    .stream()
                                    .filter(searchInfo -> Objects.equals(String.valueOf(context.getSearchInfo().indexOf(searchInfo) + 1), referItemDTO.getIndex()) &&
                                                          "website".equals(referItemDTO.getSource()))
                                    .findFirst()
                                    .ifPresent(searchInfo -> {
                                        referItemDTO.setUri(searchInfo.getUrl());
                                        referItemDTO.setContent(searchInfo.getName());
                                        referItemDTO.setPublishTime(searchInfo.getDatePublished());
                                    });
                            Optional.ofNullable(context.getRagInfo())
                                    .orElse(Collections.emptyList())
                                    .stream()
                                    .filter(ragInfo -> Objects.equals(String.valueOf(context.getRagInfo().indexOf(ragInfo) + 1), referItemDTO.getIndex()) &&
                                                          Objects.equals(ragInfo.getResourceChannel(),
                                                                  referItemDTO.getSource()))
                                    .findFirst()
                                    .ifPresent(searchInfo -> {
                                        referItemDTO.setUri(searchInfo.getUri());
                                        referItemDTO.setContent(searchInfo.getContent());
                                        referItemDTO.setPublishTime(searchInfo.getPublishTime());
                                    });

                            Optional.ofNullable(context.getShopRagInfo())
                                    .orElse(Collections.emptyList())
                                    .stream()
                                    .filter(ragInfo -> Objects.equals(String.valueOf(context.getShopRagInfo().indexOf(ragInfo) + 1), referItemDTO.getIndex()) &&
                                            Objects.equals(ragInfo.getResourceChannel(),
                                                    referItemDTO.getSource()))
                                    .findFirst()
                                    .ifPresent(searchInfo -> {
                                        referItemDTO.setUri(searchInfo.getUri());
                                        referItemDTO.setContent(searchInfo.getContent());
                                        referItemDTO.setPublishTime(searchInfo.getPublishTime());
                                    });
                        })
                        .filter(referItemDTO -> StringUtils.isNotBlank(referItemDTO.getContent()))
                        .collect(Collectors.collectingAndThen(
                                Collectors.toMap(
                                        ReferItemDTO::getUri,
                                        item -> item,
                                        (existing, replacement) -> existing  // 如果有重复，保留第一个
                                ),
                                map -> new ArrayList<>(map.values())
                        ));

                long count = list.stream().filter(e -> Lists.newArrayList("ugcReview", "ugcDianping", "ugcQa").contains(e.getSource())).count();
                if (CollectionUtils.isNotEmpty(list)) {
                    Map<String, Object> cardProps = new HashMap<>();
                    cardProps.put("refer", list);
                    cardProps.put("trueCase", count);

                    referItemsEvent = sendCardEvent(
                            StreamEventCardTypeEnum.REFERENCE_CARD,
                            "referItems",
                            cardProps,
                            sseEmitter,
                            sseIndex.getAndIncrement()
                    );
                }
            }

            return singletonEvent(referItemsEvent);
        }

        return emptyEvents();
    }

    @Override
    public int getOrder() {
        return 7;
    }
} 