package com.sankuai.dzhealth.ai.service.agent.domain.tools;


import com.alibaba.fastjson.JSON;
import com.dianping.lion.Environment;
import com.dianping.lion.client.util.StringUtils;
import com.sankuai.dzhealth.ai.service.agent.domain.tools.param.GeocodingDescRequest;
import com.sankuai.dzhealth.ai.service.agent.domain.tools.param.GeocodingRequest;
import com.sankuai.dzhealth.ai.service.agent.domain.tools.param.PoiSearchRequest;
import com.sankuai.dzhealth.ai.service.agent.domain.tools.param.PoiSearchResult;
import com.sankuai.dzhealth.ai.service.domain.utils.JsonUtils;
import com.sankuai.map.open.platform.api.MapOpenApiService;
import com.sankuai.map.open.platform.api.geo.GeoRequest;
import com.sankuai.map.open.platform.api.geo.GeoResponse;
import com.sankuai.map.open.platform.api.geo.Geocode;
import com.sankuai.map.open.platform.api.regeo.RegeoRequest;
import com.sankuai.map.open.platform.api.regeo.RegeoResponse;
import com.sankuai.map.open.platform.api.regeo.Regeocode;
import lombok.extern.slf4j.Slf4j;
import mtmap.geoinfo.geoinfo_base.Poi;
import mtmap.geoinfo.geoinfo_base.SearchServiceRequest;
import mtmap.geoinfo.geoinfo_base.SearchServiceResponse;
import org.springframework.ai.tool.annotation.Tool;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 地址转换服务 - 根据经纬度获取地址信息
 *
 * @author: jiyizhou
 * @time: 2025/7/10 11:00
 * @version: 0.0.1
 */
@Service
@Slf4j
public class GeocodingTool {

    @Autowired
    private MapOpenApiService.Iface districtService;


    private static final String TEST_KEY = "mb0fdc36dc44438b8d35375270668fdt";

    private static final String PROD_KEY = "mfacf02b218842d4a97e667389f8365t";


    /**
     * 根据经纬度获取地址信息
     *
     * @param request 经纬度请求参数
     * @return 地址字符串，获取失败时返回null
     */
    @Tool(name = "getAddressByLocation", description = "Get address by latitude and longitude")
    public String getAddressByLocation(GeocodingRequest request) {
        log.info("getAddressByLocation_request={}", JSON.toJSONString(request));
        if (request == null || request.getLat() == null || request.getLng() == null) {
            log.info("经纬度参数为空");
            return null;
        }

        try {

            // 构建请求参数
            RegeoRequest Regorequest = new RegeoRequest();
            Regorequest.setKey(Environment.isTestEnv() ? TEST_KEY : PROD_KEY);
            Regorequest.setLocation(String.format("%.6f,%.6f", request.getLng(), request.getLat()));
            Regorequest.setShow_fields("base|admin");

            // 调用地图服务进行逆地理编码
            RegeoResponse response = districtService.regeo(Regorequest);

            // 处理响应结果
            if (response != null && response.getStatus() == 200) {
                if (response.getRegeocode() != null && !response.getRegeocode().isEmpty()) {
                    Regeocode addressInfo = response.getRegeocode().get(0);

                    StringBuilder address = new StringBuilder();
                    if (addressInfo.getProvince() != null) {
                        address.append(addressInfo.getProvince());
                    }
                    if (addressInfo.getCity() != null && !addressInfo.getCity().equals(addressInfo.getProvince())) {
                        address.append(addressInfo.getCity());
                    }
                    if (addressInfo.getDistrict() != null) {
                        address.append(addressInfo.getDistrict());
                    }
                    if (addressInfo.getTownship() != null) {
                        address.append(addressInfo.getTownship());
                    }
                    if (addressInfo.getFormatted_address() != null && !addressInfo.getFormatted_address().equals(addressInfo.getTownship())) {
                        address.append(addressInfo.getFormatted_address());
                    }
                    log.info("getAddressByLocation_res={}", address.toString());

                    return address.toString();
                }
            }
            log.info("逆地理编码失败");

            return null;

        } catch (Exception e) {
            log.error("地址解析异常", e);
            return null;
        }
    }

    /**
     * 根据城市ID和地址信息获取经纬度
     *
     * @param request 地址请求参数
     * @return 经纬度字符串，格式为"经度,纬度"，获取失败时返回null
     */
    @Tool(name = "getLocationByAddress", description = "Get latitude and longitude by address")
    public String getLocationByAddress(GeocodingDescRequest request) {
        log.info("getLocationByAddress_request={}", JSON.toJSONString(request));
        if (request == null || request.getAddress() == null || request.getCityId() == null) {
            log.info("城市ID或地址参数为空");
            return null;
        }

        try {
            // 构建请求参数
            GeoRequest geoRequest = new GeoRequest();
            geoRequest.setKey(Environment.isTestEnv() ? TEST_KEY : PROD_KEY);
            geoRequest.setAddress(request.getAddress());
            geoRequest.setCity(request.getCityId());

            // 调用地图服务进行地理编码
            GeoResponse response = districtService.geo(geoRequest);

            // 处理响应结果
            if (response != null && response.getStatus() == 200) {
                if (response.getGeocodes() != null && !response.getGeocodes().isEmpty()) {
                    Geocode geocode = response.getGeocodes().get(0);

                    log.info("getLocationByAddress_res={}", JsonUtils.toJsonString(geocode));
                    // 返回location字段，格式已经是"经度,纬度"
                    return geocode.getLocation();
                }
            }
            log.info("地理编码失败");

            return null;

        } catch (Exception e) {
            log.error("地理编码异常", e);
            return null;
        }
    }

    /**
     * 根据关键字搜索城市代码和位置
     *
     * @param poiSearchRequest
     * @return
     */
    @Tool(name = "searchLocationByKeyword", description = "Get city code and location by keyword")
    public PoiSearchResult searchLocationByKeyword(PoiSearchRequest poiSearchRequest) {
        log.info("searchLocationByKeyword_request={}", JSON.toJSONString(poiSearchRequest));

        if (poiSearchRequest == null || StringUtils.isBlank(poiSearchRequest.getKeyword())) {
            log.info("关键字为空");
            return null;
        }
        
        try {
            // 1. 构建请求参数
            SearchServiceRequest request = new SearchServiceRequest();
            request.setKey(Environment.isTestEnv() ? TEST_KEY : PROD_KEY);
            // 设置关键字
            request.setKeywords(poiSearchRequest.getKeyword());
            
            // 只有当经纬度都不为null且都大于0时才设置位置信息
            if (poiSearchRequest.getLat() != null && poiSearchRequest.getLng() != null 
                && poiSearchRequest.getLat() > 0.0 && poiSearchRequest.getLng() > 0.0) {
                // 设置经纬度
                request.setUser_location(String.format("%.6f,%.6f", poiSearchRequest.getLng(), poiSearchRequest.getLat()));
            } 

            log.info("searchPoiByKeyword_request={}", JSON.toJSONString(request));

            // 2. 调用地图服务进行周边搜索
            SearchServiceResponse response = districtService.text(request);

            log.info("searchPoiByKeyword_response={}", JSON.toJSONString(response));

            // 3. 处理响应结果
            if (response != null && response.getStatus() == 200) {
                if (response.getPois() != null && !response.getPois().isEmpty()) {
                    // 获取POI列表中的第一个结果
                    Poi firstPoi = response.getPois().get(0);
                    String cityCode = firstPoi.getMt_open_cityid();
                    String location = firstPoi.getLocation();
                    log.info("周边搜索成功_cityCode={},location={}", cityCode, location);
                    return new PoiSearchResult(cityCode, location);
                }
            }
            log.info("周边搜索失败或未找到结果_response={}", JSON.toJSONString(response));
            return null;

        } catch (Exception e) {
            log.error("周边搜索异常", e);
            return null;
        }
    }
}

