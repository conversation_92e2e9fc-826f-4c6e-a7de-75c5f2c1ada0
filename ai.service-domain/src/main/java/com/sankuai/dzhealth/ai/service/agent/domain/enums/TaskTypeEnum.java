package com.sankuai.dzhealth.ai.service.agent.domain.enums;

import lombok.Getter;

/**
 * @author:chenwei
 * @time: 2025/7/11 11:37
 * @version: 0.0.1
 */

@Getter
public enum TaskTypeEnum {


    MEDICAL_AGENT_TASK("medicalAgent", "医美口腔Agent"),

    MEDICAL_MEMORY_UPDATE_TASK("medicalMemoryUpdate", "记忆更新任务"),

    MEDICAL_BAIKE_TASK("medicalBaike", "医美科普任务"),
    MEDICAL_PRODUCT_TASK("medicalProduct", "医美商品导购"),
    MEDICAL_APPOINTMENT_TASK("medicalAppointment", "医美预约任务"),

    RECOMMEND_LIST_TASK("recommendList", "查看推荐list"),

    RELATED_QUESTION_TASK("relatedQuestion", "猜你想问"),

    UPDATE_SESSION_DIGEST_TASK("updateSessionDigest", "更新会话摘要"),

    ;


    private String type;

    private String desc;

    TaskTypeEnum(String type, String desc) {
        this.type = type;
        this.desc = desc;
    }
}
