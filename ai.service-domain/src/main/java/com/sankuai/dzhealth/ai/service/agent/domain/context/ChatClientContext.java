package com.sankuai.dzhealth.ai.service.agent.domain.context;

import com.sankuai.dzhealth.ai.service.agent.domain.buffer.MarkBuffer;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * @author:chenwei
 * @time: 2025/7/11 14:38
 * @version: 0.0.1
 */

@Data
public class ChatClientContext {

    private String systemPrompt;

    private String userPrompt;

    private String kmsKey;

    private String model;

    private List<String> toolNames;

    private MessageContext messageContext;

    private boolean stream;

    private Integer maxTokens;

    private String taskType;

    private Map<String, Object> extraBodyConfig;

    private String thinkingText;

    private MarkBuffer markBuffer;

}
