package com.sankuai.dzhealth.ai.service.agent.domain.utils;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Bean工具类，用于获取Spring容器中的Bean实例
 * 
 * @author:chenwei
 * @time: 2025/8/19 15:41
 * @version: 0.0.1
 */
@Component
public class BeanContextUtils implements ApplicationContextAware {
    
    private static ApplicationContext applicationContext;
    
    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        BeanContextUtils.applicationContext = applicationContext;
    }
    
    /**
     * 根据bean名称获取bean实例
     * 
     * @param beanName bean名称
     * @return bean实例，如果未找到返回null
     */
    public static Object getBean(String beanName) {
        if (applicationContext == null) {
            return null;
        }
        try {
            return applicationContext.getBean(beanName);
        } catch (BeansException e) {
            return null;
        }
    }
    
    /**
     * 根据bean名称和类型获取bean实例
     * 
     * @param beanName bean名称
     * @param clazz 期望的bean类型
     * @param <T> 泛型类型
     * @return 指定类型的bean实例，如果未找到或类型不匹配返回null
     */
    public static <T> T getBean(String beanName, Class<T> clazz) {
        if (applicationContext == null) {
            throw new IllegalStateException("ApplicationContext未初始化");
        }
        try {
            return applicationContext.getBean(beanName, clazz);
        } catch (BeansException e) {
            return null;
        }
    }
    
    /**
     * 根据类型获取bean实例
     * 
     * @param clazz bean类型
     * @param <T> 泛型类型
     * @return 指定类型的bean实例，如果未找到返回null
     */
    public static <T> T getBean(Class<T> clazz) {
        if (applicationContext == null) {
            throw new IllegalStateException("ApplicationContext未初始化");
        }
        try {
            return applicationContext.getBean(clazz);
        } catch (BeansException e) {
            return null;
        }
    }
    
    /**
     * 检查容器中是否包含指定名称的bean
     * 
     * @param beanName bean名称
     * @return 如果包含返回true，否则返回false
     */
    public static boolean containsBean(String beanName) {
        if (applicationContext == null) {
            return false;
        }
        return applicationContext.containsBean(beanName);
    }
    
    /**
     * 根据一批bean名称获取bean实例列表，自动过滤null值
     * 
     * @param beanNames bean名称数组
     * @return 非null的bean实例列表
     */
    public static List<Object> getBeans(List<String> beanNames) {
        if (CollectionUtils.isEmpty(beanNames)) {
            return new ArrayList<>();
        }
        return beanNames.stream()
                .map(BeanContextUtils::getBean)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }
    
    /**
     * 根据一批bean名称和指定类型获取bean实例列表，自动过滤null值
     * 
     * @param clazz 期望的bean类型
     * @param beanNames bean名称数组
     * @param <T> 泛型类型
     * @return 指定类型的非null bean实例列表
     */
    @SuppressWarnings("unchecked")
    public static <T> List<T> getBeans(Class<T> clazz, String... beanNames) {
        if (beanNames == null || beanNames.length == 0) {
            return new ArrayList<>();
        }
        return Arrays.stream(beanNames)
                .map(beanName -> getBean(beanName, clazz))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }
    
    /**
     * 根据一批bean名称和指定类型获取bean实例列表，自动过滤null值
     * 
     * @param clazz 期望的bean类型
     * @param beanNames bean名称集合
     * @param <T> 泛型类型
     * @return 指定类型的非null bean实例列表
     */
    @SuppressWarnings("unchecked")
    public static <T> List<T> getBeans(Class<T> clazz, Collection<String> beanNames) {
        if (beanNames == null || beanNames.isEmpty()) {
            return new ArrayList<>();
        }
        return beanNames.stream()
                .map(beanName -> getBean(beanName, clazz))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }
}
