package com.sankuai.dzhealth.ai.service.domain.utils.context;

import com.alibaba.fastjson.JSON;
import com.dianping.cat.util.Pair;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.sankuai.dzhealth.ai.service.domain.service.strategy.RagEsStrategy;
import com.sankuai.dzhealth.ai.service.domain.service.strategy.SearchStrategy;
import com.sankuai.dzhealth.ai.service.domain.service.strategy.ShopRagEsStrategy;
import com.sankuai.dzhealth.ai.service.infrastructure.observability.Span;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.LinkedList;
import java.util.List;
import java.util.Map;

/**
 * @author:chenwei
 * @time: 2025/3/19 16:31
 * @version: 0.0.1
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AiAnswerContext {

    @FieldDoc(description = "用户id", requiredness = Requiredness.REQUIRED)
    private String userId;

    @FieldDoc(description = "用户选择的地址纬度", requiredness = Requiredness.OPTIONAL)
    private Double lat;


    @FieldDoc(description = "用户选择的地址经度", requiredness = Requiredness.OPTIONAL)
    private Double lng;


    @FieldDoc(description = "客户端类型", requiredness = Requiredness.OPTIONAL)
    private String clientType;


    @FieldDoc(description = "uuid美团侧唯一设备id", requiredness = Requiredness.OPTIONAL)
    private String uuid;


    @FieldDoc(description = "app版本，如8.29.0", requiredness = Requiredness.OPTIONAL)
    private String appVersion;


    @FieldDoc(description = "用户选择的地址-二级城市id，如city=北京市", requiredness = Requiredness.OPTIONAL)
    private Integer cityId;

    @FieldDoc(description = "用户真实cityId", requiredness = Requiredness.OPTIONAL)
    private Integer userCityId;

    @FieldDoc(description = "是否授权地理位置信息", requiredness = Requiredness.OPTIONAL)
    private Integer locationAuthorized;

    @FieldDoc(
            description = "会话id"
    )
    private Long sessionId;

    @FieldDoc(
            description = "消息id"
    )
    private Long msgId;

    @FieldDoc(
            description = "前端请求id"
    )
    private String requestId;

    @FieldDoc(
            description = "扩展信息-json"
    )
    private String extension;

    @FieldDoc(
            description = "业务场景"
    )
    private String bizScene;

    @FieldDoc(
            description = "请求时间"
    )
    private String requestTime;

    @FieldDoc(
            description = "请求内容"
    )
    private String content;

    @FieldDoc(
            description = "请求消息类型 1:text、2:image"
    )
    private Integer type;

    @FieldDoc(
            description = "角色: user"
    )
    private String role;

    @FieldDoc(
            description = "历史上下文摘要"
    )
    private String history;

    @FieldDoc(
            description = "商户id"
    )
    private Long shopId;

    @FieldDoc(
            description = "商户id"
    )
    private Long mtShopId;

    @FieldDoc(
            description = "是否流式"
    )
    private Boolean stream;

    @FieldDoc(
            description = "平台"
    )
    private Integer platform;

    @FieldDoc(
            description = "会话唯一标识"
    )
    private String conversationIdStr;

    @FieldDoc(
            description = "消息唯一标识"
    )
    private String messageIdStr;

    @FieldDoc(
            description = "TraceId"
    )
    private String traceId;

    @FieldDoc(
            description = "TraceId"
    )
    private List<Span> spans;

    @FieldDoc(
            description = "业务关联类型(SHOP/PRODUCT/ORDER/ACTIVITY)"
    )
    private String businessType;

    @FieldDoc(
            description = "关联业务ID"
    )
    private String businessId;

    private Map<String, Pair<Long, Long>> departName2IdMap;

    private Map<String, Integer> stdDepartName2IdMap;

    @FieldDoc(
            description = "回复可见内容"
    )
    private String assistantVisibleContent;

    @FieldDoc(
            description = "回复全部内容"
    )
    private String assistantContent;

    private String status;

    private List<SearchStrategy.SearchStrategyInfo.BaseSearchStrategyInfo> searchInfo;

    private List<RagEsStrategy.RagStrategyInfo.BaseRagStrategyInfo> ragInfo;

    private List<ShopRagEsStrategy.ShopRagStrategyInfo.ShopBaseRagStrategyInfo> shopRagInfo;

    @FieldDoc(
            description = "前置是否有文字输出"
    )
    private boolean hasText;

    @FieldDoc(
            description = "意图识别结果的key"
    )
    private String intentionKey;

    public void addSpan(Span span){
        if (this.spans == null){
            this.spans = new LinkedList<>();
        }
        this.spans.add(span);
    }
    public void addSpans(List<Span> span){
        if (this.spans == null){
            this.spans = new LinkedList<>();
        }
        this.spans.addAll(span);
    }

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }

    public void setSearchInfo(List<SearchStrategy.SearchStrategyInfo.BaseSearchStrategyInfo> searchInfo) {
        this.searchInfo = searchInfo;
    }

    public List<SearchStrategy.SearchStrategyInfo.BaseSearchStrategyInfo> getSearchInfo() {
        return searchInfo;
    }

    public void setRagInfo(List<RagEsStrategy.RagStrategyInfo.BaseRagStrategyInfo> ragInfo) {
        this.ragInfo = ragInfo;
    }

    public List<RagEsStrategy.RagStrategyInfo.BaseRagStrategyInfo> getRagInfo() {
        return ragInfo;
    }
}
