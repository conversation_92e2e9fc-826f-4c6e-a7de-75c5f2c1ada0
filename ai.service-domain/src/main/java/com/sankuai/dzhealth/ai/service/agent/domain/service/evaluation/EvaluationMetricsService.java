package com.sankuai.dzhealth.ai.service.agent.domain.service.evaluation;

import com.dianping.haima.entity.haima.HaimaContent;
import com.meituan.mdp.boot.starter.trace.annotation.MdpTrace;
import com.meituan.mtrace.ITracer;
import com.meituan.mtrace.Tracer;
import com.sankuai.dzhealth.ai.service.domain.utils.JsonUtils;
import com.sankuai.dzhealth.ai.service.dto.haima.EvaluationMetric;
import com.sankuai.dzhealth.ai.service.infrastructure.acl.HaimaAcl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class EvaluationMetricsService {
    @Autowired
    private HaimaAcl haimaAcl;

    private static final String EVALUATION_METRIC_INFO = "evaluation_metric_info";

    public List<EvaluationMetric> getEvaluationMetricInfo() {
        ITracer tracer = Tracer.getServerTracer();
        List<HaimaContent> medicalTaskAiConfig = haimaAcl.getContent(EVALUATION_METRIC_INFO, null);
        return medicalTaskAiConfig
                .stream()
                .map(e -> {
                    return JsonUtils.parseObject(e.getExtJson(), EvaluationMetric.class);
                })
                .toList();
    }
}
