package com.sankuai.dzhealth.ai.service.domain.chat.model;

import com.alibaba.fastjson.JSON;
import com.facebook.swift.codec.ThriftField;
import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import lombok.*;

@ThriftStruct
@NoArgsConstructor
@AllArgsConstructor
public class ChatSummaryResult {
    @FieldDoc(description = "总结结果")
    @Getter(onMethod_ = @ThriftField(value = 1))
    @Setter(onMethod_ = @ThriftField)
    private Integer summaryResult;

    /**
     * 针对用户问题给出的结论
     */
    @FieldDoc(description = "结论")
    @Getter(onMethod_ = @ThriftField(value = 2))
    @Setter(onMethod_ = @ThriftField)
    private String conclusion;

    /**
     * 结论的相关回答
     */
    @FieldDoc(description = "相关回答")
    @Getter(onMethod_ = @ThriftField(value = 3))
    @Setter(onMethod_ = @ThriftField)
    private String relatedAnswers;

    public ChatSummaryResult(Integer summaryResult) {
        this.summaryResult = summaryResult;
    }

    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }
}
