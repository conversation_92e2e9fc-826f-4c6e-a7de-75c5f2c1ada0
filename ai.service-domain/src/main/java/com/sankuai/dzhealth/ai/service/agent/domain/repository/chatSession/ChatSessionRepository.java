package com.sankuai.dzhealth.ai.service.agent.domain.repository.chatSession;

import com.dianping.lion.client.util.CollectionUtils;
import com.meituan.beauty.fundamental.light.plat.Platform;
import com.sankuai.dzhealth.ai.service.agent.domain.entity.ChatSessionEntity;
import com.sankuai.dzhealth.ai.service.agent.infrastructure.dal.entity.ChatSessionDOWithBLOBs;
import com.sankuai.dzhealth.ai.service.agent.infrastructure.dal.example.ChatSessionDOExample;
import com.sankuai.dzhealth.ai.service.agent.infrastructure.dal.mapper.ChatSessionDOMapper;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 主要负责 chat_session 表的 CRUD 操作，供领域层调用。
 */
@Repository
@RequiredArgsConstructor
public class ChatSessionRepository {
    @Resource
    private ChatSessionDOMapper chatSessionDOMapper;

    public static final Integer STATUS_NORMAL = 0;
    public static final Integer STATUS_DELETED = 1;

    /**
     * 包装方法，处理类型转换
     */
    private List<ChatSessionDOWithBLOBs> selectWithBLOBs(ChatSessionDOExample example) {
        return chatSessionDOMapper.selectByExampleWithBLOBs(example)
                .stream()
                .filter(obj -> obj instanceof ChatSessionDOWithBLOBs)
                .map(obj -> (ChatSessionDOWithBLOBs) obj)
                .collect(Collectors.toList());
    }

    /**
     * 类型转换方法：ChatSessionEntity -> ChatSessionDOWithBLOBs
     */
    private ChatSessionDOWithBLOBs convertToDO(ChatSessionEntity entity) {
        return ChatSessionDOWithBLOBs.builder()
                .sessionId(entity.getSessionId())
                .userId(entity.getUserId())
                .platform(entity.getPlatform())
                .bizId(entity.getBizId())
                .bizType(entity.getBizType())
                .title(entity.getTitle())
                .digest(entity.getDigest())
                .memory(entity.getMemory())
                .extra(entity.getExtra())
                .status(entity.getStatus())
                .createTime(entity.getCreateTime())
                .updateTime(Optional.ofNullable(entity.getUpdateTime()).orElse(new Date()))
                .build();
    }

    private ChatSessionEntity convertToEntity(ChatSessionDOWithBLOBs doWithBLOBs) {
        return ChatSessionEntity.builder()
                .id(doWithBLOBs.getId())
                .sessionId(doWithBLOBs.getSessionId())
                .platform(doWithBLOBs.getPlatform())
                .userId(doWithBLOBs.getUserId())
                .bizType(doWithBLOBs.getBizType())
                .bizId(doWithBLOBs.getBizId())
                .title(doWithBLOBs.getTitle())
                .digest(doWithBLOBs.getDigest())
                .memory(doWithBLOBs.getMemory())
                .status(doWithBLOBs.getStatus())
                .createTime(doWithBLOBs.getCreateTime())
                .updateTime(Optional.ofNullable(doWithBLOBs.getUpdateTime()).orElse(new Date()))
                .build();
    }


    public void insertSession(String sessionId, Long userId, String bizType, int platform, String title, String digest) {

        ChatSessionEntity chatSessionEntity = buildSessionEntity(sessionId, userId, bizType, platform, title, digest);
        insert(chatSessionEntity);


    }

    /**
     * 插入会话
     */
    public void insert(ChatSessionEntity session) {
        // 校验必填字段
        if (session == null) {
            throw new IllegalArgumentException("session不能为空");
        }
        if (StringUtils.isBlank(session.getSessionId())) {
            throw new IllegalArgumentException("sessionId不能为空");
        }
        if (StringUtils.isBlank(session.getBizType())) {
            throw new IllegalArgumentException("bizType不能为空");
        }
        if (Platform.get(session.getPlatform()) == null) {
            throw new IllegalArgumentException("platform非法");
        }
        if (session.getUserId() == null) {
            throw new IllegalArgumentException("userId不能为空");
        }

        ChatSessionDOWithBLOBs sessionDO = convertToDO(session);
        chatSessionDOMapper.insertSelective(sessionDO);
    }

    public ChatSessionEntity buildSessionEntity(String sessionId, Long userId, String bizType, int platform, String title, String digest) {
        return ChatSessionEntity.builder()
                .sessionId(sessionId)
                .userId(userId)
                .bizType(bizType)
                .platform(platform)
                .title(Optional.ofNullable(title).orElse(""))
                .digest(Optional.ofNullable(digest).orElse(""))
                .updateTime(new Date())
                .build();
    }

    /**
     * 批量插入会话
     */
    public void batchInsert(List<ChatSessionEntity> sessions) {
        if (sessions == null || sessions.isEmpty()) {
            return;
        }
        for (ChatSessionEntity session : sessions) {
            insert(session);
        }
    }

    /**
     * 根据会话ID和用户ID删除会话（逻辑删除）
     */
    public int deleteSingleSession(String sessionId) {
        return updateSession(sessionId, null, null, null, STATUS_DELETED); // 状态设为1表示删除
    }

    public int deleteAllSession(Long userId, int platform, String bizType) {
        ChatSessionEntity updateSession = ChatSessionEntity.builder()
                .status(STATUS_DELETED)
                .updateTime(new Date())
                .build();

        ChatSessionDOExample example = new ChatSessionDOExample();
        example.createCriteria()
                .andUserIdEqualTo(userId)
                .andPlatformEqualTo(platform)
                .andBizTypeEqualTo(bizType);
        // 将 ChatSessionEntity 转换为 ChatSessionDOWithBLOBs
        ChatSessionDOWithBLOBs sessionDO = convertToDO(updateSession);
        return chatSessionDOMapper.updateByExampleSelective(sessionDO, example);

    }

    /**
     * 根据会话ID和用户ID更新会话
     */
    public int updateSingleSession(ChatSessionEntity updateSession, String sessionId) {
        ChatSessionDOExample example = new ChatSessionDOExample();
        example.createCriteria()
                .andSessionIdEqualTo(sessionId);
        // 将 ChatSessionEntity 转换为 ChatSessionDOWithBLOBs
        ChatSessionDOWithBLOBs sessionDO = convertToDO(updateSession);
        return chatSessionDOMapper.updateByExampleSelective(sessionDO, example);
    }

    /**
     * 根据会话ID和用户ID更新标题
     */
    public int updateSession(String sessionId, String title, String digest, String memory, Integer status) {
        ChatSessionEntity updateSession = ChatSessionEntity.builder()
                .title(title)
                .digest(digest)
                .memory(memory)
                .status(status)
                .updateTime(new Date())
                .build();
        return updateSingleSession(updateSession, sessionId);
    }

    /**
     * 根据会话ID和用户ID查询会话
     */
    public ChatSessionEntity findBySessionId(String sessionId, Long userId, int platform, String bizType) {
        ChatSessionDOExample example = new ChatSessionDOExample();
        ChatSessionDOExample.Criteria criteria = example.createCriteria()
                .andSessionIdEqualTo(sessionId)
                .andPlatformEqualTo(platform)
                .andStatusEqualTo(STATUS_NORMAL)
                .andUserIdEqualTo(userId);

        if (StringUtils.isNotBlank(bizType)) {
            criteria.andBizTypeEqualTo(bizType);
        }
        List<ChatSessionDOWithBLOBs> results = selectWithBLOBs(example);
        return results.isEmpty() ? null : convertToEntity(results.get(0));
    }

    /**
     * 根据用户ID、业务类型和状态查询会话列表（分页通用方法）
     *
     * @param userId           用户ID
     * @param platform         平台类型
     * @param bizType          业务类型
     * @param status           状态
     * @param recentUpdateTime 最近更新时间过滤，查询更新时间大于等于此时间的记录，为null时不过滤
     * @param limit            分页大小
     * @param offset           分页偏移量
     * @return 会话实体列表
     */
    public List<ChatSessionEntity> findSessions(Long userId, Integer platform, String bizType, Integer status, Date recentUpdateTime, int limit, int offset, boolean emptyTitle) {
        ChatSessionDOExample example = new ChatSessionDOExample();
        ChatSessionDOExample.Criteria criteria = example.createCriteria()
                .andUserIdEqualTo(userId);


        if (bizType != null) {
            criteria.andBizTypeEqualTo(bizType);
        }

        if (platform != null) {
            criteria.andPlatformEqualTo(platform);
        }

        if (status != null) {
            criteria.andStatusEqualTo(status);
        }

        if (recentUpdateTime != null) {
            criteria.andUpdateTimeGreaterThanOrEqualTo(recentUpdateTime);
        }

        if (!emptyTitle) {
            criteria.andTitleNotEqualTo(StringUtils.EMPTY);
        }

        example.setOrderByClause("create_time DESC LIMIT " + limit + " OFFSET " + offset);
        return selectWithBLOBs(example).stream().map(this::convertToEntity).collect(Collectors.toList());
    }


    public List<ChatSessionEntity> findSessionsByUpdateTime(Long userId, Integer platform, String bizType, Integer status, Date recentUpdateTime, int limit, int offset, boolean emptyTitle) {
        ChatSessionDOExample example = new ChatSessionDOExample();
        ChatSessionDOExample.Criteria criteria = example.createCriteria()
                .andUserIdEqualTo(userId);

        if (bizType != null) {
            criteria.andBizTypeEqualTo(bizType);
        }

        if (platform != null) {
            criteria.andPlatformEqualTo(platform);
        }

        if (status != null) {
            criteria.andStatusEqualTo(status);
        }

        if (recentUpdateTime != null) {
            criteria.andUpdateTimeGreaterThanOrEqualTo(recentUpdateTime);
        }

        if (!emptyTitle) {
            criteria.andTitleNotEqualTo(StringUtils.EMPTY);
        }

        example.setOrderByClause("update_time DESC LIMIT " + limit + " OFFSET " + offset);
        return selectWithBLOBs(example).stream().map(this::convertToEntity).collect(Collectors.toList());
    }


    public List<ChatSessionDOWithBLOBs> findSessionsBySessionIDs(List<String> sessionIDs, Long useID) {
        ChatSessionDOExample example = new ChatSessionDOExample();
        example.createCriteria()
                .andSessionIdIn(sessionIDs).andUserIdEqualTo(useID);
        return selectWithBLOBs(example);
    }

    public int batchInsertChatSessionDOWithBLOBs(List<ChatSessionDOWithBLOBs> chatSessionDOWithBLOBs) {
        if (CollectionUtils.isEmpty(chatSessionDOWithBLOBs)) {
            return 0;
        }
        return chatSessionDOMapper.batchInsert(chatSessionDOWithBLOBs);
    }

}
