package com.sankuai.dzhealth.ai.service.domain.evaluation.strategy;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.Cat;
import com.dianping.rhino.threadpool.ThreadPool;
import com.meituan.mtrace.Tracer;
import com.sankuai.dzhealth.ai.service.domain.evaluation.entity.RagEvaluationRequest;
import com.sankuai.dzhealth.ai.service.domain.evaluation.entity.RagEvaluationResponse;
import com.sankuai.dzhealth.ai.service.domain.utils.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.messages.AbstractMessage;
import org.springframework.ai.chat.model.ChatResponse;
import org.springframework.ai.chat.model.Generation;
import org.springframework.ai.document.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import com.sankuai.dzhealth.ai.service.enums.MetadataKeyEnum;

import java.util.*;
import java.util.concurrent.CompletableFuture;

/**
 * @author: yangweicheng
 * @date: 2025/4/23 16:29
 * @version: 1.0
 */
@Component
@Slf4j
public class LLMEvaluationStrategy {

    @Autowired
    private ChatClient evaluationChatClient;

    public CompletableFuture<List<RagEvaluationResponse>> execute(
            RagEvaluationRequest request,
            String systemPrompt,
            String userPrompt,
            ThreadPool threadPool) {

        return CompletableFuture.supplyAsync(() -> {
            List<RagEvaluationResponse> evaluationResults = new ArrayList<>();
            Map<String, String> tags = new HashMap<>();
            tags.put("bizScene", request.getBizScene());
            tags.put("modelScene", request.getModelScene());

            try {
                String user = userPrompt;
                user = user.replace("${query}", request.getUserText());

                String ragContent = "";
                if (request.getDataList() != null && !request.getDataList().isEmpty()) {
                    StringBuilder ragContentBuilder = new StringBuilder();
                    for (Document doc : request.getDataList()) {
                        ragContentBuilder.append("\n## ").append(doc.getMetadata().get(MetadataKeyEnum.SOURCE.getKey())).append("\n").append(doc.getText());
                    }
                    ragContent = ragContentBuilder.toString();
                }
                user = user.replace("${rag_content}", ragContent);
                user = user.replace("${answer}", request.getResponseContent());

                // 调用评估服务
                Optional<JSONObject> response = Optional.ofNullable(
                                evaluationChatClient.prompt()
                                        .system(systemPrompt)
                                        .user(user)
                                        .call()
                                        .chatResponse())
                        .map(ChatResponse::getResult)
                        .map(Generation::getOutput)
                        .map(AbstractMessage::getText)
                        .map(JsonUtils::extractJsonContent)
                        .filter(JsonUtils::isValidJson)
                        .map(JSON::parseObject);

                if (!response.isPresent()) {
                    evaluationResults.add(RagEvaluationResponse.builder()
                            .key("error")
                            .description("Empty response")
                            .pass(false)
                            .score(0)
                            .feedback("No evaluation result returned")
                            .success(false)
                            .sessionId(request.getSessionId())
                            .msgId(request.getMsgId())
                            .bizScene(request.getBizScene())
                            .modelScene(request.getModelScene())
                            .build());
                    return evaluationResults;
                }

                JSONObject jsonObject = response.get();

                try {
                    JSONArray metrics = jsonObject.getJSONArray("metrics");
                    for (int i = 0; i < metrics.size(); i++) {
                        JSONObject metric = metrics.getJSONObject(i);
                        boolean isValid = metric.containsKey("key")
                                && metric.containsKey("description");

                        String key = JsonUtils.getStringSafely(metric, "key", "unnamed_metric_" + i);
                        String description = JsonUtils.getStringSafely(metric, "description", "");
                        String score = JsonUtils.getStringSafely(metric, "score", "0");
                        String feedback = JsonUtils.getStringSafely(metric, "feedback", "");
                        boolean pass = Boolean.parseBoolean(JsonUtils.getStringSafely(metric, "pass", "false"));
                        Map<String, Object> metaDataMap = new HashMap<>();
                        String metaData = JsonUtils.getStringSafely(metric, "metadata", "");
                        if (!metaData.isEmpty()) {
                            try {
                                metaDataMap = JSON.parseObject(metaData, Map.class);
                            } catch (Exception e) {
                                log.error("Failed to parse metadata to Map", e);
                            }
                        }

                        evaluationResults.add(RagEvaluationResponse.builder()
                                .pass(pass)
                                .key(key)
                                .modelScene(request.getModelScene())
                                .bizScene(request.getBizScene())
                                .description(description)
                                .success(isValid)
                                .score(Float.parseFloat(score))
                                .feedback(feedback)
                                .metadata(metaDataMap)
                                .sessionId(request.getSessionId())
                                .msgId(request.getMsgId())
                                .build());

                        logMetric(description, score, tags);
                    }
                } catch (Exception e) {
                    evaluationResults.add(RagEvaluationResponse.builder()
                            .key("error")
                            .description("Failed to parse metrics")
                            .score(0)
                            .feedback("Invalid metrics format: " + e.getMessage())
                            .success(false)
                            .sessionId(request.getSessionId())
                            .msgId(request.getMsgId())
                            .bizScene(request.getBizScene())
                            .modelScene(request.getModelScene())
                            .build());
                }

                return evaluationResults;
            } catch (Exception e) {
                log.error("Error in LLM evaluation", e);
                Cat.logError("Error in LLM evaluation", e);
                Cat.logMetricForCount("评价失败", 1, tags);
                evaluationResults.add(RagEvaluationResponse.builder()
                        .key("error")
                        .description("System error")
                        .score(0.0f)
                        .feedback(e.getMessage()+" traceId= " + Tracer.id())
                        .success(false)
                        .sessionId(request.getSessionId())
                        .msgId(request.getMsgId())
                        .build());
                return evaluationResults;
            }
        }, threadPool.getExecutor());
    }

    private void logMetric(String description, String score, Map<String, String> tags) {
        try {
            long scoreValue = Long.parseLong(score);
            Cat.logMetricForValue(description, scoreValue, tags);

        } catch (NumberFormatException e) {
            // ignore
            log.warn("Failed to log metric for description: {}, score: {}", description, score, e);
        }
    }
}
