package com.sankuai.dzhealth.ai.service.domain.service.strategy;

import com.dianping.cat.Cat;
import com.sankuai.dzhealth.ai.service.domain.chat.model.IntentionResult;
import com.sankuai.dzhealth.ai.service.domain.utils.context.AiAnswerContext;
import org.apache.commons.lang3.StringUtils;

import java.util.concurrent.CompletableFuture;

/**
 * 数据源策略接口
 * @param <T> 策略执行结果的类型
 */
public interface DataSourceStrategy<T> {
    /**
     * 是否应该执行该策略
     * @param intentionResult 意图识别结果
     * @return 是否执行
     */
    boolean shouldExecute(IntentionResult intentionResult);

    /**
     * 执行策略
     * @param context 上下文
     * @param rewriteText 改写后的文本
     * @return 策略执行结果
     */
    CompletableFuture<T> execute(AiAnswerContext context, String rewriteText);

    /**
     * 对策略结果进行过滤
     * 每个策略内部通过@MdpConfig配置决定是否真正执行过滤
     * 如果配置为不过滤，直接返回原结果即可
     * @param rewriteText 用户查询
     * @param strategyResult 策略执行的原始结果
     * @return 过滤后的结果，默认直接返回原结果（不过滤）
     */
    default T filterResult(String rewriteText, T strategyResult) {
        // 默认不过滤，直接返回原结果
        return strategyResult;
    }

    /**
     * 模板方法：执行策略并进行过滤
     * 所有策略都走统一的 execute → filter 流程
     *
     * @param context 上下文
     * @param rewriteText 改写后的文本
     * @return 执行和过滤后的结果
     */
    default CompletableFuture<T> executeWithFiltering(AiAnswerContext context,
                                                      String rewriteText) {
        long overallStartTime = System.currentTimeMillis();

        return execute(context, rewriteText)
            .thenApply(result -> {
                // 记录抓取阶段耗时（execute 完成时）
                long fetchDuration = System.currentTimeMillis() - overallStartTime;
                Cat.newTransactionWithDuration("DataSourceStrategy",this.getClass().getSimpleName()+".fetch", fetchDuration).complete();


                if (result == null) {
                    return null;
                }

                // 统一调用过滤方法，策略内部决定是否真正执行过滤
                if (StringUtils.isNotBlank(rewriteText)) {
                    long filterStartTime = System.currentTimeMillis();

                    try {
                        return filterResult(rewriteText, result);
                    } catch (Exception e) {
                        // 过滤异常时返回原始结果
                        return result;
                    } finally {
                        Cat.newTransactionWithDuration("DataSourceStrategy",this.getClass().getSimpleName()+".filter", System.currentTimeMillis() - filterStartTime).complete();
                    }
                } else {
                    // 查询为空时直接返回原结果
                    return result;
                }
            })
            .whenComplete((r, t) -> {
                // 记录整体耗时（execute + filter）
                long totalDuration = System.currentTimeMillis() - overallStartTime;
                Cat.newTransactionWithDuration("DataSourceStrategy",this.getClass().getSimpleName()+".total", totalDuration).complete();
            });
    }
} 