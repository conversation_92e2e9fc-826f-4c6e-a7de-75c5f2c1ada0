package com.sankuai.dzhealth.ai.service.domain.converter;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.sankuai.beautycontent.beautylaunchapi.guide.dto.AuthorModel;
import com.sankuai.beautycontent.beautylaunchapi.guide.dto.GuideModel;
import com.sankuai.beautycontent.beautylaunchapi.guide.dto.PitModel;
import com.sankuai.beautycontent.beautylaunchapi.guide.dto.TabModel;
import com.sankuai.dzhealth.ai.service.domain.utils.AestheticMedicineAiUrlUtils;
import com.sankuai.dzhealth.ai.service.dto.DoctorInfoDTO;
import com.sankuai.dzhealth.ai.service.dto.QaItemDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jsoup.Jsoup;

import java.util.ArrayList;
import java.util.List;

public class Guide2QaConverter {

    /**
     * GuideModel -> QaItemDTO list
     * 从guide的tabs层级结构中递归提取所有pits
     */
    public static List<QaItemDTO> convert(GuideModel guide, String productId, String productTitle, int platform, int idType, int categoryId) {
        List<QaItemDTO> list = new ArrayList<>();
        if (guide == null) {
            return list;
        }

        // 首先处理guide直接关联的pits（兜底逻辑）
        if (CollectionUtils.isNotEmpty(guide.getPits())) {
            for (PitModel pit : guide.getPits()) {
                QaItemDTO item = convertPitToQa(pit, productId, productTitle, platform, idType, categoryId, list.isEmpty());
                if (item != null) {
                    list.add(item);
                }
            }
        }

        // 然后递归处理tabs中的pits
        if (CollectionUtils.isNotEmpty(guide.getTabs())) {
            for (TabModel tab : guide.getTabs()) {
                extractPitsFromTab(tab, list, productId, productTitle, platform, idType, categoryId);
            }
        }

        return list;
    }

    /**
     * 递归从TabModel及其子tabs中提取所有pits
     */
    private static void extractPitsFromTab(TabModel tab, List<QaItemDTO> qaList, String productId, String productTitle, int platform, int idType, int categoryId) {
        if (tab == null) {
            return;
        }

        // 处理当前tab的pits
        if (CollectionUtils.isNotEmpty(tab.getPits())) {
            for (PitModel pit : tab.getPits()) {
                // 检查是否是第一个有效的问答项（qaList为空时表示这是第一个有效的）
                boolean isMainQuestion = qaList.isEmpty();
                QaItemDTO item = convertPitToQa(pit, productId, productTitle, platform, idType, categoryId, isMainQuestion);
                if (item != null) {
                    qaList.add(item);
                }
            }
        }

        // 递归处理子tabs
        if (CollectionUtils.isNotEmpty(tab.getTabs())) {
            for (TabModel childTab : tab.getTabs()) {
                extractPitsFromTab(childTab, qaList, productId, productTitle, platform, idType, categoryId);
            }
        }
    }

    /**
     * 将单个PitModel转换为QaItemDTO
     */
    private static QaItemDTO convertPitToQa(PitModel pit, String productId, String productTitle, int platform, int idType, int categoryId, boolean mainQuestion) {
        if (pit == null) {
            return null;
        }

        // 检查问题标题是否有效（包括富文本格式过滤）
        if (StringUtils.isBlank(pit.getTitle()) || isInvalidTitle(pit.getTitle())) {
            return null;
        }

        String answer = null;
        // 1. 优先用tips
        if (StringUtils.isNotBlank(pit.getTips())) {
            answer = pit.getTips();
        } else {
            // 2. 先做内容替换，再结构化提取
            String text = replaceText(pit.getText(), productTitle); // 可扩展更多参数
            String filterText = getPContent(text, "instrument", "summary");
            if (StringUtils.isBlank(filterText)) {
                filterText = getPContent(text, "p", "content");
            }
            answer = filterText;
        }

        // 检查答案是否有效，过滤掉空答案或者只有无效内容的情况
        if (StringUtils.isBlank(answer) || isInvalidAnswer(answer)) {
            return null;
        }

        return QaItemDTO.builder()
                .question(pit.getTitle())
                .answer(answer)
                .imageUrl(pit.getPic() != null ? pit.getPic().getUrl() : null)
                .agentQaUrl(AestheticMedicineAiUrlUtils.buildAgentUrl(productId, productTitle, platform, idType, categoryId, pit.getTitle(), mainQuestion))
                .build();
    }

    /**
     * 检查标题是否为无效内容（包含HTML标签或不是问句格式就视为无效）
     * <AUTHOR>
     */
    private static boolean isInvalidTitle(String title) {
        if (title == null) {
            return true;
        }

        // 检查是否包含HTML标签，有标签就视为无效
        if (title.contains("<") && title.contains(">")) {
            return true;
        }

        // 检查是否包含问号或"吗"字，没有就视为无效（不是问句格式）
        if (!title.contains("?") && !title.contains("？") && !title.contains("吗")) {
            return true;
        }

        return false;
    }

    /**
     * 检查答案是否为无效内容
     * <AUTHOR>
     */
    private static boolean isInvalidAnswer(String answer) {
        if (answer == null) {
            return true;
        }

        String trimmedAnswer = answer.trim();
        // 检查是否为空字符串
        if (trimmedAnswer.isEmpty()) {
            return true;
        }

        // 检查是否只有空的JSON数组
        if ("[]".equals(trimmedAnswer)) {
            return true;
        }

        // 检查是否只有空的JSON对象
        if ("{}".equals(trimmedAnswer)) {
            return true;
        }

        // 检查是否只包含空白字符、换行符等
        if (trimmedAnswer.matches("^[\\s\\n\\r\\t]*$")) {
            return true;
        }

        return false;
    }

    /**
     * 简单内容替换能力，支持${productName}等占位符
     */
    private static String replaceText(String text, String productTitle) {
        if (StringUtils.isBlank(text)) {
            return text;
        }
        // 可扩展更多变量
        return text.replace("${productName}", productTitle == null ? "" : productTitle);
    }

    /**
     * 结构化内容提取，参考launchweb逻辑
     */
    private static String getPContent(String text, String tag, String param) {
        if (StringUtils.isEmpty(text)) {
            return StringUtils.EMPTY;
        }
        try {
            // text为JSON数组字符串
            List<JSONObject> list = JSONArray.parseArray(text, JSONObject.class);
            List<String> textList = new ArrayList<>();
            if (list != null) {
                for (JSONObject obj : list) {
                    if (tag.equals(obj.getString("tag")) && StringUtils.isNotEmpty(obj.getString(param))) {
                        // 去除HTML标签
                        textList.add(Jsoup.parse(obj.getString(param)).text());
                    }
                }
            }
            return String.join("", textList);
        } catch (Exception e) {
            // 解析失败直接返回空
            return StringUtils.EMPTY;
        }
    }

    public static DoctorInfoDTO buildDoctorInfo(AuthorModel author) {
        if (author == null) return null;
        return DoctorInfoDTO.builder()
                .doctorId(String.valueOf(author.getId()))
                .name(author.getName())
                .title(author.getTitle())
                .hospitalLevel("三甲")
                .avatar(author.getPortrait())
                .build();
    }

} 