package com.sankuai.dzhealth.ai.service.domain.service.strategy;

import com.alibaba.fastjson.JSON;
import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.dianping.rhino.threadpool.ThreadPool;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.meituan.mdp.boot.starter.config.annotation.MdpConfig;
import com.sankuai.dzhealth.ai.service.api.ESVectorStoreService;
import com.sankuai.dzhealth.ai.service.api.RemoteResponse;
import com.sankuai.dzhealth.ai.service.domain.chat.model.IntentionResult;
import com.sankuai.dzhealth.ai.service.domain.service.ChatSessionService;
import com.sankuai.dzhealth.ai.service.domain.ability.llm.FilterableItem;
import com.sankuai.dzhealth.ai.service.domain.ability.llm.LLMResultFilterService;
import com.sankuai.dzhealth.ai.service.domain.utils.context.AiAnswerContext;
import com.sankuai.dzhealth.ai.service.dto.DocumentDTO;
import com.sankuai.dzhealth.ai.service.infrastructure.observability.Span;
import com.sankuai.dzhealth.ai.service.request.DocumentSearchRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.stream.IntStream;

import static java.util.stream.Collectors.toList;

import com.sankuai.dzhealth.ai.service.enums.MetadataKeyEnum;

/**
 * RAG院内信息策略实现
 */
@Component
@Slf4j
public class RagEsStrategy implements DataSourceStrategy<RagEsStrategy.RagStrategyInfo> {

    @MdpConfig("globe_rag_cnt:5")
    private int globe_topK = 5;

    /**
     * 是否启用RAG结果过滤功能
     * 配置项：public_hospital.rag.enable_result_filtering
     * 默认值：true（RAG结果可能存在噪音，建议默认开启过滤）
     */
    @MdpConfig("public_hospital.rag.enable_result_filtering:false")
    private Boolean enableRagResultFiltering;

    @Autowired
    private ESVectorStoreService esVectorStoreService;

    @Autowired
    private ChatClient retrieveStrategyResEvaChatClient;

    @Autowired
    private LLMResultFilterService llmResultFilterService;

    private static final ThreadPool taskPool = ChatSessionService.TASK_POOL;

    @Override
    public boolean shouldExecute(IntentionResult intentionResult) {
        return intentionResult.getRagNeed() != null && intentionResult.getRagNeed();
    }

    @Override
    public CompletableFuture<RagStrategyInfo> execute(AiAnswerContext context, String rewriteText) {

        Map<String, List<String>> metaMapDomain = new HashMap<>();
        metaMapDomain.put(MetadataKeyEnum.CHANNEL.getKey(), Lists.newArrayList("leadDisease", "leadSymptom",
                "knowledgeDisease", "knowledgeSymptom"));
        return CompletableFuture.supplyAsync(() -> queryEsVector(context, rewriteText, metaMapDomain, globe_topK),
                taskPool.getExecutor());
    }

    private RagStrategyInfo toAdd(RagStrategyInfo first, RagStrategyInfo second) {
        List<RagStrategyInfo.BaseRagStrategyInfo> firstRagInfo = first.getRagInfo();
        List<RagStrategyInfo.BaseRagStrategyInfo> secondRagInfo = second.getRagInfo();

        List<RagStrategyInfo.BaseRagStrategyInfo> addRagInfo = new ArrayList<>();
        addRagInfo.addAll(Optional.ofNullable(firstRagInfo).orElse(Collections.emptyList()));
        addRagInfo.addAll(Optional.ofNullable(secondRagInfo).orElse(Collections.emptyList()));
        RagStrategyInfo res = new RagStrategyInfo();
        res.setRagInfo(addRagInfo);
        ArrayList<Span> spans = new ArrayList<>();
        spans.addAll(Optional.ofNullable(first.getSpan()).orElse(Collections.emptyList()));
        spans.addAll(Optional.ofNullable(second.getSpan()).orElse(Collections.emptyList()));
        res.setSpan(spans);
        return res;

    }

    /**
     * 查询ES向量数据
     */
    private RagStrategyInfo queryEsVector(AiAnswerContext context,
                                          String rewriteText,
                                          Map<String, List<String>> metaMap,
                                          int topK) {
        Transaction transaction = Cat.newTransaction(getClass().getSimpleName(), "queryEsVector");
        long startTime = System.currentTimeMillis();
        try {
            DocumentSearchRequest searchRequest =
                    DocumentSearchRequest.builder().query(rewriteText).topK(topK).metaData(metaMap).build();
            //rag召回信息
            RemoteResponse<List<DocumentDTO>> listRemoteResponse = esVectorStoreService.similaritySearch(searchRequest);
            RagStrategyInfo ragEsInfo = new RagStrategyInfo();
            if (listRemoteResponse.getSuccess() && CollectionUtils.isNotEmpty(listRemoteResponse.getData())) {

                List<RagStrategyInfo.BaseRagStrategyInfo> ragStrategyInfos =
                        listRemoteResponse.getData().stream().map(e -> {
                            RagStrategyInfo.BaseRagStrategyInfo baseRagStrategyInfo =
                                    new RagStrategyInfo.BaseRagStrategyInfo();
                            baseRagStrategyInfo.setContent(e.getText());
                            baseRagStrategyInfo.setUri(e.getMetadata().get(MetadataKeyEnum.RESOURCE_URI.getKey()));
                            baseRagStrategyInfo.setPublishTime(e.getMetadata().get(MetadataKeyEnum.PUBLISH_TIME.getKey()));
                            baseRagStrategyInfo.setResourceChannel(e.getMetadata().get(MetadataKeyEnum.CHANNEL.getKey()));
                            return baseRagStrategyInfo;
                        }).collect(toList());
                ragEsInfo.setRagInfo(ragStrategyInfos);
            }
            Cat.newTransactionWithDuration(getClass().getSimpleName(), "EsRag", System.currentTimeMillis() - startTime)
                    .complete();
            ragEsInfo.setSpan(Collections.singletonList(Span.builder()
                    .key(getClass().getSimpleName())
                    .value(JSON.toJSONString(ImmutableMap.of("request", searchRequest, "response", listRemoteResponse)))
                    .duration(System.currentTimeMillis() - startTime)
                    .build()));
            transaction.setSuccessStatus();
            return ragEsInfo;
        } catch (Exception e) {
            transaction.setStatus(e);
            return new RagStrategyInfo();
        } finally {
            transaction.complete();
        }
    }

    @Override
    public RagStrategyInfo filterResult(String rewriteText, RagStrategyInfo strategyResult) {
        if (strategyResult == null || CollectionUtils.isEmpty(strategyResult.getRagInfo())) {
            return strategyResult;
        }

        // 根据配置决定是否执行过滤
        if (!enableRagResultFiltering) {
            log.debug("RAG结果过滤功能已禁用，返回原始结果");
            return strategyResult;
        }

        // RAG结果专用的系统提示
        String systemMessageContent = "你是一个专业的知识库检索结果评估助手，专门负责评估RAG检索结果的相关性。" +
                "请评估每个知识库检索结果是否有助于回答用户的医疗健康问题。" +
                "评估标准：1)内容与问题高度相关 2)医学知识准确 3)信息完整且有用 4)避免重复内容。" +
                "返回格式：JSON数组，每个元素包含resultId（从1开始）和isValid（true/false）。" +
                "示例：[{\"resultId\":1,\"isValid\":true},{\"resultId\":2,\"isValid\":false}]";

        List<RagStrategyInfo.BaseRagStrategyInfo> filteredRagInfo = llmResultFilterService.filterResults(
                rewriteText,
                strategyResult.getRagInfo(),
                systemMessageContent,
                retrieveStrategyResEvaChatClient
        );

        // 创建新的RagStrategyInfo
        RagStrategyInfo newResult = new RagStrategyInfo();
        newResult.setRagInfo(filteredRagInfo);
        newResult.setSpan(strategyResult.getSpan());

        return newResult;
    }

    @Data
    @EqualsAndHashCode(callSuper = true)
    public static class RagStrategyInfo extends BaseStrategyInfo {

        private List<BaseRagStrategyInfo> ragInfo;

        @Override
        public String toPrompt() {
            if (ragInfo == null || ragInfo.isEmpty()) {
                return "[]";
            }
            List<String> jsonResults = IntStream.range(0, ragInfo.size())
                    .mapToObj(index -> {
                        BaseRagStrategyInfo result = ragInfo.get(index);
                        return String.format(
                                "{\"序号\":\"%d\",\"内容\":\"%s\",\"来源\":\"%s\",\"发布时间\":\"%s\",\"类型\":\"%s\"}",
                                index + 1,
                                Optional.ofNullable(result.getContent()).orElse(""),
                                Optional.ofNullable(result.getResourceChannel()).orElse(""),
                                Optional.ofNullable(result.getPublishTime()).orElse(""),
                                "article"
                        );
                    })
                    .collect(toList());
            return "[" + String.join(",", jsonResults) + "]";
        }

        @Data
        public static class BaseRagStrategyInfo implements FilterableItem {

            private String content;

            private String uri;

            private String publishTime;

            private String resourceChannel;

            @Override
            public Map<String, String> getFieldsForEvaluation() {
                Map<String, String> fields = new LinkedHashMap<>();
                fields.put("内容", Optional.ofNullable(content).orElse(""));
                fields.put("来源渠道", Optional.ofNullable(resourceChannel).orElse(""));
                fields.put("发布时间", Optional.ofNullable(publishTime).orElse(""));
                return fields;
            }
        }
    }

}