package com.sankuai.dzhealth.ai.service.domain.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.ai.document.Document;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * JSON工具类，提供常用的JSON处理功能
 *
 * <AUTHOR>
 * @date 2024/06/11
 */
@Slf4j
public class JsonUtils {

    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();
    private static final Gson GSON = new GsonBuilder()
            .disableHtmlEscaping()
            .excludeFieldsWithoutExposeAnnotation()
            .create();

    static {
        // 初始化ObjectMapper配置
        OBJECT_MAPPER.setTimeZone(TimeZone.getTimeZone("GMT+8"));
        // 日期格式化
        OBJECT_MAPPER.setDateFormat(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"));
        // 序列化时忽略null值
        OBJECT_MAPPER.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        // 反序列化时忽略未知属性
        OBJECT_MAPPER.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    }

    /**
     * 对象转JSON字符串（Gson 版本，支持 @SerializedName/@Expose）
     */
    public static String toGsonString(Object object) {
        if (object == null) {
            return null;
        }
        try {
            return GSON.toJson(object);
        } catch (Exception e) {
            log.error("Convert object to JSON string by Gson error", e);
            return null;
        }
    }

    /**
     * 获取ObjectMapper实例
     *
     * @return ObjectMapper实例
     */
    public static ObjectMapper getObjectMapper() {
        return OBJECT_MAPPER;
    }

    /**
     * 对象转JSON字符串
     *
     * @param object 对象
     * @return JSON字符串
     */
    public static String toJsonString(Object object) {
        if (object == null) {
            return null;
        }
        try {
            return OBJECT_MAPPER.writeValueAsString(object);
        } catch (JsonProcessingException e) {
            log.error("Convert object to JSON string error", e);
            return null;
        }
    }

    /**
     * 对象转格式化的JSON字符串（用于日志打印）
     *
     * @param object 对象
     * @return 格式化的JSON字符串
     */
    public static String toPrettyJsonString(Object object) {
        if (object == null) {
            return null;
        }
        try {
            return OBJECT_MAPPER.writerWithDefaultPrettyPrinter().writeValueAsString(object);
        } catch (JsonProcessingException e) {
            log.error("Convert object to pretty JSON string error", e);
            return null;
        }
    }

    /**
     * JSON字符串转对象
     *
     * @param jsonString JSON字符串
     * @param clazz      目标类
     * @param <T>        目标类型
     * @return 目标对象
     */
    public static <T> T parseObject(String jsonString, Class<T> clazz) {
        if (jsonString == null || jsonString.isEmpty()) {
            return null;
        }
        try {
            return OBJECT_MAPPER.readValue(jsonString, clazz);
        } catch (IOException e) {
            log.error("Parse JSON string to object error, jsonString: {}, class: {}", jsonString, clazz.getName(), e);
            return null;
        }
    }

    /**
     * JSON字符串转复杂对象（使用TypeReference）
     *
     * @param jsonString    JSON字符串
     * @param typeReference 类型引用
     * @param <T>           目标类型
     * @return 目标对象
     */
    public static <T> T parseObject(String jsonString, TypeReference<T> typeReference) {
        if (jsonString == null || jsonString.isEmpty()) {
            return null;
        }
        try {
            return OBJECT_MAPPER.readValue(jsonString, typeReference);
        } catch (IOException e) {
            log.error("Parse JSON string to object error, jsonString: {}", jsonString, e);
            return null;
        }
    }

    /**
     * JSON字符串转列表
     *
     * @param jsonString JSON字符串
     * @param clazz      列表元素类
     * @param <T>        列表元素类型
     * @return 列表
     */
    public static <T> List<T> parseArray(String jsonString, Class<T> clazz) {
        if (jsonString == null || jsonString.isEmpty()) {
            return Collections.emptyList();
        }
        try {
            JavaType javaType = OBJECT_MAPPER.getTypeFactory().constructParametricType(List.class, clazz);
            return OBJECT_MAPPER.readValue(jsonString, javaType);
        } catch (IOException e) {
            log.error("Parse JSON string to array error, jsonString: {}, class: {}", jsonString, clazz.getName(), e);
            return Collections.emptyList();
        }
    }

    /**
     * JSON字符串转Map
     *
     * @param jsonString JSON字符串
     * @return Map对象
     */
    public static Map<String, Object> parseMap(String jsonString) {
        if (jsonString == null || jsonString.isEmpty()) {
            return Collections.emptyMap();
        }
        try {
            return OBJECT_MAPPER.readValue(jsonString, new TypeReference<Map<String, Object>>() {
            });
        } catch (IOException e) {
            log.error("Parse JSON string to map error, jsonString: {}", jsonString, e);
            return Collections.emptyMap();
        }
    }

    /**
     * JSON字符串转JsonNode
     *
     * @param jsonString JSON字符串
     * @return JsonNode对象
     */
    public static JsonNode parseJsonNode(String jsonString) {
        if (jsonString == null || jsonString.isEmpty()) {
            return null;
        }
        try {
            return OBJECT_MAPPER.readTree(jsonString);
        } catch (IOException e) {
            log.error("Parse JSON string to JsonNode error, jsonString: {}", jsonString, e);
            return null;
        }
    }

    /**
     * 对象转换为另一个对象
     *
     * @param source      源对象
     * @param targetClass 目标类
     * @param <T>         目标类型
     * @return 目标对象
     */
    public static <T> T convertValue(Object source, Class<T> targetClass) {
        if (source == null) {
            return null;
        }
        try {
            return OBJECT_MAPPER.convertValue(source, targetClass);
        } catch (IllegalArgumentException e) {
            log.error("Convert object to another object error", e);
            return null;
        }
    }

    /**
     * 对象转换为另一个复杂对象（使用TypeReference）
     *
     * @param source        源对象
     * @param typeReference 类型引用
     * @param <T>           目标类型
     * @return 目标对象
     */
    public static <T> T convertValue(Object source, TypeReference<T> typeReference) {
        if (source == null) {
            return null;
        }
        try {
            return OBJECT_MAPPER.convertValue(source, typeReference);
        } catch (IllegalArgumentException e) {
            log.error("Convert object to another object error", e);
            return null;
        }
    }

    /**
     * 判断字符串是否为有效的JSON
     *
     * @param jsonString JSON字符串
     * @return 是否为有效的JSON
     */
    public static boolean isValidJson(String jsonString) {
        if (jsonString == null || jsonString.isEmpty()) {
            return false;
        }
        try {
            OBJECT_MAPPER.readTree(jsonString);
            return true;
        } catch (IOException e) {
            return false;
        }
    }

    /**
     * 将逗号分隔的字符串转换为字符串列表
     *
     * @param str 逗号分隔的字符串，如："a,b,c"
     * @return 字符串列表，如：["a", "b", "c"]
     */
    public static List<String> splitToStringList(String str) {
        if (str == null || str.isEmpty()) {
            return Collections.emptyList();
        }
        return Arrays.stream(str.split(","))
                .map(String::trim)
                .filter(s -> !s.isEmpty())
                .collect(Collectors.toList());
    }

    /**
     * 将逗号分隔的字符串转换为指定类型的对象列表
     *
     * @param str   逗号分隔的字符串，如："1,2,3"
     * @param clazz 目标类型，如：Integer.class
     * @param <T>   目标类型
     * @return 目标类型列表，如：[1, 2, 3]
     */
    public static <T> List<T> splitToList(String str, Class<T> clazz) {
        if (str == null || str.isEmpty()) {
            return Collections.emptyList();
        }
        List<String> strList = splitToStringList(str);
        try {
            return strList.stream()
                    .map(item -> {
                        try {
                            return OBJECT_MAPPER.convertValue(item, clazz);
                        } catch (IllegalArgumentException e) {
                            log.error("Convert string to {} error, string: {}", clazz.getName(), item, e);
                            return null;
                        }
                    })
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("Split string to list error, string: {}, class: {}", str, clazz.getName(), e);
            return Collections.emptyList();
        }
    }

    /**
     * 从JSON字符串中获取指定字段的字符串值
     *
     * @param jsonString JSON字符串
     * @param key 字段名
     * @return 字符串值，如果字段不存在或不是字符串类型则返回null
     */
    public static String getString(String jsonString, String key) {
        if (jsonString == null || jsonString.isEmpty() || key == null) {
            return null;
        }
        try {
            JsonNode jsonNode = OBJECT_MAPPER.readTree(jsonString);
            if (jsonNode.has(key) && !jsonNode.get(key).isNull()) {
                JsonNode value = jsonNode.get(key);
                return value.isTextual() ? value.asText() : null;
            }
            return null;
        } catch (IOException e) {
            log.error("Get string from JSON error, jsonString: {}, key: {}", jsonString, key, e);
            return null;
        }
    }

    /**
     * 从JSON字符串中获取指定字段的布尔值
     *
     * @param jsonString JSON字符串
     * @param key 字段名
     * @return 布尔值，如果字段不存在或不是布尔类型则返回null
     */
    public static Boolean getBoolean(String jsonString, String key) {
        if (jsonString == null || jsonString.isEmpty() || key == null) {
            return null;
        }
        try {
            JsonNode jsonNode = OBJECT_MAPPER.readTree(jsonString);
            if (jsonNode.has(key) && !jsonNode.get(key).isNull()) {
                JsonNode value = jsonNode.get(key);
                return value.isBoolean() ? value.asBoolean() : null;
            }
            return null;
        } catch (IOException e) {
            log.error("Get boolean from JSON error, jsonString: {}, key: {}", jsonString, key, e);
            return null;
        }
    }

    /**
     * 从JSON字符串中获取指定字段的整数值
     *
     * @param jsonString JSON字符串
     * @param key 字段名
     * @return 整数值，如果字段不存在或不是数字类型则返回null
     */
    public static int getInt(String jsonString, String key) {
        if (jsonString == null || jsonString.isEmpty() || key == null) {
            return 0;
        }
        try {
            JsonNode jsonNode = OBJECT_MAPPER.readTree(jsonString);
            if (jsonNode.has(key) && !jsonNode.get(key).isNull()) {
                JsonNode value = jsonNode.get(key);
                return value.isInt() ? value.asInt() : 0;
            }
            return 0;
        } catch (IOException e) {
            log.error("Get integer from JSON error, jsonString: {}, key: {}", jsonString, key, e);
            return 0;
        }
    }

    /**
     * 从JSON字符串中获取指定字段的长整数值
     *
     * @param jsonString JSON字符串
     * @param key 字段名
     * @return 长整数值，如果字段不存在或不是数字类型则返回null
     */
    public static Long getLong(String jsonString, String key) {
        if (jsonString == null || jsonString.isEmpty() || key == null) {
            return null;
        }
        try {
            JsonNode jsonNode = OBJECT_MAPPER.readTree(jsonString);
            if (jsonNode.has(key) && !jsonNode.get(key).isNull()) {
                JsonNode value = jsonNode.get(key);
                return value.isNumber() ? value.asLong() : null;
            }
            return null;
        } catch (IOException e) {
            log.error("Get long from JSON error, jsonString: {}, key: {}", jsonString, key, e);
            return null;
        }
    }

    /**
     * 从JSON字符串中获取指定字段的浮点数值
     *
     * @param jsonString JSON字符串
     * @param key 字段名
     * @return 浮点数值，如果字段不存在或不是数字类型则返回null
     */
    public static Double getDouble(String jsonString, String key) {
        if (jsonString == null || jsonString.isEmpty() || key == null) {
            return null;
        }
        try {
            JsonNode jsonNode = OBJECT_MAPPER.readTree(jsonString);
            if (jsonNode.has(key) && !jsonNode.get(key).isNull()) {
                JsonNode value = jsonNode.get(key);
                return value.isNumber() ? value.asDouble() : null;
            }
            return null;
        } catch (IOException e) {
            log.error("Get double from JSON error, jsonString: {}, key: {}", jsonString, key, e);
            return null;
        }
    }

    /**
     * 从JSON字符串中获取指定字段的字符串数组
     *
     * @param jsonString JSON字符串
     * @param key 字段名
     * @return 字符串列表，如果字段不存在或不是数组类型则返回空列表
     */
    public static List<String> getStringList(String jsonString, String key) {
        if (jsonString == null || jsonString.isEmpty() || key == null) {
            return Collections.emptyList();
        }
        try {
            JsonNode jsonNode = OBJECT_MAPPER.readTree(jsonString);
            if (jsonNode.has(key) && jsonNode.get(key).isArray()) {
                List<String> result = new ArrayList<>();
                jsonNode.get(key).forEach(item -> {
                    if (item.isTextual()) {
                        result.add(item.asText());
                    }
                });
                return result;
            }
            return Collections.emptyList();
        } catch (IOException e) {
            log.error("Get string list from JSON error, jsonString: {}, key: {}", jsonString, key, e);
            return Collections.emptyList();
        }
    }

    /**
     * 从JSON字符串中获取指定字段的对象数组
     *
     * @param jsonString JSON字符串
     * @param key 字段名
     * @param clazz 数组元素类型
     * @param <T> 数组元素类型
     * @return 对象列表，如果字段不存在或不是数组类型则返回空列表
     */
    public static <T> List<T> getObjectList(String jsonString, String key, Class<T> clazz) {
        if (jsonString == null || jsonString.isEmpty() || key == null) {
            return Collections.emptyList();
        }
        try {
            JsonNode jsonNode = OBJECT_MAPPER.readTree(jsonString);
            if (jsonNode.has(key) && jsonNode.get(key).isArray()) {
                JavaType javaType = OBJECT_MAPPER.getTypeFactory().constructParametricType(List.class, clazz);
                return OBJECT_MAPPER.convertValue(jsonNode.get(key), javaType);
            }
            return Collections.emptyList();
        } catch (IOException | IllegalArgumentException e) {
            log.error("Get object list from JSON error, jsonString: {}, key: {}, class: {}",
                    jsonString, key, clazz.getName(), e);
            return Collections.emptyList();
        }
    }

    /**
     * 从JSON字符串中获取指定字段的子对象
     *
     * @param jsonString JSON字符串
     * @param key 字段名
     * @param clazz 对象类型
     * @param <T> 对象类型
     * @return 对象实例，如果字段不存在或不是对象类型则返回null
     */
    public static <T> T getObject(String jsonString, String key, Class<T> clazz) {
        if (jsonString == null || jsonString.isEmpty() || key == null) {
            return null;
        }
        try {
            JsonNode jsonNode = OBJECT_MAPPER.readTree(jsonString);
            if (jsonNode.has(key) && !jsonNode.get(key).isNull() && (jsonNode.get(key).isObject() || jsonNode.get(key).isValueNode())) {
                return OBJECT_MAPPER.convertValue(jsonNode.get(key), clazz);
            }
            return null;
        } catch (IOException | IllegalArgumentException e) {
            log.error("Get object from JSON error, jsonString: {}, key: {}, class: {}",
                    jsonString, key, clazz.getName(), e);
            return null;
        }
    }

    /**
     * 从JSON字符串中获取嵌套字段的值（通过路径访问）
     * 例如：getByPath(json, "user.address.city")
     *
     * @param jsonString JSON字符串
     * @param path 字段路径，使用点号分隔
     * @return JsonNode对象，如果路径不存在则返回null
     */
    public static JsonNode getByPath(String jsonString, String path) {
        if (jsonString == null || jsonString.isEmpty() || path == null) {
            return null;
        }
        try {
            JsonNode jsonNode = OBJECT_MAPPER.readTree(jsonString);
            String[] parts = path.split("\\.");
            for (String part : parts) {
                if (jsonNode == null || !jsonNode.has(part)) {
                    return null;
                }
                jsonNode = jsonNode.get(part);
            }
            return jsonNode;
        } catch (IOException e) {
            log.error("Get value by path from JSON error, jsonString: {}, path: {}", jsonString, path, e);
            return null;
        }
    }

    /**
     * 判断JSON字符串中是否存在指定字段
     *
     * @param jsonString JSON字符串
     * @param key 字段名
     * @return 是否存在该字段
     */
    public static boolean hasKey(String jsonString, String key) {
        if (jsonString == null || jsonString.isEmpty() || key == null) {
            return false;
        }
        try {
            JsonNode jsonNode = OBJECT_MAPPER.readTree(jsonString);
            return jsonNode.has(key);
        } catch (IOException e) {
            log.error("Check key existence in JSON error, jsonString: {}, key: {}", jsonString, key, e);
            return false;
        }
    }

    /**
     * 从字符串中提取JSON内容
     * 使用更健壮的方式提取完整的JSON对象
     *
     * @param input 包含JSON的字符串
     * @return JSON字符串，如果未找到则返回空字符串
     */
    public static String extractJsonContent(String input) {
        if (StringUtils.isBlank(input)) {
            return "";
        }

        int start = input.indexOf("{");
        if (start == -1) {
            return "";
        }

        int braceCount = 0;
        int end = -1;
        
        for (int i = start; i < input.length(); i++) {
            char c = input.charAt(i);
            if (c == '{') {
                braceCount++;
            } else if (c == '}') {
                braceCount--;
                if (braceCount == 0) {
                    end = i;
                    break;
                }
            }
        }

        if (end == -1 || end < start) {
            return "";
        }

        String jsonContent = input.substring(start, end + 1);
        try {
            // 验证提取的内容是否为有效的JSON
            JSON.parseObject(jsonContent);
            return jsonContent;
        } catch (Exception e) {
            log.error("Invalid JSON content extracted: {}", jsonContent, e);
            return "";
        }
    }

    /**
     * 将JSON字符串反序列化为Document对象列表
     * @param json JSON字符串
     * @return Document对象列表
     */
    public static List<Document> parseDocumentList(String json) {
        if (StringUtils.isBlank(json)) {
            return Collections.emptyList();
        }
        
        try {
            // 首先将JSON解析为JSONArray
            JSONArray jsonArray = JSON.parseArray(json);
            if (jsonArray == null || jsonArray.size() == 0) {
                return Collections.emptyList();
            }
            
            List<Document> documents = new ArrayList<>();
            for (int i = 0; i < jsonArray.size(); i++) {
                JSONObject jsonObject = jsonArray.getJSONObject(i);
                if (jsonObject == null) {
                    continue;
                }
                
                // 获取text和metadata字段
                String text = jsonObject.getString("text");
                JSONObject metadataJson = jsonObject.getJSONObject("metadata");
                
                // 确保text不为空
                if (StringUtils.isBlank(text)) {
                    continue;
                }
                
                // 转换metadata
                Map<String, Object> metadata = new HashMap<>();
                if (metadataJson != null) {
                    for (String key : metadataJson.keySet()) {
                        metadata.put(key, metadataJson.get(key));
                    }
                }
                
                // 创建Document对象
                Document document = new Document(text, metadata);
                documents.add(document);
            }
            
            return documents;
        } catch (Exception e) {
            log.error("Failed to parse Document list from JSON: {}", json, e);
            return Collections.emptyList();
        }
    }

    /**
     * 将JSON字符串反序列化为单个Document对象
     * @param json JSON字符串
     * @return Document对象，如果解析失败则返回null
     */
    public static Document parseDocument(String json) {
        if (StringUtils.isBlank(json)) {
            return null;
        }
        
        try {
            JSONObject jsonObject = JSON.parseObject(json);
            if (jsonObject == null) {
                return null;
            }
            
            String text = jsonObject.getString("text");
            JSONObject metadataJson = jsonObject.getJSONObject("metadata");
            
            if (StringUtils.isBlank(text)) {
                return null;
            }
            
            Map<String, Object> metadata = new HashMap<>();
            if (metadataJson != null) {
                for (String key : metadataJson.keySet()) {
                    metadata.put(key, metadataJson.get(key));
                }
            }
            
            return new Document(text, metadata);
        } catch (Exception e) {
            log.error("Failed to parse Document from JSON: {}", json, e);
            return null;
        }
    }

    /**
     * 从JSONObject中安全地获取字符串值
     * 如果key不存在或值为null，返回默认值
     *
     * @param obj JSONObject对象
     * @param key 键名
     * @param defaultValue 默认值
     * @return 字符串值
     */
    public static String getStringSafely(JSONObject obj, String key, String defaultValue) {
        try {
            if (!obj.containsKey(key)) {
                return defaultValue;
            }
            Object value = obj.get(key);
            return value != null ? String.valueOf(value) : defaultValue;
        } catch (Exception e) {
            return defaultValue;
        }
    }
}