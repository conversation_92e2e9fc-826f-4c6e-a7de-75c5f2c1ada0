package com.sankuai.dzhealth.ai.service.domain.evaluation;

import com.alibaba.fastjson.JSON;
import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.dianping.haima.entity.haima.HaimaContent;
import com.dianping.rhino.Rhino;
import com.dianping.rhino.threadpool.DefaultThreadPoolProperties;
import com.dianping.rhino.threadpool.ThreadPool;
import com.meituan.mdp.boot.starter.config.annotation.MdpConfig;
import com.sankuai.dzhealth.ai.service.api.ESVectorStoreService;
import com.sankuai.dzhealth.ai.service.api.RemoteResponse;
import com.sankuai.dzhealth.ai.service.domain.evaluation.config.EvaluationConfig;
import com.sankuai.dzhealth.ai.service.domain.evaluation.config.EvaluationConfigManager;
import com.sankuai.dzhealth.ai.service.domain.evaluation.entity.RagEvaluationRequest;
import com.sankuai.dzhealth.ai.service.domain.evaluation.entity.RagEvaluationResponse;
import com.sankuai.dzhealth.ai.service.domain.evaluation.strategy.LLMEvaluationStrategy;
import com.sankuai.dzhealth.ai.service.domain.service.thinking.ThinkingEngineService;
import com.sankuai.dzhealth.ai.service.dto.DocumentDTO;
import com.sankuai.dzhealth.ai.service.dto.SseAwareException;
import com.sankuai.dzhealth.ai.service.enums.BizSceneEnum;
import com.sankuai.dzhealth.ai.service.enums.MetadataKeyEnum;
import com.sankuai.dzhealth.ai.service.enums.StreamEventErrorTypeEnum;
import com.sankuai.dzhealth.ai.service.infrastructure.acl.HaimaAcl;
import com.sankuai.dzhealth.ai.service.infrastructure.acl.ShopAcl;
import com.sankuai.dzhealth.ai.service.infrastructure.dal.entity.ChatConversationEntity;
import com.sankuai.dzhealth.ai.service.infrastructure.dal.mapper.RagEvaluationResponseEntityMapper;
import com.sankuai.dzhealth.ai.service.infrastructure.repository.conversation.ChatMessageRepository;
import com.sankuai.dzhealth.ai.service.infrastructure.repository.deepsearch.DeepSearchResultRepository;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;

/**
 * 评价上下文类，管理所有策略
 */
@Component
@Slf4j
public class EvaluationService {

    @Autowired
    private final LLMEvaluationStrategy lLMEvaluationStrategy;

    @Autowired
    private EvaluationRepository evaluationRepository;

    @Autowired
    private EvaluationConfigManager evaluationConfigManager;

    @Autowired
    private ESVectorStoreService esVectorStoreService;

    @Autowired
    private DeepSearchResultRepository deepSearchResultRepository;

    @Autowired
    private HaimaAcl haimaAcl;

    @Autowired
    private ChatMessageRepository chatMessageRepository;

    @Resource
    private RagEvaluationResponseEntityMapper ragEvaluationResponseEntityMapper;

    @MdpConfig("public_hospital.evaluation.hallucination_threshold:20")
    private Float hallucinationThreshold = 20.0f;

    @MdpConfig("public_hospital.search.sites:[\"www.dayi.org.cn\",\"www.zhihu.com\",\"www.bohe.cn\",\"www.fh21.com.cn\",\"www.familydoctor.com.cn\",\"www.haodf.com\",\"m.youlai.cn\",\"mp.weixin.qq.com\",\"baijiahao.baidu.com\"]")
    private String searchSites;

    @Autowired
    private ShopAcl shopAcl;

    @Autowired
    private ThinkingEngineService thinkingEngineService;

    public static final ThreadPool EVALUATION_POOL = Rhino.newThreadPool("EVALUATION_POOL",
            DefaultThreadPoolProperties.Setter().withCoreSize(100).withMaxSize(200).withMaxQueueSize(1000));

    public EvaluationService(LLMEvaluationStrategy lLMEvaluationStrategy) {
        this.lLMEvaluationStrategy = lLMEvaluationStrategy;
    }

    public void executeStrategies(RagEvaluationRequest request) {
        Transaction transaction = Cat.newTransaction("Evaluation", "executeStrategies");
        log.info("开始执行评价策略");
        try {
            List<EvaluationConfig> evaluationConfigs = evaluationConfigManager.getAllEvaluationConfigs();
            if (evaluationConfigs.isEmpty()) {
                log.error("未找到有效的评价配置");
                throw new SseAwareException(StreamEventErrorTypeEnum.SERVER_ERROR);
            }

            for (EvaluationConfig config : evaluationConfigs) {
                log.info("开始执行{}评价", config.getEvaluationType());

                String systemPrompt = config.getSystemPrompt();
                String userPrompt = config.getUserPrompt();

                try {
                    lLMEvaluationStrategy.execute(request, systemPrompt, userPrompt, EvaluationService.EVALUATION_POOL)
                            .thenApply(this::persistEvaluationResult)
                            .thenAccept(responses -> {
                                // 只对chat场景的请求进行幻觉检测和深度搜索
                                if ("chat".equals(request.getModelScene())) {
                                    checkAndHandleHallucination(responses,request);
                                }
                            })
                            .exceptionally(e -> {
                                log.error("Error in strategy execution chain", e);
                                Cat.logError("StrategyExecutionChainError", e);
                                return null;
                            });
                    log.info("{}评价策略执行成功", config.getEvaluationType());
                } catch (Exception e) {
                    log.error("执行{}评价策略时出错", config.getEvaluationType(), e);
                    Cat.logError("执行{}评价策略时出错", e);
                }
            }

            log.info("评价策略执行成功");
            transaction.setSuccessStatus();
        } catch (Exception e) {
            log.error("评价策略执行失败", e);
            Cat.logError("评价策略执行失败", e);
            transaction.setStatus(e);
        } finally {
            transaction.complete();
        }
    }

    private List<RagEvaluationResponse> persistEvaluationResult(List<RagEvaluationResponse> result) {
        try {
            evaluationRepository.insertEvaluations(result);
            log.info("EvaluationInfo persisted successfully: {}", result);
        } catch (Exception e) {
            log.error("Error persisting EvaluationInfo", e);
            Cat.logError("EvaluationInfo PersistenceError", e);
        }
        return result;
    }

    /**
     * 检查评测结果并处理幻觉
     */
    private void checkAndHandleHallucination(List<RagEvaluationResponse> responses,RagEvaluationRequest request) {
        if (CollectionUtils.isEmpty(responses)) {
            return;
        }

        for (RagEvaluationResponse response : responses) {
            if (isHallucinationDetected(response)) {
                log.info("检测到chat场景的幻觉，触发深度搜索，sessionId={}, msgId={}", response.getSessionId(), response.getMsgId());
                triggerDeepSearch(response,request);
            }
        }
    }

    /**
     * 检查评测结果是否存在幻觉
     */
    private boolean isHallucinationDetected(RagEvaluationResponse response) {
        if ("幻觉分数".equals(response.getDescription())) {
            // 幻觉分数大于阈值认为存在幻觉
            return response.getScore() > hallucinationThreshold;
        }
        return false;
    }

    /**
     * 触发深度搜索并存储结果
     */
    private void triggerDeepSearch(RagEvaluationResponse response,RagEvaluationRequest request) {
        Long sessionId = response.getSessionId();
        Long msgId = response.getMsgId();
        // 从answer_context获取用户问题
        String query = getQuestion(request);
        if (StringUtils.isBlank(query)) {
            log.warn("无法获取原始问题，取消深度搜索, sessionId={}, msgId={}", sessionId, msgId);
            return;
        }

        // 构建搜索查询信息，添加医院简称和更新搜索站点
        SearchQueryInfo queryInfo = buildSearchQueryInfo(sessionId, query, request);
        List<String> sites = JSON.parseArray(searchSites, String.class);
        ;
        String shopId = queryInfo.getShopId(); // 获取shopId用于后续存储

        // 异步执行深度搜索
        CompletableFuture.supplyAsync(() -> {
            Transaction transaction = Cat.newTransaction("EvaluationService", "deepSearch");
            try {
                log.info("开始执行深度搜索: {}, 搜索站点: {}", query, sites);

                // 构建业务参数
                Map<String, Object> businessParams = new HashMap<>();
                businessParams.put("bizSource", "chat_evaluation");
                businessParams.put("sessionId", sessionId);
                businessParams.put("msgId", msgId);
                businessParams.put("sites", sites);
                // 创建思考会话
                Long thinkingSessionId = thinkingEngineService.createThinkingSession(query, businessParams);
                if (thinkingSessionId == null) {
                    log.error("创建思考会话失败, sessionId={}, msgId={}", sessionId, msgId);
                    return null;
                }
                // 执行思考过程，设置超时时间
                Map<String, Object> result = thinkingEngineService.executeThinking(thinkingSessionId);
                if (result == null) {
                    log.error("执行思考过程失败, thinkingSessionId={}", thinkingSessionId);
                    return null;
                }
                // 处理结果
                if (result.containsKey("finalAnswer")) {
                    String answer = (String) result.get("finalAnswer");
                    List<String> sourceUrls = (List<String>) result.getOrDefault("sourceUrls", Collections.emptyList());
                    BigDecimal confidenceScore = new BigDecimal(String.valueOf(result.getOrDefault("confidenceScore", "8.0")));
                    // 存储到数据库
                    deepSearchResultRepository.save(
                            BizSceneEnum.PUBLIC_HOSPITAL.getBizScene(),
                            query,
                            answer,
                            sourceUrls,
                            confidenceScore);
                    // 存储到RAG向量库
                    saveToVectorStore(query, answer, sourceUrls, shopId);
                    log.info("深度搜索结果已保存，sessionId={}, msgId={}, thinkingSessionId={}", sessionId, msgId, thinkingSessionId);
                } else {
                    log.warn("深度搜索未返回有效结果，sessionId={}, msgId={}, thinkingSessionId={}", sessionId, msgId, thinkingSessionId);
                }
                transaction.setSuccessStatus();
                return result;
            } catch (Exception e) {
                transaction.setStatus(e);
                log.error("执行深度搜索异常, sessionId={}, msgId={}", sessionId, msgId, e);
                return null;
            } finally {
                transaction.complete();
            }
        }, EVALUATION_POOL.getExecutor());
    }

    /**
     * 构建搜索查询信息
     */
    private SearchQueryInfo buildSearchQueryInfo(Long sessionId, String userQuery, RagEvaluationRequest request) {
        String shopId = null;
        List<String> sites = JSON.parseArray(searchSites, String.class);
        List<String> updatedSites = new ArrayList<>(sites);

        // 优先从RagEvaluationRequest的metadata中获取shopId
        if (request != null && request.getMetadata() != null) {
            Object shopIdObj = request.getMetadata().get(MetadataKeyEnum.SHOP_ID.getKey());
            if (shopIdObj instanceof String && StringUtils.isNotBlank((String) shopIdObj)) {
                shopId = (String) shopIdObj;
                log.info("从RagEvaluationRequest.metadata中获取到shopId: {}", shopId);
            } else if (shopIdObj instanceof Number) {
                shopId = String.valueOf(shopIdObj);
                log.info("从RagEvaluationRequest.metadata中获取到shopId: {}", shopId);
            }
        }

        // 如果metadata中没有shopId，使用原有的数据库查询方式作为兜底
        if (StringUtils.isBlank(shopId)) {
            try {
                // 直接通过sessionId获取会话实体
                List<ChatConversationEntity> conversations = chatMessageRepository.findConversationById(sessionId);

                // 从会话实体中获取businessId作为shopId
                if (CollectionUtils.isNotEmpty(conversations)) {
                    shopId = conversations.get(0).getBusinessId();
                    log.info("从会话实体中获取到shopId（兜底方式）: {}", shopId);
                }
            } catch (Exception e) {
                log.error("获取医院信息失败", e);
            }
        }

        // rewriteText（userQuery）已在意图识别阶段加入医院信息（若需要），无需再次处理
        String query = userQuery;
        return new SearchQueryInfo(query, updatedSites, shopId);
    }

    /**
     * 获取医院名称，优先从海马配置获取，兜底使用ShopAcl
     */
    private String getHospitalName(String shopId, List<String> updatedSites) {
        try {
            // 优先从海马配置获取
            Map<String, String> fields = new HashMap<>();
            fields.put("mtShopId", shopId);

            List<HaimaContent> hospitalBriefs = haimaAcl.getContent("public_hospital_brief", fields);
            if (CollectionUtils.isNotEmpty(hospitalBriefs)) {
                HaimaContent content = hospitalBriefs.get(0);
                String simpleName = content.getContentString("simpleName");

                // 如果有URL，添加到搜索站点
                if (StringUtils.isNotBlank(content.getContentString("url"))) {
                    updatedSites.add(content.getContentString("url"));
                }

                if (StringUtils.isNotBlank(simpleName)) {
                    return simpleName;
                }
            }

            // 海马配置没有医院简称，使用ShopAcl作为兜底
            String hospitalName = shopAcl.getHospitalName(Long.valueOf(shopId));
            if (StringUtils.isNotBlank(hospitalName)) {
                log.info("深度搜索通过ShopAcl兜底获取到医院名称: shopId={}, name={}", shopId, hospitalName);
                return hospitalName;
            } else {
                log.warn("深度搜索ShopAcl兜底也未获取到医院名称: shopId={}", shopId);
            }
        } catch (Exception e) {
            log.error("深度搜索获取医院名称失败: shopId={}", shopId, e);
        }

        return null;
    }

    /**
     * 判断意图是否需要医院名称
     */
    private boolean shouldIncludeHospitalName(String intentionKey) {
        // 需要医院上下文的意图：分诊、服务可及性确认、就诊指南
        return "triage".equals(intentionKey) ||
               "service".equals(intentionKey) ||
               "guide".equals(intentionKey);
    }


    /**
     * 搜索查询信息类
     */
    @Data
    private static class SearchQueryInfo {
        private final String query;
        private final List<String> sites;
        private final String shopId;

        public SearchQueryInfo(String query, List<String> sites, String shopId) {
            this.query = query;
            this.sites = sites;
            this.shopId = shopId;
        }
    }

    /**
     * 保存结果到向量存储
     */
    private void saveToVectorStore(String query, String answer, List<String> sourceUrls, String shopId) {
        try {
            DocumentDTO doc = new DocumentDTO();
            doc.setId(UUID.randomUUID().toString());
            // 构造格式化文本
            String formattedText = String.format("问题：%s\n答案：%s", query, answer);
            doc.setText(formattedText);
            // 设置元数据
            Map<String, String> metadata = new HashMap<>();
            metadata.put(MetadataKeyEnum.CHANNEL.getKey(), "deep_search_result");

            // 添加医院ID用于后续ES查询
            if (StringUtils.isNotBlank(shopId)) {
                metadata.put(MetadataKeyEnum.SHOP_ID.getKey(), shopId);
            }

            // 处理URL
            if (!sourceUrls.isEmpty()) {
                metadata.put("url", sourceUrls.get(0));
                metadata.put("urls", JSON.toJSONString(sourceUrls));
            }
            doc.setMetadata(metadata);
            // 保存到向量库
            List<DocumentDTO> documents = Collections.singletonList(doc);
            RemoteResponse<Boolean> response = esVectorStoreService.buildIndex(documents);
            if (response.getSuccess() && Boolean.TRUE.equals(response.getData())) {
                log.info("深度搜索结果已保存到向量库，shopId: {}", shopId);
            } else {
                log.warn("保存深度搜索结果到向量库失败: {}", response.getMsg());
            }
        } catch (Exception e) {
            log.error("保存深度搜索结果到向量库失败", e);
        }
    }

    /**
     * 获取原始用户查询
     */
    private String getQuestion(RagEvaluationRequest request) {
        String rewriteText = Optional.ofNullable(request)
                .map(RagEvaluationRequest::getMetadata)
                .map(metadata -> metadata.get(MetadataKeyEnum.REWRITE_TEXT.getKey()))
                .filter(String.class::isInstance)
                .map(String.class::cast)
                .filter(StringUtils::isNotBlank)
                .orElse(null);

        if (rewriteText != null) {
            log.info("从RagEvaluationRequest.metadata中获取到rewriteText: {}", rewriteText);
            return rewriteText;
        }

        log.warn("未获取到rewriteText，深度搜索query为null");
        return null;
    }
}
