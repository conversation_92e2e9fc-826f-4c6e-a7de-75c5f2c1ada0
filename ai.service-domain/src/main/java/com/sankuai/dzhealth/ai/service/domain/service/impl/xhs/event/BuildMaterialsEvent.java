package com.sankuai.dzhealth.ai.service.domain.service.impl.xhs.event;

import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.sankuai.dzhealth.ai.service.api.ESVectorStoreService;
import com.sankuai.dzhealth.ai.service.api.RemoteResponse;
import com.sankuai.dzhealth.ai.service.domain.entity.xhs.XhsBuildNoteResult;
import com.sankuai.dzhealth.ai.service.domain.entity.xhs.XhsNoteRequest;
import com.sankuai.dzhealth.ai.service.domain.prompt.XhsBuildNotePrompt;
import com.sankuai.dzhealth.ai.service.domain.prompt.XhsNoteCoverPrompt;
import com.sankuai.dzhealth.ai.service.domain.prompt.XhsTemplateNotePrompt;
import com.sankuai.dzhealth.ai.service.dto.DocumentDTO;
import com.sankuai.dzhealth.ai.service.infrastructure.utils.StrTools;
import com.sankuai.dzhealth.ai.service.request.DocumentSearchRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Component
public class BuildMaterialsEvent {

    @Resource
    private ESVectorStoreService esVectorStoreService;

    @Resource
    private ChatClient xhsNoteLLMClient;

    @Resource
    private XhsBuildNotePrompt xhsBuildNotePrompt;

    /**
     * 获取热门笔记
     *
     * @param tcsd         BuildNoteRequest.TCSD
     * @param hotNoteBatch String
     * @return List<String>
     */
    public List<String> getHotNotes(XhsNoteRequest.TCSD tcsd, String hotNoteBatch) {
        String query = tcsd.join();
        Map<String, List<String>> metaData = new ImmutableMap.Builder<String, List<String>>()
                .put("hotNoteStatus.keyword", Lists.newArrayList("1"))
                .put("hotNoteBatchStr.keyword", Lists.newArrayList(hotNoteBatch)).build();
        DocumentSearchRequest documentSearchRequest = DocumentSearchRequest.builder().query(query).topK(100).metaData(metaData).build();
        RemoteResponse<List<DocumentDTO>> listRemoteResponse = esVectorStoreService.similaritySearch(documentSearchRequest);
        if (listRemoteResponse.getSuccess()) {
            return listRemoteResponse.getData().stream().map(DocumentDTO::getText).filter(StringUtils::isNotBlank).toList();
        }

        return Lists.newArrayList();
    }

    /**
     * 获取知识库
     *
     * @param tcsd  BuildNoteRequest.TCSD
     * @return String
     */
    public String getKnowledge(XhsNoteRequest.TCSD tcsd) {
        String query = tcsd.join();

        DocumentSearchRequest documentSearchRequest = DocumentSearchRequest.builder().query(query)
                .topK(100).metaData(new ImmutableMap.Builder<String, List<String>>()
                        .put("channel.keyword", Lists.newArrayList("mart_general_beauty.medical_guide", "mart_general_beauty.medical_baike"))
                        .build()).build();
        RemoteResponse<List<DocumentDTO>> listRemoteResponse = esVectorStoreService.similaritySearch(documentSearchRequest);
        if (listRemoteResponse.getSuccess()) {
            return listRemoteResponse.getData().stream().map(DocumentDTO::getText).collect(Collectors.joining("\n")).trim();
        }

        return "";
    }

    /**
     * 构建模板
     *
     * @param hotNotes List<String>
     * @return String
     */
    public String buildTemplate(List<String> hotNotes) {

        // 构建prompt
        String prompt = XhsTemplateNotePrompt.builder().hotNotes(hotNotes.stream().filter(StringUtils::isNotBlank).collect(Collectors.joining("\n\n"))).build();
        // call 大模型生成模板
        String result = xhsNoteLLMClient.prompt().user(prompt).call().content();
        result = StrTools.trimMKjson(result);
        return result;
    }

    /**
     * 构建笔记prompt
     *
     * @param tcsd      BuildNoteRequest.TCSD
     * @param hotNotes  List<String>
     * @param template  String
     * @param knowledge String
     * @return String
     */
    public String buildNotePrompt(XhsNoteRequest.TCSD tcsd, List<String> hotNotes, String template, String knowledge, Long id) {
        return xhsBuildNotePrompt.builder()
                // 方向
                .noteType(Lists.newArrayList(tcsd.getTheme()))
                // 品类
                .product(Lists.newArrayList(tcsd.getCategory()))
                // 场景
                .scene(Lists.newArrayList(tcsd.getScene()))
                // 人群
                .targetAudience(Lists.newArrayList(tcsd.getDroves()))
                // 热门笔记
                .hotNotes(hotNotes)
                // 模版
                .template(template)
                // 知识库
                .knowledge(knowledge).build(xhsBuildNotePrompt, id);
    }

    public String buildNoteCoverPrompt(XhsBuildNoteResult.Note note, String knowledge) {
        return XhsNoteCoverPrompt.builder().title(note.strTitle()).content(note.getContent()).knowledge(knowledge).build();
    }

}
