package com.sankuai.dzhealth.ai.service.domain.finetune;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: yangweicheng
 * @date: 2025/5/7 15:08
 * @version: 1.0
 */

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class FineTuneData {

    /**
     * 字段: id
     * 说明: 主键
     */
    private Long id;

    /**
     * 字段: session_id
     * 说明: 会话ID
     */
    private Long sessionId;

    /**
     * 字段: msg_id
     * 说明: 消息ID
     */
    private Long msgId;

    /**
     * 字段: data_key
     * 说明: 评估项标识
     */
    private String dataKey;

    /**
     * 字段: description
     * 说明: 评估项描述
     */
    private String description;

    /**
     * 字段: success
     * 说明: 是否成功(1:成功,0:失败)
     */
    private Boolean success;

    /**
     * 字段: feedback
     * 说明: 评估反馈
     */
    private String feedback;

    /**
     * 字段: metadata
     * 说明: 元数据(JSON格式)
     */
    private String metadata;
}
