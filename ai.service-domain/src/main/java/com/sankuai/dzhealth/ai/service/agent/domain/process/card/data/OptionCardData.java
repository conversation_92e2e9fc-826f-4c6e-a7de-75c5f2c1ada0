package com.sankuai.dzhealth.ai.service.agent.domain.process.card.data;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @author:chenwei
 * @time: 2025/7/14 14:43
 * @version: 0.0.1
 */

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OptionCardData {

    private String questionText;

    private String filterType;

    private String extraButtonText;

    private List<Option> options;


    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class Option {

        private String imageUrl;

        private String text;

        private String desc;
    }


}
