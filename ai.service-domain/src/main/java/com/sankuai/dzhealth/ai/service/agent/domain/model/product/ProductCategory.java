package com.sankuai.dzhealth.ai.service.agent.domain.model.product;

import lombok.Builder;
import lombok.Data;

/**
 * 商品类目信息 - Value Object
 * 统一封装不同商品体系的类目字段
 */
@Data
@Builder
public class ProductCategory {

    /**
     * 业务分类ID（二级类目）
     * 注意：团购和泛商品的categoryId可能碰撞，需要结合商品体系区分
     */
    private Long categoryId;

    /**
     * 平台类目ID
     */
    private Long platformCategoryId;

    /**
     * 服务类型（主要用于团单商品的末级分类）
     */
    private String serviceType;

    /**
     * 服务类型ID（主要用于团单商品的末级分类）
     */
    private Long serviceTypeId;

    /**
     * 三级类目ID（从扩展属性category3中获取，主要用于泛商品）
     */
    private String thirdCategoryId;

    /**
     * 四级类目ID（从扩展属性category4中获取，主要用于泛商品）
     */
    private String fourthCategoryId;

    /**
     * 五级类目ID（从扩展属性category5中获取，主要用于泛商品）
     */
    private String fifthCategoryId;

    /**
     * 获取最精确的类目ID
     * 优先级：三级类目 > 服务类型ID > 二级类目ID
     */
    public String getMostPreciseCategoryId() {
        if (thirdCategoryId != null && !thirdCategoryId.trim().isEmpty()) {
            return thirdCategoryId;
        }
        if (serviceTypeId != null) {
            return String.valueOf(serviceTypeId);
        }
        if (categoryId != null) {
            return String.valueOf(categoryId);
        }
        return null;
    }
}

