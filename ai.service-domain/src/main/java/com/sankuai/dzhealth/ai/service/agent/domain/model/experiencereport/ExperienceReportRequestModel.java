package com.sankuai.dzhealth.ai.service.agent.domain.model.experiencereport;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 体验报告请求领域模型
 *
 * <AUTHOR>
 * @version 0.0.1
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ExperienceReportRequestModel implements Serializable {

    /**
     * 平台
     */
    private Integer platform;

    /**
     * 城市ID
     */
    private Integer cityId;

    /**
     * 商户ID
     */
    private Long shopId;

    /**
     * 商品ID
     */
    private Long productId;

    /**
     * 起始页
     */
    private Integer offset;

    /**
     * 每页大小
     */
    private Integer limit;

    /**
     * 设备ID
     */
    private String deviceId;

    /**
     * 是否站内
     */
    private Boolean inApp;

    /**
     * 微详情页类型
     */
    private Integer type;

    /**
     * 手艺人ID
     */
    private Integer techId;

    /**
     * 手艺人ID列表
     */
    private List<Integer> techIds;

    /**
     * 搜索词
     */
    private String searchWord;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 纬度
     */
    private Double lat;

    /**
     * 经度
     */
    private Double lng;

    /**
     * 筛选标签ID列表
     */
    private List<Long> filterTagIds;

    /**
     * AI导购推荐标签ID列表
     */
    private List<Long> aiRecommendTags;

    /**
     * 融合医生ID
     */
    private Long mergeDoctorId;

    /**
     * 融合医生ID列表
     */
    private List<Long> mergeDoctorIds;

    /**
     * 体验报告ID列表
     */
    private List<Long> reportIds;

    /**
     * 页数
     */
    private Integer pageNo;
}

