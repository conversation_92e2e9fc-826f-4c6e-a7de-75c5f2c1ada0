package com.sankuai.dzhealth.ai.service.agent.domain.process.card;

import com.sankuai.dzhealth.ai.service.agent.domain.context.MessageContext;
import com.sankuai.dzhealth.ai.service.agent.enums.ContextExtraKey;
import com.sankuai.dzhealth.ai.service.domain.utils.JsonUtils;
import com.sankuai.dzhealth.ai.service.dto.SseAwareException;
import com.sankuai.dzhealth.ai.service.dto.stream.StreamEventCardDataDTO;
import com.sankuai.dzhealth.ai.service.enums.StreamEventCardTypeEnum;
import com.sankuai.dzhealth.ai.service.enums.StreamEventErrorTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

/**
 * @author:chenwei
 * @time: 2025/7/19 17:41
 * @version: 0.0.1
 */

@Service
@Slf4j
public class TitleCardBuilder implements CardBuilder {
    @Override
    public boolean accept(String cardType) {
        return StreamEventCardTypeEnum.RICH_TEXT_TITLE_CARD.getType().equals(cardType);
    }

    @Override
    public void padding(StreamEventCardDataDTO streamEventCardDataDTO, MessageContext messageContext) {

        String key  = streamEventCardDataDTO.getKey();

        int index = key.indexOf(":");

        if (index < 0) {
            throw new SseAwareException(StreamEventErrorTypeEnum.SERVER_ERROR);
        }

        String supplyId  = key.substring(0, index);

        String title  = key.substring(index + 1);

        log.info("query={},padding_supplyId={},title={},key={}", messageContext.getMsg(), supplyId, title, key);
        Map<String, Serializable> extra = messageContext.getExtra();

        // 确保extra不为null
        if (extra == null) {
            extra = new HashMap<>();
            messageContext.setExtra(extra);
        }

        String suppId2MapJson = (String) extra.get(ContextExtraKey.SUPPLY_ID_TITLE.getKey());
        Map<String, Object> supplyId2Map = suppId2MapJson != null ?
                new HashMap<>(JsonUtils.parseMap(suppId2MapJson)) : new HashMap<>();
        supplyId2Map.put(supplyId, title);

        extra.put(ContextExtraKey.SUPPLY_ID_TITLE.getKey(), JsonUtils.toJsonString(supplyId2Map));


        Map<String, Object> cardProps = streamEventCardDataDTO.getCardProps();
        if (supplyId.contains("A")) {
            cardProps.put("title", title);
        } else {
            cardProps.put("title", key);
        }
        cardProps.put("imageUrl", "https://p0.meituan.net/ingee/a567250dcec22524bff773330b672d791599.png");

        streamEventCardDataDTO.setCardProps(cardProps);

    }

    @Override
    public String parseCardProps(StreamEventCardDataDTO streamEventCardDataDTO) {

        StringBuilder sb = new StringBuilder("标题");
        String key  = streamEventCardDataDTO.getKey();

        int index = key.indexOf(":");

        if (index < 0) {
            sb.append(streamEventCardDataDTO.getKey()).append("\n");
            return sb.toString();
        }
        String title  = key.substring(index + 1);
        sb.append(title).append("\n");
        return sb.toString();
    }
}
