package com.sankuai.dzhealth.ai.service.domain.card.handlers;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.sankuai.dzhealth.ai.service.domain.card.AbstractCardHandler;
import com.sankuai.dzhealth.ai.service.domain.utils.context.AiAnswerContext;
import com.sankuai.dzhealth.ai.service.dto.stream.StreamEventDTO;
import com.sankuai.dzhealth.ai.service.enums.StreamEventCardTypeEnum;
import com.sankuai.dzhealth.ai.service.infrastructure.observability.Span;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 追加问题卡片处理器 - 最高优先级，因为如果有追加问题需要立即返回
 */
@Slf4j
@Component
@Order(2)
public class ContinueAskCardHandler extends AbstractCardHandler {
    
    // 这个标志将由ChatSessionService检查，如果为true，则提前结束流
    private ThreadLocal<Boolean> shouldCompleteEarly = new ThreadLocal<>();
    
    @Override
    public boolean supports(String fieldName) {
        return "continue_ask".equals(fieldName);
    }
    
    @Override
    public int getOrder() {
        return 2;
    }
    
    /**
     * 检查是否应该提前完成(用于ChatSessionService检查)
     */
    public boolean shouldCompleteEarly() {
        Boolean value = shouldCompleteEarly.get();
        // 获取后重置，避免影响下一次调用
        shouldCompleteEarly.remove();
        return Boolean.TRUE.equals(value);
    }
    
    @Override
    public List<StreamEventDTO> handle(JSONObject jsonObject, 
                                    AiAnswerContext context, 
                                    SseEmitter sseEmitter, 
                                    AtomicInteger sseIndex,
                                    long startChatTime) {
        String continueAsk = jsonObject.getString("continue_ask");
        if (StringUtils.isNotBlank(continueAsk)) {
            JSONObject continueAskJson = JSON.parseObject(continueAsk);
            String singleSelect = continueAskJson.getString("single_select");
            String choiceTitle = continueAskJson.getString("choice_title");
            List<String> choiceTab = continueAskJson.getJSONArray("choice_tab") == null ? 
                    Lists.newArrayList() : 
                    continueAskJson.getJSONArray("choice_tab").toJavaList(String.class);
            
            if (StringUtils.isNotBlank(choiceTitle) && CollectionUtils.isNotEmpty(choiceTab)) {
                Map<String, Object> cardProps = new HashMap<>();
                cardProps.put("selectionMode", singleSelect.equals("true") ? 0 : 1);
                cardProps.put("text", choiceTitle);
                cardProps.put("tab", choiceTab);
                cardProps.put("inputSource", 2);
                
                log.info("continueAsk_duration={}", (System.currentTimeMillis() - startChatTime));
                context.addSpan(Span.builder()
                        .key("continueAsk")
                        .duration(System.currentTimeMillis() - startChatTime)
                        .build());
                
                StreamEventDTO continueAskEvent = sendCardEvent(
                        StreamEventCardTypeEnum.TAB_CARD,
                        "continueAsk", 
                        cardProps, 
                        sseEmitter, 
                        sseIndex.getAndIncrement()
                );
                
                return singletonEvent(continueAskEvent);
            }
        }
        
        return emptyEvents();
    }
} 