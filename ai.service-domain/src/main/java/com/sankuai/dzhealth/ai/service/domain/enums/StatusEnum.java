package com.sankuai.dzhealth.ai.service.domain.enums;

import lombok.Getter;

/**
 * @author:chenwei
 * @time: 2025/3/19 19:27
 * @version: 0.0.1
 */
@Getter
public enum StatusEnum {

    /**
     * 成功
     */
    SUCCESS(0, "success"),
    /**
     * 会话不存在
     */
    SESSION_NOT_EXISTS(1001, "会话不存在"),
    /**
     * 会话已结束
     */
    SESSION_END(1002, "会话已结束");



    ;
    private int code;
    private String msg;

    StatusEnum(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }
}
