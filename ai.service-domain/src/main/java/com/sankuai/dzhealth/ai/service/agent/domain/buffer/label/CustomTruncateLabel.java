package com.sankuai.dzhealth.ai.service.agent.domain.buffer.label;

import com.meituan.mdp.boot.starter.util.Pair;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @author:chenwei
 * @time: 2025/7/7 16:35
 * @version: 0.0.1
 */


public class CustomTruncateLabel implements TruncateLabel {

    private static final String IDENTIFIER = "<";
    private static final Pattern LABEL_PATTERN = Pattern.compile("<([\\w-,;:]+)>(.*?)</\\1>",Pattern.DOTALL);
    
    @Override
    public boolean acceptTruncate(String data) {
        if (StringUtils.isBlank(data)) {
            return false;
        }
        return data.contains(IDENTIFIER);
    }

    @Override
    public List<Pair<String, String>> parseLabels(String data) {
        if (data == null) {
            return List.of();
        }
        
        List<Pair<String, String>> labels = new ArrayList<>();
        Matcher matcher = LABEL_PATTERN.matcher(data);
        
        while (matcher.find()) {
            String tagName = matcher.group(1);    // 标签名，如 "div"
            String content = matcher.group(2);    // 内容，如 "content1"
            labels.add(new Pair<>(tagName, content));
        }
        
        return labels;
    }

    @Override
    public String getIdentifier() {
        return IDENTIFIER;
    }

    @Override
    public String getContent(Pair<String, String> label) {
        return  "<" + (label.getKey()) + ">" + label.getValue() + "</" + (label.getKey()) + ">";
    }
}
