package com.sankuai.dzhealth.ai.service.domain.entity.xhs;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @author:chenwei
 * @time: 2025/6/23 19:46
 * @version: 0.0.1
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class Direction implements Serializable {

    private String project;

    private String product;

    private String scene;

    private String person;

    @Override
    public String toString() {
        return String.join(",",
            project != null ? project : "",
            product != null ? product : "",
            scene != null ? scene : "",
            person != null ? person : ""
        );
    }
}
