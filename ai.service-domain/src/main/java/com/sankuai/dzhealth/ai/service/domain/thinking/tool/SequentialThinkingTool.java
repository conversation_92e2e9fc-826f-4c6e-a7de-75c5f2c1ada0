package com.sankuai.dzhealth.ai.service.domain.thinking.tool;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.sankuai.dzhealth.ai.service.domain.enums.thinking.ThinkingSessionStatusEnum;
import com.sankuai.dzhealth.ai.service.domain.model.thinking.SequentialThought;
import com.sankuai.dzhealth.ai.service.domain.service.thinking.ThinkingSessionService;
import com.sankuai.dzhealth.ai.service.domain.service.thinking.ThinkingStepService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.ai.tool.annotation.Tool;
import org.springframework.ai.tool.annotation.ToolParam;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 序列思考工具
 * 实现Spring AI的Tool接口
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class SequentialThinkingTool {
    private final ThinkingStepService thinkingStepService;
    private final ThinkingSessionService thinkingSessionService;
    private final ObjectMapper objectMapper;
    private static final Logger logger = LoggerFactory.getLogger(SequentialThinkingTool.class);

    /**
     * 序列思考工具方法
     * 通过Spring AI的Tool注解注册为工具方法
     *
     * @param thought 思考内容
     * @param nextThoughtNeeded 是否需要下一个思考步骤
     * @param thoughtNumber 当前思考序号
     * @param totalThoughts 总思考步骤数
     * @param isRevision 是否是对之前思考的修正
     * @param revisesThought 修正的是哪个思考步骤
     * @param branchFromThought 从哪个思考步骤分支出来的
     * @param branchId 分支ID
     * @param needsMoreThoughts 是否需要更多思考步骤
     * @param confidenceScore 思考的置信度评分
     * @return 思考响应结果
     */
    @Tool(description = "一个详细的工具，通过思考步骤进行动态反思性的问题解决。每个思考步骤可以是常规分析、修订、分支或补充。适用于复杂问题分解、规划、分析和需要多步推理的场景。")
    public String sequentialthinking(
            @ToolParam(description = "当前的思考内容。可以是常规分析、对前面思考的修订、对决策的质疑、需要更多分析的提示、思路变化、假设生成或验证等。") String thought,
            @ToolParam(description = "是否需要下一个思考步骤。为true表示还需继续思考，即使已到预估结尾。") Boolean nextThoughtNeeded,
            @ToolParam(description = "当前思考序号，从1开始，可超过初始总数。") Long thoughtNumber,
            @ToolParam(description = "当前预计总思考步骤数，可动态调整。") Long totalThoughts,
            @ToolParam(description = "是否为对之前思考的修订。true表示本步骤是修订。", required = false) Boolean isRevision,
            @ToolParam(description = "如果是修订，表示修订的是哪一步（思考序号）。", required = false) Long revisesThought,
            @ToolParam(description = "如果是分支，表示从哪一步分支出来（思考序号）。", required = false) Long branchFromThought,
            @ToolParam(description = "分支ID。用于标识当前分支路径。", required = false) String branchId,
            @ToolParam(description = "是否需要更多思考步骤。用于到达结尾时发现还需补充分析。", required = false) Boolean needsMoreThoughts,
            @ToolParam(description = "本步思考的置信度评分，0~1之间。", required = true) BigDecimal confidenceScore,
            @ToolParam(description = "本步引用的外部信息URL列表，通常为webSearch工具返回的结果。", required = false) List<String> searchResults
    ) {

        logger.info("收到思考步骤: {}, 序号: {}/{}, 是否需要下一步: {}", thought, thoughtNumber, totalThoughts, nextThoughtNeeded);
        Long sessionId = thinkingStepService.getCurrentSessionId();
        if (sessionId == null) {
            logger.error("未找到当前会话ID");
            throw new IllegalStateException("未找到当前会话ID");
        }
        // 构建思考步骤对象
        SequentialThought sequentialThought = SequentialThought.builder()
                .thought(thought)
                .thoughtNumber(thoughtNumber)
                .totalThoughts(totalThoughts)
                .isRevision(isRevision)
                .revisesThought(revisesThought)
                .branchFromThought(branchFromThought)
                .branchId(branchId)
                .needsMoreThoughts(needsMoreThoughts)
                .nextThoughtNeeded(nextThoughtNeeded)
                .confidenceScore(confidenceScore)
                .searchResults(searchResults)
                .build();
        // 保存思考步骤
        Long stepId = thinkingStepService.saveThinkingStep(sessionId, sequentialThought);
        // 如果不需要下一步思考，则更新会话状态为已完成
        if (Boolean.FALSE.equals(nextThoughtNeeded)) {
            thinkingSessionService.updateSessionStatus(sessionId, ThinkingSessionStatusEnum.COMPLETED);
        }
        // 构建返回结果
        Map<String, Object> result = new HashMap<>();
        result.put("thoughtNumber", thoughtNumber);
        result.put("totalThoughts", totalThoughts);
        result.put("nextThoughtNeeded", nextThoughtNeeded);
        result.put("stepId", stepId);
        result.put("sessionId", sessionId);
        try {
            return objectMapper.writeValueAsString(result);
        } catch (JsonProcessingException e) {
            logger.error("序列化思考结果失败", e);
            throw new RuntimeException("序列化思考结果失败", e);
        }
    }
}