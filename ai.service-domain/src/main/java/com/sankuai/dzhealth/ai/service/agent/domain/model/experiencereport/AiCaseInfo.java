package com.sankuai.dzhealth.ai.service.agent.domain.model.experiencereport;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * AI助手案例信息领域模型
 * 用于前后对比图片的案例展示
 *
 * <AUTHOR>
 * @version 0.0.1
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AiCaseInfo implements Serializable {

    /**
     * 案例ID (对应noteId)
     */
    private Long caseId;

    /**
     * 体验前图片信息
     */
    private ImageInfo beforeImage;

    /**
     * 体验后图片信息
     */
    private ImageInfo afterImage;

    /**
     * 跳转链接
     */
    private String jumpUrl;

    /**
     * 关联商品ID
     */
    private Long productId;

    /**
     * 关联门店ID
     */
    private Long dpShopId;

    /**
     * 价格信息
     */
    private String price;

    /**
     * 体验报告标题
     */
    private String title;

    /**
     * 图片信息内部类
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class ImageInfo implements Serializable {

        /**
         * 图片URL
         */
        private String url;

        /**
         * 图片标签 (如：体验前、体验30天后)
         */
        private String label;

        /**
         * 图片ID
         */
        private Long picId;
    }
}

