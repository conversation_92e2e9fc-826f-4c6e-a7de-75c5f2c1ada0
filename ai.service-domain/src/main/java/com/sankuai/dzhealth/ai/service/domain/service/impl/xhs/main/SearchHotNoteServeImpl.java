package com.sankuai.dzhealth.ai.service.domain.service.impl.xhs.main;

import com.sankuai.dzhealth.ai.service.api.ESVectorStoreService;
import com.sankuai.dzhealth.ai.service.domain.service.XhsNoteAbstractService;
import com.sankuai.dzhealth.ai.service.domain.utils.JsonUtils;
import com.sankuai.dzhealth.ai.service.request.DocumentSearchRequest;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class SearchHotNoteServeImpl extends XhsNoteAbstractService<String> {

    @Resource
    private ESVectorStoreService esVectorStoreService;

    @Override
    protected String service(String request) throws Exception {
        DocumentSearchRequest searchRequest = JsonUtils.parseObject(request, DocumentSearchRequest.class);
        String res = JsonUtils.toJsonString(esVectorStoreService.similaritySearch(searchRequest));


        return res;
    }
}
