package com.sankuai.dzhealth.ai.service.agent.domain.tools.param;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.ai.tool.annotation.ToolParam;

/**
 * POI搜索请求参数
 * POI Search Request Parameters
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PoiSearchRequest {

    @ToolParam(
        description = "搜索关键字，如地点名称、城市名称等 / Search keyword, such as location name, city name, etc.",
        required = true
    )
    private String keyword;

    @ToolParam(
        description = "纬度坐标，必须大于0的正数，可选参数 / Latitude coordinate, must be a positive number greater than 0, optional",
        required = false
    )
    private Double lat;

    @ToolParam(
        description = "经度坐标，必须大于0的正数，可选参数 / Longitude coordinate, must be a positive number greater than 0, optional",
        required = false
    )
    private Double lng;
}
