package com.sankuai.dzhealth.ai.service.agent.domain.process.task;

import com.meituan.service.inf.kms.utils.KmsResultNullException;
import com.sankuai.dzhealth.ai.service.agent.domain.context.ChatClientContext;
import com.sankuai.dzhealth.ai.service.agent.domain.context.MessageContext;
import com.sankuai.dzhealth.ai.service.agent.domain.context.TaskContext;
import com.sankuai.dzhealth.ai.service.agent.domain.memory.ChatHistoryMemory;
import com.sankuai.dzhealth.ai.service.agent.domain.service.ChatClientService;
import com.sankuai.dzhealth.ai.service.agent.infrastructure.model.MultiEvaluationRequest;
import com.sankuai.dzhealth.ai.service.domain.evaluation.entity.EvaluationMessage;
import com.sankuai.dzhealth.ai.service.domain.utils.JsonUtils;
import com.sankuai.dzhealth.ai.service.enums.QuerySourceEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.ai.chat.messages.Message;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;

import java.util.List;

/**
 * @author:chenwei
 * @time: 2025/7/13 13:36
 * @version: 0.0.1
 */

@Component
@Slf4j
public class GeneralTask {

    @Autowired
    private ChatClientService chatClientService;
    @Autowired
    private ChatHistoryMemory chatHistoryMemory;

    public String getAnswer(TaskContext context) {
        String answer = StringUtils.EMPTY;;
        try {
            ChatClientContext chatClientContext = buildChatClientContext(context);
            if (chatClientContext.isStream()) {
                answer = chatClientService.fridayChatStream(chatClientContext).join();
            } else {
                answer = chatClientService.fridayChatCall(chatClientContext);
            }

        } catch (KmsResultNullException e) {
            log.error("获取kms appid异常", e);
        }
        return answer;

    }


    public Flux<String> getFluxThink(TaskContext context) {
        try {
            ChatClientContext chatClientContext = buildChatClientContext(context);
            return chatClientService.fridayChatStreamFlux(chatClientContext);

        } catch (KmsResultNullException e) {
            log.error("kms appid errror", e);
            return Flux.empty();
        } catch (Exception e) {
            log.error("getMockThink error", e);
            throw e;
        }

    }




    public String getJsonAnswer(TaskContext context) {
        String answer = StringUtils.EMPTY;;
        try {
            ChatClientContext chatClientContext = buildChatClientContext(context);
            answer = chatClientService.fridayChatWithJson(chatClientContext);
        } catch (Exception e) {
            log.error("获取kms appid异常", e);
        }
        return answer;

    }



    private ChatClientContext buildChatClientContext(TaskContext context) {
        TaskConfig taskConfig = context.getTaskConfig();
        ChatClientContext chatClientContext = new ChatClientContext();
        chatClientContext.setMessageContext(context.getMessageContext());
        chatClientContext.setModel(taskConfig.getModel());
        chatClientContext.setKmsKey(taskConfig.getKmsKey());
        chatClientContext.setSystemPrompt(taskConfig.getSystemPrompt());
        chatClientContext.setUserPrompt(taskConfig.getUserPrompt());
        chatClientContext.setToolNames(taskConfig.getToolNames());
        chatClientContext.setStream(taskConfig.isStream());
        chatClientContext.setMaxTokens(taskConfig.getMaxTokens());
        chatClientContext.setTaskType(taskConfig.getType());
        chatClientContext.setExtraBodyConfig(taskConfig.getExtraBodyConfig());
        chatClientContext.setThinkingText(context.getMessageContext().getThinkText());
        return chatClientContext;
    }

    public MultiEvaluationRequest  buildMultiDialogueEvaluationRequest(MessageContext messageContext, TaskConfig taskConfig, String answer) {
        List<MultiEvaluationRequest> multiEvaluationRequests = messageContext.getMultiEvaluationRequests();
        String querySource = messageContext.getQuerySource();
        QuerySourceEnum querySourceEnum = QuerySourceEnum.getByType(querySource);
        List<Message> messages = chatHistoryMemory.get(messageContext.getSessionId());
        List<EvaluationMessage> evaluationMessages = messages.stream().map(ms -> new EvaluationMessage(ms.getMessageType(), ms.getText())).toList();
        String chatHistoryMessages = JsonUtils.toJsonString(evaluationMessages);
        MultiEvaluationRequest multiEvaluationRequest = MultiEvaluationRequest.builder()
                .bizScene(messageContext.getBizType())
                .sessionId(messageContext.getSessionId())
                .messageId(messageContext.getMsgId())
                .modelScene(taskConfig.getType())
                .dialog(MultiEvaluationRequest.Message.builder()
                        .system(taskConfig.getSystemPrompt())
                        .userQuery(taskConfig.getUserPrompt())
                        .assistantAnswer(answer)
                        .build())
                .context(chatHistoryMessages).source(querySourceEnum.getCode())
                .searchRequestCount(messageContext.getSearchRequestCount().get())
                .searchRequestSuccessCount(messageContext.getSearchRequestSuccessCount().get())
                .build();
        multiEvaluationRequests.add(multiEvaluationRequest);
        return multiEvaluationRequest;
    }
}
